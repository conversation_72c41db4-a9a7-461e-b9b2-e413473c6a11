/**
 * CtmsSoapBindingImpl.java
 *
 * This file was auto-generated from WSDL
 * by the Apache Axis 1.4 Apr 22, 2006 (06:55:48 PDT) WSDL2Java emitter.
 */

package com.pukka.iptv.api.in.webservice.server;

import com.pukka.iptv.api.in.webservice.constants.ResultConstants;
import com.pukka.iptv.common.api.feign.SysInPassageFeignClient;
import com.pukka.iptv.common.base.constant.RedisKeyConstants;
import com.pukka.iptv.common.core.c2in.InOrderCP;
import com.pukka.iptv.common.core.util.IdUtils;
import com.pukka.iptv.common.core.util.SpringUtils;
import com.pukka.iptv.common.core.util.StringUtils;
import com.pukka.iptv.common.data.model.c2in.ResultDescribe;
import com.pukka.iptv.common.data.model.sys.SysDictionaryItem;
import com.pukka.iptv.common.data.model.sys.SysInPassage;
import com.pukka.iptv.common.rabbitmq.config.InOrderDirectMQConfig;
import com.pukka.iptv.common.rabbitmq.vo.MessageBody;
import com.pukka.iptv.common.redis.service.RedisService;
import lombok.extern.slf4j.Slf4j;

import org.apache.commons.lang3.ObjectUtils;
import org.springframework.amqp.rabbit.connection.CorrelationData;
import org.springframework.amqp.rabbit.core.RabbitTemplate;

import java.util.HashMap;
import java.util.Map;
import java.util.UUID;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.TimeUnit;

@Slf4j
public class CtmsSoapBindingImpl implements CSPRequest{

    private RabbitTemplate rabbitTemplate = SpringUtils.getBean("rabbitTemplate");

    private SysInPassageFeignClient sysInPassageFeignClient = SpringUtils.getBean(SysInPassageFeignClient.class);

    private RedisService redisService =  SpringUtils.getBean(RedisService.class);


    public CSPResult execCmd(String CSPID, String LSPID, String correlateID, String cmdFileURL) throws java.rmi.RemoteException {
        CSPResult cspResult = new CSPResult();
        ResultDescribe resultDescribe = null;
        try{
            log.info("CSPID:" + CSPID +"LSPID:"+ LSPID + "correlateId:" + correlateID + "cmdFileURL:"+ cmdFileURL);
            //todo 校验,系统缓存需要加入redis缓存中
            resultDescribe = checkParam(CSPID, LSPID, correlateID, cmdFileURL);

            if(resultDescribe.getCode().equals(ResultConstants.SUCCESS_CODE)){
                SysInPassage sysInPassage = null;
                //系统判断cp是否存在，直接读取全局redis缓存
                Map<String, SysInPassage> stringSysInPassageMap = redisService.getCacheMap(RedisKeyConstants.SYS_IN_PASSAGE);
                if(stringSysInPassageMap != null){
                    if(stringSysInPassageMap.containsKey(CSPID)){
                        sysInPassage = stringSysInPassageMap.get(CSPID);
                    }
                }
                //数据组帧
                MessageBody message = new MessageBody();
                Map<String, Object> params = new ConcurrentHashMap<>();
                params.put("CSPID", CSPID);
                params.put("LSPID", LSPID);
                params.put("correlateID", correlateID);
                params.put("cmdFileURL", cmdFileURL);
                params.put("inPassageId", sysInPassage.getId());
                params.put("inPassageName", sysInPassage.getName());
                message.setId(IdUtils.simpleUUID());
                message.setMsg(params);

                //全局唯一 不然ReturnCallback 无效
                CorrelationData correlationData = new CorrelationData(UUID.randomUUID().toString());
                this.rabbitTemplate.convertAndSend(InOrderDirectMQConfig.IN_ORDER_EXCHANGE, InOrderDirectMQConfig.IN_ORDER_ROUTING, message, correlationData);
                log.info(" Send message to RabbitMQ " );
                log.info("messge id info:" + message.getId());
            }else{
                log.info("Illegal CSPID, don't Send message to RabbitMQ !" );
            }
            cspResult.setResult(resultDescribe.getCode());
            cspResult.setErrorDescription(resultDescribe.getDesc());
        }catch (Exception e){
            cspResult.setResult(ResultConstants.ERROR_CODE);
            cspResult.setErrorDescription(" Treat failure ");
            log.error("接收注入工单异常：{}",e.fillInStackTrace());
        }
        return cspResult;
    }

    /**
     * 校验传输参数
     * @param cspId
     * @param lspId
     * @param correlateId
     * @param cmdFileUrl
     * @return
     */
    public ResultDescribe checkParam(String cspId, String lspId, String correlateId, String cmdFileUrl){
        ResultDescribe resultDescribe = new ResultDescribe();
        //入库前，需要对CSPID与LSPID进行验证
        if ( StringUtils.isEmpty(cspId)){
            resultDescribe.setCode(ResultConstants.ERROR_CODE);
            resultDescribe.setDesc(" CSPID is null!");
            log.error(" CSPID is null!");
            return resultDescribe;
        }else{
            //系统判断cp是否存在，直接读取全局redis缓存
            Map<String, SysInPassage> stringSysInPassageMap = redisService.getCacheMap(RedisKeyConstants.SYS_IN_PASSAGE);

            if(!stringSysInPassageMap.containsKey(cspId)){
                resultDescribe.setCode(ResultConstants.ERROR_CODE);
                resultDescribe.setDesc(" illegal  CSPID");
                log.error(" illegal  CSPID!");
                return resultDescribe;
            }else{
            }
            //二期修改磁盘容量判断（超过限定的容量，注入不进来）
            SysInPassage sysInPassage = null;
            if(stringSysInPassageMap != null){
                if(stringSysInPassageMap.containsKey(cspId)){
                    sysInPassage = stringSysInPassageMap.get(cspId);
                }
            }
            Map<String, Boolean> cpDisktSpaceMap = redisService.getCacheMap(RedisKeyConstants.CP_DISK_SPACE);
            boolean cpDiskFlag = true;
            if(cpDisktSpaceMap != null){
                if(cpDisktSpaceMap.containsKey(sysInPassage.getCpId().toString())){
                    cpDiskFlag = cpDisktSpaceMap.get(sysInPassage.getCpId().toString());
                    if(!cpDiskFlag){
                        resultDescribe.setCode(ResultConstants.ERROR_CODE);
                        resultDescribe.setDesc(" he disk space is full !");
                        log.error("The disk space is full：{}", sysInPassage.getCpName());
                        return resultDescribe;
                    }
                }
            }
        }
        if (StringUtils.isEmpty(lspId)){
            resultDescribe.setCode(ResultConstants.ERROR_CODE);
            resultDescribe.setDesc(" LSPID is null!");
            log.error(" LSPID is null!");
            return resultDescribe;
        }else{
            //需要系统判断lspid是否存在  ut测试不需要鉴权lspid
//            SysDictionaryItem sysDictionaryItem = redisService.getCacheObject(RedisKeyConstants.SYS_DICTIONARY_ITEM_PLATFORM_IDENTIFICATION_KEY);
//
//            if(ObjectUtils.isNotEmpty(sysDictionaryItem)){
//                if(sysDictionaryItem.getStatus().equals(ResultConstants.DICTIONARY_STATUS)){
//                    if(!lspId.equals(sysDictionaryItem.getCode())){
//                        resultDescribe.setCode(ResultConstants.ERROR_CODE);
//                        resultDescribe.setDesc(" illegal  LSPID");
//                        log.error(" illegal  LSPID!");
//                        return resultDescribe;
//                    }
//                }
//            }
        }

        if (StringUtils.isEmpty(correlateId)){
            resultDescribe.setCode(ResultConstants.ERROR_CODE);
            resultDescribe.setDesc(" correlateID is null!");
            log.error(" correlateID is null!");
            return resultDescribe;
        }
        if (StringUtils.isEmpty(cmdFileUrl)){
            resultDescribe.setCode(ResultConstants.ERROR_CODE);
            resultDescribe.setDesc(" cmdFileURL is null!");
            log.error(" cmdFileURL is null!");
            return resultDescribe;
        }
        resultDescribe.setCode(ResultConstants.SUCCESS_CODE);
        resultDescribe.setDesc(ResultConstants.SUCCESS);
        return resultDescribe;
    }



}
