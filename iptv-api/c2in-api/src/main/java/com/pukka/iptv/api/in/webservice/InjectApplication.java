package com.pukka.iptv.api.in.webservice;

import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.autoconfigure.jdbc.DataSourceAutoConfiguration;
import org.springframework.boot.web.servlet.ServletComponentScan;
import org.springframework.cloud.openfeign.EnableFeignClients;

/**
 * <AUTHOR>
 * @date 2021/7/19
 */

//扫描自定义的WebFilter和WebListener,否则无法扫描到
@ServletComponentScan
@SpringBootApplication
@EnableFeignClients(basePackages = "com.pukka.iptv.common.api.feign")
public class InjectApplication {

	public static void main(String[] args) {
		SpringApplication.run(InjectApplication.class, args);
	}

}
