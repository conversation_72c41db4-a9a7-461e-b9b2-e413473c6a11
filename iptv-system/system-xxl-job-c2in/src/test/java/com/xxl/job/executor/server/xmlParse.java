package com.xxl.job.executor.server;

import com.alibaba.fastjson.JSON;
import com.pukka.iptv.common.core.util.StringUtils;
import com.xxl.job.executor.XxlJobC2inExecutorApplication;
import com.xxl.job.executor.c2in.common.enums.ActionEnums;
import com.xxl.job.executor.c2in.common.enums.ElementTypeEnums;
import com.xxl.job.executor.c2in.common.exception.ParserException;
import com.xxl.job.executor.c2in.common.util.ManageHelper;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.time.StopWatch;
import org.dom4j.Attribute;
import org.dom4j.Document;
import org.dom4j.DocumentException;
import org.dom4j.Element;
import org.dom4j.io.SAXReader;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import java.io.File;
import java.util.ArrayList;
import java.util.List;
@RunWith(SpringRunner.class)
@SpringBootTest(classes = XxlJobC2inExecutorApplication.class)
@Slf4j
public class xmlParse {
    @Test
    public void test() throws DocumentException {

        SAXReader reader = new SAXReader();
        Document document = reader.read(new File("C:\\Users\\<USER>\\Desktop\\自动下发工单(3)\\节目单\\jmd_RESGIT.xml"));
        Element ADI = document.getRootElement();
        if (!"ADI".equals(ADI.getName())) {
            throw new DocumentException("工单解析异常");
        }
        //规则1
        Element objects = ADI.element("Objects");
        List<?> objectsElement = objects.elements("Object");
        if (objectsElement == null || objectsElement.isEmpty()) return;
        List<CmsSchedule> schedules = new ArrayList<>();
        StopWatch sw = StopWatch.createStarted();
        objectsElement.forEach(object -> {
            Element element = (Element) object;
            Attribute contentTypeAttribute = element.attribute("ElementType");
            String contentType = contentTypeAttribute.getValue();
            //根据抽象工厂获取到不同的解析族
            if (ElementTypeEnums.Schedule.getInfo().equals(contentType)) {
                CmsSchedule cmsSchedule = new CmsSchedule();
                Attribute actionAttr = element.attribute("Action");
                if (actionAttr == null) {
                    throw new ParserException("Catalog 缺少'Action'属性值");
                }
                cmsSchedule.setAction(ActionEnums.getCodeByInfo(actionAttr.getValue()));
                ParseFactory scheduleFactory = new ScheduleFactory();
                scheduleFactory.createScheduleParse().parse(element, cmsSchedule);
                schedules.add(cmsSchedule);

            }

        });
        System.out.printf("耗时：%dms.\n", sw.getTime());
        for (CmsSchedule schedule : schedules) {
            System.out.println(JSON.toJSONString(schedule));
        }
        //在规则中遍历objects 获取到每一个object的ElementType 根据ElementType来判断是哪个产品族
        //规则2
        Element mappings = ADI.element("Mappings");
    }



    interface ParseFactory {
        AbstractParse<CmsSchedule> createScheduleParse();
    }

    class ScheduleFactory implements ParseFactory {

        @Override
        public AbstractParse<CmsSchedule> createScheduleParse() {
            return new scheduleParse();
        }
    }


    abstract static class AbstractParse<T> {
        public abstract void parse(Element element, T t);
    }

    class scheduleParse extends AbstractParse<CmsSchedule> {
        @Override
        public void parse(Element element, CmsSchedule ret) {
            List<?> propertysEle = element.elements("Property");
            propertysEle.forEach(e -> {
                Element property = (Element) e;
                Attribute nameAttr = property.attribute("Name");
                if (nameAttr == null || StringUtils.isEmpty(nameAttr.getValue())) {
                    if (ManageHelper.isRegistXML(ret.getAction())) {
                        throw new ParserException("Schedule属性列表中缺少'Name'属性名称");
                    }
                }
                String text = property.getTextTrim();
                String value = nameAttr.getValue();
                scheduleEvent(value, text, ret);
            });
        }
    }

    public void scheduleEvent(String value, String text, CmsSchedule cmsSchedule) {
        switch (value) {
            case "ChannelCode":
                cmsSchedule.setChannelCode(text);
                break;
            case "ChannelID":
                cmsSchedule.setChannelId(text);
            case "ProgramName":
                cmsSchedule.setProgramName(text);
        }
    }

    interface filter {
        <T> void doFilter(T t, Element object);
    }

    class objectFilter implements filter {

        @Override
        public <T> void doFilter(T t, Element object) {

        }
    }

    @Getter
    @Setter
    @Accessors(chain = true)
    class CmsSchedule {
        private Integer action;
        private String channelCode;
        private String channelId;
        private String programName;
    }

    @Getter
    @Setter
    @Accessors(chain = true)
    class cmsPackage {
        private Integer action;
        private String name;
        private String sortName;
        private String description;
    }
}
