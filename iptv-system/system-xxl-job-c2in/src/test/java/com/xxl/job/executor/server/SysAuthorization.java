package com.xxl.job.executor.server;

import com.alibaba.excel.annotation.ExcelProperty;
import com.baomidou.mybatisplus.annotation.*;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import java.util.Date;

/**
 *
 * @author: luo
 * @date: 2021-8-27 22:15:03
 */

@Data
public class SysAuthorization {
	/**全局唯一标号*/
    @ExcelProperty("code")
    private String code;
	/**合同号*/
	@ExcelProperty("name")
    private String name;
	/**cpId*/
	@ExcelProperty("cp_id")
    private Long cpId;
	/**CP名称*/
	@ExcelProperty("cp_name")
    private String cpName;
	/**spId*/
	@ExcelProperty("sp_id")
    private Long spId;
	/**SP名称*/
	@ExcelProperty("sp_name")
    private String spName;
	/**自动授权 1：是 2：否*/
	@ExcelProperty("auto_authorize")
    private Integer autoAuthorize;
	/**描述*/
	@ExcelProperty("description")
    private String description;
	/**是否删除,1否 255是*/
	@ExcelProperty("status")
    private Integer status;
}
