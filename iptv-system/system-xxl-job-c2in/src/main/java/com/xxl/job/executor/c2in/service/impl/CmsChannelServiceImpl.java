package com.xxl.job.executor.c2in.service.impl;

import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.pukka.iptv.common.base.enums.ContentTypeEnum;
import com.pukka.iptv.common.base.enums.PublishStatusEnum;
import com.pukka.iptv.common.base.exception.BizException;
import com.xxl.job.executor.c2in.model.*;
import com.pukka.iptv.common.data.model.cms.CmsChannel;
import com.pukka.iptv.common.data.model.cms.CmsProgram;
import com.pukka.iptv.common.data.model.cms.CmsSeries;
import com.xxl.job.executor.c2in.common.constant.TextConstants;
import com.xxl.job.executor.c2in.mapper.BmsContentMapper;
import com.xxl.job.executor.c2in.mapper.CmsChannelMapper;
import com.xxl.job.executor.c2in.service.CmsChannelService;
import com.xxl.job.executor.c2in.service.condition.RuleCondition;
import com.xxl.job.executor.c2in.service.condition.RuleResult;
import com.xxl.job.executor.c2in.service.condition.rule.LockStatusRule;
import com.xxl.job.executor.c2in.service.condition.rule.PublishStatusRule;
import com.xxl.job.executor.c2in.service.condition.rule.enums.PublishCheck;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

/**
 *
 * @author: tan
 * @date: 2021年8月27日 上午10:01:17
 */

@Service
public class CmsChannelServiceImpl extends ServiceImpl<CmsChannelMapper, CmsChannel> implements CmsChannelService {

    @Autowired
    private BmsContentMapper bmsContentMapper;
    @Override
    public void removeByCode(String code) {
        LambdaQueryWrapper<CmsChannel> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.eq(CmsChannel::getCode, code);
        this.remove(queryWrapper);
    }

    @Override
    public CmsChannel getByCodeCpid(String code, Long cpId) {
        LambdaQueryWrapper<CmsChannel> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.eq(CmsChannel::getCode, code);
        queryWrapper.eq(CmsChannel::getCpId, cpId).last(TextConstants.LIMIT_ONE);
        return this.getOne(queryWrapper);
    }

    @Override
    public CmsChannel getByCode(String code) {
        LambdaQueryWrapper<CmsChannel> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.eq(CmsChannel::getCode, code).last(TextConstants.LIMIT_ONE);
        return this.getOne(queryWrapper);
    }

    @Override
    public Long countByCode(String code) {
        LambdaQueryWrapper<CmsChannel> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.eq(CmsChannel::getCode, code);
        return this.count(queryWrapper);
    }
    /**
     * 编辑回显时 做发布状态检查
     *
     * @param type 1.单集 2.子集 3.连续剧 4.直播 5.物理频道 6.节目单
     * @param id   需要编辑的媒资ID
     * @return 为true可以打开页面
     */
    @Transactional(rollbackFor = Exception.class)
    public boolean check(Integer type, Long id) {
        LambdaQueryWrapper<BmsContent> eq = null;
        BmsContent badData = null;
        RuleResult rr = null;
        switch (type) {
            case 1:
                eq = Wrappers.lambdaQuery(BmsContent.class)
                        .select(BmsContent::getName, BmsContent::getSpName)
                        .eq(BmsContent::getCmsContentId, id)
                        .eq(BmsContent::getContentType, ContentTypeEnum.FILM.getValue())
                        .in(BmsContent::getPublishStatus, PublishStatusEnum.PUBLISHING.getCode(), PublishStatusEnum.ROLLBACKING.getCode(), PublishStatusEnum.UPDATING.getCode())
                        .last(" limit 1 ");
                badData = bmsContentMapper.selectOne(eq);
                if (ObjectUtil.isNotEmpty(badData)) {
                    throw new BizException("发布状态不能为 发布中、更新中、回收中");
                }
                rr = RuleCondition.create()
                        .and(LockStatusRule.init(CmsProgram.class).data(id))
                        .execute();
                break;
            case 2:
                rr = RuleCondition.create()
                        .and(LockStatusRule.init(CmsProgram.class).data(id))
                        .and(PublishStatusRule.init(BmsProgram.class).data(id).condCol(BmsProgram::getCmsContentId).policy(PublishCheck.ING))
                        .execute();
                break;
            case 3:
                eq = Wrappers.lambdaQuery(BmsContent.class)
                        .select(BmsContent::getName, BmsContent::getSpName)
                        .eq(BmsContent::getCmsContentId, id)
                        .in(BmsContent::getContentType, ContentTypeEnum.TELEPLAY.getValue(), ContentTypeEnum.EPISODES.getValue())
                        .in(BmsContent::getPublishStatus, PublishStatusEnum.PUBLISHING.getCode(), PublishStatusEnum.ROLLBACKING.getCode(), PublishStatusEnum.UPDATING.getCode())
                        .last(" limit 1 ");
                badData = bmsContentMapper.selectOne(eq);
                if (ObjectUtil.isNotEmpty(badData)) {
                    throw new BizException("发布状态不能为 发布中、更新中、回收中");
                }
                rr = RuleCondition.create()
                        .and(LockStatusRule.init(CmsSeries.class).data(id))
                        .execute();
                break;
            case 4:
                rr = RuleCondition.create()
                        .and(PublishStatusRule.init(BmsChannel.class).data(id).condCol(BmsChannel::getCmsChannelId).policy(PublishCheck.ING))
                        .execute();
                break;
            case 5:
                rr = RuleCondition.create()
                        .and(PublishStatusRule.init(BmsPhysicalChannel.class).data(id).condCol(BmsPhysicalChannel::getCmsPhysicalChannelId).policy(PublishCheck.ING).col(BmsPhysicalChannel::getChannelName))
                        .execute();
                break;
            case 6:
                rr = RuleCondition.create()
                        .and(PublishStatusRule.init(BmsSchedule.class).data(id).condCol(BmsSchedule::getCmsScheduleId).policy(PublishCheck.ING).col(BmsSchedule::getProgramName))
                        .execute();
                break;
            default:
                throw new BizException("case值有误");
        }
        if (ObjectUtil.isEmpty(rr)) {
            return true;
        }
        if (!rr.isPass()) {
            throw new BizException("请检查所选项的锁定或发布状态(发布状态不能为 发布中、更新中、回收中)");
        }
        return true;
    }
}


