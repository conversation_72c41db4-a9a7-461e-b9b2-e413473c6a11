package com.xxl.job.executor.c2in.common.parse.impl;

import com.google.common.base.Preconditions;
import com.google.common.base.Strings;
import com.google.common.primitives.Ints;
import com.pukka.iptv.common.core.util.DateUtils;
import com.pukka.iptv.common.data.model.in.InChannel;
import com.xxl.job.executor.c2in.common.enums.ActionEnums;
import com.xxl.job.executor.c2in.common.exception.ParserException;
import com.xxl.job.executor.c2in.common.parse.IParse;
import com.xxl.job.executor.c2in.common.util.ManageHelper;
import org.apache.commons.lang3.math.NumberUtils;
import org.dom4j.Attribute;
import org.dom4j.Element;

import java.util.Date;
import java.util.List;

/**
 * @ClassName ChannelParseImpl
 * @Description TV频道信息解析类
 * <AUTHOR>
 * @Date 2021/8/27 10:09
 * @Version
 */
public class ChannelParseImpl implements IParse<Element, InChannel> {

//    private static final ChannelParseImpl INSTANCE = new ChannelParseImpl();
//
//    public static ChannelParseImpl getInstance(){
//        return INSTANCE;
//    }

    public ChannelParseImpl(){

    }


    @Override
    public InChannel get(Element element) {
        Preconditions.checkArgument(element != null);
        Attribute attr = element.attribute("Action");
        if (attr == null || Strings.isNullOrEmpty(attr.getValue())) {
            throw new ParserException("Channel 缺少'Action'属性值");
        }

        InChannel inChannel = new InChannel();
        inChannel.setAction(ActionEnums.getCodeByInfo(attr.getValue()));

        attr = element.attribute("Code");
        if (attr == null || Strings.isNullOrEmpty(attr.getValue())) {
            throw new ParserException("Channel 缺少'Code'属性值");
        }
        inChannel.setCode(attr.getValue());

        attr = element.attribute("ID");
        if (attr == null || Strings.isNullOrEmpty(attr.getValue())) {
            throw new ParserException("Channel 缺少'ID'属性值");
        }
        inChannel.setCorrelateId(attr.getValue());

        for (Element o : (List<Element>) element.elements("Property")) {
            Attribute name = o.attribute("Name");
            if (name == null || Strings.isNullOrEmpty(name.getValue())) {
                if(ManageHelper.isRegistXML(inChannel.getAction())){
                    throw new ParserException("Channel属性列表中缺少'Name'属性名称");
                }
            }

            String pn = name.getValue();
            String value = o.getTextTrim();
            if (pn.equals("ChannelNumber")) {
                inChannel.setChannelNumber(value);
                continue;
            }

            if (pn.equals("Name")) {
                if (Strings.isNullOrEmpty(value)) {
                    if(ManageHelper.isRegistXML(inChannel.getAction())){
                        throw new ParserException("Channel属性列表中缺少'Name'属性值");
                    }

                }
                inChannel.setName(value);
                continue;
            }

            if (pn.equals("CallSign")) {
                inChannel.setCallSign(value);
                continue;
            }

            if (pn.equals("TimeShift")) {
                Integer v = Ints.tryParse(value);
                if (v != null) {
                    inChannel.setTimeShift(v);
                }
                continue;
            }

            if (pn.equals("StorageDuration")) {
                inChannel.setStorageDuration(NumberUtils.toInt(value));
                continue;
            }

            if (pn.equals("TimeShiftDuration")) {
                inChannel.setTimeShiftDuration(NumberUtils.toInt(value));
                continue;
            }

            if (pn.equals("Description")) {
                inChannel.setDescription(value);
                continue;
            }

            if (pn.equals("Country")) {
                inChannel.setCountry(value);
                continue;
            }

            if (pn.equals("State")) {
                inChannel.setState(value);
                continue;
            }

            if (pn.equals("City")) {
                inChannel.setCity(value);
                continue;
            }

            if (pn.equals("ZipCode")) {
                inChannel.setZipCode(value);
                continue;
            }

            if (pn.equals("Type")) {
                Integer v = Ints.tryParse(value);
                if (v == null) {
                    if(ManageHelper.isRegistXML(inChannel.getAction())){
                        throw new ParserException("Channel 缺少'Type'属性值");
                    }

                }
                inChannel.setType(v);
                continue;
            }

            if (pn.equals("SubType")) {
                Integer v = Ints.tryParse(value);
                if (v != null) {
                    inChannel.setSubType(v);
                }
                continue;
            }

            if (pn.equals("Language")) {
                inChannel.setLanguage(value);
                continue;
            }

            if (pn.equals("Status")) {
//                Integer v = Ints.tryParse(value);
//                if (v != null) {
//                    inChannel.setStatus(v);
//                }
                inChannel.setStatus(NumberUtils.toInt(value, 1));
                continue;
            }

            if (pn.equals("StartTime")) {
                inChannel.setStartTime(value);
                continue;
            }

            if (pn.equals("EndTime")) {
                inChannel.setEndTime(value);
                continue;
            }

            if (pn.equals("Macrovision")) {
                Integer v = Ints.tryParse(value);
                if (v != null) {
                    inChannel.setMacrovision(v);
                }
                continue;
            }

            if (pn.equals("Bilingual")) {
                Integer v = Ints.tryParse(value);
                if (v != null) {
                    inChannel.setBilingual(v);
                }
                continue;
            }

            if (pn.equals("VSPCode")) {
                inChannel.setVspCode(value);
            }
        }
        if (inChannel.getType() == null) {
            if(ManageHelper.isRegistXML(inChannel.getAction())){
                throw new ParserException("Channel属性列表中缺少'Type'属性值");
            }

        }
        if (Strings.isNullOrEmpty(inChannel.getName())) {
            if(ManageHelper.isRegistXML(inChannel.getAction())){
                throw new ParserException("Channel属性列表中缺少'Name'属性值");
            }

        }
        inChannel.setCreateTime(new Date());
        return inChannel;
    }
}
