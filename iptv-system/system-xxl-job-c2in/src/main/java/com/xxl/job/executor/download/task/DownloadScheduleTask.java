package com.xxl.job.executor.download.task;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.pukka.iptv.common.base.enums.CpDownloadModeEnum;
import com.pukka.iptv.common.data.model.cms.CmsDownload;
import com.xxl.job.executor.c2in.common.enums.DownloadStatusEnum;
import com.xxl.job.executor.c2in.mapper.CmsDownloadMapper;
import com.xxl.job.executor.config.DownloadTaskConfig;
import com.xxl.job.executor.download.task.util.DownloadTaskUtil;
import java.util.List;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;

/**
 * @Author: chiron
 * @Date: 2023/08/14/15:33
 * @Description: 下载任务
 */

@Slf4j
@Service
public class DownloadScheduleTask {

  @Autowired
  private CmsDownloadMapper cmsDownloadMapper;

  @Autowired
  private DownloadTaskConfig downloadTaskConfig;

  @Autowired
  private DownloadTaskUtil downloadTaskUtil;

  //每隔10秒执行一次
  @Scheduled(initialDelay = 1000, fixedRateString = "${spring.iptv.download.task.config.fixedratestring}")
  public void init() {
    List<CmsDownload> cmsDownloadList = null;
    //打印执行时间
    log.info("NormalHandle队列 is running...");
    if (downloadTaskConfig.getEnable()) {
      LambdaQueryWrapper<CmsDownload> queryWrapper = Wrappers.lambdaQuery();
      queryWrapper.eq(CmsDownload::getStatus, DownloadStatusEnum.WAIT.getCode());
      queryWrapper.isNotNull(CmsDownload::getCallBackUrl);
      queryWrapper.orderByAsc(CmsDownload::getPriority).orderByAsc(CmsDownload::getCreateTime);
      cmsDownloadList = cmsDownloadMapper.selectList(queryWrapper);
      if (cmsDownloadList == null || cmsDownloadList.isEmpty()) {
        log.info("NormalHandle队列 is empty...");
      }
      log.info("添加下载任务到NormalHandle队列中...");
      downloadTaskUtil.setDownloadTask(CpDownloadModeEnum.DEFAULT, cmsDownloadList);
      log.info("添加下载任务到NormalHandle队列中完成.添加下载任务:{} 条", cmsDownloadList.size());
      log.debug("NormalHandle队列中的任务:{}", cmsDownloadList);
    } else {
      downloadTaskUtil.clear();
      log.debug("下载任务限制CP策略已关闭，当前添加下载任务跳过.");
    }
  }

}
