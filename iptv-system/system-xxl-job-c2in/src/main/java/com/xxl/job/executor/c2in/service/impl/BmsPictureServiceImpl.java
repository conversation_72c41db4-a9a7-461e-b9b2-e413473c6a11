package com.xxl.job.executor.c2in.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.xxl.job.executor.c2in.common.constant.TextConstants;
import com.xxl.job.executor.c2in.common.enums.PublishStatusEnums;
import com.xxl.job.executor.c2in.mapper.BmsPictureMapper;
import com.xxl.job.executor.c2in.model.BmsPicture;
import com.xxl.job.executor.c2in.service.BmsPictureService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.List;


/**
 * @author: liuli
 * @date: 2021年9月2日 下午3:35:47
 */

@Service
@Slf4j
public class BmsPictureServiceImpl extends ServiceImpl<BmsPictureMapper, BmsPicture> implements BmsPictureService {

    @Override
    public List<BmsPicture> listByCmsPictureCode(String code){
        LambdaQueryWrapper<BmsPicture> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.eq(BmsPicture::getCmsPictureCode ,code);
        return this.list(queryWrapper);
    }

    @Override
    public void removeByCmsPictureCode(String code) {
        LambdaQueryWrapper<BmsPicture> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.eq(BmsPicture::getCmsPictureCode ,code);
        this.remove(queryWrapper);
    }

    @Override
    public Long countByContentCode(String contentCode) {
        LambdaQueryWrapper<BmsPicture> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.eq(BmsPicture::getContentCode ,contentCode);
        return this.count(queryWrapper);
    }

    @Override
    public List<BmsPicture> listByCmsPictureCodeAndContentCode(String code, String contentCode){
        LambdaQueryWrapper<BmsPicture> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.eq(BmsPicture::getCmsPictureCode ,code).eq(BmsPicture::getContentCode, contentCode);
        return this.list(queryWrapper);
    }

    @Override
    public void removeByCmsPictureCodeAndContentCode(String code, String contentCode) {
        LambdaUpdateWrapper<BmsPicture> updateWrapper = Wrappers.lambdaUpdate();
        updateWrapper.eq(BmsPicture::getCmsPictureCode ,code)
                .eq(BmsPicture::getContentCode, contentCode);
        remove(updateWrapper);
    }

    @Override
    public void removeByContentCode(String code) {
        LambdaQueryWrapper<BmsPicture> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.eq(BmsPicture::getContentCode ,code);
        remove(queryWrapper);
    }

    @Override
    public List<BmsPicture> getByContentCode(String code) {
        LambdaQueryWrapper<BmsPicture> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.eq(BmsPicture::getContentCode ,code);
        return this.list(queryWrapper);
    }

    @Override
    public BmsPicture getByCmsPicCodeContentCodeSpId(String pictureCode, String contentCode, Long spId) {
        LambdaQueryWrapper<BmsPicture> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.eq(BmsPicture::getCmsPictureCode ,pictureCode).eq(BmsPicture::getSpId, spId).eq(BmsPicture::getContentCode, contentCode).last(TextConstants.LIMIT_ONE);
        return getOne(queryWrapper);
    }

    @Override
    public List<BmsPicture> listByCmsPictureId(Long cmsPictureId) {
        LambdaQueryWrapper<BmsPicture> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.eq(BmsPicture::getCmsPictureId ,cmsPictureId);
        return this.list(queryWrapper);
    }
}


