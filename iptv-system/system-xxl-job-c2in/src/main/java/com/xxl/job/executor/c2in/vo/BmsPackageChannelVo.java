package com.xxl.job.executor.c2in.vo;

import com.xxl.job.executor.c2in.model.BmsPackageChannel;
import io.swagger.annotations.ApiModel;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;
import java.util.Date;
import lombok.Data;
import com.fasterxml.jackson.annotation.JsonFormat;

/**
 *
 * @author: liuli
 * @date: 2021年9月7日 上午11:31:10
 */

@Data
@AllArgsConstructor
@JsonIgnoreProperties(value = { "handler" })
@ApiModel("BmsPackageChannelVo")
public class BmsPackageChannelVo extends BmsPackageChannel implements java.io.Serializable{
    
}
