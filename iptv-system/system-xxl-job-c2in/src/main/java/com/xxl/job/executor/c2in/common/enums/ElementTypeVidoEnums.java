package com.xxl.job.executor.c2in.common.enums;

public enum ElementTypeVidoEnums {

    Category(1, "Category"),
    Movie(2, "Movie"),
    Package(3, "Package"),
    Picture(4, "Picture"),
    Program(5, "Program"),
    Series(6, "Series"),
    Mappings(9, "Mappings"),
    NullElemet(99, "NullElemet");

    private final Integer code;
    private final String info;


    public static Integer getCodeByInfo(String info) {
        ElementTypeVidoEnums[] actionEnums = values();
        for (ElementTypeVidoEnums element : actionEnums) {
            if (element.getInfo().equals(info)) {
                return element.getCode();
            }
        }
        return null;
    }

    public static String getInfoByCode(Integer code) {
        ElementTypeVidoEnums[] actionEnums = values();
        for (ElementTypeVidoEnums element : actionEnums) {
            if (element.getCode().intValue() == code.intValue()) {
                return element.getInfo();
            }
        }
        return null;
    }

    ElementTypeVidoEnums(Integer code, String info)
    {
        this.code = code;
        this.info = info;
    }

    public Integer getCode()
    {
        return code;
    }

    public String getInfo()
    {
        return info;
    }
}
