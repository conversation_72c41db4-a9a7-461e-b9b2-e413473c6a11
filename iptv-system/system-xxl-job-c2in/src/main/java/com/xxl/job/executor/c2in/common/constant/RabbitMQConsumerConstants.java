package com.xxl.job.executor.c2in.common.constant;

/**
 * @ClassName RabbitMQConsumerConstants
 * @Description TODO
 * <AUTHOR>
 * @Date 2021/11/30 15:58
 * @Version
 */
public class RabbitMQConsumerConstants {

    // 处理成功
    public static final  String ACTION_SUCCESS = "SUCCESS";
    //可以重试的错误，消息重回队列
    public static final  String ACTION_RETRY = "RETRY";
    //无需重试的错误，拒绝消息，并重队列中删除
    public static final  String ACTION_REJECT = "REJECT";
}
