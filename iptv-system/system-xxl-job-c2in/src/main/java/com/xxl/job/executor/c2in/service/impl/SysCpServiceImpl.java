package com.xxl.job.executor.c2in.service.impl;


import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.pukka.iptv.common.base.constant.RedisKeyConstants;
import com.pukka.iptv.common.data.model.sys.SysCp;
import com.pukka.iptv.common.redis.service.RedisService;
import com.xxl.job.executor.c2in.mapper.SysCpMapper;
import com.xxl.job.executor.c2in.service.SysCpService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Service
public class SysCpServiceImpl extends ServiceImpl<SysCpMapper, SysCp> implements SysCpService {
    @Autowired
    RedisService redisService;


    public SysCp cpName(Long cpId) {
        return redisService.getCacheMapValue(RedisKeyConstants.SYS_CP, String.valueOf(cpId));
    }
}
