package com.xxl.job.executor.c2in.controller;

import javax.validation.Valid;

import com.pukka.iptv.common.base.vo.CommonResponse;
import com.pukka.iptv.common.data.model.in.InProgram;
import com.pukka.iptv.common.data.vo.*;
import com.xxl.job.executor.c2in.service.InProgramService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.*;
import org.springframework.beans.factory.annotation.Autowired;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.AllArgsConstructor;

/**
 *
 * @author: tan
 * @date: 2021-8-24 10:49:48
 */

@RestController
@AllArgsConstructor
@RequestMapping(value = "/inProgram", produces = MediaType.APPLICATION_JSON_VALUE)
@Api(tags="inProgram管理")
public class InProgramController {

    @Autowired
    private InProgramService inProgramService;

    @ApiOperation(value = "分页")
    @GetMapping("/page" )
    public CommonResponse<Page> page(@Valid Page page, InProgram inProgram) {
        return  CommonResponse.success(inProgramService.page(page, Wrappers.query(inProgram)));
    }

    @ApiOperation(value = "详情")
    @GetMapping("/getById")
    public CommonResponse<InProgram> getById(@Valid @RequestParam(name = "id", required = true)  Long id) {
        return CommonResponse.success(inProgramService.getById(id));
    }

    @ApiOperation(value = "新增")
    @PostMapping("/save")
    public CommonResponse<Boolean> save(@Valid @RequestBody InProgram inProgram) {
        return  CommonResponse.success(inProgramService.save(inProgram));
    }

    @ApiOperation(value = "修改")
    @PutMapping("/update")
    public CommonResponse<Boolean> updateById(@Valid @RequestBody InProgram inProgram) {
        return CommonResponse.success(inProgramService.updateById(inProgram));
    }

    @ApiOperation(value = "删除")
    @DeleteMapping("/deleteById" )
    public CommonResponse<Boolean> deleteById(@Valid @RequestParam(name = "id", required = true) Long id) {
        return  CommonResponse.success(inProgramService.removeById(id));
    }

    @ApiOperation(value = "批量删除")
    @DeleteMapping("/deleteByIds" )
    public CommonResponse<Boolean> deleteByIds(@Valid @RequestBody IdList idList) {
        return  CommonResponse.success(inProgramService.removeByIds(idList.getIds()));
    }

}
