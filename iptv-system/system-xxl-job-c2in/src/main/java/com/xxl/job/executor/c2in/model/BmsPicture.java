package com.xxl.job.executor.c2in.model;

import com.baomidou.mybatisplus.annotation.*;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.NoArgsConstructor;
import lombok.EqualsAndHashCode;
import lombok.Data;

import java.util.Date;

/**
 *
 * @author: liuli
 * @date: 2021年10月12日 上午9:47:53
 */

@Data
@EqualsAndHashCode
@JsonIgnoreProperties(value = { "handler" })
@NoArgsConstructor
@TableName(value = "bms_picture",autoResultMap=true)
public class BmsPicture extends Model<BmsPicture> implements java.io.Serializable{

    private static final long serialVersionUID = 1L;
	/**主键*/
	@TableField(value = "id")
    @TableId(type = IdType.AUTO)
    @ApiModelProperty(value="主键",dataType="Long",name="id")
    private Long id;
	/**全局唯一标识*/
	@TableField(value = "code", fill = FieldFill.INSERT)
    @ApiModelProperty(value="全局唯一标识",dataType="String",name="code")
    private String code;
	/**图片文件 URL*/
	@TableField(value = "file_url")
    @ApiModelProperty(value="图片文件 URL",dataType="String",name="fileUrl")
    private String fileUrl;
	/**描述*/
	@TableField(value = "description")
    @ApiModelProperty(value="描述",dataType="String",name="description")
    private String description;
	/**1：电影 2：子集 3：电视剧 4：系列片 5：片花 6：直播 7：物理频道 8：栏目 9：产品包 10：节目单 11：图片 12：视频介质 13：节目图片 14：剧集图片 15：产品包图片 16：栏目图片 17：频道图片 18：栏目节目 19：栏目剧集 20：栏目频道 21：产品包节目 22：产品包剧集*/
	@TableField(value = "content_type")
    @ApiModelProperty(value="1：电影 2：子集 3：电视剧 4：系列片 5：片花 6：直播 7：物理频道 8：栏目 9：产品包 10：节目单 11：图片 12：视频介质 13：节目图片 14：剧集图片 15：产品包图片 16：栏目图片 17：频道图片 18：栏目节目 19：栏目剧集 20：栏目频道 21：产品包节目 22：产品包剧集",dataType="Integer",name="contentType")
    private Integer contentType;
	/**内容code，由cms的内容code和bms的栏目、产品包code组成,需指定类型才可用*/
	@TableField(value = "content_code")
    @ApiModelProperty(value="内容code，由cms的内容code和bms的栏目、产品包code组成,需指定类型才可用",dataType="String",name="contentCode")
    private String contentCode;
	/**内容ID*/
	@TableField(value = "bms_content_id")
    @ApiModelProperty(value="内容ID",dataType="Long",name="bmsContentId")
    private Long bmsContentId;
	/**cpId*/
	@TableField(value = "cp_id")
    @ApiModelProperty(value="cpId",dataType="Long",name="cpId")
    private Long cpId;
	/**CP名称*/
	@TableField(value = "cp_name")
    @ApiModelProperty(value="CP名称",dataType="String",name="cpName")
    private String cpName;
	/**创建时间*/
	@TableField(value = "create_time")
    @ApiModelProperty(value="创建时间",dataType="Date",name="createTime")
    @JsonFormat(shape = JsonFormat.Shape.STRING,pattern = "yyyy-MM-dd HH:mm:ss",timezone="GMT+8")
    private Date createTime;
	/**更新时间*/
	@TableField(value = "update_time")
    @ApiModelProperty(value="更新时间",dataType="Date",name="updateTime")
    @JsonFormat(shape = JsonFormat.Shape.STRING,pattern = "yyyy-MM-dd HH:mm:ss",timezone="GMT+8")
    private Date updateTime;
	/**图片类型  0: 缩略图 1: 海报 2: 剧照 3: 图标 4: 标题图 5: 广告图 6: 草图 7: 背景图 9: 频道图片 10: 频道黑白图片 11: 频道 Logo 12: 频道名字图片 20：首页推荐位 1 21：首页推荐位 2 22：首页推荐位 3 23：专区推荐位 1 24：专区推荐位 2 25：专区推荐位 3 26：海报 2 27：专题海报 28：剧照 1 29：底图 99: 其他*/
	@TableField(value = "type")
    @ApiModelProperty(value="图片类型  0: 缩略图 1: 海报 2: 剧照 3: 图标 4: 标题图 5: 广告图 6: 草图 7: 背景图 9: 频道图片 10: 频道黑白图片 11: 频道 Logo 12: 频道名字图片 20：首页推荐位 1 21：首页推荐位 2 22：首页推荐位 3 23：专区推荐位 1 24：专区推荐位 2 25：专区推荐位 3 26：海报 2 27：专题海报 28：剧照 1 29：底图 99: 其他",dataType="Integer",name="type")
    private Integer type;
	/**sp id*/
	@TableField(value = "sp_id")
    @ApiModelProperty(value="sp id",dataType="Long",name="spId")
    private Long spId;
	/**SP名称*/
	@TableField(value = "sp_name")
    @ApiModelProperty(value="SP名称",dataType="String",name="spName")
    private String spName;
	/**状态1：有效 2：删除    */
	@TableField(value = "status")
    @ApiModelProperty(value="状态1：有效 2：删除    ",dataType="Integer",name="status")
    private Integer status;
	/**图片排序*/
	@TableField(value = "sequence")
    @ApiModelProperty(value="图片排序",dataType="Integer",name="sequence")
    private Integer sequence;
	/**分辨率 1280：720 */
	@TableField(value = "ratio")
    @ApiModelProperty(value="分辨率 1280：720 ",dataType="String",name="ratio")
    private String ratio;
	/**发布通道ID，多个ID以英文逗号隔开*/
	@TableField(value = "out_passage_ids")
    @ApiModelProperty(value="发布通道ID，多个ID以英文逗号隔开",dataType="String",name="outPassageIds")
    private String outPassageIds;
	/**分发通道名称以英文逗号 隔开*/
	@TableField(value = "out_passage_names")
    @ApiModelProperty(value="分发通道名称以英文逗号 隔开",dataType="String",name="outPassageNames")
    private String outPassageNames;
	/**发布状态  1：待发布 2：发布中 3：发布成功 4：发布失败 5：待更新 6：更新中 7：更新失败 8：回收中 9：回收成功 10：回收失败*/
	@TableField(value = "publish_status")
    @ApiModelProperty(value="发布状态  1：待发布 2：发布中 3：发布成功 4：发布失败 5：待更新 6：更新中 7：更新失败 8：回收中 9：回收成功 10：回收失败",dataType="Integer",name="publishStatus")
    private Integer publishStatus;
	/**发布时间*/
	@TableField(value = "publish_time")
    @ApiModelProperty(value="发布时间",dataType="String",name="publishTime")
    private String publishTime;
	/**发布描述*/
	@TableField(value = "publish_description")
    @ApiModelProperty(value="发布描述",dataType="String",name="publishDescription")
    private String publishDescription;
	/**cms_picture主键id*/
	@TableField(value = "cms_picture_id")
    @ApiModelProperty(value="cms_picture主键id",dataType="Long",name="cmsPictureId")
    private Long cmsPictureId;
	/**cms_picture code*/
	@TableField(value = "cms_picture_code")
    @ApiModelProperty(value="cms_picture code",dataType="String",name="cmsPictureCode")
    private String cmsPictureCode;
    /**所属存储*/
    @TableField(value = "storage_name")
    @ApiModelProperty(value="所属存储",dataType="String",name="storageName")
    private String storageName;
    /**存储ID*/
    @TableField(value = "storage_id")
    @ApiModelProperty(value="存储ID",dataType="Long",name="storageId")
    private Long storageId;
}
