package com.xxl.job.executor.c2in.controller;

import javax.validation.Valid;

import com.pukka.iptv.common.base.vo.CommonResponse;
import com.pukka.iptv.common.data.model.in.InSeries;
import com.pukka.iptv.common.data.vo.*;
import com.xxl.job.executor.c2in.service.InSeriesService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.*;
import org.springframework.beans.factory.annotation.Autowired;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.AllArgsConstructor;

/**
 *
 * @author: tan
 * @date: 2021-8-24 10:50:13
 */

@RestController
@AllArgsConstructor
@RequestMapping(value = "/inSeries", produces = MediaType.APPLICATION_JSON_VALUE)
@Api(tags="inSeries管理")
public class InSeriesController {

    @Autowired
    private InSeriesService inSeriesService;

    @ApiOperation(value = "分页")
    @GetMapping("/page" )
    public CommonResponse<Page> page(@Valid Page page, InSeries inSeries) {
        return  CommonResponse.success(inSeriesService.page(page, Wrappers.query(inSeries)));
    }

    @ApiOperation(value = "详情")
    @GetMapping("/getById")
    public CommonResponse<InSeries> getById(@Valid @RequestParam(name = "id", required = true)  Long id) {
        return CommonResponse.success(inSeriesService.getById(id));
    }

    @ApiOperation(value = "新增")
    @PostMapping("/save")
    public CommonResponse<Boolean> save(@Valid @RequestBody InSeries inSeries) {
        return  CommonResponse.success(inSeriesService.save(inSeries));
    }

    @ApiOperation(value = "修改")
    @PutMapping("/update")
    public CommonResponse<Boolean> updateById(@Valid @RequestBody InSeries inSeries) {
        return CommonResponse.success(inSeriesService.updateById(inSeries));
    }

    @ApiOperation(value = "删除")
    @DeleteMapping("/deleteById" )
    public CommonResponse<Boolean> deleteById(@Valid @RequestParam(name = "id", required = true) Long id) {
        return  CommonResponse.success(inSeriesService.removeById(id));
    }

    @ApiOperation(value = "批量删除")
    @DeleteMapping("/deleteByIds" )
    public CommonResponse<Boolean> deleteByIds(@Valid @RequestBody IdList idList) {
        return  CommonResponse.success(inSeriesService.removeByIds(idList.getIds()));
    }

}
