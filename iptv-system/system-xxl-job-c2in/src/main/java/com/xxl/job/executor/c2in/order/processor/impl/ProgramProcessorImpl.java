package com.xxl.job.executor.c2in.order.processor.impl;

import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.pukka.iptv.common.api.feign.cms.CmsProgramFeignClient;
import com.pukka.iptv.common.api.feign.copyright.RuleProhibitFeignClient;
import com.pukka.iptv.common.base.constant.RedisKeyConstants;
import com.pukka.iptv.common.base.enums.*;
import com.pukka.iptv.common.base.vo.CommonResponse;
import com.pukka.iptv.common.data.dto.CmsProgramDto;
import com.pukka.iptv.common.data.model.cms.CmsProgram;
import com.pukka.iptv.common.data.model.cms.CmsResource;
import com.pukka.iptv.common.data.model.in.InOrder;
import com.pukka.iptv.common.data.model.in.InProgram;
import com.pukka.iptv.common.data.model.sys.SysCp;
import com.pukka.iptv.common.data.vo.cms.CmsProgramVO;
import com.xxl.job.executor.c2in.common.constant.TableNameConstants;
import com.xxl.job.executor.c2in.common.constant.TextConstants;
import com.xxl.job.executor.c2in.common.enums.LockStatusEnums;
import com.xxl.job.executor.c2in.common.enums.PublishStatusEnums;
import com.xxl.job.executor.c2in.common.enums.SeriesTypeEnums;
import com.xxl.job.executor.c2in.common.exception.ConfigException;
import com.xxl.job.executor.c2in.common.util.StatusUtils;
import com.xxl.job.executor.c2in.model.BmsContent;
import com.xxl.job.executor.c2in.model.BmsProgram;
import com.xxl.job.executor.c2in.service.*;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @Date 2021/9/2 9:19
 * @Description
 */
@Slf4j
@Component(TableNameConstants.IN_PROGRAM_TABLE)
public class ProgramProcessorImpl extends AbsOrderProcessor {

    @Autowired
    CmsProgramService cmsProgramService;

    @Autowired
    BmsProgramService bmsProgramService;

    @Autowired
    InProgramService inProgramService;

    @Autowired
    InOrderMappingsService inOrderMappingsService;

    @Autowired
    BmsContentService bmsContentService;

    @Autowired
    CmsSeriesService cmsSeriesService;

    @Autowired
    CmsPictureService cmsPictureService;

    @Autowired
    BmsPictureService bmsPictureService;

    @Autowired
    CmsMovieService cmsMovieService;

    @Autowired
    BmsCategoryContentService bmsCategoryContentService;

    @Autowired
    BmsPackageContentService bmsPackageContentService;

    @Autowired
    CmsProgramFeignClient cmsProgramFeignClient;

    @Autowired
    CmsResourceService cmsResourceService;

    @Autowired
    RuleProhibitFeignClient ruleProhibitFeignClient;


    @Override
    public void updateProcess(Object data, InOrder order) {
        InProgram inProgram = (InProgram) data;
        //20230220 vspCode直接从cp中取code值
        if (ObjectUtils.isEmpty(sysCp)) {
            log.warn("当前注入工单处理,工单ID:{},sysCp信息为空，重新获取,内容:{}", order.getId(), JSON.toJSONString(inProgram));
            sysCp = getSysCpByOrder(order);
            log.info("当前注入工单处理,工单ID:{},sysCp信息:{}", order.getId(), JSON.toJSONString(sysCp));
        }
        inProgram.setVspCode(sysCp.getCode());
        CmsProgram cmsProgram = cmsProgramService.getByCode(inProgram.getCode());
        if (cmsProgram == null) {
            log.warn("对象更新失败,对象不存在 cmsProgramCode:{}", inProgram.getCode());
            registProcess(data, order);
            return;
        }
        if (!sysInPassage.getCpId().equals(cmsProgram.getCpId())) {
            log.error("对象更新失败,cmsProgram不属于当前cp, cmsProgram:{}", cmsProgram.getCode());
            updateStatus(order, inProgram, StatusUtils.fail("对象更新失败,cmsProgram不属于当前cp"));
            return;
        }
        CmsProgramDto cmsProgramDto = new CmsProgramDto(inProgram);
        cmsProgramDto.setId(cmsProgram.getId());
        cmsProgramDto.setCpId(cmsProgram.getCpId());
        cmsProgramDto.setRequestResource(SourceEnum.SYSWORK.getValue());
        if (SeriesTypeEnums.SUB_SET.getCode().equals(cmsProgram.getSeriesFlag())) {
            //查询源片id
            CmsProgramDto resourceIds =
                    cmsResourceService.listByContentIdAndContentType(cmsProgramDto.getId(),2);
            cmsProgramDto.setMovIds(resourceIds.getMovIds());
            cmsProgramDto.setPreviewIds(resourceIds.getPreviewIds());
            // 调用子集更新接口,不使用feign,暂不修改
            CommonResponse commonResponse = cmsProgramFeignClient.subsetUpdate(cmsProgramDto);
            if (TextConstants.OK.equals(commonResponse.getCode())) {
                updateStatus(order, inProgram, StatusUtils.success());
            } else {
                log.info("子集update失败 code:{}, Response:{}", inProgram.getCode(), commonResponse.toString());
                updateStatus(order, inProgram, StatusUtils.fail(commonResponse.getMessage()));
            }

        } else if (SeriesTypeEnums.SIMPLE_SET.getCode().equals(cmsProgram.getSeriesFlag())) {
            //查询源片id
            CmsProgramDto resourceIds =
                    cmsResourceService.listByContentIdAndContentType(cmsProgramDto.getId(),1);
            cmsProgramDto.setMovIds(resourceIds.getMovIds());
            cmsProgramDto.setPreviewIds(resourceIds.getPreviewIds());
            //设置违禁状态为检测中
            cmsProgramDto.setIsProhibit(IsProhibitEnum.ING.getValue());
            CommonResponse commonResponse = cmsProgramFeignClient.simpleSetUpdate(cmsProgramDto);
            if (TextConstants.OK.equals(commonResponse.getCode())) {
                //违禁检测
                checkRuleProhibit(cmsProgramDto.getCode());
                updateStatus(order, inProgram, StatusUtils.success());
            } else {
                log.info("单集update失败 code:{}, Response:{}", inProgram.getCode(), commonResponse.toString());
                updateStatus(order, inProgram, StatusUtils.fail(commonResponse.getMessage()));
            }
        }
    }

    @Override
    public void registProcess(Object data, InOrder order) {
        InProgram inProgram = (InProgram) data;
        //20230220 vspCode直接从cp中取code值
        if (ObjectUtils.isEmpty(sysCp)) {
            log.warn("当前注入工单处理,工单ID:{},sysCp信息为空，重新获取,内容:{}", order.getId(), JSON.toJSONString(inProgram));
            sysCp = getSysCpByOrder(order);
            log.info("当前注入工单处理,工单ID:{},sysCp信息:{}", order.getId(), JSON.toJSONString(sysCp));
        }
        inProgram.setVspCode(sysCp.getCode());

        initCpByVspCode(inProgram.getVspCode());
        Long cmsProgramNum = cmsProgramService.countByCode(inProgram.getCode());
        if (cmsProgramNum == 0) {
            CmsProgramDto cmsProgramDto = new CmsProgramDto(inProgram);
            cmsProgramDto.setCpId(sysCp.getId());
            cmsProgramDto.setCpName(sysCp.getName());
            cmsProgramDto.setRequestResource(SourceEnum.SYSWORK.getValue());
            cmsProgramDto.setCspId(sysInPassage.getCspId());
            if (StringUtils.isEmpty(cmsProgramDto.getOriginalName())) {
                cmsProgramDto.setOriginalName(cmsProgramDto.getName());
            }
            if (SeriesTypeEnums.SUB_SET.getCode().equals(cmsProgramDto.getSeriesFlag())) {
                CommonResponse commonResponse = cmsProgramFeignClient.subsetSave(cmsProgramDto);
                if (TextConstants.OK.equals(commonResponse.getCode())) {
                    updateStatus(order, inProgram, StatusUtils.success());
                } else {
                    log.info("子集regist失败 code:{}, Response:{}", inProgram.getCode(), commonResponse.toString());
                    updateStatus(order, inProgram, StatusUtils.fail(commonResponse.getMessage()));
                }
            } else if (SeriesTypeEnums.SIMPLE_SET.getCode().equals(cmsProgramDto.getSeriesFlag())) {
                //设置违禁状态为检测中
                cmsProgramDto.setIsProhibit(IsProhibitEnum.ING.getValue());
                CommonResponse commonResponse = cmsProgramFeignClient.simpleSetSave(cmsProgramDto);
                if (TextConstants.OK.equals(commonResponse.getCode())) {
                    //违禁检测
                    checkRuleProhibit(cmsProgramDto.getCode());
                    updateStatus(order, inProgram, StatusUtils.success());
                } else {
                    log.info("单集regist失败 code:{}, Response:{}", inProgram.getCode(), commonResponse.toString());
                    updateStatus(order, inProgram, StatusUtils.fail(commonResponse.getMessage()));
                }
            } else {
                log.info("program新增失败 code:{}", inProgram.getCode());
                updateStatus(order, inProgram, StatusUtils.fail("SeriesFlag为空"));
            }
        } else {
            updateProcess(data, order);
            log.warn("CmsProgram 表已存在 code为 {} 的对象", inProgram.getCode());
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void deleteProcess(Object data, InOrder order) {
        InProgram inProgram = (InProgram) data;
        CmsProgram cmsProgram = cmsProgramService.getByCode(inProgram.getCode());
        if (cmsProgram == null) {
            log.warn("对象不存在 删除失败, code :{} cpId:{}", inProgram.getCode(), sysInPassage.getCpId());
            updateStatus(order, inProgram, StatusUtils.success());
            return;
        }
        if (!sysInPassage.getCpId().equals(cmsProgram.getCpId())) {
            log.error("对象删除失败,cmsProgram不属于当前cp, cmsProgram:{}", cmsProgram.getCode());
            updateStatus(order, inProgram, StatusUtils.fail("对象删除失败,cmsProgram不属于当前cp"));
            return;
        }
        // 锁定不能删除
        if (LockStatusEnums.LOCKED.getCode().equals(cmsProgram.getLockStatus())) {
            log.error("删除失败,Program对象已锁定, code :{}", cmsProgram.getCode());
            updateStatus(order, inProgram, StatusUtils.fail("删除失败,Program对象已锁定"));
            return;
        }
        // 判断单集还是子集
        if (SeriesTypeEnums.SUB_SET.getCode().equals(cmsProgram.getSeriesFlag())) {
            // 子集
            // 检查bms_Program表
            List<BmsProgram> bmsProgramList = bmsProgramService.listByCmsContentCode(inProgram.getCode());
            for (BmsProgram bmsProgram : bmsProgramList) {
                // 不是待发布 并且 不是回收成功 则不能删除
                if (!PublishStatusEnums.checkDeleteStatus(bmsProgram.getPublishStatus())) {
                    log.error("删除失败,发布状态不是待发布和回收成功发布失败, code :{} spId:{}", inProgram.getCode(), bmsProgram.getSpId());
                    updateStatus(order, inProgram, StatusUtils.fail("删除失败,发布状态不是待发布和回收成功发布失败"));
                    return;
                }
            }

            // 删cms_program 和 bms_program
            cmsProgramService.removeByCode(inProgram.getCode());
            bmsProgramService.removeByCmsContentCode(inProgram.getCode());
            // 删除图片
            cmsPictureService.removeByContentCode(inProgram.getCode());
            // 删除视频
            if (StringUtils.isNotBlank(cmsProgram.getResourcePreviewCode())) {
                cmsMovieService.removeByResourceCode(cmsProgram.getResourcePreviewCode());
                cmsResourceService.removeByCode(cmsProgram.getResourcePreviewCode());
            }
            if (StringUtils.isNotBlank(cmsProgram.getResourceReleaseCode())) {
                cmsMovieService.removeByResourceCode(cmsProgram.getResourceReleaseCode());
                cmsResourceService.removeByCode(cmsProgram.getResourceReleaseCode());
            }
            updateStatus(order, inProgram, StatusUtils.success());

        } else if (SeriesTypeEnums.SIMPLE_SET.getCode().equals(cmsProgram.getSeriesFlag())) {
            // 单集
            // 检查bms_content表
            List<BmsContent> bmsContentList = bmsContentService.listByCmsContentCode(cmsProgram.getCode());
            for (BmsContent bmsContent : bmsContentList) {
                // 不是待发布 并且 不是回收成功 则不能删除
                if (!PublishStatusEnums.checkDeleteStatus(bmsContent.getPublishStatus())) {
                    log.error("删除失败,发布状态不是待发布和回收成功发布失败, code :{} spId:{}", inProgram.getCode(), bmsContent.getSpId());
                    updateStatus(order, inProgram, StatusUtils.fail("删除失败,发布状态不是待发布和回收成功发布失败"));
                    return;
                }
                // 锁定不能删除
                if (LockStatusEnums.LOCKED.getCode().equals(bmsContent.getLockStatus())) {
                    log.error("删除失败,内容已锁定, code :{} spId:{}", inProgram.getCode(), bmsContent.getSpId());
                    updateStatus(order, inProgram, StatusUtils.fail("删除失败,内容已锁定"));
                    return;
                }
            }
            // 删cms_program 和 bms_content
            cmsProgramService.removeByCode(inProgram.getCode());
            bmsContentService.removeByCmsContentCode(inProgram.getCode());
            // 删关系
            bmsCategoryContentService.removeByContentCode(inProgram.getCode());
            bmsPackageContentService.removebyContentCode(inProgram.getCode());
            // 删除图片
            cmsPictureService.removeByContentCode(inProgram.getCode());
            // 删除视频
            if (StringUtils.isNotBlank(cmsProgram.getResourcePreviewCode())) {
                cmsMovieService.removeByResourceCode(cmsProgram.getResourcePreviewCode());
                cmsResourceService.removeByCode(cmsProgram.getResourcePreviewCode());
            }
            if (StringUtils.isNotBlank(cmsProgram.getResourceReleaseCode())) {
                cmsMovieService.removeByResourceCode(cmsProgram.getResourceReleaseCode());
                cmsResourceService.removeByCode(cmsProgram.getResourceReleaseCode());
            }
            updateStatus(order, inProgram, StatusUtils.success());
        } else {
            log.error("删除失败,SeriesFlag为空.code :{}", inProgram.getCode());
            updateStatus(order, inProgram, StatusUtils.fail("删除失败,SeriesFlag为空"));
        }
    }

    /**
     * 工单表状态更新
     *
     * @param order     主工单
     * @param inProgram 子工单
     * @param status    处理结果
     */
    private void updateStatus(InOrder order, InProgram inProgram, StatusUtils.Status status) {
        inProgramService.updateStatusById(inProgram.getId(), status);
        inOrderMappingsService.updateStatus(order.getCorrelateId(), TableNameConstants.IN_PROGRAM_TABLE, inProgram.getId(), status);
    }

    @Override
    public void updateStatus(InOrder order, Object data, StatusUtils.Status status) {
        InProgram inProgram = (InProgram) data;
        // 子工单对象直接通过id更新状态
        updateStatus(order, inProgram, status);
    }

    @Override
    public boolean deleteProcessIsExist(Object data, InOrder order) {
        return true;
    }

    /**
     * 违禁检测
     *
     * @param code
     */
    private void checkRuleProhibit(String code) {
        CmsProgram cmsProgram = cmsProgramService.getByCode(code);
        try {
            if (ObjectUtils.isEmpty(cmsProgram)) {
                log.warn("注入单集违禁检测查询关联媒资信息为空，单集code:{}", code);
                return;
            }
            CmsProgramVO cmsProgramVO = new CmsProgramVO(cmsProgram, ProhibitPointEnum.INSERT);
            //添加违禁检测
            CommonResponse<CmsProgram> ruleProhibitResponseCommonResponse = ruleProhibitFeignClient.ruleProhibitProgram(cmsProgramVO);
            CmsProgram ruleProhibitResponse = ruleProhibitResponseCommonResponse.getData();
            if (ObjectUtils.isNotEmpty(ruleProhibitResponse)) {
                cmsProgram.setProhibitStatus(ruleProhibitResponse.getProhibitStatus());
                cmsProgram.setIsProhibit(ruleProhibitResponse.getIsProhibit());
            } else {
                cmsProgram.setIsProhibit(IsProhibitEnum.ING.getValue());
                cmsProgram.setProhibitStatus(ProhibitStatusEnum.NO.getValue());
                log.warn("注入单集违禁检测返回结果为空，单集信息:{}", cmsProgram);
            }

        } catch (Exception exception) {
            log.error("注入单集违禁检测错误，单集信息:{}, 错误信息:{}", cmsProgram, exception);
        } finally {
            if (ObjectUtils.isNotEmpty(cmsProgram)) {
                CmsProgramDto cmsProgramDto = new CmsProgramDto(cmsProgram);
                CommonResponse commonResponse1 = cmsProgramFeignClient.simpleSetUpdate(cmsProgramDto);
                if (TextConstants.OK.equals(commonResponse1.getCode())) {
                    log.info("注入单集违禁检测入库成功,单集信息:{}", cmsProgram);
                } else {
                    log.warn("注入单集违禁检测入库失败,单集信息:{}", cmsProgram);
                }
            }
        }
    }
}
