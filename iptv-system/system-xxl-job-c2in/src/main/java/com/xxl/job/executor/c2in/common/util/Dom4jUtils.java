package com.xxl.job.executor.c2in.common.util;

import org.apache.commons.logging.Log;
import org.apache.commons.logging.LogFactory;
import org.dom4j.*;
import org.dom4j.io.OutputFormat;
import org.dom4j.io.SAXReader;
import org.dom4j.io.XMLWriter;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.File;
import java.io.FileOutputStream;
import java.io.IOException;
import java.util.ArrayList;
import java.util.Iterator;
import java.util.List;

public class Dom4jUtils {
	//从文件读取XML，输入文件名，返回XML文档
    public static Document read(String filePath){
    	Document document =null;
    	try{
	       SAXReader reader = new SAXReader();
	       document = reader.read(new File(filePath));
    	}catch(Exception ex){
    		ex.printStackTrace();
    	}
       return document;
    }
    private final static Logger LOGGER = LoggerFactory.getLogger(Dom4jUtils.class);
    //从文件读取XML，输入文件名，返回XML文档
    public static Document readUrl(String url){
    	Document document =null;
    	try{
	       SAXReader reader = new SAXReader();
	       document = reader.read(url);
    	}catch(Exception ex){
    	    LOGGER.error("save readUrl",ex);
    	    ex.printStackTrace();
    	}
       return document;
    }
    
    //从字符串读取XML，输入XML字符串，返回XML元素
    public static Element getRootElementByStr(String content) throws DocumentException{
    	Document document = DocumentHelper.parseText(content); 
    	Element element = null;
    	if(document!=null){
    		element=getRootElement(document);
    	}
       return element;
    }
    
    //获取根元素
    public static Element getRootElement(Document doc){
       return doc.getRootElement();
    }
    
    //获取根元素根据路径
    public static Element getRootElementByUrl(String url){
    	Document document = readUrl(url);
        return document.getRootElement();
     }
    
    //获取根元素根据路径
    public static Element getRootElement(String filePath){
    	Document document = read(filePath);
        return document.getRootElement();
     }
    
    //搜索子元素
    public static List findNodes(Element ele,String nodeStr){
    	List list = ele.selectNodes(nodeStr); //如foo/bar
    	return list;
    }
    
    //搜索单个子元素
    public static Node findNode(Element ele,String nodeStr){
    	Node node = ele.selectSingleNode(nodeStr); //如foo/bar/author
    	return node;
    }
    
    //XML转字符串
    public static String xml2Str(Document doc){
    	String text = doc.asXML();
    	return text;
    }
    
    //字符串转XML
    public static Document str2Xml(String str){
        Document document=null;
		try {
			document = DocumentHelper.parseText(str);
		} catch (DocumentException e) {
			e.printStackTrace();
		}
    	return document;
    }
    
    //获取子元素
    public static List<ElementXMLMap> getElements(Element ele){
    	List<ElementXMLMap> lst = new ArrayList();
    	// 枚举所有子节点
        for (Iterator i = ele.elementIterator(); i.hasNext();){
           Element element = (Element)i.next();
           ElementXMLMap mm = new ElementXMLMap(element.getName(),element.getTextTrim());
           lst.add(mm);
        }
        return lst;
    }
    
    //获取子元素
    public static List<ElementXMLMap> getElements(Element ele, String attrName){
    	List<ElementXMLMap> lst = new ArrayList();
    	// 枚举所有子节点
        for (Iterator i = ele.elementIterator(); i.hasNext();){
           Element element = (Element)i.next();
           ElementXMLMap mm = new ElementXMLMap(element.attribute(attrName).getText(),element.getTextTrim());
           lst.add(mm);
        }
        return lst;
    }
    
    //获取所有属性
    public static List<ElementXMLMap> getAttributes(Element ele){
    	List<ElementXMLMap> lst = new ArrayList();
    	// 枚举所有子节点
        for (Iterator i = ele.attributeIterator(); i.hasNext();){
           Attribute attribute = (Attribute) i.next();
           ElementXMLMap mm = new ElementXMLMap(attribute.getName(),attribute.getText());
           lst.add(mm);
        }
        return lst;
    }
    
    //创建文档
    public static Document createDocument() {
        Document document = DocumentHelper.createDocument();
        return document;
    }
    
    //创建元素
    public static Element createElement(String eleName) {
    	Element ele = DocumentHelper.createElement(eleName);
        return ele;
    }
    
    //创建元素
    public static Element createAdiElement() {
    	Element root = DocumentHelper.createElement("ADI");
		root.addAttribute("StaffID","48").addAttribute("xmlns:xsi","http://www.w3.org/2001/XMLSchema-instance");
        return root;
    }
    
    //获取生成内容
    public static String getOutXmlContent(Element root){
    	Document document = DocumentHelper.createDocument(root);
    	return document.asXML();
    }
    
    //写文件
    public static boolean writeFile(Element root,String filePath){
    	Document document = DocumentHelper.createDocument(root);
    	return writeFile(document,filePath);
    }
    
    //写文件
    public static boolean writeFile(Document doc,String filePath){
    	boolean isWrite = false;
    	Log log = LogFactory.getLog(Dom4jUtils.class);
    	log.info(doc.toString());
		try {
			//美化格式
	        OutputFormat format = OutputFormat.createPrettyPrint();
	        format.setEncoding("UTF-8");
			XMLWriter writer = new XMLWriter(new FileOutputStream(filePath),format);
			writer.write(doc);
			writer.close();			
	    	isWrite = true;
		} catch (IOException e) {
			e.printStackTrace();
		}
    	return isWrite;
    }
    
}
