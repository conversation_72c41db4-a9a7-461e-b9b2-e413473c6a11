package com.xxl.job.executor.c2in.common.parse.impl;

import com.google.common.base.Strings;
import com.pukka.iptv.common.core.util.StringUtils;
import com.pukka.iptv.common.data.model.in.InSchedule;
import com.xxl.job.executor.c2in.common.enums.ActionEnums;
import com.xxl.job.executor.c2in.common.exception.ParserException;
import com.xxl.job.executor.c2in.common.parse.IParse;
import com.xxl.job.executor.c2in.common.util.ManageHelper;
import org.apache.commons.lang3.math.NumberUtils;
import org.dom4j.Attribute;
import org.dom4j.Element;

import java.util.Date;
import java.util.Iterator;
import java.util.List;

/**
 * @ClassName CategoryParseImpl
 * @Description 编排目录信息
 * <AUTHOR>
 * @Date 2021/8/27 10:09
 * @Version
 */
public class ScheduleParseImpl implements IParse<Element, InSchedule> {

    @Override
    public InSchedule get(Element arg) {
        if (arg == null) {
            return null;
        }
        Attribute actionAttr = arg.attribute("Action");
        if (actionAttr == null) {
            throw new ParserException("Catalog 缺少'Action'属性值");
        }

        InSchedule ret = new InSchedule();

        ret.setAction(ActionEnums.getCodeByInfo(actionAttr.getValue()));
        Attribute codeAttr = arg.attribute("Code");
        if (codeAttr == null || Strings.isNullOrEmpty(codeAttr.getValue())) {
            throw new ParserException("Catalog 缺少'Code'属性值");
        }
        ret.setCode(codeAttr.getValue());

        Attribute idAttr = arg.attribute("ID");
        if (idAttr == null || Strings.isNullOrEmpty(idAttr.getValue())) {
            throw new ParserException("Catalog 缺少'ID'属性值");
        }
        ret.setCorrelateId(idAttr.getValue());

        List<?> propertysEle = arg.elements("Property");
        if (propertysEle == null)
            return ret;

        Attribute nameAttr;
        String text;
        for (Iterator<?> iter = propertysEle.iterator(); iter.hasNext(); ) {
            Element property = (Element) iter.next();
            nameAttr = property.attribute("Name");
            if (nameAttr == null || StringUtils.isEmpty(nameAttr.getValue())) {
                if(ManageHelper.isRegistXML(ret.getAction())){
                    throw new ParserException("Schedule属性列表中缺少'Name'属性名称");
                }

            }
            text = property.getTextTrim();
            String value = nameAttr.getValue();
            if (value.equals("ChannelCode")) {
                ret.setChannelCode(text);
                continue;
            }
            if (value.equals("ChannelID")) {
                ret.setChannelId(NumberUtils.toLong(text));
                continue;
            }
            if (value.equals("ProgramName")) {
                ret.setProgramName(text);
                continue;
            }
            if (value.equals("StartDate")) {
                ret.setStartDate(text);
                continue;
            }
            if (value.equals("StartTime")) {
                ret.setStartTime(text);
            }
            if (value.equals("Duration")) {
                ret.setDuration(text);
            }
            if (value.equals("StorageDuration")) {
                ret.setStorageDuration(NumberUtils.toInt(text));
            }
            if (value.equals("Status")) {
                ret.setStatus(NumberUtils.toInt(text, 1));
            }
            if (value.equals("Description")) {
                ret.setDescription(text);
            }
            if (value.equals("Genre")) {
                ret.setGenre(text);
            }
            if (value.equals("Result")) {
                ret.setResult(NumberUtils.toInt(text));
            }
            if (value.equals("ErrorDescription")) {
                ret.setErrorDescription(text);
            }

        }
        if (ret.getStatus() == null) {
            if(ManageHelper.isRegistXML(ret.getAction())){
                throw new ParserException("Catalog属性列表中缺少'Status'属性值");
            }
        }
        ret.setCreateTime(new Date());
        return ret;
    }
}
