package com.xxl.job.executor.c2in.service.impl;


import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.pukka.iptv.common.base.constant.RedisKeyConstants;
import com.pukka.iptv.common.core.util.ResultData;
import com.pukka.iptv.common.core.util.StringUtils;
import com.pukka.iptv.common.data.dto.DateFormatCompletionDto;
import com.pukka.iptv.common.data.model.in.InOrder;
import com.pukka.iptv.common.data.model.in.InResult;
import com.pukka.iptv.common.data.model.sys.SysCp;
import com.pukka.iptv.common.data.model.sys.SysInPassage;
import com.pukka.iptv.common.data.model.sys.SysStorage;
import com.pukka.iptv.common.data.model.sys.SysStorageDirctory;
import com.pukka.iptv.common.redis.service.RedisService;
import com.xxl.job.executor.c2in.common.constant.*;
import com.xxl.job.executor.c2in.common.enums.*;
import com.xxl.job.executor.c2in.common.util.*;
import com.xxl.job.executor.c2in.dto.InorderDto;
import com.xxl.job.executor.c2in.mapper.*;
import com.xxl.job.executor.c2in.service.*;
import com.xxl.job.executor.util.RedisOperationUtil;
import com.xxl.job.executor.util.TimeResolutionUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.io.ByteArrayInputStream;
import java.text.SimpleDateFormat;
import java.util.*;


/**
 * @author: tan
 * @date: 2021-8-24 10:46:24
 */

@Service
@Slf4j
public class InOrderServiceImpl extends ServiceImpl<InOrderMapper, InOrder> implements InOrderService {


    @Autowired(required = false)
    private InOrderMapper inOrderMapper;

    @Autowired
    private InResultService inResultService;

    @Autowired
    private RedisService redisService;


    @Override
    @Transactional(rollbackFor = Exception.class)
    public ResultData saveByCp(String cspId, String lspId, String correlateId, String cmdFileUrl,
                               String inPassageId, String inPassageName) {

        ResultData resultData = new ResultData();
        //入库前，需要对CSPID与LSPID进行验证
        if (StringUtils.isEmpty(cspId)) {
            resultData.setResultCode(-1);
            resultData.setResultDesc(" CSPID is null!");
            log.error(" CSPID is null!");
            return resultData;
        } else {
            //需要系统判断cp是否存在

        }
        if (StringUtils.isEmpty(lspId)) {
            resultData.setResultCode(-1);
            resultData.setResultDesc(" LSPID is null!");
            log.error(" LSPID is null!");
            return resultData;
        } else {
            //需要系统判断lspid是否存在

        }

        if (StringUtils.isEmpty(correlateId)) {
            resultData.setResultCode(-1);
            resultData.setResultDesc(" correlateID is null!");
            log.error(" correlateID is null!");
            return resultData;
        }
        if (StringUtils.isEmpty(cmdFileUrl)) {
            resultData.setResultCode(-1);
            resultData.setResultDesc(" cmdFileURL is null!");
            log.error(" cmdFileURL is null!");
            return resultData;
        }


        String inOrderId = ManageHelper.getObjectID("CmdIn");
        InOrder inOrder = new InOrder();
        inOrder.setId(inOrderId);
        inOrder.setCspId(cspId);
        inOrder.setLspId(lspId);
        inOrder.setCorrelateId(correlateId);
        inOrder.setCmdFileUrl(cmdFileUrl);
        inOrder.setResult(InOrderResultEnums.IN.getCode());
        inOrder.setErrorDescription(TableNameConstants.SUCCESS);
        inOrder.setStatus(InOrderStatusEnums.INJECT.getCode());
        inOrder.setInPassageId(Long.valueOf(inPassageId));
        inOrder.setInPassageName(inPassageName);
        inOrderMapper.insert(inOrder);

        resultData.setResultCode(InOrderResultEnums.IN.getCode());
        resultData.setResultDesc(TableNameConstants.SUCCESS);

        return resultData;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void inOrderHandler(Integer status, InOrder inOrder, String message, Integer cmdResult, Integer cmdStatus) {

        inOrder.setStatus(status);
        inOrder.setResult(cmdStatus);
        if (StringUtils.isNotEmpty(message)) {
            message = StringUtils.substring(message.replaceAll("null", ""), 0);
        }
        if (status.equals(InOrderResultEnums.TODATABASE_SUCCESS.getCode())) {
            message = "success";
        }
        inOrder.setErrorDescription(message);
        inOrderMapper.updateById(inOrder);

        QueryWrapper queryWrapper = new QueryWrapper();
        queryWrapper.eq("csp_id", inOrder.getCspId());
        queryWrapper.eq("lsp_id", inOrder.getLspId());
        queryWrapper.eq("correlate_id", inOrder.getCorrelateId());
        queryWrapper.eq("in_order_id", inOrder.getId());


        InResult inResult = inResultService.getOne(queryWrapper);
        if (inResult != null) {
            log.info("in_result反馈记录表已存在，不重复插入：" + inOrder.getCorrelateId() + ";" + cmdResult);
        } else {
            String objectId = ManageHelper.getObjectID("CmdInR");
//            String xmlUrl = getReply(objectId, cmdResult, message, inOrder.getCspId());
            log.info("in_result反馈记录表：" + objectId);
            String xmlUrl = getReply(objectId, cmdResult, message, String.valueOf(inOrder.getInPassageId()));
            log.info("in_result反馈记录表 xmlUrl：" + xmlUrl);
            inResult = new InResult();
            inResult.setInResultId(objectId);
            inResult.setInOrderId(inOrder.getId());
            inResult.setCorrelateId(inOrder.getCorrelateId());
            inResult.setLspId(inOrder.getLspId());
            inResult.setCspId(inOrder.getCspId());
            inResult.setOrderStatus(cmdStatus);
            inResult.setRequestTime(DateUtils.parseDateToStr(DateUtils.YYYY_MM_DD_HH_MM_SS, inOrder.getCreateTime()));
            inResult.setResult(cmdResult);
            if (xmlUrl != null) {
                inResult.setResultFileUrl(xmlUrl);
            }
            inResultService.save(inResult);
        }
        //todo 待生成反馈xml文件

    }

    @Override
    public void inOrder2InResult(Integer status, InOrder inOrder) {
        inOrder.setStatus(status);

        LambdaQueryWrapper<InResult> inOrderLambdaQueryWrapper = new LambdaQueryWrapper<>();
        inOrderLambdaQueryWrapper.eq(InResult::getCorrelateId, inOrder.getCorrelateId()).
                eq(InResult::getCspId, inOrder.getCspId()).
                eq(InResult::getLspId, inOrder.getLspId()).
                eq(InResult::getInOrderId, inOrder.getId());

        InResult inResult = inResultService.getOne(inOrderLambdaQueryWrapper);
        if (inResult != null) {
            log.info("in_result反馈记录表已存在，不重复插入：" + inOrder.getCorrelateId());
        } else {
            String objectId = ManageHelper.getObjectID("CmdInR");
            inResult = new InResult();
            inResult.setInResultId(objectId);
            inResult.setInOrderId(inOrder.getId());
            inResult.setCorrelateId(inOrder.getCorrelateId());
            inResult.setLspId(inOrder.getLspId());
            inResult.setCspId(inOrder.getCspId());
            inResult.setOrderStatus(status);
            inResult.setRequestTime(DateUtils.parseDateToStr(DateUtils.YYYY_MM_DD_HH_MM_SS, inOrder.getCreateTime()));
            inResultService.save(inResult);
        }
    }

    public String getReply(String resultId, int result, String desc, String cspid) {
        String str = null;
        //错误组工单
        if ((result == 0) || (result == -1)) {
            String objectId = ManageHelper.getObjectID("CmdInP");

            org.dom4j.Element root = Dom4jUtils.createAdiElement();
            org.dom4j.Element reply = root.addElement("Reply");
            reply.addElement("Result").addText(SafeUtils.getString(result));
            reply.addElement("Description").addText(desc == null ? "" : desc);
            str = generateCmdXml(root, cspid, resultId);
        }
        return str;
    }

    public String generateCmdXml(org.dom4j.Element root, String cspid, String objectID) {
        Date dt = new Date();
        SimpleDateFormat time = new SimpleDateFormat("yyyyMMdd");
        String filePath = FileConstants.FILE_PRE_INT_TO_CSP + cspid + "/" + time.format(dt) + FileConstants.FILE_PRE_IN;
        String xmlUrl = "";
        try {
            String key = objectID + ".xml";
            String content = Dom4jUtils.getOutXmlContent(root);

            SysInPassage sysInPassage = null;

            Map<String, SysInPassage> stringSysInPassageMap = redisService.getCacheMap(RedisKeyConstants.SYS_IN_PASSAGE);

            if(stringSysInPassageMap != null){
                for (Map.Entry<String, SysInPassage> sysInPassageEntry : stringSysInPassageMap.entrySet()) {
                    if(sysInPassageEntry.getValue().getId().equals(Long.valueOf(cspid))){
                        sysInPassage = sysInPassageEntry.getValue();
                        break;
                    }
                }
            }

            if(sysInPassage != null){
                SysCp sysCp = redisService.getCacheMapValue(RedisKeyConstants.SYS_CP, String.valueOf(sysInPassage.getCpId()));
                log.info("in_result反馈记录 sysCp" + sysCp.toString());
                if (sysCp != null) {

                    SysStorage sysStorage = redisService.getCacheMapValue(RedisKeyConstants.SYS_STORAGE_KEY, String.valueOf(sysCp.getStorageId()));
                    log.info("in_result反馈记录 sysStorage" + sysStorage.toString());
                    if (sysStorage != null) {
                        if (StorageEnums.FTP.getCode().equals(sysStorage.getStorageType())) {

                            SysStorageDirctory sysStorageDirctory = redisService.getCacheMapValue(RedisKeyConstants.SYS_STORAGE_DIRCTORY_KEY,
                                    org.apache.commons.lang3.StringUtils.joinWith(":", sysCp.getStorageId(), DirectoryTypeEnums.ORDER.getCode(), AuthorityTypeEnums.READ_WRITE.getCode()));
                            log.info("in_result反馈记录 sysStorageDirctory" + sysStorageDirctory.toString());
                            if (sysStorageDirctory != null) {
                                Map<String, String> ipPort = URLUtil.parseIpAndPort(sysStorage.getInnerUrl());
                                log.info("生成工单ip：port：" + ipPort.toString());
                                FtpUtil ftpUtil = new FtpUtil(ipPort.get("ip"), NumberUtils.toInt(ipPort.get("port"), 21), sysStorageDirctory.getAccount(),
                                        sysStorageDirctory.getPassword(), true);
                                xmlUrl = ftpUtil.upFile(new ByteArrayInputStream(content.getBytes("UTF-8")), filePath + "/" + key,
                                        sysStorage.getInXmlFtpPrefix(), FileConstants.File_BASE);
                                log.info("生成工单xmlUrl：" + xmlUrl);
                            }
                        }
                    }
                }
            }



        } catch (Exception e) {
            log.error(e.getMessage(), e);
        }
        return xmlUrl;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public synchronized void updateStatus(Integer status, InOrder inOrder, String message, Integer result) {

        inOrder.setStatus(status);
        inOrder.setResult(result);
        inOrder.setDescription(message);
        inOrderMapper.updateById(inOrder);
    }

    @Override
    public ResultData updateStatus(Integer status, InOrder inOrder, String message, Integer result, Boolean flag) {
        ResultData resultData = new ResultData();
        inOrder.setStatus(status);
        inOrder.setResult(result);
        inOrder.setDescription(message);
        inOrder.setErrorDescription("");
        inOrder.setResendTime(new Date());
        inOrderMapper.updateById(inOrder);

        resultData.setResultCode(InOrderResultEnums.IN.getCode());
        resultData.setResultDesc(TableNameConstants.SUCCESS);

        return resultData;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateStatus(Integer status, InOrder inOrder, Integer result) {
        inOrder.setStatus(status);
        inOrder.setResult(result);
        inOrderMapper.updateById(inOrder);
    }

    @Override
    public Map<String, List<Object>> inOrder2CmsBmsForObjects() {

        return null;
    }




    @Override
    public Page<InOrder> selectList(InorderDto inorderDto) {
        Long current = inorderDto.getCurrent();
        Long size = inorderDto.getSize();
        //开始、结束时间格式化
        DateFormatCompletionDto dateFormatCompletionDto = new DateFormatCompletionDto().setStartTime(inorderDto.getStartDateTime()).setEndTime(inorderDto.getEndDateTime());
        Boolean aBoolean = TimeResolutionUtil.dateTimeCompare(dateFormatCompletionDto);
        //分页参数检查
        if (current < 0) {
            current = 1L;
        }
        if (size < 0 || size > 500) {
            size = 20L;
        }
        LambdaQueryWrapper<InOrder> wrapper = new LambdaQueryWrapper<>();

        //未换行，模糊查询,换行后精确查询
        if(StringUtils.isNotEmpty(inorderDto.getShowName())){
            // 是否为精确查询
            boolean equals = false;
            String[] nameList = inorderDto.getShowName().split("\n");
            if (inorderDto.getShowName().contains("\n") && nameList.length >= 1) {
                equals = true;
            }
            for (int i = 0; i < nameList.length ; i++) {
                nameList[i] = nameList[i].trim();
            }

            if(equals){
                wrapper.in(InOrder::getShowName, Arrays.asList(nameList));
            } else {
                wrapper.like(InOrder::getShowName,nameList[0]);
            }
        }
        if (ObjectUtils.isNotEmpty(inorderDto.getInPassageId())) {
            wrapper.eq(InOrder::getInPassageId, inorderDto.getInPassageId());
        }
        if (ObjectUtils.isNotEmpty(inorderDto.getStatus())) {
            if(inorderDto.getStatus().equals(PageConstants.IN_ORDER_VIEW_STATE_ALL)){

            }else if(inorderDto.getStatus() <= PageConstants.IN_ORDER_VIEW_STATE_ERROR_DATABASE &&
                    inorderDto.getStatus() > PageConstants.IN_ORDER_VIEW_STATE_ALL){
                wrapper.eq(InOrder::getStatus, inorderDto.getStatus());
            }else{
                wrapper.eq(InOrder::getResult, inorderDto.getStatus());
            }
        }
        if (ObjectUtils.isNotEmpty(inorderDto.getId())) {
            wrapper.eq(InOrder::getId, inorderDto.getId());
        }
        //新增唯一标识查询
        if (ObjectUtils.isNotEmpty(inorderDto.getCorrelateId())) {
            wrapper.eq(InOrder::getCorrelateId, inorderDto.getCorrelateId());
        }
        if (ObjectUtils.isNotEmpty(dateFormatCompletionDto.getStartTime()) && ObjectUtils.isNotEmpty(dateFormatCompletionDto.getEndTime())) {
            wrapper.le(InOrder::getCreateTime, dateFormatCompletionDto.getEndTime()).ge(InOrder::getCreateTime, dateFormatCompletionDto.getStartTime());
        }
        wrapper.orderByDesc(InOrder::getCreateTime);
        Page iPage = new Page(current, size);
        return inOrderMapper.selectPage(iPage, wrapper);
    }

    @Override
    public List<InOrder> selectInOrderList() {

        LambdaQueryWrapper<InOrder> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(InOrder::getResult, InOrderResultEnums.IN.getCode()).orderByAsc(InOrder::getId);

        IPage<InOrder> iPage = new Page(PageConstants.PAGE_NUMBER, PageConstants.IN_ORDER_PAGE_SIZE);
        iPage = inOrderMapper.selectPage(iPage, wrapper);
        List<InOrder> inResultList = iPage.getRecords();
        return inResultList;
    }

    @Override
    public void updateConfFailStatus(String inOredrId){
        LambdaUpdateWrapper<InOrder> updateWrapper = Wrappers.lambdaUpdate();
        updateWrapper.eq(InOrder::getId, inOredrId);
        updateWrapper.set(InOrder::getStatus, 7);
        updateWrapper.set(InOrder::getErrorDescription, "系统配置错误");
        this.update(updateWrapper);
    }

    @Override
    public InOrder getByCorrelateId(String correlateId) {
        LambdaQueryWrapper<InOrder> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.eq(InOrder::getCorrelateId, correlateId);
        queryWrapper.orderByDesc(InOrder::getCreateTime).last(TextConstants.LIMIT_ONE);
        return getOne(queryWrapper);
    }

    @Override
    public String getResultFileUrlByInOrderId(String inOrderId) {
        InResult result= inResultService.getByInOrderId(inOrderId);
        if (result == null) {
            return null;
        }
        return result.getResultFileUrl();
    }

}




