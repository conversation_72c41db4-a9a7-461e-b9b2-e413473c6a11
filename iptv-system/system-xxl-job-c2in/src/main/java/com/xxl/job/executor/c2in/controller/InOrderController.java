package com.xxl.job.executor.c2in.controller;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.pukka.iptv.common.base.enums.CommonResponseEnum;
import com.pukka.iptv.common.base.vo.CommonResponse;
import com.pukka.iptv.common.core.util.ResultData;
import com.pukka.iptv.common.data.model.in.InOrder;
import com.pukka.iptv.common.data.model.in.InOrderMappings;
import com.pukka.iptv.common.data.model.in.InResult;
import com.pukka.iptv.common.data.vo.IdList;
import com.xxl.job.executor.c2in.common.enums.InOrderResultEnums;
import com.xxl.job.executor.c2in.common.enums.InOrderStatusEnums;
import com.xxl.job.executor.c2in.dto.InorderDto;
import com.xxl.job.executor.c2in.service.InOrderMappingsService;
import com.xxl.job.executor.c2in.service.InOrderService;
import com.xxl.job.executor.c2in.service.InResultService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * @author: tan
 * @date: 2021-8-24 10:46:24
 */

@RestController
@AllArgsConstructor
@RequestMapping(value = "/inOrder", produces = MediaType.APPLICATION_JSON_VALUE)
@Api(tags = "inOrder管理")
@Slf4j
public class InOrderController {

    @Autowired
    private InOrderService inOrderService;

    @Autowired
    private InResultService inResultService;

    @Autowired
    private InOrderMappingsService inOrderMappingsService;

    @ApiOperation(value = "分页")
    @GetMapping("/page")
    public CommonResponse<Page> page(@Valid Page page, InOrder inOrder) {

        return CommonResponse.success(inOrderService.page(page, Wrappers.query(inOrder)));
    }

    @ApiOperation(value = "明细查看")
    @PostMapping("/pageForDetailedList")
    public CommonResponse<Page> pageForDetailedList(@RequestBody InorderDto inorderDto) {
        Page page = new Page();
        page.setSize(inorderDto.getSize());
        page.setCurrent(inorderDto.getCurrent());

        LambdaQueryWrapper<InOrderMappings> inOrderMappingsLambdaQueryWrapper = new LambdaQueryWrapper<>();
        inOrderMappingsLambdaQueryWrapper.eq(InOrderMappings::getInOrderId, inorderDto.getId());

        inOrderMappingsService.page(page, inOrderMappingsLambdaQueryWrapper);

        return CommonResponse.success(inOrderMappingsService.page(page, inOrderMappingsLambdaQueryWrapper));
    }

    @ApiOperation(value = "查询初始化页面")
    @PostMapping("/pageForList")
    public CommonResponse<Page> pageForList(@RequestBody InorderDto inorderDto) {
        return CommonResponse.success(inOrderService.selectList(inorderDto));
    }

    @ApiOperation(value = "重新解析")
    @PostMapping("/reParsing")
    @Transactional(rollbackFor = Exception.class)
    public CommonResponse<List<ResultData>> reParsing(@RequestBody List<InOrder> inOrderList) {
        List<ResultData> resultDataList = new ArrayList<>();
        for (InOrder in : inOrderList) {
            LambdaQueryWrapper<InOrderMappings> inOrderMappingsLambdaQueryWrapper = new LambdaQueryWrapper<>();
            inOrderMappingsLambdaQueryWrapper.eq(InOrderMappings::getInOrderId, in.getId());
            inOrderMappingsService.remove(inOrderMappingsLambdaQueryWrapper);

            QueryWrapper<InResult> queryWrapper = new QueryWrapper<>();
            queryWrapper.eq("in_order_id", in.getId());

            inResultService.remove(queryWrapper);
            ResultData resultData = inOrderService.updateStatus(InOrderStatusEnums.INJECT.getCode(),
                    in, "", InOrderResultEnums.IN.getCode(), true);
//            ResultData resultData = inOrderService.saveByCp(inOrder.getCspId(), inOrder.getLspId(), inOrder.getCorrelateId(), inOrder.getCmdFileUrl(),
//                    String.valueOf(inOrder.getInPassageId()), inOrder.getInPassageName());
            resultDataList.add(resultData);
        }
        return CommonResponse.success(resultDataList);
    }

    @ApiOperation(value = "重新入库")
    @PostMapping("/re2DataBase")
    @Transactional(rollbackFor = Exception.class)
    public CommonResponse<List<ResultData>> re2DataBase(@RequestBody List<InOrder> inOrderList) {
        List<ResultData> resultDataList = new ArrayList<>();
        for (InOrder inOrder : inOrderList) {

            InOrder inOrderTemp = new InOrder();
            inOrderTemp.setStatus(InOrderStatusEnums.TODATABASE.getCode());
            inOrderTemp.setResendTime(new Date());
            LambdaQueryWrapper<InOrder> inOrderLambdaQueryWrapper = new LambdaQueryWrapper<>();
            inOrderLambdaQueryWrapper.eq(InOrder::getId, inOrder.getId());

            inOrderService.update(inOrderTemp, inOrderLambdaQueryWrapper);

            InOrder inOrderDe = inOrderService.getById(inOrder.getId());
            QueryWrapper<InResult> queryWrapper = new QueryWrapper<>();
            queryWrapper.eq("in_order_id", inOrderDe.getId());

            inResultService.remove(queryWrapper);
        }
        return CommonResponse.success(resultDataList);
    }

    @ApiOperation(value = "重新反馈")
    @PostMapping("/reFeedBack")
    public CommonResponse<List<ResultData>> reFeedBack(@RequestBody List<InOrder> inOrderList) {
        List<ResultData> resultDataList = new ArrayList<>();
        for (InOrder inOrder : inOrderList) {
            InResult inResult = new InResult();
            inResult.setOrderStatus(InOrderStatusEnums.FEEDBACK.getCode());

            LambdaQueryWrapper<InResult> inResultLambdaQueryWrapper = new LambdaQueryWrapper<>();
            inResultLambdaQueryWrapper.eq(InResult::getInOrderId, inOrder.getId());
            inResultService.update(inResult, inResultLambdaQueryWrapper);
        }
        return CommonResponse.success(resultDataList);
    }

    @ApiOperation(value = "重设优先级")
    @PutMapping("/rePriority")
    public CommonResponse rePriority(@RequestBody Map<String, String> param) {

        for (String id : param.get("ids").split(",")) {

            LambdaUpdateWrapper<InOrder> wrapper = new LambdaUpdateWrapper<>();
            wrapper.set(InOrder::getPriority, param.get("priority"))
                    .eq(InOrder::getId, id);

            inOrderService.update(wrapper);
        }

        return CommonResponse.general(CommonResponseEnum.SUCCESS);
    }

    @ApiOperation(value = "详情")
    @GetMapping("/getById")
    public CommonResponse<InOrder> getById(@Valid @RequestParam(name = "id", required = true) Long id) {

        return CommonResponse.success(inOrderService.getById(id));
    }

    @ApiOperation(value = "新增")
    @PostMapping("/save")
    public CommonResponse<Boolean> save(@Valid @RequestBody InOrder inOrder) {
        return CommonResponse.success(inOrderService.save(inOrder));
    }

    @ApiOperation(value = "修改")
    @PutMapping("/update")
    public CommonResponse<Boolean> updateById(@Valid @RequestBody InOrder inOrder) {
        return CommonResponse.success(inOrderService.updateById(inOrder));
    }

    @ApiOperation(value = "删除")
    @DeleteMapping("/deleteById")
    public CommonResponse<Boolean> deleteById(@Valid @RequestParam(name = "id", required = true) Long id) {
        return CommonResponse.success(inOrderService.removeById(id));
    }

    @ApiOperation(value = "批量删除")
    @DeleteMapping("/deleteByIds")
    public CommonResponse<Boolean> deleteByIds(@Valid @RequestBody IdList idList) {
        return CommonResponse.success(inOrderService.removeByIds(idList.getIds()));
    }

    @ApiOperation(value = "根据工单id获取反馈工单地址")
    @GetMapping("/getResultFileUrlByInOrderId")
    public CommonResponse<String> getResultFileUrlByInOrderId(@RequestParam String inOrderId) {
        return CommonResponse.success(inOrderService.getResultFileUrlByInOrderId(inOrderId));
    }

    /**
     * 获取总行数
     * @return
     */
    @ApiOperation(value = "获取总行数")
    @GetMapping("/getCount")
    public CommonResponse<Long> getCount() {
        return CommonResponse.success(inOrderService.count());
    }
}
