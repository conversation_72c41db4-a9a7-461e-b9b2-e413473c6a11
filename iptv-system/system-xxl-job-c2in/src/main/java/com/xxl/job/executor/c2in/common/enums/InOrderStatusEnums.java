package com.xxl.job.executor.c2in.common.enums;

public enum InOrderStatusEnums {

    INJECT(1, "注入成功，待解析入库"),
    RESOLVE(2, "解析中"),
    RESOLVE_ING(100, "正在处理工单，待解析"),
    RESOLVE_SUCCESS(3, "解析成功"),
    RESOLVE_FAIL(4, "解析失败"),
    TODATABASE(5, "入库中"),
    TODATABASE_AYSN_DOWNLOAD(51, "异步下载"),
    PROCESSING(52, "处理中"),
    TODATABASE_SUCCESS(6, "入库成功"),
    TODATABASE_FAIL(7, "入库失败"),
    TODATABASE_AUTO_PUBLIC_FEIGN_FAIL(71, "调用自动发布feign失败"),
    FEEDBACK(8, "待反馈"),
    FEEDBACK_DOING(81, "反馈处理中"),
    FEEDBACK_AUTO_DOING(82, "自动下发反馈处理中"),
    FEEDBACK_SUCCESS(9, "反馈成功"),
    FEEDBACK_ERROR(10, "反馈失败");

    private final Integer code;
    private final String info;


    public static Integer getCodeByInfo(String info) {
        InOrderStatusEnums[] actionEnums = values();
        for (InOrderStatusEnums element : actionEnums) {
            if (element.getInfo().equals(info)) {
                return element.getCode();
            }
        }
        return null;
    }

    public static String getInfoByCode(Integer code) {
        InOrderStatusEnums[] actionEnums = values();
        for (InOrderStatusEnums element : actionEnums) {
            if (element.getCode().intValue() == code.intValue()) {
                return element.getInfo();
            }
        }
        return null;
    }

    InOrderStatusEnums(Integer code, String info)
    {
        this.code = code;
        this.info = info;
    }

    public Integer getCode()
    {
        return code;
    }

    public String getInfo()
    {
        return info;
    }
}
