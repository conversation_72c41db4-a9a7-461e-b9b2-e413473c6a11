package com.xxl.job.executor.c2in.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.pukka.iptv.common.data.model.cms.CmsSchedule;
import com.xxl.job.executor.c2in.common.constant.TextConstants;
import com.xxl.job.executor.c2in.mapper.CmsScheduleMapper;
import com.xxl.job.executor.c2in.service.CmsScheduleService;
import org.springframework.stereotype.Service;

/**
 *
 * @author: tan
 * @date: 2021年8月27日 上午10:00:16
 */

@Service
public class CmsScheduleServiceImpl extends ServiceImpl<CmsScheduleMapper, CmsSchedule> implements CmsScheduleService {

    @Override
    public Long countByChannelCode(String channelCode) {
        LambdaQueryWrapper<CmsSchedule> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.eq(CmsSchedule::getChannelCode, channelCode);
        return this.count(queryWrapper);
    }

    @Override
    public void removeByCode(String code) {
        LambdaQueryWrapper<CmsSchedule> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.eq(CmsSchedule::getCode, code);
        this.remove(queryWrapper);
    }

    @Override
    public CmsSchedule getByCode(String code) {
        LambdaQueryWrapper<CmsSchedule> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.eq(CmsSchedule::getCode, code).last(TextConstants.LIMIT_ONE);
        return this.getOne(queryWrapper);
    }

    @Override
    public CmsSchedule getByCodeCpId(String code, Long cpId) {
        LambdaQueryWrapper<CmsSchedule> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.eq(CmsSchedule::getCode, code).eq(CmsSchedule::getCpId, cpId).last(TextConstants.LIMIT_ONE);
        return this.getOne(queryWrapper);
    }

    @Override
    public void removeByChannelCode(String channelCode) {
        LambdaQueryWrapper<CmsSchedule> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.eq(CmsSchedule::getChannelCode, channelCode);
        this.remove(queryWrapper);
    }

    @Override
    public void removeByChannelCodeAndStartDate(String channelCode, String startDate) {
        LambdaQueryWrapper<CmsSchedule> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.eq(CmsSchedule::getChannelCode, channelCode)
                .eq(CmsSchedule::getStartDate, startDate);
        remove(queryWrapper);
    }

    @Override
    public void removeByChannelCodeAndStartDateAndTime(String channelCode, String startDate, String startTime) {
        LambdaQueryWrapper<CmsSchedule> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.eq(CmsSchedule::getChannelCode, channelCode)
                .eq(CmsSchedule::getStartDate, startDate);
        queryWrapper.and(wrapper -> {
            wrapper.ge(CmsSchedule::getStartTime, startTime);
        });
        remove(queryWrapper);
    }
}


