package com.xxl.job.executor.c2in.controller;

import javax.validation.Valid;

import com.pukka.iptv.common.base.vo.CommonResponse;
import com.pukka.iptv.common.data.model.in.InChannel;
import com.pukka.iptv.common.data.vo.*;
import com.xxl.job.executor.c2in.service.InChannelService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.*;
import org.springframework.beans.factory.annotation.Autowired;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.AllArgsConstructor;


/**
 *
 * @author: tan
 * @date: 2021-8-24 10:45:45
 */

@RestController
@AllArgsConstructor
@RequestMapping(value = "/inChannel", produces = MediaType.APPLICATION_JSON_VALUE)
@Api(tags="inChannel管理")
public class InChannelController {

    @Autowired
    private InChannelService inChannelService;

    @ApiOperation(value = "分页")
    @GetMapping("/page" )
    public CommonResponse<Page> page(@Valid Page page, InChannel inChannel) {
        return  CommonResponse.success(inChannelService.page(page, Wrappers.query(inChannel)));
    }

    @ApiOperation(value = "详情")
    @GetMapping("/getById")
    public CommonResponse<InChannel> getById(@Valid @RequestParam(name = "id", required = true)  Long id) {
        return CommonResponse.success(inChannelService.getById(id));
    }

    @ApiOperation(value = "新增")
    @PostMapping("/save")
    public CommonResponse<Boolean> save(@Valid @RequestBody InChannel inChannel) {
        return  CommonResponse.success(inChannelService.save(inChannel));
    }

    @ApiOperation(value = "修改")
    @PutMapping("/update")
    public CommonResponse<Boolean> updateById(@Valid @RequestBody InChannel inChannel) {
        return CommonResponse.success(inChannelService.updateById(inChannel));
    }

    @ApiOperation(value = "删除")
    @DeleteMapping("/deleteById" )
    public CommonResponse<Boolean> deleteById(@Valid @RequestParam(name = "id", required = true) Long id) {
        return  CommonResponse.success(inChannelService.removeById(id));
    }

    @ApiOperation(value = "批量删除")
    @DeleteMapping("/deleteByIds" )
    public CommonResponse<Boolean> deleteByIds(@Valid @RequestBody IdList idList) {
        return  CommonResponse.success(inChannelService.removeByIds(idList.getIds()));
    }

}
