package com.xxl.job.executor.c2in.job;

import com.pukka.iptv.common.base.constant.RedisKeyConstants;
import com.pukka.iptv.common.data.model.in.InOrder;
import com.pukka.iptv.common.data.model.in.InResult;
import com.pukka.iptv.common.data.model.sys.SysInPassage;
import com.pukka.iptv.common.redis.service.RedisService;
import com.xxl.job.executor.c2in.common.constant.FileConstants;
import com.xxl.job.executor.c2in.common.enums.InOrderStatusEnums;
import com.xxl.job.executor.c2in.common.util.DateUtils;
import com.xxl.job.executor.c2in.service.InOrderService;
import com.xxl.job.executor.c2in.service.InResultService;
import com.xxl.job.executor.c2in.webService.cmdResult.CSPResponse;
import com.xxl.job.executor.c2in.webService.cmdResult.CSPResponseServiceLocator;
import com.xxl.job.executor.c2in.webService.cmdResult.CSPResult;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import javax.xml.rpc.ServiceException;
import java.net.MalformedURLException;
import java.rmi.RemoteException;
import java.util.List;

/**
 * @ClassName Feedback
 * @Description 向cp端反馈任务
 * <AUTHOR>
 * @Date 2021/9/14 8:56
 * @Version
 */
@Component
@Slf4j
public class FeedBackJob implements ICinJob{
    @Autowired
    private InResultService inResultService;

    @Autowired
    private InOrderService inOrderService;

    @Autowired
    private RedisService redisService;


    private void feedBack() throws ServiceException {
          log.info("开始启动反馈");
          List<InResult> inResultList = inResultService.getFeedBackList();
          if(inResultList.size() > 0 && inResultList != null){
              for(InResult inResult : inResultList){
                  feedBack2CpByWs(inResult);
              }
          }

    }

    private void feedBack2CpByWs(InResult inResult) throws ServiceException {
        SysInPassage sysInPassage = redisService.getCacheMapValue(RedisKeyConstants.SYS_IN_PASSAGE, inResult.getCspId());
         if(sysInPassage == null){
             return;
         }
         String url = sysInPassage.getReportUrl();
         String cspid = inResult.getCspId();
         String lspid = inResult.getLspId();
         String correlationId = inResult.getCorrelateId();
         Integer result = inResult.getResult();
         InOrder inorder = null;
        try {
//           java.net.URL reportUrl = new java.net.URL("http://172.25.239.158:8081/services/HelloServiceImpl");

            java.net.URL reportUrl = new java.net.URL(url);
            CSPResponse client = new CSPResponseServiceLocator().getctms(reportUrl);
            log.info("发送反馈工单初始地址result：" + result);
            log.info("发送反馈工单初始地址correlationId：" + correlationId);
            log.info("发送反馈工单初始地址eportUrl：" + reportUrl);
            log.info("发送反馈工单初始地址xml：" + inResult.getResultFileUrl());
            CSPResult cspResult = client.resultNotify(cspid, lspid, correlationId, result, inResult.getResultFileUrl());
            log.info("client result -------: result=" + cspResult.getResult() + ", errorDescription=" + cspResult.getErrorDescription());

//            inResult.setResult(cspResult.getResult());
            inResult.setErrorDescription(cspResult.getErrorDescription());
            inResult.setResponseTime(DateUtils.dateTimeNow());
            if(cspResult.getResult() == FileConstants.R_SUCCESS_INT){
                inResult.setOrderStatus(InOrderStatusEnums.FEEDBACK_SUCCESS.getCode());
            }else {
                inResult.setOrderStatus(InOrderStatusEnums.FEEDBACK_ERROR.getCode());
            }

            inorder = inOrderService.getById(inResult.getInOrderId());
            if(inorder != null){
                inorder.setResult(inResult.getOrderStatus());
                //更新订单表
                inOrderService.updateById(inorder);
                //更新结果表
                inResultService.updateById(inResult);
            }else {
                inResultService.updateById(inResult);
            }


        } catch (MalformedURLException | RemoteException e) {
            log.error(e.getMessage());
            inResult.setResponseTime(DateUtils.dateTimeNow());
            inResult.setOrderStatus(InOrderStatusEnums.FEEDBACK_ERROR.getCode());

            inorder = inOrderService.getById(inResult.getInOrderId());
            if(inorder != null){
                inorder.setResult(InOrderStatusEnums.FEEDBACK_ERROR.getCode());
                inOrderService.updateById(inorder);
            }
            inResultService.updateById(inResult);
        }


    }

    /**
     * 执行任务
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void executeJob(){
        try{
            log.info("FeedBackJob start； "+ DateUtils.dateTimeNow());
            feedBack();
            log.info("FeedBackJob end : " + DateUtils.dateTimeNow());
        }catch (Exception exception){
            log.info("FeedBackJob()" + exception.getMessage(), exception);
        }
    }






}
