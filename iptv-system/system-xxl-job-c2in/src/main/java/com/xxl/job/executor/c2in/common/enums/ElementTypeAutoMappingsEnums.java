package com.xxl.job.executor.c2in.common.enums;

import com.pukka.iptv.common.base.constant.ObjectsTypeConstants;

public enum ElementTypeAutoMappingsEnums {

    Category(8, "Category"),
    Movie(12, "Movie"),
    Package(9, "Package"),
    Picture(11, "Picture"),
    Program(1, "Program"),
    SUBSET(2, "子集"),
    Series(3, "Series"),
    EPISODES(4, "序列片"),
    PREVIEW(5,"片花"),
    Channel(6, "Channel"),
    PhysicalChannel(7, "PhysicalChannel"),
    Mappings(9, "Mappings"),
    Schedule(10, "Schedule"),
    PROGRAM_PICTURE(13, "节目图片"),
    SERIES_PICTURE(14, "剧集图片"),
    PACKAGE_PICTURE(15, "产品包图片"),
    CATEGORY_PICTURE(16, "栏目图片"),
    CHANNEL_PICTURE(17, "频道图片"),
    CATEGORY_PROGRAM(18, "栏目节目"),
    CATEGORY_SERIES(19, "栏目剧集"),
    CATEGORY_CHANNEL(20, "栏目频道"),
    PACKAGE_PROGRAM(21, "产品包节目"),
    PACKAGE_SERIES(22, "产品包剧集"),
    PACKAGE_CHANNEL(23, "产品包频道"),
    // 频道+物理频道
    CHANNEL_PHYSICAL(24,  "直播+物理频道"),
    //剧集携带子集
    SERIES_SUBSET(25,"剧集+子集"),
    //关系
    RELATIONSHIP(30,"关系"),
    NullElemet(99, "NullElemet");

    private final Integer code;
    private final String info;


    public static Integer getCodeByInfo(String info) {
        ElementTypeAutoMappingsEnums[] actionEnums = values();
        for (ElementTypeAutoMappingsEnums element : actionEnums) {
            if (element.getInfo().equals(info)) {
                return element.getCode();
            }
        }
        return null;
    }

    public static String getInfoByCode(Integer code) {
        ElementTypeAutoMappingsEnums[] actionEnums = values();
        for (ElementTypeAutoMappingsEnums element : actionEnums) {
            if (element.getCode().intValue() == code.intValue()) {
                return element.getInfo();
            }
        }
        return null;
    }

    ElementTypeAutoMappingsEnums(Integer code, String info)
    {
        this.code = code;
        this.info = info;
    }

    public Integer getCode()
    {
        return code;
    }

    public String getInfo()
    {
        return info;
    }
}
