package com.xxl.job.executor.c2in.service.impl;


import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.pukka.iptv.common.data.model.in.InPicture;
import com.xxl.job.executor.c2in.common.util.StatusUtils;
import com.xxl.job.executor.c2in.mapper.InPictureMapper;
import com.xxl.job.executor.c2in.service.InPictureService;
import org.springframework.stereotype.Service;


/**
 *
 * @author: tan
 * @date: 2021-8-24 10:49:36
 */

@Service
public class InPictureServiceImpl extends ServiceImpl<InPictureMapper, InPicture> implements InPictureService {

    @Override
    public void updateStatusById(Long id, StatusUtils.Status status) {
        InPicture inPicture = new InPicture();
        inPicture.setResult(status.getCode());
        inPicture.setErrorDescription(status.getMsg());
        LambdaUpdateWrapper<InPicture> updateWrapper = Wrappers.lambdaUpdate();
        updateWrapper.eq(InPicture::getId, id);
        this.update(inPicture, updateWrapper);
    }
}


