package com.xxl.job.executor.c2in.service.condition;

import cn.hutool.extra.spring.SpringUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.pukka.iptv.common.base.enums.ContentTypeEnum;
import com.pukka.iptv.common.data.model.bms.*;
import com.xxl.job.executor.c2in.mapper.BmsContentMapper;
import org.springframework.util.CollectionUtils;

import java.util.*;

public class RuleCondition {
    private Map<Integer, List<BaseRule>> hashMap = new HashMap<>();
    private static final int AND = 1;
    private static final int OR = 0;

    public static RuleCondition create() {
        return new RuleCondition();
    }

    public static <T> Class<?> getTypeClass(Integer type) {
        ContentTypeEnum typeEnum = ContentTypeEnum.getByValue(type);
        if (typeEnum != null) {
            switch (typeEnum) {
                case CATEGORY:
                    return BmsCategory.class;
                case TELEPLAY:
                    return BmsContent.class;
                case FILM:
                    return BmsContent.class;
                case SUBSET:
                    return BmsProgram.class;
                case CHANNEL:
                    return BmsChannel.class;
                case PACKAGE:
                    return BmsPackage.class;
            }
        }
        return null;
    }

    public RuleCondition and(Boolean flag, BaseRule rule) {
        if (flag) {
            List<BaseRule> andRule = hashMap.get(AND);
            if (andRule == null) {
                andRule = new ArrayList<>();
            }
            andRule.add(rule);
            hashMap.put(AND, andRule);
        }
        return this;
    }

    public RuleCondition and(BaseRule rule) {
        List<BaseRule> andRule = hashMap.get(AND);
        if (andRule == null) {
            andRule = new ArrayList<>();
        }
        andRule.add(rule);
        hashMap.put(AND, andRule);
        return this;
    }

    public RuleCondition and(List<BaseRule> rules) {
        List<BaseRule> andRule = hashMap.get(AND);
        if (andRule == null) {
            andRule = new ArrayList<>();
        }
        andRule.addAll(rules);
        hashMap.put(AND, andRule);
        return this;
    }

    public RuleCondition or(List<BaseRule> ruleList) {
        hashMap.put(OR, ruleList);
        return this;
    }


    public RuleResult wrapperExecute() {
        for (Map.Entry<Integer, List<BaseRule>> item : hashMap.entrySet()) {
            List<BaseRule> ruleList = item.getValue();
            Set<QueryWrapper<RuleInfo>> set = new HashSet<>();
            for (BaseRule baseRule : ruleList) {
                QueryWrapper<RuleInfo> wrapper = baseRule.wrapper();
                set.add(wrapper);
            }
            for (QueryWrapper wrapper : set) {
                BmsContentMapper bean = SpringUtil.getBean(BmsContentMapper.class);
                List<BmsContent> list = bean.selectList(wrapper);
                if (!CollectionUtils.isEmpty(list)) {
                    BmsContent o = list.get(0);
                    String name = o.getName();
                    return RuleResult.fail(name + "不符合规则", name);
                }
            }
        }
        return RuleResult.ok();
    }

    public RuleResult execute() {
        for (Map.Entry<Integer, List<BaseRule>> item : hashMap.entrySet()) {
            List<BaseRule> ruleList = item.getValue();
            switch (item.getKey()) {
                case AND:
                    // 如果是 and 关系，同步执行
                    RuleResult result = andExecute(ruleList);
                    if (!result.isPass()) {
                        return result;
                    }
                    break;
                case OR:
                    // 如果是 or 关系，并行执行
                    RuleResult orResult = orExecute(ruleList);
                    if (orResult.isPass()) {
                        return orResult;
                    }
                    break;
                default:
                    break;
            }
        }
        return RuleResult.ok();
    }


    private RuleResult andExecute(List<BaseRule> ruleList) {
        for (BaseRule rule : ruleList) {
            RuleResult result = rule.execute();
            if (!result.isPass()) {
                // and 关系匹配失败一次，返回 false
                return result;
            }
        }
        // and 关系全部匹配成功，返回 true
        return RuleResult.ok();
    }

    private RuleResult orExecute(List<BaseRule> ruleList) {
        for (BaseRule rule : ruleList) {
            RuleResult result = rule.execute();
            if (result.isPass()) {
                // or 关系匹配到一个就返回 true
                return result;
            }
        }
        // or 关系一个都匹配不到就返回 false
        return RuleResult.fail("没有一个条件通过", "");
    }
}
