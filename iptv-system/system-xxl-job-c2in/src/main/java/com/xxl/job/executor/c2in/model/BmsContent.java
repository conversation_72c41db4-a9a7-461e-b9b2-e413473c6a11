package com.xxl.job.executor.c2in.model;

import com.baomidou.mybatisplus.annotation.*;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.NoArgsConstructor;
import lombok.EqualsAndHashCode;
import lombok.Data;

import java.util.Date;

/**
 *
 * @author: liuli
 * @date: 2021年10月12日 上午9:47:52
 */

@Data
@EqualsAndHashCode
@JsonIgnoreProperties(value = { "handler" })
@NoArgsConstructor
@TableName(value = "bms_content",autoResultMap=true)
public class BmsContent extends Model<BmsContent> implements java.io.Serializable{

    private static final long serialVersionUID = 1L;
	/**主键*/
	@TableField(value = "id")
    @TableId(type = IdType.AUTO)
    @ApiModelProperty(value="主键",dataType="Long",name="id")
    private Long id;
	/**全局唯一标识*/
	@TableField(value = "code", fill = FieldFill.INSERT)
    @ApiModelProperty(value="全局唯一标识",dataType="String",name="code")
    private String code;
	/**节目名称*/
	@TableField(value = "name")
    @ApiModelProperty(value="节目名称",dataType="String",name="name")
    private String name;
	/**节目订购编号*/
	@TableField(value = "order_number")
    @ApiModelProperty(value="节目订购编号",dataType="String",name="orderNumber")
    private String orderNumber;
	/**原名*/
	@TableField(value = "original_name")
    @ApiModelProperty(value="原名",dataType="String",name="originalName")
    private String originalName;
	/**有效开始时间（YYYYMMDDHH24MiSS）*/
	@TableField(value = "licensing_window_start")
    @ApiModelProperty(value="有效开始时间（YYYYMMDDHH24MiSS）",dataType="String",name="licensingWindowStart")
    private String licensingWindowStart;
	/**有效结束时间（YYYYMMDDHH24MiSS）*/
	@TableField(value = "licensing_window_end")
    @ApiModelProperty(value="有效结束时间（YYYYMMDDHH24MiSS）",dataType="String",name="licensingWindowEnd")
    private String licensingWindowEnd;
	/**新到天数*/
	@TableField(value = "display_as_new")
    @ApiModelProperty(value="新到天数",dataType="Integer",name="displayAsNew")
    private Integer displayAsNew;
	/**剩余天数 */
	@TableField(value = "display_as_last_chance")
    @ApiModelProperty(value="剩余天数 ",dataType="Integer",name="displayAsLastChance")
    private Integer displayAsLastChance;
	/**媒资分类ID，来自pgm_category表*/
	@TableField(value = "pgm_category_id")
    @ApiModelProperty(value="媒资分类ID，来自pgm_category表",dataType="Long",name="pgmCategoryId")
    private Long pgmCategoryId;
	/**媒资分类，节目形态，如：新闻，电影*/
	@TableField(value = "pgm_category")
    @ApiModelProperty(value="媒资分类，节目形态，如：新闻，电影",dataType="String",name="pgmCategory")
    private String pgmCategory;
	/**媒资类型ID，来自pgm_snd_class表*/
	@TableField(value = "pgm_snd_class_id")
    @ApiModelProperty(value="媒资类型ID，来自pgm_snd_class表",dataType="Long",name="pgmSndClassId")
    private String pgmSndClassId;
	/**二级标签，如：动作，科幻*/
	@TableField(value = "pgm_snd_class")
    @ApiModelProperty(value="二级标签，如：动作，科幻",dataType="String",name="pgmSndClass")
    private String pgmSndClass;
	/**状态标志 1:生效 0:失效 255:删除*/
	@TableField(value = "status")
    @ApiModelProperty(value="状态标志 1:生效 0:失效 255:删除",dataType="Integer",name="status")
    private Integer status;
	/**内容提供商标识*/
	@TableField(value = "content_provider")
    @ApiModelProperty(value="内容提供商标识",dataType="String",name="contentProvider")
    private String contentProvider;
	/**cpId*/
	@TableField(value = "cp_id")
    @ApiModelProperty(value="cpId",dataType="Long",name="cpId")
    private Long cpId;
	/**CP名称*/
	@TableField(value = "cp_name")
    @ApiModelProperty(value="CP名称",dataType="String",name="cpName")
    private String cpName;
	/**创建时间*/
	@TableField(value = "create_time")
    @ApiModelProperty(value="创建时间",dataType="Date",name="createTime")
    @JsonFormat(shape = JsonFormat.Shape.STRING,pattern = "yyyy-MM-dd HH:mm:ss",timezone="GMT+8")
    private Date createTime;
	/**更新时间*/
	@TableField(value = "update_time")
    @ApiModelProperty(value="更新时间",dataType="Date",name="updateTime")
    @JsonFormat(shape = JsonFormat.Shape.STRING,pattern = "yyyy-MM-dd HH:mm:ss",timezone="GMT+8")
    private Date updateTime;
	/**spId*/
	@TableField(value = "sp_id")
    @ApiModelProperty(value="spId",dataType="Long",name="spId")
    private Long spId;
	/**SP名称*/
	@TableField(value = "sp_name")
    @ApiModelProperty(value="SP名称",dataType="String",name="spName")
    private String spName;
	/**1：电影 3：电视剧 4：系列片 5：片花 此处没有子集，子集由program表单独保存*/
	@TableField(value = "content_type")
    @ApiModelProperty(value="1：电影 3：电视剧 4：系列片 5：片花 此处没有子集，子集由program表单独保存",dataType="Integer",name="contentType")
    private Integer contentType;
	/**审核状态 1：未审核 2：审核中 3：审核通过 4：未通过 2：通过*/
	@TableField(value = "cp_check_status")
    @ApiModelProperty(value="审核状态 1：未审核 2：审核中 3：审核未通过 4：通过  ",dataType="Integer",name="cpCheckStatus")
    private Integer cpCheckStatus;
	/**自审描述*/
	@TableField(value = "cp_check_desc")
    @ApiModelProperty(value="自审描述",dataType="String",name="cpCheckDesc")
    private String cpCheckDesc;
	/**审核时间*/
	@TableField(value = "cp_check_time")
    @ApiModelProperty(value="审核时间",dataType="String",name="cpCheckTime")
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date cpCheckTime;
	/**审核人*/
	@TableField(value = "cp_checker")
    @ApiModelProperty(value="审核人",dataType="String",name="cpChecker")
    private String cpChecker;
	/**op审核 1：op未审核 2：审核中 3：审核通过 4：审核未通过*/
	@TableField(value = "op_check_status")
    @ApiModelProperty(value="op审核 1：op未审核 2：审核中 3：审核未通过 4：审核通过",dataType="Integer",name="opCheckStatus")
    private Integer opCheckStatus;
	/**op审核描述*/
	@TableField(value = "op_check_desc")
    @ApiModelProperty(value="op审核描述",dataType="String",name="opCheckDesc")
    private String opCheckDesc;
	/**终审人员*/
	@TableField(value = "op_checker")
    @ApiModelProperty(value="终审人员",dataType="String",name="opChecker")
    private String opChecker;
	/**终审时间*/
	@TableField(value = "op_check_time")
    @ApiModelProperty(value="终审时间",dataType="String",name="opCheckTime")
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date opCheckTime;
	/**来源1：专线注入 2：人工创建*/
	@TableField(value = "source")
    @ApiModelProperty(value="来源1：专线注入 2：人工创建",dataType="Integer",name="source")
    private Integer source;
	/**内容编辑锁状态1:未锁定 2:已锁定*/
	@TableField(value = "lock_status")
    @ApiModelProperty(value="内容编辑锁状态1:未锁定 2:已锁定",dataType="Integer",name="lockStatus")
    private Integer lockStatus;
	/**发布通道ID，多个ID以英文逗号隔开*/
	@TableField(value = "out_passage_ids")
    @ApiModelProperty(value="发布通道ID，多个ID以英文逗号隔开",dataType="String",name="outPassageIds")
    private String outPassageIds;
	/**分发通道名称以英文逗号 隔开*/
	@TableField(value = "out_passage_names")
    @ApiModelProperty(value="分发通道名称以英文逗号 隔开",dataType="String",name="outPassageNames")
    private String outPassageNames;
	/**发布状态  1：待发布 2：发布中 3：发布成功 4：发布失败 5：待更新 6：更新中 7：更新失败 8：回收中 9：回收成功 10：回收失败*/
	@TableField(value = "publish_status")
    @ApiModelProperty(value="发布状态  1：待发布 2：发布中 3：发布成功 4：发布失败 5：待更新 6：更新中 7：更新失败 8：回收中 9：回收成功 10：回收失败",dataType="Integer",name="publishStatus")
    private Integer publishStatus;
	/**发布时间*/
	@TableField(value = "publish_time")
    @ApiModelProperty(value="发布时间",dataType="String",name="publishTime")
    private String publishTime;
	/**publishDescription*/
	@TableField(value = "publish_description")
    @ApiModelProperty(value="publishDescription",dataType="String",name="publishDescription")
    private String publishDescription;
	/**定时发布，时间存在则代表已经设定为定时发布*/
	@TableField(value = "timed_publish")
    @ApiModelProperty(value="定时发布，时间存在则代表已经设定为定时发布",dataType="String",name="timedPublish")
    private String timedPublish;
	/**CP内容编码*/
	@TableField(value = "cms_content_code")
    @ApiModelProperty(value="CP内容编码",dataType="String",name="cmsContentCode")
    private String cmsContentCode;
	/**内容ID*/
	@TableField(value = "cms_content_id")
    @ApiModelProperty(value="内容ID",dataType="Long",name="cmsContentId")
    private Long cmsContentId;
	/**缺集信息，信息存在说明缺集*/
	@TableField(value = "missed_info")
    @ApiModelProperty(value="缺集信息，信息存在说明缺集",dataType="String",name="missedInfo")
    private String missedInfo;
	/**所属渠道*/
	@TableField(value = "bms_sp_channel_name")
    @ApiModelProperty(value="所属渠道",dataType="String",name="bmsSpChannelName")
    private String bmsSpChannelName;
	/**所属渠道id*/
	@TableField(value = "bms_sp_channel_id")
    @ApiModelProperty(value="所属渠道id",dataType="Long",name="bmsSpChannelId")
    private Long bmsSpChannelId;
	/**产品包ID集合，以英文逗号分割*/
	@TableField(value = "package_ids")
    @ApiModelProperty(value="产品包ID集合，以英文逗号分割",dataType="String",name="packageIds")
    private String packageIds;
	/**产品包name集合，以英文逗号分割*/
	@TableField(value = "package_names")
    @ApiModelProperty(value="产品包name集合，以英文逗号分割",dataType="String",name="packageNames")
    private String packageNames;
	/**栏目ID集合，以英文逗号分割*/
	@TableField(value = "category_ids")
    @ApiModelProperty(value="栏目ID集合，以英文逗号分割",dataType="String",name="categoryIds")
    private String categoryIds;
	/**栏目Name集合，以英文逗号分割*/
	@TableField(value = "category_names")
    @ApiModelProperty(value="栏目Name集合，以英文逗号分割",dataType="String",name="categoryNames")
    private String categoryNames;
	/**正片关联状态 1：待关联 2：已关联*/
	@TableField(value = "release_status")
    @ApiModelProperty(value="正片关联状态 1：待关联 2：已关联",dataType="Integer",name="releaseStatus")
    private Integer releaseStatus;
	/**预览片关联状态 1：待关联 2：已关联*/
	@TableField(value = "preview_status")
    @ApiModelProperty(value="预览片关联状态 1：待关联 2：已关联",dataType="Integer",name="previewStatus")
    private Integer previewStatus;
}
