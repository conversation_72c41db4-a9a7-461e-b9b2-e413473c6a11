package com.xxl.job.executor.c2in.controller;

import javax.validation.Valid;

import com.pukka.iptv.common.base.vo.CommonResponse;
import com.pukka.iptv.common.data.model.in.InMapping;
import com.pukka.iptv.common.data.vo.*;
import com.xxl.job.executor.c2in.service.InMappingService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.*;
import org.springframework.beans.factory.annotation.Autowired;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.AllArgsConstructor;

/**
 *
 * @author: tan
 * @date: 2021-8-24 10:45:59
 */

@RestController
@AllArgsConstructor
@RequestMapping(value = "/inMapping", produces = MediaType.APPLICATION_JSON_VALUE)
@Api(tags="inMapping管理")
public class InMappingController {

    @Autowired
    private InMappingService inMappingService;

    @ApiOperation(value = "分页")
    @GetMapping("/page" )
    public CommonResponse<Page> page(@Valid Page page, InMapping inMapping) {
        return  CommonResponse.success(inMappingService.page(page, Wrappers.query(inMapping)));
    }

    @ApiOperation(value = "详情")
    @GetMapping("/getById")
    public CommonResponse<InMapping> getById(@Valid @RequestParam(name = "id", required = true)  Long id) {
        return CommonResponse.success(inMappingService.getById(id));
    }

    @ApiOperation(value = "新增")
    @PostMapping("/save")
    public CommonResponse<Boolean> save(@Valid @RequestBody InMapping inMapping) {
        return  CommonResponse.success(inMappingService.save(inMapping));
    }

    @ApiOperation(value = "修改")
    @PutMapping("/update")
    public CommonResponse<Boolean> updateById(@Valid @RequestBody InMapping inMapping) {
        return CommonResponse.success(inMappingService.updateById(inMapping));
    }

    @ApiOperation(value = "删除")
    @DeleteMapping("/deleteById" )
    public CommonResponse<Boolean> deleteById(@Valid @RequestParam(name = "id", required = true) Long id) {
        return  CommonResponse.success(inMappingService.removeById(id));
    }

    @ApiOperation(value = "批量删除")
    @DeleteMapping("/deleteByIds" )
    public CommonResponse<Boolean> deleteByIds(@Valid @RequestBody IdList idList) {
        return  CommonResponse.success(inMappingService.removeByIds(idList.getIds()));
    }

}
