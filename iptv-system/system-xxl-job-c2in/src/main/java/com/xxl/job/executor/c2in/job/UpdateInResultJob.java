package com.xxl.job.executor.c2in.job;

import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.pukka.iptv.common.api.feign.out.C2outFeignClient;
import com.pukka.iptv.common.api.feign.sys.AutoPublishContentFeignClient;
import com.pukka.iptv.common.base.constant.RedisKeyConstants;
import com.pukka.iptv.common.base.enums.AutoPublishResultEmum;
import com.pukka.iptv.common.core.constant.SymbolConstant;
import com.pukka.iptv.common.core.util.IdUtils;
import com.pukka.iptv.common.core.util.ResultData;
import com.pukka.iptv.common.core.util.StringUtils;
import com.pukka.iptv.common.data.model.OutAuto;
import com.pukka.iptv.common.data.model.in.InOrder;
import com.pukka.iptv.common.data.model.sys.SysInPassage;
import com.pukka.iptv.common.rabbitmq.constans.OutPublishConstant;
import com.pukka.iptv.common.rabbitmq.vo.MessageBody;
import com.pukka.iptv.common.redis.service.RedisService;
import com.xxl.job.executor.c2in.common.constant.FileConstants;
import com.xxl.job.executor.c2in.common.constant.PageConstants;
import com.xxl.job.executor.c2in.common.enums.InOrderStatusEnums;
import com.xxl.job.executor.c2in.common.util.DateUtils;
import com.xxl.job.executor.c2in.service.InOrderAPIService;
import com.xxl.job.executor.c2in.service.InOrderMappingsService;
import com.xxl.job.executor.c2in.service.InOrderService;
import com.xxl.job.executor.c2in.service.InResultService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.amqp.rabbit.connection.CorrelationData;
import org.springframework.amqp.rabbit.core.RabbitTemplate;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;

/**
 * @ClassName UpdataInResult
 * @Description 更新入库-》反馈状态
 * <AUTHOR>
 * @Date 2021/9/27 15:04
 * @Version
 */
@Component
@Slf4j
public class UpdateInResultJob implements ICinJob {

    @Autowired
    private InResultService inResultService;

    @Autowired
    private InOrderService inOrderService;

    @Autowired
    private InOrderAPIService inOrderAPIService;

    @Autowired
    private RedisService redisService;

    @Autowired
    private C2outFeignClient c2outFeignClient;

    @Autowired
    private AutoPublishContentFeignClient autoPublishContentFeignClient;

    @Autowired
    private RabbitTemplate rabbitTemplate;

    @Autowired
    private InOrderMappingsService inOrderMappingsService;

    private void update() {
        //获取需反馈的in_order
        Page<InOrder> page = new Page<InOrder>(PageConstants.PAGE_NUMBER, PageConstants.IN_ORDER_PAGE_SIZE);
        LambdaQueryWrapper<InOrder> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(InOrder::getStatus, InOrderStatusEnums.TODATABASE.getCode()).orderByDesc(InOrder::getCreateTime);

        Page<InOrder> inOrderPage = inOrderService.page(page, wrapper);

        for (InOrder inOrder : inOrderPage.getRecords()) {
            ResultData resultData = inOrderAPIService.checkInOrderMappings(inOrder);
            if (InOrderStatusEnums.TODATABASE_SUCCESS.getCode().intValue() == (resultData.getResultCode())) {

                inOrderService.inOrderHandler(InOrderStatusEnums.TODATABASE_SUCCESS.getCode(), inOrder,
                        FileConstants.R_SUCCESS_DES, FileConstants.R_SUCCESS_INT, InOrderStatusEnums.FEEDBACK.getCode());

            } else if (InOrderStatusEnums.TODATABASE_FAIL.getCode().intValue() == (resultData.getResultCode())) {
                inOrderService.inOrderHandler(InOrderStatusEnums.TODATABASE_FAIL.getCode(), inOrder,
                        resultData.getResultDesc(), FileConstants.R_FAILE_INT, InOrderStatusEnums.FEEDBACK.getCode());
            } else if (InOrderStatusEnums.TODATABASE_AYSN_DOWNLOAD.getCode().intValue() == resultData.getResultCode()) {

                inOrderService.updateStatus(InOrderStatusEnums.TODATABASE.getCode(), inOrder, resultData.getResultDesc(),
                        InOrderStatusEnums.TODATABASE_AYSN_DOWNLOAD.getCode());
            } else {

            }
        }
    }

    /**
     * 指定主工单，判断是否对应的子工单是否全部完成
     *
     * @param inOrder
     */
    public void update(InOrder inOrder) {
        //获取需反馈的in_order
        ResultData resultData = inOrderAPIService.checkInOrderMappings(inOrder);
        if (InOrderStatusEnums.TODATABASE_SUCCESS.getCode() == (resultData.getResultCode())) {
            sendToSpBySysInPassge(inOrder, resultData);
//            inOrderService.inOrderHandler(InOrderStatusEnums.TODATABASE_SUCCESS.getCode(), inOrder,
//                    FileConstants.R_SUCCESS_DES, FileConstants.R_SUCCESS_INT, InOrderStatusEnums.FEEDBACK.getCode());

        } else if (InOrderStatusEnums.TODATABASE_FAIL.getCode() == (resultData.getResultCode())) {
            inOrderService.inOrderHandler(InOrderStatusEnums.TODATABASE_FAIL.getCode(), inOrder,
                    resultData.getResultDesc(), FileConstants.R_FAILE_INT, InOrderStatusEnums.FEEDBACK.getCode());
        } else if (InOrderStatusEnums.TODATABASE_AYSN_DOWNLOAD.getCode() == resultData.getResultCode()) {

            inOrderService.updateStatus(InOrderStatusEnums.TODATABASE_AYSN_DOWNLOAD.getCode(), inOrder, resultData.getResultDesc(),
                    InOrderStatusEnums.TODATABASE_AYSN_DOWNLOAD.getCode());
        } else {

        }
    }

    /**
     * 根据通道不同的类型，生成反馈状态
     *
     * @param inOrder
     * @param resultData
     */
    public void sendToSpBySysInPassge(InOrder inOrder, ResultData resultData) {
        SysInPassage sysInPassage = redisService.getCacheMapValue(RedisKeyConstants.SYS_IN_PASSAGE, inOrder.getCspId());
        if (sysInPassage != null) {
            if (sysInPassage.getOrderPublishType() != null) {
                if (sysInPassage.getOrderPublishType() == 3) {
                    List<String> spIds = inOrderAPIService.checkAutoOrderLegal(inOrder, sysInPassage);
                    if (CollectionUtils.isNotEmpty(spIds) && spIds.size() > 0) {
                        String spids = String.join(SymbolConstant.COMMA, spIds);
                        //调用自动发送feign接口
                        try {
                            // TODO: 2022/1/5
                            log.info("自动发送c2outFeignClient数据: " + inOrder.getCspId() + " " + inOrder.getCorrelateId() + " " + spids);
//                            CommonResponse<Boolean> commonResponse =  c2outFeignClient.issue(inOrder.getCspId(),
//                                    inOrder.getCorrelateId(), sysInPassage.getOutSpIds());
//                            log.info("自动发送autoPublishContentFeignClient数据: "+ inOrder.getCorrelateId() + " "+ sysInPassage.getOutSpIds() + " "+commonResponse.getData());
                            MessageBody message = new MessageBody();

                            OutAuto outAuto = new OutAuto();
                            outAuto.setCorrelateId(inOrder.getCorrelateId());
                            outAuto.setCspId(inOrder.getCspId());
                            outAuto.setSpIds(spids);

                            //填充名称和类型值
                            OutAuto outAutoTDO = inOrderMappingsService.getOutAutoExtendByInOrderId(inOrder.getId());
                            outAuto.setContentType(outAutoTDO.getContentType());
                            outAuto.setShowName(inOrder.getShowName());

                            Map<String, Object> paramsOutAuto = JSON.parseObject(JSON.toJSONString(outAuto), Map.class);
                            message.setId(IdUtils.simpleUUID());
                            message.setMsg(paramsOutAuto);
                            log.info("OutAuto:" + outAuto.toString());
                            //全局唯一 不然ReturnCallback 无效
                            CorrelationData correlationData = new CorrelationData(UUID.randomUUID().toString());
                            try {
                                this.rabbitTemplate.convertAndSend(OutPublishConstant.OUT_EXCHANGE, OutPublishConstant.OUT_AUTO_SEND_ROUTING, message, correlationData);
                                autoPublishContentFeignClient.dealContent(inOrder.getCorrelateId(), spids
                                        , AutoPublishResultEmum.SUCCESS.getCode(), false);
                            } catch (Exception e) {
                                log.info("auto发送消息，数据：{}", inOrder.toString());
                                autoPublishContentFeignClient.dealContent(inOrder.getCorrelateId(), sysInPassage.getOutPassageIds()
                                        , AutoPublishResultEmum.FAILED.getCode(), false);
                            }

                            //commonResponse.getData()
//                            if(true){
//                                autoPublishContentFeignClient.dealContent(inOrder.getCorrelateId(), sysInPassage.getOutSpIds()
//                                        , AutoPublishResultEmum.SUCCESS.getCode(), false);
//                            }else{
//                                autoPublishContentFeignClient.dealContent(inOrder.getCorrelateId(), sysInPassage.getOutPassageIds()
//                                        , AutoPublishResultEmum.FAILED.getCode(), false);
//                            }
                        } catch (Exception e) {
                            log.info("调用feign失败，数据：{}", inOrder.toString());
                            inOrderService.inOrderHandler(InOrderStatusEnums.TODATABASE_AUTO_PUBLIC_FEIGN_FAIL.getCode(), inOrder,
                                    resultData.getResultDesc(), FileConstants.R_FAILE_INT, InOrderStatusEnums.FEEDBACK.getCode());
                        }
                        //插入反馈表
                        inOrderService.inOrderHandler(InOrderStatusEnums.TODATABASE_SUCCESS.getCode(), inOrder,
                                FileConstants.R_SUCCESS_DES, FileConstants.R_SUCCESS_INT, InOrderStatusEnums.FEEDBACK_AUTO_DOING.getCode());

                    } else {
                        inOrderService.inOrderHandler(InOrderStatusEnums.TODATABASE_SUCCESS.getCode(), inOrder,
                                FileConstants.R_SUCCESS_DES, FileConstants.R_SUCCESS_INT, InOrderStatusEnums.FEEDBACK.getCode());
                    }
                } else {
                    inOrderService.inOrderHandler(InOrderStatusEnums.TODATABASE_SUCCESS.getCode(), inOrder,
                            FileConstants.R_SUCCESS_DES, FileConstants.R_SUCCESS_INT, InOrderStatusEnums.FEEDBACK.getCode());
                }
            } else {
                inOrderService.inOrderHandler(InOrderStatusEnums.TODATABASE_SUCCESS.getCode(), inOrder,
                        FileConstants.R_SUCCESS_DES, FileConstants.R_SUCCESS_INT, InOrderStatusEnums.FEEDBACK.getCode());
            }
        } else {
            inOrderService.inOrderHandler(InOrderStatusEnums.TODATABASE_SUCCESS.getCode(), inOrder,
                    FileConstants.R_SUCCESS_DES, FileConstants.R_SUCCESS_INT, InOrderStatusEnums.FEEDBACK.getCode());
        }
    }

    /**
     * 指定主工单，判断是否对应的子工单是否全部完成
     *
     * @param inOrderId
     */
    public void update(String inOrderId) {
        //获取需反馈的in_order
        InOrder inOrder = inOrderService.getById(inOrderId);
        if (inOrder != null) {
            update(inOrder);
        }

    }

    /**
     * 执行任务
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void executeJob() {
        try {
            log.info("UpdateInResultJob start； " + DateUtils.dateTimeNow());
            update();
            log.info("UpdateInResultJob end : " + DateUtils.dateTimeNow());
        } catch (Exception exception) {
            log.info("UpdateInResultJob()" + exception.getMessage(), exception);
        }
    }
}
