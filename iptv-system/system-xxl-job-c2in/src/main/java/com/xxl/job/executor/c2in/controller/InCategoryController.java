package com.xxl.job.executor.c2in.controller;

import javax.validation.Valid;

import com.pukka.iptv.common.base.vo.CommonResponse;
import com.pukka.iptv.common.data.model.in.InCategory;
import com.pukka.iptv.common.data.vo.*;
import com.xxl.job.executor.c2in.job.FeedBackJob;
import com.xxl.job.executor.c2in.job.ParseJob;
import com.xxl.job.executor.c2in.job.UpdateInResultJob;
import com.xxl.job.executor.c2in.service.InCategoryService;
import com.xxl.job.executor.c2in.service.InOrderAPIService;
import com.xxl.job.executor.c2in.service.InOrderService;
import com.xxl.job.executor.service.jobhandler.OrderProcessXxlJob;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.checkerframework.checker.units.qual.A;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.*;
import org.springframework.beans.factory.annotation.Autowired;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.AllArgsConstructor;

/**
 *
 * @author: tan
 * @date: 2021-8-24 10:43:49
 */

@RestController
@AllArgsConstructor
@RequestMapping(value = "/inCategory", produces = MediaType.APPLICATION_JSON_VALUE)
@Api(tags="inCategory管理")
public class InCategoryController {

    @Autowired
    private InCategoryService inCategoryService;

    @Autowired
    private ParseJob parseJob;

    @Autowired
    private InOrderService inOrderService;

    @Autowired
    private InOrderAPIService inOrderAPIService;

    @Autowired
    private UpdateInResultJob updateInResultJob;

    @Autowired
    private FeedBackJob feedBackJob;

    @Autowired
    private OrderProcessXxlJob orderProcessXxlJob;


    @ApiOperation(value = "分页")
    @GetMapping("/page" )
    public CommonResponse<Page> page(@Valid Page page, InCategory inCategory) {

//        inOrderAPIService.inOrder2CmsBmsForObjects();
        parseJob.inOrderParse();
//        updateInResultJob.executeJob();
//        feedBackJob.executeJob();
//          inOrderService.inOrder2CmsBmsForObjects();
//        orderProcessXxlJob.handler();
        String curl = "ftp://cjy:cjy@**************:21/head0000000REGIST-series.xml";
//        try {
//            FtpUtil ftpUtil = new FtpUtil(curl);
//            String xmlString = ftpUtil.getFtpContent(curl);
//            if (xmlString == null){
//
//            }else{
//                xmlString = xmlString.substring(xmlString.indexOf("<?xml"));
//            }
//
//            AdiParse adiParse = new AdiParse();
//            adiParse.parse(xmlString);
//            adiParse.save(null);
//        } catch (Exception e) {
//            e.printStackTrace();
//        }
        return  CommonResponse.success(inCategoryService.page(page, Wrappers.query(inCategory)));
    }

    @ApiOperation(value = "详情")
    @GetMapping("/getById")
    public CommonResponse<InCategory> getById(@Valid @RequestParam(name = "id", required = true)  Long id) {
        return CommonResponse.success(inCategoryService.getById(id));
    }

    @ApiOperation(value = "新增")
    @PostMapping("/save")
    public CommonResponse<Boolean> save(@Valid @RequestBody InCategory inCategory) {
        return  CommonResponse.success(inCategoryService.save(inCategory));
    }

    @ApiOperation(value = "修改")
    @PutMapping("/update")
    public CommonResponse<Boolean> updateById(@Valid @RequestBody InCategory inCategory) {
        return CommonResponse.success(inCategoryService.updateById(inCategory));
    }

    @ApiOperation(value = "删除")
    @DeleteMapping("/deleteById" )
    public CommonResponse<Boolean> deleteById(@Valid @RequestParam(name = "id", required = true) Long id) {
        return  CommonResponse.success(inCategoryService.removeById(id));
    }

    @ApiOperation(value = "批量删除")
    @DeleteMapping("/deleteByIds" )
    public CommonResponse<Boolean> deleteByIds(@Valid @RequestBody IdList idList) {
        IdList idList1 = new IdList();
        return  CommonResponse.success(inCategoryService.removeByIds(idList.getIds()));
    }

}
