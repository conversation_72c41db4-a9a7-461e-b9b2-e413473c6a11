package com.xxl.job.executor.c2in.service.condition.rule.enums;

/**
 * @author: wz
 * @date: 2021/9/10 10:55
 * @description:
 */
public enum PublishCheck {
    DEFAULT,
    PUBLISH_SUCCESS,//检查是否都发布成功 发布成功检查
    NOT_PUBLISH, //检查是否都未发布
    CAN_PUBLISH,//待发布检查 检查 媒资是否是 [待发布，待更新，发布失败，更新失败，回收成功，回收成功] 的状态; 如果不是 则返回异常
    ING,//发布中，更新中，回收中
    MUST_ING,//检查 不是 [发布中，更新中，回收中] 的状态; 如果不是 则返回异常
    PU,//已发布下游检查
    UN_PUBLISHED,//未发布到下游检查
    CAN_DELETE//是否可以删除该频道关系
}
