package com.xxl.job.executor.c2in.model;

import com.baomidou.mybatisplus.annotation.*;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.NoArgsConstructor;
import lombok.EqualsAndHashCode;
import lombok.Data;

import java.util.Date;

/**
 *
 * @author: liuli
 * @date: 2021年10月12日 上午9:47:52
 */

@Data
@EqualsAndHashCode
@JsonIgnoreProperties(value = { "handler" })
@NoArgsConstructor
@TableName(value = "bms_channel",autoResultMap=true)
public class BmsChannel extends Model<BmsChannel> implements java.io.Serializable{

    private static final long serialVersionUID = 1L;
	/**主键*/
	@TableField(value = "id")
    @TableId(type = IdType.AUTO)
    @ApiModelProperty(value="主键",dataType="Long",name="id")
    private Long id;
	/**全局唯一标识*/
	@TableField(value = "code", fill = FieldFill.INSERT)
    @ApiModelProperty(value="全局唯一标识",dataType="String",name="code")
    private String code;
	/**建议频道号*/
	@TableField(value = "channel_number")
    @ApiModelProperty(value="建议频道号",dataType="String",name="channelNumber")
    private String channelNumber;
	/**频道名称*/
	@TableField(value = "name")
    @ApiModelProperty(value="频道名称",dataType="String",name="name")
    private String name;
	/**cpId*/
	@TableField(value = "cp_id")
    @ApiModelProperty(value="cpId",dataType="Long",name="cpId")
    private Long cpId;
	/**CP名称*/
	@TableField(value = "cp_name")
    @ApiModelProperty(value="CP名称",dataType="String",name="cpName")
    private String cpName;
	/**创建时间*/
	@TableField(value = "create_time")
    @ApiModelProperty(value="创建时间",dataType="Date",name="createTime")
    @JsonFormat(shape = JsonFormat.Shape.STRING,pattern = "yyyy-MM-dd HH:mm:ss",timezone="GMT+8")
    private Date createTime;
	/**更新时间*/
	@TableField(value = "update_time")
    @ApiModelProperty(value="更新时间",dataType="Date",name="updateTime")
    @JsonFormat(shape = JsonFormat.Shape.STRING,pattern = "yyyy-MM-dd HH:mm:ss",timezone="GMT+8")
    private Date updateTime;
	/**spId*/
	@TableField(value = "sp_id")
    @ApiModelProperty(value="spId",dataType="Long",name="spId")
    private Long spId;
	/**SP名称*/
	@TableField(value = "sp_name")
    @ApiModelProperty(value="SP名称",dataType="String",name="spName")
    private String spName;
	/**发布通道ID，多个ID以英文逗号隔开*/
	@TableField(value = "out_passage_ids")
    @ApiModelProperty(value="发布通道ID，多个ID以英文逗号隔开",dataType="String",name="outPassageIds")
    private String outPassageIds;
	/**分发通道名称以英文逗号 隔开*/
	@TableField(value = "out_passage_names")
    @ApiModelProperty(value="分发通道名称以英文逗号 隔开",dataType="String",name="outPassageNames")
    private String outPassageNames;
	/**发布状态  1：待发布 2：发布中 3：发布成功 4：发布失败 5：待更新 6：更新中 7：更新失败 8：回收中 9：回收成功 10：回收失败*/
	@TableField(value = "publish_status")
    @ApiModelProperty(value="发布状态  1：待发布 2：发布中 3：发布成功 4：发布失败 5：待更新 6：更新中 7：更新失败 8：回收中 9：回收成功 10：回收失败",dataType="Integer",name="publishStatus")
    private Integer publishStatus;
	/**发布时间*/
	@TableField(value = "publish_time")
    @ApiModelProperty(value="发布时间",dataType="String",name="publishTime")
    private String publishTime;
	/**发布描述*/
	@TableField(value = "publish_description")
    @ApiModelProperty(value="发布描述",dataType="String",name="publishDescription")
    private String publishDescription;
	/**cms频道code*/
	@TableField(value = "cms_channel_code")
    @ApiModelProperty(value="cms频道code",dataType="String",name="cmsChannelCode")
    private String cmsChannelCode;
	/**cms频道ID*/
	@TableField(value = "cms_channel_id")
    @ApiModelProperty(value="cms频道ID",dataType="Long",name="cmsChannelId")
    private Long cmsChannelId;
	/**内容提供商标识*/
	@TableField(value = "content_provider")
    @ApiModelProperty(value="内容提供商标识",dataType="String",name="contentProvider")
    private String contentProvider;
	/**产品包ID集合，以英文逗号分割*/
	@TableField(value = "package_ids")
    @ApiModelProperty(value="产品包ID集合，以英文逗号分割",dataType="String",name="packageIds")
    private String packageIds;
	/**产品包name集合，以英文逗号分割*/
	@TableField(value = "package_names")
    @ApiModelProperty(value="产品包name集合，以英文逗号分割",dataType="String",name="packageNames")
    private String packageNames;
	/**栏目ID集合，以英文逗号分割*/
	@TableField(value = "category_ids")
    @ApiModelProperty(value="栏目ID集合，以英文逗号分割",dataType="String",name="categoryIds")
    private String categoryIds;
	/**栏目Name集合，以英文逗号分割*/
	@TableField(value = "category_names")
    @ApiModelProperty(value="栏目Name集合，以英文逗号分割",dataType="String",name="categoryNames")
    private String categoryNames;
	/**定时发布，时间存在则代表已经设定为定时发布*/
	@TableField(value = "timed_publish")
    @ApiModelProperty(value="定时发布，时间存在则代表已经设定为定时发布",dataType="String",name="timedPublish")
    private String timedPublish;
}
