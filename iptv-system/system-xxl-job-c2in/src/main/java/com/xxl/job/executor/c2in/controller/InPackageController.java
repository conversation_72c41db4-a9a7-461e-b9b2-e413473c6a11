package com.xxl.job.executor.c2in.controller;

import javax.validation.Valid;

import com.pukka.iptv.common.base.vo.CommonResponse;
import com.pukka.iptv.common.data.model.in.InPackage;
import com.pukka.iptv.common.data.vo.*;
import com.xxl.job.executor.c2in.service.InPackageService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.*;
import org.springframework.beans.factory.annotation.Autowired;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.AllArgsConstructor;

/**
 *
 * @author: tan
 * @date: 2021-8-24 10:47:07
 */

@RestController
@AllArgsConstructor
@RequestMapping(value = "/inPackage", produces = MediaType.APPLICATION_JSON_VALUE)
@Api(tags="inPackage管理")
public class InPackageController {

    @Autowired
    private InPackageService inPackageService;

    @ApiOperation(value = "分页")
    @GetMapping("/page" )
    public CommonResponse<Page> page(@Valid Page page, InPackage inPackage) {
        return  CommonResponse.success(inPackageService.page(page, Wrappers.query(inPackage)));
    }

    @ApiOperation(value = "详情")
    @GetMapping("/getById")
    public CommonResponse<InPackage> getById(@Valid @RequestParam(name = "id", required = true)  Long id) {
        return CommonResponse.success(inPackageService.getById(id));
    }

    @ApiOperation(value = "新增")
    @PostMapping("/save")
    public CommonResponse<Boolean> save(@Valid @RequestBody InPackage inPackage) {
        return  CommonResponse.success(inPackageService.save(inPackage));
    }

    @ApiOperation(value = "修改")
    @PutMapping("/update")
    public CommonResponse<Boolean> updateById(@Valid @RequestBody InPackage inPackage) {
        return CommonResponse.success(inPackageService.updateById(inPackage));
    }

    @ApiOperation(value = "删除")
    @DeleteMapping("/deleteById" )
    public CommonResponse<Boolean> deleteById(@Valid @RequestParam(name = "id", required = true) Long id) {
        return  CommonResponse.success(inPackageService.removeById(id));
    }

    @ApiOperation(value = "批量删除")
    @DeleteMapping("/deleteByIds" )
    public CommonResponse<Boolean> deleteByIds(@Valid @RequestBody IdList idList) {
        return  CommonResponse.success(inPackageService.removeByIds(idList.getIds()));
    }

}
