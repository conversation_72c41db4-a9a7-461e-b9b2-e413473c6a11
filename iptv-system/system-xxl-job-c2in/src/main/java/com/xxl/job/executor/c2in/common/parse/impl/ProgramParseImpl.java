package com.xxl.job.executor.c2in.common.parse.impl;

import com.google.common.base.Strings;
import com.pukka.iptv.common.base.enums.DefinitionEnums;
import com.pukka.iptv.common.core.util.DateUtils;
import com.pukka.iptv.common.core.util.StringUtils;
import com.pukka.iptv.common.data.model.in.InProgram;
import com.xxl.job.executor.c2in.common.enums.ActionEnums;
import com.xxl.job.executor.c2in.common.enums.InProgramSourceTypeEnums;
import com.xxl.job.executor.c2in.common.exception.ParserException;
import com.xxl.job.executor.c2in.common.parse.IParse;
import com.xxl.job.executor.c2in.common.util.ManageHelper;
import org.apache.commons.lang3.math.NumberUtils;
import org.dom4j.Attribute;
import org.dom4j.Element;

import java.util.Date;
import java.util.Iterator;
import java.util.List;

/**
 * @ClassName ProgramParseImpl
 * @Description TODO
 * <AUTHOR>
 * @Date 2021/8/27 15:39
 * @Version
 */
public class ProgramParseImpl implements IParse<Element, InProgram> {
    @Override
    public InProgram get(Element element) {
        if (element == null)
            return null;
        Attribute actionAttr = element.attribute("Action");
        if (actionAttr == null) {
            throw new ParserException("Program 缺少'Action'属性值");
        }
        InProgram inProgram = new InProgram();

        inProgram.setAction(ActionEnums.getCodeByInfo(actionAttr.getValue()));
        Attribute codeAttr = element.attribute("Code");
        if (codeAttr == null || Strings.isNullOrEmpty(codeAttr.getValue())) {
            throw new ParserException("Program 缺少'Code'属性值");
        }
        inProgram.setCode(codeAttr.getValue());

        Attribute idAttr = element.attribute("ID");
        if (idAttr == null || Strings.isNullOrEmpty(idAttr.getValue())) {
            throw new ParserException("Program 缺少'ID'属性值");
        }
        inProgram.setCorrelateId(idAttr.getValue());

        List<?> propertysEle = element.elements("Property");
        if (propertysEle == null)
            return inProgram;

        Attribute nameAttr;
        String text;
        for (Iterator<?> iter = propertysEle.iterator(); iter.hasNext(); ) {
            Element property = (Element) iter.next();
            nameAttr = property.attribute("Name");
            if (nameAttr == null || StringUtils.isEmpty(nameAttr.getValue())) {
                if(ManageHelper.isRegistXML(inProgram.getAction())){
                    throw new ParserException("Program属性列表中缺少'Name'属性名称");
                }

            }
            text = property.getTextTrim();
            String value = nameAttr.getValue();
            if (value.equals("Name")) {
                inProgram.setName(text);
                continue;
            }
            if (value.equals("OrderNumber")) {
                inProgram.setOrderNumber(text);
                continue;
            }
            if (value.equals("Status")) {
                inProgram.setStatus(NumberUtils.toInt(text, 1));
                continue;
            }
            if (value.equals("OriginalName")) {
                inProgram.setOriginalName(text);
                continue;
            }
            if (value.equals("SortName")) {
                inProgram.setSortName(text);
                continue;
            }
            if (value.equals("SearchName")) {
                inProgram.setSearchName(text);
                continue;
            }
            if (value.equals("ActorDisplay")) {
                inProgram.setActorDisplay(text);
                continue;
            }
            if (value.equals("WriterDisplay")) {
                inProgram.setWriterDisplay(text);
                continue;
            }
            if (value.equals("OriginalCountry")) {
                inProgram.setOriginalCountry(text);
                continue;
            }
            if (value.equals("Language")) {
                inProgram.setLanguage(text);
                continue;
            }
            if (value.equals("ReleaseYear")) {
                inProgram.setReleaseYear(text);
                continue;
            }
            if (value.equals("OrgAirDate")) {
                inProgram.setOrgAirDate(text);
                continue;
            }
            if (value.equals("LicensingWindowStart")) {
                inProgram.setLicensingWindowStart(text);
                continue;
            }
            if (value.equals("LicensingWindowEnd")) {
                inProgram.setLicensingWindowEnd(text);
                continue;
            }
            if (value.equals("DisplayAsNew")) {
                inProgram.setDisplayAsNew(NumberUtils.toInt(text));
                continue;
            }
            if (value.equals("DisplayAsLastChance")) {
                inProgram.setDisplayAsLastChance(NumberUtils.toInt(text));
                continue;
            }
            if (value.equals("Macrovision")) {
                inProgram.setMacrovision(NumberUtils.toInt(text));
                continue;
            }
            if (value.equals("Description")) {
                inProgram.setDescription(text);
                continue;
            }
            if (value.equals("PriceTaxIn")) {
                if(StringUtils.isNotEmpty(text))
                    inProgram.setPriceTaxIn(NumberUtils.toScaledBigDecimal(text));
                continue;
            }
            if (value.equals("SourceType")) {
                inProgram.setSourceType(NumberUtils.toInt(text));
                continue;
            }
            if (value.equals("SeriesFlag")) {
                inProgram.setSeriesFlag(NumberUtils.toInt(text));
                continue;
            }
            if (value.equals("Kpeople")) {
                inProgram.setKpeople(text);
                continue;
            }
            if (value.equals("Director")) {
                inProgram.setDirector(text);
                continue;
            }
            if (value.equals("ScriptWriter")) {
                inProgram.setScriptWriter(text);
                continue;
            }
            if (value.equals("Compere")) {
                inProgram.setCompere(text);
                continue;
            }
            if (value.equals("Guest")) {
                inProgram.setGuest(text);
                continue;
            }
            if (value.equals("reporter")) {
                inProgram.setReporter(text);
                continue;
            }
            if (value.equals("Reporter")) {
                inProgram.setReporter(text);
                continue;
            }

            if (value.equals("OPIncharge")) {
                inProgram.setOpIncharge(text);
                continue;
            }
            if (value.equals("VSPCode")) {
                inProgram.setVspCode(text);
                continue;
            }
            if (value.equals("CopyRight")) {
                inProgram.setCopyRight(text);
                continue;
            }
            if (value.equals("ContentProvider")) {
                inProgram.setContentProvider(text);
                continue;
            }
            if (value.equals("Duration")) {
                inProgram.setDuration(NumberUtils.toInt(text));
            }

            if (value.equals("PgmCategory")) {
                inProgram.setPgmCategory(text);
                continue;
            }
            if (value.equals("PgmSndClass")) {
                inProgram.setPgmSndClass(text);
                continue;
            }
            if (value.equals("Rating")) {
                inProgram.setRating(text);
                continue;
            }
/*

            if (value.equals("Duration1")) {
                inProgram.setDuration(NumberUtils.toInt(text));
                continue;
            }
*/

            /** 兼容旧规范 PgmCategory */
            if (value.equals("Type")) {
                inProgram.setType(text);
                continue;
            }
            /** 兼容旧规范,PgmSndClass */
            if (value.equals("Tags")) {
                inProgram.setTags(text);
                continue;
            }
            /** 清晰度，DefinitionFlag  */
            if (value.equals("DefinitionFlag")) {
                inProgram.setDefinitionFlag(NumberUtils.toInt(text,  DefinitionEnums.OTHER.getCode()));
                continue;
            }
            if (value.equals("NewPrice")) {
                inProgram.setNewPrice(text);
                continue;
            }
        }
        if (Strings.isNullOrEmpty(inProgram.getName())) {
            if(ManageHelper.isRegistXML(inProgram.getAction())){
                throw new ParserException("Program属性列表中缺少'Name'属性值");
            }

        }
        /**
         * 清晰度默认为其他
         */
         if(null == inProgram.getDefinitionFlag()){
             inProgram.setDefinitionFlag(DefinitionEnums.OTHER.getCode());
         }
        /**
         * 如果为空，则赋值为1；如果不在[1,2] 范围则抛异常
         */
        if(null == inProgram.getSourceType()){
            inProgram.setSourceType(InProgramSourceTypeEnums.VIDEO.getCode());
        }

        if((inProgram.getSourceType() != InProgramSourceTypeEnums.VIDEO.getCode()) &&
                (inProgram.getSourceType() != InProgramSourceTypeEnums.PICTURE.getCode()) ){
            throw new ParserException("Program属性列表中'SourceType'属性值范围值不在[1,2]中");
        }

        if(!"DELETE".equals(actionAttr.getValue())){
            if (Strings.isNullOrEmpty(inProgram.getLicensingWindowEnd())) {
                throw new ParserException("Program属性列表中缺少'LicensingWindowEnd'属性值");
            }

            if (Strings.isNullOrEmpty(inProgram.getLicensingWindowStart())) {
                throw new ParserException("Program属性列表中缺少'LicensingWindowStart'属性值");
            }

            if (Strings.isNullOrEmpty(inProgram.getContentProvider())) {
//                throw new ParserException("Program属性列表中缺少'ContentProvider'属性值");
            }

            if (Strings.isNullOrEmpty(inProgram.getName())) {
                throw new ParserException("Program属性列表中缺少'Name'属性值");
            }
        }else{
            inProgram.setLicensingWindowStart("");
            inProgram.setLicensingWindowEnd("");
        }
        inProgram.setCreateTime(new Date());
        return inProgram;
    }
}
