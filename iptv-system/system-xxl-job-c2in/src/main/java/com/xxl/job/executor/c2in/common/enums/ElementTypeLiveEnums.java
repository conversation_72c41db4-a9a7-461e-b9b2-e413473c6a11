package com.xxl.job.executor.c2in.common.enums;

public enum ElementTypeLiveEnums {

    Channel(7, "Channel"),
    PhysicalChannel(8, "PhysicalChannel"),
    Schedule(10, "Schedule"),
    NullElemet(99, "NullElemet");

    private final Integer code;
    private final String info;


    public static Integer getCodeByInfo(String info) {
        ElementTypeLiveEnums[] actionEnums = values();
        for (ElementTypeLiveEnums element : actionEnums) {
            if (element.getInfo().equals(info)) {
                return element.getCode();
            }
        }
        return null;
    }

    public static String getInfoByCode(Integer code) {
        ElementTypeLiveEnums[] actionEnums = values();
        for (ElementTypeLiveEnums element : actionEnums) {
            if (element.getCode().intValue() == code.intValue()) {
                return element.getInfo();
            }
        }
        return null;
    }

    ElementTypeLiveEnums(Integer code, String info)
    {
        this.code = code;
        this.info = info;
    }

    public Integer getCode()
    {
        return code;
    }

    public String getInfo()
    {
        return info;
    }
}
