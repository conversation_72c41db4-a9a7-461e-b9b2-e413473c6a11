package com.xxl.job.executor.c2in.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.pukka.iptv.common.data.model.*;
import com.xxl.job.executor.c2in.model.BmsPackage;

import java.util.List;

/**
 *
 * @author: liuli
 * @date: 2021年9月2日 下午3:35:20
 */

public interface BmsPackageService extends IService<BmsPackage> {
    /**
     * 根据spid和packageCode查询产品包
     *
     * @param packageCode packageCode
     * @param spId spId
     * @return
     */
    BmsPackage getByCodeAndSpId(String packageCode, Long spId);

    /**
     * 根据packageCode查询数据
     *
     * @param packageCode packageCode
     * @return
     */
    List<BmsPackage> listByCode(String packageCode);

    /**
     * 根据packageCode删除
     *
     * @param code code
     */
    void removeByCode(String code);

    List<BmsPackage> listByCodeAndCpId(String elementCode, Long id);

    BmsPackage getByCode(String packCode);
}


