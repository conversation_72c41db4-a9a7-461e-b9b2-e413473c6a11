package com.xxl.job.executor.c2in.order.processor;

import com.pukka.iptv.common.data.model.in.InOrder;
import com.pukka.iptv.common.data.model.sys.SysCp;
import com.xxl.job.executor.c2in.common.util.StatusUtils;
import org.apache.poi.ss.formula.functions.T;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @Date 2021/8/25 16:23
 * @Description 处理器顶层接口  其实现类创建bean时需要指定beanName 与拿到的数据中的数据key相同
 */

public interface IOrderProcessor {

    /**
     * 传递当前子工单所需要的依赖的其他数据,根据自身业务需要实现
     *
     * @param order 工单
     */
    default void setDataList(Map<String, List<Object>> order) {}

    /**
     * 前置处理逻辑
     * @param dataList dataList
     * @param order order
     */
    default void beforeProcess(List<Object> dataList, InOrder order){}

    /**
     * 处理子工单数据
     *
     * @param dataList 数据信息
     * @param order 主工单对象
     */
    void process(List<Object> dataList, InOrder order);

    /**
     * 更新对象
     *
     * @param data 数据信息
     * @param order 主工单对象
     */
    void updateProcess(Object data, InOrder order);

    /**
     * 对象新增
     *
     * @param data 数据信息
     * @param order 主工单对象
     */
    void registProcess(Object data, InOrder order);

    /**
     * 对象删除
     *
     * @param data 要删除的对象
     * @param order 工单对象
     */
    void deleteProcess(Object data, InOrder order);

    /**
     * 工单处理后根据处理结果写入工单状态
     * @param order 主工单
     * @param data 对象
     * @param status 状态
     */
    void updateStatus(InOrder order, Object data, StatusUtils.Status status);

    /**
     * 根据cspId统一获取配置
     * @param order
     */
    void initSysConfig(InOrder order);

    /**
     * 判断节目单对应的频道是否存在
     * @param data
     * @param order
     * @return
     */
    boolean deleteProcessIsExist(Object data, InOrder order);

}
