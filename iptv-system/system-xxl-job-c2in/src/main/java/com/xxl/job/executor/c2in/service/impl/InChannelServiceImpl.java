package com.xxl.job.executor.c2in.service.impl;

import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.pukka.iptv.common.data.model.in.InChannel;
import com.xxl.job.executor.c2in.common.util.StatusUtils;
import com.xxl.job.executor.c2in.mapper.InChannelMapper;
import com.xxl.job.executor.c2in.service.InChannelService;
import org.springframework.stereotype.Service;


/**
 *
 * @author: tan
 * @date: 2021-8-24 10:45:45
 */

@Service
public class InChannelServiceImpl extends ServiceImpl<InChannelMapper, InChannel> implements InChannelService {

    @Override
    public void updateStatusById(Long id, StatusUtils.Status status) {
        InChannel inChannel = new InChannel();
        inChannel.setResult(status.getCode());
        inChannel.setErrorDescription(status.getMsg());
        LambdaUpdateWrapper<InChannel> updateWrapper = Wrappers.lambdaUpdate();
        updateWrapper.eq(InChannel::getId, id);
        this.update(inChannel, updateWrapper);
    }
}


