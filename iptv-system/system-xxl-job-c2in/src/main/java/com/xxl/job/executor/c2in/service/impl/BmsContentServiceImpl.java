package com.xxl.job.executor.c2in.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.xxl.job.executor.c2in.common.constant.TextConstants;
import com.xxl.job.executor.c2in.common.enums.ContentTypeEnums;
import com.xxl.job.executor.c2in.common.util.StrUtils;
import com.xxl.job.executor.c2in.mapper.BmsContentMapper;
import com.xxl.job.executor.c2in.model.BmsCategory;
import com.xxl.job.executor.c2in.model.BmsContent;
import com.xxl.job.executor.c2in.model.BmsPackage;
import com.xxl.job.executor.c2in.service.BmsContentService;
import org.springframework.stereotype.Service;

import javax.print.DocFlavor;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;


/**
 *
 * @author: liuli
 * @date: 2021年9月2日 下午3:35:12
 */

@Service
public class BmsContentServiceImpl extends ServiceImpl<BmsContentMapper, BmsContent> implements BmsContentService {

    @Override
    public BmsContent getByCodeAndSpId(String cmsContentCode, Long spId) {
        LambdaQueryWrapper<BmsContent> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.eq(BmsContent::getCmsContentCode, cmsContentCode).eq(BmsContent::getSpId, spId).last(TextConstants.LIMIT_ONE);
        return this.getOne(queryWrapper);
    }

    @Override
    public List<BmsContent> listByCmsContentCodeCpId(String seriesCode, Long cpId) {
        LambdaQueryWrapper<BmsContent> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.eq(BmsContent::getCmsContentCode, seriesCode);
        queryWrapper.eq(BmsContent::getCpId, cpId);
        return this.list(queryWrapper);
    }

    @Override
    public void removeByCmsContentCode(String code) {
        LambdaQueryWrapper<BmsContent> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.eq(BmsContent::getCmsContentCode, code);
        this.remove(queryWrapper);
    }

    @Override
    public Long countByCmsContentCode(String code) {
        LambdaQueryWrapper<BmsContent> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.eq(BmsContent::getCmsContentCode, code);
        return this.count(queryWrapper);
    }

    @Override
    public void removeMappingPackageByIds(List<BmsContent> filterLit, List<BmsPackage> packageList) {
        Set<String> idSet = packageList.stream().map(bc -> String.valueOf(bc.getId())).collect(Collectors.toSet());
        Set<String> nameSet = packageList.stream().map(BmsPackage::getName).collect(Collectors.toSet());
        for (BmsContent bmsContent : filterLit) {
            LambdaUpdateWrapper<BmsContent> updateWrapper = Wrappers.lambdaUpdate();
            updateWrapper.eq(BmsContent::getId, bmsContent.getId());
            updateWrapper.set(BmsContent::getPackageIds, StrUtils.removeSet(bmsContent.getPackageIds(), idSet));
            updateWrapper.set(BmsContent::getPackageNames, StrUtils.removeSet(bmsContent.getPackageNames(), nameSet));
            update(updateWrapper);
        }
    }

    @Override
    public void removeMappingCategoryByIds(List<BmsContent> filterLit, List<BmsCategory> categoryList) {
        Set<String> idSet = categoryList.stream().map(bc -> String.valueOf(bc.getId())).collect(Collectors.toSet());
        Set<String> nameSet = categoryList.stream().map(BmsCategory::getName).collect(Collectors.toSet());
        for (BmsContent bmsContent : filterLit) {
            LambdaUpdateWrapper<BmsContent> updateWrapper = Wrappers.lambdaUpdate();
            updateWrapper.eq(BmsContent::getId, bmsContent.getId());
            updateWrapper.set(BmsContent::getCategoryIds, StrUtils.removeSet(bmsContent.getCategoryIds(), idSet));
            updateWrapper.set(BmsContent::getCategoryNames, StrUtils.removeSet(bmsContent.getCategoryNames(), nameSet));
            update(updateWrapper);
        }
    }

    @Override
    public List<BmsContent> listByCmsSeriesId(Long id) {
        LambdaQueryWrapper<BmsContent> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.eq(BmsContent::getCmsContentId, id).
                and(wrapper -> wrapper.eq(BmsContent::getContentType, ContentTypeEnums.SERIES_0.getCode()).or().eq(BmsContent::getContentType, ContentTypeEnums.SERIES_1.getCode()));
        return this.list(queryWrapper);
    }

    @Override
    public List<BmsContent> listByCmsProgramId(Long id) {
        LambdaQueryWrapper<BmsContent> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.eq(BmsContent::getCmsContentId, id).eq(BmsContent::getContentType, ContentTypeEnums.PROGRAM_0.getCode());
        return this.list(queryWrapper);
    }

    @Override
    public List<BmsContent> listByCmsContentCode(String cmsContentCode) {
        LambdaQueryWrapper<BmsContent> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.eq(BmsContent::getCmsContentCode, cmsContentCode);
        return this.list(queryWrapper);
    }
}


