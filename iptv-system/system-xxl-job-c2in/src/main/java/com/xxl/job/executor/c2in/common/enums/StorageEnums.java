package com.xxl.job.executor.c2in.common.enums;

/**
 * 存储类型类
 */
public enum StorageEnums {

    OSS(1, "OSS"),
    OSSFTP(2, "OSSFTP"),
    FTP(3, "FTP"),
    ISSEARCH(4, "ISSEARCH");

    private final Integer code;
    private final String info;


    public static Integer getCodeByInfo(String info) {
        StorageEnums[] actionEnums = values();
        for (StorageEnums element : actionEnums) {
            if (element.getInfo().equals(info)) {
                return element.getCode();
            }
        }
        return null;
    }

    public static String getInfoByCode(Integer code) {
        StorageEnums[] actionEnums = values();
        for (StorageEnums element : actionEnums) {
            if (element.getCode().intValue() == code.intValue()) {
                return element.getInfo();
            }
        }
        return null;
    }

    StorageEnums(Integer code, String info)
    {
        this.code = code;
        this.info = info;
    }

    public Integer getCode()
    {
        return code;
    }

    public String getInfo()
    {
        return info;
    }
}
