package com.xxl.job.executor.c2in.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.pukka.iptv.common.api.feign.sys.DownloadFeignClient;
import com.pukka.iptv.common.base.vo.CommonResponse;
import com.pukka.iptv.common.data.model.cms.CmsMovie;
import com.xxl.job.executor.c2in.common.constant.TextConstants;
import com.xxl.job.executor.c2in.common.enums.ContentStatusEnums;
import com.xxl.job.executor.c2in.mapper.CmsMovieMapper;
import com.xxl.job.executor.c2in.service.CmsMovieService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 *
 * @author: tan
 * @date: 2021年8月27日 上午10:01:04
 */

@Service
@Slf4j
public class CmsMovieServiceImpl extends ServiceImpl<CmsMovieMapper, CmsMovie> implements CmsMovieService {

    @Autowired
    DownloadFeignClient downloadFeignClient;

    @Override
    public void removeMappingByContentCode(String contentCode, String movieCode) {
        LambdaUpdateWrapper<CmsMovie> updateWrapper = Wrappers.lambdaUpdate();
        updateWrapper.eq(CmsMovie::getContentCode, contentCode).eq(CmsMovie::getCode, movieCode);
        updateWrapper.set(CmsMovie::getContentCode, null)
                .set(CmsMovie::getContentStatus, null)
                .set(CmsMovie::getContentType, null)
                .set(CmsMovie::getContentId, null);
        this.update(updateWrapper);
    }

    @Override
    public Long countByResourceCode(String code) {
        LambdaQueryWrapper<CmsMovie> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.eq(CmsMovie::getResourceCode, code);
        return this.count(queryWrapper);
    }

    @Override
    public CmsMovie getByResourceCode(String code) {
        LambdaQueryWrapper<CmsMovie> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.eq(CmsMovie::getResourceCode, code).last(TextConstants.LIMIT_ONE);
        return this.getOne(queryWrapper);
    }

    @Override
    public CmsMovie getByResourceCodeCpId(String code, Long cpId) {
        LambdaQueryWrapper<CmsMovie> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.eq(CmsMovie::getResourceCode, code).eq(CmsMovie::getCpId, cpId).last(TextConstants.LIMIT_ONE);
        return this.getOne(queryWrapper);
    }


    @Override
    public void removeByResourceCode(String resourceCode) {
        LambdaQueryWrapper<CmsMovie> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.eq(CmsMovie::getResourceCode, resourceCode);
        remove(queryWrapper);
    }

    @Override
    public void updateMediaSpec(String mediaSpec,String code) {
        this.update(Wrappers.<CmsMovie>lambdaUpdate().set(CmsMovie::getMediaSpec,mediaSpec).eq(CmsMovie::getCode,code));
    }
}


