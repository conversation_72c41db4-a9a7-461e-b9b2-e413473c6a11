package com.xxl.job.executor.c2in.common.util;

import com.xxl.job.executor.c2in.common.enums.ActionEnums;

import java.text.SimpleDateFormat;
import java.util.*;
import java.util.concurrent.locks.Lock;
import java.util.concurrent.locks.ReentrantLock;


public class ManageHelper {

	 
	 //获取对象ID
	 public static String getObjectID(String type){
		 StringBuilder sb = new StringBuilder();
		 sb.append("0080"); //CP ID		 
		 //类型
		 if("PROG".equals(type)){
			 sb.append("00000001");      //Program
		 }else if("MOVI".equals(type)){
			 sb.append("00000002");      //Movie
		 }else if("CAST".equals(type)){
			 sb.append("00000003");      //Cast
		 }else if("CARM".equals(type)){
			 sb.append("00000004");      //CastRoleMap
		 }else if("Channel".equals(type)){
			 sb.append("00000005");      //Channel
		 }else if("PHCH".equals(type)){
			 sb.append("00000006");      //PhysicalChannel
		 }else if("SCHE".equals(type)){
			 sb.append("00000007");      //Schedule
		 }else if("PICT".equals(type)){
			 sb.append("00000008");      //Picture
		 }else if("CATE".equals(type)){
			 sb.append("00000009");      //Category
		 }else if("SERI".equals(type)){
			 sb.append("00000010");      //Series
		 }else if("PACK".equals(type)){
			 sb.append("00000011");      //Package
		 }else if("Map".equals(type)){
			 sb.append("00000030");      //Mappings
		 }else if("CmdIn".equals(type)){
			 sb.append("00000040");      //CmdIn
		 }else if("CmdInR".equals(type)){
			 sb.append("00000041");      //CmdInResult
		 }else if("CmdInH".equals(type)){
			 sb.append("00000042");      //CmdInHist
		 }else if("CmdInM".equals(type)){
			 sb.append("00000043");      //CmdInMapping
		 }else if("CmdInD".equals(type)){
			 sb.append("00000044");      //CmdInDownload
		 }else if("CmdInP".equals(type)){
			 sb.append("00000045");      //CmdInReplay
		 }else if("CmdOut".equals(type)){
			 sb.append("00000050");      //CmdOut
		 }else if("CmdOuR".equals(type)){
			 sb.append("00000051");      //CmdOuResult
		 }else if("CmdOuH".equals(type)){
			 sb.append("00000052");      //CmdOuHist
		 }else if("CmdOuM".equals(type)){
			 sb.append("00000053");      //CmdOuMapping
		 }else if("CmdOuD".equals(type)){
			 sb.append("00000054");      //CmdOuDownload
		 }else if("XmlTable".equals(type)){
			 sb.append("00000060");      //XmlTable
		 }else if("XmlColoumn".equals(type)){
			 sb.append("00000061");      //XmlColoumn
		 }else if("CmdOuRV".equals(type)){
			 sb.append("00000070");      //CmdOuResultVirtual
		 }
		 //日期格式段
		 Date dt = new Date();
		 SimpleDateFormat time=new SimpleDateFormat("yyyyMMddHHmmssSSS"); 
		 sb.append(time.format(dt)); 
		 int i= getObjectOrder();
		 if (i < 10) {
			 sb.append("00");
		 } else if (i < 100) {
			 sb.append('0');
		 }
		 sb.append(i);
		 return sb.toString();
	 }
	 
	 //获取图片对象ID
	 public static String getPicObjectID(String type,String contentId,String contentType){
		 StringBuilder sb = new StringBuilder();
		 sb.append("00000080"); //CP ID	
		 sb.append("00000008"); //图片id
	    if("Thumbimg".equals(type)){
		  sb.append("00000000");
		}else if("Poster".equals(type)){
			sb.append("00000001");
		}else if("Still".equals(type)){
			sb.append("00000002");
		}else if(("Recommend").equals(type)){
			sb.append("00000099");
		}else if("focus".equals(type)){
			sb.append("00000021");
		}else if(("blur").equals(type)){
			sb.append("00000022");
		}
	    contentId = "00000000"+contentId;
	    if("Series".equals(contentType)){
	    	contentId += "10";
	    }else if("Program".equals(contentType)){
	    	contentId += "01";
	    }else if("Category".equals(contentType)){
	    	contentId += "20";
	    }
	    
	    contentId = contentId.substring(contentId.length()-8, contentId.length());
	    sb.append(contentId);
		return sb.toString();
	 }
	 

	private static int getObjectOrder() {
		LOCKER.lock();
		try {
			int ret = objectIdOrder++;
			if (objectIdOrder >= 1000) {
				objectIdOrder = 0;
			}

			return ret;
		} finally {
			LOCKER.unlock();
		}
	}
	private static int objectIdOrder = 0;
	private static final Lock LOCKER = new ReentrantLock();
	
	
	 //是否完整地址
	 public static boolean isCompleteAddress(String url){
		 boolean isca = false;
		 java.util.regex.Pattern pattern = java.util.regex.Pattern.compile("(http|ftp|https):\\/\\/([\\w.]+\\/?)\\S*");
		 java.util.regex.Matcher matcher = pattern.matcher(url);
		 isca = matcher.matches();
		 return isca;
	 }
	 
	 //是否http地址
	 public static boolean isHttpAddress(String url){
		 boolean isca = false;
		 java.util.regex.Pattern pattern = java.util.regex.Pattern.compile("(http|https):\\/\\/([\\w.]+\\/?)\\S*");
		 java.util.regex.Matcher matcher = pattern.matcher(url);
		 isca = matcher.matches();
		 return isca;
	 }
	 
	 //是否ftp地址
	 public static boolean isFtpAddress(String url){
		 boolean isca = false;
		 java.util.regex.Pattern pattern = java.util.regex.Pattern.compile("ftp:\\/\\/([\\w.]+\\/?)\\S*");
		 java.util.regex.Matcher matcher = pattern.matcher(url);
		 isca = matcher.matches();
		 return isca;
	 }

	 public static boolean isRegistXML(Integer action){
	 	boolean flag = false;
	 	if(action.equals(ActionEnums.REGIST.getCode())){
	 		flag = true;
		}else if(action.equals(ActionEnums.UPDATE.getCode())){
	 		flag = false;
		}else if(action.equals(ActionEnums.DELETE.getCode())){
	 		flag = false;
		}else{
	 	    flag = false;
		}
	 	return flag;
	 }
	
}
