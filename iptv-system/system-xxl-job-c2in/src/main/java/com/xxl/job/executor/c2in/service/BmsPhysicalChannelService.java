package com.xxl.job.executor.c2in.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.pukka.iptv.common.data.model.*;
import com.xxl.job.executor.c2in.model.BmsPhysicalChannel;

import java.util.List;

/**
 *
 * @author: liuli
 * @date: 2021年9月2日 下午3:35:37
 */

public interface BmsPhysicalChannelService extends IService<BmsPhysicalChannel> {
    /**
     * 根据channelCode统计
     * @param channelCode channelCode
     * @return
     */
    Long countByChannelCode(String channelCode);

    /**
     * 根据CmsPhysicalChannelCode查询数据
     * @param cmsPhysicalChannelCode cmsPhysicalChannelCode
     * @return
     */
    List<BmsPhysicalChannel> listByCmsPhysicalChannelCode(String cmsPhysicalChannelCode);

    /**
     * 根据CmsPhysicalChannelCode删除
     *
     * @param cmsPhysicalChannelCode cmsPhysicalChannelCode
     */
    void removeByCmsPhysicalChannelCode(String cmsPhysicalChannelCode);

    void removeByCmsChannelCode(String channelCode);
}


