package com.xxl.job.executor.c2in.model;

import com.baomidou.mybatisplus.annotation.*;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.NoArgsConstructor;
import lombok.EqualsAndHashCode;
import lombok.Data;

import java.util.Date;

/**
 *
 * @author: liuli
 * @date: 2021年10月12日 上午9:47:53
 */

@Data
@EqualsAndHashCode
@JsonIgnoreProperties(value = { "handler" })
@NoArgsConstructor
@TableName(value = "bms_schedule",autoResultMap=true)
public class BmsSchedule extends Model<BmsSchedule> implements java.io.Serializable{

    private static final long serialVersionUID = 1L;
	/**主键*/
	@TableField(value = "id")
    @TableId(type = IdType.AUTO)
    @ApiModelProperty(value="主键",dataType="Long",name="id")
    private Long id;
	/**全局唯一标识*/
	@TableField(value = "code", fill = FieldFill.INSERT)
    @ApiModelProperty(value="全局唯一标识",dataType="String",name="code")
    private String code;
	/**逻辑频道 Code*/
	@TableField(value = "cms_channel_code")
    @ApiModelProperty(value="逻辑频道 Code",dataType="String",name="cmsChannelCode")
    private String cmsChannelCode;
	/**逻辑频道 ID*/
	@TableField(value = "cms_channel_id")
    @ApiModelProperty(value="逻辑频道 ID",dataType="Long",name="cmsChannelId")
    private Long cmsChannelId;
	/**节目名称*/
	@TableField(value = "program_name")
    @ApiModelProperty(value="节目名称",dataType="String",name="programName")
    private String programName;
	/**节目开播日期*/
	@TableField(value = "start_date")
    @ApiModelProperty(value="节目开播日期",dataType="String",name="startDate")
    private String startDate;
	/**节目开播时间*/
	@TableField(value = "start_time")
    @ApiModelProperty(value="节目开播时间",dataType="String",name="startTime")
    private String startTime;
	/**节目时长(HH24MISS) */
	@TableField(value = "duration")
    @ApiModelProperty(value="节目时长(HH24MISS) ",dataType="String",name="duration")
    private String duration;
	/**TVOD 保存时长(小时) 缺省为空*/
	@TableField(value = "storage_duration")
    @ApiModelProperty(value="TVOD 保存时长(小时) 缺省为空",dataType="Integer",name="storageDuration")
    private Integer storageDuration;
	/**状态标志 0:失效 1:生效*/
	@TableField(value = "status")
    @ApiModelProperty(value="状态标志 0:失效 1:生效",dataType="Integer",name="status")
    private Integer status;
	/**描述信息*/
	@TableField(value = "description")
    @ApiModelProperty(value="描述信息",dataType="String",name="description")
    private String description;
	/**节目的分类标签，如“体育”， 多个标签用空格或“;”区分*/
	@TableField(value = "genre")
    @ApiModelProperty(value="节目的分类标签，如“体育”， 多个标签用空格或“;”区分",dataType="String",name="genre")
    private String genre;
	/**cpId*/
	@TableField(value = "cp_id")
    @ApiModelProperty(value="cpId",dataType="Long",name="cpId")
    private Long cpId;
	/**CP名称*/
	@TableField(value = "cp_name")
    @ApiModelProperty(value="CP名称",dataType="String",name="cpName")
    private String cpName;
	/**创建时间*/
	@TableField(value = "create_time")
    @ApiModelProperty(value="创建时间",dataType="Date",name="createTime")
    @JsonFormat(shape = JsonFormat.Shape.STRING,pattern = "yyyy-MM-dd HH:mm:ss",timezone="GMT+8")
    private Date createTime;
	/**更新时间*/
	@TableField(value = "update_time")
    @ApiModelProperty(value="更新时间",dataType="Date",name="updateTime")
    @JsonFormat(shape = JsonFormat.Shape.STRING,pattern = "yyyy-MM-dd HH:mm:ss",timezone="GMT+8")
    private Date updateTime;
	/**spId*/
	@TableField(value = "sp_id")
    @ApiModelProperty(value="spId",dataType="Long",name="spId")
    private Long spId;
	/**SP名称*/
	@TableField(value = "sp_name")
    @ApiModelProperty(value="SP名称",dataType="String",name="spName")
    private String spName;
	/**发布状态  1：待发布 2：发布中 3：发布成功 4：发布失败 5：待更新 6：更新中 7：更新失败 8：回收中 9：回收成功 10：回收失败*/
	@TableField(value = "publish_status")
    @ApiModelProperty(value="发布状态  1：待发布 2：发布中 3：发布成功 4：发布失败 5：待更新 6：更新中 7：更新失败 8：回收中 9：回收成功 10：回收失败",dataType="Integer",name="publishStatus")
    private Integer publishStatus;
	/**发布时间*/
	@TableField(value = "publish_time")
    @ApiModelProperty(value="发布时间",dataType="String",name="publishTime")
    private String publishTime;
	/**发布描述*/
	@TableField(value = "publish_description")
    @ApiModelProperty(value="发布描述",dataType="String",name="publishDescription")
    private String publishDescription;
	/**节目单 Code*/
	@TableField(value = "cms_schedule_code")
    @ApiModelProperty(value="节目单 Code",dataType="String",name="cmsScheduleCode")
    private String cmsScheduleCode;
	/**节目单 ID*/
	@TableField(value = "cms_schedule_id")
    @ApiModelProperty(value="节目单 ID",dataType="Long",name="cmsScheduleId")
    private Long cmsScheduleId;
	/**定时发布，时间存在则代表已经设定为定时发布*/
	@TableField(value = "timed_publish")
    @ApiModelProperty(value="定时发布，时间存在则代表已经设定为定时发布",dataType="String",name="timedPublish")
    private String timedPublish;
}
