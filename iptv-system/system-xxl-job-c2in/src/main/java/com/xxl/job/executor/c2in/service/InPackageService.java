package com.xxl.job.executor.c2in.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.pukka.iptv.common.data.model.in.InPackage;
import com.xxl.job.executor.c2in.common.util.StatusUtils;

/**
 *
 * @author: tan
 * @date: 2021-8-24 10:47:07
 */

public interface InPackageService extends IService<InPackage> {
    /**
     * 根据主键更新工单状态
     *
     * @param id 主键
     * @param status 状态
     */
    void updateStatusById(Long id, StatusUtils.Status status);
}


