package com.xxl.job.executor.c2in.service.impl;

import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.pukka.iptv.common.data.model.in.InProgram;
import com.xxl.job.executor.c2in.common.util.StatusUtils;
import com.xxl.job.executor.c2in.mapper.InProgramMapper;
import com.xxl.job.executor.c2in.service.InProgramService;
import org.springframework.stereotype.Service;

/**
 *
 * @author: liuli
 * @date: 2021年8月31日 下午2:43:04
 */

@Service
public class InProgramServiceImpl extends ServiceImpl<InProgramMapper, InProgram> implements InProgramService {

    @Override
    public void updateStatusById(Long id, StatusUtils.Status status) {
        InProgram inProgram = new InProgram();
        inProgram.setResult(status.getCode());
        inProgram.setErrorDescription(status.getMsg());
        LambdaUpdateWrapper<InProgram> updateWrapper = Wrappers.lambdaUpdate();
        updateWrapper.eq(InProgram::getId, id);
        this.update(inProgram, updateWrapper);
    }
}


