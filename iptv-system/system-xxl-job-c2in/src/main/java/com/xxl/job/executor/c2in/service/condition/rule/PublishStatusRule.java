package com.xxl.job.executor.c2in.service.condition.rule;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.pukka.iptv.common.base.enums.PublishStatusEnum;
import com.pukka.iptv.common.core.util.SpringUtils;
import com.xxl.job.executor.c2in.mapper.BmsBaseMapper;
import com.xxl.job.executor.c2in.service.condition.AbstractRule;
import com.xxl.job.executor.c2in.service.condition.BaseRule;
import com.xxl.job.executor.c2in.service.condition.RuleResult;
import com.xxl.job.executor.c2in.service.condition.rule.enums.PublishCheck;
import lombok.Setter;

import java.util.Arrays;
import java.util.Collection;
import java.util.List;
import java.util.Map;

import static com.xxl.job.executor.c2in.service.condition.RuleResult.fail;


/**
 * @author: wz
 * @date: 2021/9/7 18:57
 * @description:
 */
@Setter
public class PublishStatusRule<T> extends AbstractRule<T, PublishCheck> {
    //检查 媒资是否可以发布 [待发布，待更新，更新失败，发布失败,回收成功] 的状态；如果不是这个些状态 则返回异常
    public final static List<Integer> CAN_PUBLISH_LIST =
            Arrays.asList(PublishStatusEnum.WAITUPDATE.getCode(),
                    PublishStatusEnum.WAITPUBLISH.getCode(),
                    PublishStatusEnum.FAILUPDATE.getCode(),
                    PublishStatusEnum.ROLLBACK.getCode(),
                    PublishStatusEnum.FAILPUBLISH.getCode());
    //是否 已发布检查 查询发布状态  一下状态都是发布成功
    public final static List<Integer> PUBLISH_SUCCESS_LIST =
            Arrays.asList(PublishStatusEnum.PUBLISH.getCode(),
                    PublishStatusEnum.FAILUPDATE.getCode(),
                    PublishStatusEnum.WAITUPDATE.getCode(),
                    PublishStatusEnum.UPDATING.getCode(),
                    PublishStatusEnum.FAILROLLBACK.getCode());
    // 检查 是否是 发布中，更新中，回收中 的状态
    public final static List<Integer> ING_LIST =
            Arrays.asList(PublishStatusEnum.PUBLISHING.getCode(),
                    PublishStatusEnum.UPDATING.getCode(),
                    PublishStatusEnum.ROLLBACKING.getCode());
    // 检查是否 有数据不是 发布中 的状态
    public final static List<Integer> MUST_ING_LIST =
            Arrays.asList(PublishStatusEnum.PUBLISHING.getCode(),
                    PublishStatusEnum.UPDATING.getCode(),
                    PublishStatusEnum.ROLLBACKING.getCode());
    // 已发布到下游
    public final static List<Integer> PU_LIST =
            Arrays.asList(PublishStatusEnum.PUBLISH.getCode(),
                    PublishStatusEnum.FAILUPDATE.getCode(),
                    PublishStatusEnum.WAITUPDATE.getCode(),
                    PublishStatusEnum.UPDATING.getCode(),
                    PublishStatusEnum.FAILROLLBACK.getCode(),
                    PublishStatusEnum.PUBLISHING.getCode(),
                    PublishStatusEnum.UPDATING.getCode(),
                    PublishStatusEnum.ROLLBACKING.getCode());
    // 未发布到下游
    public final static List<Integer> UN_PUBLISHED_LIST =
            Arrays.asList(PublishStatusEnum.ROLLBACK.getCode(),
                    PublishStatusEnum.WAITPUBLISH.getCode(),
                    PublishStatusEnum.FAILPUBLISH.getCode());

    // 可删除
    public final static List<Integer> CAN_DELETE_LIST =
            Arrays.asList(PublishStatusEnum.WAITPUBLISH.getCode(),
                    PublishStatusEnum.ROLLBACK.getCode(),
                    PublishStatusEnum.FAILPUBLISH.getCode());

    public final static List<Integer> CAN_UPDATE_LIST =
            Arrays.asList(PublishStatusEnum.WAITUPDATE.getCode(),
                    PublishStatusEnum.FAILUPDATE.getCode());

    public final static List<Integer> PUBLISH_LIST =
            Arrays.asList(PublishStatusEnum.WAITPUBLISH.getCode(),
                    PublishStatusEnum.ROLLBACK.getCode(),
                    PublishStatusEnum.FAILPUBLISH.getCode());

    protected PublishStatusRule(Class<T> clazz) {
        super(clazz);
    }

    public static <T> PublishStatusRule<T> init(Class<T> clazz) {
        return new PublishStatusRule<>(clazz);
    }


    @Override
    public RuleResult execute() {
        //获取要查询的数据
        Collection<Long> data = getData();
        //为空不检查
        if (CollectionUtil.isEmpty(data)) return RuleResult.ok();
        //要查询的列
        String col = getCol();
        //查询的列的别名值
        String colAlias = getColAlias();
        //表名称
        String tableName = getTableName();
        //条件列
        String condCol = getCondCol();
        // true: condCol in(data) ; false: conCol not in(data)
        boolean ins = false;
        //条件枚举
        Collection<Integer> status = null;
        //策略
        PublishCheck action = defaultVal(getPolicy(), PublishCheck.DEFAULT);
        String type = getType();
        BmsBaseMapper bean = SpringUtils.getBean(BmsBaseMapper.class);
        switch (action) {
            //检查 媒资是否可以发布 [待发布，待更新，更新失败，回收成功，回收失败] 的状态；如果不是这个些状态 则返回异常
            case CAN_PUBLISH:
                status = CAN_PUBLISH_LIST;
                break;
            //是否已都发布成功 有未发布的就提示
            case PUBLISH_SUCCESS:
                status = PUBLISH_SUCCESS_LIST;
                break;
            //检查是否 都未发布 有发布成功的就提示
            case NOT_PUBLISH:
                status = PUBLISH_SUCCESS_LIST;
                ins = true;
                break;
            //检查 媒资是否是 发布中，更新中，回收中 的状态；如果是则返回异常
            case ING:
                status = ING_LIST;
                ins = true;
                break;
            //检查是否 有数据不是 发布中 的状态
            case MUST_ING:
                status = MUST_ING_LIST;
                break;
            //已发布下游检查
            case PU:
                status = PU_LIST;
                ins = true;
                break;
            //未发布下游检查
            case UN_PUBLISHED:
                status = UN_PUBLISHED_LIST;
                break;
            case CAN_DELETE:
                status = CAN_DELETE_LIST;
            default:
                break;
        }
        Map<String, Object> result = bean.getColByPublishStatus(tableName, col, colAlias, condCol, data, status, ins);
        if (ObjectUtil.isNotEmpty(result)) {

            return fail(makeMsg(col, result, status, ins, type), type);
        }
        return RuleResult.ok();
    }


    private static String makeMsg(String col, Map<String, Object> result, Collection<Integer> publishStatus, Boolean ins, String type) {
        StringBuilder builder = new StringBuilder();
        for (Integer status : publishStatus) {
            builder.append(PublishStatusEnum.getByValue(status).getMsg()).append(" ");
        }
        Object colData = result.get(col);
        Object psData = result.get("publish_status");
        psData = psData == null ? -1 : psData;//
        return (NAME.equals(col) ? colData : (col + " = " + colData + " 的数据")) + " 是 " + PublishStatusEnum.getByValue((int) psData).getMsg() + " 状态不能进行" + (type == null ? "发布" : type);
        //return fail((NAME.equals(col) ? colData : (col + " = " + colData + " 的数据")) + " 是 " + PublishStatusEnum.getByValue((int) psData).getMsg() + " 的状态,不能进行"+type, "");
    }


    @Override
    public BaseRule wrapper(QueryWrapper wrapper) {
        return this;
    }


}
