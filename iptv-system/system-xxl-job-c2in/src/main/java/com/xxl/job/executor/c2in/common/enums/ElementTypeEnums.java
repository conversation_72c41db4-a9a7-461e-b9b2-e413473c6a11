package com.xxl.job.executor.c2in.common.enums;

import java.util.Objects;

public enum ElementTypeEnums {

    Category(1, "Category"),
    Movie(2, "Movie"),
    Package(3, "Package"),
    Picture(4, "Picture"),
    Program(5, "Program"),
    Series(6, "Series"),
    Channel(7, "Channel"),
    PhysicalChannel(8, "PhysicalChannel"),
    Mappings(9, "Mappings"),
    Schedule(10, "Schedule"),
    Subset(11,"Subset"),
    NullElemet(99, "NullElemet");

    private final Integer code;
    private final String info;


    public static Integer getCodeByInfo(String info) {
        ElementTypeEnums[] actionEnums = values();
        for (ElementTypeEnums element : actionEnums) {
            if (element.getInfo().equals(info)) {
                return element.getCode();
            }
        }
        return null;
    }

    public static String getInfoByCode(Integer code) {
        ElementTypeEnums[] actionEnums = values();
        for (ElementTypeEnums element : actionEnums) {
            if (element.getCode().intValue() == code.intValue()) {
                return element.getInfo();
            }
        }
        return null;
    }

    public static ElementTypeEnums getEnum(String info) {
        for (ElementTypeEnums value : values()) {
            if (Objects.equals(value.info, info)) {
                return value;
            }
        }
        return null;
    }

    ElementTypeEnums(Integer code, String info)
    {
        this.code = code;
        this.info = info;
    }

    public Integer getCode()
    {
        return code;
    }

    public String getInfo()
    {
        return info;
    }
}
