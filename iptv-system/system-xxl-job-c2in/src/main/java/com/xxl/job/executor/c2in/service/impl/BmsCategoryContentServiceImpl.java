package com.xxl.job.executor.c2in.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.pukka.iptv.common.data.model.bms.BmsCategoryChannel;
import com.xxl.job.executor.c2in.common.constant.TextConstants;
import com.xxl.job.executor.c2in.mapper.BmsCategoryContentMapper;
import com.xxl.job.executor.c2in.model.BmsCategoryContent;
import com.xxl.job.executor.c2in.service.BmsCategoryChannelService;
import com.xxl.job.executor.c2in.service.BmsCategoryContentService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * @author: liuli
 * @date: 2021年9月2日 下午3:34:52
 */

@Service
public class BmsCategoryContentServiceImpl extends ServiceImpl<BmsCategoryContentMapper, BmsCategoryContent> implements BmsCategoryContentService {

    @Autowired
    BmsCategoryChannelService categoryChannelService;

    @Override
    public Long countByCategoryCode(String categoryCode) {
        LambdaQueryWrapper<BmsCategoryContent> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.eq(BmsCategoryContent::getCategoryCode, categoryCode);
        return this.count(queryWrapper);
    }

    @Override
    public Long countByCmsContentCode(String code) {
        LambdaQueryWrapper<BmsCategoryContent> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.eq(BmsCategoryContent::getCmsContentCode, code);
        return this.count(queryWrapper);
    }

    @Override
    public BmsCategoryContent getByCategoryCodeContentCodeSpId(String categoryCode, String contentCode, Long spId) {
        LambdaQueryWrapper<BmsCategoryContent> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.eq(BmsCategoryContent::getCmsContentCode, contentCode)
                .eq(BmsCategoryContent::getCategoryCode, categoryCode)
                .eq(BmsCategoryContent::getSpId, spId)
                .last(TextConstants.LIMIT_ONE);
        return getOne(queryWrapper);
    }

    @Override
    public void removeByCategoryCodeContentCode(String categoryCode, String contentCode) {
        LambdaQueryWrapper<BmsCategoryContent> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.eq(BmsCategoryContent::getCmsContentCode, contentCode).
                eq(BmsCategoryContent::getCategoryCode, categoryCode);
        this.remove(queryWrapper);
    }

    @Override
    public List<BmsCategoryContent> listByCategoryCode(String code) {
        LambdaQueryWrapper<BmsCategoryContent> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.eq(BmsCategoryContent::getCategoryCode, code);
        return this.list(queryWrapper);
    }

    @Override
    public void removeByCategoryCode(String code) {
        LambdaQueryWrapper<BmsCategoryContent> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.eq(BmsCategoryContent::getCategoryCode, code);
        this.remove(queryWrapper);
    }

    @Override
    public void removeByContentCode(String contentCode) {
        LambdaQueryWrapper<BmsCategoryContent> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.eq(BmsCategoryContent::getCmsContentCode, contentCode);
        this.remove(queryWrapper);
    }

    @Override
    public List<BmsCategoryContent> listByCategoryCodeContentCode(String categoryCode, String contentCode) {
        LambdaQueryWrapper<BmsCategoryContent> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.eq(BmsCategoryContent::getCmsContentCode, contentCode)
                .eq(BmsCategoryContent::getCategoryCode, categoryCode);
        return list(queryWrapper);
    }

    @Override
    public Integer getSequence(Long id, Integer sequence) {
        if (sequence != null) {
            return sequence;
        }
        return categoryChannelService.getSequence(id, BmsCategoryContent.class);
    }
}


