package com.xxl.job.executor.c2in.service.impl;


import com.pukka.iptv.common.base.constant.RedisKeyConstants;
import com.pukka.iptv.common.data.model.sys.SysDictionaryItem;
import com.pukka.iptv.common.redis.service.RedisService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Slf4j
@Component
public class CacheServiceImpl {

    @Autowired
    private RedisService redisService;
    public boolean cacheDictionaryItem(List<SysDictionaryItem> sysDictionaryItemList) {
        try {
            Map<String, SysDictionaryItem> dictionaryItemMap = sysDictionaryItemList.stream()
                    .collect(Collectors.toMap(sysDictionaryItem ->
                                    StringUtils.joinWith(":", sysDictionaryItem.getDictionaryBaseId(), sysDictionaryItem.getName()),
                            sysDictionaryBase -> sysDictionaryBase, (val1, val2) -> val2));
            redisService.setCacheMap(RedisKeyConstants.SYS_DICTIONARY_ITEM_KEY, dictionaryItemMap);
            return true;
        } catch (Exception exception) {
            log.error("redis插入SysDictionaryItem失败，", exception);
            return false;
        }
    }
}
