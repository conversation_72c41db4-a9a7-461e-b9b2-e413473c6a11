package com.xxl.job.executor.c2in.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.pukka.iptv.common.data.model.cms.CmsDownload;

import java.util.List;

/**
 *
 * @author: liuli
 * @date: 2021年10月22日 下午3:56:03
 */

public interface CmsDownloadService extends IService<CmsDownload> {

    List<CmsDownload> getDataLimit(int limit);

    /**
     * true 存在
     * @param code
     * @return
     */
    boolean containsCode(String code);

    CmsDownload getByCode(String code);
}


