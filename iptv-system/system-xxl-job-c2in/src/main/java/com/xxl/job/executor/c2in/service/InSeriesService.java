package com.xxl.job.executor.c2in.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.pukka.iptv.common.data.model.in.InSeries;
import com.xxl.job.executor.c2in.common.util.StatusUtils;

/**
 *
 * @author: tan
 * @date: 2021-8-24 10:50:13
 */

public interface InSeriesService extends IService<InSeries> {

    /**
     * 更新工单状态
     *
     * @param id 主键
     * @param status 处理结果
     */
    void updateStatusById(Long id, StatusUtils.Status status);
}


