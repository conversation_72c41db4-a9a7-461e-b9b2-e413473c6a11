package com.xxl.job.executor.c2in.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.pukka.iptv.common.data.model.in.InMapping;
import com.pukka.iptv.common.data.model.in.InOrder;
import com.xxl.job.executor.c2in.common.constant.TextConstants;
import com.xxl.job.executor.c2in.common.util.StatusUtils;
import com.xxl.job.executor.c2in.mapper.InMappingMapper;
import com.xxl.job.executor.c2in.service.InMappingService;
import org.springframework.stereotype.Service;


/**
 *
 * @author: tan
 * @date: 2021-8-24 10:45:59
 */

@Service
public class InMappingServiceImpl extends ServiceImpl<InMappingMapper, InMapping> implements InMappingService {

    @Override
    public void updateStatus(Long id, StatusUtils.Status status) {
        InMapping inMapping = new InMapping();
        inMapping.setStatus(status.getCode());
        LambdaUpdateWrapper<InMapping> updateWrapper = Wrappers.lambdaUpdate();
        updateWrapper.eq(InMapping::getId, id);
        this.update(inMapping, updateWrapper);

    }

    @Override
    public InMapping getInMapping(Long id) {
        LambdaQueryWrapper<InMapping> wrapper = Wrappers.lambdaQuery();
        wrapper.eq(InMapping::getId, id);
        wrapper.last(TextConstants.LIMIT_ONE);
        return getOne(wrapper);
    }
}


