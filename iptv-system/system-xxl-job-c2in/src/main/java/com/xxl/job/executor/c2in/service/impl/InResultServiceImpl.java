package com.xxl.job.executor.c2in.service.impl;


import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.pukka.iptv.common.data.model.in.InResult;
import com.xxl.job.executor.c2in.common.constant.PageConstants;
import com.xxl.job.executor.c2in.common.constant.TextConstants;
import com.xxl.job.executor.c2in.common.enums.InOrderStatusEnums;
import com.xxl.job.executor.c2in.mapper.InResultMapper;
import com.xxl.job.executor.c2in.service.InResultService;
import io.swagger.models.auth.In;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;


/**
 *
 * @author: tan
 * @date: 2021-8-24 10:50:00
 */

@Service
public class InResultServiceImpl extends ServiceImpl<InResultMapper, InResult> implements InResultService {

    @Autowired
    private InResultMapper inResultMapper;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public List<InResult> getFeedBackList() {

        LambdaQueryWrapper<InResult> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(InResult::getOrderStatus, InOrderStatusEnums.FEEDBACK.getCode());

        IPage<InResult> iPage = new Page(PageConstants.PAGE_NUMBER, PageConstants.PAGE_SIZE);
        iPage = inResultMapper.selectPage(iPage, wrapper);
        List<InResult> inResultList = iPage.getRecords();

        //更新反馈状态
        for(InResult inResult : inResultList){
             inResult.setOrderStatus(InOrderStatusEnums.FEEDBACK_DOING.getCode());
             inResultMapper.updateById(inResult);
        }
        return inResultList;
    }

    @Override
    public boolean updateState(InResult inResult) {
        LambdaQueryWrapper<InResult> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(InResult::getCorrelateId, inResult.getCorrelateId());
        wrapper.eq(InResult::getOrderStatus, InOrderStatusEnums.FEEDBACK_AUTO_DOING.getCode()).last(" limit 1 ");
//        wrapper.eq(InResult::getCspId, inResult.getCspId()).last(" limit 1 ");
        InResult inResultQuery = inResultMapper.selectOne(wrapper);

        int iResult = 0;
        if(inResultQuery != null){
            inResultQuery.setOrderStatus(InOrderStatusEnums.FEEDBACK.getCode());
            inResultQuery.setResult(inResult.getResult());
            iResult = inResultMapper.updateById(inResultQuery);
        }
        if(iResult > 0){
            return true;
        }
        return false;
    }

    @Override
    public InResult getByInOrderId(String inOrderId) {
        LambdaQueryWrapper<InResult> query = Wrappers.lambdaQuery();
        query.eq(InResult::getInOrderId, inOrderId)
                .orderBy(true,true,InResult::getRequestTime)
                .last(TextConstants.LIMIT_ONE);
        return getOne(query);
    }
}


