package com.xxl.job.executor.service.jobhandler;

import com.pukka.iptv.common.api.feign.cms.CmsProgramFeignClient;
import com.pukka.iptv.common.api.feign.cms.CmsResourceFeignClient;
import com.pukka.iptv.common.api.feign.cms.CmsSeriesFeignClient;
import com.pukka.iptv.common.base.enums.CommonResponseEnum;
import com.pukka.iptv.common.base.vo.CommonResponse;
import com.pukka.iptv.common.core.util.StringUtils;
import com.pukka.iptv.common.data.config.CommonConfig;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

/**
 * @Author: liaowj
 * @Description: 视频节目关联任务
 * @CreateDate: 2021/10/27 11:21
 * @Version: 1.0
*/
@Component
@Slf4j
public class ResourceRelateXxlJob {

    @Autowired
    private CmsProgramFeignClient cmsProgramFeignClient;
    @Autowired
    private CmsSeriesFeignClient cmsSeriesFeignClient;

    @Autowired
    private CmsResourceFeignClient cmsResourceFeignClient;

    @Autowired
    private CommonConfig commonConfig;

    /**
     * Resource关联Program
     */
    @XxlJob("RelateProgramResourceJob")
    public void relateProgram() {
        /** 关联Program 的resource */
        CommonResponse<Boolean> releaseResponse = cmsProgramFeignClient.relateRelease(Integer.valueOf(commonConfig.getRelateCount()));
        if (!releaseResponse.getCode().equals(CommonResponseEnum.SUCCESS.getCode())) {
            log.error("Program 关联 正片Resource失败", releaseResponse.getMessage());
        }

        CommonResponse<Boolean> previewResponse = cmsProgramFeignClient.relatePreview(Integer.valueOf(commonConfig.getRelateCount()));

        if (!previewResponse.getCode().equals(CommonResponseEnum.SUCCESS.getCode())) {
            log.error(previewResponse.getMessage());
            log.error("Program 关联 预览片Resource失败", previewResponse.getMessage());
        }
        log.debug("Program绑定Resource结束");
    }

    /**
     * Resource关联Series（只有预览片，type=2）
     */
    @XxlJob("RelateSeriesResourceJob")
    public void relateSeries() {
        CommonResponse<Boolean> response = cmsSeriesFeignClient.relatePreview(Integer.valueOf(commonConfig.getRelateCount()));
        if (!response.getCode().equals(CommonResponseEnum.SUCCESS.getCode())) {
            log.error("Series 关联 预览片Resource失败", response.getMessage());
        }
        log.debug("Program绑定Resource结束");
    }

    /**
     * 删除介质任务
     */
    @XxlJob("ReleteResourceTsJob")
    public void deleteTsJob(){
        CommonResponse<Boolean> response = cmsResourceFeignClient.deleteByIdsOnlyTSBySelect();
    }


}
