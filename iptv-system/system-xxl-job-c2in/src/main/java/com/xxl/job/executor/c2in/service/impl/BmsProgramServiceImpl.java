package com.xxl.job.executor.c2in.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.xxl.job.executor.c2in.common.constant.TextConstants;
import com.xxl.job.executor.c2in.mapper.BmsProgramMapper;
import com.xxl.job.executor.c2in.model.BmsProgram;
import com.xxl.job.executor.c2in.service.BmsProgramService;
import org.apache.ibatis.jdbc.SQL;
import org.springframework.stereotype.Service;

import java.util.List;


/**
 * @author: liuli
 * @date: 2021年9月2日 下午3:35:55
 */

@Service
public class BmsProgramServiceImpl extends ServiceImpl<BmsProgramMapper, BmsProgram> implements BmsProgramService {

    @Override
    public BmsProgram getByCodeAndSpId(String cmsContentCode, Long spId) {
        LambdaQueryWrapper<BmsProgram> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.eq(BmsProgram::getCmsContentCode, cmsContentCode).eq(BmsProgram::getSpId, spId).last(TextConstants.LIMIT_ONE);
        return getOne(queryWrapper);
    }

    @Override
    public Long countByCmsSeriesCode(String code) {
        LambdaQueryWrapper<BmsProgram> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.eq(BmsProgram::getCmsSeriesCode, code);
        return count(queryWrapper);
    }

    @Override
    public List<BmsProgram> listByCmsContentCode(String code) {
        LambdaQueryWrapper<BmsProgram> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.eq(BmsProgram::getCmsContentCode, code);
        return list(queryWrapper);
    }

    @Override
    public void removeByCmsContentCode(String code) {
        LambdaQueryWrapper<BmsProgram> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.eq(BmsProgram::getCmsContentCode, code);
        remove(queryWrapper);
    }

    @Override
    public List<BmsProgram> listByProgramCodeSeriesCode(String seriesCode, String programCode) {
        LambdaQueryWrapper<BmsProgram> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.eq(BmsProgram::getCmsContentCode, programCode).eq(BmsProgram::getCmsSeriesCode, seriesCode);
        return list(queryWrapper);
    }

    @Override
    public void deleteMappingSeriesByCode(String seriesCode, String programCode) {
        LambdaQueryWrapper<BmsProgram> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.eq(BmsProgram::getCmsSeriesCode, seriesCode).eq(BmsProgram::getCmsContentCode, programCode);
        remove(queryWrapper);
    }

    @Override
    public void removeByCmsSeriesId(Long id) {
        LambdaQueryWrapper<BmsProgram> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.eq(BmsProgram::getCmsSeriesId, id);
        remove(queryWrapper);
    }

}


