package com.xxl.job.executor.c2in.service.impl;


import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.pukka.iptv.common.data.model.in.InSeries;
import com.xxl.job.executor.c2in.common.util.StatusUtils;
import com.xxl.job.executor.c2in.mapper.InSeriesMapper;
import com.xxl.job.executor.c2in.service.InSeriesService;
import org.springframework.stereotype.Service;

/**
 *
 * @author: tan
 * @date: 2021-8-24 10:50:13
 */

@Service
public class InSeriesServiceImpl extends ServiceImpl<InSeriesMapper, InSeries> implements InSeriesService {

    @Override
    public void updateStatusById(Long id, StatusUtils.Status status) {
        InSeries inSeries = new InSeries();
        inSeries.setResult(status.getCode());
        inSeries.setErrorDescription(status.getMsg());
        LambdaUpdateWrapper<InSeries> updateWrapper = Wrappers.lambdaUpdate();
        updateWrapper.eq(InSeries::getId, id);
        this.update(inSeries, updateWrapper);
    }
}


