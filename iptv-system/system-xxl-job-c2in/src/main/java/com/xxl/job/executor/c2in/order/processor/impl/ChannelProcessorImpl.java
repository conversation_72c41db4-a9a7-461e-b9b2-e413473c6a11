package com.xxl.job.executor.c2in.order.processor.impl;

import com.alibaba.fastjson.JSON;
import com.pukka.iptv.common.api.feign.cms.CmsChannelFeignClient;
import com.pukka.iptv.common.base.enums.SourceEnum;
import com.pukka.iptv.common.base.vo.CommonResponse;
import com.pukka.iptv.common.data.dto.CmsChannelDto;
import com.pukka.iptv.common.data.model.cms.CmsChannel;
import com.pukka.iptv.common.data.model.in.InChannel;
import com.pukka.iptv.common.data.model.in.InOrder;
import com.pukka.iptv.common.data.model.in.InSchedule;
import com.xxl.job.executor.c2in.common.constant.TableNameConstants;
import com.xxl.job.executor.c2in.common.constant.TextConstants;
import com.xxl.job.executor.c2in.common.enums.*;
import com.xxl.job.executor.c2in.common.util.StatusUtils;
import com.xxl.job.executor.c2in.model.BmsChannel;
import com.xxl.job.executor.c2in.service.*;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

/**
 * <AUTHOR>
 * @Date 2021/8/31 16:43
 * @Description
 */
@Slf4j
@Component(TableNameConstants.IN_CHANNEL_TABLE)
public class  ChannelProcessorImpl extends AbsOrderProcessor {

    @Autowired
    CmsChannelService cmsChannelService;

    @Autowired
    CmsScheduleService cmsScheduleService;

    @Autowired
    CmsPhysicalChannelService cmsPhysicalChannelService;

    @Autowired
    InChannelService inChannelService;

    @Autowired
    InOrderMappingsService inOrderMappingsService;

    @Autowired
    BmsChannelService bmsChannelService;

    @Autowired
    BmsPhysicalChannelService bmsPhysicalChannelService;

    @Autowired
    BmsScheduleService bmsScheduleService;

    @Autowired
    BmsCategoryChannelService bmsCategoryChannelService;

    @Autowired
    BmsPictureService bmsPictureService;

    @Autowired
    CmsPictureService cmsPictureService;

    @Autowired
    CmsChannelFeignClient cmsChannelFeignClient;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void deleteProcess(Object data, InOrder order) {
        InChannel inChannel = (InChannel)data;
        CmsChannel cmsChannel = cmsChannelService.getByCode(inChannel.getCode());
        if (cmsChannel == null) {
            log.warn("对象删除结束,对象不存在 ChannelCode:{}", inChannel.getCode());
            updateStatus(order, inChannel, StatusUtils.success());
            return;
        }
        if (!sysInPassage.getCpId().equals(cmsChannel.getCpId())) {
            log.error("对象删除失败,内容不属于当前cp cmsChannelCode:{}", cmsChannel.getCode());
            updateStatus(order, inChannel, StatusUtils.fail("对象删除失败,内容不属于当前cp"));
            return;
        }
        // 检查bms表中的类容
        List<BmsChannel> bmsChannelList = bmsChannelService.listByCmsChannelCodeCpId(inChannel.getCode(), sysInPassage.getCpId());
        for (BmsChannel bmsChannel : bmsChannelList) {
            // 不是待发布 并且 不是回收成功 则不能删除
            if (!PublishStatusEnums.checkDeleteStatus(bmsChannel.getPublishStatus())){
                log.error("删除失败,发布状态不是待发布和回收成功发布失败, code :{} spId:{}", bmsChannel.getCode(), bmsChannel.getSpId());
                updateStatus(order, inChannel, StatusUtils.fail("删除失败,发布状态不是待发布和回收成功发布失败"));
                return;
            }
        }
        // 删物理频道
        cmsScheduleService.removeByChannelCode(inChannel.getCode());
        bmsScheduleService.removeByCmsChannelCode(inChannel.getCode());
        // 删节目单
        cmsPhysicalChannelService.removeByChannelCode(inChannel.getCode());
        bmsPhysicalChannelService.removeByCmsChannelCode(inChannel.getCode());
        // 删自身
        cmsChannelService.removeByCode(inChannel.getCode());
        bmsChannelService.removeByCmsChannelCode(inChannel.getCode());
        // 删关系
        bmsCategoryChannelService.removeByChannelCode(inChannel.getCode());
        // 删图片
        cmsPictureService.removeByContentCode(inChannel.getCode());

        //删除bms图片
        bmsPictureService.removeByCmsPictureCode(inChannel.getCode());
        // 删除成功  处理工单状态
        updateStatus(order, inChannel, StatusUtils.success());
    }


    @Override
    public void updateProcess(Object data, InOrder order) {
        InChannel inChannel = (InChannel)data;
        //20230220 vspCode直接从cp中取code值
        if (ObjectUtils.isEmpty(sysCp)) {
            log.warn("当前注入工单处理,工单ID:{},sysCp信息为空，重新获取,内容:{}", order.getId(), JSON.toJSONString(inChannel));
            sysCp = getSysCpByOrder(order);
            log.info("当前注入工单处理,工单ID:{},sysCp信息:{}", order.getId(), JSON.toJSONString(sysCp));
        }
        inChannel.setVspCode(sysCp.getCode());
        CmsChannel cmsChannel = cmsChannelService.getByCode(inChannel.getCode());
        if (cmsChannel == null) {
            log.warn("对象更新失败,对象不存在 ChannelCode:{}", inChannel.getCode());
            registProcess(data, order);
            return;
        }
        if (!sysInPassage.getCpId().equals(cmsChannel.getCpId())) {
            log.error("对象更新失败,内容不属于当前cp cmsChannelCode:{}", cmsChannel.getCode());
            updateStatus(order, inChannel, StatusUtils.fail("对象更新失败,内容不属于当前cp"));
            return;
        }
        CmsChannelDto cmsChannelDto = new CmsChannelDto(inChannel);
        cmsChannelDto.setId(cmsChannel.getId());
        cmsChannelDto.setCpId(cmsChannel.getCpId());
        cmsChannelDto.setRequestResource(SourceEnum.SYSWORK.getValue());
        CommonResponse commonResponse = cmsChannelFeignClient.updateById(cmsChannelDto);
        if (TextConstants.OK.equals(commonResponse.getCode())) {
            // 更新状态 结束
            updateStatus(order, inChannel, StatusUtils.success());
        } else {
            // 写失败状态
            updateStatus(order, inChannel, StatusUtils.fail(commonResponse.getMessage()));
            log.error("逻辑频道update失败,code:{}, Response:{}", inChannel.getCode(), commonResponse.toString());
        }
    }

    @Override
    public void registProcess(Object data, InOrder order) {
        InChannel inChannel = (InChannel) data;
        //20230220 vspCode直接从cp中取code值
        if (ObjectUtils.isEmpty(sysCp)) {
            log.warn("当前注入工单处理,工单ID:{},sysCp信息为空，重新获取,内容:{}", order.getId(), JSON.toJSONString(inChannel));
            sysCp = getSysCpByOrder(order);
            log.info("当前注入工单处理,工单ID:{},sysCp信息:{}", order.getId(), JSON.toJSONString(sysCp));
        }
        inChannel.setVspCode(sysCp.getCode());
        initCpByVspCode(inChannel.getVspCode());
        CmsChannel cmsChannel =  cmsChannelService.getByCode(inChannel.getCode());
        if (cmsChannel == null) {
            // 新增
            CmsChannelDto cmsChannelDto = new CmsChannelDto(inChannel);
            cmsChannelDto.setRequestResource(SourceEnum.SYSWORK.getValue());
            cmsChannelDto.setCpName(sysCp.getName());
            cmsChannelDto.setCpId(sysCp.getId());
            cmsChannelDto.setCspId(sysInPassage.getCspId());
            // 插入数据到cms_channel表
            CommonResponse commonResponse = cmsChannelFeignClient.save(cmsChannelDto);
            if (TextConstants.OK.equals(commonResponse.getCode())) {
                // 新增完成 处理状态
                updateStatus(order, inChannel, StatusUtils.success());
            } else {
                // 失败状态
                updateStatus(order, inChannel, StatusUtils.fail(commonResponse.getMessage()));
                log.error("逻辑频道regis失败, code:{}, Response:{}", inChannel.getCode(), commonResponse.toString());
            }
        } else {
            // 新增已经存在的数据 为update
            log.warn("cmsChannel表已存在 code为{}的对象", inChannel.getCode());
            updateProcess(data, order);
        }
    }

    /**
     * 更新工单表状态 多表更新 事务操作
     *
     * @param inChannel 子工单对象
     * @param status 处理状态
     */
    public void updateStatus(InOrder order, InChannel inChannel, StatusUtils.Status status) {
        // 子工单对象直接通过id更新状态
        inChannelService.updateStatusById(inChannel.getId(), status);
        inOrderMappingsService.updateStatus(order.getCorrelateId(), TableNameConstants.IN_CHANNEL_TABLE, inChannel.getId(), status);
    }

    @Override
    public void updateStatus(InOrder order, Object data, StatusUtils.Status status) {
        InChannel inChannel = (InChannel) data;
        // 子工单对象直接通过id更新状态
        updateStatus(order, inChannel, status);
    }

    @Override
    public boolean deleteProcessIsExist(Object data, InOrder order) {
        InChannel inChannel = (InChannel)data;
        CmsChannel cmsChannel = cmsChannelService.getByCodeCpid(inChannel.getCode(), getSysCp().getId());
        if (cmsChannel == null) {
            // 若频道不存在 则结束
            log.error("对象更新失败,cmsSchedule对应的cp：{}, cmsChannel不存在", getSysCp().getId()
                    );
            return false;
        }
        return true;
    }

}
