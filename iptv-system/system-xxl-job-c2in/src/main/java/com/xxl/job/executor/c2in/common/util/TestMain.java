package com.xxl.job.executor.c2in.common.util;




public class TestMain {

    public  static void main(String[] args ){

        String Str = new String("******************************************/Movie/khj0611/20191202.jpg");

        System.out.print("返回值 :" );
        System.out.println(Str.replace( "172.25.224.111:21", "172.25.224.222:21"));


//        System.out.print("返回值 :" );
//        System.out.println(Str.replace('u', 'D'));
//        // 创建一个 HashMap
//        HashMap<Integer, Integer> prices = new HashMap<>();
//
//        // 往HashMap中添加映射项
//        prices.put(1, 200);
//        prices.put(2, 300);
//        prices.put(2, 150);
//        prices.put(16, 230);
//        System.out.println("HashMap: " + prices);

//        // 计算 Shirt 的值
//        int shirtPrice = prices.computeIfAbsent("Shoes", key -> 280);
//        System.out.println("Price of Shirt: " + shirtPrice);
//
//        // 输出更新后的HashMap
//        System.out.println("Updated HashMap: " + prices);


//        System.out.println(ActionEnums.REGIST.getInfo());
//        System.out.println(ActionEnums.REGIST.getCode());
//        System.out.println(ActionEnums.getCodeByInfo("DELETE"));
//        Long.parseLong("1256858");
//        System.out.println(  Long.parseLong("1256858"));
//        System.out.println(new Date());
//        int temp =99;
//        try{
//             temp = testFinally();
//        }catch (Exception e){
////            e.printStackTrace();
//            System.out.println(temp);
//        }

//        ArrayList<Integer> myNumbers = new ArrayList<Integer>();
//        myNumbers.add(33);
//        myNumbers.add(15);
//        myNumbers.add(20);
//        myNumbers.add(34);
//        myNumbers.add(8);
//        myNumbers.add(12);
//        myNumbers.add(12);
//
//
//        Collections.sort(myNumbers);  // 数字排序
//
//        for (int i : myNumbers) {
//            System.out.println(i);
//        }

//        String info = "你是我的好哥们面向，我们一起学习面向对象";
//        String reStr = "面向对象" ;
//        info = info.replace(reStr,"java的思路");
//        System.out.println(info);
//        StringBuffer sb = new StringBuffer("buff");
//        String s = "aaa";
//        int i = 1;
////        Person p = new Person("aa",12);
//        i=2;
//        change(s,i,sb,p);
////        s="222";
//        System.out.println(s);
//        System.out.println(i);
//        System.out.println(sb.toString());
//        System.out.println(p);

    }

//    public static String change(String s, int i, StringBuffer sb, Person p){
//        s="123";
//        i=3;
//        sb.append("woshi");
//        p.setAge(100);
//        sb = new StringBuffer("sbsb");
////        p = new Person("bb",44);
//        return s;
//    }

    public static int testFinally(){
        int temp = 1;
        int a = 0;
        try{
            a = 1/0;
            System.out.println("--------------1------------");
        }catch (Exception e){
            System.out.println("--------------2------------");
//            e.printStackTrace();
            throw e;
//            return 2;
        }finally {
            System.out.println("--------------3------------");
//            return 3;
        }
        System.out.println("--------------4------------");
        return 4;
    }
}
