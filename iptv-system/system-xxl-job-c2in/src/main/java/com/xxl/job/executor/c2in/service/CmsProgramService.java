package com.xxl.job.executor.c2in.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.pukka.iptv.common.base.vo.CommonResponse;
import com.pukka.iptv.common.data.dto.CmsProgramDto;
import com.pukka.iptv.common.data.model.cms.CmsProgram;

import java.util.List;

/**
 *
 * @author: tan
 * @date: 2021年8月27日 上午10:00:33
 */

public interface CmsProgramService extends IService<CmsProgram> {

    /**
     * 根据code统计
     *
     * @param code code
     * @return
     */
    Long countByCode(String code);

    /**
     * 根据seriesCode统计
     *
     * @param seriesCode seriesCode
     * @return
     */
    Long countBySeriesCode(String seriesCode);

    /**
     * 根据code查询
     *
     * @param code code
     * @return
     */
    CmsProgram getByCode(String code);

    CmsProgram getByCodeCpId(String code, Long cpId);

    /**
     * 根据code删除
     * @param code code
     */
    void removeByCode(String code);

    /**
     * 根据 seriesCode code 查询
     *
     * @param seriesCode
     * @param code
     * @return
     */
    CmsProgram getByCodeSeriesCode(String seriesCode, String code);

    /**
     * 删除关系 清除series相关字段
     *
     * @param seriesCode
     * @param programCode
     */
    void deleteMappingSeriesByCode(String seriesCode, String programCode);

    void deleteMappingSeriesBySeriesId(Long seriesId);

    /**
     * 清除和movie的关系
     * @param flag flag 0 清除预览片  1 正片
     */
    void removeMovieMappingByCode(int flag, CmsProgram cmsProgram);

    List<CmsProgram> getBySeriesCode(String seriesCode);

    CmsProgram getByPreviewCode(String previewCode);

    CmsProgram getByReleaseCode(String releaseCode);

    CommonResponse subsetUp(CmsProgramDto cmsProgramDto);
}


