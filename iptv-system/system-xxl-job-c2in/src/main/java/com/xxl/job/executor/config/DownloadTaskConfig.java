package com.xxl.job.executor.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.context.annotation.Configuration;

/**
 * @Author: chiron
 * @Date: 2023/08/15/09:03
 * @Description: 下载任务配置
 */
@Data
@Configuration
@RefreshScope
@ConfigurationProperties(prefix = "spring.iptv.download.task.config")
public class DownloadTaskConfig {

  /**
   * 是否启用
   */
  private Boolean enable;
  /**
   * 下载条数限制
   */
  private String limit;
  /**
   * cpId集合 逗号分割
   */
  private String cpids;

  /**
   * 限制下载的总数
   */
  private String restrictednumber;

}
