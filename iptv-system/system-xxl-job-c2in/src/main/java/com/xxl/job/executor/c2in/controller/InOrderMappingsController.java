package com.xxl.job.executor.c2in.controller;

import javax.validation.Valid;

import com.pukka.iptv.common.base.vo.CommonResponse;
import com.pukka.iptv.common.data.model.in.InOrderMappings;
import com.pukka.iptv.common.data.vo.*;
import com.xxl.job.executor.c2in.service.InOrderMappingsService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.*;
import org.springframework.beans.factory.annotation.Autowired;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.AllArgsConstructor;

/**
 *
 * @author: tan
 * @date: 2021-8-24 10:46:41
 */

@RestController
@AllArgsConstructor
@RequestMapping(value = "/inOrderMappings", produces = MediaType.APPLICATION_JSON_VALUE)
@Api(tags="inOrderMappings管理")
public class InOrderMappingsController {

    @Autowired
    private InOrderMappingsService inOrderMappingsService;

    @ApiOperation(value = "分页")
    @GetMapping("/page" )
    public CommonResponse<Page> page(@Valid Page page, InOrderMappings inOrderMappings) {
        return  CommonResponse.success(inOrderMappingsService.page(page, Wrappers.query(inOrderMappings)));
    }

    @ApiOperation(value = "详情")
    @GetMapping("/getById")
    public CommonResponse<InOrderMappings> getById(@Valid @RequestParam(name = "id", required = true)  Long id) {
        return CommonResponse.success(inOrderMappingsService.getById(id));
    }

    @ApiOperation(value = "新增")
    @PostMapping("/save")
    public CommonResponse<Boolean> save(@Valid @RequestBody InOrderMappings inOrderMappings) {
        return  CommonResponse.success(inOrderMappingsService.save(inOrderMappings));
    }

    @ApiOperation(value = "修改")
    @PutMapping("/update")
    public CommonResponse<Boolean> updateById(@Valid @RequestBody InOrderMappings inOrderMappings) {
        return CommonResponse.success(inOrderMappingsService.updateById(inOrderMappings));
    }

    @ApiOperation(value = "删除")
    @DeleteMapping("/deleteById" )
    public CommonResponse<Boolean> deleteById(@Valid @RequestParam(name = "id", required = true) Long id) {
        return  CommonResponse.success(inOrderMappingsService.removeById(id));
    }

    @ApiOperation(value = "批量删除")
    @DeleteMapping("/deleteByIds" )
    public CommonResponse<Boolean> deleteByIds(@Valid @RequestBody IdList idList) {
        return  CommonResponse.success(inOrderMappingsService.removeByIds(idList.getIds()));
    }

}
