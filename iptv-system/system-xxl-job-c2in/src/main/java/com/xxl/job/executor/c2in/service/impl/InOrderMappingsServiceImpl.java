package com.xxl.job.executor.c2in.service.impl;

import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.pukka.iptv.common.data.model.OutAuto;
import com.pukka.iptv.common.data.model.in.*;
import com.xxl.job.executor.c2in.common.enums.ElementTypeAutoMappingsEnums;
import com.xxl.job.executor.c2in.common.enums.ElementTypeEnums;
import com.xxl.job.executor.c2in.common.enums.MovieTypeEnums;
import com.xxl.job.executor.c2in.common.enums.SeriesTypeEnums;
import com.xxl.job.executor.c2in.common.util.StatusUtils;
import com.xxl.job.executor.c2in.mapper.InOrderMappingsMapper;
import com.xxl.job.executor.c2in.service.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;

/**
 *
 * @author: tan
 * @date: 2021-8-24 10:46:41
 */

@Service
public class InOrderMappingsServiceImpl extends ServiceImpl<InOrderMappingsMapper, InOrderMappings> implements InOrderMappingsService {

    @Autowired
    private InMappingService inMappingService;
    @Autowired
    private InMovieService inMovieService;
    @Autowired
    private InSeriesService inSeriesService;
    @Autowired
    private InProgramService inProgramService;

    @Override
    public void updateStatus(String correlateId, String tableName, Long tableRowId, StatusUtils.Status status) {
        InOrderMappings inOrderMappings = new InOrderMappings();
        inOrderMappings.setStatus(status.getCode());
        inOrderMappings.setErrorDesc(status.getMsg());
        LambdaUpdateWrapper<InOrderMappings> updateWrapper = Wrappers.lambdaUpdate();
        // 根据主工单id 和 对象code 确定更新哪条数据
        updateWrapper.eq(InOrderMappings::getCorrelateId, correlateId).eq(InOrderMappings::getTableName, tableName).eq(InOrderMappings::getTableRowId, tableRowId);
        this.update(inOrderMappings, updateWrapper);
    }

    @Override
    public List<InOrderMappings> listByInOrderId(String inOrderId) {
        LambdaUpdateWrapper<InOrderMappings> updateWrapper = Wrappers.lambdaUpdate();
        updateWrapper.eq(InOrderMappings::getInOrderId, inOrderId);
        return list(updateWrapper);
    }

    @Override
    public OutAuto getOutAutoByInOrderId(String inOrderId) {
        OutAuto outAuto = new OutAuto();
        List<InOrderMappings> inOrderMappingsList = listByInOrderId(inOrderId);

//        Set<String> typeSet = new HashSet<>();
        for(InOrderMappings inOrderMappings : inOrderMappingsList){
//            typeSet.add(inOrderMappings.getType());
            if(inOrderMappings.getType().equals(ElementTypeAutoMappingsEnums.Series.getInfo())){
                outAuto.setContentType(ElementTypeAutoMappingsEnums.Series.getCode());
                break;
            }else {
                if(inOrderMappings.getType().equals(ElementTypeAutoMappingsEnums.Mappings.getInfo())){
                   InMapping inMapping =  inMappingService.getInMapping(inOrderMappings.getTableRowId());
                   outAuto.setContentType( ElementTypeAutoMappingsEnums.getCodeByInfo(inMapping.getParentType()));
                }else {
                    outAuto.setContentType(ElementTypeAutoMappingsEnums.getCodeByInfo(inOrderMappings.getType()));
                }
            }

        }


//        for(String s : typeSet){
//            if(s.equals(ElementTypeEnums.Series.getInfo())){
//                outAuto.setContentType(ElementTypeEnums.Series.getCode());
//                break;
//            }else {
//                if(s.equals(ElementTypeEnums.Mappings.getInfo())){
//                    inMappingService.getInMapping()
//                }else {
//                    outAuto.setContentType(ElementTypeEnums.getCodeByInfo(s));
//                }
//            }
//
//        }
        return outAuto;
    }

    /**
     *逻辑关系
     1.父栏目>子栏目>剧集>子集>视频=图片;
     2.父栏目>子栏目>单集>>视频=图片;
     3.产品包>单集>视频=图片;
     4.产品包>剧集>子集>视频=图片;
     5.逻辑频道>物理频道>图片;
     6.逻辑频道>节目单;
     * @param inOrderId
     * @return
     */
    @Override
    public OutAuto getOutAutoExtendByInOrderId(String inOrderId) {
        OutAuto outAuto = new OutAuto();
        List<InOrderMappings> inOrderMappingsList = listByInOrderId(inOrderId);

        List<String> typeList = new ArrayList<>();
        List<String> mappingTypeList = new ArrayList<>();
        Map<String,Long> rowIdList = new HashMap<>();
        inOrderMappingsList.forEach(e -> {
           if(e.getType().equals(ElementTypeAutoMappingsEnums.Mappings.getInfo())){
               InMapping inMapping = inMappingService.getInMapping(e.getTableRowId());
               mappingTypeList.add(inMapping.getParentType());
               mappingTypeList.add(inMapping.getElementType());
           }else{
              typeList.add(e.getType());
              rowIdList.put(e.getType(),e.getTableRowId());
           }
        });
        String type = null;

        if(typeList.contains(ElementTypeAutoMappingsEnums.Category.getInfo())) {
            type = ElementTypeAutoMappingsEnums.Category.getInfo();
        }else if(typeList.contains(ElementTypeAutoMappingsEnums.Package.getInfo())){
            type = ElementTypeAutoMappingsEnums.Package.getInfo();
        }else if(typeList.contains(ElementTypeAutoMappingsEnums.Channel.getInfo())){
            if (typeList.contains(ElementTypeAutoMappingsEnums.PhysicalChannel.getInfo())){
                type = ElementTypeAutoMappingsEnums.CHANNEL_PHYSICAL.getInfo();
            }else if (mappingTypeList.contains(ElementTypeAutoMappingsEnums.Category.getInfo())){
                type = ElementTypeAutoMappingsEnums.CATEGORY_CHANNEL.getInfo();
            }else if (mappingTypeList.contains(ElementTypeAutoMappingsEnums.Package.getInfo())){
                type = ElementTypeAutoMappingsEnums.PACKAGE_CHANNEL.getInfo();
            } else {
                type = ElementTypeAutoMappingsEnums.Channel.getInfo();
            }
        }else if (typeList.contains(ElementTypeAutoMappingsEnums.PhysicalChannel.getInfo())){
            type = ElementTypeAutoMappingsEnums.PhysicalChannel.getInfo();
        }else{
            type = getType(typeList,mappingTypeList,rowIdList);
        }
        outAuto.setContentType(ElementTypeAutoMappingsEnums.getCodeByInfo(type));
        return outAuto;
    }

    private  String getType(List<String> typeList, List<String> mappingTypeList, Map<String, Long> rowIdList){
        String type = null;
        if(typeList.contains(ElementTypeAutoMappingsEnums.Series.getInfo())){
            Long rawId = rowIdList.get(ElementTypeAutoMappingsEnums.Series.getInfo());
            InSeries inSeries = inSeriesService.getById(rawId);
            if (mappingTypeList.contains(ElementTypeAutoMappingsEnums.Category.getInfo())){
                type = ElementTypeAutoMappingsEnums.CATEGORY_SERIES.getInfo();
            }else if (mappingTypeList.contains(ElementTypeAutoMappingsEnums.Package.getInfo())){
                type = ElementTypeAutoMappingsEnums.PACKAGE_SERIES.getInfo();
            }else if (SeriesTypeEnums.SERIAL.getCode().equals(inSeries.getSeriesType())){
                type = ElementTypeAutoMappingsEnums.EPISODES.getInfo();
            }else {
                type = ElementTypeAutoMappingsEnums.Series.getInfo();
            }
        }else if(typeList.contains(ElementTypeAutoMappingsEnums.Program.getInfo())){
            Long rawId = rowIdList.get(ElementTypeAutoMappingsEnums.Program.getInfo());
            InProgram inProgram = inProgramService.getById(rawId);
            if (mappingTypeList.contains(ElementTypeAutoMappingsEnums.Category.getInfo())){
                type = ElementTypeAutoMappingsEnums.CATEGORY_PROGRAM.getInfo();
            }else if (mappingTypeList.contains(ElementTypeAutoMappingsEnums.Package.getInfo())){
                type = ElementTypeAutoMappingsEnums.PACKAGE_PROGRAM.getInfo();
            }else if (SeriesTypeEnums.SUB_SET.getCode().equals(inProgram.getSeriesFlag())){
                type = ElementTypeAutoMappingsEnums.SUBSET.getInfo();
            }else {
                type = ElementTypeAutoMappingsEnums.Program.getInfo();
            }
        }else if(typeList.contains(ElementTypeAutoMappingsEnums.Movie.getInfo())){
            Long rawId = rowIdList.get(ElementTypeAutoMappingsEnums.Movie.getInfo());
            InMovie inMovie = inMovieService.getById(rawId);
            if (MovieTypeEnums.PREVIEW.getCode().equals(inMovie.getType())){
                type = ElementTypeAutoMappingsEnums.PREVIEW.getInfo();
            }else {
                type = ElementTypeAutoMappingsEnums.Movie.getInfo();
            }
        }else if (typeList.contains(ElementTypeAutoMappingsEnums.Picture.getInfo())){
            if (mappingTypeList.contains(ElementTypeAutoMappingsEnums.Category.getInfo())) {
                type = ElementTypeAutoMappingsEnums.CATEGORY_PICTURE.getInfo();
            }else if (mappingTypeList.contains(ElementTypeAutoMappingsEnums.Package.getInfo())){
                type = ElementTypeAutoMappingsEnums.PACKAGE_PICTURE.getInfo();
            }else if (mappingTypeList.contains(ElementTypeAutoMappingsEnums.Channel.getInfo())){
                type = ElementTypeAutoMappingsEnums.CHANNEL_PICTURE.getInfo();
            }else if (mappingTypeList.contains(ElementTypeAutoMappingsEnums.Series.getInfo())){
                type = ElementTypeAutoMappingsEnums.SERIES_PICTURE.getInfo();
            }else if (mappingTypeList.contains(ElementTypeAutoMappingsEnums.Program.getInfo())){
                type = ElementTypeAutoMappingsEnums.PROGRAM_PICTURE.getInfo();
            }else {
                type = ElementTypeAutoMappingsEnums.PACKAGE_PICTURE.getInfo();
            }
        }else if (typeList.contains(ElementTypeAutoMappingsEnums.Schedule.getInfo())){
             type = ElementTypeAutoMappingsEnums.Schedule.getInfo();
        }else {
            type = ElementTypeAutoMappingsEnums.RELATIONSHIP.getInfo();
        }
        return type;
    }

}


