package com.xxl.job.executor.c2in.order.processor.impl;

import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.pukka.iptv.common.api.feign.cms.CmsSeriesFeignClient;
import com.pukka.iptv.common.api.feign.copyright.RuleProhibitFeignClient;
import com.pukka.iptv.common.base.enums.*;
import com.pukka.iptv.common.base.vo.CommonResponse;
import com.pukka.iptv.common.data.dto.CmsSeriesDto;
import com.pukka.iptv.common.data.model.cms.CmsProgram;
import com.pukka.iptv.common.data.model.cms.CmsSeries;
import com.pukka.iptv.common.data.model.in.InOrder;
import com.pukka.iptv.common.data.model.in.InSeries;
import com.pukka.iptv.common.data.vo.cms.CmsSeriesVO;
import com.xxl.job.executor.c2in.common.constant.TableNameConstants;
import com.xxl.job.executor.c2in.common.constant.TextConstants;
import com.xxl.job.executor.c2in.common.enums.LockStatusEnums;
import com.xxl.job.executor.c2in.common.enums.PublishStatusEnums;
import com.xxl.job.executor.c2in.common.util.StatusUtils;
import com.xxl.job.executor.c2in.model.BmsContent;
import com.xxl.job.executor.c2in.service.*;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @Date 2021/9/2 9:40
 * @Description
 */
@Slf4j
@Component(TableNameConstants.IN_SERIES_TABLE)
public class SeriesProcessorImpl extends AbsOrderProcessor {
    @Autowired
    CmsSeriesService cmsSeriesService;

    @Autowired
    InSeriesService inSeriesService;

    @Autowired
    InOrderMappingsService inOrderMappingsService;

    @Autowired
    BmsContentService bmsContentService;

    @Autowired
    CmsProgramService cmsProgramService;

    @Autowired
    BmsProgramService bmsProgramService;

    @Autowired
    CmsPictureService cmsPictureService;

    @Autowired
    BmsPictureService bmsPictureService;

    @Autowired
    BmsCategoryContentService bmsCategoryContentService;

    @Autowired
    BmsPackageContentService bmsPackageContentService;

    @Autowired
    CmsSeriesFeignClient cmsSeriesFeignClient;

    @Autowired
    CmsMovieService cmsMovieService;

    @Autowired
    CmsResourceService cmsResourceService;

    @Autowired
    RuleProhibitFeignClient ruleProhibitFeignClient;


    @Override
    public void updateProcess(Object data, InOrder order) {
        InSeries inSeries = (InSeries) data;
        //20230220 vspCode直接从cp中取code值
        if (ObjectUtils.isEmpty(sysCp)) {
            log.warn("当前注入工单处理,工单ID:{},sysCp信息为空，重新获取,内容:{}", order.getId(), JSON.toJSONString(inSeries));
            sysCp = getSysCpByOrder(order);
            log.info("当前注入工单处理,工单ID:{},sysCp信息:{}", order.getId(), JSON.toJSONString(sysCp));
        }
        inSeries.setVspCode(sysCp.getCode());
        CmsSeries cmsSeries = cmsSeriesService.getByCode(inSeries.getCode());
        if (cmsSeries == null) {
            log.warn("对象更新失败,对象不存在 cmsSeriesCode:{}", inSeries.getCode());
            registProcess(data, order);
            return;
        }
        if (!sysInPassage.getCpId().equals(cmsSeries.getCpId())) {
            log.error("对象更新失败,cmsSeries不属于当前cp, cmsSeries:{}", cmsSeries.getCode());
            updateStatus(order, inSeries, StatusUtils.fail("对象更新失败,cmsSeries不属于当前cp"));
            return;
        }
        CmsSeriesDto cmsSeriesDto = new CmsSeriesDto(inSeries);
        cmsSeriesDto.setId(cmsSeries.getId());
        cmsSeriesDto.setCpId(cmsSeries.getCpId());
        cmsSeriesDto.setRequestResource(SourceEnum.SYSWORK.getValue());
        // 判断总集数是否修改
        if (ObjectUtils.isNotEmpty(cmsSeriesDto.getVolumnCount())){
            if (!cmsSeriesDto.getVolumnCount().equals(cmsSeries.getVolumnCount())){
                if (cmsSeriesDto.getVolumnCount() < cmsSeries.getVolumnUpdate()){
                    log.error("对象更新失败,cmsSeries总集数小于实际集数, cmsSeries:{}", cmsSeries.getCode());
                    updateStatus(order, inSeries, StatusUtils.fail("对象更新失败,cmsSeries总集数小于实际集数"));
                    return;
                }
                List<CmsProgram> cmsProgramList = cmsProgramService.list(Wrappers.<CmsProgram>lambdaQuery()
                        .select(CmsProgram::getEpisodeIndex,CmsProgram::getCode)
                        .eq(CmsProgram::getSeriesId, cmsSeriesDto.getId()));
                List<CmsProgram> wrongCmsProgramList = cmsProgramList.stream()
                        .filter(cmsProgram -> ObjectUtils.isNotEmpty(cmsProgram.getEpisodeIndex()))
                        .filter(cmsProgram -> cmsProgram.getEpisodeIndex() > cmsSeriesDto.getVolumnCount())
                        .collect(Collectors.toList());
                if (ObjectUtils.isNotEmpty(wrongCmsProgramList)){
                    log.error("对象更新失败,cmsSeries总集数小于子集集数, cmsSeries:{} ，cmsProgram:{}"
                            , cmsSeries.getCode(),wrongCmsProgramList.get(0).getCode());
                    updateStatus(order, inSeries, StatusUtils.fail("对象更新失败,cmsSeries总集数小于子集集数"));
                    return;
                }
            }
        }else {
            log.error("对象更新失败,cmsSeries总集数不能为空, cmsSeries:{}", cmsSeries.getCode());
            updateStatus(order, inSeries, StatusUtils.fail("对象更新失败,cmsSeries总集数不能为空"));
            return;
        }
        //设置违禁状态为检测中
        cmsSeriesDto.setIsProhibit(IsProhibitEnum.ING.getValue());
        CommonResponse commonResponse = cmsSeriesFeignClient.updateById(cmsSeriesDto);
        if (TextConstants.OK.equals(commonResponse.getCode())) {
            //违禁检测
            checkRuleProhibit(cmsSeriesDto.getCode());
            // 更新状态 结束
            updateStatus(order, inSeries, StatusUtils.success());
            return;
        } else {
            log.error("inSeries新增失败 code : {}, Response :{}", inSeries.getCode(), commonResponse.toString());
            updateStatus(order, inSeries, StatusUtils.fail(commonResponse.getMessage()));
            return;
        }
    }

    @Override
    public void registProcess(Object data, InOrder order) {
        InSeries inSeries = (InSeries) data;
        //20230220 vspCode直接从cp中取code值
        if (ObjectUtils.isEmpty(sysCp)) {
            log.warn("当前注入工单处理,工单ID:{},sysCp信息为空，重新获取,内容:{}", order.getId(), JSON.toJSONString(inSeries));
            sysCp = getSysCpByOrder(order);
            log.info("当前注入工单处理,工单ID:{},sysCp信息:{}", order.getId(), JSON.toJSONString(sysCp));
        }
        inSeries.setVspCode(sysCp.getCode());

        initCpByVspCode(inSeries.getVspCode());
        Long cmsSeriesNum = cmsSeriesService.countByCode(inSeries.getCode());
        if (cmsSeriesNum != 0) {
            log.warn("cmsSeries表已存在 code为{}的对象", inSeries.getCode());
            updateProcess(data, order);
            return;
        }
        CmsSeriesDto cmsSeriesDto = new CmsSeriesDto(inSeries);
        cmsSeriesDto.setCpId(sysCp.getId());
        cmsSeriesDto.setCpName(sysCp.getName());
        cmsSeriesDto.setRequestResource(SourceEnum.SYSWORK.getValue());
        cmsSeriesDto.setCspId(sysInPassage.getCspId());
        //设置违禁状态为检测中
        cmsSeriesDto.setIsProhibit(IsProhibitEnum.ING.getValue());
        if (StringUtils.isEmpty(cmsSeriesDto.getOriginalName())) {
            cmsSeriesDto.setOriginalName(cmsSeriesDto.getName());
        }
        CommonResponse commonResponse = cmsSeriesFeignClient.save(cmsSeriesDto);
        if (TextConstants.OK.equals(commonResponse.getCode())) {
            //违禁检测
            checkRuleProhibit(cmsSeriesDto.getCode());
            // 更新状态 结束
            updateStatus(order, inSeries, StatusUtils.success());
            return;
        } else {
            log.error("series新增失败 code : {}, Response :{}", inSeries.getCode(), commonResponse.toString());
            updateStatus(order, inSeries, StatusUtils.fail(commonResponse.getMessage()));
            return;
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void deleteProcess(Object data, InOrder order) {
        InSeries inSeries = (InSeries) data;
        // 检查锁定状态
        CmsSeries cmsSeries = cmsSeriesService.getByCode(inSeries.getCode());
        if (cmsSeries == null) {
            log.warn("删除失败,cmsSeries对象不存在, code :{}, cpId:{}", inSeries.getCode(), sysInPassage.getCpId());
            updateStatus(order, inSeries, StatusUtils.success());
            return;
        }
        if (!sysInPassage.getCpId().equals(cmsSeries.getCpId())) {
            log.error("对象删除失败,cmsSeries不属于当前cp, cmsSeries:{}", cmsSeries.getCode());
            updateStatus(order, inSeries, StatusUtils.fail("对象删除失败,cmsSeries不属于当前cp"));
            return;
        }
        // 锁定不能删除
        if (LockStatusEnums.LOCKED.getCode().equals(cmsSeries.getLockStatus())) {
            log.error("删除失败,Series对象已锁定, code :{}", cmsSeries.getCode());
            updateStatus(order, inSeries, StatusUtils.fail("删除失败,Series对象已锁定"));
            return;
        }
        // 检查bms_content表
        List<BmsContent> bmsContentList = bmsContentService.listByCmsContentCode(inSeries.getCode());
        for (BmsContent bmsContent : bmsContentList) {
            // 不是待发布 并且 不是回收成功 则不能删除
            if (!PublishStatusEnums.checkDeleteStatus(bmsContent.getPublishStatus())) {
                log.error("删除失败,发布状态不是待发布和回收成功发布失败, code :{} spId:{}", inSeries.getCode(), bmsContent.getSpId());
                updateStatus(order, inSeries, StatusUtils.fail("删除失败,发布状态不是待发布和回收成功发布失败"));
                return;
            }
            // 锁定不能删除
            if (LockStatusEnums.LOCKED.getCode().equals(bmsContent.getLockStatus())) {
                log.error("删除失败,栏目已锁定, code :{} spId:{}", inSeries.getCode(), bmsContent.getSpId());
                updateStatus(order, inSeries, StatusUtils.fail("删除失败,栏目已锁定"));
                return;
            }
        }
        // 先解绑子集
        cmsProgramService.deleteMappingSeriesBySeriesId(cmsSeries.getId());
        // 删bms表的子集
        bmsProgramService.removeByCmsSeriesId(cmsSeries.getId());
        // 删自身
        cmsSeriesService.removeByCode(inSeries.getCode());
        bmsContentService.removeByCmsContentCode(inSeries.getCode());
        // 删关系
        bmsCategoryContentService.removeByContentCode(inSeries.getCode());
        bmsPackageContentService.removebyContentCode(inSeries.getCode());
        // 删图片
        cmsPictureService.removeByContentCode(inSeries.getCode());
        // 删预览片
        if (StringUtils.isNotBlank(cmsSeries.getResourcePreviewCode())) {
            cmsMovieService.removeByResourceCode(cmsSeries.getResourcePreviewCode());
            cmsResourceService.removeByCode(cmsSeries.getResourcePreviewCode());
        }

        updateStatus(order, inSeries, StatusUtils.success());

    }

    /**
     * 工单表状态更新
     *
     * @param order    主工单
     * @param inSeries 子工单
     * @param status   处理结果
     */
    private void updateStatus(InOrder order, InSeries inSeries, StatusUtils.Status status) {
        inSeriesService.updateStatusById(inSeries.getId(), status);
        inOrderMappingsService.updateStatus(order.getCorrelateId(), TableNameConstants.IN_SERIES_TABLE, inSeries.getId(), status);
    }

    @Override
    public void updateStatus(InOrder order, Object data, StatusUtils.Status status) {
        InSeries inSeries = (InSeries) data;
        // 子工单对象直接通过id更新状态
        updateStatus(order, inSeries, status);
    }

    @Override
    public boolean deleteProcessIsExist(Object data, InOrder order) {
        return true;
    }

    /**
     * 违禁检测
     *
     * @param code
     */
    private void checkRuleProhibit(String code) {
        CmsSeries cmsSeries = cmsSeriesService.getByCode(code);
        try {
            if (ObjectUtils.isEmpty(cmsSeries)) {
                log.warn("注入剧集违禁检测查询关联媒资信息为空，剧集code:{}", code);
                return;
            }
            CmsSeriesVO cmsSeriesVO = new CmsSeriesVO(cmsSeries, ProhibitPointEnum.INSERT);
            //添加违禁检测
            CommonResponse<CmsSeries> ruleProhibitResponseCommonResponse = ruleProhibitFeignClient.ruleProhibitSeries(cmsSeriesVO);
            CmsSeries ruleProhibitResponse = ruleProhibitResponseCommonResponse.getData();
            if (ObjectUtils.isNotEmpty(ruleProhibitResponse)) {
                cmsSeries.setProhibitStatus(cmsSeries.getProhibitStatus());
                cmsSeries.setIsProhibit(cmsSeries.getIsProhibit());
            } else {
                cmsSeries.setIsProhibit(IsProhibitEnum.ING.getValue());
                cmsSeries.setProhibitStatus(ProhibitStatusEnum.NO.getValue());
                log.warn("注入剧集违禁检测返回结果为空，剧集信息:{}", cmsSeries);
            }
        } catch (Exception exception) {
            log.error("注入剧集违禁检测错误，剧集信息:{}, 错误信息:{}", cmsSeries, exception);
        } finally {
            if (ObjectUtils.isNotEmpty(cmsSeries)) {
                CmsSeriesDto cmsSeriesDto = new CmsSeriesDto(cmsSeries);
                CommonResponse commonResponse1 = cmsSeriesFeignClient.updateById(cmsSeriesDto);
                if (TextConstants.OK.equals(commonResponse1.getCode())) {
                    log.info("注入剧集违禁检测入库成功,剧集信息:{}", cmsSeries);
                } else {
                    log.warn("注入剧集违禁检测入库失败,剧集信息:{}", cmsSeries);
                }
            }
        }
    }
}
