package com.xxl.job.executor.c2in.service.condition;


import com.pukka.iptv.common.base.exception.BizException;
import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
public class RuleResult {
    private boolean pass;
    private String msg;
    private String nameVal;
    private Exception e;

    public static RuleResult ok() {
        return new RuleResult(true, null, null);
    }

    public RuleResult(boolean pass, String msg, String nameVal, Exception e) {
        this.pass = pass;
        this.msg = msg;
        this.nameVal = nameVal;
        this.e = e;
    }

    public void check() {
        if (!pass) {
            throw new BizException(msg);
        }
    }

    public static RuleResult fail() {
        return new RuleResult(false, null, null);
    }

    public static RuleResult fail(String msg, String nameVal) {
        return new RuleResult(false, msg, nameVal, null);
    }

    public static RuleResult fail(String msg, Exception e) {
        return new RuleResult(false, msg, e);
    }

    public RuleResult(boolean pass, String msg, Exception e) {
        this.pass = pass;
        this.msg = msg;
        this.e = e;
    }
}
