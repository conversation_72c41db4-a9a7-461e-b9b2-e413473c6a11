package com.xxl.job.executor.c2in.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.pukka.iptv.common.data.model.*;
import com.xxl.job.executor.c2in.model.BmsPackage;
import com.xxl.job.executor.c2in.model.BmsPackageContent;

import java.util.List;

/**
 *
 * @author: liuli
 * @date: 2021年9月2日 下午3:35:28
 */

public interface BmsPackageContentService extends IService<BmsPackageContent> {
    /**
     * 根据packageCode查关系
     *
     * @param packageCode packageCode
     * @return
     */
    Long countByPackageCode(String packageCode);

    /**
     * 根据CmsContentCode统计
     * @param code code
     * @return
     */
    Long countByCmsContentCode(String code);

    /**
     * 根据包code 和 contentCode查询
     * @return
     * @param parentCode parentCode
     * @param elementCode elementCode
     */
    BmsPackageContent getByPackageCodeContentCodeSpId(String parentCode, String elementCode, Long spId);

    /**
     * 根据PackageCode和ContentCode删除
     * @param parentCode parentCode
     * @param elementCode elementCode
     */
    void removeByPackageCodeContentCode(String parentCode, String elementCode);

    List<BmsPackageContent> listByPackageCode(String packageCode);

    void removeByPackageCode(String packageCode);

    void removebyContentCode(String contentCode);

    List<BmsPackageContent> listByPackageCodeContentCode(String packageCode, String contentCode);
}


