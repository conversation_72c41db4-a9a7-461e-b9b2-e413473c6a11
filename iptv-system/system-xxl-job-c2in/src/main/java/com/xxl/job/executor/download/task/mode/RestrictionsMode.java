package com.xxl.job.executor.download.task.mode;

import com.pukka.iptv.common.base.enums.CpDownloadModeEnum;
import com.pukka.iptv.common.data.model.cms.CmsDownload;
import com.pukka.iptv.downloader.util.SpringUtils;
import com.xxl.job.executor.download.task.util.DownloadTaskUtil;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.util.function.BiFunction;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;

/**
 * @Author: chiron
 * @Date: 2023/06/14/17:21
 * @Description:
 */
@Data
@Slf4j
public class RestrictionsMode {

  private DownloadTaskUtil downloadTaskUtil = SpringUtils.getBean(
      DownloadTaskUtil.class);
  private Integer limit;
  private List<CmsDownload> cmsDownloadList;
  private CpDownloadModeEnum cpDownloadModeEnum;

  private static final Map<CpDownloadModeEnum, BiFunction<CpDownloadModeEnum, List<CmsDownload>, Boolean>> SET_MAP = new ConcurrentHashMap<>();
  private static final Map<CpDownloadModeEnum, BiFunction<CpDownloadModeEnum, Integer, List<CmsDownload>>> GET_MAP = new ConcurrentHashMap<>();

  // Use an instance initialization block for initialization
  {
    log.info("RestrictionsMode init");
    init();
    log.info("RestrictionsMode init success");
  }

  private void init() {
    BiFunction<CpDownloadModeEnum, List<CmsDownload>, Boolean> setFunction = downloadTaskUtil::setDownloadTask;
    SET_MAP.put(CpDownloadModeEnum.DEFAULT, setFunction);
    SET_MAP.put(CpDownloadModeEnum.RESTRICTED, setFunction);

    BiFunction<CpDownloadModeEnum, Integer, List<CmsDownload>> getFunction = downloadTaskUtil::getDownloadTask;
    GET_MAP.put(CpDownloadModeEnum.DEFAULT, getFunction);
    GET_MAP.put(CpDownloadModeEnum.RESTRICTED, getFunction);
  }

  public void setApply() {
    SET_MAP.get(cpDownloadModeEnum).apply(cpDownloadModeEnum, cmsDownloadList);
  }

  public List<CmsDownload> getApply() {
    return GET_MAP.get(cpDownloadModeEnum).apply(cpDownloadModeEnum, limit);
  }
}