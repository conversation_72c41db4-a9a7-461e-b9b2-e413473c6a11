package com.xxl.job.executor.c2in.common.parse.impl;

import com.google.common.base.Preconditions;
import com.google.common.base.Strings;
import com.google.common.primitives.Ints;
import com.pukka.iptv.common.data.model.in.InPhysicalChannel;
import com.xxl.job.executor.c2in.common.enums.ActionEnums;
import com.xxl.job.executor.c2in.common.exception.ParserException;
import com.xxl.job.executor.c2in.common.parse.IParse;
import com.xxl.job.executor.c2in.common.util.ManageHelper;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.dom4j.Attribute;
import org.dom4j.Element;

import java.util.List;

/**
 * @ClassName PhisicalChannelParseImpl
 * @Description 频道参数物理信息解析
 * <AUTHOR>
 * @Date 2021/8/27 15:06
 * @Version
 */
public class PhysicalChannelParseImpl implements IParse<Element, InPhysicalChannel> {
    @Override
    public InPhysicalChannel get(Element element) {

        Preconditions.checkArgument(element != null);
        Attribute attr = element.attribute("Action");
        if (attr == null || Strings.isNullOrEmpty(attr.getValue())) {
            throw new ParserException("PhysicalChannel 缺少'Action'属性值");
        }

        InPhysicalChannel inPhysicalChannel = new InPhysicalChannel();
        inPhysicalChannel.setAction(ActionEnums.getCodeByInfo(attr.getValue()));

        attr = element.attribute("Code");
        if (attr == null || Strings.isNullOrEmpty(attr.getValue())) {
            throw new ParserException("PhysicalChannel 缺少'Code'属性值");
        }
        inPhysicalChannel.setCode(attr.getValue());

        attr = element.attribute("ID");
        if (attr == null || Strings.isNullOrEmpty(attr.getValue())) {
            throw new ParserException("PhysicalChannel 缺少'ID'属性值");
        }
        inPhysicalChannel.setCorrelateId(attr.getValue());
        for (Element o : (List<Element>) element.elements("Property")) {
            Attribute name = o.attribute("Name");
            if (name == null || Strings.isNullOrEmpty(name.getValue())) {
                if(ManageHelper.isRegistXML(inPhysicalChannel.getAction())){
                    throw new ParserException("PhysicalChannel属性列表中缺少'Name'属性名称");
                }

            }

            String pn = name.getValue();
            String value = o.getTextTrim();
            if (pn.equals("ChannelCode")) {
                if (Strings.isNullOrEmpty(value)) {
                    if(ManageHelper.isRegistXML(inPhysicalChannel.getAction())){
                        throw new ParserException("PhysicalChannel属性列表中缺少'ChannelCode'属性值");
                    }

                }

                inPhysicalChannel.setChannelCode(value);
                continue;
            }

            if (pn.equals("ChannelID")) {
                inPhysicalChannel.setChannelId(NumberUtils.toLong(value));
                continue;
            }

            if (pn.equals("BitRateType")) {
                Integer v = Ints.tryParse(value);
                if (v != null) {
                    inPhysicalChannel.setBitRateType(v);
                }
                continue;
            }

            if (pn.equals("MultiCastIP")) {
                if (Strings.isNullOrEmpty(value)) {
                    if(ManageHelper.isRegistXML(inPhysicalChannel.getAction())){
                        throw new ParserException("PhysicalChannel属性列表中缺少'MultiCastIP'属性值");
                    }

                }
                inPhysicalChannel.setMultiCastIp(value);
                continue;
            }

            if (pn.equals("MultiCastPort")) {
                Integer v = Ints.tryParse(value);
                if (v == null) {
                    if(ManageHelper.isRegistXML(inPhysicalChannel.getAction())){
                        throw new ParserException("PhysicalChannel属性列表中缺少'MultiCastPort'属性值");
                    }
                }
                inPhysicalChannel.setMultiCastPort(String.valueOf(v));
            }

            if (pn.equals("MediaSpec")) {
                inPhysicalChannel.setMediaSpec(value);
            }
        }
        if (Strings.isNullOrEmpty(inPhysicalChannel.getChannelCode())) {
            if(ManageHelper.isRegistXML(inPhysicalChannel.getAction())){
                throw new ParserException("PhysicalChannel属性列表中缺少'ChannelCode'属性值");
            }
        }
        if (Strings.isNullOrEmpty(inPhysicalChannel.getMultiCastIp())) {
            if(ManageHelper.isRegistXML(inPhysicalChannel.getAction())){
                throw new ParserException("PhysicalChannel属性列表中缺少'MultiCastIP'属性值");
            }
        }
        if (inPhysicalChannel.getMultiCastPort() == null) {
            if(ManageHelper.isRegistXML(inPhysicalChannel.getAction())){
                throw new ParserException("PhysicalChannel属性列表中缺少'MultiCastPort'属性值");
            }
        }
        return inPhysicalChannel;
    }
}
