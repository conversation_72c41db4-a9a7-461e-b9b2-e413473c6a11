package com.xxl.job.executor.c2in.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.pukka.iptv.common.data.model.in.InSchedule;
import com.xxl.job.executor.c2in.common.util.StatusUtils;

/**
 *
 * @author: liuli
 * @date: 2021年8月31日 下午2:39:47
 */

public interface InScheduleService extends IService<InSchedule> {

    /**
     * 更新工单状态
     *
     * @param id 主键
     * @param status 处理结果
     */
    void updateStatusById(Long id, StatusUtils.Status status);
}


