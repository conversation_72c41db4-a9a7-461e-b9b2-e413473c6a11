package com.xxl.job.executor.c2in.service.impl;

import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.pukka.iptv.common.data.model.in.InMovie;
import com.xxl.job.executor.c2in.common.util.StatusUtils;
import com.xxl.job.executor.c2in.mapper.InMovieMapper;
import com.xxl.job.executor.c2in.service.InMovieService;
import org.springframework.stereotype.Service;


/**
 *
 * @author: tan
 * @date: 2021-8-24 10:21:29
 */

@Service
public class InMovieServiceImpl extends ServiceImpl<InMovieMapper, InMovie> implements InMovieService {

    @Override
    public void updateStatusById(Long id, StatusUtils.Status status) {
        InMovie inMovie = new InMovie();
        inMovie.setResult(status.getCode());
        inMovie.setErrorDescription(status.getMsg());
        LambdaUpdateWrapper<InMovie> updateWrapper = Wrappers.lambdaUpdate();
        updateWrapper.eq(InMovie::getId, id);
        this.update(inMovie, updateWrapper);
    }
}


