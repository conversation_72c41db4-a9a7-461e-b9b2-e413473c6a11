package com.xxl.job.executor.c2in.service.impl;


import cn.hutool.core.lang.UUID;
import com.alibaba.fastjson.JSON;
import com.pukka.iptv.common.core.util.IdUtils;
import com.pukka.iptv.common.data.model.statistics.StatisticsInDelete;
import com.pukka.iptv.common.rabbitmq.config.StatisticInDirectMQConfig;
import com.pukka.iptv.common.rabbitmq.vo.MessageBody;
import com.xxl.job.executor.c2in.service.StatisticsInDeleteService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.amqp.rabbit.connection.CorrelationData;
import org.springframework.amqp.rabbit.core.RabbitTemplate;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Map;

@Service
@Slf4j
public class StatisticsInDeleteServiceImpl implements StatisticsInDeleteService {
    @Autowired
    RabbitTemplate rabbitTemplate;

    @Override
    public Boolean sendMessage(StatisticsInDelete statisticsInDelete) {

        Map<String,Object> paramsOutAuto = JSON.parseObject(JSON.toJSONString(statisticsInDelete),Map.class);
        MessageBody message = new MessageBody();
        message.setId(IdUtils.simpleUUID());
        message.setMsg(paramsOutAuto);
        log.info("OutAuto:" + paramsOutAuto.toString());
        //全局唯一 不然ReturnCallback 无效
        CorrelationData correlationData = new CorrelationData(UUID.randomUUID().toString());
        try{
            this.rabbitTemplate.convertAndSend(StatisticInDirectMQConfig.STATISTIC_IN_EXCHANGE, StatisticInDirectMQConfig.STATISTIC_IN_ROUTING, message, correlationData);
        }catch (Exception e){
            log.info("auto发送消息删除锚点异常，数据：{}", statisticsInDelete.toString());
        }
        return true;
    }
}
