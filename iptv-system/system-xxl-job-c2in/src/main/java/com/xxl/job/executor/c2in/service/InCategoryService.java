package com.xxl.job.executor.c2in.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.pukka.iptv.common.data.model.in.InCategory;
import com.xxl.job.executor.c2in.common.util.StatusUtils;

/**
 *
 * @author: tan
 * @date: 2021-8-24 10:43:49
 */

public interface InCategoryService extends IService<InCategory> {

    /**
     * 根据主键更新工单状态
     *
     * @param id 主键
     * @param status 状态
     */
    void updateStatusById(Long id, StatusUtils.Status status);
}


