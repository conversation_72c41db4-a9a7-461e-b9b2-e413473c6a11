package com.xxl.job.executor.c2in.common.util;

import com.google.common.base.Preconditions;
import com.pukka.iptv.common.core.util.StringUtils;
import com.pukka.iptv.common.proxy.config.SystemInProxyProperties;
import org.apache.commons.net.ftp.FTPClient;
import org.apache.commons.net.ftp.FTPFile;
import org.apache.commons.net.ftp.FTPHTTPClient;
import org.apache.commons.net.ftp.FTPReply;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.*;
import java.util.Map;
import java.util.StringTokenizer;


/**
 * @ClassName FtpUtil
 * @Description TODO
 * <AUTHOR>
 * @date 2016-5-5
 */

public class FtpUtil {
    private static final String FTP_HEADER = "ftp://";
	private static Logger log = LoggerFactory.getLogger(FtpUtil.class);
	
	private FTPClient ftpClient;
	private String ipAddress;
	private int ipPort;
	private String userName;
	private String passWord;
	private String encoding = "utf-8";
	/**
	 * 构造函数
	 * 
	 * @param ip
	 *            String 机器IP
	 * @param port
	 *            String 机器FTP端口号
	 * @param username
	 *            String FTP用户名
	 * @param password
	 *            String FTP密码
	 * @throws Exception
	 */
	public FtpUtil(String ip, int port, String username, String password)
			throws Exception {
        init(ip, port + "", username, password);
    }

	public FtpUtil(String ip, int port, String username, String password, Boolean flag)
			throws Exception {
		init(ip, port + "", username, password);
	}
	public FtpUtil(String ip, int port, String username, String password, SystemInProxyProperties systemInProxyProperties) {
		try {
			ipAddress = new String(ip);
			ipPort = port;
			ftpClient =  new FTPHTTPClient(systemInProxyProperties.getHost(),systemInProxyProperties.getPort(),systemInProxyProperties.getUsername(),systemInProxyProperties.getPassword());
			ftpClient.setDefaultPort(ipPort);
			userName = new String(username);
			passWord = new String(password);

			//设置超时时间
			ftpClient.setConnectTimeout(10 * 1000);
			ftpClient.connect(ipAddress);
			ftpClient.setDataTimeout(60 * 1000);
			//ftpclient.setDefaultTimeout(10*1000);//对于 FTPClient 而言，setDefaultTimeout() 超时的工作跟 setSoTimeout() 是相同的，区别仅在于后者会覆盖掉前者设置的值。
			ftpClient.setSoTimeout(30 * 1000);

			ftpClient.enterLocalPassiveMode();//client被动模式
		} catch (Exception e) {
			log.error(e.getMessage());
		}

	}
	/**
	 * 构造函数
	 * 
	 * @param ip
	 *            String 机器IP，默认端口为21
	 * @param username
	 *            String FTP用户名
	 * @param password
	 *            String FTP密码
	 * @throws Exception
	 */
	public FtpUtil(String ip, String username, String password)
			throws Exception {
		init(ip,"",username,password);
	}
	
	public FtpUtil(String ftpurl)
	throws Exception {
		Map<String, String> map = URLUtil.parseFtp(ftpurl);
		init(map.get("ipAddress"),map.get("ipPort"),map.get("userName"),map.get("passWord"));
	}

	/**
	 * 代理模式初始化
	 * @param ftpurl
	 * @param systemInProxyProperties
	 * @throws Exception
	 */
	public FtpUtil(String ftpurl,  SystemInProxyProperties systemInProxyProperties)
			throws Exception {
		Map<String, String> map = URLUtil.parseFtp(ftpurl);
		init(map.get("ipAddress"),map.get("ipPort"),map.get("userName"),map.get("passWord"), systemInProxyProperties);
	}

	/**
	 * 增加代理方式初始化
	 * @param ip
	 * @param port
	 * @param username
	 * @param password
	 * @param systemInProxyProperties
	 * @throws Exception
	 */
	private void init(String ip, String port, String username, String password, SystemInProxyProperties systemInProxyProperties)
	throws Exception {
		ipAddress = ip;
        if (StringUtils.isNotEmpty(port)) {
            ipPort = Integer.parseInt(port);
        } else {
            ipPort = 21;
        }
        log.info("systemProxyProperties info:" + systemInProxyProperties.toString());
		ftpClient =  new FTPHTTPClient(systemInProxyProperties.getHost(),systemInProxyProperties.getPort(),systemInProxyProperties.getUsername(),systemInProxyProperties.getPassword());
		ftpClient.setDefaultPort(ipPort);
        userName = username;
		passWord = password;
		//设置超时时间
		ftpClient.setConnectTimeout(10 * 1000);
		if(!ftpClient.isConnected()){
			ftpClient.connect(ipAddress);
		}
//		ftpClient.connect(ipAddress);
		ftpClient.setDataTimeout(60 * 1000);
		//ftpclient.setDefaultTimeout(10*1000);//对于 FTPClient 而言，setDefaultTimeout() 超时的工作跟 setSoTimeout() 是相同的，区别仅在于后者会覆盖掉前者设置的值。
		ftpClient.setSoTimeout(30 * 1000);

		ftpClient.enterLocalPassiveMode();//client被动模式
	}

	private void init(String ip, String port, String username, String password)
			throws Exception {
		ipAddress = ip;
		if (StringUtils.isNotEmpty(port)) {
			ipPort = Integer.parseInt(port);
		} else {
			ipPort = 21;
		}

		ftpClient = new FTPClient();
		userName = username;
		passWord = password;
	}

	/**
	 * 登录FTP服务器
	 * 
	 * @throws Exception
	 */
    public void login() throws Exception {
		if(!ftpClient.isConnected()){
			ftpClient.connect(ipAddress, ipPort);
		}
        ftpClient.login(userName, passWord);
        int reply = ftpClient.getReplyCode();
        if (!FTPReply.isPositiveCompletion(reply)) {
            logout();
            log.error("服务器拒绝连接，返回码：" + reply);
            return;
        }
        ftpClient.setAutodetectUTF8(true);
        ftpClient.setControlEncoding(encoding);
        //设置文件类型（二进制）
        ftpClient.setFileType(FTPClient.BINARY_FILE_TYPE);
        ftpClient.setBufferSize(1024);
        ftpClient.enterLocalPassiveMode();
    }

    /**
	 * 退出FTP服务器
	 * 
	 * @throws Exception
	 */
	public void logout() throws Exception {
        if (ftpClient != null) { 
        	try {
        		ftpClient.logout();
    		} catch (Exception  e) {			
    			log.error("ftpclient.logout:"+e.getMessage(),e);
    		}finally{
    			if (ftpClient.isConnected()) {
    				try {  
    					ftpClient.disconnect();
    				} catch(Exception ioe) {  
    					log.error("ftpclient.disconnect:"+ioe.getMessage(),ioe); 
    				}  
        		}
    		}
        } 
	}

	/**
	 * 在FTP服务器上建立指定的目录,当目录已经存在的情下不会影响目录下的文件,这样用以判断FTP
	 * 上传文件时保证目录的存在目录格式必须以"/"根目录开头
	 * 
	 * @param pathList
	 *            String
	 * @throws Exception
	 */
	public void buildList(String pathList) {
		try {
			login();
			StringTokenizer s = new StringTokenizer(pathList, "/"); // sign
			String pathName = "";
			String tempdir = "";
			
			while (s.hasMoreElements()) {
				tempdir = utf8toiso8859((String) s.nextElement());
				pathName =  utf8toiso8859((pathName + "/" + tempdir));
				boolean flag = ftpClient.changeWorkingDirectory(tempdir);
				if(!flag){
					ftpClient.makeDirectory(tempdir);
					ftpClient.changeWorkingDirectory(tempdir);
				}
			}
		}catch (Exception e) {
			 e.printStackTrace();
		}finally{
			try {
				logout();
			} catch (Exception e) {
				e.printStackTrace();
			}
	    }
	}
	
	public void doBuildList(String pathList) throws Exception{
		StringTokenizer s = new StringTokenizer(pathList, "/"); // sign
		String pathName = "";
		String tempdir = "";
		while (s.hasMoreElements()) {
			tempdir = utf8toiso8859((String) s.nextElement());
			pathName =  utf8toiso8859((pathName + "/" + tempdir));
			boolean flag = ftpClient.changeWorkingDirectory(tempdir);
			if(!flag){
				ftpClient.makeDirectory(tempdir);
				ftpClient.changeWorkingDirectory(tempdir);
			}
		}
	}


	/**
	 * 上传文件到FTP服务器,destination路径以FTP服务器的"/"开始，带文件名、 上传文件只能使用二进制模式，当文件存在时再次上传则会覆盖
	 * 
	 * @param source
	 *            String
	 * @param destination
	 *            String
	 * @throws Exception
	 */
	public String upFile(String source, String destination,String rootUrl){
		String url = "";
		FileInputStream  ftpIn = null;
		try {
			login();
			String name = destination;
			if(destination.indexOf("/")>=0){
				String temp = destination.substring(0, destination.lastIndexOf("/"));
				boolean changeDir = ftpClient.changeWorkingDirectory(temp);
				if(!changeDir)doBuildList(temp);
				name = destination.substring(destination.lastIndexOf("/")+1);
			}
			ftpIn = new FileInputStream(new File(source));
			boolean flag = ftpClient.storeFile(name, ftpIn);
			log.info("store file success:" + flag + ",ftpclient reply string:" + ftpClient.getReplyString());
			
			if(flag)url = rootUrl + destination;
		}catch (Exception e) {
			 e.printStackTrace();
		}finally{
			try {
				if(ftpIn != null){
					ftpIn.close();
				}
				logout();
			} catch (Exception e) {
				e.printStackTrace();
			}
	    }
		return url;
	}
	
	public String upFile(InputStream input, String destination,String rootUrl){
		String url = "";
		try {
			login();
			String name = destination;
			if(destination.indexOf("/")>=0){
				String temp = destination.substring(0, destination.lastIndexOf("/"));
				boolean changeDir = ftpClient.changeWorkingDirectory(temp);
				if(!changeDir)doBuildList(temp);
				name = destination.substring(destination.lastIndexOf("/")+1);
			}
			boolean flag = ftpClient.storeFile(name, input);
			log.info("store file success:" + flag + ",ftpclient reply string:" + ftpClient.getReplyString());
			
			if(flag) url = rootUrl + destination;
		}catch (Exception e) {
			 e.printStackTrace();
		}finally{
			try {
				logout();
				if(input != null){
					input.close();
				}
			} catch (Exception e) {
				e.printStackTrace();
			}
	    }
		return url;
	}


	public String upFile(InputStream input, String destination,String rootUrl, String inFolder){
		String url = "";
		try {
			login();
			String name = destination;
			if(destination.indexOf("/")>=0){
				String temp = destination.substring(0, destination.lastIndexOf("/"));
				boolean changeDir = ftpClient.changeWorkingDirectory(temp);
				if(!changeDir)doBuildList(temp);
				name = destination.substring(destination.lastIndexOf("/")+1);
			}
			log.info("store name:"+ name);
			boolean flag = ftpClient.storeFile(name, input);
			log.info("store file success:" + flag + ",ftpclient reply string:" + ftpClient.getReplyString());

			if(flag) url = rootUrl + destination;
		}catch (Exception e) {
			e.printStackTrace();
		}finally{
			try {
//				logout();
				if (ftpClient != null) {
					try {
						if (ftpClient.isConnected()){
							ftpClient.logout();
						}
					} catch (Exception  e) {
						log.error("ftpclient.logout:"+e.getMessage(),e);
					}finally{
						if (ftpClient.isConnected()) {
							try {
								ftpClient.disconnect();
							} catch(Exception ioe) {
								log.error("ftpclient.disconnect:"+ioe.getMessage(),ioe);
							}
						}
					}
				}
				if(input != null){
					input.close();
				}
			} catch (Exception e) {
				e.printStackTrace();
			}
		}
		return url;
	}

	/**
	 * JSP中的流上传到FTP服务器, 上传文件只能使用二进制模式，当文件存在时再次上传则会覆盖 字节数组做为文件的输入流,此方法适用于JSP中通过
	 * request输入流来直接上传文件在RequestUpload类中调用了此方法， destination路径以FTP服务器的"/"开始，带文件名
	 * 
	 * @param sourceData
	 *            byte[]
	 * @param destination
	 *            String
	 * @throws Exception
	 */
	public String upFile(byte[] sourceData, String destination,String rootUrl){
		String url = "";
		InputStream  ftpIn = null;
		try {
			login();
			String name = destination;
			if(destination.indexOf("/")>=0){
				String temp = destination.substring(0, destination.lastIndexOf("/"));
				boolean changeDir = ftpClient.changeWorkingDirectory(temp);
				if(!changeDir)doBuildList(temp);
				name = destination.substring(destination.lastIndexOf("/")+1);
			}
			ftpIn = new ByteArrayInputStream(sourceData);
			boolean flag = ftpClient.storeFile(name, ftpIn);
			log.info("store file success:" + flag + ",ftpclient reply string:" + ftpClient.getReplyString());
			
			if(flag)url = rootUrl + destination;
		}catch (Exception e) {
			 e.printStackTrace();
		}finally{
			try {
				if(ftpIn != null){
					ftpIn.close();
				}
				logout();
			} catch (Exception e) {
				e.printStackTrace();
			}
	    }
		return url;
	}
	
	public String upFile(File file, String destination,String rootUrl) {
		String url = "";
		FileInputStream  ftpIn = null;
		try {
			login();
			String name = destination;
			if(destination.indexOf("/")>=0){
				String temp = destination.substring(0, destination.lastIndexOf("/"));
				boolean changeDir = ftpClient.changeWorkingDirectory(temp);
				if(!changeDir)doBuildList(temp);
				name = destination.substring(destination.lastIndexOf("/")+1);
			}
			ftpIn = new FileInputStream(file);
			boolean flag = ftpClient.storeFile(name, ftpIn);
			log.info("store file success:" + flag + ",ftpclient reply string:" + ftpClient.getReplyString());
			
			if(flag)url = rootUrl + destination;
		}catch (Exception e) {
			 e.printStackTrace();
		}finally{
			try {
				if(ftpIn != null){
					ftpIn.close();
				}
				logout();
			} catch (Exception e) {
				e.printStackTrace();
			}
	    }
		return url;
	}

	/**
	 * 从FTP文件服务器上下载文件SourceFileName，到本地destinationFileName 所有的文件名中都要求包括完整的路径名在内
	 * 
	 * @param sourceFileName the absolute path in the ftp server .eg /a/b/c
	 *            String
	 * @param destinationFileName
	 *            String
	 * @throws Exception
	 */
    public boolean downFile(String sourceFileName, String destinationFileName) {
        FileOutputStream fos = null;
        try {
            login();
            fos = new FileOutputStream(destinationFileName);
			int lastIndexOf = sourceFileName.lastIndexOf('/');
			String dir = lastIndexOf == 0 ? "/" : sourceFileName.substring(0, lastIndexOf);
			if (!cd(dir)) {
				log.error("goto dir error:" + dir);
				return false;
			}

			boolean flag = ftpClient.retrieveFile(sourceFileName.substring(lastIndexOf + 1), fos);
            log.info("down file success:" + flag + ",ftpclient reply string:" + ftpClient
                    .getReplyString());
            return flag;
        } catch (Exception e) {
            log.error("", e);
        } finally {
            try {
                if (fos != null) {
                    fos.close();
                }
            } catch (Exception e) {
                log.error("close file error", e);
            }
			try {
				logout();
			} catch (Exception e) {
				log.error("logout error", e);
			}
		}
        return false;
    }

    /** 
     * 转码[utf-8 -> ISO-8859-1] 不同的平台需要不同的转码     FTP协议传输的是iso-8859-1 根据FTP服务器配置的编码转成对应的iso-8859-1
     * 
     * @param obj 
     * @return "" 
     */ 
    public  String utf8toiso8859(Object obj) { 
            try { 
            	if (obj == null) 
                     return ""; 
            	else 
                     return new String(obj.toString().getBytes(encoding), "iso-8859-1"); 
            } catch (Exception e) { 
                    return ""; 
            } 
    } 
    
    /** 
     * 删除一个文件 
     */ 
    public boolean deleteFile(String filename) { 
        boolean flag = true; 
        try { 
        	login();
            flag = ftpClient.deleteFile(utf8toiso8859(filename)); 
        	log.info("delete file "+filename+" success:" + flag + ",ftpclient reply string:" + ftpClient.getReplyString());
        } catch (Exception e) {
			e.printStackTrace();
		}finally{
			try {
				logout();
			} catch (Exception e) {
				e.printStackTrace();
			}
	    }
        return flag; 
    } 
   
    /** 
     * 删除一个文件 
     */ 
    public boolean doDeleteFile(String filename) { 
        boolean flag = true; 
        try { 
            flag = ftpClient.deleteFile(utf8toiso8859(filename)); 
        	log.info("delete file "+filename+" success:" + flag + ",ftpclient reply string:" + ftpClient.getReplyString());
        } catch (Exception e) {
			e.printStackTrace();
		}
        return flag; 
    } 
    
    /** 
     * 删除目录 
     */ 
    public Boolean deleteDirectory(String pathname) { 
    	boolean flag = true; 
        try { 
        	login();
        	if(ftpClient.changeWorkingDirectory(utf8toiso8859(pathname))){
            	FTPFile[] files = ftpClient.listFiles();
            	for(FTPFile file : files){
            		//System.out.println(file.getName());
            		//windows 系统做ftp服务器 会出现获取文件列表二外获取到.和 .. 两个目录
                    if (file.isDirectory() && !".".equals(file.getName()) && !"..".equals(file.getName())) { 
                    	doDeleteDirectory(pathname+file.getName()+"/");
                    	//doDeleteDirectory(file.getName());
                    	//System.out.println("del dir "+file.getName());
	                } else { 
	                	doDeleteFile(pathname+file.getName());
	                	//System.out.println("del "+file.getName());
	                } 
            	}
        	 }
        	//System.out.println("del dir "+pathname);
 
        	flag = ftpClient.removeDirectory(utf8toiso8859(pathname)); 
        	log.info("delete file "+pathname+" success:" + flag + ",ftpclient reply string:" + ftpClient.getReplyString());
        } catch (Exception e) {
			e.printStackTrace();
		}finally{
			try {
				logout();
			} catch (Exception e) {
				e.printStackTrace();
			}
	    }
        return flag; 
    } 
    
    /** 
     * 删除目录 
     */ 
    public Boolean doDeleteDirectory(String pathname) { 
    	boolean flag = true; 
        try { 
        	if(ftpClient.changeWorkingDirectory(utf8toiso8859(pathname))){
            	FTPFile[] files = ftpClient.listFiles();
            	for(FTPFile file : files){
            		//System.out.println(file.getName());
                    if (file.isDirectory() && !".".equals(file.getName()) && !"..".equals(file.getName())) { 
                    	doDeleteDirectory(pathname+file.getName()+"/");
                    	//doDeleteDirectory(file.getName());
                    	//System.out.println("del dir "+file.getName());
	                } else { 
	                	doDeleteFile(pathname+file.getName());
	                	//System.out.println("del "+file.getName());
	                } 
            	}
        	 }
        	flag = ftpClient.removeDirectory(utf8toiso8859(pathname)); 
        	log.info("delete file "+pathname+" success:" + flag + ",ftpclient reply string:" + ftpClient.getReplyString());
        } catch (Exception e) {
			e.printStackTrace();
		}
        return flag; 
    } 
   
    //获取FTP内容
    public String getFtpContent(String ftpurl) {
    	String str=null;
		BufferedReader br = null;
		InputStream in = null;
 	    try {
 		  Map<String, String> map = URLUtil.parseFtp(ftpurl);
 		  String ipAdress = map.get("ipAddress");
 		  int ipPort = SafeUtils.getInt(map.get("ipPort"),21);
 		  String userName = map.get("userName");
 		  String passWord = map.get("passWord");
// 		  log.info("ftp ipAdress="+ipAdress+",ipPort="+ipPort+",userName="+userName+",passWord="+passWord);
 		  login();
	      	String path = map.get("path");
	      	path=path.replace("//","/"); //防止双杠导致目录失败
	      	log.info("ftp path="+path);
			String changePath = path.substring(0, path.lastIndexOf("/")+1);
//			log.info("ftp changePath="+changePath);
			//先试全部目录
			boolean re = ftpClient.changeWorkingDirectory(changePath);
//			log.info("ftp re "+re);
			//如果整体不能取，分开取
	         if(!re){
				String[] tempChangePath = changePath.split("/");
				for (int i = 1; i < tempChangePath.length; i++) {
					re = ftpClient.changeWorkingDirectory(tempChangePath[i]);
					if(!re){
						log.error("changeWorkingDirectory is fail!");
						return str;
					}
				}
	         }
			String fileStr = path.replace(changePath,"");
			log.info("ftp fileStr="+fileStr);
	      	in =  ftpClient.retrieveFileStream(fileStr);
	      	br = new BufferedReader(new InputStreamReader(in,"utf-8"));
	         String data = null;  
	      	StringBuffer resultBuffer = new StringBuffer();  
	         while ((data = br.readLine()) != null) {  
	             resultBuffer.append(data + "\n");  
	         } 
	         str = resultBuffer.toString();
//			log.info("ftp content:"+str);

 		} catch (Exception e) {
 			log.error("getFtpContent:"+e.getMessage(),e);
 		}
 	    finally {
			try {
				if (ftpClient != null) {
					try {
						ftpClient.logout();
					} catch (Exception  e) {
						log.error("ftpclient.logout:"+e.getMessage(),e);
					}finally{
						if (ftpClient.isConnected()) {
							try {
								ftpClient.disconnect();
							} catch(Exception ioe) {
								log.error("ftpclient.disconnect:"+ioe.getMessage(),ioe);
							}
						}
					}
				}
				if (br != null) {
					br.close();
				}
				if (in != null) {
					in.close();
				}
			} catch (Exception e) {
				e.printStackTrace();
			}
		}
 		
 	    return str;
    }


	public String getFtpContentByGBK(String ftpurl) {
		String str=null;
		BufferedReader br = null;
		InputStream in = null;
		try {
			Map<String, String> map = URLUtil.parseFtp(ftpurl);
			String ipAdress = map.get("ipAddress");
			int ipPort = SafeUtils.getInt(map.get("ipPort"),21);
			String userName = map.get("userName");
			String passWord = map.get("passWord");
// 		  log.info("ftp ipAdress="+ipAdress+",ipPort="+ipPort+",userName="+userName+",passWord="+passWord);
			login();
			String path = map.get("path");
			path=path.replace("//","/"); //防止双杠导致目录失败
			log.info("ftp path="+path);
			String changePath = path.substring(0, path.lastIndexOf("/")+1);
//			log.info("ftp changePath="+changePath);
			//先试全部目录
			boolean re = ftpClient.changeWorkingDirectory(changePath);
//			log.info("ftp re "+re);
			//如果整体不能取，分开取
			if(!re){
				String[] tempChangePath = changePath.split("/");
				for (int i = 1; i < tempChangePath.length; i++) {
					re = ftpClient.changeWorkingDirectory(tempChangePath[i]);
					if(!re){
						log.error("changeWorkingDirectory is fail!");
						return str;
					}
				}
			}
			String fileStr = path.replace(changePath,"");
			log.info("ftp fileStr="+fileStr);
			in =  ftpClient.retrieveFileStream(fileStr);
			br = new BufferedReader(new InputStreamReader(in,"GBK"));
			String data = null;
			StringBuffer resultBuffer = new StringBuffer();
			while ((data = br.readLine()) != null) {
				resultBuffer.append(data + "\n");
			}
			str = resultBuffer.toString();
//			log.info("ftp content:"+str);

		} catch (Exception e) {
			log.error("getFtpContent:"+e.getMessage(),e);
		}
		finally {
			try {
				if (ftpClient != null) {
					try {
						ftpClient.logout();
					} catch (Exception  e) {
						log.error("ftpclient.logout:"+e.getMessage(),e);
					}finally{
						if (ftpClient.isConnected()) {
							try {
								ftpClient.disconnect();
							} catch(Exception ioe) {
								log.error("ftpclient.disconnect:"+ioe.getMessage(),ioe);
							}
						}
					}
				}
				if (br != null) {
					br.close();
				}
				if (in != null) {
					in.close();
				}
			} catch (Exception e) {
				e.printStackTrace();
			}
		}

		return str;
	}

	/**
	 * @param ftpUrl standard ftp url
	 * @return the remote path, .eg ftp://************/a/b/c -> /a/b/c
     */
    public static String getPath(String ftpUrl) {
		int index = ftpUrl.indexOf('/', FTP_HEADER.length() + 1);
		if (index >= 0) {
			return ftpUrl.substring(index);
		}
        return "/";
    }

    public static String getUrl(String userName, String passWord,
            String host, int port, String path) {
        return FTP_HEADER + userName + ":" + passWord + "@" + host + ":" + port + path;
    }

    /**
     * get path size
     * @param ftpUrl ftp url
     * @return the size of file or directory
     */
    public long getSize(String ftpUrl) {
        try {
            login();
            String path = getPath(ftpUrl);
            FTPFile[] files = ftpClient.listFiles(path);
            if (files == null || files.length == 0) {
                return -1;
            }

            long ret = 0;
            for (FTPFile f : files) {
                ret += f.getSize();
            }
            return ret;
        } catch (Exception e) {
            log.error("get size", e);
        } finally {
            try {
                logout();
            } catch (Exception e) {
                log.error("log out", e);
            }
        }
        return -1;
    }

	/**
	 * mkdirs
	 * @param remotePath
     */
	public void mkdirs(String remotePath) {
		if (remotePath.startsWith("/")) {
			remotePath = remotePath.substring(1);
		}
		try {
			login();
			int index = 0, nindex;
			while ((nindex = remotePath.indexOf('/', index)) > 0) {
				ftpClient.makeDirectory(remotePath.substring(0, nindex));
				index = nindex + 1;
			}
			ftpClient.makeDirectory(remotePath);
		} catch (Exception e) {
			log.error("mkdir error:" + remotePath, e);
		} finally {
			try {
				logout();
			} catch (Exception e) {
				log.error("log out", e);
			}
		}
	}

	private void makeSureDirExist(String dir) {
		mkdirs(dir);
	}

	private boolean cd(String dir) throws IOException {
		boolean flag = ftpClient.changeWorkingDirectory(dir);
		log.info("goto dir result:" + flag + ",ftpclient reply string:"
				+ ftpClient.getReplyString());
		return flag;
	}

	public boolean upFile(String localFilePath, String remoteFilePath) {
        Preconditions.checkArgument(remoteFilePath.charAt(0) == '/');
		FileInputStream ftpIn = null;
        if (remoteFilePath.indexOf('/', 1) > 0) {
            makeSureDirExist(remoteFilePath.substring(0, remoteFilePath.lastIndexOf("/")));
        }

		try {
			login();
			ftpIn = new FileInputStream(new File(localFilePath));
			int endIndex = remoteFilePath.lastIndexOf('/');
			cd(endIndex == 0 ? "/" : remoteFilePath.substring(0, endIndex));
			boolean flag = ftpClient.storeFile(remoteFilePath.substring(endIndex + 1), ftpIn);
			log.info("upload file result:" + flag + ",ftpclient reply string:"
					+ ftpClient.getReplyString());
			return flag;
		} catch (Exception e) {
			log.error("upload failed", e);
		} finally {
			try {
				if (ftpIn != null) {
					ftpIn.close();
				}
			} catch (Exception e) {
				log.error("close file error", e);
			}

			try {
				logout();
			} catch (Exception e) {
				log.error("log out error", e);
			}
		}
		return false;
	}

    public static void main(String[] args) throws Exception {
		testDownload("***************************************/test/20160630/test.jpg");
    }

	private static void testDownload(String url) throws Exception {
		FtpUtil ftpUtil = new FtpUtil(url);
		String path = getPath(url);
		String targetPath = "/home/<USER>/work/zzfw/code/ATN/c2in/download" + path;
		FileUtil.makeSure(new File(targetPath).getParentFile());
		ftpUtil.downFile(path, targetPath);
	}
}
