package com.xxl.job.executor.c2in.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.pukka.iptv.common.data.model.cms.CmsPicture;

import java.util.List;

/**
 *
 * @author: tan
 * @date: 2021年8月27日 上午10:00:46
 */

public interface CmsPictureService extends IService<CmsPicture> {

    /**
     * 根据code查询
     * @param code code
     * @return
     */
    CmsPicture getByCode(String code);

    CmsPicture getByCodeCpId(String code, Long cpId);

    /**
     *
     * @param code
     * @return 查询content属性为空的数据
     */
    CmsPicture getByCodeAndContentIsNull(String code);

    /**
     * 根据code删除
     * @param code code
     */
    void removeByCode(String code);

    void removeByContentCode(String code);

    /**
     * 根据contentCode统计
     * @param contentCode contentCode
     * @return
     */
    Long countByContentCode(String contentCode);

    /**
     * 根据content查询
     * @param content content
     * @param code code
     * @return
     */
    CmsPicture getByCodeAndContentCode(String code, String content);

    /**
     * 根据code 和 contentCode删除
     * @param code code
     */
    void removeMappingByCodeAndContentCode(String code, String content);

    List<CmsPicture> listByContentCode(String code);


    List<CmsPicture> listByCode(String code);

    void updateByCode(CmsPicture cmsPicture);
}


