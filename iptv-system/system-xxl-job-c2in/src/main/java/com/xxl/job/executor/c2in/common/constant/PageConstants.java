package com.xxl.job.executor.c2in.common.constant;

/**
 * @ClassName PageConstants
 * @Description 页面常量
 * <AUTHOR>
 * @Date 2021/9/14 9:36
 * @Version
 */
public class PageConstants {

    /**
     * 页面
     */
    public static final Integer PAGE_NUMBER  = 1;

    /**
     * 页数
     */
    public static final Integer PAGE_SIZE = 10;




    //每次获取推送的订单数量

    public static final Integer IN_ORDER_PAGE_SIZE = 5;

    //条件查询所有
    public static final Integer IN_ORDER_VIEW_STATE_ALL = 0;

    public static final Integer IN_ORDER_VIEW_STATE_WAIT_PARSE = 2;

    public static final Integer IN_ORDER_VIEW_STATE_PARSING = 3;

    public static final Integer IN_ORDER_VIEW_STATE_PARSE_ERROR = 4;

    public static final Integer IN_ORDER_VIEW_STATE_WAIT_DATABASE = 5;

    public static final Integer IN_ORDER_VIEW_STATE_DONGING_DATABASE = 6;

    public static final Integer IN_ORDER_VIEW_STATE_SUCCESS_DATABASE = 7;

    public static final Integer IN_ORDER_VIEW_STATE_ERROR_DATABASE = 8;

    /**
     * 对应数据库定义字段--状态
     * 1：待解析
     * 2：解析中
     * 3：解析成功
     * 4：解析失败
     * 5：入库中
     * 6：入库成功
     * 7：入库失败
     * 8：反馈中
     * 9：反馈成功
     * 10：反馈失败
     */
    public static final Integer DATABASE_VIEW_STATE_WAIT_PARSE = 1;

    public static final Integer DATABASE_VIEW_STATE_PARSING = 2;

    public static final Integer DATABASE_VIEW_STATE_PARSING_SUCCESS = 3;

    public static final Integer DATABASE_VIEW_STATE_PARSE_ERROR = 4;

    public static final Integer DATABASE_VIEW_STATE_WAIT_DATABASE = 5;

    public static final Integer DATABASE_VIEW_STATE_SUCCESS_DATABASE = 6;

    public static final Integer DATABASE_VIEW_STATE_ERROR_DATABASE = 7;

    public static final Integer DATABASE_VIEW_STATE_WAIT_FEEDBACK = 8;

    public static final Integer DATABASE_VIEW_STATE_SUCCESS_FEEDBACK = 9;

    public static final Integer DATABASE_VIEW_STATE_ERROR_FEEDBACK = 10;
}
