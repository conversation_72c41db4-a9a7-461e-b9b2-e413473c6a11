package com.xxl.job.executor.c2in.common.enums;

public enum  ActionEnums {

    REGIST(1, "REGIST"), UPDATE(2, "UPDATE"), DELETE(3, "DELETE");

    private final Integer code;
    private final String info;


    public static Integer getCodeByInfo(String info) {
        ActionEnums[] actionEnums = values();
        for (ActionEnums element : actionEnums) {
            if (element.getInfo().equals(info.trim())) {
                return element.getCode();
            }
        }
        return null;
    }

    public static String getInfoByCode(Integer code) {
        ActionEnums[] actionEnums = values();
        for (ActionEnums element : actionEnums) {
            if (element.getCode().intValue() == code.intValue()) {
                return element.getInfo();
            }
        }
        return null;
    }

    ActionEnums(Integer code, String info)
    {
        this.code = code;
        this.info = info;
    }

    public Integer getCode()
    {
        return code;
    }

    public String getInfo()
    {
        return info;
    }
}
