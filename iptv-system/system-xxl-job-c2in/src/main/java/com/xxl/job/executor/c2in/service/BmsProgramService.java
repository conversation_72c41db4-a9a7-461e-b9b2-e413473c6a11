package com.xxl.job.executor.c2in.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.pukka.iptv.common.data.model.*;
import com.xxl.job.executor.c2in.model.BmsChannel;
import com.xxl.job.executor.c2in.model.BmsProgram;

import java.util.List;

/**
 *
 * @author: liuli
 * @date: 2021年9月2日 下午3:35:55
 */

public interface BmsProgramService extends IService<BmsProgram> {
    /**
     * 根据code和spid查 BmsProgram
     * @param cmsContentCode
     * @param spId
     * @return
     */
    BmsProgram getByCodeAndSpId(String cmsContentCode, Long spId);

    /**
     * 根据CmsSeriesCode统计
     * @param code code
     * @return
     */
    Long countByCmsSeriesCode(String code);

    /**
     * 根据CmsContentCode查询
     *
     * @return
     */
    List<BmsProgram> listByCmsContentCode(String code);

    /**
     * 根据CmsContentCode查询
     * @param code
     */
    void removeByCmsContentCode(String code);

    /**
     * 根据Program和Series code删除
     *
     * @param seriesCode seriesCode
     * @param programCode programCode
     * @return
     */
    List<BmsProgram> listByProgramCodeSeriesCode(String seriesCode, String programCode);

    /**
     * 删除关系 清除series相关字段
     *
     * @param seriesCode
     * @param programCode
     */
    void deleteMappingSeriesByCode(String seriesCode, String programCode);

    void removeByCmsSeriesId(Long id);

}


