package com.xxl.job.executor.c2in.common.util;

/**
 * <AUTHOR>
 * @Date 2021/10/16 10:52
 * @Description
 */
public class StatusUtils {

    public static class Status {
        int code;
        String msg;

        public Status(int code, String msg){
            this.code = code;
            this.msg = msg;
        }

        public int getCode() {
            return code;
        }

        public String getMsg() {
            return msg;
        }
    }

    public static Status success(){
        return new Status(1, "处理成功");
    }

    public static Status success(String msg){
        return new Status(1, "处理成功");
    }

    public static Status success(String msg, boolean flag){
        return new Status(1, msg);
    }

    public static Status asy(){
        return new Status(2, "异步处理");
    }

    public static Status fail(String msg){

        return new Status(0, (msg != null && msg.length()>200) ? msg.substring(0, 200) : msg);
    }

    public static Status uploadFail(String msg){
        // 错误信息长度最多200
        return new Status(3, (msg != null && msg.length()>200) ? msg.substring(0, 200) : msg);
    }
}
