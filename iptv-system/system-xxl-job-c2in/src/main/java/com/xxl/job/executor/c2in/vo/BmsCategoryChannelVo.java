package com.xxl.job.executor.c2in.vo;

import com.xxl.job.executor.c2in.common.enums.PublishStatusEnums;
import com.xxl.job.executor.c2in.common.enums.SourceEnums;
import com.xxl.job.executor.c2in.model.BmsCategory;
import com.xxl.job.executor.c2in.model.BmsCategoryChannel;
import com.xxl.job.executor.c2in.model.BmsChannel;
import io.swagger.annotations.ApiModel;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;
import lombok.Data;

/**
 *
 * @author: liuli
 * @date: 2021年9月7日 上午11:29:55
 */

@Data
@AllArgsConstructor
@JsonIgnoreProperties(value = { "handler" })
@ApiModel("BmsCategoryChannelVo")
public class BmsCategoryChannelVo extends BmsCategoryChannel implements java.io.Serializable{

    public BmsCategoryChannelVo(BmsCategory bmsCategory, BmsChannel bmsChannel) {
        this.setBmsChannelId(bmsChannel.getId());
        this.setCmsChannelCode(bmsChannel.getCmsChannelCode());
        this.setChannelName(bmsChannel.getName());
        this.setCategoryId(bmsCategory.getId());
        this.setCategoryCode(bmsCategory.getCode());
        this.setCategoryName(bmsCategory.getName());
        this.setSpId(bmsCategory.getSpId());
        this.setSpName(bmsCategory.getSpName());
        this.setStatus(bmsCategory.getStatus());
        this.setPublishStatus(PublishStatusEnums.WAITPUBLISH.getCode());
        this.setOutPassageIds(bmsCategory.getOutPassageIds());
        this.setOutPassageNames(bmsCategory.getOutPassageNames());
        this.setSource(SourceEnums.INJECT.valueOf());

    }
}
