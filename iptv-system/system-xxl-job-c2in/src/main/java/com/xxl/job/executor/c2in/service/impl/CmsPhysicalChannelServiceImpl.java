package com.xxl.job.executor.c2in.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.pukka.iptv.common.data.model.cms.CmsPhysicalChannel;
import com.xxl.job.executor.c2in.common.constant.TextConstants;
import com.xxl.job.executor.c2in.mapper.CmsPhysicalChannelMapper;
import com.xxl.job.executor.c2in.service.CmsPhysicalChannelService;
import org.springframework.stereotype.Service;

/**
 *
 * @author: tan
 * @date: 2021年8月27日 上午10:00:53
 */

@Service
public class CmsPhysicalChannelServiceImpl extends ServiceImpl<CmsPhysicalChannelMapper, CmsPhysicalChannel> implements CmsPhysicalChannelService {

    @Override
    public Long countByChannelCode(String channelCode) {
        LambdaQueryWrapper<CmsPhysicalChannel> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.eq(CmsPhysicalChannel::getChannelCode, channelCode);
        return this.count(queryWrapper);
    }

    @Override
    public Long countByCode(String code) {
        LambdaQueryWrapper<CmsPhysicalChannel> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.eq(CmsPhysicalChannel::getCode, code);
        return this.count(queryWrapper);
    }

    @Override
    public void removeByCode(String code) {
        LambdaQueryWrapper<CmsPhysicalChannel> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.eq(CmsPhysicalChannel::getCode, code);
        this.remove(queryWrapper);
    }

    @Override
    public CmsPhysicalChannel getByCode(String code) {
        LambdaQueryWrapper<CmsPhysicalChannel> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.eq(CmsPhysicalChannel::getCode, code).last(TextConstants.LIMIT_ONE);
        return this.getOne(queryWrapper);
    }

    @Override
    public CmsPhysicalChannel getByCodeCpId(String code, Long cpId) {
        LambdaQueryWrapper<CmsPhysicalChannel> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.eq(CmsPhysicalChannel::getCode, code).eq(CmsPhysicalChannel::getCpId, cpId).last(TextConstants.LIMIT_ONE);
        return this.getOne(queryWrapper);
    }

    @Override
    public void removeByChannelCode(String channelCode) {
        LambdaQueryWrapper<CmsPhysicalChannel> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.eq(CmsPhysicalChannel::getChannelCode, channelCode);
        this.remove(queryWrapper);
    }
}


