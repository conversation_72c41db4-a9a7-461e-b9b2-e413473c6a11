package com.xxl.job.executor.service.jobhandler;

import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.pukka.iptv.common.base.enums.ContentTypeEnum;
import com.pukka.iptv.common.base.enums.MissingDetectionEnum;
import com.pukka.iptv.common.data.model.cms.CmsProgram;
import com.pukka.iptv.common.data.model.cms.CmsSeries;
import com.xxl.job.core.handler.annotation.XxlJob;
import com.xxl.job.executor.c2in.common.enums.MissedStatusEnums;
import com.xxl.job.executor.c2in.common.enums.MissingDetectionEnums;
import com.xxl.job.executor.c2in.mapper.BmsContentMapper;
import com.xxl.job.executor.c2in.mapper.CmsProgramMapper;
import com.xxl.job.executor.c2in.mapper.CmsSeriesMapper;
import com.xxl.job.executor.c2in.model.BmsContent;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR> 2021-10-12 17:18:19
 */
@Component
@Slf4j
public class MissedInfoXxlJob {

    @Autowired
    private CmsSeriesMapper cmsSeriesMapper;

    @Autowired
    private CmsProgramMapper cmsProgramMapper;

    @Autowired
    private BmsContentMapper bmsContentMapper;

 
    /**
     * 缺集检测:
     * 定时任务扫描剧集表 对表中缺集检测状态为 需要检测 的剧集进行检测
     * 如果检测到剧集下有子集集数不连续 将剧集缺集状态设置为 是 将缺集信息保存
     */
    @XxlJob("MissedXxlJob")
    public void missedXxlJob() {
        try {
            //需要检测的剧集
            List<CmsSeries> cmsSeries = cmsSeriesMapper.selectList(Wrappers.<CmsSeries>lambdaQuery().eq(CmsSeries::getMissingDetection, MissingDetectionEnum.YES.getValue()));
            for (CmsSeries series : cmsSeries) {
                try {
                    LambdaUpdateWrapper<CmsSeries> wrapper = new LambdaUpdateWrapper<>();
                    //剧集下的所有子集
                    List<CmsProgram> cmsPrograms = cmsProgramMapper.selectList(Wrappers.<CmsProgram>lambdaQuery().eq(CmsProgram::getSeriesId, series.getId()));
                    if (ObjectUtil.isNotEmpty(cmsPrograms)) {
                        wrapper.set(CmsSeries::getVolumnUpdate, cmsPrograms.size());
                        //采集子集集数
                        List<Integer> episodeIndex = new ArrayList<>();
                        for (CmsProgram cmsProgram : cmsPrograms) {
                            if (ObjectUtil.isNotEmpty(cmsProgram.getEpisodeIndex())) {
                                episodeIndex.add(cmsProgram.getEpisodeIndex());
                            }
                        }
                        if (ObjectUtil.isNotEmpty(episodeIndex)) {
                            //缺集信息
                            Integer integer = episodeIndex.stream().max(Integer::compare).get();
                            List<String> missing = missedInfo(episodeIndex.toArray(new Integer[0]), integer);
                            //            LambdaUpdateWrapper<BmsContent> bmsContentLambdaUpdateWrapper = new LambdaUpdateWrapper<>();
                            if (ObjectUtil.isNotEmpty(missing)) {
                                wrapper
                                        .set(CmsSeries::getMissedInfo, "疑似缺集: " + StringUtils.join(missing, "、"))
                                        .set(CmsSeries::getMissedStatus, MissedStatusEnums.YES.getCode());

                                //                bmsContentLambdaUpdateWrapper
                                //                        .set(BmsContent::getMissedInfo, "疑似缺集: " + StringUtils.join(missing, "、"));
                                //缺集信息补齐后 清空缺集信息
                            } else {
                                wrapper
                                        .set(CmsSeries::getMissedStatus, MissedStatusEnums.NO.getCode())
                                        .set(CmsSeries::getMissingDetection, MissingDetectionEnums.NO.getCode())
                                        .set(CmsSeries::getMissedInfo, "");

                 /*       bmsContentLambdaUpdateWrapper
                                .set(BmsContent::getMissedInfo, "");*/
                            }

           /*         bmsContentMapper.update(null, bmsContentLambdaUpdateWrapper
                            .eq(BmsContent::getCmsContentId, series.getId())
                            .eq(BmsContent::getContentType, ContentTypeEnum.TELEPLAY.getValue())
                    );*/

                        }
                    } else {
                        //剧集下没有子集也需要将缺集信息清空
                        wrapper
                                .set(CmsSeries::getMissedStatus, MissedStatusEnums.NO.getCode())
                                .set(CmsSeries::getMissingDetection, MissingDetectionEnums.NO.getCode())
                                .set(CmsSeries::getVolumnUpdate, 0)
                                .set(CmsSeries::getMissedInfo, "");
                        ;

                 /*       bmsContentLambdaUpdateWrapper
                                .set(BmsContent::getMissedInfo, "");*/
                    }
                    cmsSeriesMapper.update(null, wrapper.eq(CmsSeries::getId, series.getId()));
                } catch (Exception e) {
                    log.error("缺集检测执行失败",e);
                }
            }
            log.info("任务调度器执行了,当前时间:" + LocalDateTime.now());
        } catch (Exception e) {
            log.error("缺集检测执行失败",e);
        }
    }

    /**
     * 对连数组进行缺字段检测
     *
     * @param array 待检测的数组
     * @param end   数组的最后一位
     * @return 连续数组中缺失的信息
     */
    public static List<String> missedInfo(Integer[] array, Integer end) {
        List<String> missing = new ArrayList<>();
        int b[] = null;
        b = new int[end];
        for (int i = 0; i < array.length; i++) {
            b[array[i] - 1] = 1;
        }
        for (int i = 0; i < b.length; i++) {
            if (b[i] == 0) {
                // 最多展示150集缺集信息
                if (missing.size() >= 150) {
                    missing.add((i + 1) + "等...");
                    break;
                }
                missing.add(String.valueOf(i + 1));
            }
        }
        return missing;
    }
}
