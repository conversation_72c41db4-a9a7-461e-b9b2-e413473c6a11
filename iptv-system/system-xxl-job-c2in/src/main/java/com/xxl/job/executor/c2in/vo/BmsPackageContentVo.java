package com.xxl.job.executor.c2in.vo;

import com.xxl.job.executor.c2in.common.enums.PublishStatusEnums;
import com.xxl.job.executor.c2in.common.enums.SourceEnums;
import com.xxl.job.executor.c2in.model.BmsContent;
import com.xxl.job.executor.c2in.model.BmsPackage;
import com.xxl.job.executor.c2in.model.BmsPackageContent;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.pukka.iptv.common.data.model.*;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;
import lombok.NoArgsConstructor;
import java.util.Date;
import lombok.Data;
import com.fasterxml.jackson.annotation.JsonFormat;

/**
 *
 * @author: liuli
 * @date: 2021年9月2日 下午3:35:28
 */

@Data
@AllArgsConstructor
@JsonIgnoreProperties(value = { "handler" })
@ApiModel("BmsPackageContentVo")
public class BmsPackageContentVo extends BmsPackageContent implements java.io.Serializable{

    public BmsPackageContentVo(BmsPackage bmsPackage, BmsContent bmsContent) {
        this.setBmsContentId(bmsContent.getId());
        this.setCmsContentCode(bmsContent.getCmsContentCode());
        this.setContentType(bmsContent.getContentType());
        this.setContentName(bmsContent.getName());
        this.setPackageName(bmsPackage.getName());
        this.setPackageId(bmsPackage.getId());
        this.setPackageCode(bmsPackage.getCode());
        this.setSpId(bmsPackage.getSpId());
        this.setSpName(bmsPackage.getSpName());
        this.setStatus(bmsPackage.getStatus());
        this.setOutPassageIds(bmsPackage.getOutPassageIds());
        this.setOutPassageNames(bmsPackage.getOutPassageNames());
        this.setPublishStatus(PublishStatusEnums.WAITPUBLISH.getCode());
        this.setSource(SourceEnums.INJECT.valueOf());
    }
}
