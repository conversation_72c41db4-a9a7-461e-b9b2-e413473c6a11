package com.xxl.job.executor.c2in.order.processor.impl;

import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.pukka.iptv.common.api.feign.cms.CmsProgramFeignClient;
import com.pukka.iptv.common.base.constant.RedisKeyConstants;
import com.pukka.iptv.common.base.enums.*;
import com.pukka.iptv.common.base.exception.CommonResponseException;
import com.pukka.iptv.common.base.vo.CommonResponse;
import com.pukka.iptv.common.core.util.bean.BeanUtils;
import com.pukka.iptv.common.data.dto.CmsProgramDto;
import com.pukka.iptv.common.data.model.cms.*;
import com.pukka.iptv.common.data.model.in.InMapping;
import com.pukka.iptv.common.data.model.in.InMovie;
import com.pukka.iptv.common.data.model.in.InOrder;
import com.pukka.iptv.common.data.model.sys.SysDictionaryItem;
import com.pukka.iptv.common.data.model.sys.SysSp;
import com.pukka.iptv.common.rabbitmq.config.CmsContentRenameMQConfig;
import com.xxl.job.executor.c2in.common.constant.TableNameConstants;
import com.xxl.job.executor.c2in.common.constant.TextConstants;
import com.xxl.job.executor.c2in.common.enums.*;
import com.xxl.job.executor.c2in.common.enums.ActionEnums;
import com.xxl.job.executor.c2in.common.enums.InPassageTypeEnum;
import com.xxl.job.executor.c2in.common.enums.MovieTypeEnums;
import com.xxl.job.executor.c2in.common.util.DateUtils;
import com.xxl.job.executor.c2in.common.util.StatusUtils;
import com.xxl.job.executor.c2in.common.util.StrUtils;
import com.xxl.job.executor.c2in.model.*;
import com.xxl.job.executor.c2in.service.*;
import com.xxl.job.executor.c2in.vo.*;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.amqp.rabbit.core.RabbitTemplate;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @Date 2021/9/2 9:48
 * @Description 对象关系处理器
 */
@Slf4j
@Component(TableNameConstants.IN_MAPPING_TABLE)
public class MappingProcessorImpl extends AbsOrderProcessor {

    @Autowired
    CmsPictureService cmsPictureService;

    @Autowired
    CmsChannelService cmsChannelService;

    @Autowired
    CmsSeriesService cmsSeriesService;

    @Autowired
    CmsProgramService cmsProgramService;

    @Autowired
    BmsCategoryService bmsCategoryService;

    @Autowired
    BmsPackageService bmsPackageService;

    Map<String, InMovie> movieVoMap = null;

    @Autowired
    InMovieService inMovieService;

    @Autowired
    InOrderMappingsService inOrderMappingsService;

    @Autowired
    InMappingService inMappingService;

    @Autowired
    BmsChannelService bmsChannelService;

    @Autowired
    BmsCategoryChannelService bmsCategoryChannelService;

    @Autowired
    BmsContentService bmsContentService;

    @Autowired
    BmsCategoryContentService bmsCategoryContentService;

    @Autowired
    BmsPackageContentService bmsPackageContentService;

    @Autowired
    BmsPictureService bmsPictureService;

    @Autowired
    BmsProgramService bmsProgramService;

    @Autowired
    CmsMovieService cmsMovieService;

    @Autowired
    CmsResourceService cmsResourceService;

    @Autowired
    CmsProgramFeignClient cmsProgramFeignClient;

    @Autowired
    CmsDownloadService cmsDownloadService;

    @Autowired
    private RabbitTemplate rabbitTemplate;

    @Override
    public void beforeProcess(List<Object> dataList, InOrder order) {
        if (ActionEnums.REGIST.getCode().equals(order.getAction())) {
            // 图片的关系最后处理
            dataList.sort((o1, o2) -> {
                InMapping c1 = (InMapping) o1;
                InMapping c2 = (InMapping) o2;
                if (ElementTypeEnums.Picture.getInfo().equals(c1.getParentType()) && ElementTypeEnums.Picture.getInfo().equals(c2.getParentType())) {
                    return 0;
                } else if (ElementTypeEnums.Picture.getInfo().equals(c1.getParentType()) && !ElementTypeEnums.Picture.getInfo().equals(c2.getParentType())) {
                    // c1为pic  c2不为pic
                    return 1;
                } else {
                    // c1不为pic  c2为pic
                    return -1;
                }
            });
        }
    }

    @Override
    public void setDataList(Map<String, List<Object>> order) {
        clearData();
        if (order.get(TableNameConstants.IN_MOVIE_TABLE) == null) {
            return;
        }
        movieVoMap = order.get(TableNameConstants.IN_MOVIE_TABLE).stream().map(obj -> (InMovie) obj).collect(Collectors.toMap(InMovie::getCode, obj -> obj, (v1, v2) -> v2));
    }

    void clearData() {
        if (movieVoMap != null) {
            movieVoMap = null;
        }
    }

    @Override
    public void updateProcess(Object data, InOrder order) {
        // update操作针对图片和子集
        InMapping mapping = (InMapping) data;
        StatusUtils.Status status;
        if (ElementTypeEnums.Picture.getInfo().equals(mapping.getParentType())) {
            // 1 图片的type和sequence字段
            status = parentTypeIsPictureUpdateProcess(mapping);
        } else if (ElementTypeEnums.Series.getInfo().equals(mapping.getParentType())) {
            // 2 子集的sequence字段
            status = parentTypeIsSeriesUpdateProcess(mapping);
        } else if (ElementTypeEnums.Category.getInfo().equals(mapping.getParentType())) {
            status = parentTypeIsCategoryUpdateProcess(mapping);
        } else {
            status = StatusUtils.success();
        }
        updateStatus(order, mapping, status);

    }

    private StatusUtils.Status parentTypeIsCategoryUpdateProcess(InMapping mapping) {
        if (ElementTypeEnums.Channel.getInfo().equals(mapping.getElementType())) {
            // 栏目频道关系
            List<BmsCategoryChannel> categoryChannelList = bmsCategoryChannelService.listByCategoryCodeChannelCode(mapping.getParentCode(), mapping.getElementCode());
            for (BmsCategoryChannel categoryChannel : categoryChannelList) {
                if (PublishStatusEnums.checkUpdateStatus(categoryChannel.getPublishStatus())) {
                    log.warn("栏目关系发布状态不能更新, 更新结束");
                    return StatusUtils.fail("栏目关系发布状态不能更新");
                }
            }
            for (BmsCategoryChannel categoryChannel : categoryChannelList) {
                categoryChannel.setSequence(mapping.getSequence());
                // 若为发布成功 则改为待更新, 其他不变
                categoryChannel.setPublishStatus(PublishStatusEnums.getUpdatePublishStatus(categoryChannel.getPublishStatus()));
                bmsCategoryChannelService.updateById(categoryChannel);
            }

        } else {
            // 栏目内容关系
            List<BmsCategoryContent> categoryContentList = bmsCategoryContentService.listByCategoryCodeContentCode(mapping.getParentCode(), mapping.getElementCode());
            for (BmsCategoryContent categoryContent : categoryContentList) {
                if (PublishStatusEnums.checkUpdateStatus(categoryContent.getPublishStatus())) {
                    log.warn("栏目关系发布状态不能更新, 更新结束");
                    return StatusUtils.fail("栏目关系发布状态不能更新");
                }
            }
            for (BmsCategoryContent categoryContent : categoryContentList) {
                categoryContent.setSequence(mapping.getSequence());
                // 若为发布成功 则改为待更新, 其他不变
                categoryContent.setPublishStatus(PublishStatusEnums.getUpdatePublishStatus(categoryContent.getPublishStatus()));
                bmsCategoryContentService.updateById(categoryContent);
            }
        }
        return StatusUtils.success();
    }

    @Override
    public void registProcess(Object data, InOrder order) {
        InMapping mapping = (InMapping) data;
        StatusUtils.Status status;
        if (ElementTypeEnums.Picture.getInfo().equals(mapping.getParentType())) {
            status = parentTypeIsPictureRegistProcess(mapping);
        } else if (ElementTypeEnums.Program.getInfo().equals(mapping.getParentType())) {
            status = parentTypeIsProgramRegistProcess(mapping);
        } else if (ElementTypeEnums.Series.getInfo().equals(mapping.getParentType())) {
            status = parentTypeIsSeriesRegistProcess(mapping);
        } else if (ElementTypeEnums.Category.getInfo().equals(mapping.getParentType())) {
            status = parentTypeIsCategoryRegistProcess(mapping);
        } else if (ElementTypeEnums.Package.getInfo().equals(mapping.getParentType())) {
            status = parentTypeIsPackageRegistProcess(mapping);
        } else {
            status = StatusUtils.fail("新增失败, 类型错误");
            log.error("\"ParentType\" and \"ElementType\" 不匹配. mappingId:{}", mapping.getId());
        }
        updateStatus(order, mapping, status);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void deleteProcess(Object data, InOrder order) {
        InMapping mapping = (InMapping) data;
        StatusUtils.Status status;
        if (ElementTypeEnums.Picture.getInfo().equals(mapping.getParentType())) {
            status = parentTypeIsPictureDeleteProcess(mapping);
        } else if (ElementTypeEnums.Program.getInfo().equals(mapping.getParentType())) {
            status = parentTypeIsProgramDeleteProcess(mapping);
        } else if (ElementTypeEnums.Series.getInfo().equals(mapping.getParentType())) {
            status = parentTypeIsSeriesDeleteProcess(mapping);
        } else if (ElementTypeEnums.Category.getInfo().equals(mapping.getParentType())) {
            status = parentTypeIsCategoryDeleteProcess(mapping);
        } else if (ElementTypeEnums.Package.getInfo().equals(mapping.getParentType())) {
            status = parentTypeIsPackageDeleteProcess(mapping);
        } else {
            status = StatusUtils.fail("删除失败,ElementType类型不匹配");
            log.error("\"ParentType\" and \"ElementType\" 不匹配. mappingId:{}", mapping.getId());
        }
        updateStatus(order, mapping, status);

    }

    private StatusUtils.Status parentTypeIsPictureRegistProcess(InMapping mapping) {

        if (ElementTypeEnums.Channel.getInfo().equals(mapping.getElementType())){
            //增加校验图片cp对应的频道是否存在
            CmsChannel cmsChannelCheck = cmsChannelService.getByCodeCpid(mapping.getElementCode(), getSysCp().getId());
            if (cmsChannelCheck == null) {
                // 若频道不存在 则结束
                log.error("新增频道绑定图片时，其cp对应的频道不存在,CmsChannelCode:{}", mapping.getElementCode());
                return StatusUtils.fail("新增频道绑定图片时，其cp对应的频道不存在");
            }
        }

        // 判断Picture表是否存在数据
        List<CmsPicture> cmsPictureList = cmsPictureService.listByCode(mapping.getParentCode());
        if (cmsPictureList.size() == 0) {
            // cmsPicture对象不存在 处理失败 写状态
            log.error("cmsPicture对象不存在, code:{}", mapping.getParentCode());
            return StatusUtils.fail("关系新增失败,cmsPicture对象不存在");
        }
        // 映射是否存在
        boolean isMapping = false;
        CmsPicture cmsPicture = null;
        for (CmsPicture picture : cmsPictureList) {
            // 检查数据
            if (mapping.getElementCode().equals(picture.getContentCode())) {
                if(ElementTypeEnums.Category.getInfo().equals(mapping.getElementType())){

                }else{
                    // 若相等 则说明关系存在
                    isMapping = true;
                    break;
                }
            }
            if (StringUtils.isBlank(picture.getContentCode()) && StringUtils.isBlank(picture.getContentCode())) {
                // 找到那条content属性为空的数据
                cmsPicture = picture;
                break;
            }
        }
/*        // 判断关系是否存在
        if (isMapping) {
            // 忽略
            log.warn("图片关系已存在");
            return StatusUtils.success();
        }*/

        // 根据元素类型 查询ContentId 然后更新Picture表
        if (ElementTypeEnums.Channel.getInfo().equals(mapping.getElementType())) {
            //增加校验图片cp对应的频道是否存在
            CmsChannel cmsChannelCheck = cmsChannelService.getByCodeCpid(mapping.getElementCode(), getSysCp().getId());
            if (cmsChannelCheck == null) {
                // 若频道不存在 则结束
                log.error("新增节目单时,其父频道不存在,CmsChannelCode:{}", mapping.getElementCode());
                return StatusUtils.fail("新增节目单时,其父频道不存在,对应cp的cmsChannel对象不存在");
            }

            // 查询channel表的数据id
            CmsChannel cmsChannel = cmsChannelService.getByCode(mapping.getElementCode());
            if (cmsChannel == null) {
                log.error("cmsChannel对象不存在, code:{}", mapping.getElementCode());
                return StatusUtils.fail("关系新增失败,cmsChannel对象不存在");
            }
            if (isMapping) {
                // 忽略
                log.warn("图片关系已存在");
                cmsPicture = cmsPictureList.get(0);
            }else {
                if (cmsPicture != null) {
                    updateCmsPicture(cmsChannel.getId(), ContentTypeEnums.CHANNEL.getCode(), mapping, cmsPicture);
                } else {
                    // 若需要新增 则任取一条, 将id置空, 重置content属性  然后插入到cms表
                    cmsPicture = cmsPictureList.get(0);
                    insertCmsPicture(cmsPicture, ContentTypeEnums.CHANNEL.getCode(), mapping, cmsChannel.getId());
                }
            }
            List<BmsChannel> bmsChannelList = bmsChannelService.listByCmsContentId(cmsChannel.getId());
            // bms表新增数据
            for (BmsChannel bmsChannel : bmsChannelList) {
                // 这里是为了查询 bms_content_id
                insertBmsPicture(cmsPicture, bmsChannel.getId(), bmsChannel.getSpId(), ContentTypeEnums.CHANNEL.getCode(), mapping);
            }
            return StatusUtils.success();

        } else if (ElementTypeEnums.Series.getInfo().equals(mapping.getElementType())) {
            // 查Series表数据
            CmsSeries cmsSeries = cmsSeriesService.getByCode(mapping.getElementCode());
            ContentTypeEnums type;
            if (cmsSeries == null) {
                // cmsPicture对象不存在 处理失败 写状态
                log.error("cmsSeries对象不存在, code:{}", mapping.getElementCode());
                return StatusUtils.fail("关系新增失败,cmsSeries对象不存在");
            }
            if (SeriesTypeEnums.SERIAL.getCode().equals(cmsSeries.getSeriesType())) {
                type = ContentTypeEnums.SERIES_1;
            } else {
                type = ContentTypeEnums.SERIES_0;
            }
            if (isMapping) {
                // 忽略
                log.warn("图片关系已存在");
                cmsPicture = cmsPictureList.get(0);
            }else {
                if (cmsPicture != null) {
                    updateCmsPicture(cmsSeries.getId(), type.getCode(), mapping, cmsPicture);
                } else {
                    // 若需要新增 则任取一条, 将id置空, 重置content属性  然后插入到cms表
                    cmsPicture = cmsPictureList.get(0);
                    insertCmsPicture(cmsPicture, type.getCode(), mapping, cmsSeries.getId());
                }
            }
            List<BmsContent> bmsContentList = bmsContentService.listByCmsSeriesId(cmsSeries.getId());
            // bms表关系新增
            for (BmsContent bmsContent : bmsContentList) {
                insertBmsPicture(cmsPicture, bmsContent.getId(), bmsContent.getSpId(), type.getCode(), mapping);
            }
            return StatusUtils.success();

        } else if (ElementTypeEnums.Program.getInfo().equals(mapping.getElementType())) {
            // 查询Program表数据
            CmsProgram cmsProgram = cmsProgramService.getByCode(mapping.getElementCode());
            if (cmsProgram == null) {
                log.error("cmsProgram对象不存在, code:{}", mapping.getParentCode());
                return StatusUtils.fail("新增关系失败,cmsProgram对象不存在");
            }
            if (SeriesTypeEnums.SUB_SET.getCode().equals(cmsProgram.getSeriesFlag())) {
                if (isMapping) {
                    // 忽略
                    log.warn("图片关系已存在");
                    cmsPicture = cmsPictureList.get(0);
                }else {
                    if (cmsPicture != null) {
                        updateCmsPicture(cmsProgram.getId(), ContentTypeEnums.PROGRAM_1.getCode(), mapping, cmsPicture);
                    } else {
                        // 若需要新增 则任取一条, 将id置空, 重置content属性  然后插入到cms表
                        cmsPicture = cmsPictureList.get(0);
                        insertCmsPicture(cmsPicture, ContentTypeEnums.PROGRAM_1.getCode(), mapping, cmsProgram.getId());
                    }
                }
                List<BmsProgram> bmsProgramList = bmsProgramService.listByCmsContentCode(cmsProgram.getCode());
                // bms表关系新增 单集查bms_content 子集查bmsProgram
                for (BmsProgram bmsProgram : bmsProgramList) {
                    // 这里是为了查询 bms_Program_id
                    insertBmsPicture(cmsPicture, bmsProgram.getId(), bmsProgram.getSpId(), ContentTypeEnums.PROGRAM_1.getCode(), mapping);
                }

            } else {
                if (isMapping) {
                    // 忽略
                    log.warn("图片关系已存在");
                    cmsPicture = cmsPictureList.get(0);
                }else {
                    if (cmsPicture != null) {
                        updateCmsPicture(cmsProgram.getId(), ContentTypeEnums.PROGRAM_0.getCode(), mapping, cmsPicture);
                    } else {
                        // 若需要新增 则任取一条, 将id置空, 重置content属性  然后插入到cms表
                        cmsPicture = cmsPictureList.get(0);
                        insertCmsPicture(cmsPicture, ContentTypeEnums.PROGRAM_0.getCode(), mapping, cmsProgram.getId());
                    }
                }
                List<BmsContent> bmsContentList = bmsContentService.listByCmsProgramId(cmsProgram.getId());
                // bms表关系新增 单集查content 子集查bmsProgram
                for (BmsContent bmsContent : bmsContentList) {
                    // 这里是为了查询 bms_content_id
                    insertBmsPicture(cmsPicture, bmsContent.getId(), bmsContent.getSpId(), ContentTypeEnums.PROGRAM_0.getCode(), mapping);
                }
            }
            return StatusUtils.success();

        } else if (ElementTypeEnums.Category.getInfo().equals(mapping.getElementType())) {
            List<BmsCategory> bmsCategoryList = bmsCategoryService.listByCode(mapping.getElementCode());
            if (bmsCategoryList.size() == 0) {
                log.error("bmsCategory 对象不存在 code:{} ", mapping.getElementCode());
                return StatusUtils.fail("新增关系失败,bmsCategory对象不存在");
            }
            for (BmsCategory bmsCategory : bmsCategoryList) {
                if (isMapping) {
                    // 忽略
                    log.warn("图片关系已存在");
                    cmsPicture = cmsPictureList.get(0);
                }else {
                    if (cmsPicture != null && StringUtils.isBlank(cmsPicture.getContentCode()) &&
                            StringUtils.isBlank(cmsPicture.getContentCode())) {
                        // content属性为空 则不用新增 直接 填充content属性即可
                        updateCmsPicture(bmsCategory.getId(), ContentTypeEnums.CATEGORY.getCode(), mapping, cmsPicture);
                    } else {
                        // 若需要新增 则任取一条, 将id置空, 重置content属性  然后插入到cms表
                        cmsPicture = cmsPictureList.get(0);
                        insertCmsPicture(cmsPicture, ContentTypeEnums.CATEGORY.getCode(), mapping, bmsCategory.getId());
                    }
                }
                insertBmsPicture(cmsPicture, bmsCategory.getId(), bmsCategory.getSpId(), ContentTypeEnums.CATEGORY.getCode(), mapping);
            }
            return StatusUtils.success();

        } else if (ElementTypeEnums.Package.getInfo().equals(mapping.getElementType())) {
            List<BmsPackage> bmsPackageList = bmsPackageService.listByCode(mapping.getElementCode());
            if (bmsPackageList.size() == 0) {
                log.error("bmsPackage 对象不存在 code:{}", mapping.getElementCode());
                return StatusUtils.fail("新增关系失败,bmsPackage对象不存在");
            }
            for (BmsPackage bmsPackage : bmsPackageList) {
                if (isMapping) {
                    // 忽略
                    log.warn("图片关系已存在");
                    cmsPicture = cmsPictureList.get(0);
                }else {
                    if (cmsPicture != null && StringUtils.isBlank(cmsPicture.getContentCode()) &&
                            StringUtils.isBlank(cmsPicture.getContentCode())) {
                        // content属性为空 则不用新增 直接 填充content属性即可
                        updateCmsPicture(bmsPackage.getId(), ContentTypeEnums.PACKAGE.getCode(), mapping, cmsPicture);
                    } else {
                        // 若需要新增 则任取一条, 将id置空, 重置content属性  然后插入到cms表
                        cmsPicture = cmsPictureList.get(0);
                        insertCmsPicture(cmsPicture, ContentTypeEnums.PACKAGE.getCode(), mapping, bmsPackage.getId());
                    }
                }
                insertBmsPicture(cmsPicture, bmsPackage.getId(), bmsPackage.getSpId(), ContentTypeEnums.PACKAGE.getCode(), mapping);
            }
            return StatusUtils.success();
        } else {
            log.error("\"ParentType\" 和 \"ElementType\" 不匹配, mappingId:{}", mapping.getId());
            return StatusUtils.fail("新增失败,\"ParentType\"和\"ElementType\"不匹配");
        }
    }

    private StatusUtils.Status parentTypeIsPackageRegistProcess(InMapping mapping) {
        // 检查父对象是否存在
        List<BmsPackage> bmsPackageList = bmsPackageService.listByCode(mapping.getParentCode());
        if (bmsPackageList.size() == 0) {
            log.error("父对象 Package不存在 code:{}", mapping.getParentCode());
            return StatusUtils.fail("新增失败,父对象Package不存在");
        }
        for (BmsPackage bmsPackage : bmsPackageList) {
            if (ElementTypeEnums.Program.getInfo().equals(mapping.getElementType())
                    || ElementTypeEnums.Series.getInfo().equals(mapping.getElementType())) {
                // 在分类下增加节目
                // 查询需要关联的子对象
                BmsContent bmsContent = bmsContentService.getByCodeAndSpId(mapping.getElementCode(), bmsPackage.getSpId());
                if (bmsContent == null) {
                    log.warn("bmsContent不存在, code:{} spid:{}", mapping.getElementCode(), bmsPackage.getSpId());
                    continue;
                }
                // 检查关系是否已经存在
                BmsPackageContent bmsPackageContent = bmsPackageContentService.getByPackageCodeContentCodeSpId(mapping.getParentCode(), mapping.getElementCode(), bmsPackage.getSpId());
                if (bmsPackageContent != null) {
                    log.warn("关系已存在 parentType:{}, type:{}, parentCode:{}, code:{}", mapping.getParentType(), mapping.getElementType(), mapping.getParentCode(), mapping.getElementCode());
                    continue;
                }
                BmsContent bmsContentVo = new BmsContent();
                bmsContentVo.setPackageIds(StrUtils.mergeSet(bmsContent.getPackageIds(), String.valueOf(bmsPackage.getId())));
                bmsContentVo.setPackageNames(StrUtils.mergeSet(bmsContent.getPackageNames(), bmsPackage.getName()));
                bmsContentVo.setId(bmsContent.getId());
                bmsContentService.updateById(bmsContentVo);

                // 写到bms_category_content
                BmsPackageContentVo bmsPackageContentVo = new BmsPackageContentVo(bmsPackage, bmsContent);
                bmsPackageContentVo.setContentType(bmsContent.getContentType());
                if (InPassageTypeEnum.TOW.getCode().equals(sysInPassage.getSpPublishType())) {
                    // 只有明确的配置了 默认为发布成功 才设置
                    bmsPackageContentVo.setPublishStatus(PublishStatusEnums.PUBLISH.getCode());
                    bmsPackageContentVo.setPublishTime(DateUtils.dateTimeNow(DateUtils.YYYY_MM_DD_HH_MM_SS));
                    bmsPackageContentVo.setPublishDescription("系统配置,自动发布");
                }
                bmsPackageContentService.save(bmsPackageContentVo);

            } else {
                log.error("\"ParentType\" and \"ElementType\" 不匹配. mappingId:{}", mapping.getId());
                return StatusUtils.fail("新增失败,\"ParentType\"and\"ElementType\"不匹配");
            }
        }
        return StatusUtils.success();
    }

    private StatusUtils.Status parentTypeIsCategoryRegistProcess(InMapping mapping) {
        // 检查父对象是否存在
        List<BmsCategory> bmsCategoryList = bmsCategoryService.listByCode(mapping.getParentCode());
        if (bmsCategoryList.size() == 0) {
            log.error("父对象 Category不存在 code:{}", mapping.getParentCode());
            return StatusUtils.fail("新增关系失败,Category不存在");
        }
        for (BmsCategory bmsCategory : bmsCategoryList) {
            if (ElementTypeEnums.Channel.getInfo().equals(mapping.getElementType())) {
                // 在分类下增加频道
                // 查询需要关联的子对象 只能引入当前sp的内容
                BmsChannel bmsChannel = bmsChannelService.getByCmsChannelCodeAndSpId(mapping.getElementCode(), bmsCategory.getSpId());
                if (bmsChannel == null) {
                    log.warn("bmsChannel不存在code:{} spId:{}", mapping.getElementCode(), bmsCategory.getSpId());
                    continue;
                }
                // 检查关系是否已经存在
                BmsCategoryChannel bmsCategoryChannel = bmsCategoryChannelService.getByCategoryCodeChannelCodeSpId(mapping.getParentCode(), mapping.getElementCode(), bmsCategory.getSpId());
                if (bmsCategoryChannel != null) {
                    log.warn("关系已存在 parentType:{}, type:{}, parentCode:{}, code:{}", mapping.getParentType(), mapping.getElementType(), mapping.getParentCode(), mapping.getElementCode());
                    continue;
                }
                // 填入产品包id
                BmsChannel bmsChannelVo = new BmsChannel();
                bmsChannelVo.setCategoryIds(StrUtils.mergeSet(bmsChannel.getCategoryIds(), String.valueOf(bmsCategory.getId())));
                bmsChannelVo.setCategoryNames(StrUtils.mergeSet(bmsChannel.getCategoryNames(), bmsCategory.getName()));
                bmsChannelVo.setId(bmsChannel.getId());
                bmsChannelService.updateById(bmsChannelVo);

                // 写到bms_category_channel
                BmsCategoryChannelVo bmsCategoryChannelVo = new BmsCategoryChannelVo(bmsCategory, bmsChannel);
                bmsCategoryChannelVo.setSequence(bmsCategoryChannelService.getSequence(bmsCategory.getId(), mapping.getSequence()));
                bmsCategoryChannelVo.setContentType(ContentTypeEnums.CHANNEL.getCode());
                if (InPassageTypeEnum.TOW.getCode().equals(sysInPassage.getSpPublishType())) {
                    // 只有明确的配置了 默认为发布成功 才设置
                    bmsCategoryChannelVo.setPublishStatus(PublishStatusEnums.PUBLISH.getCode());
                    bmsCategoryChannelVo.setPublishTime(DateUtils.dateTimeNow(DateUtils.YYYY_MM_DD_HH_MM_SS));
                    bmsCategoryChannelVo.setPublishDescription("系统配置,自动发布");
                }
                bmsCategoryChannelService.save(bmsCategoryChannelVo);
                // 当前循环结束
                continue;

            } else if (ElementTypeEnums.Program.getInfo().equals(mapping.getElementType())
                    || ElementTypeEnums.Series.getInfo().equals(mapping.getElementType())) {
                // 在分类下增加节目
                // 查询需要关联的子对象
                BmsContent bmsContent = bmsContentService.getByCodeAndSpId(mapping.getElementCode(), bmsCategory.getSpId());
                if (bmsContent == null) {
                    log.warn("bmsContent不存在code:{} spId:{}", mapping.getElementCode(), bmsCategory.getSpId());
                    continue;
                }
                // 检查关系是否已经存在
                BmsCategoryContent bmsCategoryContent = bmsCategoryContentService.getByCategoryCodeContentCodeSpId(mapping.getParentCode(), mapping.getElementCode(), bmsCategory.getSpId());
                if (bmsCategoryContent != null) {
                    log.warn("关系已存在 parentType:{}, type:{}, parentCode:{}, code:{}", mapping.getParentType(), mapping.getElementType(), mapping.getParentCode(), mapping.getElementCode());
                    // 当前循环结束
                    continue;
                }
                BmsContent bmsContentVo = new BmsContent();
                bmsContentVo.setCategoryIds(StrUtils.mergeSet(bmsContent.getCategoryIds(), String.valueOf(bmsCategory.getId())));
                bmsContentVo.setCategoryNames(StrUtils.mergeSet(bmsContent.getCategoryNames(), String.valueOf(bmsCategory.getName())));
                bmsContentVo.setId(bmsContent.getId());
                bmsContentService.updateById(bmsContentVo);

                // 写到bms_category_content
                BmsCategoryContentVo bmsCategoryContentVo = new BmsCategoryContentVo(bmsCategory, bmsContent);
                bmsCategoryContentVo.setSequence(bmsCategoryContentService.getSequence(bmsCategory.getId(), mapping.getSequence()));
                bmsCategoryContentVo.setContentType(bmsContent.getContentType());
                if (InPassageTypeEnum.TOW.getCode().equals(sysInPassage.getSpPublishType())) {
                    // 只有明确的配置了 默认为发布成功 才设置
                    bmsCategoryContentVo.setPublishStatus(PublishStatusEnums.PUBLISH.getCode());
                    bmsCategoryContentVo.setPublishTime(DateUtils.dateTimeNow(DateUtils.YYYY_MM_DD_HH_MM_SS));
                    bmsCategoryContentVo.setPublishDescription("系统配置,自动发布");
                }
                bmsCategoryContentService.save(bmsCategoryContentVo);
                // 当前循环结束
                continue;
            } else {
                log.error("\"ParentType\" and \"ElementType\" 不匹配. mappingId:{}", mapping.getId());
                return StatusUtils.fail("新增失败,\"ParentType\"and\"ElementType\"不匹配");
            }
        }
        return StatusUtils.success();
    }

    private StatusUtils.Status parentTypeIsProgramRegistProcess(InMapping mapping) {
        if (ElementTypeEnums.Movie.getInfo().equals(mapping.getElementType())) {
            // 为Program分配Movie
            CmsProgram cmsProgram = cmsProgramService.getByCode(mapping.getParentCode());
            if (cmsProgram == null) {
                // cmsProgram对象不存在 处理失败 写状态
                log.error("cmsProgram对象不存在, code:{}", mapping.getParentCode());
                return StatusUtils.fail("新增关系失败,cmsProgram对象不存在");
            }
            InMovie movie;
            // 先看当前工单中是否存在视频code
            if (movieVoMap != null && movieVoMap.containsKey(mapping.getElementCode())) {
                movie = movieVoMap.get(mapping.getElementCode());
            } else {
                // 若当前没有 则去工单表中查movie信息 按时间排序 取最近一条
                LambdaQueryWrapper<InMovie> queryWrapper1 = Wrappers.lambdaQuery();
                queryWrapper1.eq(InMovie::getCode, mapping.getElementCode()).orderBy(true, false, InMovie::getCreateTime).last(TextConstants.LIMIT_ONE);
                movie = inMovieService.getOne(queryWrapper1);
            }
            if (movie != null && movie.getType() != null) {
                updateProgramMovie(movie.getType(), mapping, cmsProgram);
                // 这里不需要写bms表 因为bms表不关联movie
                return StatusUtils.success();
            } else {
                // movie不存在 关联失败
                log.error("movie对象不存在, code:{}", mapping.getElementCode());
                return StatusUtils.fail("新增失败,movie对象不存在");
            }
        } else {
            log.error("\"ParentType\" and \"ElementType\" 不匹配. mappingId:{}", mapping.getId());
            return StatusUtils.fail("新增关系失败,\"ParentType\"and\"ElementType\"不匹配");
        }
    }

    private StatusUtils.Status parentTypeIsSeriesRegistProcess(InMapping mapping) {
        if (ElementTypeEnums.Movie.getInfo().equals(mapping.getElementType())) {
            // 为连续剧剧头分配Movie
            CmsSeries cmsSeries = cmsSeriesService.getByCode(mapping.getParentCode());
            if (cmsSeries == null || LockStatusEnums.LOCKED.getCode().equals(cmsSeries.getLockStatus())) {
                // cmsSeries对象不存在 处理失败 写状态
                log.error("cmsSeries对象不存在, code:{}", mapping.getParentCode());
                return StatusUtils.fail("新增关系失败,cmsSeries对象不存在");
            }
            if (StringUtils.isNotEmpty(cmsSeries.getResourcePreviewCode())) {
                log.warn("cmsSeries对象已经存在预览片关系,关系新增忽略, code:{}", mapping.getParentCode());
                return StatusUtils.success();
            }
            // 填入视频code
            cmsSeriesService.updateResourceById(mapping.getElementCode(), cmsSeries.getId());
            // 同理,这里也不用写bms表  因为bms表不关联movie
            return StatusUtils.success();
        } else if (ElementTypeEnums.Program.getInfo().equals(mapping.getElementType())) {
            // 为连续剧添加单集
            CmsProgram cmsProgram = cmsProgramService.getByCode(mapping.getElementCode());
            if (cmsProgram == null || LockStatusEnums.LOCKED.getCode().equals(cmsProgram.getLockStatus())) {
                // cmsProgram对象不存在 或已锁定 处理失败 写状态
                log.error("cmsProgram对象不存在, code:{}", mapping.getElementCode());
                return StatusUtils.fail("新增关系失败,cmsProgram对象不存在");
            }
            // 查cmsSeries的id字段
            CmsSeries cmsSeries = cmsSeriesService.getByCode(mapping.getParentCode());
            if (cmsSeries == null) {
                // cmsSeries对象不存在 处理失败 写状态
                log.error("cmsSeries对象不存在, code:{}", mapping.getParentCode());
                return StatusUtils.fail("新增关系失败,cmsSeries对象不存在");
            }

            CmsProgram cmsProgramOld = new CmsProgram();
            BeanUtils.copyProperties(cmsProgram, cmsProgramOld);
            cmsProgram.setEpisodeIndex(mapping.getSequence());
            cmsProgram.setSeriesCode(mapping.getParentCode());
            cmsProgram.setSeriesName(cmsSeries.getName());
            cmsProgram.setSeriesId(cmsSeries.getId());
            //添加子集所属剧集是否违禁检测
            if (ObjectUtils.isNotEmpty(cmsSeries) && IsProhibitEnum.YES.getValue().equals(cmsSeries.getIsProhibit())
                    && ProhibitStatusEnum.YES.getValue().equals(cmsSeries.getProhibitStatus())) {
                cmsProgram.setIsProhibit(IsProhibitEnum.YES.getValue());
                cmsProgram.setProhibitStatus(ProhibitStatusEnum.YES.getValue());
                cmsProgram.setLockStatus(LockStatusEnums.LOCKED.getCode());
            }
            //1.判断要绑定的子集集数是否已存在
            List<CmsProgram> cmsPrograms = cmsProgramService.list(Wrappers.<CmsProgram>lambdaQuery()
                    .select(CmsProgram::getEpisodeIndex)
                    .eq(CmsProgram::getSeriesId,cmsSeries.getId())
                    .eq(CmsProgram::getEpisodeIndex, mapping.getSequence())
                    .ne(CmsProgram::getId,cmsProgram.getId()));
            if (mapping.getSequence() == null || mapping.getSequence() == 0 || mapping.getSequence() < 0){
                log.warn("子集绑定剧集失败，子集code:{},剧集code:{}",cmsProgram.getCode(),cmsSeries.getCode());
                return StatusUtils.fail("新增关系失败,绑定的子集集数为空或者0或者是负数");
            }
            if (ObjectUtil.isNotEmpty(cmsPrograms)){
                log.warn("子集绑定剧集失败，子集code:{},剧集code:{}",cmsProgram.getCode(),cmsSeries.getCode());
                return StatusUtils.fail("新增关系失败,绑定的子集集数在剧集中已存在");
            }
            //2.判断子集集数是否超过剧集总集数
            if ((!mapping.getParentCode().equals(cmsProgramOld.getSeriesCode()))
                    && cmsSeries.getVolumnCount() < cmsSeries.getVolumnUpdate() + 1) {
                log.warn("子集绑定剧集失败，子集code:{},剧集code:{}",cmsProgram.getCode(),cmsSeries.getCode());
                return StatusUtils.fail("新增关系失败,子集绑定数量大于剧集总集数");
            }
            if (cmsSeries.getVolumnCount() < mapping.getSequence()){
                log.warn("子集绑定剧集失败，子集code:{},剧集code:{}",cmsProgram.getCode(),cmsSeries.getCode());
                return StatusUtils.fail("新增关系失败,子集的序号大于剧集总集数");
            }
            cmsProgramService.updateById(cmsProgram);
            //修改统计报表中子集集数
            sendMQ(cmsProgramOld, cmsProgram);
            cmsSeries.setMissingDetection(1);
            cmsSeriesService.updateById(cmsSeries);

            // 子集入bms表
            List<BmsContent> bmsContents = bmsContentService.listByCmsSeriesId(cmsSeries.getId());
            for (BmsContent bmsContent : bmsContents) {
                BmsProgram bmsProgram = bmsProgramService.getByCodeAndSpId(cmsProgram.getCode(), bmsContent.getSpId());
                if (bmsProgram != null) {
                    log.warn("子集和剧头关系已经入库 cmsContentCode:{}, spId:{}", cmsProgram.getCode(), bmsContent.getSpId());
                    continue;
                }
                BmsProgramVo bmsProgramVo = new BmsProgramVo(cmsProgram);
                if (InPassageTypeEnum.TOW.getCode().equals(sysInPassage.getSpPublishType())) {
                    // 只有明确的配置了 默认为发布成功 才设置
                    bmsProgramVo.setPublishStatus(PublishStatusEnums.PUBLISH.getCode());
                    bmsProgramVo.setPublishTime(DateUtils.dateTimeNow(DateUtils.YYYY_MM_DD_HH_MM_SS));
                    bmsProgramVo.setPublishDescription("系统配置,自动发布");
                }
                bmsProgramVo.setStatus(StatusEnum.COME.getCode());
                bmsProgramVo.setSpId(bmsContent.getSpId());
                bmsProgramVo.setSpName(bmsContent.getSpName());
                bmsProgramVo.setContentProvider(bmsContent.getContentProvider());
                bmsProgramVo.setOutPassageNames(bmsContent.getOutPassageNames());
                bmsProgramVo.setOutPassageIds(bmsContent.getOutPassageIds());
                bmsProgramVo.setBmsSpChannelId(bmsContent.getBmsSpChannelId());
                bmsProgramVo.setBmsSpChannelName(bmsContent.getBmsSpChannelName());
                if (ObjectUtils.isNotEmpty(mapping.getSequence())){
                    bmsProgramVo.setEpisodeIndex(mapping.getSequence());
                }
                bmsProgramService.save(bmsProgramVo);
            }
            return StatusUtils.success();
        } else {
            log.error("\"ParentType\" and \"ElementType\" 不匹配. mappingId:{}", mapping.getId());
            return StatusUtils.fail("新增关系失败,ElementType类型不匹配");
        }
    }


    private void sendMQ(CmsProgram cmsProgramOld, CmsProgram cmsProgram) {
        log.error("cmsprogram.sendmq.cmsProgramOld={},cmsProgram={}", JSON.toJSONString(cmsProgramOld),
                JSON.toJSONString(cmsProgram));
        try {
            Integer episodeIndex = cmsProgram.getEpisodeIndex();
            if (null == episodeIndex) {
                return;
            }
            HashMap<String, Object> map = new HashMap<>();
            String cmsContentCode = cmsProgram.getCode();
            map.put("cmsContentCode", cmsContentCode);
            HashMap<String, Object> hashMap = new HashMap<>();
            //子集特殊处理集数
            if (!episodeIndex.equals(cmsProgramOld.getEpisodeIndex())) {
                map.put("episodeIndex", episodeIndex);
            }
            com.pukka.iptv.common.data.model.bms.BmsProgram bmsProgram = JSON.parseObject(JSON.toJSONString(map), com.pukka.iptv.common.data.model.bms.BmsProgram.class);
            hashMap.put(StatisticsMediaMQEnum.BMS_PROGRAM.getValue(), bmsProgram);
            log.error("mappingprocess.sendMQ.hashMap={}", JSON.toJSONString(hashMap));
            this.rabbitTemplate.convertAndSend(CmsContentRenameMQConfig.CMS_CONTENT_RENAME_EXCHANGE,
                    CmsContentRenameMQConfig.CMS_CONTENT_RENAME_ROUTING, hashMap);
        } catch (Exception e) {
            log.error("CmsProgramServiceImpl.sendMQ.cmsProgramOld={},cmsProgram={},Exception=",
                    JSON.toJSONString(cmsProgramOld), JSON.toJSONString(cmsProgram), e);
        }
    }

    private void updateCmsPicture(Long contentId, Integer contentType, InMapping mapping, CmsPicture cmsPicture) {
        CmsPicture cmsPictureVo = new CmsPicture();
        cmsPictureVo.setContentType(contentType);
        cmsPictureVo.setContentCode(mapping.getElementCode());
        cmsPictureVo.setContentId(contentId);
        cmsPictureVo.setSequence(mapping.getSequence());
        cmsPictureVo.setType(mapping.getType());
        // 更新到cmsPicture表
        cmsPictureVo.setId(cmsPicture.getId());
        cmsPictureService.updateById(cmsPictureVo);
    }

    private StatusUtils.Status parentTypeIsPictureDeleteProcess(InMapping mapping) {
        // 判断Picture表是否存在数据
        CmsPicture cmsPicture = cmsPictureService.getByCodeAndContentCode(mapping.getParentCode(), mapping.getElementCode());
        if (cmsPicture == null) {
            log.warn("cmsPicture对象不存在,无法删除关系, code:{}", mapping.getParentCode());
            return StatusUtils.success();
        }
        // 用CmsPictureCode去 BmsPicture表里查 可能多个sp 所有可能有多条数据
        List<BmsPicture> bmsPictureList = bmsPictureService.listByCmsPictureCodeAndContentCode(mapping.getParentCode(), mapping.getElementCode());
        for (BmsPicture bmsPicture : bmsPictureList) {
            if (!PublishStatusEnums.checkDeleteStatus(bmsPicture.getPublishStatus())) {
                log.error("删除失败,发布状态不是待发布和回收成功发布失败, code :{} spId:{}", bmsPicture.getCode(), bmsPicture.getSpId());
                return StatusUtils.fail("删除失败,发布状态不是待发布和回收成功发布失败");
            }
        }
        // 判断锁定状态 锁定不能删除关系
        if (ElementTypeEnums.Series.getInfo().equals(mapping.getElementType())) {
            CmsSeries cmsSeries = cmsSeriesService.getByCode(mapping.getElementCode());
            if (LockStatusEnums.LOCKED.getCode().equals(cmsSeries.getLockStatus())) {
                log.error("删除失败,关系的对象锁定中, cmsSeriesCode :{}", mapping.getElementCode());
                return StatusUtils.fail("删除关系失败,关系的对象锁定中");
            }

            List<BmsContent> bmsContentList = bmsContentService.listByCmsContentCode(mapping.getElementCode());
            for (BmsContent bmsContent : bmsContentList) {
                if (LockStatusEnums.LOCKED.getCode().equals(bmsContent.getLockStatus())) {
                    log.error("删除失败,关系的对象锁定中, bmsSeriesCode :{}", mapping.getElementCode());
                    return StatusUtils.fail("删除关系失败,关系的对象锁定中");
                }
            }

        } else if (ElementTypeEnums.Program.getInfo().equals(mapping.getElementType())) {
            CmsProgram cmsProgram = cmsProgramService.getByCode(mapping.getElementCode());
            if (LockStatusEnums.LOCKED.getCode().equals(cmsProgram.getLockStatus())) {
                log.error("删除失败,关系的对象锁定中, cmsSeriesCode :{}", mapping.getElementCode());
                return StatusUtils.fail("删除关系失败,关系的对象锁定中");
            }
            // 单集的话查cms表
            if (SeriesTypeEnums.SIMPLE_SET.getCode().equals(cmsProgram.getSeriesFlag())) {
                List<BmsContent> bmsContentList = bmsContentService.listByCmsContentCode(mapping.getElementCode());
                for (BmsContent bmsContent : bmsContentList) {
                    if (LockStatusEnums.LOCKED.getCode().equals(bmsContent.getLockStatus())) {
                        log.error("删除失败,关系的对象锁定中, bmsSeriesCode :{}", mapping.getElementCode());
                        return StatusUtils.fail("删除关系失败,关系的对象锁定中");
                    }
                }
            }

        } else if (ElementTypeEnums.Category.getInfo().equals(mapping.getElementType())) {
            List<BmsCategory> bmsCategoryList = bmsCategoryService.listByCode(mapping.getElementCode());
            for (BmsCategory bmsCategory : bmsCategoryList) {
                if (LockStatusEnums.LOCKED.getCode().equals(bmsCategory.getLockStatus())) {
                    log.error("删除失败,关系的对象锁定中, bmsSeriesCode :{}", mapping.getElementCode());
                    return StatusUtils.fail("删除关系失败,关系的对象锁定中");
                }
            }

        } else if (ElementTypeEnums.Package.getInfo().equals(mapping.getElementType())) {
            List<BmsPackage> bmsPackageList = bmsPackageService.listByCode(mapping.getElementCode());
            for (BmsPackage bmsPackage : bmsPackageList) {
                if (LockStatusEnums.LOCKED.getCode().equals(bmsPackage.getLockStatus())) {
                    log.error("删除失败,关系的对象锁定中, bmsSeriesCode :{}", mapping.getElementCode());
                    return StatusUtils.fail("删除关系失败,关系的对象锁定中");
                }
            }
        } else if (ElementTypeEnums.Channel.getInfo().equals(mapping.getElementType())) {
            // Channel 不用判断锁定状态
            //增加校验图片cp对应的频道是否存在
            CmsChannel cmsChannelCheck = cmsChannelService.getByCodeCpid(mapping.getElementCode(), getSysCp().getId());
            if (cmsChannelCheck == null) {
                // 若频道不存在 则结束
                log.error("删除图片频道关系,其cp对应的频道不存在,CmsChannelCode:{}", mapping.getElementCode());
                return StatusUtils.fail("删除图片频道关系,其cp对应的频道不存在");
            }
        } else {
            log.error("删除关系失败,关系的类型不匹配,  parentType:{} ElementType :{}, mappingId:{}", mapping.getParentType(), mapping.getElementType(), mapping.getId());
            return StatusUtils.fail("删除关系失败,关系的类型不匹配");
        }
        cmsPictureService.removeMappingByCodeAndContentCode(mapping.getParentCode(), mapping.getElementCode());
       // bmsPictureService.removeByCmsPictureCodeAndContentCode(mapping.getParentCode(), mapping.getElementCode());
        return StatusUtils.success();
    }

    private StatusUtils.Status parentTypeIsPackageDeleteProcess(InMapping mapping) {

        List<BmsPackage> bmsPackageList = bmsPackageService.listByCode(mapping.getParentCode());
        if (bmsPackageList.size() == 0) {
            log.warn("删除关系结束, 产品包不存在 code:{}", mapping.getParentCode());
            return StatusUtils.success();
        }
        // 判断包的锁定状态
        for (BmsPackage bmsPackage : bmsPackageList) {
            if (LockStatusEnums.LOCKED.getCode().equals(bmsPackage.getLockStatus())) {
                log.error("删除失败,关系的对象锁定中, bmsPackageCode :{}", mapping.getParentCode());
                return StatusUtils.fail("删除关系失败,关系的对象锁定中");
            }
        }
        // 关系是否发布
        List<BmsPackageContent> bmsPackageContentList = bmsPackageContentService.listByPackageCodeContentCode(mapping.getParentCode(), mapping.getElementCode());
        if (bmsPackageContentList.size() == 0) {
            log.warn("关系不存在,不用删除,ParentCode:{}, ElementCode{}", mapping.getParentCode(), mapping.getElementCode());
            return StatusUtils.success();
        }
        for (BmsPackageContent bmsPackageContent : bmsPackageContentList) {
            if (!PublishStatusEnums.checkDeleteStatus(bmsPackageContent.getPublishStatus())) {
                log.error("删除失败,package发布状态不是待发布和回收成功发布失败, packageCode :{} spId:{}", bmsPackageContent.getPackageCode(), bmsPackageContent.getSpId());
                return StatusUtils.fail("删除失败,package发布状态不是待发布和回收成功发布失败");
            }
        }

        List<Long> idList = bmsPackageContentList.stream().map(BmsPackageContent::getBmsContentId).collect(Collectors.toList());
        // 拿到需要操作的对象
        List<BmsContent> bmsContentList = bmsContentService.listByIds(idList);
        // 判断内容的锁定状态
        for (BmsContent bmsContent : bmsContentList) {
            if (LockStatusEnums.LOCKED.getCode().equals(bmsContent.getLockStatus())) {
                log.error("删除失败,关系的对象锁定中, bmsSeriesCode :{}", mapping.getElementCode());
                return StatusUtils.fail("删除失败,关系的对象锁定中");
            }
        }
        // 移除bmsContentList中的所有的记录的包id
        bmsContentService.removeMappingPackageByIds(bmsContentList, bmsPackageList);
        // 删除关系表中的关系
        bmsPackageContentService.removeByPackageCodeContentCode(mapping.getParentCode(), mapping.getElementCode());
        return StatusUtils.success();
    }

    private StatusUtils.Status parentTypeIsCategoryDeleteProcess(InMapping mapping) {
        List<BmsCategory> bmsCategoryList = bmsCategoryService.listByCode(mapping.getParentCode());
        if (bmsCategoryList.size() == 0) {
            log.warn("删除结束,栏目不存在 code:{}", mapping.getParentCode());
            return StatusUtils.success();
        }
        // 判断包的锁定状态
        for (BmsCategory bmsCategory : bmsCategoryList) {
            if (LockStatusEnums.LOCKED.getCode().equals(bmsCategory.getLockStatus())) {
                log.error("删除失败,关系的对象锁定中, bmsCategoryCode :{}", mapping.getParentCode());
                return StatusUtils.fail("删除关系失败,bmsCategory锁定中");
            }
        }

        if (ElementTypeEnums.Channel.getInfo().equals(mapping.getElementType())) {
            // 拿到所有的关系
            List<BmsCategoryChannel> bmsCategoryChannelList = bmsCategoryChannelService.listByCategoryCodeChannelCode(mapping.getParentCode(), mapping.getElementCode());
            if (bmsCategoryChannelList.size() == 0) {
                log.warn("关系不存在,不用删除,ParentCode:{}, ElementCode{}", mapping.getParentCode(), mapping.getElementCode());
                return StatusUtils.success();
            }
            // 检查发布状态
            for (BmsCategoryChannel bmsCategoryChannel : bmsCategoryChannelList) {
                if (!PublishStatusEnums.checkDeleteStatus(bmsCategoryChannel.getPublishStatus())) {
                    log.error("删除失败,发布状态不是待发布和回收成功发布失败, CategoryCode :{} channelCode:{} spId:{}", bmsCategoryChannel.getCategoryCode(), bmsCategoryChannel.getCmsChannelCode(), bmsCategoryChannel.getSpId());
                    return StatusUtils.fail("删除失败,bmsCategory发布状态不是待发布和回收成功发布失败");
                }
            }
            // 拿到需要操作的bmsChannelId
            List<Long> idList = bmsCategoryChannelList.stream().map(BmsCategoryChannel::getBmsChannelId).collect(Collectors.toList());
            // 根据id拿到对象
            List<BmsChannel> bmsChannelList = bmsChannelService.listByIds(idList);
            // 移除这些对象的栏目属性
            bmsChannelService.removeMappingCategoryByIds(bmsChannelList, bmsCategoryList);
            // 删除这些关系
            bmsCategoryChannelService.removeByCategoryCodeContentCode(mapping.getParentCode(), mapping.getElementCode());
            return StatusUtils.success();

        } else if (ElementTypeEnums.Program.getInfo().equals(mapping.getElementType()) ||
                ElementTypeEnums.Series.getInfo().equals(mapping.getElementType())) {
            // 拿到这些关系
            List<BmsCategoryContent> bmsCategoryContentList = bmsCategoryContentService.listByCategoryCodeContentCode(mapping.getParentCode(), mapping.getElementCode());
            if (bmsCategoryContentList.size() == 0) {
                log.warn("关系不存在,不用删除,ParentCode:{}, ElementCode{}", mapping.getParentCode(), mapping.getElementCode());
                return StatusUtils.success();
            }
            for (BmsCategoryContent bmsCategoryContent : bmsCategoryContentList) {
                // 判断关系的状态
                if (!PublishStatusEnums.checkDeleteStatus(bmsCategoryContent.getPublishStatus())) {
                    log.error("删除失败,发布状态不是待发布和回收成功发布失败, CategoryCode :{} spId:{}", bmsCategoryContent.getCategoryCode(), bmsCategoryContent.getSpId());
                    return StatusUtils.fail("删除失败,发布状态不是允许删除的状态");
                }
            }
            // 拿到需要操作的bmsChannelId
            List<Long> idList = bmsCategoryContentList.stream().map(BmsCategoryContent::getBmsContentId).collect(Collectors.toList());
            // 根据id拿到对象
            List<BmsContent> bmsContentList = bmsContentService.listByIds(idList);

            for (BmsContent bmsContent : bmsContentList) {
                if (LockStatusEnums.LOCKED.getCode().equals(bmsContent.getLockStatus())) {
                    log.error("删除失败,关系的对象锁定中, bmsContentCode :{}", mapping.getElementCode());
                    return StatusUtils.fail("删除关系失败,bmsContent锁定中");
                }
            }
            // 移除栏目id
            bmsContentService.removeMappingCategoryByIds(bmsContentList, bmsCategoryList);
            // 删除关系表中的关系
            bmsCategoryContentService.removeByCategoryCodeContentCode(mapping.getParentCode(), mapping.getElementCode());
            return StatusUtils.success();

        } else {
            log.error("删除失败,关系的类型不匹配,  parentType:{} ElementType :{}, mappingId:{}", mapping.getParentType(), mapping.getElementType(), mapping.getId());
            return StatusUtils.fail("删除关系失败,关系的类型不匹配");
        }
    }

    private StatusUtils.Status parentTypeIsSeriesDeleteProcess(InMapping mapping) {
        if (ElementTypeEnums.Movie.getInfo().equals(mapping.getElementType())) {
            CmsSeries cmsSeries = cmsSeriesService.getByCodePreviewCode(mapping.getParentCode(), mapping.getElementCode());
            if (cmsSeries == null) {
                log.warn("关系不存在,不用删除,ParentCode:{}, ElementCode{}", mapping.getParentCode(), mapping.getElementCode());
                return StatusUtils.success();
            }
            if (LockStatusEnums.LOCKED.getCode().equals(cmsSeries.getLockStatus())) {
                log.error("删除失败,关系的对象锁定中, cmsSeriesCode :{}", mapping.getElementCode());
                return StatusUtils.fail("删除关系失败,cmsSeries锁定中");
            }

            List<BmsContent> bmsContentList = bmsContentService.listByCmsContentCode(cmsSeries.getCode());
            for (BmsContent bmsContent : bmsContentList) {
                // 不是待发布 并且 不是回收成功 则不能删除
                if (!PublishStatusEnums.checkDeleteStatus(bmsContent.getPublishStatus())) {
                    log.error("删除失败,发布状态不是待发布和回收成功或发布失败, bmsContent :{} spId:{}", bmsContent.getCode(), bmsContent.getSpId());
                    return StatusUtils.fail("删除失败,发布状态不是待发布和回收成功或发布失败");
                }
                // 锁定不能删除
                if (LockStatusEnums.LOCKED.getCode().equals(bmsContent.getLockStatus())) {
                    log.error("删除失败,栏目已锁定, bmsContent :{} spId:{}", bmsContent.getCode(), bmsContent.getSpId());
                    return StatusUtils.fail("删除失败,栏目已锁定");
                }
            }
            cmsSeriesService.removeMappingMovieByCodePreviewCode(mapping.getParentCode(), mapping.getElementCode());
            // 操作cms_movie表
            cmsMovieService.removeMappingByContentCode(mapping.getParentCode(), mapping.getElementCode());
            // 操作cms_resource表
            cmsResourceService.removeMappingByContentCode(mapping.getParentCode(), mapping.getElementCode());
            return StatusUtils.success();

        } else if (ElementTypeEnums.Program.getInfo().equals(mapping.getElementType())) {

            CmsProgram cmsProgram = cmsProgramService.getByCodeSeriesCode(mapping.getParentType(), mapping.getElementCode());
            if (cmsProgram == null) {
                log.warn("删除的对象不存在. cmsProgramCode:{}, seriesCode:{}", mapping.getElementCode(), mapping.getParentType());
                return StatusUtils.success();
            }
            if (LockStatusEnums.LOCKED.getCode().equals(cmsProgram.getLockStatus())) {
                log.error("删除失败,关系的对象锁定中, cmsProgramCode :{}", mapping.getElementCode());
                return StatusUtils.fail("删除关系失败,cmsProgram锁定中");
            }

            List<BmsProgram> bmsProgramList = bmsProgramService.listByProgramCodeSeriesCode(mapping.getParentType(), mapping.getElementCode());
            for (BmsProgram bmsProgram : bmsProgramList) {
                if (!PublishStatusEnums.checkDeleteStatus(bmsProgram.getPublishStatus())) {
                    log.error("删除失败,发布状态不是待发布和回收成功,cmsProgramCode:{}, seriesCode:{}", mapping.getElementCode(), mapping.getParentType());
                    return StatusUtils.fail("删除关系失败,发布状态不是待发布和回收成功");
                }
            }
            // 删除bms_Program表和csm_Program表的关系
            cmsProgramService.deleteMappingSeriesByCode(mapping.getParentType(), mapping.getElementCode());
            bmsProgramService.deleteMappingSeriesByCode(mapping.getParentType(), mapping.getElementCode());
            return StatusUtils.success();

        } else {
            log.error("删除失败,关系的类型不匹配,  parentType:{} ElementType :{}, mappingId:{}", mapping.getParentType(), mapping.getElementType(), mapping.getId());
            return StatusUtils.fail("删除失败,关系的类型不匹配");
        }
    }

    private StatusUtils.Status parentTypeIsProgramDeleteProcess(InMapping mapping) {
        // 先检查关系是否存在
        CmsProgram cmsProgram = cmsProgramService.getByCode(mapping.getParentCode());
        if (cmsProgram == null) {
            log.warn("删除的关系不存在. cmsProgramCode:{}, movieCode:{}", mapping.getElementCode(), mapping.getParentType());
            return StatusUtils.success();
        }
        int flag = -1;
        if (mapping.getElementCode().equals(cmsProgram.getResourcePreviewCode())) {
            // 预览片
            flag = 0;
        } else if (mapping.getElementCode().equals(cmsProgram.getResourceReleaseCode())) {
            // 正片关系解除
            flag = 1;
        } else {
            log.warn("删除的关系不存在. cmsProgramCode:{}, movieCode:{}", mapping.getElementCode(), mapping.getParentType());
            return StatusUtils.success();
        }
        if (LockStatusEnums.LOCKED.getCode().equals(cmsProgram.getLockStatus())) {
            log.error("删除失败,关系的对象锁定中, bmsChannelCode :{}", mapping.getElementCode());
            return StatusUtils.fail("删除关系失败,关系的对象锁定中");
        }

        List<BmsProgram> bmsProgramList = bmsProgramService.listByCmsContentCode(mapping.getParentCode());
        for (BmsProgram bmsProgram : bmsProgramList) {
            // 不是待发布 并且 不是回收成功 则不能删除
            if (!PublishStatusEnums.checkDeleteStatus(bmsProgram.getPublishStatus())) {
                log.error("删除失败,发布状态不是待发布和回收成功发布失败, bmsProgramCode :{} spId:{}", bmsProgram.getCode(), bmsProgram.getSpId());
                return StatusUtils.fail("删除关系失败,发布状态不是待发布和回收成功发布失败");
            }
        }
        cmsProgramService.removeMovieMappingByCode(flag, cmsProgram);
        // 操作cms_movie表
        cmsMovieService.removeMappingByContentCode(mapping.getParentCode(), mapping.getElementCode());
        // 操作cms_resource表
        cmsResourceService.removeMappingByContentCode(mapping.getParentCode(), mapping.getElementCode());
        return StatusUtils.success();

    }

    private StatusUtils.Status parentTypeIsPictureUpdateProcess(InMapping mapping) {

        if (ElementTypeEnums.Channel.getInfo().equals(mapping.getElementType())) {
            //增加校验图片cp对应的频道是否存在
            CmsChannel cmsChannelCheck = cmsChannelService.getByCodeCpid(mapping.getElementCode(), getSysCp().getId());
            if (cmsChannelCheck == null) {
                // 若频道不存在 则结束
                log.error("修改频道图片关系时,其cp对应的频道不存在,CmsChannelCode:{}", mapping.getElementCode());
                return StatusUtils.fail("修改频道图片关系时,其cp对应的频道不存在");
            }
        }
        CmsPicture cmsPicture = cmsPictureService.getByCodeAndContentCode(mapping.getParentCode(), mapping.getElementCode());
        if (cmsPicture == null) {
            log.warn("图片关系不存在, 更新结束");
            return StatusUtils.success();
        }
        List<BmsPicture> bmsPictureList = bmsPictureService.listByCmsPictureId(cmsPicture.getId());
        for (BmsPicture bmsPicture : bmsPictureList) {
            if (PublishStatusEnums.checkUpdateStatus(bmsPicture.getPublishStatus())) {
                log.warn("图片发布状态不能更新, 更新结束");
                return StatusUtils.fail("图片发布状态不能更新");
            }
        }

        CmsPicture pic = new CmsPicture();
        pic.setId(cmsPicture.getId());
        pic.setSequence(mapping.getSequence());
        pic.setType(mapping.getType());
        cmsPictureService.updateById(pic);

        // 修改bms表属性
        for (BmsPicture bmsPicture : bmsPictureList) {
            bmsPicture.setSequence(mapping.getSequence());
            bmsPicture.setType(mapping.getType());
            // 若为发布成功 则改为待更新, 其他不变
            bmsPicture.setPublishStatus(PublishStatusEnums.getUpdatePublishStatus(bmsPicture.getPublishStatus()));
            bmsPictureService.updateById(bmsPicture);
        }
        return StatusUtils.success();
    }

    /**
     * 关系发布，当父类型是剧集时更新剧集子集集数信息
     * @param mapping
     * @return
     */
    private StatusUtils.Status parentTypeIsSeriesUpdateProcess(InMapping mapping) {
        CmsProgram cmsProgram = cmsProgramService.getByCodeSeriesCode(mapping.getParentCode(), mapping.getElementCode());
        if (cmsProgram == null) {
            log.error("子集关系表更新失败,关系不存在: data:{}", mapping.toString());
            return StatusUtils.fail("子集关系更新失败");
        }
        CmsSeries cmsSeries = cmsSeriesService.getByCode(mapping.getParentCode());
        if (cmsSeries == null) {
            // cmsSeries对象不存在 处理失败 写状态
            log.error("cmsSeries对象不存在, code:{}", mapping.getParentCode());
            return StatusUtils.fail("修改关系失败,cmsSeries对象不存在");
        }
        //1.判断要绑定的子集集数是否已存在
        List<CmsProgram> cmsPrograms = cmsProgramService.list(Wrappers.<CmsProgram>lambdaQuery()
                .select(CmsProgram::getEpisodeIndex)
                .eq(CmsProgram::getSeriesId,cmsSeries.getId())
                .eq(CmsProgram::getEpisodeIndex, mapping.getSequence())
                .ne(CmsProgram::getId,cmsProgram.getId()));
        if (mapping.getSequence() == null || mapping.getSequence() == 0 || mapping.getSequence() < 0){
            log.warn("子集绑定剧集失败，子集code:{},剧集code:{}",cmsProgram.getCode(),cmsSeries.getCode());
            return StatusUtils.fail("新增关系失败,绑定的子集集数为空或者0或者是负数");
        }
        if (ObjectUtil.isNotEmpty(cmsPrograms)){
            log.warn("子集绑定剧集失败，子集code:{},剧集code:{}",cmsProgram.getCode(),cmsSeries.getCode());
            return StatusUtils.fail("修改关系失败,修改的子集集数在剧集中已存在");
        }
        if (cmsSeries.getVolumnCount() < mapping.getSequence()){
            log.warn("子集绑定剧集失败，子集code:{},剧集code:{}",cmsProgram.getCode(),cmsSeries.getCode());
            return StatusUtils.fail("修改关系失败,子集的序号大于剧集总集数");
        }
        CmsProgramDto cmsProgramDto = new CmsProgramDto();
        cmsProgramDto.setEpisodeIndex(mapping.getSequence());
        cmsProgramDto.setId(cmsProgram.getId());
        cmsProgramDto.setCpId(cmsProgram.getCpId());
        cmsProgramDto.setRequestResource(SourceEnum.SYSWORK.getValue());
        //不使用feign,暂时不修改
        CommonResponse commonResponse = cmsProgramFeignClient.subsetUpdate(cmsProgramDto);
        if (TextConstants.OK.equals(commonResponse.getCode())) {
            log.info("子集关系更新成功");
        } else {
            log.error("子集关系更新失败, fein接口调用失败. data:{}", commonResponse);
            return StatusUtils.fail("子集关系更新失败");
        }
        return StatusUtils.success();
    }

    /**
     * 为bmsPicture添加关系
     *
     * @param cmsPicture
     * @param bmsContentId
     * @param contentType
     */
    private void insertBmsPicture(CmsPicture cmsPicture, Long bmsContentId, Long spId, int contentType, InMapping inMapping) {
        BmsPicture bmsPicture = bmsPictureService.getByCmsPicCodeContentCodeSpId(cmsPicture.getCode(), inMapping.getElementCode(), spId);
        if (bmsPicture != null) {
            log.warn("关系入库失败,bms表图片已经存在关系, picCode :{}, spid:{}", cmsPicture.getCode(), spId);
            return;
        }
        BmsPictureVo bmsPictureVo = new BmsPictureVo(cmsPicture);
        SysSp sysSp = redisService.getCacheMapValue(RedisKeyConstants.SYS_SP, String.valueOf(spId));
        if (sysSp == null) {
            log.warn("bms表关系入库失败 sysSp不存在 spId:{}", spId);
            return;
        }
        if (InPassageTypeEnum.TOW.getCode().equals(sysInPassage.getSpPublishType())) {
            // 只有明确的配置了 默认为发布成功 才设置
            bmsPictureVo.setPublishStatus(PublishStatusEnums.PUBLISH.getCode());
            bmsPictureVo.setPublishTime(DateUtils.dateTimeNow(DateUtils.YYYY_MM_DD_HH_MM_SS));
            bmsPictureVo.setPublishDescription("系统配置,自动发布");
        }
        bmsPictureVo.setContentType(contentType);
        bmsPictureVo.setContentCode(inMapping.getElementCode());
        bmsPictureVo.setSequence(inMapping.getSequence());
        bmsPictureVo.setType(inMapping.getType());
        bmsPictureVo.setBmsContentId(bmsContentId);
        bmsPictureVo.setCmsPictureCode(cmsPicture.getCode());
        bmsPictureVo.setCmsPictureId(cmsPicture.getId());
        bmsPictureVo.setSpName(sysSp.getName());
        bmsPictureVo.setSpId(sysSp.getId());
        bmsPictureVo.setOutPassageNames(sysSp.getOutPassageNames());
        bmsPictureVo.setOutPassageIds(sysSp.getOutPassageIds());
        bmsPictureService.save(bmsPictureVo);
    }

    private void updateProgramMovie(Integer movieType, InMapping mapping, CmsProgram cmsProgram) {
        LambdaUpdateWrapper<CmsProgram> updateWrapper = Wrappers.lambdaUpdate();
        if (MovieTypeEnums.RELEASE.getCode().equals(movieType)) {
            // 写到正片code
            if (StringUtils.isNotEmpty(cmsProgram.getResourceReleaseCode())) {
                log.warn("cmsProgram对象已经存在正片关系,关系新增忽略, code:{}", mapping.getParentCode());
                return;
            }
            updateWrapper.set(CmsProgram::getResourceReleaseCode, mapping.getElementCode());
            updateWrapper.set(CmsProgram::getReleaseStatus, ContentStatusEnums.AWAIT_RELEVANCY.getCode());
        } else {
            // 写到预览片code字段
            if (StringUtils.isNotEmpty(cmsProgram.getResourcePreviewCode())) {
                log.warn("cmsProgram对象已经存在预览片关系,关系新增忽略, code:{}", mapping.getParentCode());
                return;
            }
            updateWrapper.set(CmsProgram::getResourcePreviewCode, mapping.getElementCode());
            updateWrapper.set(CmsProgram::getPreviewStatus, ContentStatusEnums.AWAIT_RELEVANCY.getCode());
        }
        updateWrapper.eq(CmsProgram::getId, cmsProgram.getId());
        // 更新到cmsProgram表
        cmsProgramService.update(updateWrapper);
        // 修改cms_download的所属媒字段
        CmsDownload cmsDownload = cmsDownloadService.getByCode(mapping.getElementCode());
        if (cmsDownload != null) {
            cmsDownload.setContentName(cmsProgram.getName());
            cmsDownloadService.updateById(cmsDownload);
        }
    }

    /**
     * 更新工单表状态 多表更新 事务操作
     *
     * @param inMapping 关系工单
     * @param status    处理状态
     */
    public void updateStatus(InOrder order, InMapping inMapping, StatusUtils.Status status) {
        inMappingService.updateStatus(inMapping.getId(), status);
        inOrderMappingsService.updateStatus(order.getCorrelateId(), TableNameConstants.IN_MAPPING_TABLE, inMapping.getId(), status);
    }

    @Override
    public void updateStatus(InOrder order, Object data, StatusUtils.Status status) {
        InMapping inMapping = (InMapping) data;
        // 子工单对象直接通过id更新状态
        updateStatus(order, inMapping, status);
    }

    @Override
    public boolean deleteProcessIsExist(Object data, InOrder order) {
        InMapping mapping = (InMapping) data;
        if(mapping.getElementType().equals(ElementTypeEnums.Channel.getInfo())){
            CmsChannel cmsChannelCheck = cmsChannelService.getByCodeCpid(mapping.getElementCode(), getSysCp().getId());
            if (cmsChannelCheck == null) {
                // 若频道不存在 则结束
                log.error("删除频道图片关系时,其cp对应的频道不存在,CmsChannelCode:{}", mapping.getElementCode());
                return false;
            }
        }
        return true;
    }

    public void insertCmsPicture(CmsPicture cmsPicture, int type, InMapping mapping, Long contentId) {
        cmsPicture.setId(null);
        cmsPicture.setContentType(type);
        cmsPicture.setType(mapping.getType());
        cmsPicture.setContentCode(mapping.getElementCode());
        cmsPicture.setContentId(contentId);
        cmsPicture.setSequence(mapping.getSequence());
        cmsPicture.setCreateTime(null);
        cmsPicture.setUpdateTime(null);
        cmsPictureService.save(cmsPicture);
    }

}
