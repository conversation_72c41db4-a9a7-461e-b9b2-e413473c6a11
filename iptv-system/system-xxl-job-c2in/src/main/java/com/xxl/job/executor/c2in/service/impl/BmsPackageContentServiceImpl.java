package com.xxl.job.executor.c2in.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.xxl.job.executor.c2in.common.constant.TextConstants;
import com.xxl.job.executor.c2in.mapper.BmsPackageContentMapper;
import com.xxl.job.executor.c2in.model.BmsPackage;
import com.xxl.job.executor.c2in.model.BmsPackageContent;
import com.xxl.job.executor.c2in.service.BmsPackageContentService;
import org.springframework.stereotype.Service;

import java.util.List;


/**
 * @author: liuli
 * @date: 2021年9月2日 下午3:35:28
 */

@Service
public class BmsPackageContentServiceImpl extends ServiceImpl<BmsPackageContentMapper, BmsPackageContent> implements BmsPackageContentService {

    @Override
    public Long countByPackageCode(String packageCode) {
        LambdaQueryWrapper<BmsPackageContent> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.eq(BmsPackageContent::getPackageCode, packageCode);
        return this.count(queryWrapper);
    }

    @Override
    public Long countByCmsContentCode(String code) {
        LambdaQueryWrapper<BmsPackageContent> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.eq(BmsPackageContent::getCmsContentCode, code);
        return this.count(queryWrapper);
    }

    @Override
    public BmsPackageContent getByPackageCodeContentCodeSpId(String parentCode, String elementCode, Long spId) {
        LambdaQueryWrapper<BmsPackageContent> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.eq(BmsPackageContent::getPackageCode, parentCode)
                .eq(BmsPackageContent::getCmsContentCode, elementCode)
                .eq(BmsPackageContent::getSpId, spId)
                .last(TextConstants.LIMIT_ONE);
        return getOne(queryWrapper);
    }

    @Override
    public void removeByPackageCodeContentCode(String parentCode, String elementCode) {
        LambdaQueryWrapper<BmsPackageContent> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.eq(BmsPackageContent::getPackageCode, parentCode);
        queryWrapper.eq(BmsPackageContent::getCmsContentCode, elementCode);
        this.remove(queryWrapper);
    }

    @Override
    public List<BmsPackageContent> listByPackageCode(String packageCode) {
        LambdaQueryWrapper<BmsPackageContent> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.eq(BmsPackageContent::getPackageCode, packageCode);
        return this.list(queryWrapper);
    }

    @Override
    public void removeByPackageCode(String packageCode) {
        LambdaQueryWrapper<BmsPackageContent> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.eq(BmsPackageContent::getPackageCode, packageCode);
        this.remove(queryWrapper);
    }

    @Override
    public void removebyContentCode(String contentCode) {
        LambdaQueryWrapper<BmsPackageContent> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.eq(BmsPackageContent::getCmsContentCode, contentCode);
        this.remove(queryWrapper);
    }

    @Override
    public List<BmsPackageContent> listByPackageCodeContentCode(String packageCode, String contentCode) {
        LambdaQueryWrapper<BmsPackageContent> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.eq(BmsPackageContent::getPackageCode, packageCode)
                .eq(BmsPackageContent::getCmsContentCode, contentCode);
        return list(queryWrapper);
    }
}


