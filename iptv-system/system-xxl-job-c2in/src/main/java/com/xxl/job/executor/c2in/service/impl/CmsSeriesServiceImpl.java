package com.xxl.job.executor.c2in.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.pukka.iptv.common.data.model.cms.CmsSeries;
import com.xxl.job.executor.c2in.common.constant.TextConstants;
import com.xxl.job.executor.c2in.common.enums.ContentStatusEnums;
import com.xxl.job.executor.c2in.common.enums.ContentTypeEnums;
import com.xxl.job.executor.c2in.mapper.CmsSeriesMapper;
import com.xxl.job.executor.c2in.service.CmsSeriesService;
import org.springframework.stereotype.Service;

/**
 *
 * @author: tan
 * @date: 2021年8月27日 上午9:59:05
 */

@Service
public class CmsSeriesServiceImpl extends ServiceImpl<CmsSeriesMapper, CmsSeries> implements CmsSeriesService {

    @Override
    public CmsSeries getByCode(String seriesCode) {
        LambdaQueryWrapper<CmsSeries> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.eq(CmsSeries::getCode, seriesCode).last(TextConstants.LIMIT_ONE);
        return this.getOne(queryWrapper);
    }

    @Override
    public CmsSeries getByCodeCpId(String seriesCode, Long cpId) {
        LambdaQueryWrapper<CmsSeries> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.eq(CmsSeries::getCode, seriesCode).eq(CmsSeries::getCpId, cpId).last(TextConstants.LIMIT_ONE);
        return this.getOne(queryWrapper);
    }

    @Override
    public Long countByCode(String code) {
        LambdaQueryWrapper<CmsSeries> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.eq(CmsSeries::getCode, code);
        return this.count(queryWrapper);
    }

    @Override
    public void removeByCode(String code) {
        LambdaQueryWrapper<CmsSeries> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.eq(CmsSeries::getCode, code);
        this.remove(queryWrapper);
    }

    @Override
    public CmsSeries getByCodePreviewCode(String code, String previewCode) {
        LambdaQueryWrapper<CmsSeries> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.eq(CmsSeries::getCode, code).eq(CmsSeries::getResourcePreviewCode, previewCode).last(TextConstants.LIMIT_ONE);
        return this.getOne(queryWrapper);
    }

    @Override
    public void removeMappingMovieByCodePreviewCode(String code, String previewCode) {
        LambdaUpdateWrapper<CmsSeries> updateWrapper = Wrappers.lambdaUpdate();
        updateWrapper.eq(CmsSeries::getCode, code).eq(CmsSeries::getResourcePreviewCode, previewCode);
        updateWrapper.set(CmsSeries::getResourcePreviewCode, null);
        updateWrapper.set(CmsSeries::getPreviewStatus, null);
        update(updateWrapper);
    }

    @Override
    public void removeMappingMovieById(Long id) {
        LambdaUpdateWrapper<CmsSeries> updateWrapper = Wrappers.lambdaUpdate();
        updateWrapper.eq(CmsSeries::getId, id);
        updateWrapper.set(CmsSeries::getResourcePreviewCode, null);
        updateWrapper.set(CmsSeries::getPreviewStatus, null);
        update(updateWrapper);
    }

    @Override
    public CmsSeries getByPreviewCode(String previewCode) {
        LambdaQueryWrapper<CmsSeries> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.eq(CmsSeries::getResourcePreviewCode, previewCode).last(TextConstants.LIMIT_ONE);
        return this.getOne(queryWrapper);
    }

    @Override
    public void updateResourceById(String resourceCode, Long cmsSeriesId) {
        LambdaUpdateWrapper<CmsSeries> updateWrapper = Wrappers.lambdaUpdate();
        updateWrapper.eq(CmsSeries::getId, cmsSeriesId);
        updateWrapper.set(CmsSeries::getResourcePreviewCode, resourceCode);
        updateWrapper.set(CmsSeries::getPreviewStatus, ContentStatusEnums.AWAIT_RELEVANCY.getCode());
        update(updateWrapper);
    }
}


