package com.xxl.job.executor.c2in.order.processor.impl;

import com.pukka.iptv.common.base.constant.RedisKeyConstants;
import com.pukka.iptv.common.base.enums.AutoAuthorizeEnum;
import com.pukka.iptv.common.base.exception.CommonResponseException;
import com.pukka.iptv.common.data.model.sys.SysAuthorization;
import com.pukka.iptv.common.data.model.cms.CmsDownload;
import com.pukka.iptv.common.data.model.in.InMovie;
import com.pukka.iptv.common.data.model.in.InOrder;
import com.pukka.iptv.common.data.model.in.InPicture;
import com.pukka.iptv.common.data.model.sys.*;
import com.pukka.iptv.common.proxy.config.SystemProxyProperties;
import com.pukka.iptv.common.redis.service.RedisService;
import com.pukka.iptv.downloader.model.FileTask;
import com.pukka.iptv.downloader.model.Proxy;
import com.xxl.job.executor.c2in.common.constant.TextConstants;
import com.xxl.job.executor.c2in.common.enums.*;
import com.xxl.job.executor.c2in.common.exception.ConfigException;
import com.xxl.job.executor.c2in.common.exception.EmptyFtpFileException;
import com.xxl.job.executor.c2in.common.exception.UTException;
import com.xxl.job.executor.c2in.common.util.DateUtils;
import com.xxl.job.executor.c2in.common.util.SafeUtils;
import com.xxl.job.executor.c2in.common.util.StatusUtils;
import com.xxl.job.executor.c2in.order.processor.IOrderProcessor;
import com.xxl.job.executor.c2in.service.InOrderAPIService;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;

import java.util.Collections;
import java.util.List;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @Date 2021/9/8 14:27
 * @Description
 */
@Slf4j
@Data
public abstract class AbsOrderProcessor implements IOrderProcessor {

    @Autowired
    RedisService redisService;

    @Autowired
    private SystemProxyProperties proxy;

    @Autowired
    private InOrderAPIService inOrderAPIService;

    @Value("${spring.application.name}")
    String serviceName;

    SysInPassage sysInPassage;

    List<SysAuthorization> allSysAuthorizationList;

    List<SysAuthorization> autoSysAuthorizationList;

    SysCp sysCp;

    SysStorage storage;

    SysStorageDirctory movieStorageDirctory;

    SysStorageDirctory picStorageDirctory;

    static Pattern compile = Pattern.compile("\\d+\\.\\d+\\.\\d+\\.\\d+(:\\d+)*");

    @Override
    public void process(List<Object> dataList, InOrder order) {
        try {
            beforeProcess(dataList, order);
        } catch (Exception e) {
            log.error("前置处理错误:InOrderId{}", order.getId(), e);
        }
        for (Object data : dataList) {
            try {
                //校验媒资直播/点播类型与注入通道是否匹配
                if (inOrderAPIService.comparMediaTypes(order, sysInPassage)) {
                    if (ActionEnums.DELETE.getCode().equals((order.getAction()))) {
                        // 当Action是删除 并且不是自动下发才执行删除工单, 否则不执行删除工单,直接写成功状态
                        if (InPassageTypeEnum.THREE.getCode().equals(sysInPassage.getOrderPublishType())) {
                            if (inOrderAPIService.checkMediaType(order, sysInPassage)) {
                                if (deleteProcessIsExist(data, order)) {
                                    this.updateStatus(order, data, StatusUtils.success("自动下发,删除操作自动忽略并成功"));
                                } else {
                                    this.updateStatus(order, data, StatusUtils.fail("对象删除失败,对应的cp cmsChannel不存在或节目单不属于当前cp"));
                                }

                            } else {
                                deleteProcess(data, order);
                            }

                        } else {
                            deleteProcess(data, order);
                        }
                    } else if (ActionEnums.REGIST.getCode().equals((order.getAction()))) {
                        registProcess(data, order);
                    } else {
                        updateProcess(data, order);
                    }
                } else {
                    this.updateStatus(order, data, StatusUtils.fail("当前注入CP类型与内容类型不符合"));
                }
            } catch (CommonResponseException ex) {
                log.error("fein调用失败. data:{}", data.toString(), ex);
                // 最多存50个字符, 防止异常信息太多造成错误
                this.updateStatus(order, data, StatusUtils.fail("fein调用失败"
                        + ((ex.getMessage() != null && ex.getMessage().length() > 50) ? ex.getMessage().substring(0, 50) + ";" : ex.getMessage())));

            } catch (ConfigException cex) {
                log.error("ut对接逻辑,系统配置错误, data:{}", data.toString(), cex);
                // 最多存50个字符, 防止异常信息太多造成错误
                this.updateStatus(order, data, StatusUtils.fail("ut对接逻辑,系统配置错误,"
                        + ((cex.getMessage() != null && cex.getMessage().length() > 50) ? cex.getMessage().substring(0, 50) + ";" : cex.getMessage())));

            } catch (UTException uex) {
                log.error("ut对接逻辑,工单错误. data:{}", data.toString(), uex);
                // 最多存50个字符, 防止异常信息太多造成错误
                this.updateStatus(order, data, StatusUtils.fail("ut对接逻辑,工单信息异常,"
                        + ((uex.getMessage() != null && uex.getMessage().length() > 50) ? uex.getMessage().substring(0, 50) + ";" : uex.getMessage())));

            } catch (EmptyFtpFileException eff){
                log.error(eff.getMessage());
                this.updateStatus(order, data, StatusUtils.fail(eff.getMessage()));
            } catch (Exception ex) {
                log.error("服务内部错误. data:{}", data.toString(), ex);
                this.updateStatus(order, data, StatusUtils.fail("服务内部错误" + ex.getClass().getName()));

            }
        }
    }

    @Override
    public void initSysConfig(InOrder order) {
        sysInPassage = redisService.getCacheMapValue(RedisKeyConstants.SYS_IN_PASSAGE, order.getCspId());
        if (sysInPassage == null) {
            log.error("sysInPassage配置为空,处理失败. cspId:{}", order.getCspId());
            throw new ConfigException("sysInPassage配置为空,处理失败");
        }
        if (InPassageTypeEnum.TOW.getCode().equals(sysInPassage.getCpSourceType())) {
            // 若等于2 配置不在这里初始化 在对象新增时初始化
            return;
        }
        initCpByCpId(sysInPassage.getCpId());
    }

    void initCpByCpId(Long cpId) {
        // 获取cp
        sysCp = redisService.getCacheMapValue(RedisKeyConstants.SYS_CP, String.valueOf(cpId));
        if (sysCp == null) {
            log.error("sysCp配置为空,处理失败. cpId:{}", cpId);
            throw new ConfigException("sysCp配置为空,处理失败");
        }
        initSysConfig();
    }

    public void initCpByVspCode(String vspCode) {
        // 只需要在新增的时候初始化cp即可 拿到cpCode 从缓存中取cp
        if (!InPassageTypeEnum.TOW.getCode().equals(sysInPassage.getCpSourceType())) {
            // type 不为2 则直接结束
            return;
        }
        if (StringUtils.isBlank(vspCode)) {
            log.error("vspCode 为空");
            throw new ConfigException("vspCode为空,处理失败");
        }
        sysCp = redisService.getCacheMapValue(RedisKeyConstants.SYS_CP_CODE, vspCode);
        if (sysCp == null) {
            log.error("sysCp配置为空,处理失败. cpCode:{}", vspCode);
            throw new ConfigException("sysCp配置为空,处理失败");
        }
        initSysConfig();
    }

    /**
     * 重载配置初始化 供下层调用
     * 修改cp后需要 调用该方法
     */
    void initSysConfig() {
        // 1 获取cp对应的授权的sp信息
        allSysAuthorizationList = redisService.getCacheMapValue(RedisKeyConstants.ALL_SYS_AUTHORIZATION, String.valueOf(sysCp.getId()));
        if (allSysAuthorizationList == null) {
            allSysAuthorizationList = Collections.emptyList();
        }
        autoSysAuthorizationList = allSysAuthorizationList.stream()
                .filter(list -> AutoAuthorizeEnum.Yes.getValue().equals(list.getAutoAuthorize())).collect(Collectors.toList());

        storage = redisService.getCacheMapValue(RedisKeyConstants.SYS_STORAGE_KEY, String.valueOf(sysCp.getStorageId()));
        if (storage == null) {
            log.error("storage配置为空,处理失败. storageId:{}", sysCp.getStorageId());
            throw new ConfigException("storage配置为空,处理失败");
        }

        movieStorageDirctory = redisService.getCacheMapValue(RedisKeyConstants.SYS_STORAGE_DIRCTORY_KEY,
                StringUtils.joinWith(":", sysCp.getStorageId(), DirectoryTypeEnums.MOVIE.getCode(), AuthorityTypeEnums.READ_WRITE.getCode()));
        if (movieStorageDirctory == null) {
            log.warn("movieStorageDirctory配置为空.");
        }

        picStorageDirctory = redisService.getCacheMapValue(RedisKeyConstants.SYS_STORAGE_DIRCTORY_KEY,
                StringUtils.joinWith(":", sysCp.getStorageId(), DirectoryTypeEnums.PIC.getCode(), AuthorityTypeEnums.READ_WRITE.getCode()));
        if (picStorageDirctory == null) {
            log.warn("picStorageDirctory配置为空.");
        }
    }


    public FileTask getDownloadParamByInPicture(InPicture inPicture) {
        String fileUrl = inPicture.getFileUrl();
        // 是否需要代理
        Boolean isProxy = isProxy(fileUrl);
        String tagFtpUrl = buildPicTagFtpUrl(fileUrl);
        if (tagFtpUrl == null) {
            return null;
        }
        FileTask fileTask = new FileTask();
        Proxy properties = new Proxy();
        if (isProxy) {
            BeanUtils.copyProperties(proxy, properties);
            fileTask.setProxy(properties);
        } else {
            properties.setEnable(false);
            fileTask.setProxy(properties);
        }
        fileTask.setStoreId(getStorage().getId());
        fileTask.setPriority(getSysInPassage().getPriority());
        fileTask.setFileCode(inPicture.getCode());
        fileTask.setSourceUrl(fileUrl);
        // 文件类型 1：视频 2：图片
        fileTask.setFileType(FileTypeEnums.PIC.getCode());
        fileTask.setTargetUrl(tagFtpUrl);
        fileTask.setAsync(2);
        fileTask.setNotifyUrl("null");
        return fileTask;
    }


    public CmsDownload getDownloadParamByInMovie(InMovie inMovie) {
        String fileUrl = inMovie.getFileUrl();
        // 是否需要代理
        Boolean isProxy = isProxy(fileUrl);
        String tagFtpUrl = buildMovieTagFtpUrl(fileUrl);
        if (tagFtpUrl == null) {
            return null;
        }
        String fileName = fileUrl.substring(fileUrl.lastIndexOf("/") + 1);
        CmsDownload cmsDownload = new CmsDownload();
        cmsDownload.setPriority(sysInPassage.getPriority());
        cmsDownload.setName(fileName);
        cmsDownload.setCmsContentCode(inMovie.getCode());
        cmsDownload.setCpId(sysCp.getId());
        cmsDownload.setCpName(sysCp.getName());
        cmsDownload.setType(1);
        cmsDownload.setTargetAddress(tagFtpUrl);
        cmsDownload.setSourceAddress(fileUrl);
        cmsDownload.setStatus(DownloadStatusEnum.WAIT.getCode());
        cmsDownload.setInPassageId(sysInPassage.getId());
        cmsDownload.setInPassageName(sysInPassage.getName());
        cmsDownload.setStorageId(getStorage().getId());
        if (isProxy) {
            cmsDownload.setProxyFlag(1);
        } else {
            cmsDownload.setProxyFlag(0);
        }
        return cmsDownload;
    }

    /**
     * 视频目标地址构建
     *
     * @param sourceUrl 源地址
     * @return 目标地址
     */
    private String buildMovieTagFtpUrl(String sourceUrl) {
        StringBuilder result = new StringBuilder();
        SysStorage sysStorage = getStorage();
        String fileName = sourceUrl.substring(sourceUrl.lastIndexOf("/"));
        String suffix = fileName.substring(fileName.lastIndexOf(".") + 1);
        SysStorageDirctory sysStorageDirctory = getMovieStorageDirctory();
        if (sysStorageDirctory == null || sysStorage == null) {
            return null;
        }
        if (TextConstants.M3U8.equalsIgnoreCase(suffix)) {
            // m3u8 用http前缀
            result.append(sysStorage.getMovieHttpPrefix());
            if (sysStorage.getMovieHttpPrefix().endsWith("/")) {
                result.deleteCharAt(result.length() - 1);
            }
        } else {
            // ts 用ftp前缀
            result.append(TextConstants.FTP).append(sysStorageDirctory.getAccount()).append(":")
                    .append(sysStorageDirctory.getPassword()).append("@").append(sysStorage.getInnerUrl());
        }
        // 拼接目录
        /*if (sysStorage.getMovieDirectory().startsWith("/")) {
            result.append(sysStorage.getMovieDirectory());
        } else {
            result.append("/").append(sysStorage.getMovieDirectory());
        }*/
        // 拼接后缀
        result.append("/").append(suffix).append("/").append(sysCp.getCode());
        // 拼接日期 加uuid
        result.append("/").append(DateUtils.dateTimeNow(DateUtils.YYYYMMDD));
        if (TextConstants.M3U8.equals(suffix)) {
            result.append("/").append(SafeUtils.getGenerateCode());
        }
        //加原文件名称
        result.append(fileName);
        return result.toString();
    }

    /**
     * 图片目标地址构建
     *
     * @param sourceUrl 源地址
     * @return 目标地址
     */
    private String buildPicTagFtpUrl(String sourceUrl) {
        StringBuilder result = new StringBuilder();
        SysStorage sysStorage = getStorage();
        String fileName = sourceUrl.substring(sourceUrl.lastIndexOf("/"));
        SysStorageDirctory sysStorageDirctory = getPicStorageDirctory();
        if (sysStorageDirctory == null || sysStorage == null) {
            return null;
        }
        // ts 用ftp前缀
        result.append(TextConstants.FTP).append(sysStorageDirctory.getAccount()).append(":")
                .append(sysStorageDirctory.getPassword()).append("@").append(sysStorage.getInnerUrl());

        // 拼接目录
        /*if (sysStorage.getPictureDirectory().startsWith("/")) {
            result.append(sysStorage.getPictureDirectory());
        } else {
            result.append("/").append(sysStorage.getPictureDirectory());
        }*/
        // 拼接后缀
        result.append("/").append(TextConstants.PIC).append("/").append(sysCp.getCode());
        // 拼接日期 加uuid
        result.append("/").append(DateUtils.dateTimeNow(DateUtils.YYYYMMDD));
        //加原文件名称
        result.append(fileName);
        return result.toString();
    }

    public String buildNotifyUrl(Long id, String correlateId, Long cmsDownLoadId, String inOrderId) {
        StringBuilder result = new StringBuilder();
        result.append(TextConstants.HTTP).append(serviceName).append(TextConstants.DOWNLOAD_CALL_BACK_URI)
                .append("?")
                .append(buildParam(TextConstants.SUB_ORDER_ID, id)).append("&")
                .append(buildParam(TextConstants.CORRELATE_ID, correlateId)).append("&")
                .append(buildParam(TextConstants.CMS_DOWNLOAD_ID, cmsDownLoadId)).append("&")
                .append(buildParam(TextConstants.ORDER_ID, inOrderId));
        return result.toString();
    }

    String buildParam(String paramName, Object param) {
        return paramName + "=" + param;
    }

    /**
     * 匹配源地址ip  是否需要代理
     *
     * @param sourceUrl 源地址
     * @return 替换后的地址
     */
    Boolean isProxy(String sourceUrl) {
        Matcher matcher = compile.matcher(sourceUrl);
        String address;
        if (matcher.find()) {
            address = matcher.group();
        } else {
            return false;
        }
        if (!address.contains(":")) {
            // 若不存在冒号 则说明没有端口号 则需要加上默认端口号
            if (sourceUrl.startsWith(TextConstants.FTP)) {
                // ftp协议默认21端口
                address = address + ":" + 21;
            } else {
                // http协议默认80端口
                address = address + ":" + 80;
            }
        }
        SysNat nat = redisService.getCacheMapValue(RedisKeyConstants.SYS_NAT_KEY, address);
        if (nat == null) {
            return false;
        } else {
            return true;
        }
    }

    /**
     * 获取cp信息
     *
     * @param order
     * @return
     */
    public SysCp getSysCpByOrder(InOrder order) {
        SysInPassage sysInPassage = redisService.getCacheMapValue(RedisKeyConstants.SYS_IN_PASSAGE, order.getCspId());
        if (sysInPassage == null) {
            log.error("sysInPassage配置为空,处理失败. cspId:{}", order.getCspId());
            return null;
        }
        // 获取cp
        SysCp sysCp = redisService.getCacheMapValue(RedisKeyConstants.SYS_CP, String.valueOf(sysInPassage.getCpId()));
        if (sysCp == null) {
            log.error("sysCp配置为空,处理失败. cpId:{}", sysInPassage.getCpId());
            return null;
        }
        return sysCp;
    }

}
