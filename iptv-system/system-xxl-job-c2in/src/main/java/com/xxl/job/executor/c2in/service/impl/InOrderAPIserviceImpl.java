package com.xxl.job.executor.c2in.service.impl;

import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.pukka.iptv.common.base.constant.RedisKeyConstants;
import com.pukka.iptv.common.base.enums.BusinessTypeEnum;
import com.pukka.iptv.common.base.enums.StatusEnum;
import com.pukka.iptv.common.core.constant.SymbolConstant;
import com.pukka.iptv.common.core.util.ResultData;
import com.pukka.iptv.common.core.util.StringUtils;
import com.pukka.iptv.common.data.model.in.*;
import com.pukka.iptv.common.data.model.sys.SysInPassage;
import com.pukka.iptv.common.data.model.sys.SysSp;
import com.pukka.iptv.common.redis.service.RedisService;
import com.xxl.job.executor.c2in.common.constant.ProcessStatusConstants;
import com.xxl.job.executor.c2in.common.constant.ResultConstants;
import com.xxl.job.executor.c2in.common.constant.TableNameConstants;
import com.xxl.job.executor.c2in.common.constant.TextConstants;
import com.xxl.job.executor.c2in.common.enums.*;
import com.xxl.job.executor.c2in.common.exception.ConfigException;
import com.xxl.job.executor.c2in.common.util.Result;
import com.xxl.job.executor.c2in.common.util.SafeUtils;
import com.xxl.job.executor.c2in.mapper.*;
import com.xxl.job.executor.c2in.service.InMappingService;
import com.xxl.job.executor.c2in.service.InOrderAPIService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.*;
import java.util.stream.Collectors;

/**
 * @ClassName InOrderAPIserviceImpl
 * @Description 主工单入cms、bms库；以及主工单反馈逻辑判断
 * <AUTHOR>
 * @Date 2021/9/15 16:03
 * @Version
 */
@Service
@Slf4j
public class InOrderAPIserviceImpl implements InOrderAPIService {

    @Value("${businesstype.delaytime}")
    private String delaytime;

    @Value("${businesstype.realtime}")
    private String realtime;

    @Autowired
    private InOrderMapper inOrderMapper;

    @Autowired
    private InResultMapper inResultMapper;

    @Autowired
    private InOrderMappingsMapper inOrderMappingsMapper;

    @Autowired
    private InCategoryMapper inCategoryMapper;

    @Autowired
    private InChannelMapper inChannelMapper;

    @Autowired
    private InMappingMapper inMappingMapper;

    @Autowired
    private InMappingService inMappingService;

    @Autowired
    private InMovieMapper inMovieMapper;

    @Autowired
    private InPackageMapper inPackageMapper;

    @Autowired
    private InPhysicalChannelMapper inPhysicalChannelMapper;

    @Autowired
    private InPictureMapper inPictureMapper;

    @Autowired
    private InProgramMapper inProgramMapper;

    @Autowired
    private InSeriesMapper inSeriesMapper;

    @Autowired
    private InScheduleMapper inScheduleMapper;

    @Autowired
    private RedisService redisService;

    @Override
    public List<List<Map<String, List<Object>>>> inOrder2CmsBmsForObjects(int batchSize) {
        List<List<Map<String, List<Object>>>> result = new ArrayList<>();
        //获取订单数
        LambdaQueryWrapper<InOrder> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(InOrder::getStatus, InOrderStatusEnums.TODATABASE.getCode()).orderByAsc(InOrder::getCreateTime).last("limit " + batchSize);
        List<InOrder> orderList = inOrderMapper.selectList(wrapper);
        for (InOrder order : orderList) {
            order.setStatus(InOrderStatusEnums.PROCESSING.getCode());
            inOrderMapper.updateById(order);
            // 结果初始化
            Map<String, List<Object>> registryObjectList = new LinkedHashMap<>();
            Map<String, List<Object>> deleteObjectList = new LinkedHashMap<>();
            Map<String, List<Object>> updateObjectList = new LinkedHashMap<>();
            List<Map<String, List<Object>>> temp = new ArrayList<>();
            temp.add(deleteObjectList);
            temp.add(registryObjectList);
            temp.add(updateObjectList);
            Map<Integer, List<Object>> inCategoryMap = new HashMap<>();
            Map<Integer, List<Object>> inChannelMap = new HashMap<>();
            Map<Integer, List<Object>> inMappingMap = new HashMap<>();
            Map<Integer, List<Object>> inMovieMap = new HashMap<>();
            Map<Integer, List<Object>> inPackageMap = new HashMap<>();
            Map<Integer, List<Object>> inPhysicalChannelMap = new HashMap<>();
            Map<Integer, List<Object>> inPictureMap = new HashMap<>();
            Map<Integer, List<Object>> inProgramMap = new HashMap<>();
            Map<Integer, List<Object>> inScheduleMap = new HashMap<>();
            Map<Integer, List<Object>> inSeriesMap = new HashMap<>();
            inOrderHandler(order, inCategoryMap, inChannelMap, inMappingMap, inMovieMap,
                    inPackageMap, inPhysicalChannelMap, inPictureMap, inProgramMap, inScheduleMap,
                    inSeriesMap);
            // 添加主工单
            // 根据主工单类型排序
            // 新增内容工单对象处理顺序
            // 注意 入表顺序不可轻易修改
            {
                // copy一个新增工单
                InOrder addOrder = new InOrder();
                BeanUtils.copyProperties(order, addOrder);
                addOrder.setAction(ActionEnums.REGIST.getCode());
                registryObjectList.put(TableNameConstants.IN_ORDER_TABLE, Collections.singletonList(addOrder));
                List<Object> inCategoryList = inCategoryMap.get(ActionEnums.REGIST.getCode());
                if (inCategoryList != null && inCategoryList.size() > 0) {
                    registryObjectList.put(TableNameConstants.IN_CATEGORY_TABLE, inCategoryList);
                }
                List<Object> inPackageList = inPackageMap.get(ActionEnums.REGIST.getCode());
                if (inPackageList != null && inPackageList.size() > 0) {
                    registryObjectList.put(TableNameConstants.IN_PACKAGE_TABLE, inPackageList);
                }
                List<Object> inChannelList = inChannelMap.get(ActionEnums.REGIST.getCode());
                if (inChannelList != null && inChannelList.size() > 0) {
                    registryObjectList.put(TableNameConstants.IN_CHANNEL_TABLE, inChannelList);
                }
                List<Object> inSeriesList = inSeriesMap.get(ActionEnums.REGIST.getCode());
                if (inSeriesList != null && inSeriesList.size() > 0) {
                    registryObjectList.put(TableNameConstants.IN_SERIES_TABLE, inSeriesList);
                }
                List<Object> inPhysicalChannelList = inPhysicalChannelMap.get(ActionEnums.REGIST.getCode());
                if (inPhysicalChannelList != null && inPhysicalChannelList.size() > 0) {
                    registryObjectList.put(TableNameConstants.IN_PHYSICAL_CHANNEL_TABLE, inPhysicalChannelList);
                }
                List<Object> inProgramList = inProgramMap.get(ActionEnums.REGIST.getCode());
                if (inProgramList != null && inProgramList.size() > 0) {
                    registryObjectList.put(TableNameConstants.IN_PROGRAM_TABLE, inProgramList);
                }
                List<Object> inScheduleList = inScheduleMap.get(ActionEnums.REGIST.getCode());
                if (inScheduleList != null && inScheduleList.size() > 0) {
                    registryObjectList.put(TableNameConstants.IN_SCHEDULE_TABLE, inScheduleList);
                }
                List<Object> inMovieList = inMovieMap.get(ActionEnums.REGIST.getCode());
                if (inMovieList != null && inMovieList.size() > 0) {
                    registryObjectList.put(TableNameConstants.IN_MOVIE_TABLE, inMovieList);
                }
                List<Object> inPictureList = inPictureMap.get(ActionEnums.REGIST.getCode());
                if (inPictureList != null && inPictureList.size() > 0) {
                    registryObjectList.put(TableNameConstants.IN_PICTURE_TABLE, inPictureList);
                }
                List<Object> inMappingList = inMappingMap.get(ActionEnums.REGIST.getCode());
                if (inMappingList != null && inMappingList.size() > 0) {
                    registryObjectList.put(TableNameConstants.IN_MAPPING_TABLE, inMappingList);
                }
            }
            // 更新操作没有顺序限制 和删除操作顺序一样
            {
                // copy一个删除工单
                InOrder delOrder = new InOrder();
                BeanUtils.copyProperties(order, delOrder);
                delOrder.setAction(ActionEnums.DELETE.getCode());
                deleteObjectList.put(TableNameConstants.IN_ORDER_TABLE, Collections.singletonList(delOrder));
                List<Object> inMappingList = inMappingMap.get(ActionEnums.DELETE.getCode());
                if (inMappingList != null && inMappingList.size() > 0) {
                    deleteObjectList.put(TableNameConstants.IN_MAPPING_TABLE, inMappingList);
                }
                List<Object> inMovieList = inMovieMap.get(ActionEnums.DELETE.getCode());
                if (inMovieList != null && inMovieList.size() > 0) {
                    deleteObjectList.put(TableNameConstants.IN_MOVIE_TABLE, inMovieList);
                }
                List<Object> inPictureList = inPictureMap.get(ActionEnums.DELETE.getCode());
                if (inPictureList != null && inPictureList.size() > 0) {
                    deleteObjectList.put(TableNameConstants.IN_PICTURE_TABLE, inPictureList);
                }
                List<Object> inPhysicalChannelList = inPhysicalChannelMap.get(ActionEnums.DELETE.getCode());
                if (inPhysicalChannelList != null && inPhysicalChannelList.size() > 0) {
                    deleteObjectList.put(TableNameConstants.IN_PHYSICAL_CHANNEL_TABLE, inPhysicalChannelList);
                }
                List<Object> inProgramList = inProgramMap.get(ActionEnums.DELETE.getCode());
                if (inProgramList != null && inProgramList.size() > 0) {
                    deleteObjectList.put(TableNameConstants.IN_PROGRAM_TABLE, inProgramList);
                }
                List<Object> inScheduleList = inScheduleMap.get(ActionEnums.DELETE.getCode());
                if (inScheduleList != null && inScheduleList.size() > 0) {
                    deleteObjectList.put(TableNameConstants.IN_SCHEDULE_TABLE, inScheduleList);
                }
                List<Object> inCategoryList = inCategoryMap.get(ActionEnums.DELETE.getCode());
                if (inCategoryList != null && inCategoryList.size() > 0) {
                    deleteObjectList.put(TableNameConstants.IN_CATEGORY_TABLE, inCategoryList);
                }
                List<Object> inPackageList = inPackageMap.get(ActionEnums.DELETE.getCode());
                if (inPackageList != null && inPackageList.size() > 0) {
                    deleteObjectList.put(TableNameConstants.IN_PACKAGE_TABLE, inPackageList);
                }
                List<Object> inChannelList = inChannelMap.get(ActionEnums.DELETE.getCode());
                if (inChannelList != null && inChannelList.size() > 0) {
                    deleteObjectList.put(TableNameConstants.IN_CHANNEL_TABLE, inChannelList);
                }
                List<Object> inSeriesList = inSeriesMap.get(ActionEnums.DELETE.getCode());
                if (inSeriesList != null && inSeriesList.size() > 0) {
                    deleteObjectList.put(TableNameConstants.IN_SERIES_TABLE, inSeriesList);
                }
            }
            {
                // copy一个UPDATE工单
                InOrder updateOrder = new InOrder();
                BeanUtils.copyProperties(order, updateOrder);
                updateOrder.setAction(ActionEnums.UPDATE.getCode());
                // 更新工单
                updateObjectList.put(TableNameConstants.IN_ORDER_TABLE, Collections.singletonList(updateOrder));
                List<Object> inMappingList = inMappingMap.get(ActionEnums.UPDATE.getCode());
                if (inMappingList != null && inMappingList.size() > 0) {
                    updateObjectList.put(TableNameConstants.IN_MAPPING_TABLE, inMappingList);
                }
                List<Object> inMovieList = inMovieMap.get(ActionEnums.UPDATE.getCode());
                if (inMovieList != null && inMovieList.size() > 0) {
                    updateObjectList.put(TableNameConstants.IN_MOVIE_TABLE, inMovieList);
                }
                List<Object> inPictureList = inPictureMap.get(ActionEnums.UPDATE.getCode());
                if (inPictureList != null && inPictureList.size() > 0) {
                    updateObjectList.put(TableNameConstants.IN_PICTURE_TABLE, inPictureList);
                }
                List<Object> inPhysicalChannelList = inPhysicalChannelMap.get(ActionEnums.UPDATE.getCode());
                if (inPhysicalChannelList != null && inPhysicalChannelList.size() > 0) {
                    updateObjectList.put(TableNameConstants.IN_PHYSICAL_CHANNEL_TABLE, inPhysicalChannelList);
                }
                List<Object> inProgramList = inProgramMap.get(ActionEnums.UPDATE.getCode());
                if (inProgramList != null && inProgramList.size() > 0) {
                    updateObjectList.put(TableNameConstants.IN_PROGRAM_TABLE, inProgramList);
                }
                List<Object> inScheduleList = inScheduleMap.get(ActionEnums.UPDATE.getCode());
                if (inScheduleList != null && inScheduleList.size() > 0) {
                    updateObjectList.put(TableNameConstants.IN_SCHEDULE_TABLE, inScheduleList);
                }
                List<Object> inCategoryList = inCategoryMap.get(ActionEnums.UPDATE.getCode());
                if (inCategoryList != null && inCategoryList.size() > 0) {
                    updateObjectList.put(TableNameConstants.IN_CATEGORY_TABLE, inCategoryList);
                }
                List<Object> inPackageList = inPackageMap.get(ActionEnums.UPDATE.getCode());
                if (inPackageList != null && inPackageList.size() > 0) {
                    updateObjectList.put(TableNameConstants.IN_PACKAGE_TABLE, inPackageList);
                }
                List<Object> inChannelList = inChannelMap.get(ActionEnums.UPDATE.getCode());
                if (inChannelList != null && inChannelList.size() > 0) {
                    updateObjectList.put(TableNameConstants.IN_CHANNEL_TABLE, inChannelList);
                }
                List<Object> inSeriesList = inSeriesMap.get(ActionEnums.UPDATE.getCode());
                if (inSeriesList != null && inSeriesList.size() > 0) {
                    updateObjectList.put(TableNameConstants.IN_SERIES_TABLE, inSeriesList);
                }
            }
            result.add(temp);
        }
        //todo 生成工单信息
        return result;
    }


    /**
     * 修改in_result表状态
     *
     * @param inResult
     */
    public void updateForInResult(InResult inResult) {
//        if(checkInOrderMappings(inResult)){
//            updateInResult(inResult);
//        }
    }

    private void updateInResult(InResult inResult) {
        inResult.setOrderStatus(InOrderStatusEnums.TODATABASE_SUCCESS.getCode());
        inResultMapper.updateById(inResult);
    }

    /**
     * 检查主工单下子工单是否都已完成
     *
     * @param inOrder
     * @return 完成返回true，其他返回false
     */
    @Override
    public ResultData checkInOrderMappings(InOrder inOrder) {
        ResultData resultData = new ResultData();
        resultData.setResultCode(InOrderStatusEnums.TODATABASE_SUCCESS.getCode());
        StringBuilder describe = new StringBuilder();
        List<Integer> resultCodeList = new ArrayList<>();

        LambdaQueryWrapper<InOrderMappings> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(InOrderMappings::getInOrderId, inOrder.getId());

        List<InOrderMappings> inOrderMappingsList = inOrderMappingsMapper.selectList(wrapper);

        for (InOrderMappings inOrderMappings : inOrderMappingsList) {
            if (inOrderMappings.getStatus().equals(ProcessStatusConstants.IN_ORDER_MAPPINGS_INIT)) {
                resultData.setResultCode(InOrderStatusEnums.TODATABASE.getCode());
                return resultData;
            }
            if (inOrderMappings.getStatus().equals(ProcessStatusConstants.IN_ORDER_MAPPINGS_AYSN)) {
                resultData.setResultCode(InOrderStatusEnums.TODATABASE_AYSN_DOWNLOAD.getCode());
                return resultData;
            }
        }

        for (InOrderMappings inOrderMappings : inOrderMappingsList) {
            if (describe.indexOf(inOrderMappings.getErrorDesc()) >= 0) {
            } else {
                describe.append(inOrderMappings.getErrorDesc()).append("--");
            }
            resultCodeList.add(inOrderMappings.getStatus());
        }
        //判断入库是否成功
        if (resultCodeList.size() > 0) {
            boolean flagCode = false;
            for (Integer code : resultCodeList) {
                if (!code.equals(ProcessStatusConstants.IN_ORDER_MAPPINGS_SUCCESS)) {
                    flagCode = true;
                    break;
                }
            }
            resultData.setResultDesc(describe.toString());
            if (flagCode) {
                resultData.setResultCode(InOrderStatusEnums.TODATABASE_FAIL.getCode());
            } else {
                resultData.setResultCode(InOrderStatusEnums.TODATABASE_SUCCESS.getCode());
            }
        }

        return resultData;
    }

    @Override
    public Set<String> getInorderElementType(InOrder inOrder) {
        Set<String> elementTypeList = new LinkedHashSet<>();
        LambdaQueryWrapper<InOrderMappings> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(InOrderMappings::getInOrderId, inOrder.getId());

        List<InOrderMappings> inOrderMappingsList = inOrderMappingsMapper.selectList(wrapper);
        for (InOrderMappings inOrderMappings : inOrderMappingsList) {
            InMapping inMapping = inMappingService.getById(inOrderMappings.getTableRowId());
            if (ElementTypeEnums.Mappings.getInfo().equals(inOrderMappings.getType())) {
                if (null != inMapping) {
                    elementTypeList.add(distinguishMappingMediaType(inMapping.getElementType(), inMapping.getElementCode()));
                    elementTypeList.add(distinguishMappingMediaType(inMapping.getParentType(), inMapping.getParentCode()));
                }
            } else {
                elementTypeList.add(distinguishContentMediaType(inOrderMappings));
            }
        }

        return elementTypeList;
    }

    /**
     * 区分单集子集类型
     *
     * @param inOrderMappings
     * @return
     */
    private String distinguishContentMediaType(InOrderMappings inOrderMappings) {
        String elementType = "";
        if (ElementTypeEnums.Program.getInfo().equals(inOrderMappings.getType())) {
            InProgram inProgram = inProgramMapper.selectById(inOrderMappings.getTableRowId());
            if (ObjectUtils.isEmpty(inProgram.getSeriesFlag())) {
                LambdaQueryWrapper<InProgram> wrapper = new LambdaQueryWrapper<>();
                wrapper.eq(InProgram::getCode, inProgram.getCode())
                        .eq(InProgram::getStatus, StatusEnum.COME.getCode());
                List<InProgram> inPrograms = inProgramMapper.selectList(wrapper);
                inPrograms.stream().filter(inProgram1 -> ObjectUtils.isNotEmpty(inProgram1.getSeriesFlag())).findFirst().ifPresent(inProgram1 -> inProgram.setSeriesFlag(inProgram1.getSeriesFlag()));
            }
            if (SeriesTypeEnums.SUB_SET.getCode().equals(inProgram.getSeriesFlag())) {
                elementType = ElementTypeEnums.Subset.getInfo();
            } else if (SeriesTypeEnums.SIMPLE_SET.getCode().equals(inProgram.getSeriesFlag())) {
                elementType = ElementTypeEnums.Program.getInfo();
            }
            return StringUtils.isNotEmpty(elementType) ? elementType : inOrderMappings.getType();
        }
        return inOrderMappings.getType();
    }

    /**
     * 区分单集子集类型
     *
     * @param type
     * @param code
     * @return
     */
    private String distinguishMappingMediaType(String type, String code) {
        String elementType = "";
        if (ElementTypeEnums.Program.getInfo().equals(type)) {
            List<InProgram> inPrograms = inProgramMapper.selectList(new LambdaQueryWrapper<InProgram>()
                    .eq(InProgram::getCode, code).eq(InProgram::getStatus,1));
            if (!CollectionUtils.isEmpty(inPrograms) && inPrograms.size() > 0) {
                InProgram inProgram = inPrograms.stream().findFirst().get();
                if (SeriesTypeEnums.SUB_SET.getCode().equals(inProgram.getSeriesFlag())) {
                    elementType = ElementTypeEnums.Subset.getInfo();
                } else if (SeriesTypeEnums.SIMPLE_SET.getCode().equals(inProgram.getSeriesFlag())) {
                    elementType = ElementTypeEnums.Program.getInfo();
                }
                return StringUtils.isNotEmpty(elementType) ? elementType : type;
            }
            log.warn("区分关系类型单集子集类型,code:{},type:{},未找到对应单集，子集数据", code, type);
        }
        return type;
    }

    /**
     * 校验媒资直播/点播
     *
     * @param inOrder
     * @param sysInPassage
     * @return
     */
    @Override
    public Boolean checkMediaType(InOrder inOrder, SysInPassage sysInPassage) {
        if (ObjectUtils.isEmpty(sysInPassage)) {
            log.warn("校验媒资直播/点播类型 -----> 注入工单id:{}.传入注入通道媒资类型信息为空", inOrder.getId());
            sysInPassage = getRedisCache(inOrder);
        }
        List<String> ids = new ArrayList<>();
        if (ObjectUtils.isNotEmpty(sysInPassage.getOrderPublishLiveIds())) {
            ids.addAll(Arrays.asList(sysInPassage.getOrderPublishLiveIds().split(SymbolConstant.COMMA)));
        }
        if (ObjectUtils.isNotEmpty(sysInPassage.getOrderPublishRecordIds())) {
            ids.addAll(Arrays.asList(sysInPassage.getOrderPublishRecordIds().split(SymbolConstant.COMMA)));
        }
        if (CollectionUtils.isEmpty(ids)) {
            log.warn("校验媒资直播/点播类型 -----> 传入注入通道媒资类型信息:{},获取媒资直播点播类型为空", JSON.toJSONString(sysInPassage));
            return false;
        }
        //获取媒资类型信息
        Set<String> elementTypeList = getInorderElementType(inOrder);
        if (ObjectUtils.isEmpty(elementTypeList)) {
            log.warn("校验媒资直播/点播类型 -----> 传入注入工单信息:{},获取媒资类型为空", JSON.toJSONString(inOrder));
            return false;
        }
        List<String> elementArray = elementTypeList.stream().map(ElementTypeEnums::getCodeByInfo).map(String::valueOf).collect(Collectors.toList());
        Boolean result = elementArray.stream().anyMatch(ids::contains);
        return result;
    }

    /**
     * 校验媒资直播点播是否允许自动发布
     *
     * @param inOrder
     * @param sysInPassage
     * @return
     */
    @Override
    public List<String> checkAutoOrderLegal(InOrder inOrder, SysInPassage sysInPassage) {
        List<String> result = new ArrayList<>();
        if (ObjectUtils.isEmpty(sysInPassage)) {
            log.warn("校验媒资直播/点播类型 -----> 注入工单id:{}.传入注入通道媒资类型信息为空", inOrder.getId());
            sysInPassage = getRedisCache(inOrder);
        }
        if (ObjectUtils.isEmpty(sysInPassage.getBusinessType())) {
            log.warn("校验媒资直播/点播类型 -----> 注入工单id:{}.传入cp媒资类型信息为空", inOrder.getId());
            return null;
        }
        BusinessTypeEnum anEnum = BusinessTypeEnum.getEnum(sysInPassage.getBusinessType());
        //获取媒资类型信息
        Set<String> elementTypeList = getInorderElementType(inOrder);
        if (ObjectUtils.isEmpty(elementTypeList)) {
            log.warn("校验媒资直播/点播类型 -----> 传入注入工单信息:{},获取媒资类型为空", JSON.toJSONString(inOrder));
            return null;
        }
        List<String> elementArray = elementTypeList.stream().map(ElementTypeEnums::getCodeByInfo).map(String::valueOf).collect(Collectors.toList());
        switch (anEnum) {
            //当cp为所有类型，不检验sp类型
            case ALL:
                //直播类型
                if (ObjectUtils.isNotEmpty(sysInPassage.getOrderPublishLiveIds())) {
                    List<String> orderPublishLiveIds = Arrays.asList(sysInPassage.getOrderPublishLiveIds().split(SymbolConstant.COMMA));
                    if (elementArray.stream().anyMatch(orderPublishLiveIds::contains)) {
                        result.addAll(Arrays.asList(sysInPassage.getRealTimeSpIds().split(SymbolConstant.COMMA)));
                    }
                }
                //点播类型
                if (ObjectUtils.isNotEmpty(sysInPassage.getOrderPublishRecordIds())) {
                    List<String> orderPublishRecordIds = Arrays.asList(sysInPassage.getOrderPublishRecordIds().split(SymbolConstant.COMMA));
                    if (elementArray.stream().anyMatch(orderPublishRecordIds::contains)) {
                        result.addAll(Arrays.asList(sysInPassage.getDelayTimeSpIds().split(SymbolConstant.COMMA)));
                    }
                }
                break;
            case MEDIA:
                //校验sp是否为媒资类型
                if (ObjectUtils.isEmpty(sysInPassage.getDelayTimeSpIds())) {
                    log.warn("校验媒资直播/点播类型 -----> 传入注入工单信息:{},获取点播sp信息为空", JSON.toJSONString(inOrder));
                    return null;
                }
                List<String> spIds = Arrays.asList(sysInPassage.getDelayTimeSpIds().split(SymbolConstant.COMMA));
                List<String> isLegalSps = isLegalSps(spIds, anEnum);
                if (ObjectUtils.isNotEmpty(sysInPassage.getOrderPublishRecordIds())) {
                    List<String> orderPublishRecordIds = Arrays.asList(sysInPassage.getOrderPublishRecordIds().split(SymbolConstant.COMMA));
                    if (elementArray.stream().anyMatch(orderPublishRecordIds::contains)) {
                        result.addAll(isLegalSps);
                    }
                }
                break;
            case LIVE:
                //检验sp是否为直播类型
                if (ObjectUtils.isEmpty(sysInPassage.getRealTimeSpIds())) {
                    log.warn("校验媒资直播/点播类型 -----> 传入注入工单信息:{},获取直播sp信息为空", JSON.toJSONString(inOrder));
                    return null;
                }
                List<String> strings = Arrays.asList(sysInPassage.getRealTimeSpIds().split(SymbolConstant.COMMA));
                List<String> legalSps = isLegalSps(strings, anEnum);
                if (ObjectUtils.isNotEmpty(sysInPassage.getOrderPublishLiveIds())) {
                    List<String> orderPublishLiveIds = Arrays.asList(sysInPassage.getOrderPublishLiveIds().split(SymbolConstant.COMMA));
                    if (elementArray.stream().anyMatch(orderPublishLiveIds::contains)) {
                        result.addAll(legalSps);
                    }
                }
                break;
            default:
                log.warn("校验媒资直播/点播类型 -----> 传入注入工单信息:{},获取直播/点播类型信息为空", JSON.toJSONString(inOrder));
                break;
        }
        return result;
    }

    /**
     * 比较注入通道与直播点播类型
     *
     * @param inOrder
     * @param sysInPassage
     * @return
     */
    @Override
    public Boolean comparMediaTypes(InOrder inOrder, SysInPassage sysInPassage) {
        if (ObjectUtils.isEmpty(sysInPassage)) {
            log.warn("比较注入通道与直播点播类型 -----> 注入工单id:{}.传入注入通道媒资类型信息为空", inOrder.getId());
            sysInPassage = getRedisCache(inOrder);
        }
        if (ObjectUtils.isEmpty(sysInPassage.getBusinessType())) {
            log.warn("比较注入通道与直播点播类型 -----> 注入工单id:{}.直播点播类型为空", inOrder.getId());
        }
        BusinessTypeEnum anEnum = BusinessTypeEnum.getEnum(sysInPassage.getBusinessType());
        //获取媒资类型信息
        Set<String> elementTypeList = getInorderElementType(inOrder);
        if (ObjectUtils.isEmpty(elementTypeList)) {
            log.warn("比较注入通道与直播点播类型 -----> 传入注入工单信息:{},获取媒资类型为空", JSON.toJSONString(inOrder));
            return false;
        }
        boolean result = false;
        List<String> elementArray = elementTypeList.stream().map(ElementTypeEnums::getCodeByInfo).map(String::valueOf).collect(Collectors.toList());
        switch (anEnum) {
            case ALL:
                result = true;
                break;
            case LIVE:
                List<String> collect = Arrays.stream(realtime.split(SymbolConstant.COMMA)).collect(Collectors.toList());
                result = elementArray.stream().anyMatch(item -> !collect.contains(item)) ? false : true;
                break;
            case MEDIA:
                List<String> collect1 = Arrays.stream(delaytime.split(SymbolConstant.COMMA)).collect(Collectors.toList());
                result = elementArray.stream().anyMatch(item -> !collect1.contains(item)) ? false : true;
                break;
        }
        return result;
    }

    /**
     * 校验sp信息是否合法
     *
     * @param spIds
     * @param businessTypeEnum
     * @return
     */
    public List<String> isLegalSps(List<String> spIds, BusinessTypeEnum businessTypeEnum) {
        List<String> spList = new ArrayList<>();
        spIds.forEach(spId -> {
            SysSp sp = redisService.getCacheMapValue(RedisKeyConstants.SYS_SP, spId);
            if (ObjectUtils.isEmpty(sp.getBusinessType())) {
                return;
            }
            if (sp.getBusinessType().equals(BusinessTypeEnum.ALL.getCode()) || sp.getBusinessType().equals(businessTypeEnum.getCode())) {
                spList.add(spId);
            }
        });
        return spList;
    }

    /**
     * 根据注入工单信息获取注入通道信息
     *
     * @param order
     * @return
     */
    public SysInPassage getRedisCache(InOrder order) {
        SysInPassage sysInPassage = redisService.getCacheMapValue(RedisKeyConstants.SYS_IN_PASSAGE, order.getCspId());
        if (ObjectUtils.isEmpty(sysInPassage)) {
            log.error("sysInPassage配置为空,处理失败. cspId:{}", order.getCspId());
            throw new ConfigException("sysInPassage配置为空,处理失败");
        }
        return sysInPassage;
    }

    /**
     * ToDo:20230214 弃用方法
     *
     * @param inOrder
     * @param sysInPassage
     * @return
     */
    @Override
    public Boolean checkInOrderElementTypeLiveAndRecord(InOrder inOrder, SysInPassage sysInPassage) {
        //增加点播和直播规则

        if (StringUtils.isEmpty(sysInPassage.getOrderPublishLiveIds()) &&
                StringUtils.isEmpty(sysInPassage.getOrderPublishRecordIds())
        ) {
            return true;
        }
        String[] liveIds = {TextConstants.ORDER_PUBLIC_LIVE_IDS};
        String[] recordIds = {TextConstants.ORDER_PUBLIC_RECORD_IDS};
        if (StringUtils.isNotEmpty(sysInPassage.getOrderPublishLiveIds())) {
            liveIds = sysInPassage.getOrderPublishLiveIds().split(",");
        }
        if (StringUtils.isNotEmpty(sysInPassage.getOrderPublishRecordIds())) {
            recordIds = sysInPassage.getOrderPublishRecordIds().split(",");
        } else {
        }

        if (liveIds == null || recordIds == null) {
            return false;
        }
        /*字符串数组转换为集合*/
        List<String> liveIdsList = Arrays.asList(liveIds);
        List<String> recordIdsList = Arrays.asList(recordIds);
        log.info("liveIdsList: " + liveIdsList);
        log.info("recordIdsList: " + recordIdsList);

        Boolean flag = false;
        Set<String> elementTypeList = getInorderElementType(inOrder);

        log.info("elementTypeList:" + elementTypeList);
        for (String s : elementTypeList) {
            for (String lives : liveIdsList) {
                if (lives.equals(TextConstants.ORDER_PUBLIC_LIVE_IDS)) {
                    break;
                }
                log.info(ElementTypeEnums.getInfoByCode(Integer.valueOf(lives)));
                if (s.equals(ElementTypeEnums.getInfoByCode(Integer.valueOf(lives)))) {
                    flag = true;
                    break;
                }
            }
            for (String recods : recordIdsList) {
                if (recods.equals(TextConstants.ORDER_PUBLIC_LIVE_IDS)) {
                    break;
                }
                if (s.equals(ElementTypeEnums.getInfoByCode(Integer.valueOf(recods)))) {
                    flag = true;
                    break;
                }
            }
            if (flag) {
                break;
            }
        }
        log.info("live and record flag:" + flag);
        return flag;
    }

    private Result<String> inOrderHandler(InOrder inOrder, Map<Integer, List<Object>> inCategoryMap,
                                          Map<Integer, List<Object>> inChannelMap, Map<Integer, List<Object>> inMappingMap,
                                          Map<Integer, List<Object>> inMovieMap, Map<Integer, List<Object>> inPackageMap,
                                          Map<Integer, List<Object>> inPhysicalChannelMap, Map<Integer, List<Object>> inPictureMap,
                                          Map<Integer, List<Object>> inProgramMap, Map<Integer, List<Object>> inScheduleMap,
                                          Map<Integer, List<Object>> inSeriesMap) {
        Result<String> result = new Result<>();

        LambdaQueryWrapper<InOrderMappings> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(InOrderMappings::getInOrderId, inOrder.getId());
        List<InOrderMappings> inOrderMappingsList = inOrderMappingsMapper.selectList(wrapper);

        if (inOrderMappingsList == null || inOrderMappingsList.size() == 0) {
            result.setDesc(ResultConstants.NO_DATA);
            return result;
        }
        for (InOrderMappings inOrderMappings : inOrderMappingsList) {

            String tableName = inOrderMappings.getTableName();
            long tableRowId = SafeUtils.getLong(inOrderMappings.getTableRowId());
            if (TableNameConstants.IN_CATEGORY_TABLE.equals(tableName)) {
                InCategory inCategory = inCategoryMapper.selectById(tableRowId);
                if (inCategory != null) {
                    inCategoryMap.computeIfAbsent(inCategory.getAction(), k -> new ArrayList<>()).add(inCategory);
                }

            } else if (TableNameConstants.IN_CHANNEL_TABLE.equals(tableName)) {
                InChannel inChannel = inChannelMapper.selectById(tableRowId);
                if (inChannel != null) {
                    inChannelMap.computeIfAbsent(inChannel.getAction(), k -> new ArrayList<>()).add(inChannel);
                }

            } else if (TableNameConstants.IN_MAPPING_TABLE.equals(tableName)) {
                InMapping inMapping = inMappingMapper.selectById(tableRowId);
                if (inMapping != null) {
                    inMappingMap.computeIfAbsent(inMapping.getAction(), k -> new ArrayList<>()).add(inMapping);
                }
            } else if (TableNameConstants.IN_MOVIE_TABLE.equals(tableName)) {
                InMovie inMovie = inMovieMapper.selectById(tableRowId);
                if (inMovie != null) {
                    inMovieMap.computeIfAbsent(inMovie.getAction(), k -> new ArrayList<>()).add(inMovie);
                }
            } else if (TableNameConstants.IN_PACKAGE_TABLE.equals(tableName)) {
                InPackage inPackage = inPackageMapper.selectById(tableRowId);
                if (inPackage != null) {
                    inPackageMap.computeIfAbsent(inPackage.getAction(), k -> new ArrayList<>()).add(inPackage);
                }
            } else if (TableNameConstants.IN_PHYSICAL_CHANNEL_TABLE.equals(tableName)) {
                InPhysicalChannel inPhysicalChannel = inPhysicalChannelMapper.selectById(tableRowId);
                if (inPhysicalChannel != null) {
                    inPhysicalChannelMap.computeIfAbsent(inPhysicalChannel.getAction(), k -> new ArrayList<>()).add(inPhysicalChannel);
                }
            } else if (TableNameConstants.IN_PICTURE_TABLE.equals(tableName)) {
                InPicture inPicture = inPictureMapper.selectById(tableRowId);
                if (inPicture != null) {
                    inPictureMap.computeIfAbsent(inPicture.getAction(), k -> new ArrayList<>()).add(inPicture);
                }
            } else if (TableNameConstants.IN_PROGRAM_TABLE.equals(tableName)) {
                InProgram inProgram = inProgramMapper.selectById(tableRowId);
                if (inProgram != null) {
                    inProgramMap.computeIfAbsent(inProgram.getAction(), k -> new ArrayList<>()).add(inProgram);
                }
            } else if (TableNameConstants.IN_SERIES_TABLE.equals(tableName)) {
                InSeries inSeries = inSeriesMapper.selectById(tableRowId);
                if (inSeries != null) {
                    inSeriesMap.computeIfAbsent(inSeries.getAction(), k -> new ArrayList<>()).add(inSeries);
                }
            } else if (TableNameConstants.IN_SCHEDULE_TABLE.equals(tableName)) {
                InSchedule inSchedule = inScheduleMapper.selectById(tableRowId);
                if (inSchedule != null) {
                    inScheduleMap.computeIfAbsent(inSchedule.getAction(), k -> new ArrayList<>()).add(inSchedule);
                }
            } else {

            }

        }


        return result;

    }
}
