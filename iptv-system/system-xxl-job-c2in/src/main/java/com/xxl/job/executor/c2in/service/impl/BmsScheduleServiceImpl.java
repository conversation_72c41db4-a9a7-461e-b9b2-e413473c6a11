package com.xxl.job.executor.c2in.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.xxl.job.executor.c2in.mapper.BmsScheduleMapper;
import com.xxl.job.executor.c2in.model.BmsCategoryChannel;
import com.xxl.job.executor.c2in.model.BmsSchedule;
import com.xxl.job.executor.c2in.service.BmsScheduleService;
import org.springframework.stereotype.Service;

import java.util.List;


/**
 * @author: liuli
 * @date: 2021年9月2日 下午3:36:02
 */

@Service
public class BmsScheduleServiceImpl extends ServiceImpl<BmsScheduleMapper, BmsSchedule> implements BmsScheduleService {

    @Override
    public Long countByChannelCode(String channelCode) {
        LambdaQueryWrapper<BmsSchedule> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.eq(BmsSchedule::getCmsChannelCode, channelCode);
        return this.count(queryWrapper);
    }

    @Override
    public List<BmsSchedule> listByCmsScheduleCode(String cmsScheduleCode) {
        LambdaQueryWrapper<BmsSchedule> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.eq(BmsSchedule::getCmsScheduleCode, cmsScheduleCode);
        return this.list(queryWrapper);
    }

    @Override
    public void removeByCmsScheduleCode(String code) {
        LambdaQueryWrapper<BmsSchedule> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.eq(BmsSchedule::getCmsScheduleCode, code);
        this.remove(queryWrapper);
    }

    @Override
    public void removeByCmsChannelCode(String channelCode) {
        LambdaQueryWrapper<BmsSchedule> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.eq(BmsSchedule::getCmsChannelCode, channelCode);
        this.remove(queryWrapper);
    }

    @Override
    public void removeByChannelCodeAndStartDate(String channelCode, String startDate) {
        LambdaQueryWrapper<BmsSchedule> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.eq(BmsSchedule::getCmsChannelCode, channelCode)
                .eq(BmsSchedule::getStartDate, startDate);
        remove(queryWrapper);
    }

    @Override
    public void removeByChannelCodeAndStartDateAndTime(String channelCode, String startDate, String startTime) {
        LambdaQueryWrapper<BmsSchedule> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.eq(BmsSchedule::getCmsChannelCode, channelCode)
                .eq(BmsSchedule::getStartDate, startDate);
        queryWrapper.and(wrapper -> {
            wrapper.ge(BmsSchedule::getStartTime, startTime);
        });
        remove(queryWrapper);
    }
}


