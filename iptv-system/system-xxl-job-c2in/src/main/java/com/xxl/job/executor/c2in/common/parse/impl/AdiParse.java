package com.xxl.job.executor.c2in.common.parse.impl;

import com.google.common.base.Preconditions;
import com.pukka.iptv.common.core.util.StringUtils;
import com.pukka.iptv.common.data.model.in.*;
import com.xxl.job.executor.c2in.common.enums.ElementTypeEnums;
import com.xxl.job.executor.c2in.common.exception.ParserException;
import com.xxl.job.executor.c2in.common.parse.IParse;
import com.xxl.job.executor.c2in.common.util.Dom4jUtils;
import com.xxl.job.executor.c2in.service.impl.AdiManagerService;
import lombok.Data;
import org.dom4j.Attribute;
import org.dom4j.Document;
import org.dom4j.Element;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.ArrayList;
import java.util.Iterator;
import java.util.List;

/**
 * @ClassName AdiParse
 * @Description 解析ADI
 * <AUTHOR>
 * @Date 2021/8/30 9:32
 * @Version
 */
@Data
public class AdiParse {
    private  static final Logger LOGGER = LoggerFactory.getLogger(AdiParse.class);

    private String parseException;

    private IParse<Element, InCategory> categoryIParse;
    private IParse<Element, InMapping> mappingIParse;
    private IParse<Element, InMovie> movieIParse;
    private IParse<Element, InPackage> packageIParse;
    private IParse<Element, InPicture> pictureIParse;
    private IParse<Element, InProgram> programIParse;
    private IParse<Element, InSeries> seriesIParse;
    private IParse<Element, InChannel> channelIParse;
    private IParse<Element, InPhysicalChannel> physicalChannelIParse;

    private IParse<Element, InSchedule> sheduleIParse;

    private List<InCategory> inCategoryList;
    private List<InMovie> inMovieList;
    private List<InPackage> inPackageList;
    private List<InPicture>  inPictureList;
    private List<InProgram> inProgramList;
    private List<InSeries> inSeriesList;
    private List<InMapping> inMappingList;

    private List<InSchedule> inScheduleList;

    //增加处理方式
    private List<InChannel> inChannelList;
    private List<InPhysicalChannel> inPhysicalChannelList;

    public AdiParse(){
        categoryIParse = new CategoryParseImpl();
        mappingIParse = new MappingParseImpl();
        movieIParse = new MovieParseImpl();
        packageIParse = new PackageParseImpl();
        pictureIParse = new PictureParseImpl();
        programIParse = new ProgramParseImpl();
        seriesIParse = new SeriesParseImpl();
        channelIParse = new ChannelParseImpl();
        physicalChannelIParse = new PhysicalChannelParseImpl();
        sheduleIParse = new ScheduleParseImpl();

        inCategoryList = new ArrayList<>();
        inMovieList = new ArrayList<>();
        inPackageList = new ArrayList<>();
        inPictureList = new ArrayList<>();
        inProgramList = new ArrayList<>();
        inSeriesList = new ArrayList<>();
        inMappingList = new ArrayList<>();
        inChannelList = new ArrayList<>();
        inPhysicalChannelList = new ArrayList<>();
        inScheduleList = new ArrayList<>();
    }

    public boolean parse(String adi) throws Exception {
        LOGGER.debug("parse data:" + adi);
        Document document = Dom4jUtils.str2Xml(adi);
        if (document == null) {
            this.parseException = "xml解析失败";
            return false;
        }
        Element rootElement = document.getRootElement();
        return parse(rootElement);
    }

    public boolean parseHttpUrl(String httpUrl) throws Exception {
        LOGGER.debug("parse httpUrl:" + httpUrl);
        Element rootElement = Dom4jUtils.getRootElementByUrl(httpUrl);
        return parse(rootElement);
    }

    public boolean parse(Element rootElement) throws Exception {
        if (rootElement == null) {
            return false;
        }

        if (!"ADI".equals(rootElement.getName())) {
            return false;
        }

        Element objectsElement = rootElement.element("Objects");
        try {
            parseObjects(objectsElement);
        } catch (ParserException error) {
            parseException = error.getMessage();
            return false;
        }

        Element mappingsElement = rootElement.element("Mappings");
        try {
            parseMappings(mappingsElement);
        } catch (ParserException error) {
            parseException = error.getMessage();
            return false;
        }
        return true;
    }

    private void parseObjects(Element element){
        if (element == null) return;
        List<?> objectsElement = element.elements("Object");
        if (objectsElement == null || objectsElement.isEmpty()) return;
        Element objectElement;
        Attribute elementTypeAttribute;
        String elementType;

        InCategory inCategory;
        InMovie inMovie;
        InPackage inPackage;
        InPicture inPicture;
        InProgram inProgram;
        InSeries inSeries;
        InChannel inChannel;
        InPhysicalChannel inPhysicalChannel;
        InSchedule inSchedule;

        for (Iterator<?> iter = objectsElement.iterator(); iter.hasNext(); ) {
            objectElement = (Element) iter.next();
            elementTypeAttribute = objectElement.attribute("ElementType");
            if (elementTypeAttribute == null || StringUtils.isEmpty(elementTypeAttribute.getValue()))
                continue;
            elementType = elementTypeAttribute.getValue();
            if (elementType.equals(ElementTypeEnums.Category.getInfo())) {
                inCategory = categoryIParse.get(objectElement);
                if (inCategory != null){
                    inCategoryList.add(inCategory);
                }
            } else if (elementType.equals(ElementTypeEnums.Movie.getInfo())) {

                inMovie = movieIParse.get(objectElement);
                if (inMovie != null){
                    inMovieList.add(inMovie);
                }
            } else if (elementType.equals(ElementTypeEnums.Package.getInfo())) {
                inPackage = packageIParse.get(objectElement);
                if(inPackage != null){
                    inPackageList.add(inPackage);
                }
            } else if (elementType.equals(ElementTypeEnums.Picture.getInfo())) {
                inPicture = pictureIParse.get(objectElement);
                if(inPicture != null){
                    inPictureList.add(inPicture);
                }
            } else if (elementType.equals(ElementTypeEnums.Program.getInfo())) {
                inProgram = programIParse.get(objectElement);
                if(inProgram != null){
                    inProgramList.add(inProgram);
                }
            } else if (elementType.equals(ElementTypeEnums.Series.getInfo())) {
                inSeries = seriesIParse.get(objectElement);
                if(inSeries != null){
                    inSeriesList.add(inSeries);
                }
            } else if (elementType.equals(ElementTypeEnums.Channel.getInfo())) {
                inChannel = channelIParse.get(objectElement);
                if(inChannel != null){
                    inChannelList.add(inChannel);
                }
            }else if (elementType.equals(ElementTypeEnums.PhysicalChannel.getInfo())) {
                inPhysicalChannel = physicalChannelIParse.get(objectElement);
                if(inPhysicalChannel != null){
                    inPhysicalChannelList.add(inPhysicalChannel);
                }
            }else if (elementType.equals(ElementTypeEnums.Schedule.getInfo())) {
                inSchedule = sheduleIParse.get(objectElement);
                if(inSchedule != null){
                    inScheduleList.add(inSchedule);
                }
            }else {

            }
        }
    }

    /**
     * 解析mapping
     * @param element
     */
    private void parseMappings(Element element){
        if (element == null) return;
        List<?> mappingsElement = element.elements("Mapping");
        if (mappingsElement == null || mappingsElement.isEmpty()) return;
        Element mappingElement;

        InMapping inMapping;

        for (Iterator<?> iter = mappingsElement.iterator(); iter.hasNext();) {
            mappingElement = (Element) iter.next();
            inMapping = mappingIParse.get(mappingElement);
            /** 针对剧头(series)中带入子集(program)的mapping关系不予处理，例如新疆大洋*/
            if(inMapping != null){
                inMappingList.add(inMapping);
            }
        }
    }

    public boolean save(final InOrder inOrder) {
        Preconditions.checkState(parseException == null, "must parse ok");
        AdiManagerService manager = new AdiManagerService();
        try {
            manager.save(inOrder, inCategoryList, inMovieList, inPackageList,
                    inPictureList, inProgramList, inSeriesList,
                    inMappingList, inChannelList, inPhysicalChannelList, inScheduleList);
            return true;
        } catch (Exception e) {
            this.parseException = e.getMessage();
            LOGGER.error("save object error", e);
        }

        return false;
    }


}
