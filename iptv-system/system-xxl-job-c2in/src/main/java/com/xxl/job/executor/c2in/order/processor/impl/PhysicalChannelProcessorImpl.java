package com.xxl.job.executor.c2in.order.processor.impl;

import com.pukka.iptv.common.api.feign.cms.CmsPhysicalChannelFeignClient;
import com.pukka.iptv.common.base.enums.SourceEnum;
import com.pukka.iptv.common.base.vo.CommonResponse;
import com.pukka.iptv.common.data.dto.CmsPhysicalChannelDto;
import com.pukka.iptv.common.data.model.cms.CmsChannel;
import com.pukka.iptv.common.data.model.cms.CmsPhysicalChannel;
import com.pukka.iptv.common.data.model.in.InChannel;
import com.pukka.iptv.common.data.model.in.InOrder;
import com.pukka.iptv.common.data.model.in.InPhysicalChannel;
import com.xxl.job.executor.c2in.common.constant.TableNameConstants;
import com.xxl.job.executor.c2in.common.constant.TextConstants;
import com.xxl.job.executor.c2in.common.enums.*;
import com.xxl.job.executor.c2in.common.exception.UTException;
import com.xxl.job.executor.c2in.common.util.StatusUtils;
import com.xxl.job.executor.c2in.model.BmsPhysicalChannel;
import com.xxl.job.executor.c2in.service.*;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

/**
 * <AUTHOR>
 * @Date 2021/8/26 11:33
 * @Description 逻辑频道工单处理器
 */
@Slf4j
@Component(TableNameConstants.IN_PHYSICAL_CHANNEL_TABLE)
public class PhysicalChannelProcessorImpl extends AbsOrderProcessor {

    @Autowired
    CmsPhysicalChannelService cmsPhysicalChannelService;

    @Autowired
    BmsPhysicalChannelService bmsPhysicalChannelService;

    @Autowired
    InOrderMappingsService inOrderMappingsService;

    @Autowired
    InPhysicalChannelService inPhysicalChannelService;

    @Autowired
    CmsChannelService cmsChannelService;

    @Autowired
    CmsPhysicalChannelFeignClient cmsPhysicalChannelFeignClient;

    @Override
    public void updateProcess(Object data, InOrder order) {
        InPhysicalChannel inPhysicalChannel = (InPhysicalChannel) data;

        CmsPhysicalChannel cmsPhysicalChannel = cmsPhysicalChannelService.getByCode(inPhysicalChannel.getCode());
        if (cmsPhysicalChannel == null) {
            log.warn("对象更新失败,对象不存在 cmsPhysicalChannelCode:{}", inPhysicalChannel.getCode());
            registProcess(data, order);
            return;
        }
        if (!sysInPassage.getCpId().equals(cmsPhysicalChannel.getCpId())) {
            log.error("对象更新失败,物理频道不属于当前cp cmsPhysicalChannelCode:{}", inPhysicalChannel.getCode());
            updateStatus(order, inPhysicalChannel, StatusUtils.fail("对象更新失败,物理频道不属于当前cp"));
            return;
        }
        CmsPhysicalChannelDto cmsPhysicalChannelDto = new CmsPhysicalChannelDto(inPhysicalChannel);
        cmsPhysicalChannelDto.setRequestResource(SourceEnum.SYSWORK.getValue());
        cmsPhysicalChannelDto.setId(cmsPhysicalChannel.getId());
        cmsPhysicalChannelDto.setCpId(cmsPhysicalChannel.getCpId());
        cmsPhysicalChannelDto.setCode(cmsPhysicalChannel.getCode());
        CommonResponse<Boolean> booleanCommonResponse = cmsPhysicalChannelFeignClient.updateById(cmsPhysicalChannelDto);
        if (TextConstants.OK.equals(booleanCommonResponse.getCode())) {
            // 更新状态 结束
            updateStatus(order, inPhysicalChannel, StatusUtils.success());
        } else {
            updateStatus(order, inPhysicalChannel, StatusUtils.fail("物理频道update失败"+booleanCommonResponse.getMessage()));
            log.error("物理频道update失败 code:{} Response:{}",inPhysicalChannel.getCode(), booleanCommonResponse.getMessage());
        }
    }

    @Override
    public void registProcess(Object data, InOrder inOrderVo) {
        InPhysicalChannel inPhysicalChannel = (InPhysicalChannel) data;
        initCp(inPhysicalChannel);
        Long cmsPhysicalNum = cmsPhysicalChannelService.countByCode(inPhysicalChannel.getCode());
        if (cmsPhysicalNum == 0) {
            // 新增
            CmsPhysicalChannelDto cmsPhysicalChannel = new CmsPhysicalChannelDto(inPhysicalChannel);
            cmsPhysicalChannel.setCpId(sysCp.getId());
            cmsPhysicalChannel.setCpName(sysCp.getName());
            cmsPhysicalChannel.setRequestResource(SourceEnum.SYSWORK.getValue());
            // 查询并填写channelId
            if (StringUtils.isNotBlank(inPhysicalChannel.getChannelCode())) {
                CmsChannel cmsChannel = cmsChannelService.getByCodeCpid(inPhysicalChannel.getChannelCode(), getSysCp().getId());
                if (cmsChannel != null) {
                    cmsPhysicalChannel.setChannelId(cmsChannel.getId());
                    cmsPhysicalChannel.setChannelCode(cmsChannel.getCode());
                    cmsPhysicalChannel.setChannelName(cmsChannel.getName());
                    cmsPhysicalChannel.setCspId(sysInPassage.getCspId());
                    CommonResponse commonResponse = cmsPhysicalChannelFeignClient.save(cmsPhysicalChannel);
                    if (TextConstants.OK.equals(commonResponse.getCode())) {
                        updateStatus(inOrderVo, inPhysicalChannel, StatusUtils.success());
                    } else {
                        updateStatus(inOrderVo, inPhysicalChannel, StatusUtils.fail(commonResponse.getMessage()));
                        log.error("物理频道regist失败 Code:{}, Response:{}", inPhysicalChannel.getCode(), commonResponse.toString());
                    }
                } else {
                    log.error("新增物理频道时,其关联的逻辑频道不存在,CmsChannelCode:{}", inPhysicalChannel.getChannelCode());
                    updateStatus(inOrderVo, inPhysicalChannel, StatusUtils.fail("对象新增失败,关联的逻辑频道不存在"));
                    return;
                }
            }
            // 更新状态
            updateStatus(inOrderVo, inPhysicalChannel, StatusUtils.success());
        } else {
            updateProcess(data, inOrderVo);
            log.warn("cmsPhysicalChannel表已存在 code为{}的对象", inPhysicalChannel.getCode());
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void deleteProcess(Object data, InOrder order) {
        InPhysicalChannel inPhysicalChannel = (InPhysicalChannel) data;
        CmsPhysicalChannel physicalChannel = cmsPhysicalChannelService.getByCode(inPhysicalChannel.getCode());
        if (physicalChannel == null) {
            log.warn("physicalChannel不存在,删除结束, code:{}, cpId:{}", inPhysicalChannel.getCode(), sysInPassage.getCpId());
            updateStatus(order, inPhysicalChannel, StatusUtils.success());
            return;
        }
        if (!sysInPassage.getCpId().equals(physicalChannel.getCpId())) {
            log.error("对象删除失败,PhysicalChannel不属于当前cp, physicalChannel:{}", physicalChannel.getCode());
            updateStatus(order, inPhysicalChannel, StatusUtils.fail("对象删除失败,PhysicalChannel不属于当前cp"));
            return;
        }
        // 检查bms表
        List<BmsPhysicalChannel> bmsPhysicalChannelList = bmsPhysicalChannelService.listByCmsPhysicalChannelCode(inPhysicalChannel.getCode());
        for (BmsPhysicalChannel bmsPhysicalChannel : bmsPhysicalChannelList) {
            // 不是待发布 并且 不是回收成功 则不能删除
            if (!PublishStatusEnums.checkDeleteStatus(bmsPhysicalChannel.getPublishStatus())){
                log.error("删除失败,发布状态不是待发布和回收成功发布失败, code :{} spId:{}", bmsPhysicalChannel.getCode(), bmsPhysicalChannel.getSpId());
                updateStatus(order, inPhysicalChannel, StatusUtils.fail("删除失败,发布状态不是待发布和回收成功发布失败"));
                return;
            }
        }
        // 删除
        cmsPhysicalChannelService.removeByCode(inPhysicalChannel.getCode());
        bmsPhysicalChannelService.removeByCmsPhysicalChannelCode(inPhysicalChannel.getCode());
        updateStatus(order, inPhysicalChannel, StatusUtils.success());
    }

    /**
     * 工单表状态更新
     *
     * @param order 主工单
     * @param inPhysicalChannel 子工单
     * @param status 处理结果
     */
    private void updateStatus(InOrder order, InPhysicalChannel inPhysicalChannel, StatusUtils.Status status) {
        inPhysicalChannelService.updateStatusById(inPhysicalChannel.getId(), status);
        inOrderMappingsService.updateStatus(order.getCorrelateId(), TableNameConstants.IN_PHYSICAL_CHANNEL_TABLE, inPhysicalChannel.getId(), status);
    }

    @Override
    public void updateStatus(InOrder order, Object data, StatusUtils.Status status) {
        InPhysicalChannel inPhysicalChannel = (InPhysicalChannel) data;
        // 子工单对象直接通过id更新状态
        updateStatus(order, inPhysicalChannel, status);
    }

    @Override
    public boolean deleteProcessIsExist(Object data, InOrder order) {

        InPhysicalChannel inPhysicalChannel = (InPhysicalChannel) data;
        CmsChannel cmsChannel = cmsChannelService.getByCodeCpid(inPhysicalChannel.getChannelCode(), getSysCp().getId());
        if (cmsChannel == null) {
            // 若频道不存在 则结束
            log.error("对象更新失败,InPhysicalChannel对应的cp：{}, cmsChannel不存在, cmsSchedule:{}", getSysCp().getId());
            return false;
        }
        return true;
    }

    void initCp(InPhysicalChannel inPhysicalChannel) {
        if (!InPassageTypeEnum.TOW.getCode().equals(sysInPassage.getCpSourceType())) {
            // type 不为2 则直接结束
            return;
        }
        String channelCode = inPhysicalChannel.getChannelCode();
        if (StringUtils.isBlank(channelCode)) {
            throw new UTException("物理频道关联的逻辑频道为空");
        }
        CmsChannel cmsChannel = cmsChannelService.getByCode(channelCode);
        if (cmsChannel == null) {
            throw new UTException("物理频道关联的逻辑频道为空");
        }
        initCpByCpId(cmsChannel.getCpId());
    }
}
