package com.xxl.job.executor.download.task.queue;

import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.xxl.job.executor.download.interfaces.TaskExistenceValidator;
import lombok.extern.slf4j.Slf4j;

import java.util.List;
import java.util.concurrent.BlockingQueue;
import java.util.concurrent.LinkedBlockingQueue;
import java.util.concurrent.TimeUnit;

/**
 * @Author: chiron
 * @Date: 2023/08/14/16:33
 * @Description: 下载任务队列
 */
@Slf4j
public class DownloadTaskQueue<T> implements TaskExistenceValidator<T> {

  private BlockingQueue<T> queue = new LinkedBlockingQueue<>();
  private final long defaultTimeout;
  private final TimeUnit defaultTimeUnit;

  public DownloadTaskQueue() {
    // 默认超时时间为 1
    this.defaultTimeout = 1;
    // 默认超时时间单位为秒
    this.defaultTimeUnit = TimeUnit.SECONDS;
  }

  /**
   * 添加下载任务
   * @param item
   * @return
   */
  public synchronized boolean pull(T item) {
    if (item != null) {
      if (!isTaskExist(item)) {
        return queue.offer(item);
      } else {
        log.warn("任务已存在于下载队列中，不会重复添加: {}", item);
        return false;
      }
    } else {
      log.warn("尝试添加空任务到下载队列");
      return false;
    }
  }

  /**
   * 添加下载任务
   * @param items
   */
  public synchronized void pull(List<T> items) {
    if (CollectionUtils.isNotEmpty(items)) {
      for (T item : items) {
        if (!isTaskExist(item)) {
          pull(item);
        }
      }
    } else {
      log.warn("尝试添加空任务列表到下载队列");
    }
  }

  /**
   * 获取下载任务
   * @return
   */
  public synchronized T poll() {
    if (!queue.isEmpty()) {
      return poll(defaultTimeout, defaultTimeUnit);
    } else {
      log.warn("下载队列为空，无法获取任务");
      return null;
    }
  }

  /**
   * 获取下载任务
   * @param timeout
   * @param unit
   * @return
   */
  public synchronized T poll(long timeout, TimeUnit unit) {
    try {
      return queue.poll(timeout, unit);
    } catch (InterruptedException e) {
      Thread.currentThread().interrupt();
      log.error("下载任务队列获取任务失败", e);
      return null;
    }
  }

  /**
   * 清空队列
   */
  public synchronized void clearQueue() {
    queue.clear();
  }

  /**
   * 获取队列大小
   * @return
   */
  public synchronized int size() {
    return queue.size();
  }

  @Override
  public boolean isTaskExist(T task) {
    return queue.contains(task);
  }
}