package com.xxl.job.executor.c2in.vo;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.NoArgsConstructor;
import com.pukka.iptv.common.data.model.cms.CmsPicture;
import com.pukka.iptv.common.data.model.in.InPicture;
import io.swagger.annotations.ApiModel;
import lombok.AllArgsConstructor;
import lombok.Data;

/**
 *
 * @author: <PERSON><PERSON><PERSON>
 * @date: 2021年8月27日 上午10:00:46
 */

@Data
@AllArgsConstructor
@JsonIgnoreProperties(value = { "handler" })
@ApiModel("CmsPictureVo")
public class CmsPictureVo extends CmsPicture implements java.io.Serializable{

    public CmsPictureVo(InPicture inPicture) {
        this.setCode(inPicture.getCode());
        this.setDescription(inPicture.getDescription());
        this.setStatus(inPicture.getStatus());
    }
}
