package com.xxl.job.executor.c2in.common.enums;

public enum RabbitMQActionEnums {

    SUCCESS(1, "SUCCESS"), RETRY(2, "RETRY"), REJECT(3, "REJECT");

    private final Integer code;
    private final String info;


    public static Integer getCodeByInfo(String info) {
        RabbitMQActionEnums[] actionEnums = values();
        for (RabbitMQActionEnums element : actionEnums) {
            if (element.getInfo().equals(info.trim())) {
                return element.getCode();
            }
        }
        return null;
    }

    public static String getInfoByCode(Integer code) {
        RabbitMQActionEnums[] actionEnums = values();
        for (RabbitMQActionEnums element : actionEnums) {
            if (element.getCode().intValue() == code.intValue()) {
                return element.getInfo();
            }
        }
        return null;
    }

    RabbitMQActionEnums(Integer code, String info)
    {
        this.code = code;
        this.info = info;
    }

    public Integer getCode()
    {
        return code;
    }

    public String getInfo()
    {
        return info;
    }
}
