package com.xxl.job.executor.c2in.common.enums;

/**
 * <AUTHOR>
 * @Date 2021/9/6 18:22
 * @Description
 */
public enum LockStatusEnums {
    /**
     * 锁定状态
     */
    UNLOCKED(1, "未锁定"),
    LOCKED(2, "已锁定");

    LockStatusEnums(int code, String info){
        this.code = code;
        this.info = info;
    }

    private final Integer code;
    private final String info;

    public Integer getCode() {
        return code;
    }

    public String getInfo() {
        return info;
    }
}

