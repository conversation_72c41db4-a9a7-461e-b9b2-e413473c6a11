package com.xxl.job.executor.c2in.common.enums;

/**
 * <AUTHOR>
 * @Date 2021/11/4 11:32
 * @Description
 */
public enum  InPassageTypeEnum {
    /**
     * 1:从关联CP获取CP信息(根据cp_id)  2：从工单VSPCode获取CP信息(根据工单里的VSPCode关联cp_code)
     * 1:注入内容默认未发布,            2:注入内容默认发布成功
     * 1：下载                         2：透传
     * 1.解析                          2.透传加解析                3.解析并自动下发',
     */
    ONE(1),
    TOW(2),
    THREE(3);
    private final Integer code;

    InPassageTypeEnum(Integer code) {
        this.code = code;
    }

    public Integer getCode() {
        return code;
    }
}
