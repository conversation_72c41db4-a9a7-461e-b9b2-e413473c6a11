package com.xxl.job.executor.c2in.RabbitMQ;

import com.pukka.iptv.common.api.feign.out.C2outFeignClient;
import com.pukka.iptv.common.base.constant.RedisKeyConstants;
import com.pukka.iptv.common.data.dto.OutParamsDto;
import com.pukka.iptv.common.data.model.in.InOrder;
import com.pukka.iptv.common.data.model.sys.SysInPassage;
import com.pukka.iptv.common.rabbitmq.config.InOrderDirectMQConfig;
import com.pukka.iptv.common.rabbitmq.vo.MessageBody;
import com.pukka.iptv.common.redis.service.RedisService;
import com.rabbitmq.client.Channel;
import com.pukka.iptv.common.base.enums.RabbitMQActionEnums;
import com.xxl.job.executor.c2in.service.InOrderService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.amqp.core.Message;
import org.springframework.amqp.rabbit.annotation.*;

import org.springframework.amqp.support.converter.MessageConverter;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import java.io.IOException;

/**
 * @ClassName InOrderConsumer
 * @Description 消费InOrder订单
 * <AUTHOR>
 * @Date 2021/9/2 15:52
 * @Version
 */
@Component
@Slf4j
public class InOrderConsumer {

    @Autowired
    private InOrderService inOrderService;

    @Autowired
    private MessageConverter jackson2JsonMessageConverter;

    @Autowired
    private C2outFeignClient c2outFeignClient;

    private static final String CSPID_UT = "UT";

    @Autowired
    private RedisService redisService;


    /**
     *处理cp收到后发送的消息
     * @param messageorigin
     * @param channel
     */
    @RabbitListener(bindings = {
            @QueueBinding(value = @Queue(value = InOrderDirectMQConfig.IN_ORDER_QUEUE),
                    exchange = @Exchange(value = InOrderDirectMQConfig.IN_ORDER_EXCHANGE),
                    key = InOrderDirectMQConfig.IN_ORDER_ROUTING)},containerFactory="rabbitListenerContainerFactory")
    public void recieved(Message messageorigin, Channel channel) {
        RabbitMQActionEnums action = RabbitMQActionEnums.SUCCESS;
        log.info("------------------rabbitlistener start --------------");
        try {
            MessageBody messageBody = (MessageBody) jackson2JsonMessageConverter.fromMessage(messageorigin);
            log.info(messageBody.getMsg().toString());
//            log.info(messageBody.getMsg().get("cmdFileURL").toString());
//            log.info(messageBody.getMsg().get("LSPID").toString());
//            log.info(messageBody.getMsg().get("CSPID").toString());
//            log.info(messageBody.getMsg().get("correlateID").toString());
//            log.info(messageBody.getMsg().get("inPassageId").toString());
//            log.info(messageBody.getMsg().get("inPassageName").toString());
//            log.info(messageBody.getId());
            inOrderService.saveByCp(messageBody.getMsg().get("CSPID").toString(), messageBody.getMsg().get("LSPID").toString(),
                             messageBody.getMsg().get("correlateID").toString(), messageBody.getMsg().get("cmdFileURL").toString(),
                             messageBody.getMsg().get("inPassageId").toString(), messageBody.getMsg().get("inPassageName").toString() );

            action = RabbitMQActionEnums.SUCCESS;

            //针对爱上通道特殊处理
            SysInPassage sysInPassage = redisService.getCacheMapValue(RedisKeyConstants.SYS_IN_PASSAGE,messageBody.getMsg().get("CSPID").toString());
            if(sysInPassage != null){
                if(sysInPassage.getOrderPublishType() != null){
                    if(sysInPassage.getOrderPublishType() == 2){
                        OutParamsDto outParamsDto = new OutParamsDto();
                        outParamsDto.setCmdFileUrl(messageBody.getMsg().get("cmdFileURL").toString());
                        outParamsDto.setCorrelateId(messageBody.getMsg().get("correlateID").toString());
                        outParamsDto.setCspId(messageBody.getMsg().get("CSPID").toString());
                        outParamsDto.setLspId(messageBody.getMsg().get("LSPID").toString());
                        outParamsDto.setOutPassageIds(sysInPassage.getOutPassageIds());
                        try{
                            log.info("透传接口，data:{}", outParamsDto.toString());
                            c2outFeignClient.send(outParamsDto);
                        }catch (Exception exception){
                            log.error("透传接口, fein接口调用失败. exception:{}", exception);
                        }
                    }
                }
            }



        } catch (Exception e) {
            log.info("get message fromBytes message exception" + e.getMessage());
            action = RabbitMQActionEnums.RETRY;
        }
        finally {
            try{
                //回调成功确认消息
                if (action == RabbitMQActionEnums.SUCCESS) {
                    //成功确认消息
                    try {
                        channel.basicAck(messageorigin.getMessageProperties().getDeliveryTag(), false);
                    } catch (IOException e) {
                        log.info("basicAck ex:" + e.getMessage());
                    }
                }else if(action == RabbitMQActionEnums.RETRY){
                        channel.basicNack(messageorigin.getMessageProperties().getDeliveryTag(), false, true);
                }else{
                }
                channel.close();
            }catch (Exception e){
                log.info("basicAck ex" + e.getMessage());
            }

        }

    }

    private Boolean saveInOrder(InOrder inOrder){
        Boolean  flag = inOrderService.save(inOrder);
        return flag;
    }

}
