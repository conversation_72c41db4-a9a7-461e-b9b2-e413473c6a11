package com.xxl.job.executor.c2in.order.processor.impl;

import com.pukka.iptv.common.api.feign.bms.BmsCategoryFeignClient;
import com.pukka.iptv.common.api.feign.sys.DownloadFeignClient;
import com.pukka.iptv.common.base.vo.CommonResponse;
import com.pukka.iptv.common.data.model.sys.SysAuthorization;
import com.pukka.iptv.common.data.model.in.InCategory;
import com.pukka.iptv.common.data.model.in.InOrder;
import com.pukka.iptv.common.data.vo.req.BmsCategoryInjectionReq;
import com.xxl.job.executor.c2in.common.constant.TableNameConstants;
import com.xxl.job.executor.c2in.common.constant.TextConstants;
import com.xxl.job.executor.c2in.common.enums.ActionEnums;
import com.xxl.job.executor.c2in.common.enums.InPassageTypeEnum;
import com.xxl.job.executor.c2in.common.enums.LockStatusEnums;
import com.xxl.job.executor.c2in.common.enums.PublishStatusEnums;
import com.xxl.job.executor.c2in.common.util.StatusUtils;
import com.xxl.job.executor.c2in.common.util.StrUtils;
import com.xxl.job.executor.c2in.model.*;
import com.xxl.job.executor.c2in.service.*;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @Date 2021/9/7 20:17
 * @Description
 */
@Slf4j
@Component(TableNameConstants.IN_CATEGORY_TABLE)
public class CategoryProcessorImpl extends AbsOrderProcessor {

    @Autowired
    InOrderMappingsService inOrderMappingsService;

    @Autowired
    BmsCategoryService bmsCategoryService;

    @Autowired
    InCategoryService inCategoryService;

    @Autowired
    BmsCategoryContentService bmsCategoryContentService;

    @Autowired
    BmsCategoryChannelService bmsCategoryChannelService;

    @Autowired
    CmsPictureService cmsPictureService;

    @Autowired
    BmsPictureService bmsPictureService;

    @Autowired
    DownloadFeignClient downloadFeignClient;

    @Autowired
    BmsContentService bmsContentService;

    @Autowired
    BmsChannelService bmsChannelService;

    @Autowired
    BmsCategoryFeignClient bmsCategoryFeignClient;

    @Override
    public void beforeProcess(List<Object> dataList, InOrder order) {
        // 对list进行排序 新增时 父节点code为空排在前面先处理
        if (ActionEnums.REGIST.getCode().equals(order.getAction())) {
            // 新增时先新增父节点 在新增子节点
            dataList.sort((o1, o2) -> {
                InCategory c1 = (InCategory) o1;
                InCategory c2 = (InCategory) o2;
                if ((StrUtils.isParentCodeBlank(c1.getParentCode()) && StrUtils.isParentCodeBlank(c2.getParentCode()))
                        || (!StrUtils.isParentCodeBlank(c1.getParentCode()) && !StrUtils.isParentCodeBlank(c2.getParentCode()))) {
                    // ParentCode 都为空 或者 ParentCode都不为空 则不变
                    return 0;
                } else if (StrUtils.isParentCodeBlank(c1.getParentCode()) && StrUtils.isParentCodeBlank(c2.getParentCode())) {
                    // o1为空 o2不为空
                    return -1;
                } else {
                    // o1不为空 o2为空
                    return 1;
                }
            });
        } else {
            // 删除时先删子节点,再删父节点
            dataList.sort((o1, o2) -> {
                InCategory c1 = (InCategory) o1;
                InCategory c2 = (InCategory) o2;
                if ((StrUtils.isParentCodeBlank(c1.getParentCode()) && StrUtils.isParentCodeBlank(c2.getParentCode()))
                        || (!StrUtils.isParentCodeBlank(c1.getParentCode()) && !StrUtils.isParentCodeBlank(c2.getParentCode()))) {
                    // ParentCode 都为空 或者 ParentCode都不为空 则不变
                    return 0;
                } else if (StrUtils.isParentCodeBlank(c1.getParentCode()) && StrUtils.isParentCodeBlank(c2.getParentCode())) {
                    // o1为空 o2不为空
                    return 1;
                } else {
                    // o1不为空 o2为空
                    return -1;
                }
            });
        }
    }

    @Override
    public void updateProcess(Object data, InOrder order) {
        InCategory inCategory = (InCategory) data;
        List<BmsCategory> bmsCategoryList = bmsCategoryService.listByCode(inCategory.getCode());
        if (bmsCategoryList.size() == 0) {
            log.warn("对象更新失败,对象不存在 CategoryVo:{}", inCategory.getCode());
            registProcess(data, order);
            return;
        }
        if (!sysInPassage.getCpId().equals(bmsCategoryList.get(0).getCpId())) {
            log.error("更新失败,产品包不属于当前cp packageCode:{}", bmsCategoryList.get(0).getCode());
            updateStatus(order, inCategory, StatusUtils.fail("更新失败栏目不属于当前cp"));
            return;
        }
        BmsCategoryInjectionReq bmsCategoryCommonReq = new BmsCategoryInjectionReq(inCategory);
        for (BmsCategory bmsCategory : bmsCategoryList) {
            bmsCategoryCommonReq.setId(bmsCategory.getId());
            //不允许更新bms的extraCode
            bmsCategoryCommonReq.setExtraCode(null);
            CommonResponse<Boolean> commonResponse = bmsCategoryFeignClient.updateById(bmsCategoryCommonReq);
            if (TextConstants.OK.equals(commonResponse.getCode())) {
                updateStatus(order, inCategory, StatusUtils.success());
            } else {
                log.error("对象更新失败. code: {} data: {}", inCategory.getCode(), commonResponse.toString());
                updateStatus(order, inCategory, StatusUtils.fail(commonResponse.getMessage()));
                return;
            }
        }
    }


    public void updateProcess(Object data, InOrder order, Long spId) {
        InCategory inCategory = (InCategory) data;
        BmsCategory bmsCategory = bmsCategoryService.getByCodeAndSpId(inCategory.getCode(), spId);
        if (bmsCategory == null) {
            log.warn("对象更新失败,对象不存在 CategoryVo:{}", inCategory.getCode());
            registProcess(data, order);
            return;
        }
        if (!sysInPassage.getCpId().equals(bmsCategory.getCpId())) {
            log.error("更新失败,产品包不属于当前cp packageCode:{}", bmsCategory.getCode());
            updateStatus(order, inCategory, StatusUtils.fail("更新失败栏目不属于当前cp"));
            return;
        }
        BmsCategoryInjectionReq bmsCategoryCommonReq = new BmsCategoryInjectionReq(inCategory);

        bmsCategoryCommonReq.setId(bmsCategory.getId());
        //不允许更新bms的extraCode
        bmsCategoryCommonReq.setExtraCode(null);
        CommonResponse<Boolean> commonResponse = bmsCategoryFeignClient.updateById(bmsCategoryCommonReq);
        if (TextConstants.OK.equals(commonResponse.getCode())) {
            updateStatus(order, inCategory, StatusUtils.success());
        } else {
            log.error("对象更新失败. code: {} data: {}", inCategory.getCode(), commonResponse.toString());
            updateStatus(order, inCategory, StatusUtils.fail(commonResponse.getMessage()));
            return;
        }

    }

    @Override
    public void registProcess(Object data, InOrder order) {
        // 初始化cp
        InCategory inCategory = (InCategory) data;
        initializeCp(inCategory);
        // 新增到 bms表
        // 授权的sp信息
        if (allSysAuthorizationList.size() == 0) {
            log.error("cp未授权sp,栏目注入失败, inCategoryCode:{}", inCategory.getCode());
            updateStatus(order, inCategory, StatusUtils.fail("cp未授权sp,栏目注入失败"));
            return;
        }
//        BmsCategory bmsCategoryDb = bmsCategoryService.getByCode(inCategory.getCode());
//        if (bmsCategoryDb != null) {
//            log.warn("栏目已存在. code:{}", inCategory.getCode());
//            updateProcess(data, order);
//            return;
//        }
        for (SysAuthorization authorization : allSysAuthorizationList) {
            //判断栏目对应的sp是否存在
            BmsCategory bmsCategoryDb = bmsCategoryService.getByCodeAndSpId(inCategory.getCode(), authorization.getSpId());
            if (bmsCategoryDb != null) {
                log.warn("栏目对应的sp已存在. code:{}, sp:{}", inCategory.getCode(), authorization.getSpId());
                updateProcess(data, order, authorization.getSpId());
//                updateStatus(order, inCategory, StatusUtils.fail("栏目对应的sp已存在"));
                continue;
            }

            BmsCategoryInjectionReq bmsCategoryCommonReq = new BmsCategoryInjectionReq(inCategory);
            bmsCategoryCommonReq.setCpId(sysCp.getId());
            bmsCategoryCommonReq.setCpName(sysCp.getName());
            bmsCategoryCommonReq.setSpId(authorization.getSpId());
            bmsCategoryCommonReq.setSpName(authorization.getSpName());
            bmsCategoryCommonReq.setCspId(order.getCspId());
            String parentCode = bmsCategoryCommonReq.getParentCode();
            if (!StrUtils.isParentCodeBlank(parentCode)) {
                BmsCategory bmsCategory = bmsCategoryService.getByCodeAndSpId(parentCode, authorization.getSpId());
                if (bmsCategory == null) {
                    log.error("新增子栏目失败,父栏目不存在.parentCode:{}", parentCode);
                    updateStatus(order, inCategory, StatusUtils.fail("新增子栏目失败,父栏目不存在"));
                    continue;
                }
                bmsCategoryCommonReq.setParentId(bmsCategory.getId());
            } else {
                // 无父栏目 自身就是父栏目
                bmsCategoryCommonReq.setParentId(0L);
                bmsCategoryCommonReq.setParentCode("0");
            }
            CommonResponse<Boolean> save = bmsCategoryFeignClient.save(bmsCategoryCommonReq);
            if (TextConstants.OK.equals(save.getCode())) {
                updateStatus(order, inCategory, StatusUtils.success());
            } else {
                updateStatus(order, inCategory, StatusUtils.fail(save.getMessage()));
                log.error("栏目注入错误: CommonResponse {}, code:{}", save.toString(), inCategory.getCode());
            }
        }
    }

    /**
     * 栏目的删除:
     * 1. 先检查用没有以当前栏目为父栏目的栏目.若有则不能删除
     * 2. 再检查要删除的栏目的发布状态,回收成功和待发布可以删除
     * 3. 再检查bms_category_channel和bms_category_content有没有当前的栏目的关系,没有则可以删除
     *
     * @param data  要删除的对象
     * @param order 工单对象
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void deleteProcess(Object data, InOrder order) {
        InCategory inCategory = (InCategory) data;
        List<BmsCategory> bmsCategoryList = bmsCategoryService.listByCode(inCategory.getCode());
        if (bmsCategoryList.size() == 0) {
            log.warn("删除结束, 栏目不存在, code:{}", inCategory.getCode());
            updateStatus(order, inCategory, StatusUtils.success());
            return;
        }
        if (!sysInPassage.getCpId().equals(bmsCategoryList.get(0).getCpId())) {
            log.error("删除失败,产品包不属于当前cp packageCode:{}", bmsCategoryList.get(0).getCode());
            updateStatus(order, inCategory, StatusUtils.fail("删除失败栏目不属于当前cp"));
            return;
        }
        BmsCategory bmsCategoryParent = bmsCategoryService.getByParentCode(inCategory.getCode());
        if (bmsCategoryParent != null) {
            log.error("删除失败,当前栏目存在子栏目, code :{}", inCategory.getCode());
            updateStatus(order, inCategory, StatusUtils.fail("删除失败,当前栏目存在子栏目"));
            return;
        }
        for (BmsCategory bmsCategory : bmsCategoryList) {
            // 不是待发布 并且 不是回收成功 则不能删除
            if (!PublishStatusEnums.checkDeleteStatus(bmsCategory.getPublishStatus())) {
                log.error("删除失败,发布状态不是待发布和回收成功发布失败, code :{} spId:{}", inCategory.getCode(), bmsCategory.getSpId());
                updateStatus(order, inCategory, StatusUtils.fail("删除失败,发布状态不是待发布和回收成功发布失败"));
                return;
            }
            // 锁定不能删除
            if (LockStatusEnums.LOCKED.getCode().equals(bmsCategory.getLockStatus())) {
                log.error("删除失败,栏目已锁定, code :{} spId:{}", inCategory.getCode(), bmsCategory.getSpId());
                updateStatus(order, inCategory, StatusUtils.fail("删除失败,栏目已锁定"));
                return;
            }
        }
        // 可以删除   删除内容和栏目的关系  删除图片
        // 删自身
        bmsCategoryService.removeByCode(inCategory.getCode());
        // 删栏目类容关系
        List<BmsCategoryContent> bmsCategoryContentList = bmsCategoryContentService.listByCategoryCode(inCategory.getCode());
        if (bmsCategoryContentList.size() > 0) {
            // 取到id列表
            List<Long> contentIds = bmsCategoryContentList.stream().map(BmsCategoryContent::getBmsContentId).collect(Collectors.toList());
            // 根据id列表查询数据
            List<BmsContent> bmsContents = bmsContentService.listByIds(contentIds);
            // 移除栏目
            bmsContentService.removeMappingCategoryByIds(bmsContents, bmsCategoryList);
            bmsCategoryContentService.removeByCategoryCode(inCategory.getCode());
        }

        //删栏目频道关系
        List<BmsCategoryChannel> bmsCategoryChannelList = bmsCategoryChannelService.listByCategoryCode(inCategory.getCode());
        if (bmsCategoryChannelList.size() > 0) {
            // 获取频道id
            List<Long> channelIds = bmsCategoryChannelList.stream().map(BmsCategoryChannel::getBmsChannelId).collect(Collectors.toList());
            // 根据频道id获取数据
            List<BmsChannel> bmsChannels = bmsChannelService.listByIds(channelIds);
            // 移除栏目code
            bmsChannelService.removeMappingCategoryByIds(bmsChannels, bmsCategoryList);
            bmsCategoryChannelService.removeByCategoryCode(inCategory.getCode());
        }

        // 删图片
        cmsPictureService.removeByContentCode(inCategory.getCode());
        updateStatus(order, inCategory, StatusUtils.success());

    }

    /**
     * 更新工单表状态 多表更新 事务操作
     *
     * @param inCategory 子工单对象
     * @param status     处理状态
     */
    public void updateStatus(InOrder order, InCategory inCategory, StatusUtils.Status status) {
        // 子工单对象直接通过id更新状态
        inCategoryService.updateStatusById(inCategory.getId(), status);
        inOrderMappingsService.updateStatus(order.getCorrelateId(), TableNameConstants.IN_CATEGORY_TABLE, inCategory.getId(), status);

    }


    @Override
    public void updateStatus(InOrder order, Object data, StatusUtils.Status status) {
        InCategory inCategory = (InCategory) data;
        // 子工单对象直接通过id更新状态
        updateStatus(order, inCategory, status);
    }

    @Override
    public boolean deleteProcessIsExist(Object data, InOrder order) {
        return true;
    }

    /**
     * 栏目注入时cp不变 但还是要调用initSysConfig方法 要初始化cp后面的配置
     */
    public void initializeCp(InCategory data) {
        if (InPassageTypeEnum.TOW.getCode().equals(sysInPassage.getCpSourceType())) {
            initCpByCpId(sysInPassage.getCpId());
        }
    }
}
