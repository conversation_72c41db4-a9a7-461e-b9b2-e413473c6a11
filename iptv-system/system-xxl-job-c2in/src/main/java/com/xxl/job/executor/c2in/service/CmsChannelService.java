package com.xxl.job.executor.c2in.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.pukka.iptv.common.data.model.cms.CmsChannel;

/**
 *
 * @author: tan
 * @date: 2021年8月27日 上午10:01:17
 */

public interface CmsChannelService extends IService<CmsChannel> {
    /**
     * 根据code删除
     * @param code code
     */
    void removeByCode(String code);

    /**
     * 根据code查询
     * @param code code
     * @return
     */
    CmsChannel getByCodeCpid(String code, Long cpId);


    CmsChannel getByCode(String code);
    /**
     * 根据code统计
     * @param code code
     * @return
     */
    Long countByCode(String code);
}


