package com.xxl.job.executor.c2in.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.pukka.iptv.common.data.model.OutAuto;
import com.pukka.iptv.common.data.model.in.InOrderMappings;
import com.xxl.job.executor.c2in.common.util.StatusUtils;

import java.util.List;

/**
 *
 * @author: tan
 * @date: 2021-8-24 10:46:41
 */

public interface InOrderMappingsService extends IService<InOrderMappings> {
    /**
     * 更新对象的状态
     *
     * @param correlateId 主工单id
     * @param tableName 表名
     * @param status 状态
     * @param tableRowId 表数据id
     */
    void updateStatus(String correlateId, String tableName , Long tableRowId, StatusUtils.Status status);


    List<InOrderMappings> listByInOrderId(String inOrderId);

    OutAuto getOutAutoByInOrderId(String inOrderId);

    OutAuto getOutAutoExtendByInOrderId(String inOrderId);
}


