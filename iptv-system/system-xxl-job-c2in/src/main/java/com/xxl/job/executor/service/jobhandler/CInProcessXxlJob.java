package com.xxl.job.executor.service.jobhandler;

import com.xxl.job.core.handler.annotation.XxlJob;
import com.xxl.job.executor.c2in.job.FeedBackJob;
import com.xxl.job.executor.c2in.job.ParseJob;
import com.xxl.job.executor.c2in.job.UpdateInResultJob;
import com.xxl.job.executor.c2in.service.InOrderAPIService;
import com.xxl.job.executor.c2in.service.InOrderService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * @ClassName CInProcessXxlJob
 * @Description xxl 媒资注入任务管理
 * <AUTHOR>
 * @Date 2021/10/8 9:48
 * @Version
 */
@Component
@Slf4j
public class CInProcessXxlJob {

    @Autowired
    private ParseJob parseJob;


    @Autowired
    private UpdateInResultJob updateInResultJob;

    @Autowired
    private FeedBackJob feedBackJob;

    /**
     * 解析处理任务
     * @throws Exception
     */
    @XxlJob("parseJobHandler")
    public void parseJobHandler() throws Exception{
        parseJob.inOrderParse();
    }

    /**
     * 更新异步处理状态任务
     * @throws Exception
     */
    @XxlJob("updateInResultJobHandler")
    public void updateInResultJobHandler() throws Exception{
        updateInResultJob.executeJob();
    }

    /**
     * 反馈处理任务
     * @throws Exception
     */
    @XxlJob("feedBackJobHandler")
    public void feedBackJobHandler() throws Exception{
        feedBackJob.executeJob();
    }
}
