package com.xxl.job.executor.c2in.common.enums;

public enum ElementTypeAutoOrderPackageProgramEnums {

    Category(4, "Package"),
    Program(3, "Program"),
    Movie(2, "Movie"),
    Picture(1, "Picture"),
    NullElemet(99, "NullElemet");

    private final Integer code;
    private final String info;


    public static Integer getCodeByInfo(String info) {
        ElementTypeAutoOrderPackageProgramEnums[] actionEnums = values();
        for (ElementTypeAutoOrderPackageProgramEnums element : actionEnums) {
            if (element.getInfo().equals(info)) {
                return element.getCode();
            }
        }
        return null;
    }

    public static String getInfoByCode(Integer code) {
        ElementTypeAutoOrderPackageProgramEnums[] actionEnums = values();
        for (ElementTypeAutoOrderPackageProgramEnums element : actionEnums) {
            if (element.getCode().intValue() == code.intValue()) {
                return element.getInfo();
            }
        }
        return null;
    }

    ElementTypeAutoOrderPackageProgramEnums(Integer code, String info)
    {
        this.code = code;
        this.info = info;
    }

    public Integer getCode()
    {
        return code;
    }

    public String getInfo()
    {
        return info;
    }
}
