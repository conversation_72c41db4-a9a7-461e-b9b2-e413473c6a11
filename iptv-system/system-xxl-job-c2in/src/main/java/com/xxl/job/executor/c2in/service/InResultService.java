package com.xxl.job.executor.c2in.service;


import com.baomidou.mybatisplus.extension.service.IService;
import com.pukka.iptv.common.data.model.in.InResult;

import java.util.List;

/**
 *
 * @author: tan
 * @date: 2021-8-24 10:50:00
 */

public interface InResultService extends IService<InResult> {


    List<InResult>  getFeedBackList();

    boolean updateState(InResult inResult);

    InResult getByInOrderId(String inOrderId);
}


