package com.xxl.job.executor.c2in.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.pukka.iptv.common.data.model.cms.CmsMovie;

/**
 *
 * @author: tan
 * @date: 2021年8月27日 上午10:01:04
 */

public interface CmsMovieService extends IService<CmsMovie> {

    /**
     * 清除movie表的关系
     *
     * @param contentCode
     * @param movieCode
     */
    void removeMappingByContentCode(String contentCode, String movieCode);

    Long countByResourceCode(String code);

    CmsMovie getByResourceCode(String code);

    CmsMovie getByResourceCodeCpId(String code, Long cpId);

    void removeByResourceCode(String resourceCode);

    void updateMediaSpec(String mediaSpec,String code);
}


