package com.xxl.job.executor.c2in.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.pukka.iptv.common.api.feign.sys.DownloadFeignClient;
import com.pukka.iptv.common.base.enums.MovieTypeEnums;
import com.pukka.iptv.common.base.vo.CommonResponse;
import com.pukka.iptv.common.data.dto.CmsProgramDto;
import com.pukka.iptv.common.data.model.cms.CmsResource;
import com.xxl.job.executor.c2in.common.constant.TextConstants;
import com.xxl.job.executor.c2in.common.enums.ContentStatusEnums;
import com.xxl.job.executor.c2in.common.exception.EmptyFtpFileException;
import com.xxl.job.executor.c2in.mapper.CmsResourceMapper;
import com.xxl.job.executor.c2in.service.CmsResourceService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.stream.Collectors;

/**
 *
 * @author: liuli
 * @date: 2021年9月17日 下午9:42:06
 */

@Service
@Slf4j
public class CmsResourceServiceImpl extends ServiceImpl<CmsResourceMapper, CmsResource> implements CmsResourceService {

    @Autowired
    DownloadFeignClient downloadFeignClient;

    @Override
    public void removeMappingByContentCode(String contentCode, String movieCode) {
        LambdaUpdateWrapper<CmsResource> updateWrapper = Wrappers.lambdaUpdate();
        updateWrapper.eq(CmsResource::getContentCode, contentCode).eq(CmsResource::getCode, movieCode);
        updateWrapper.set(CmsResource::getContentCode, null)
                .set(CmsResource::getContentStatus, ContentStatusEnums.AWAIT_RELEVANCY.getCode())
                .set(CmsResource::getContentType, null)
                .set(CmsResource::getContentId, null);
        this.update(updateWrapper);
    }

    @Override
    public void removeByCode(String code) {
        CmsResource cmsResource = getByCode(code);
        if (cmsResource != null) {
            if (cmsResource.getStorageId() != null && cmsResource.getStorageId() != -1) {
                CommonResponse<String> commonResponse = null;
                try {
                    // 删文件
                    commonResponse = downloadFeignClient.fileDelete(cmsResource.getFileUrl(), cmsResource.getStorageId());
                } catch (Exception e) {
                    log.error("源片介质已被删除");
                    throw new EmptyFtpFileException("源片介质已被删除");
                }
                if (!TextConstants.OK.equals(commonResponse.getCode())) {
                    log.error("文件删除失败. 数据库回滚 url:{} msg:{}", cmsResource.getFileUrl(), commonResponse.toString());
                    throw new RuntimeException("文件删除失败 url:" + cmsResource.getFileUrl() + commonResponse.getMessage());
                }
            }
            LambdaQueryWrapper<CmsResource> queryWrapper = Wrappers.lambdaQuery();
            queryWrapper.eq(CmsResource::getId, cmsResource.getId());
            remove(queryWrapper);
        }
    }

    @Override
    public CmsResource getByCode(String code) {
        LambdaQueryWrapper<CmsResource> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.eq(CmsResource::getCode, code).last(TextConstants.LIMIT_ONE);
        return this.getOne(queryWrapper);
    }

    @Override
    public CmsProgramDto listByContentIdAndContentType(Long contentId, Integer contentType) {
        CmsProgramDto cmsProgramDto = new CmsProgramDto();
        List<CmsResource> cmsResourceList = this.list(Wrappers.<CmsResource>lambdaQuery()
                .eq(CmsResource::getContentId, contentId)
                .eq(CmsResource::getContentType, contentType));
        if (CollectionUtils.isNotEmpty(cmsResourceList)) {
            List<String> movIds = cmsResourceList.stream()
                    .filter(cmsResource -> MovieTypeEnums.RELEASE.getCode().equals(cmsResource.getType()))
                    .map(cmsResource -> String.valueOf(cmsResource.getId()))
                    .collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(movIds)) {
                cmsProgramDto.setMovIds(movIds.get(0));
            }
            List<String> previewIds = cmsResourceList.stream()
                    .filter(cmsResource -> MovieTypeEnums.PREVIEW.getCode().equals(cmsResource.getType()))
                    .map(cmsResource -> String.valueOf(cmsResource.getId()))
                    .collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(previewIds)) {
                cmsProgramDto.setPreviewIds(previewIds.get(0));
            }
        }
        return cmsProgramDto;
    }

    @Override
    public void updateMediaSpec(String mediaSpec, String code) {
        this.update(Wrappers.<CmsResource>lambdaUpdate().set(CmsResource::getMediaSpec,mediaSpec).eq(CmsResource::getCode,code));
    }
}


