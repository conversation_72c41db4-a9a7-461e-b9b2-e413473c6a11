package com.xxl.job.executor.c2in.common.util;

import org.apache.commons.logging.Log;
import org.apache.commons.logging.LogFactory;

import java.io.*;
import java.nio.charset.Charset;

public  class FileUtil {
	protected static Log log = LogFactory.getLog(FileUtil.class);
	private static int GB = 1024 * 1024 * 1024;//定义GB的计算常量
    private static int MB = 1024 * 1024;//定义MB的计算常量
    private static int KB = 1024;//定义KB的计算常量
    public static  void copyFile(File fileFrom,String fileTo) throws IOException{//copy文件从fileFrom到fileTo
	  FileInputStream in=null;
		OutputStream out =null;
		try {
			 in = new FileInputStream(fileFrom);
			 out = new FileOutputStream(fileTo);
			byte[] bytes = new byte[1024];
			while (in.read(bytes) > 0) {
				out.write(bytes);
			}
			in.close();
			out.close();
		} catch (Exception e) {
			  log.error(e.getMessage());
		}finally{
			if(!in.equals(null)){
				in.close();
			}
			if(!out.equals(null)){
				out.close();
			}
		}
		
	}
    
    public static String readFile(String filePath){
    	//Read   the   file   into   a   string   buffer,   then   return   as   a   string.
        StringBuffer buf = null;//the   intermediary,   mutable   buffer
        BufferedReader breader = null;//reader   for   the   template   files
        String tmpret = "";
        try {
            breader = new BufferedReader(new InputStreamReader(new FileInputStream((filePath)), Charset.forName("utf-8")));
            buf = new StringBuffer();
            while (breader.ready()) {
                buf.append((char) breader.read());
            }
            breader.close();
            tmpret = buf.toString();
        }
        catch (Exception e) {
            e.printStackTrace();
        }
        return tmpret;
    }

    /** 
     * 删除文件 
     * @param filePath String 文件路径及名称 如c:/fqf.txt
     * @return boolean 
     */ 
    public static boolean delFile(String filePath) { 
    	boolean flag = false;
        try { 
            filePath = filePath.toString().replace("\\", "/"); 
            File myDelFile = new File(filePath);
            if (myDelFile.isFile() && myDelFile.exists()) {
            	myDelFile.delete();
            	flag = true;
            }
        } 
        catch (Exception e) { 
            System.out.println("删除文件操作出错"); 
        }
        return flag;
    }
    
    public static void appendFile(String fileUrl,String content){
    	try 
	    {
    		String tmpFilepre = fileUrl.substring(0, fileUrl.lastIndexOf("/"));// "d:/temp/jcheck/20130607"   		
    		File f = new File(tmpFilepre);
    		f.mkdirs();//创建目录
    		
		     //打开一个写文件器，构造函数中的第二个参数true表示以追加形式写文件
		     FileWriter writer = new FileWriter(fileUrl, true);
		     writer.write(content + "\r\n");
		     writer.close();
	    } 
	    catch (IOException e) 
	    {
	    	
	    }
    }
    
    /* 写文件 */
    public static void WriteFile(String fileUrl,String content){   	
    	try 
	    {
    		String tmpFilepre = fileUrl.substring(0, fileUrl.lastIndexOf("/"));// "d:/temp/jcheck/20130607"   		
    		File f = new File(tmpFilepre);
    		f.mkdirs();//创建目录
		     
		     OutputStreamWriter out = new OutputStreamWriter(new FileOutputStream(fileUrl),"UTF-8");
		     out.write(content);
		     out.flush();
		     out.close();
	    } 
	    catch (IOException e) 
	    {
	    	
	    }
    }
    
    /**
     * 字节转换
     * @param KSize
     * @return
     */
    public static String ByteConversionGBMBKB(Integer KSize)
    {
        if (KSize / GB >= 1)//如果当前Byte的值大于等于1GB
            return  String.valueOf( Math.round(KSize / (float)GB)) + "G";//将其转换成GB
        else if (KSize / MB >= 1)//如果当前Byte的值大于等于1MB
        	return String.valueOf( Math.round(KSize / (float)MB))+ "M";//将其转换成MB
        else if (KSize / KB >= 1)//如果当前Byte的值大于等于1KB
        	return String.valueOf( Math.round(KSize / (float)KB)) + "K";//将其转换成KGB
        else
            return KSize.toString() + "B";//显示Byte值
    }

	public static long freeSpace(File hintPath) {
        String absolutePath = hintPath.getAbsolutePath();
        return new File(absolutePath.substring(0, absolutePath.indexOf(File.separator))).getFreeSpace();
	}

    public static void touch(String filePath, long size) throws Exception {
        File dir = new File(filePath).getParentFile();
        if (!dir.exists()) {
            dir.mkdirs();
        }
        RandomAccessFile f = new RandomAccessFile(filePath, "rw");
        f.setLength(size);
    }

    public static void makeSure(String dir) {
        File dirFile = new File(dir).getParentFile();
        makeSure(dirFile);
    }

    public static void makeSure(File dirFile) {
        if (!dirFile.exists()) {
            if (!dirFile.mkdirs()) {
                throw new RuntimeException("create dir error:" + dirFile.getAbsolutePath());
            }
        }
    }
}
