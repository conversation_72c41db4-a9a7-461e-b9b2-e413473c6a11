package com.xxl.job.executor.c2in.model;

import com.baomidou.mybatisplus.annotation.*;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.NoArgsConstructor;
import lombok.EqualsAndHashCode;
import lombok.Data;

import java.util.Date;

/**
 *
 * @author: liuli
 * @date: 2021年10月12日 上午9:47:52
 */

@Data
@EqualsAndHashCode
@JsonIgnoreProperties(value = { "handler" })
@NoArgsConstructor
@TableName(value = "bms_category_content",autoResultMap=true)
public class BmsCategoryContent extends Model<BmsCategoryContent> implements java.io.Serializable{

    private static final long serialVersionUID = 1L;
	/**id*/
	@TableField(value = "id")
    @TableId(type = IdType.AUTO)
    @ApiModelProperty(value="id",dataType="Long",name="id")
    private Long id;
	/**bms content表ID*/
	@TableField(value = "bms_content_id", fill = FieldFill.INSERT)
    @ApiModelProperty(value="bms content表ID",dataType="Long",name="bmsContentId")
    private Long bmsContentId;
	/**cms 表code*/
	@TableField(value = "cms_content_code")
    @ApiModelProperty(value="cms 表code",dataType="String",name="cmsContentCode")
    private String cmsContentCode;
	/**媒资类型*/
	@TableField(value = "content_type")
    @ApiModelProperty(value="媒资类型",dataType="Integer",name="contentType")
    private Integer contentType;
	/**category表的ID*/
	@TableField(value = "category_id")
    @ApiModelProperty(value="category表的ID",dataType="Long",name="categoryId")
    private Long categoryId;
	/**category表的Code*/
	@TableField(value = "category_code")
    @ApiModelProperty(value="category表的Code",dataType="String",name="categoryCode")
    private String categoryCode;
	/**栏目名称*/
	@TableField(value = "category_name")
    @ApiModelProperty(value="栏目名称",dataType="String",name="categoryName")
    private String categoryName;
	/**spId*/
	@TableField(value = "sp_id")
    @ApiModelProperty(value="spId",dataType="Long",name="spId")
    private Long spId;
	/**SP名称*/
	@TableField(value = "sp_name")
    @ApiModelProperty(value="SP名称",dataType="String",name="spName")
    private String spName;
	/**状态 0：失效 1：有效*/
	@TableField(value = "status")
    @ApiModelProperty(value="状态 0：失效 1：有效",dataType="Integer",name="status")
    private Integer status;
	/**创建时间*/
	@TableField(value = "create_time")
    @ApiModelProperty(value="创建时间",dataType="Date",name="createTime")
    @JsonFormat(shape = JsonFormat.Shape.STRING,pattern = "yyyy-MM-dd HH:mm:ss",timezone="GMT+8")
    private Date createTime;
	/**更新时间*/
	@TableField(value = "update_time")
    @ApiModelProperty(value="更新时间",dataType="Date",name="updateTime")
    @JsonFormat(shape = JsonFormat.Shape.STRING,pattern = "yyyy-MM-dd HH:mm:ss",timezone="GMT+8")
    private Date updateTime;
	/**排序号*/
	@TableField(value = "sequence")
    @ApiModelProperty(value="排序号",dataType="Integer",name="sequence")
    private Integer sequence;
	/**媒资名称*/
	@TableField(value = "content_name")
    @ApiModelProperty(value="媒资名称",dataType="String",name="contentName")
    private String contentName;
	/**publishStatus*/
	@TableField(value = "publish_status")
    @ApiModelProperty(value="publishStatus",dataType="Integer",name="publishStatus")
    private Integer publishStatus;
	/**关系发布通道名称，用逗号分隔*/
	@TableField(value = "out_passage_names")
    @ApiModelProperty(value="关系发布通道名称，用逗号分隔",dataType="String",name="outPassageNames")
    private String outPassageNames;
	/**发布通道ids集合，用逗号分隔*/
	@TableField(value = "out_passage_ids")
    @ApiModelProperty(value="发布通道ids集合，用逗号分隔",dataType="String",name="outPassageIds")
    private String outPassageIds;
	/**来源 1：专线注入 2：人工绑定*/
	@TableField(value = "source")
    @ApiModelProperty(value="来源 1：专线注入 2：人工绑定",dataType="Integer",name="source")
    private Integer source;
	/**关系发布时间*/
	@TableField(value = "publish_time")
    @ApiModelProperty(value="关系发布时间",dataType="String",name="publishTime")
    private String publishTime;
	/**关系发布描述*/
	@TableField(value = "publish_description")
    @ApiModelProperty(value="关系发布描述",dataType="String",name="publishDescription")
    private String publishDescription;
	/**定时发布，时间存在则代表已经设定为定时发布*/
	@TableField(value = "timed_publish")
    @ApiModelProperty(value="定时发布，时间存在则代表已经设定为定时发布",dataType="String",name="timedPublish")
    private String timedPublish;
	/**绑定人员*/
	@TableField(value = "creator_name")
    @ApiModelProperty(value="绑定人员",dataType="String",name="creatorName")
    private String creatorName;
	/**creatorId*/
	@TableField(value = "creator_id")
    @ApiModelProperty(value="creatorId",dataType="Long",name="creatorId")
    private Long creatorId;

}
