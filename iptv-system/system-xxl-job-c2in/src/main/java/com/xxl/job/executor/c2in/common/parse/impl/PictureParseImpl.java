package com.xxl.job.executor.c2in.common.parse.impl;

import com.google.common.base.Strings;
import com.pukka.iptv.common.base.constant.RedisKeyConstants;
import com.pukka.iptv.common.core.util.DateUtils;
import com.pukka.iptv.common.core.util.SpringUtils;
import com.pukka.iptv.common.core.util.StringUtils;
import com.pukka.iptv.common.data.model.in.InPicture;
import com.pukka.iptv.common.data.model.sys.SysNat;
import com.pukka.iptv.common.redis.service.RedisService;
import com.xxl.job.executor.c2in.common.enums.ActionEnums;
import com.xxl.job.executor.c2in.common.exception.ParserException;
import com.xxl.job.executor.c2in.common.parse.IParse;
import com.xxl.job.executor.c2in.common.util.ManageHelper;
import org.dom4j.Attribute;
import org.dom4j.Element;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * @ClassName PictureParseImpl
 * @Description 图片解析
 * <AUTHOR>
 * @Date 2021/8/27 15:25
 * @Version
 */
public class PictureParseImpl implements IParse<Element, InPicture> {


    private RedisService redisService = SpringUtils.getBean(RedisService.class);

    @Override
    public InPicture get(Element element) {
        if (element == null)
            return null;
        Attribute actionAttr = element.attribute("Action");
        if (actionAttr == null) {
            throw new ParserException("Picture 缺少'Action'属性值");
        }
        InPicture inPicture = new InPicture();

        inPicture.setAction(ActionEnums.getCodeByInfo(actionAttr.getValue()));
        Attribute codeAttr = element.attribute("Code");
        if (codeAttr == null || Strings.isNullOrEmpty(codeAttr.getValue())) {
            throw new ParserException("Picture 缺少'Code'属性值");
        }
        inPicture.setCode(codeAttr.getValue());

        Attribute idAttr = element.attribute("ID");
        if (idAttr == null || Strings.isNullOrEmpty(idAttr.getValue())) {
            throw new ParserException("Picture 缺少'ID'属性值");
        }
        inPicture.setCorrelateId(idAttr.getValue());

        List<?> propertysEle = element.elements("Property");
        if (propertysEle == null)
            return inPicture;

        Attribute nameAttr;
        String text;
        for (Object aPropertysEle : propertysEle) {
            Element property = (Element) aPropertysEle;
            nameAttr = property.attribute("Name");
            if (nameAttr == null || StringUtils.isEmpty(nameAttr.getValue())) {
                if(ManageHelper.isRegistXML(inPicture.getAction())){
                    throw new ParserException("Picture属性列表中缺少'Name'属性名称");
                }
            }
            text = property.getTextTrim();
            String value = nameAttr.getValue();
            if (value.equals("FileURL")) {
                inPicture.setFileUrl(text);
                //需要做ip代理转换处理
//                Map<String, SysNat> sysNatMap = redisService.getCacheMap(RedisKeyConstants.SYS_NAT_KEY);
//                for(Map.Entry<String, SysNat> entry : sysNatMap.entrySet()){
//                    SysNat sysNat =entry.getValue();
//                    if(text.contains(sysNat.getNatSource())){
//                        inPicture.setFileUrl(text.replace(sysNat.getNatSource(), sysNat.getNatTarget()));
//                    }
//                }

                continue;
            }
            if (value.equals("Description")) {
                inPicture.setDescription(text);
            }
        }
        if(!"DELETE".equals(actionAttr.getValue())){
            if (Strings.isNullOrEmpty(inPicture.getFileUrl())) {
                throw new ParserException("Picture属性列表中缺少'FileURL'属性值");
            }
        }else{
            inPicture.setFileUrl("");
        }
        return inPicture;
    }
}
