package com.xxl.job.executor.c2in.vo;

import com.xxl.job.executor.c2in.common.enums.PublishStatusEnums;
import com.xxl.job.executor.c2in.common.enums.SourceEnums;
import com.xxl.job.executor.c2in.model.BmsCategory;
import com.xxl.job.executor.c2in.model.BmsCategoryContent;
import com.xxl.job.executor.c2in.model.BmsContent;
import io.swagger.annotations.ApiModel;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;
import lombok.NoArgsConstructor;
import java.util.Date;
import lombok.Data;
import com.fasterxml.jackson.annotation.JsonFormat;

/**
 *
 * @author: liuli
 * @date: 2021年9月2日 下午3:34:52
 */

@Data
@AllArgsConstructor
@JsonIgnoreProperties(value = { "handler" })
@ApiModel("BmsCategoryContentVo")
public class BmsCategoryContentVo extends BmsCategoryContent implements java.io.Serializable{

    public BmsCategoryContentVo(BmsCategory bmsCategory, BmsContent bmsContent) {
        this.setBmsContentId(bmsContent.getId());
        this.setCmsContentCode(bmsContent.getCmsContentCode());
        this.setContentType(bmsContent.getContentType());
        this.setCategoryId(bmsCategory.getId());
        this.setCategoryCode(bmsCategory.getCode());
        this.setCategoryName(bmsCategory.getName());
        this.setSpId(bmsContent.getSpId());
        this.setSpName(bmsContent.getSpName());
        this.setStatus(bmsContent.getStatus());
        this.setContentName(bmsContent.getName());
        this.setPublishStatus(PublishStatusEnums.WAITPUBLISH.getCode());
        this.setOutPassageIds(bmsCategory.getOutPassageIds());
        this.setOutPassageNames(bmsCategory.getOutPassageNames());
        this.setSource(SourceEnums.INJECT.valueOf());
    }
}
