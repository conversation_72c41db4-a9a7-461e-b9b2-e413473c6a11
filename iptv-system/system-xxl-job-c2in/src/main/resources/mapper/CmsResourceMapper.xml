<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//ibatis.apache.org//DTD Mapper 3.0//EN" "http://ibatis.apache.org/dtd/ibatis-3-mapper.dtd">


<!-- 不使用namespace的话sql搜索定位会比较方便 -->
<mapper namespace="com.pukka.iptv.manage.mapper.CmsResourceMapper">

	<!-- 表名 -->
	<sql id="tableName">
        cms_resource
    </sql>
    
    <sql id="columns">
        `id`,`code`,`name`,`file_url`,`cp_id`,`cp_name`,`content_id`,`content_status`,`content_code`,`content_type`,`type`,`bit_rate_type`,`movie_head_duration`,`movie_tail_duration`,`status`,`duration`,`file_size`,`storage_name`,`storage_id`,`source`,`md5`,`create_time`,`update_time`,`creator_name`,`creator_id`,`envelope`,`bitrate_type`,`video_codec`,`video_bitrate`,`resolution`,`frame_rate`,`audio_codec`,`audio_bitrate`,`media_spec`,`source_drm_type`,`dest_drm_type`,`audio_type`,`screen_format`,`closed_captioning`,`vid`,`fileid`,`width`,`height`,`mediainfo`
    </sql>
    
    <sql id="where">
        <where>
	        <if test="null != id">
            AND `id` = #{id}
            </if>
	        <if test="null != code and '' != code">
            AND `code` = #{code}
            </if>
	        <if test="null != name and '' != name">
            AND `name` = #{name}
            </if>
	        <if test="null != fileUrl and '' != fileUrl">
            AND `file_url` = #{fileUrl}
            </if>
	        <if test="null != cpId">
            AND `cp_id` = #{cpId}
            </if>
	        <if test="null != cpName and '' != cpName">
            AND `cp_name` = #{cpName}
            </if>
	        <if test="null != contentId">
            AND `content_id` = #{contentId}
            </if>
	        <if test="null != contentStatus">
            AND `content_status` = #{contentStatus}
            </if>
	        <if test="null != contentCode and '' != contentCode">
            AND `content_code` = #{contentCode}
            </if>
	        <if test="null != contentType">
            AND `content_type` = #{contentType}
            </if>
	        <if test="null != type">
            AND `type` = #{type}
            </if>
	        <if test="null != bitRateType">
            AND `bit_rate_type` = #{bitRateType}
            </if>
	        <if test="null != movieHeadDuration">
            AND `movie_head_duration` = #{movieHeadDuration}
            </if>
	        <if test="null != movieTailDuration">
            AND `movie_tail_duration` = #{movieTailDuration}
            </if>
	        <if test="null != status">
            AND `status` = #{status}
            </if>
	        <if test="null != duration">
            AND `duration` = #{duration}
            </if>
	        <if test="null != fileSize">
            AND `file_size` = #{fileSize}
            </if>
	        <if test="null != storageName and '' != storageName">
            AND `storage_name` = #{storageName}
            </if>
	        <if test="null != storageId">
            AND `storage_id` = #{storageId}
            </if>
	        <if test="null != source">
            AND `source` = #{source}
            </if>
	        <if test="null != md5 and '' != md5">
            AND `md5` = #{md5}
            </if>
	        <if test="null != createTime and '' != createTime">
            AND `create_time` = #{createTime}
            </if>
	        <if test="null != updateTime and '' != updateTime">
            AND `update_time` = #{updateTime}
            </if>
	        <if test="null != creatorName and '' != creatorName">
            AND `creator_name` = #{creatorName}
            </if>
	        <if test="null != creatorId">
            AND `creator_id` = #{creatorId}
            </if>
	        <if test="null != envelope and '' != envelope">
            AND `envelope` = #{envelope}
            </if>
<!--	        <if test="null != bitrateType and '' != bitrateType">-->
<!--            AND `bitrate_type` = #{bitrateType}-->
<!--            </if>-->
	        <if test="null != videoCodec and '' != videoCodec">
            AND `video_codec` = #{videoCodec}
            </if>
	        <if test="null != videoBitrate and '' != videoBitrate">
            AND `video_bitrate` = #{videoBitrate}
            </if>
	        <if test="null != resolution and '' != resolution">
            AND `resolution` = #{resolution}
            </if>
	        <if test="null != frameRate">
            AND `frame_rate` = #{frameRate}
            </if>
	        <if test="null != audioCodec and '' != audioCodec">
            AND `audio_codec` = #{audioCodec}
            </if>
	        <if test="null != audioBitrate and '' != audioBitrate">
            AND `audio_bitrate` = #{audioBitrate}
            </if>
	        <if test="null != mediaSpec and '' != mediaSpec">
            AND `media_spec` = #{mediaSpec}
            </if>
	        <if test="null != sourceDrmType">
            AND `source_drm_type` = #{sourceDrmType}
            </if>
	        <if test="null != destDrmType">
            AND `dest_drm_type` = #{destDrmType}
            </if>
	        <if test="null != audioType">
            AND `audio_type` = #{audioType}
            </if>
	        <if test="null != screenFormat">
            AND `screen_format` = #{screenFormat}
            </if>
	        <if test="null != closedCaptioning">
            AND `closed_captioning` = #{closedCaptioning}
            </if>
            <if test="null != vid">
                AND `vid` = #{vid}
            </if>
            <if test="null != fileid">
                AND `fileid` = #{fileid}
            </if>
            <if test="null != width">
                AND `width` = #{width}
            </if>
            <if test="null != height">
                AND `height` = #{height}
            </if>
            <if test="null != mediainfo">
                AND `mediainfo` = #{mediainfo}
            </if>
        </where>
    </sql>

</mapper>

