<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">


<!-- 不使用namespace的话sql搜索定位会比较方便 -->
<mapper namespace="com.xxl.job.executor.c2out.mapper.sys.OutOrderItemMapper">
    <update id="orderRepublish">
        UPDATE out_order_item${code}
        SET STATUS = 1,
        RESULT = NULL,
        STATUS_DESCRIPTION = '',
        error_description = ''
        <where>
            ID IN
            <foreach item="item" index="index" collection="ids"
                     open="(" separator="," close=")">#{item}
            </foreach>
        </where>
    </update>
    <update id="updateCmdFileUrl" parameterType="string">
        UPDATE out_order_item${code}
        SET CMD_FILE_URL = #{cmdfileurl}
        WHERE correlate_id = #{correlateId}
    </update>
    <select id="findListByPage" resultType="com.pukka.iptv.common.data.model.OutOrderItem">
        SELECT *
        FROM out_order_item${code}
        <trim prefix="where" prefixOverrides="and|or">
            <if test="outOrderItemVo.showName != null">
                `show_name` LIKE CONCAT('%',#{outOrderItemVo.showName},'%')
            </if>
            <if test="outOrderItemVo.action != null">
                and action = #{outOrderItemVo.action}
            </if>
            <if test="outOrderItemVo.contentType != null">
                and content_type = #{outOrderItemVo.contentType}
            </if>
            <if test="outOrderItemVo.creatorName != null">
                and `creator_name` LIKE CONCAT('%',#{outOrderItemVo.creatorName},'%')
            </if>
            <if test="startTime != null and startTime.trim() != ''">
                <![CDATA[   and DATE_FORMAT(create_time, '%Y/%m/%d %H:%i:%S') >=  DATE_FORMAT(#{startTime}, '%Y/%m/%d %H:%i:%S')   ]]>
            </if>
            <if test="endTime != null and endTime.trim() != ''">
                <![CDATA[  and DATE_FORMAT(create_time, '%Y/%m/%d %H:%i:%S') <= DATE_FORMAT(#{endTime}, '%Y/%m/%d %H:%i:%S')    ]]>
            </if>
            <if test="outOrderItemVo.status != null">
                and status = #{outOrderItemVo.status}
            </if>
            <if test="outOrderItemVo.bmsSpChannelId != null">
                and bms_sp_channel_id = #{outOrderItemVo.bmsSpChannelId}
            </if>
            <if test="outOrderItemVo.lspId != null">
                and lsp_id = #{outOrderItemVo.lspId}
            </if>
            <if test="outOrderItemVo.bmsContentId != null">
                and bms_content_id = #{outOrderItemVo.bmsContentId}
            </if>
        </trim>
        ORDER BY create_time DESC
        LIMIT #{page}, #{size}
    </select>
    <select id="findList" resultType="long">
        SELECT COUNT(1)
        FROM out_order_item${code}
        <trim prefix="where" prefixOverrides="and|or">
            <if test="outOrderItemVo.showName != null">
                `show_name` LIKE CONCAT('%',#{outOrderItemVo.showName},'%')
            </if>
            <if test="outOrderItemVo.action != null">
                and action = #{outOrderItemVo.action}
            </if>
            <if test="outOrderItemVo.contentType != null">
                and content_type = #{outOrderItemVo.contentType}
            </if>
            <if test="outOrderItemVo.creatorName != null">
                and `creator_name` LIKE CONCAT('%',#{outOrderItemVo.creatorName},'%')
            </if>
            <if test="startTime != null and startTime.trim() != ''">
                <![CDATA[   and DATE_FORMAT(create_time, '%Y-%m-%d %H:%i:%S') >=  DATE_FORMAT(#{startTime}, '%Y-%m-%d %H:%i:%S')   ]]>
            </if>
            <if test="endTime != null and endTime.trim() != ''">
                <![CDATA[  and DATE_FORMAT(create_time, '%Y-%m-%d %H:%i:%S') <= DATE_FORMAT(#{endTime}, '%Y-%m-%d %H:%i:%S')    ]]>
            </if>
            <if test="outOrderItemVo.status != null">
                and status = #{outOrderItemVo.status}
            </if>
            <if test="outOrderItemVo.bmsSpChannelId != null">
                and bms_sp_channel_id = #{outOrderItemVo.bmsSpChannelId}
            </if>
            <if test="outOrderItemVo.lspId != null">
                and lsp_id = #{outOrderItemVo.lspId}
            </if>
            <if test="outOrderItemVo.bmsContentId != null">
                and bms_content_id = #{outOrderItemVo.bmsContentId}
            </if>
        </trim>
        ORDER BY create_time DESC
    </select>
    <!-- 通过id，type查询contentCode -->
    <select id="selectByIdType" resultType="com.pukka.iptv.common.data.vo.sys.OutOrderItemVo">
        select id as bms_content_id, code as bms_content_code, name as show_name,cp_id as cpId
        from ${tableName}
        where id = #{contentId}
    </select>
    <!-- 通过id，type查询Category contentCode -->
    <select id="selectCategory" resultType="com.pukka.iptv.common.data.model.OutOrderItem">
        select category_id as bms_content_id, category_code as bms_content_code
        from ${tableName}
        where id = #{contentId}
    </select>
    <!-- 通过id，type查询Package  contentCode -->
    <select id="selectPackage" resultType="com.pukka.iptv.common.data.model.OutOrderItem">
        select package_id as bms_content_id, package_code as bms_content_code
        from ${tableName}
        where id = #{contentId}
    </select>
    <!-- 查询主工单id -->
    <select id="getItem" resultType="com.pukka.iptv.common.data.model.OutOrderItem">
        select *
        from out_order_item_${param.outPassageCode}
        where correlate_id = #{param.correlateId}
    </select>
    <!-- 查询子工单结果 -->
    <select id="selectResult" resultType="com.pukka.iptv.common.data.model.OutOrderItem">
        select status, result, correlate_id
        from out_order_item_${param.outPassageCode}
        where base_order_id = #{param.baseOrderId}
    </select>
    <select id="selectCategoryCodeAndNameByIdType" resultType="com.pukka.iptv.common.data.vo.sys.OutOrderItemVo">
        select id as bms_content_id, category_code as bms_content_code, category_name as show_name,cp_id as cpId
        from ${tableName}
        where id = #{contentId}
    </select>
    <select id="selectPackageCodeAndNameByIdType" resultType="com.pukka.iptv.common.data.vo.sys.OutOrderItemVo">
        select id as bms_content_id, package_code as bms_content_code, package_name as show_name,cp_id as cpId
        from ${tableName}
        where id = #{contentId}
    </select>
    <select id="selectPhysicalChannelCodeAndNameByIdType"
            resultType="com.pukka.iptv.common.data.vo.sys.OutOrderItemVo">
        select id as bms_content_id, code as bms_content_code, channel_name as show_name,cp_id as cpId
        from ${tableName}
        where id = #{contentId}
    </select>
    <select id="selectScheduleAndNameByIdType"
            resultType="com.pukka.iptv.common.data.vo.sys.OutOrderItemVo">
        select id as bms_content_id, code as bms_content_code, program_name as show_name,cp_id as cpId
        from ${tableName}
        where id = #{contentId}
    </select>
    <select id="selectPictureAndNameByIdType" resultType="com.pukka.iptv.common.data.vo.sys.OutOrderItemVo">
        select id , bms_content_id as bms_content_id, code as bms_content_code, content_type as content_type,cp_id as cpId
        from ${tableName}
        where id = #{contentId}
    </select>
    <!-- 工单写入 -->
    <insert id="insertItem">
        insert into out_order_item_${param.outPassageCode}(base_order_id, correlate_id, action, show_name,
                                                           bms_content_id, bms_content_code, content_type, csp_id,
                                                           lsp_id, out_passage_id, out_passage_code, out_passage_name,
                                                           cmd_file_url, status, status_description, priority,
                                                           publish_type, result, error_description, retry_count,
                                                           creator_id, creator_name, bms_sp_channel_id,
                                                           bms_sp_channel_name,publish_content_type,sp_name)
        values (#{param.baseOrderId}, #{param.correlateId}, #{param.action}, #{param.showName}, #{param.bmsContentId},
                #{param.bmsContentCode}, #{param.contentType}, #{param.cspId}, #{param.lspId}, #{param.outPassageId},
                #{param.outPassageCode}, #{param.outPassageName},
                #{param.cmdFileUrl}, #{param.status}, #{param.statusDescription}, #{param.priority},
                #{param.publishType}, #{param.result}, #{param.errorDescription},
                #{param.retryCount}, #{param.creatorId}, #{param.creatorName}, #{param.bmsSpChannelId},
                #{param.bmsSpChannelName},#{param.publishContentType},#{param.spName})
    </insert>
    <!--工单反馈 -->
    <update id="updateResult">
        update out_order_item_${param.outPassageCode} set status=#{param.status}
        <if test="param.statusDescription != null and param.statusDescription != ''">
            , status_description=#{param.statusDescription}
        </if>
        <if test="param.result != null">
            ,result=#{param.result}
        </if>
        <if test="param.errorDescription != null and param.errorDescription != ''">
            , error_description=#{param.errorDescription}
        </if>
        where correlate_id =#{param.correlateId}
    </update>
    <!--工单回调 -->
    <update id="callBackResult">
        update out_order_item_${param.outPassageCode} set result=#{param.result}
        <if test="param.errorDescription != null and param.errorDescription != ''">
            , error_description=#{param.errorDescription}
        </if>
        where correlate_id =#{param.correlateId}
    </update>

</mapper>

