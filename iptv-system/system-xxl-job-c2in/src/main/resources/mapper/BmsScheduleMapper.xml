<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//ibatis.apache.org//DTD Mapper 3.0//EN" "http://ibatis.apache.org/dtd/ibatis-3-mapper.dtd">


<!-- 不使用namespace的话sql搜索定位会比较方便 -->
<mapper namespace="com.pukka.iptv.manage.mapper.BmsScheduleMapper">

	<!-- 表名 -->
	<sql id="tableName">
        bms_schedule
    </sql>
    
    <sql id="columns">
        `id`,`code`,`cms_channel_code`,`cms_channel_id`,`program_name`,`start_date`,`start_time`,`duration`,`storage_duration`,`status`,`description`,`genre`,`cp_id`,`cp_name`,`create_time`,`update_time`,`sp_id`,`sp_name`,`publish_status`,`publish_time`,`publish_description`
    </sql>
    
    <sql id="where">
        <where>
	        <if test="null != id">
            AND `id` = #{id}
            </if>
	        <if test="null != code and '' != code">
            AND `code` = #{code}
            </if>
	        <if test="null != cmsChannelCode and '' != cmsChannelCode">
            AND `cms_channel_code` = #{cmsChannelCode}
            </if>
	        <if test="null != cmsChannelId">
            AND `cms_channel_id` = #{cmsChannelId}
            </if>
	        <if test="null != programName and '' != programName">
            AND `program_name` = #{programName}
            </if>
	        <if test="null != startDate and '' != startDate">
            AND `start_date` = #{startDate}
            </if>
	        <if test="null != startTime and '' != startTime">
            AND `start_time` = #{startTime}
            </if>
	        <if test="null != duration and '' != duration">
            AND `duration` = #{duration}
            </if>
	        <if test="null != storageDuration">
            AND `storage_duration` = #{storageDuration}
            </if>
	        <if test="null != status">
            AND `status` = #{status}
            </if>
	        <if test="null != description and '' != description">
            AND `description` = #{description}
            </if>
	        <if test="null != genre and '' != genre">
            AND `genre` = #{genre}
            </if>
	        <if test="null != cpId">
            AND `cp_id` = #{cpId}
            </if>
	        <if test="null != cpName and '' != cpName">
            AND `cp_name` = #{cpName}
            </if>
	        <if test="null != createTime and '' != createTime">
            AND `create_time` = #{createTime}
            </if>
	        <if test="null != updateTime and '' != updateTime">
            AND `update_time` = #{updateTime}
            </if>
	        <if test="null != spId">
            AND `sp_id` = #{spId}
            </if>
	        <if test="null != spName and '' != spName">
            AND `sp_name` = #{spName}
            </if>
	        <if test="null != publishStatus">
            AND `publish_status` = #{publishStatus}
            </if>
	        <if test="null != publishTime and '' != publishTime">
            AND `publish_time` = #{publishTime}
            </if>
	        <if test="null != publishDescription and '' != publishDescription">
            AND `publish_description` = #{publishDescription}
            </if>
        </where>
    </sql>

</mapper>

