<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//ibatis.apache.org//DTD Mapper 3.0//EN" "http://ibatis.apache.org/dtd/ibatis-3-mapper.dtd">


<!-- 不使用namespace的话sql搜索定位会比较方便 -->
<mapper namespace="com.xxl.job.executor.c2in.mapper.InChannelMapper">

	<!-- 表名 -->
	<sql id="tableName">s
        in_channel
    </sql>
    
    <sql id="columns">
        `id`,`code`,`channel_number`,`name`,`call_sign`,`time_shift`,`storage_duration`,`time_shift_duration`,`description`,`country`,`state`,`city`,`zip_code`,`type`,`sub_type`,`language`,`status`,`start_time`,`end_time`,`macrovision`,`bilingual`,`vsp_code`,`create_time`,`update_time`,`result`,`error_description`,`action`,`correlate_id`
    </sql>
    
    <sql id="where">
        <where>
	        <if test="null != id">
            AND `id` = #{id}
            </if>
	        <if test="null != code and '' != code">
            AND `code` = #{code}
            </if>
	        <if test="null != channelNumber and '' != channelNumber">
            AND `channel_number` = #{channelNumber}
            </if>
	        <if test="null != name and '' != name">
            AND `name` = #{name}
            </if>
	        <if test="null != callSign and '' != callSign">
            AND `call_sign` = #{callSign}
            </if>
	        <if test="null != timeShift">
            AND `time_shift` = #{timeShift}
            </if>
	        <if test="null != storageDuration">
            AND `storage_duration` = #{storageDuration}
            </if>
	        <if test="null != timeShiftDuration">
            AND `time_shift_duration` = #{timeShiftDuration}
            </if>
	        <if test="null != description and '' != description">
            AND `description` = #{description}
            </if>
	        <if test="null != country and '' != country">
            AND `country` = #{country}
            </if>
	        <if test="null != state and '' != state">
            AND `state` = #{state}
            </if>
	        <if test="null != city and '' != city">
            AND `city` = #{city}
            </if>
	        <if test="null != zipCode and '' != zipCode">
            AND `zip_code` = #{zipCode}
            </if>
	        <if test="null != type">
            AND `type` = #{type}
            </if>
	        <if test="null != subType">
            AND `sub_type` = #{subType}
            </if>
	        <if test="null != language and '' != language">
            AND `language` = #{language}
            </if>
	        <if test="null != status">
            AND `status` = #{status}
            </if>
	        <if test="null != startTime and '' != startTime">
            AND `start_time` = #{startTime}
            </if>
	        <if test="null != endTime and '' != endTime">
            AND `end_time` = #{endTime}
            </if>
	        <if test="null != macrovision">
            AND `macrovision` = #{macrovision}
            </if>
	        <if test="null != bilingual">
            AND `bilingual` = #{bilingual}
            </if>
	        <if test="null != vspCode and '' != vspCode">
            AND `vsp_code` = #{vspCode}
            </if>
	        <if test="null != createTime and '' != createTime">
            AND `create_time` = #{createTime}
            </if>
	        <if test="null != updateTime and '' != updateTime">
            AND `update_time` = #{updateTime}
            </if>
	        <if test="null != result">
            AND `result` = #{result}
            </if>
	        <if test="null != errorDescription and '' != errorDescription">
            AND `error_description` = #{errorDescription}
            </if>
	        <if test="null != action">
            AND `action` = #{action}
            </if>
	        <if test="null != correlateId and '' != correlateId">
            AND `correlate_id` = #{correlateId}
            </if>
        </where>
    </sql>

</mapper>

