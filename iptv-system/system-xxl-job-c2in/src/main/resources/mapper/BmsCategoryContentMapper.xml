<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//ibatis.apache.org//DTD Mapper 3.0//EN" "http://ibatis.apache.org/dtd/ibatis-3-mapper.dtd">


<!-- 不使用namespace的话sql搜索定位会比较方便 -->
<mapper namespace="com.pukka.iptv.manage.mapper.BmsCategoryContentMapper">

	<!-- 表名 -->
	<sql id="tableName">
        bms_category_content
    </sql>
    
    <sql id="columns">
        `id`,`bms_content_id`,`cms_content_code`,`content_type`,`category_id`,`category_code`,`category_name`,`sp_id`,`sp_name`,`status`,`create_time`,`update_time`,`sequence`,`content_name`,`publish_status`,`out_passage_names`,`out_passage_ids`,`source`,`publish_time`,`publish_description`,`creator_name`,`creator_id`
    </sql>
    
    <sql id="where">
        <where>
	        <if test="null != id">
            AND `id` = #{id}
            </if>
	        <if test="null != bmsContentId">
            AND `bms_content_id` = #{bmsContentId}
            </if>
	        <if test="null != cmsContentCode and '' != cmsContentCode">
            AND `cms_content_code` = #{cmsContentCode}
            </if>
	        <if test="null != contentType">
            AND `content_type` = #{contentType}
            </if>
	        <if test="null != categoryId">
            AND `category_id` = #{categoryId}
            </if>
	        <if test="null != categoryCode and '' != categoryCode">
            AND `category_code` = #{categoryCode}
            </if>
	        <if test="null != categoryName and '' != categoryName">
            AND `category_name` = #{categoryName}
            </if>
	        <if test="null != spId">
            AND `sp_id` = #{spId}
            </if>
	        <if test="null != spName and '' != spName">
            AND `sp_name` = #{spName}
            </if>
	        <if test="null != status">
            AND `status` = #{status}
            </if>
	        <if test="null != createTime and '' != createTime">
            AND `create_time` = #{createTime}
            </if>
	        <if test="null != updateTime and '' != updateTime">
            AND `update_time` = #{updateTime}
            </if>
	        <if test="null != sequence">
            AND `sequence` = #{sequence}
            </if>
	        <if test="null != contentName and '' != contentName">
            AND `content_name` = #{contentName}
            </if>
	        <if test="null != publishStatus">
            AND `publish_status` = #{publishStatus}
            </if>
	        <if test="null != outPassageNames and '' != outPassageNames">
            AND `out_passage_names` = #{outPassageNames}
            </if>
	        <if test="null != outPassageIds">
            AND `out_passage_ids` = #{outPassageIds}
            </if>
	        <if test="null != source">
            AND `source` = #{source}
            </if>
	        <if test="null != publishTime and '' != publishTime">
            AND `publish_time` = #{publishTime}
            </if>
	        <if test="null != publishDescription and '' != publishDescription">
            AND `publish_description` = #{publishDescription}
            </if>
	        <if test="null != creatorName and '' != creatorName">
            AND `creator_name` = #{creatorName}
            </if>
	        <if test="null != creatorId">
            AND `creator_id` = #{creatorId}
            </if>
        </where>
    </sql>

</mapper>

