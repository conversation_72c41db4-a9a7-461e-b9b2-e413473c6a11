<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//ibatis.apache.org//DTD Mapper 3.0//EN" "http://ibatis.apache.org/dtd/ibatis-3-mapper.dtd">


<!-- 不使用namespace的话sql搜索定位会比较方便 -->
<mapper namespace="com.pukka.iptv.manage.mapper.SysDictionaryBaseMapper">

	<!-- 表名 -->
	<sql id="tableName">
        sys_dictionary_base
    </sql>
    
    <sql id="columns">
        `id`,`code`,`name`,`sequence`,`type`,`status`,`tenant_id`,`creator_id`,`creator_name`,`create_time`,`update_time`,`description`
    </sql>
    
    <sql id="where">
        <where>
	        <if test="null != id">
            AND `id` = #{id}
            </if>
	        <if test="null != code and '' != code">
            AND `code` = #{code}
            </if>
	        <if test="null != name and '' != name">
            AND `name` = #{name}
            </if>
	        <if test="null != sequence">
            AND `sequence` = #{sequence}
            </if>
	        <if test="null != type">
            AND `type` = #{type}
            </if>
	        <if test="null != status">
            AND `status` = #{status}
            </if>
	        <if test="null != tenantId">
            AND `tenant_id` = #{tenantId}
            </if>
	        <if test="null != creatorId">
            AND `creator_id` = #{creatorId}
            </if>
	        <if test="null != creatorName and '' != creatorName">
            AND `creator_name` = #{creatorName}
            </if>
	        <if test="null != createTime and '' != createTime">
            AND `create_time` = #{createTime}
            </if>
	        <if test="null != updateTime and '' != updateTime">
            AND `update_time` = #{updateTime}
            </if>
	        <if test="null != description and '' != description">
            AND `description` = #{description}
            </if>
        </where>
    </sql>

</mapper>

