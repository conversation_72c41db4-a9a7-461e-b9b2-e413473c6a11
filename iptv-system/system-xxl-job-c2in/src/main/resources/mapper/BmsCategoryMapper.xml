<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//ibatis.apache.org//DTD Mapper 3.0//EN" "http://ibatis.apache.org/dtd/ibatis-3-mapper.dtd">


<!-- 不使用namespace的话sql搜索定位会比较方便 -->
<mapper namespace="com.pukka.iptv.manage.mapper.BmsCategoryMapper">

	<!-- 表名 -->
	<sql id="tableName">
        bms_category
    </sql>
    
    <sql id="columns">
        `id`,`code`,`name`,`parent_code`,`parent_id`,`sequence`,`status`,`description`,`create_time`,`update_time`,`sp_id`,`sp_name`,`out_passage_ids`,`out_passage_names`,`publish_status`,`publish_time`,`publish_description`,`creator_name`,`creator_id`,`source`,`type`,`lock_status`
    </sql>
    
    <sql id="where">
        <where>
	        <if test="null != id">
            AND `id` = #{id}
            </if>
	        <if test="null != code and '' != code">
            AND `code` = #{code}
            </if>
	        <if test="null != name and '' != name">
            AND `name` = #{name}
            </if>
	        <if test="null != parentCode and '' != parentCode">
            AND `parent_code` = #{parentCode}
            </if>
	        <if test="null != parentId">
            AND `parent_id` = #{parentId}
            </if>
	        <if test="null != sequence">
            AND `sequence` = #{sequence}
            </if>
	        <if test="null != status">
            AND `status` = #{status}
            </if>
	        <if test="null != description and '' != description">
            AND `description` = #{description}
            </if>
	        <if test="null != createTime and '' != createTime">
            AND `create_time` = #{createTime}
            </if>
	        <if test="null != updateTime and '' != updateTime">
            AND `update_time` = #{updateTime}
            </if>
	        <if test="null != spId">
            AND `sp_id` = #{spId}
            </if>
	        <if test="null != spName and '' != spName">
            AND `sp_name` = #{spName}
            </if>
	        <if test="null != outPassageIds and '' != outPassageIds">
            AND `out_passage_ids` = #{outPassageIds}
            </if>
	        <if test="null != outPassageNames and '' != outPassageNames">
            AND `out_passage_names` = #{outPassageNames}
            </if>
	        <if test="null != publishStatus">
            AND `publish_status` = #{publishStatus}
            </if>
	        <if test="null != publishTime and '' != publishTime">
            AND `publish_time` = #{publishTime}
            </if>
	        <if test="null != publishDescription and '' != publishDescription">
            AND `publish_description` = #{publishDescription}
            </if>
	        <if test="null != creatorName and '' != creatorName">
            AND `creator_name` = #{creatorName}
            </if>
	        <if test="null != creatorId">
            AND `creator_id` = #{creatorId}
            </if>
	        <if test="null != source">
            AND `source` = #{source}
            </if>
	        <if test="null != type">
            AND `type` = #{type}
            </if>
	        <if test="null != lockStatus">
            AND `lock_status` = #{lockStatus}
            </if>
        </where>
    </sql>

</mapper>

