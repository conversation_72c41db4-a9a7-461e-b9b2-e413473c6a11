<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//ibatis.apache.org//DTD Mapper 3.0//EN" "http://ibatis.apache.org/dtd/ibatis-3-mapper.dtd">


<!-- 不使用namespace的话sql搜索定位会比较方便 -->
<mapper namespace="com.pukka.iptv.manage.mapper.sys.SysSpMapper">

	<!-- 表名 -->
	<sql id="tableName">
        sys_sp
    </sql>
    
    <sql id="columns">
        `id`,`name`,`code`,`creator_name`,`creator_id`,`operation_type`,`status`,`bms_sp_channel_id`,`bms_sp_channel_name`,`out_passage_names`,`out_passage_ids`,`create_time`,`update_time`
    </sql>
    
    <sql id="where">
        <where>
	        <if test="null != id">
            AND `id` = #{id}
            </if>
	        <if test="null != name and '' != name">
            AND `name` = #{name}
            </if>
	        <if test="null != code and '' != code">
            AND `code` = #{code}
            </if>
	        <if test="null != creatorName and '' != creatorName">
            AND `creator_name` = #{creatorName}
            </if>
	        <if test="null != creatorId">
            AND `creator_id` = #{creatorId}
            </if>
	        <if test="null != businessType">
            AND `business_type` = #{businessType}
            </if>
	        <if test="null != status">
            AND `status` = #{status}
            </if>
	        <if test="null != bmsSpChannelId">
            AND `bms_sp_channel_id` = #{bmsSpChannelId}
            </if>
	        <if test="null != bmsSpChannelName and '' != bmsSpChannelName">
            AND `bms_sp_channel_name` = #{bmsSpChannelName}
            </if>
	        <if test="null != outPassageNames and '' != outPassageNames">
            AND `out_passage_names` = #{outPassageNames}
            </if>
	        <if test="null != outPassageIds and '' != outPassageIds">
            AND `out_passage_ids` = #{outPassageIds}
            </if>
	        <if test="null != createTime and '' != createTime">
            AND `create_time` = #{createTime}
            </if>
	        <if test="null != updateTime and '' != updateTime">
            AND `update_time` = #{updateTime}
            </if>
        </where>
    </sql>

</mapper>

