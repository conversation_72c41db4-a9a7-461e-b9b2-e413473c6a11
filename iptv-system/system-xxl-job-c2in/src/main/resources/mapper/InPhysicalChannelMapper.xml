<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//ibatis.apache.org//DTD Mapper 3.0//EN" "http://ibatis.apache.org/dtd/ibatis-3-mapper.dtd">


<!-- 不使用namespace的话sql搜索定位会比较方便 -->
<mapper namespace="com.xxl.job.executor.c2in.mapper.InPhysicalChannelMapper">

	<!-- 表名 -->
	<sql id="tableName">
        in_physical_channel
    </sql>
    
    <sql id="columns">
        `id`,`code`,`channel_code`,`channel_id`,`bit_rate_type`,`multi_cast_ip`,`multi_cast_port`,`media_spec`,`result`,`error_description`,`create_time`,`update_time`,`action`,`correlate_id`
    </sql>
    
    <sql id="where">
        <where>
	        <if test="null != id">
            AND `id` = #{id}
            </if>
	        <if test="null != code and '' != code">
            AND `code` = #{code}
            </if>
	        <if test="null != channelCode and '' != channelCode">
            AND `channel_code` = #{channelCode}
            </if>
	        <if test="null != channelId">
            AND `channel_id` = #{channelId}
            </if>
	        <if test="null != bitRateType">
            AND `bit_rate_type` = #{bitRateType}
            </if>
	        <if test="null != multiCastIp and '' != multiCastIp">
            AND `multi_cast_ip` = #{multiCastIp}
            </if>
	        <if test="null != multiCastPort and '' != multiCastPort">
            AND `multi_cast_port` = #{multiCastPort}
            </if>
	        <if test="null != mediaSpec and '' != mediaSpec">
            AND `media_spec` = #{mediaSpec}
            </if>
	        <if test="null != result">
            AND `result` = #{result}
            </if>
	        <if test="null != errorDescription and '' != errorDescription">
            AND `error_description` = #{errorDescription}
            </if>
	        <if test="null != createTime and '' != createTime">
            AND `create_time` = #{createTime}
            </if>
	        <if test="null != updateTime and '' != updateTime">
            AND `update_time` = #{updateTime}
            </if>
	        <if test="null != action">
            AND `action` = #{action}
            </if>
	        <if test="null != correlateId and '' != correlateId">
            AND `correlate_id` = #{correlateId}
            </if>
        </where>
    </sql>

</mapper>

