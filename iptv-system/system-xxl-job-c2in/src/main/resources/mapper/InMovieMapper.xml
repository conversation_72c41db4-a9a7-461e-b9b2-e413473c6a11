<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//ibatis.apache.org//DTD Mapper 3.0//EN" "http://ibatis.apache.org/dtd/ibatis-3-mapper.dtd">


<!-- 不使用namespace的话sql搜索定位会比较方便 -->
<mapper namespace="com.xxl.job.executor.c2in.mapper.InMovieMapper">

	<!-- 表名 -->
	<sql id="tableName">
        in_movie
    </sql>
    
    <sql id="columns">
        `id`,`code`,`name`,`type`,`file_url`,`play_url`,`source_drm_type`,`dest_drm_type`,`audio_type`,`screen_format`,`closed_caption`,`media_spec`,`bir_rate_type`,`movie_head_duration`,`movie_tail_duration`,`result`,`error_description`,`create_time`,`update_time`,`status`,`duration`,`action`,`correlate_id`
    </sql>
    
    <sql id="where">
        <where>
	        <if test="null != id">
            AND `id` = #{id}
            </if>
	        <if test="null != code and '' != code">
            AND `code` = #{code}
            </if>
	        <if test="null != name and '' != name">
            AND `name` = #{name}
            </if>
	        <if test="null != type">
            AND `type` = #{type}
            </if>
	        <if test="null != fileUrl and '' != fileUrl">
            AND `file_url` = #{fileUrl}
            </if>
	        <if test="null != playUrl and '' != playUrl">
            AND `play_url` = #{playUrl}
            </if>
	        <if test="null != sourceDrmType">
            AND `source_drm_type` = #{sourceDrmType}
            </if>
	        <if test="null != destDrmType">
            AND `dest_drm_type` = #{destDrmType}
            </if>
	        <if test="null != audioType">
            AND `audio_type` = #{audioType}
            </if>
	        <if test="null != screenFormat">
            AND `screen_format` = #{screenFormat}
            </if>
	        <if test="null != closedCaption">
            AND `closed_caption` = #{closedCaption}
            </if>
	        <if test="null != mediaSpec and '' != mediaSpec">
            AND `media_spec` = #{mediaSpec}
            </if>
	        <if test="null != birRateType">
            AND `bir_rate_type` = #{birRateType}
            </if>
	        <if test="null != movieHeadDuration">
            AND `movie_head_duration` = #{movieHeadDuration}
            </if>
	        <if test="null != movieTailDuration">
            AND `movie_tail_duration` = #{movieTailDuration}
            </if>
	        <if test="null != result">
            AND `result` = #{result}
            </if>
	        <if test="null != errorDescription and '' != errorDescription">
            AND `error_description` = #{errorDescription}
            </if>
	        <if test="null != createTime and '' != createTime">
            AND `create_time` = #{createTime}
            </if>
	        <if test="null != updateTime and '' != updateTime">
            AND `update_time` = #{updateTime}
            </if>
	        <if test="null != status">
            AND `status` = #{status}
            </if>
	        <if test="null != duration">
            AND `duration` = #{duration}
            </if>
	        <if test="null != action">
            AND `action` = #{action}
            </if>
	        <if test="null != correlateId and '' != correlateId">
            AND `correlate_id` = #{correlateId}
            </if>
        </where>
    </sql>

</mapper>

