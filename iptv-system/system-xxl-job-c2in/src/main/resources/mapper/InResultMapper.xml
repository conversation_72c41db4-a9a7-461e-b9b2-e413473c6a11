<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//ibatis.apache.org//DTD Mapper 3.0//EN" "http://ibatis.apache.org/dtd/ibatis-3-mapper.dtd">


<!-- 不使用namespace的话sql搜索定位会比较方便 -->
<mapper namespace="com.xxl.job.executor.c2in.mapper.InResultMapper">

	<!-- 表名 -->
	<sql id="tableName">
        in_result
    </sql>
    
    <sql id="columns">
        `id`,`in_result_id`,`in_order_id`,`csp_id`,`lsp_id`,`correlate_id`,`cmd_result`,`result_file_url`,`request_time`,`response_time`,`result`,`error_description`,`web_site_code`,`order_status`
    </sql>
    
    <sql id="where">
        <where>
	        <if test="null != id">
            AND `id` = #{id}
            </if>
	        <if test="null != inResultId and '' != inResultId">
            AND `in_result_id` = #{inResultId}
            </if>
	        <if test="null != inOrderId and '' != inOrderId">
            AND `in_order_id` = #{inOrderId}
            </if>
	        <if test="null != cspId and '' != cspId">
            AND `csp_id` = #{cspId}
            </if>
	        <if test="null != lspId and '' != lspId">
            AND `lsp_id` = #{lspId}
            </if>
	        <if test="null != correlateId and '' != correlateId">
            AND `correlate_id` = #{correlateId}
            </if>
	        <if test="null != cmdResult">
            AND `cmd_result` = #{cmdResult}
            </if>
	        <if test="null != resultFileUrl and '' != resultFileUrl">
            AND `result_file_url` = #{resultFileUrl}
            </if>
	        <if test="null != requestTime and '' != requestTime">
            AND `request_time` = #{requestTime}
            </if>
	        <if test="null != responseTime and '' != responseTime">
            AND `response_time` = #{responseTime}
            </if>
	        <if test="null != result">
            AND `result` = #{result}
            </if>
	        <if test="null != errorDescription and '' != errorDescription">
            AND `error_description` = #{errorDescription}
            </if>
	        <if test="null != webSiteCode and '' != webSiteCode">
            AND `web_site_code` = #{webSiteCode}
            </if>
	        <if test="null != orderStatus">
            AND `order_status` = #{orderStatus}
            </if>
        </where>
    </sql>

</mapper>

