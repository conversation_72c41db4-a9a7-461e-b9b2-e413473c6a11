<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">


<!-- 不使用namespace的话sql搜索定位会比较方便 -->
<mapper namespace="com.xxl.job.executor.c2out.mapper.sys.OutOrderBaseMapper">

    <!--插入主工单 -->
    <insert id="insertBase" useGeneratedKeys="true" keyProperty="id">
        insert into out_order_base(out_passage_ids, sp_id, sp_name, bms_content_id,
                                   content_type, status, action, show_name,parameters, creator_id,
                                   creator_name, bms_sp_channel_id, bms_sp_channel_name)
        values (#{param.outPassageIds}, #{param.spId}, #{param.spName}, #{param.bmsContentId},
                #{param.contentType}, #{param.status}, #{param.action}, #{param.showName},#{param.parameters}, #{param.creatorId},
                #{param.creatorName}, #{param.bmsSpChannelId}, #{param.bmsSpChannelName})
    </insert>
    <!--批量插入主工单 -->
    <insert id="insertBatchBase" useGeneratedKeys="true" keyProperty="id">
        insert into out_order_base(out_passage_ids,sp_id,sp_name,bms_content_id,
        content_type,status,action,show_name,creator_id,
        creator_name,bms_sp_channel_id,bms_sp_channel_name)
        values
        <foreach collection="params" item="param" index="index" separator=",">
            (#{param.outPassageIds},#{param.spId},#{param.spName},#{param.bmsContentId},
            #{param.contentType},#{param.status},#{param.action},#{param.showName},#{param.creatorId},
            #{param.creatorName},#{param.bmsSpChannelId},#{param.bmsSpChannelName})
        </foreach>
    </insert>
    <select id="findListByPage" resultType="com.pukka.iptv.common.data.model.OutOrderItem">
        SELECT *
        FROM out_order_item${code}
        WHERE base_order_id = #{baseOrderId}
        <if test="null != status and status != ''">
            and status =#{status}
        </if>
        <if test="null != action and action != ''">
            and action = #{action}
        </if>
        ORDER BY create_time DESC
        LIMIT #{page}, #{size}
    </select>
    <select id="findList" resultType="long">
        SELECT COUNT(1)
        FROM out_order_item${code}
        WHERE base_order_id = #{baseOrderId}
        <if test="null != status and status != ''">
            and status =#{status}
        </if>
        <if test="null != action and action != ''">
            and action = #{action}
        </if>
        ORDER BY create_time DESC
    </select>
</mapper>

