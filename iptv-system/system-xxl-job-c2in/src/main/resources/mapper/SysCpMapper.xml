<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//ibatis.apache.org//DTD Mapper 3.0//EN" "http://ibatis.apache.org/dtd/ibatis-3-mapper.dtd">


<!-- 不使用namespace的话sql搜索定位会比较方便 -->
<mapper namespace="com.pukka.iptv.manage.mapper.sys.SysCpMapper">

	<!-- 表名 -->
	<sql id="tableName">
        sys_cp
    </sql>
    
    <sql id="columns">
        `id`,`name`,`code`,`create_time`,`update_time`,`status`,`storage_id`,`storage_name`,`use_transcoding`,`skip_check`,`is_mediare_pository`,`creator_name`,`creator_id`,`operation_type`
    </sql>
    
    <sql id="where">
        <where>
	        <if test="null != id">
            AND `id` = #{id}
            </if>
	        <if test="null != name and '' != name">
            AND `name` = #{name}
            </if>
	        <if test="null != code and '' != code">
            AND `code` = #{code}
            </if>
	        <if test="null != createTime and '' != createTime">
            AND `create_time` = #{createTime}
            </if>
	        <if test="null != updateTime and '' != updateTime">
            AND `update_time` = #{updateTime}
            </if>
	        <if test="null != status">
            AND `status` = #{status}
            </if>
	        <if test="null != storageId">
            AND `storage_id` = #{storageId}
            </if>
	        <if test="null != storageName and '' != storageName">
            AND `storage_name` = #{storageName}
            </if>
	        <if test="null != useTranscoding">
            AND `use_transcoding` = #{useTranscoding}
            </if>
	        <if test="null != skipCheck">
            AND `skip_check` = #{skipCheck}
            </if>
	        <if test="null != mediarePository">
            AND `is_mediare_pository` = #{mediarePository}
            </if>
	        <if test="null != creatorName and '' != creatorName">
            AND `creator_name` = #{creatorName}
            </if>
	        <if test="null != creatorId">
            AND `creator_id` = #{creatorId}
            </if>
	        <if test="null != businessType">
            AND `business_type` = #{businessType}
            </if>
        </where>
    </sql>

</mapper>

