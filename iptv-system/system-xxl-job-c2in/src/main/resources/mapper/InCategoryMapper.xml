<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//ibatis.apache.org//DTD Mapper 3.0//EN" "http://ibatis.apache.org/dtd/ibatis-3-mapper.dtd">


<!-- 不使用namespace的话sql搜索定位会比较方便 -->
<mapper namespace="com.xxl.job.executor.c2in.mapper.InCategoryMapper">

	<!-- 表名 -->
	<sql id="tableName">
        in_category
    </sql>
    
    <sql id="columns">
        `id`,`category_id`,`action`,`code`,`name`,`parent_code`,`parent_id`,`sequence`,`status`,`description`,`create_time`,`update_time`,`result`,`error_description`,`correlate_id`
    </sql>
    
    <sql id="where">
        <where>
	        <if test="null != id">
            AND `id` = #{id}
            </if>
	        <if test="null != categoryId and '' != categoryId">
            AND `category_id` = #{categoryId}
            </if>
	        <if test="null != action">
            AND `action` = #{action}
            </if>
	        <if test="null != code and '' != code">
            AND `code` = #{code}
            </if>
	        <if test="null != name and '' != name">
            AND `name` = #{name}
            </if>
	        <if test="null != parentCode and '' != parentCode">
            AND `parent_code` = #{parentCode}
            </if>
	        <if test="null != parentId">
            AND `parent_id` = #{parentId}
            </if>
	        <if test="null != sequence">
            AND `sequence` = #{sequence}
            </if>
	        <if test="null != status">
            AND `status` = #{status}
            </if>
	        <if test="null != description and '' != description">
            AND `description` = #{description}
            </if>
	        <if test="null != createTime and '' != createTime">
            AND `create_time` = #{createTime}
            </if>
	        <if test="null != updateTime and '' != updateTime">
            AND `update_time` = #{updateTime}
            </if>
	        <if test="null != result">
            AND `result` = #{result}
            </if>
	        <if test="null != errorDescription and '' != errorDescription">
            AND `error_description` = #{errorDescription}
            </if>
	        <if test="null != correlateId and '' != correlateId">
            AND `correlate_id` = #{correlateId}
            </if>
        </where>
    </sql>

</mapper>

