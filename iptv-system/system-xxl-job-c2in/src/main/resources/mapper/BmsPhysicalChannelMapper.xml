<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//ibatis.apache.org//DTD Mapper 3.0//EN" "http://ibatis.apache.org/dtd/ibatis-3-mapper.dtd">


<!-- 不使用namespace的话sql搜索定位会比较方便 -->
<mapper namespace="com.pukka.iptv.manage.mapper.BmsPhysicalChannelMapper">

	<!-- 表名 -->
	<sql id="tableName">
        bms_physical_channel
    </sql>
    
    <sql id="columns">
        `id`,`code`,`cms_channel_code`,`channel_name`,`cms_channel_id`,`multi_cast_ip`,`multi_cast_port`,`cp_id`,`cp_name`,`create_time`,`update_time`,`sp_id`,`sp_name`,`out_passage_ids`,`out_passage_names`,`publish_status`,`publish_time`,`publish_description`,`source`
    </sql>
    
    <sql id="where">
        <where>
	        <if test="null != id">
            AND `id` = #{id}
            </if>
	        <if test="null != code and '' != code">
            AND `code` = #{code}
            </if>
	        <if test="null != cmsChannelCode and '' != cmsChannelCode">
            AND `cms_channel_code` = #{cmsChannelCode}
            </if>
	        <if test="null != channelName and '' != channelName">
            AND `channel_name` = #{channelName}
            </if>
	        <if test="null != cmsChannelId">
            AND `cms_channel_id` = #{cmsChannelId}
            </if>
	        <if test="null != multiCastIp and '' != multiCastIp">
            AND `multi_cast_ip` = #{multiCastIp}
            </if>
	        <if test="null != multiCastPort and '' != multiCastPort">
            AND `multi_cast_port` = #{multiCastPort}
            </if>
	        <if test="null != cpId">
            AND `cp_id` = #{cpId}
            </if>
	        <if test="null != cpName and '' != cpName">
            AND `cp_name` = #{cpName}
            </if>
	        <if test="null != createTime and '' != createTime">
            AND `create_time` = #{createTime}
            </if>
	        <if test="null != updateTime and '' != updateTime">
            AND `update_time` = #{updateTime}
            </if>
	        <if test="null != spId">
            AND `sp_id` = #{spId}
            </if>
	        <if test="null != spName and '' != spName">
            AND `sp_name` = #{spName}
            </if>
	        <if test="null != outPassageIds and '' != outPassageIds">
            AND `out_passage_ids` = #{outPassageIds}
            </if>
	        <if test="null != outPassageNames and '' != outPassageNames">
            AND `out_passage_names` = #{outPassageNames}
            </if>
	        <if test="null != publishStatus">
            AND `publish_status` = #{publishStatus}
            </if>
	        <if test="null != publishTime and '' != publishTime">
            AND `publish_time` = #{publishTime}
            </if>
	        <if test="null != publishDescription and '' != publishDescription">
            AND `publish_description` = #{publishDescription}
            </if>
	        <if test="null != source">
            AND `source` = #{source}
            </if>
        </where>
    </sql>

</mapper>

