<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//ibatis.apache.org//DTD Mapper 3.0//EN" "http://ibatis.apache.org/dtd/ibatis-3-mapper.dtd">


<!-- 不使用namespace的话sql搜索定位会比较方便 -->
<mapper namespace="com.pukka.iptv.manage.mapper.CmsPhysicalChannelMapper">

	<!-- 表名 -->
	<sql id="tableName">
        cms_physical_channel
    </sql>
    
    <sql id="columns">
        `id`,`code`,`channel_code`,`channel_id`,`bit_rate_type`,`multi_cast_ip`,`multi_cast_port`,`media_spec`,`cp_id`,`cp_name`,`create_time`,`update_time`,`creator_name`,`creator_id`
    </sql>
    
    <sql id="where">
        <where>
	        <if test="null != id">
            AND `id` = #{id}
            </if>
	        <if test="null != code and '' != code">
            AND `code` = #{code}
            </if>
	        <if test="null != channelCode and '' != channelCode">
            AND `channel_code` = #{channelCode}
            </if>
	        <if test="null != channelId">
            AND `channel_id` = #{channelId}
            </if>
	        <if test="null != bitRateType">
            AND `bit_rate_type` = #{bitRateType}
            </if>
	        <if test="null != multiCastIp and '' != multiCastIp">
            AND `multi_cast_ip` = #{multiCastIp}
            </if>
	        <if test="null != multiCastPort and '' != multiCastPort">
            AND `multi_cast_port` = #{multiCastPort}
            </if>
	        <if test="null != mediaSpec and '' != mediaSpec">
            AND `media_spec` = #{mediaSpec}
            </if>
	        <if test="null != cpId">
            AND `cp_id` = #{cpId}
            </if>
	        <if test="null != cpName and '' != cpName">
            AND `cp_name` = #{cpName}
            </if>
	        <if test="null != createTime and '' != createTime">
            AND `create_time` = #{createTime}
            </if>
	        <if test="null != updateTime and '' != updateTime">
            AND `update_time` = #{updateTime}
            </if>
	        <if test="null != creatorName and '' != creatorName">
            AND `creator_name` = #{creatorName}
            </if>
	        <if test="null != creatorId">
            AND `creator_id` = #{creatorId}
            </if>
        </where>
    </sql>

</mapper>

