<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//ibatis.apache.org//DTD Mapper 3.0//EN" "http://ibatis.apache.org/dtd/ibatis-3-mapper.dtd">


<!-- 不使用namespace的话sql搜索定位会比较方便 -->
<mapper namespace="com.xxl.job.executor.c2in.mapper.InOrderMappingsMapper">

	<!-- 表名 -->
	<sql id="tableName">
        in_order_mappings
    </sql>
    
    <sql id="columns">
        `object_id`,`in_order_id`,`correlate_id`,`table_name`,`table_row_id`,`type`,`show_name`,`action`,`old_object_id`,`old_object_code`,`status`,`error_desc`,`csp_id`,`create_time`,`update_time`,`is_can_out`,`notic_time`
    </sql>
    
    <sql id="where">
        <where>
	        <if test="null != objectId and '' != objectId">
            AND `object_id` = #{objectId}
            </if>
	        <if test="null != inOrderId and '' != inOrderId">
            AND `in_order_id` = #{inOrderId}
            </if>
	        <if test="null != correlateId and '' != correlateId">
            AND `correlate_id` = #{correlateId}
            </if>
	        <if test="null != tableName and '' != tableName">
            AND `table_name` = #{tableName}
            </if>
	        <if test="null != tableRowId">
            AND `table_row_id` = #{tableRowId}
            </if>
	        <if test="null != type and '' != type">
            AND `type` = #{type}
            </if>
	        <if test="null != showName and '' != showName">
            AND `show_name` = #{showName}
            </if>
	        <if test="null != action and '' != action">
            AND `action` = #{action}
            </if>
	        <if test="null != oldObjectId and '' != oldObjectId">
            AND `old_object_id` = #{oldObjectId}
            </if>
	        <if test="null != oldObjectCode and '' != oldObjectCode">
            AND `old_object_code` = #{oldObjectCode}
            </if>
	        <if test="null != status">
            AND `status` = #{status}
            </if>
	        <if test="null != errorDesc and '' != errorDesc">
            AND `error_desc` = #{errorDesc}
            </if>
	        <if test="null != cspId and '' != cspId">
            AND `csp_id` = #{cspId}
            </if>
	        <if test="null != createTime and '' != createTime">
            AND `create_time` = #{createTime}
            </if>
	        <if test="null != updateTime and '' != updateTime">
            AND `update_time` = #{updateTime}
            </if>
	        <if test="null != canOut">
            AND `is_can_out` = #{canOut}
            </if>
	        <if test="null != noticTime and '' != noticTime">
            AND `notic_time` = #{noticTime}
            </if>
        </where>
    </sql>

</mapper>

