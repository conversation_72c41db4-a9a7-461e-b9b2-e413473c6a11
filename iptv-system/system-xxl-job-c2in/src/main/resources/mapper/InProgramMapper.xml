<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//ibatis.apache.org//DTD Mapper 3.0//EN" "http://ibatis.apache.org/dtd/ibatis-3-mapper.dtd">


<!-- 不使用namespace的话sql搜索定位会比较方便 -->
<mapper namespace="com.pukka.iptv.manage.mapper.InProgramMapper">

	<!-- 表名 -->
	<sql id="tableName">
        in_program
    </sql>
    
    <sql id="columns">
        `id`,`code`,`name`,`order_number`,`original_name`,`sort_name`,`search_name`,`actor_display`,`writer_display`,`original_country`,`language`,`release_year`,`org_air_date`,`licensing_window_start`,`licensing_window_end`,`display_as_new`,`display_as_last_chance`,`macrovision`,`description`,`pgm_category`,`pgm_snd_class`,`price_tax_in`,`status`,`source_type`,`series_flag`,`series_id`,`series_code`,`kpeople`,`director`,`script_writer`,`compere`,`guest`,`reporter`,`op_incharge`,`vsp_code`,`copy_right`,`content_provider`,`duration`,`result`,`error_description`,`rating`,`new_price`,`create_time`,`update_time`,`action`,`correlate_id`
    </sql>
    
    <sql id="where">
        <where>
	        <if test="null != id">
            AND `id` = #{id}
            </if>
	        <if test="null != code and '' != code">
            AND `code` = #{code}
            </if>
	        <if test="null != name and '' != name">
            AND `name` = #{name}
            </if>
	        <if test="null != orderNumber and '' != orderNumber">
            AND `order_number` = #{orderNumber}
            </if>
	        <if test="null != originalName and '' != originalName">
            AND `original_name` = #{originalName}
            </if>
	        <if test="null != sortName and '' != sortName">
            AND `sort_name` = #{sortName}
            </if>
	        <if test="null != searchName and '' != searchName">
            AND `search_name` = #{searchName}
            </if>
	        <if test="null != actorDisplay and '' != actorDisplay">
            AND `actor_display` = #{actorDisplay}
            </if>
	        <if test="null != writerDisplay and '' != writerDisplay">
            AND `writer_display` = #{writerDisplay}
            </if>
	        <if test="null != originalCountry and '' != originalCountry">
            AND `original_country` = #{originalCountry}
            </if>
	        <if test="null != language and '' != language">
            AND `language` = #{language}
            </if>
	        <if test="null != releaseYear and '' != releaseYear">
            AND `release_year` = #{releaseYear}
            </if>
	        <if test="null != orgAirDate and '' != orgAirDate">
            AND `org_air_date` = #{orgAirDate}
            </if>
	        <if test="null != licensingWindowStart and '' != licensingWindowStart">
            AND `licensing_window_start` = #{licensingWindowStart}
            </if>
	        <if test="null != licensingWindowEnd and '' != licensingWindowEnd">
            AND `licensing_window_end` = #{licensingWindowEnd}
            </if>
	        <if test="null != displayAsNew">
            AND `display_as_new` = #{displayAsNew}
            </if>
	        <if test="null != displayAsLastChance">
            AND `display_as_last_chance` = #{displayAsLastChance}
            </if>
	        <if test="null != macrovision">
            AND `macrovision` = #{macrovision}
            </if>
	        <if test="null != description and '' != description">
            AND `description` = #{description}
            </if>
	        <if test="null != pgmCategory and '' != pgmCategory">
            AND `pgm_category` = #{pgmCategory}
            </if>
	        <if test="null != pgmSndClass and '' != pgmSndClass">
            AND `pgm_snd_class` = #{pgmSndClass}
            </if>
	        <if test="null != priceTaxIn and '' != priceTaxIn">
            AND `price_tax_in` = #{priceTaxIn}
            </if>
	        <if test="null != status">
            AND `status` = #{status}
            </if>
	        <if test="null != sourceType">
            AND `source_type` = #{sourceType}
            </if>
	        <if test="null != seriesFlag">
            AND `series_flag` = #{seriesFlag}
            </if>
	        <if test="null != seriesId">
            AND `series_id` = #{seriesId}
            </if>
	        <if test="null != seriesCode and '' != seriesCode">
            AND `series_code` = #{seriesCode}
            </if>
	        <if test="null != kpeople and '' != kpeople">
            AND `kpeople` = #{kpeople}
            </if>
	        <if test="null != director and '' != director">
            AND `director` = #{director}
            </if>
	        <if test="null != scriptWriter and '' != scriptWriter">
            AND `script_writer` = #{scriptWriter}
            </if>
	        <if test="null != compere and '' != compere">
            AND `compere` = #{compere}
            </if>
	        <if test="null != guest and '' != guest">
            AND `guest` = #{guest}
            </if>
	        <if test="null != reporter and '' != reporter">
            AND `reporter` = #{reporter}
            </if>
	        <if test="null != opIncharge and '' != opIncharge">
            AND `op_incharge` = #{opIncharge}
            </if>
	        <if test="null != vspCode and '' != vspCode">
            AND `vsp_code` = #{vspCode}
            </if>
	        <if test="null != copyRight and '' != copyRight">
            AND `copy_right` = #{copyRight}
            </if>
	        <if test="null != contentProvider and '' != contentProvider">
            AND `content_provider` = #{contentProvider}
            </if>
	        <if test="null != duration">
            AND `duration` = #{duration}
            </if>
	        <if test="null != result">
            AND `result` = #{result}
            </if>
	        <if test="null != errorDescription and '' != errorDescription">
            AND `error_description` = #{errorDescription}
            </if>
	        <if test="null != rating and '' != rating">
            AND `rating` = #{rating}
            </if>
	        <if test="null != newPrice and '' != newPrice">
            AND `new_price` = #{newPrice}
            </if>
	        <if test="null != createTime and '' != createTime">
            AND `create_time` = #{createTime}
            </if>
	        <if test="null != updateTime and '' != updateTime">
            AND `update_time` = #{updateTime}
            </if>
	        <if test="null != action">
            AND `action` = #{action}
            </if>
	        <if test="null != correlateId and '' != correlateId">
            AND `correlate_id` = #{correlateId}
            </if>
        </where>
    </sql>

</mapper>

