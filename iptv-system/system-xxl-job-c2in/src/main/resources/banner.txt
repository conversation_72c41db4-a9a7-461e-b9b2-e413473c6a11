${AnsiColor.BRIGHT_GREEN}
Application Version: ${project.version}
Spring Boot Version: ${spring-boot.version}
Spring Application Name: ${spring.application.name}
                                 ,--,
                              ,---.'|                    ,---._      ,----..                                                                ,--.
 ,--,     ,--,  ,--,     ,--, |   | :                  .-- -.' \    /   /   \      ,---,.           ,----..        ,----,     ,---,       ,--.'|
 |'. \   / .`|  |'. \   / .`| :   : |                  |    |   :  /   .     :   ,'  .'  \         /   /   \     .'   .' \ ,`--.' |   ,--,:  : |
 ; \ `\ /' / ;  ; \ `\ /' / ; |   ' :                  :    ;   | .   /   ;.  \,---.' .' |        |   :     :  ,----,'    ||   :  :,`--.'`|  ' :
 `. \  /  / .'  `. \  /  / .' ;   ; '                  :        |.   ;   /  ` ;|   |  |: |        .   |  ;. /  |    :  .  ;:   |  '|   :  :  | |
  \  \/  / ./    \  \/  / ./  '   | |__                |    :   :;   |  ; \ ; |:   :  :  /        .   ; /--`   ;    |.'  / |   :  |:   |   \ | :
   \  \.'  /      \  \.'  /   |   | :.'|               :         |   :  | ; | ':   |    ;         ;   | ;      `----'/  ;  '   '  ;|   : '  '; |
    \  ;  ;        \  ;  ;    '   :    ;               |    ;   |.   |  ' ' ' :|   :     \        |   : |        /  ;  /   |   |  |'   ' ;.    ;
   / \  \  \      / \  \  \   |   |  ./            ___ l         '   ;  \; /  ||   |   . |        .   | '___    ;  /  /-,  '   :  ;|   | | \   |
  ;  /\  \  \    ;  /\  \  \  ;   : ;            /    /\    J   : \   \  ',  / '   :  '; |        '   ; : .'|  /  /  /.`|  |   |  ''   : |  ; .'
./__;  \  ;  \ ./__;  \  ;  \ |   ,/            /  ../  `..-    ,  ;   :    /  |   |  | ;         '   | '/  :./__;      :  '   :  ||   | '`--'
|   : / \  \  ;|   : / \  \  ;'---'             \    \         ;    \   \ .'   |   :   /          |   :    / |   :    .'   ;   |.' '   : |
;   |/   \  ' |;   |/   \  ' |                   \    \      ,'      `---`     |   | ,'            \   \ .'  ;   | .'      '---'   ;   |.'
`---'     `--` `---'     `--`                     "---....--'                  `----'               `---`    `---'                 '---'
