package com.xxl.job.executor.util;

import com.rabbitmq.client.Channel;
import com.rabbitmq.client.Connection;
import com.rabbitmq.client.ConnectionFactory;
import com.rabbitmq.client.GetResponse;
import com.xxl.job.executor.core.config.RabbitMqConfig;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import java.io.IOException;
import java.util.concurrent.TimeoutException;

/**
 * <AUTHOR>
 * @description:
 * @create 2021-08-31 12:29
 */
@Slf4j
@Component
public class RabbitMQUtils {
    @Autowired
    private RabbitMqConfig rabbitMqConfig;

    static class Config {
        public static volatile String QUEUE_HOST;// "*************";
        public static volatile int QUEUE_PORT;//5672;
        public static volatile String QUEUE_USER_NAME;//"admin";
        public static volatile String QUEUE_PASSWORD;//"new.bokong";
        public static volatile String QUEUE_VIRTUAL_HOST;//"iptv_cloud";
        public static volatile String ENCODING;
        public static volatile String ASK_TYPE;
    }


    private static volatile ConnectionFactory factory;
    private static volatile boolean isRuning = false;

    public void clear() {
        Config.QUEUE_HOST = null;
        Config.QUEUE_PORT = 0;
        Config.QUEUE_USER_NAME = null;
        Config.QUEUE_PASSWORD = null;
        Config.QUEUE_VIRTUAL_HOST = null;
        Config.ENCODING = null;
        Config.ASK_TYPE = null;
        isRuning = false;
    }

    //启动时初始化连接
    @PostConstruct
    private void init() {
        initConfig();
        initConnectionFactory();
    }

    private void initConfig() {
        String config = rabbitMqConfig.getAddresses().split(",")[0];
        Config.QUEUE_HOST = config.split(":")[0];
        Config.QUEUE_PORT = Integer.parseInt(config.split(":")[1]);
        Config.QUEUE_USER_NAME = rabbitMqConfig.getUsername();
        Config.QUEUE_PASSWORD = rabbitMqConfig.getPassword();
        Config.QUEUE_VIRTUAL_HOST = rabbitMqConfig.getVhost();
    }

    //连接mq
    private void initConnectionFactory() {
        try {
            if (Config.QUEUE_HOST == null || Config.QUEUE_PORT == 0 || Config.QUEUE_USER_NAME == null || Config.QUEUE_PASSWORD == null || Config.QUEUE_VIRTUAL_HOST == null || Config.ENCODING == null) {
                Config.ASK_TYPE = "yes";
            }
            if (factory != null) return;
            log.info("初始化mq连接池");
            factory = new ConnectionFactory();
            factory.setHost(Config.QUEUE_HOST);
            factory.setPort(Config.QUEUE_PORT);
            factory.setUsername(Config.QUEUE_USER_NAME);
            factory.setPassword(Config.QUEUE_PASSWORD);
            factory.setVirtualHost(Config.QUEUE_VIRTUAL_HOST);

//            factory.setRequestedChannelMax(20);
            factory.setRequestedFrameMax(0);
            factory.setAutomaticRecoveryEnabled(true);
            factory.setNetworkRecoveryInterval(10000); //attempt recovery every 10 seconds
            factory.setConnectionTimeout(1000 * 30);
            // 设置心跳为60秒
            factory.setRequestedHeartbeat(60);
            isRuning = true;

        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    public synchronized Connection getConnection() {
        if (!isRuning) {
            initConnectionFactory();
        }

        Connection connection = null;
        try {
            connection = factory.newConnection();
        } catch (IOException e) {
            log.error(String.valueOf(e));
            log.error("连接队列服务器失败，[host]:" + Config.QUEUE_HOST + " [port]:"
                    + Config.QUEUE_PORT + "[user]:" + Config.QUEUE_USER_NAME
                    + "[password]:" + Config.QUEUE_PASSWORD
                    + "[virtual_host]:" + Config.QUEUE_VIRTUAL_HOST, e);
        } catch (TimeoutException e) {
            log.error(String.valueOf(e));
            log.error("连接队列服务器超时，[host]:" + Config.QUEUE_HOST + " [port]:"
                    + Config.QUEUE_PORT + "[user]:" + Config.QUEUE_USER_NAME
                    + "[password]:" + Config.QUEUE_PASSWORD
                    + "[virtual_host]:" + Config.QUEUE_VIRTUAL_HOST, e);
        }
        return connection;
    }

    public void ackToQueue(GetResponse response, Channel channel) {
        if (Config.ASK_TYPE.equals("yes")) {
            //通知队列操作成功并从队列中删除该条消息
            try {
                String msg = new String(response.getBody(), "UTF-8");
                log.info("确认后删除该条消息,ack:{}", msg);
                channel.basicAck(response.getEnvelope().getDeliveryTag(), false);
            } catch (IOException e) {
                log.error(String.valueOf(e));
            }
        } else if (Config.ASK_TYPE.equals("no")) {
            log.info("未开启队列应答功能");
        } else {
            log.error("不能确定是否开启自动应答功能，默认不开启");
        }
    }

    public void nAckToQueue(GetResponse response, Channel channel) {
        try {
            channel.basicNack(response.getEnvelope().getDeliveryTag(), false, true);
        } catch (IOException e) {
            log.error(String.valueOf(e));
        }
    }
}
