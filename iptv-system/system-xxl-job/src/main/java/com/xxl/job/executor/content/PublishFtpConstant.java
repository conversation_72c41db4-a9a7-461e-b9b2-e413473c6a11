package com.xxl.job.executor.content;

import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @Date 2021/9/27 9:21 上午
 * @description: Ftp
 * @Version 1.0
 */
@Component
public class PublishFtpConstant {
    //url为ftp://xstorero:iptv!#$xsro@，地址需查库拼接
    public static final String ROOTURL_PREFIX = "ftp://xstore:iptv!#$xs@**************:6069/";

    /**
     * DirPrefix
     */
    public static final String DIR_CATEGORY_CHANNEL = "/Out/Distribute/CategoryChannel/";
    public static final String DIR_CATEGORY_CONTENT = "/Out/Distribute/CategoryContent/";
    public static final String DIR_CATEGORY = "/Out/Distribute/Category/";
    public static final String DIR_CHANNEL = "/Out/Distribute/Channel/";
    public static final String DIR_PACKAGE_CONTENT = "/Out/Distribute/PackageContent/";
    public static final String DIR_PACKAGE = "/Out/Distribute/Package/";
    public static final String DIR_PHYSICAL_CHANNEL = "/Out/Distribute/PhysicalChannel/";
    public static final String DIR_PICTURE = "/Out/Distribute/Picture/";
    public static final String DIR_PROGRAM = "/Out/Distribute/Program/";
    public static final String DIR_SCHEDULE = "/Out/Distribute/Schedule/";
    public static final String DIR_SERIES = "/Out/Distribute/Series/";
    public static final String OUT_PICTURE_FTP_PREFIX = "out_picture_ftp_prefix";
    public static final String OUT_MOVIE_FTP_PREFIX = "out_movie_ftp_prefix";
}
