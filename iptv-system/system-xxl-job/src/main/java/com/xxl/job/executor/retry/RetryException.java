package com.xxl.job.executor.retry;

import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @Date 2021/10/28 7:56 下午
 * @description:
 * @Version 1.0
 */
@Component
public class RetryException extends RuntimeException {
    public RetryException() {
        super();
    }

    public RetryException(String message) {
        super(message);
    }

    public RetryException(String message,Throwable cause){
        super(message,cause);
    }

    public RetryException(Throwable cause){
        super(cause);
    }

    public RetryException(String message,Throwable cause,boolean enableSuppression,boolean writableStackTrace){
        super(message, cause, enableSuppression, writableStackTrace);
    }
}
