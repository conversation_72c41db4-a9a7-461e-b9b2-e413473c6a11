package com.xxl.job.executor.service.jobhandler;

import com.pukka.iptv.common.api.feign.bms.BmsCategoryContentFeignClient;
import com.pukka.iptv.common.api.feign.bms.BmsContentFeignClient;
import com.pukka.iptv.common.api.feign.bms.BmsProgramFeignClient;
import com.pukka.iptv.common.api.feign.bms.BmsScheduleFeignClient;
import com.pukka.iptv.common.data.model.bms.BmsProgram;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

@Component
@Slf4j
public class ContentPublishXxlJob {

    @Autowired
    private BmsContentFeignClient bmsContentFeignClient;
    @Autowired
    private BmsProgramFeignClient bmsProgramFeignClient;
    @Autowired
    private BmsCategoryContentFeignClient bmsCategoryContentFeignClient;

    // 媒资定时发布
    @XxlJob("contentSchedulePublish")
    public void contentSchedulePublish() {
        log.info("内容定时发布执行器执行");
        bmsContentFeignClient.contentSchedulePublish();
    }


    // 子集定时发布
    @XxlJob("programSchedulePublish")
    public void programSchedulePublish() {
        log.info("子集定时发布执行器执行");
        bmsProgramFeignClient.programSchedulePublish();
    }

    // 栏目内容关系定时发布
    @XxlJob("categoryContentSchedulePublish")
    public void categoryContentSchedulePublish() {
        log.info("栏目内容关系定时发布执行器执行");
        bmsCategoryContentFeignClient.schedulePublish();
    }
}
