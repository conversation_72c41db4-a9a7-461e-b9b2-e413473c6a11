package com.xxl.job.executor.service.jobhandler;

import com.pukka.iptv.downloader.dispatcher.DispatcherCenter;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @Date 2021-10-15 10:21
 */
@Component
@Slf4j
public class DownloadXxlJob {
    @Autowired
    private DispatcherCenter dispatcherCenter;

    @XxlJob("download-dispatcher")
    public void dispatcher() {
        log.info("【下载模块XXL-JOB】调度");
        dispatcherCenter.dispatch();
    }
}
