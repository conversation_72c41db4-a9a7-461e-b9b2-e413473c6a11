<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//ibatis.apache.org//DTD Mapper 3.0//EN" "http://ibatis.apache.org/dtd/ibatis-3-mapper.dtd">


<!-- 不使用namespace的话sql搜索定位会比较方便 -->
<mapper namespace="com.xxl.job.executor.c2in.mapper.InOrderMapper">

	<!-- 表名 -->
	<sql id="tableName">
        in_order
    </sql>
    
    <sql id="columns">
        `id`,`correlate_id`,`action`,`show_name`,`in_passage_id`,`in_passage_name`,`priority`,`csp_id`,`lsp_id`,`status`,`description`,`create_time`,`update_time`,`cmd_file_url`,`retry_count`,`result`,`error_description`
    </sql>
    
    <sql id="where">
        <where>
	        <if test="null != id and '' != id">
            AND `id` = #{id}
            </if>
	        <if test="null != correlateId and '' != correlateId">
            AND `correlate_id` = #{correlateId}
            </if>
	        <if test="null != action">
            AND `action` = #{action}
            </if>
	        <if test="null != showName and '' != showName">
            AND `show_name` = #{showName}
            </if>
	        <if test="null != inPassageId">
            AND `in_passage_id` = #{inPassageId}
            </if>
	        <if test="null != inPassageName and '' != inPassageName">
            AND `in_passage_name` = #{inPassageName}
            </if>
	        <if test="null != priority">
            AND `priority` = #{priority}
            </if>
	        <if test="null != cspId and '' != cspId">
            AND `csp_id` = #{cspId}
            </if>
	        <if test="null != lspId and '' != lspId">
            AND `lsp_id` = #{lspId}
            </if>
	        <if test="null != status">
            AND `status` = #{status}
            </if>
	        <if test="null != description and '' != description">
            AND `description` = #{description}
            </if>
	        <if test="null != createTime and '' != createTime">
            AND `create_time` = #{createTime}
            </if>
	        <if test="null != updateTime and '' != updateTime">
            AND `update_time` = #{updateTime}
            </if>
	        <if test="null != cmdFileUrl and '' != cmdFileUrl">
            AND `cmd_file_url` = #{cmdFileUrl}
            </if>
	        <if test="null != retryCount">
            AND `retry_count` = #{retryCount}
            </if>
	        <if test="null != result">
            AND `result` = #{result}
            </if>
	        <if test="null != errorDescription and '' != errorDescription">
            AND `error_description` = #{errorDescription}
            </if>
        </where>
    </sql>

</mapper>

