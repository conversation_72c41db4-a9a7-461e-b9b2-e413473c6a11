<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//ibatis.apache.org//DTD Mapper 3.0//EN" "http://ibatis.apache.org/dtd/ibatis-3-mapper.dtd">


<!-- 不使用namespace的话sql搜索定位会比较方便 -->
<mapper namespace="com.xxl.job.executor.c2out.mapper.sys.OutResultMapper">

	<!-- 表名 -->
	<sql id="tableName">
        out_result
    </sql>
    
    <sql id="columns">
        `id`,`correlate_id`,`result_file_url`,`result`,`error_description`,`status`,`status_description`,`create_time`,`update_time`
    </sql>
    
    <sql id="where">
        <where>
	        <if test="null != id and '' != id">
            AND `id` = #{id}
            </if>
	        <if test="null != correlateId and '' != correlateId">
            AND `correlate_id` = #{correlateId}
            </if>
	        <if test="null != resultFileUrl and '' != resultFileUrl">
            AND `result_file_url` = #{resultFileUrl}
            </if>
	        <if test="null != result">
            AND `result` = #{result}
            </if>
	        <if test="null != errorDescription and '' != errorDescription">
            AND `error_description` = #{errorDescription}
            </if>
	        <if test="null != status">
            AND `status` = #{status}
            </if>
	        <if test="null != statusDescription and '' != statusDescription">
            AND `status_description` = #{statusDescription}
            </if>
	        <if test="null != createTime and '' != createTime">
            AND `create_time` = #{createTime}
            </if>
	        <if test="null != updateTime and '' != updateTime">
            AND `update_time` = #{updateTime}
            </if>
        </where>
    </sql>

</mapper>

