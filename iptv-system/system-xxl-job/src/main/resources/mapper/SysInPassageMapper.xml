<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//ibatis.apache.org//DTD Mapper 3.0//EN" "http://ibatis.apache.org/dtd/ibatis-3-mapper.dtd">


<!-- 不使用namespace的话sql搜索定位会比较方便 -->
<mapper namespace="com.xxl.job.executor.c2out.mapper.sys.SysInPassageMapper">

	<!-- 表名 -->
	<sql id="tableName">
        sys_in_passage
    </sql>

    <sql id="columns">
        `id`,`name`,`csp_id`,`report_url`,`creator_name`,`creator_id`,`priority`,`create_time`,`update_time`,`cp_id`,`cp_name`,`status`,`deal_count`,`movie_download_type`,order_publish_type,out_passage_ids,out_passage_names
    </sql>

    <sql id="where">
        <where>
	        <if test="null != id">
            AND `id` = #{id}
            </if>
	        <if test="null != name and '' != name">
            AND `name` = #{name}
            </if>
	        <if test="null != cspId and '' != cspId">
            AND `csp_id` = #{cspId}
            </if>
	        <if test="null != reportUrl and '' != reportUrl">
            AND `report_url` = #{reportUrl}
            </if>
	        <if test="null != creatorName and '' != creatorName">
            AND `creator_name` = #{creatorName}
            </if>
	        <if test="null != creatorId">
            AND `creator_id` = #{creatorId}
            </if>
	        <if test="null != priority">
            AND `priority` = #{priority}
            </if>
	        <if test="null != createTime and '' != createTime">
            AND `create_time` = #{createTime}
            </if>
	        <if test="null != updateTime and '' != updateTime">
            AND `update_time` = #{updateTime}
            </if>
	        <if test="null != cpId">
            AND `cp_id` = #{cpId}
            </if>
	        <if test="null != cpName and '' != cpName">
            AND `cp_name` = #{cpName}
            </if>
	        <if test="null != status">
            AND `status` = #{status}
            </if>
	        <if test="null != dealCount">
            AND `deal_count` = #{dealCount}
            </if>
        </where>
    </sql>

</mapper>

