<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//ibatis.apache.org//DTD Mapper 3.0//EN" "http://ibatis.apache.org/dtd/ibatis-3-mapper.dtd">


<!-- 不使用namespace的话sql搜索定位会比较方便 -->
<mapper namespace="com.xxl.job.executor.c2in.mapper.InPictureMapper">

	<!-- 表名 -->
	<sql id="tableName">
        in_picture
    </sql>
    
    <sql id="columns">
        `id`,`code`,`file_url`,`description`,`content_type`,`content_code`,`content_id`,`result`,`error_description`,`create_time`,`update_time`,`status`,`action`,`correlate_id`
    </sql>
    
    <sql id="where">
        <where>
	        <if test="null != id">
            AND `id` = #{id}
            </if>
	        <if test="null != code and '' != code">
            AND `code` = #{code}
            </if>
	        <if test="null != fileUrl and '' != fileUrl">
            AND `file_url` = #{fileUrl}
            </if>
	        <if test="null != description and '' != description">
            AND `description` = #{description}
            </if>
	        <if test="null != contentType">
            AND `content_type` = #{contentType}
            </if>
	        <if test="null != contentCode and '' != contentCode">
            AND `content_code` = #{contentCode}
            </if>
	        <if test="null != contentId">
            AND `content_id` = #{contentId}
            </if>
	        <if test="null != result">
            AND `result` = #{result}
            </if>
	        <if test="null != errorDescription and '' != errorDescription">
            AND `error_description` = #{errorDescription}
            </if>
	        <if test="null != createTime and '' != createTime">
            AND `create_time` = #{createTime}
            </if>
	        <if test="null != updateTime and '' != updateTime">
            AND `update_time` = #{updateTime}
            </if>
	        <if test="null != status">
            AND `status` = #{status}
            </if>
	        <if test="null != action">
            AND `action` = #{action}
            </if>
	        <if test="null != correlateId and '' != correlateId">
            AND `correlate_id` = #{correlateId}
            </if>
        </where>
    </sql>

</mapper>

