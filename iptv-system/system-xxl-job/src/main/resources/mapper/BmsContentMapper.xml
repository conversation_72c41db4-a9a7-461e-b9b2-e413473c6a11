<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//ibatis.apache.org//DTD Mapper 3.0//EN" "http://ibatis.apache.org/dtd/ibatis-3-mapper.dtd">


<!-- 不使用namespace的话sql搜索定位会比较方便 -->
<mapper namespace="com.pukka.iptv.manage.mapper.BmsContentMapper">

	<!-- 表名 -->
	<sql id="tableName">
        bms_content
    </sql>
    
    <sql id="columns">
        `id`,`code`,`name`,`order_number`,`original_name`,`licensing_window_start`,`licensing_window_end`,`display_as_new`,`display_as_last_chance`,`pgm_category_id`,`pgm_category`,`pgm_snd_class_id`,`pgm_snd_class`,`status`,`content_provider`,`cp_id`,`cp_name`,`create_time`,`update_time`,`sp_id`,`sp_name`,`content_type`,`cp_check_status`,`cp_check_desc`,`cp_check_time`,`cp_checker`,`op_check_status`,`op_check_desc`,`op_checker`,`op_check_time`,`source`,`lock_status`,`out_passage_ids`,`out_passage_names`,`publish_status`,`publish_time`,`publish_description`,`timed_publish`,`cms_content_code`,`cms_content_id`,`missed_info`,`bms_sp_channel_name`,`bms_sp_channel_id`
    </sql>
    
    <sql id="where">
        <where>
	        <if test="null != id">
            AND `id` = #{id}
            </if>
	        <if test="null != code and '' != code">
            AND `code` = #{code}
            </if>
	        <if test="null != name and '' != name">
            AND `name` = #{name}
            </if>
	        <if test="null != orderNumber and '' != orderNumber">
            AND `order_number` = #{orderNumber}
            </if>
	        <if test="null != originalName and '' != originalName">
            AND `original_name` = #{originalName}
            </if>
	        <if test="null != licensingWindowStart and '' != licensingWindowStart">
            AND `licensing_window_start` = #{licensingWindowStart}
            </if>
	        <if test="null != licensingWindowEnd and '' != licensingWindowEnd">
            AND `licensing_window_end` = #{licensingWindowEnd}
            </if>
	        <if test="null != displayAsNew">
            AND `display_as_new` = #{displayAsNew}
            </if>
	        <if test="null != displayAsLastChance">
            AND `display_as_last_chance` = #{displayAsLastChance}
            </if>
	        <if test="null != pgmCategoryId">
            AND `pgm_category_id` = #{pgmCategoryId}
            </if>
	        <if test="null != pgmCategory and '' != pgmCategory">
            AND `pgm_category` = #{pgmCategory}
            </if>
	        <if test="null != pgmSndClassId">
            AND `pgm_snd_class_id` = #{pgmSndClassId}
            </if>
	        <if test="null != pgmSndClass and '' != pgmSndClass">
            AND `pgm_snd_class` = #{pgmSndClass}
            </if>
	        <if test="null != status">
            AND `status` = #{status}
            </if>
	        <if test="null != contentProvider and '' != contentProvider">
            AND `content_provider` = #{contentProvider}
            </if>
	        <if test="null != cpId">
            AND `cp_id` = #{cpId}
            </if>
	        <if test="null != cpName and '' != cpName">
            AND `cp_name` = #{cpName}
            </if>
	        <if test="null != createTime and '' != createTime">
            AND `create_time` = #{createTime}
            </if>
	        <if test="null != updateTime and '' != updateTime">
            AND `update_time` = #{updateTime}
            </if>
	        <if test="null != spId">
            AND `sp_id` = #{spId}
            </if>
	        <if test="null != spName and '' != spName">
            AND `sp_name` = #{spName}
            </if>
	        <if test="null != contentType">
            AND `content_type` = #{contentType}
            </if>
	        <if test="null != cpCheckStatus">
            AND `cp_check_status` = #{cpCheckStatus}
            </if>
	        <if test="null != cpCheckDesc and '' != cpCheckDesc">
            AND `cp_check_desc` = #{cpCheckDesc}
            </if>
	        <if test="null != cpCheckTime and '' != cpCheckTime">
            AND `cp_check_time` = #{cpCheckTime}
            </if>
	        <if test="null != cpChecker and '' != cpChecker">
            AND `cp_checker` = #{cpChecker}
            </if>
	        <if test="null != opCheckStatus">
            AND `op_check_status` = #{opCheckStatus}
            </if>
	        <if test="null != opCheckDesc and '' != opCheckDesc">
            AND `op_check_desc` = #{opCheckDesc}
            </if>
	        <if test="null != opChecker and '' != opChecker">
            AND `op_checker` = #{opChecker}
            </if>
	        <if test="null != opCheckTime and '' != opCheckTime">
            AND `op_check_time` = #{opCheckTime}
            </if>
	        <if test="null != source">
            AND `source` = #{source}
            </if>
	        <if test="null != lockStatus">
            AND `lock_status` = #{lockStatus}
            </if>
	        <if test="null != outPassageIds and '' != outPassageIds">
            AND `out_passage_ids` = #{outPassageIds}
            </if>
	        <if test="null != outPassageNames and '' != outPassageNames">
            AND `out_passage_names` = #{outPassageNames}
            </if>
	        <if test="null != publishStatus">
            AND `publish_status` = #{publishStatus}
            </if>
	        <if test="null != publishTime and '' != publishTime">
            AND `publish_time` = #{publishTime}
            </if>
	        <if test="null != publishDescription and '' != publishDescription">
            AND `publish_description` = #{publishDescription}
            </if>
	        <if test="null != timedPublish and '' != timedPublish">
            AND `timed_publish` = #{timedPublish}
            </if>
	        <if test="null != cmsContentCode and '' != cmsContentCode">
            AND `cms_content_code` = #{cmsContentCode}
            </if>
	        <if test="null != cmsContentId">
            AND `cms_content_id` = #{cmsContentId}
            </if>
	        <if test="null != missedInfo and '' != missedInfo">
            AND `missed_info` = #{missedInfo}
            </if>
	        <if test="null != bmsSpChannelName and '' != bmsSpChannelName">
            AND `bms_sp_channel_name` = #{bmsSpChannelName}
            </if>
	        <if test="null != bmsSpChannelId">
            AND `bms_sp_channel_id` = #{bmsSpChannelId}
            </if>
        </where>
    </sql>

</mapper>

