<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//ibatis.apache.org//DTD Mapper 3.0//EN" "http://ibatis.apache.org/dtd/ibatis-3-mapper.dtd">


<!-- 不使用namespace的话sql搜索定位会比较方便 -->
<mapper namespace="com.xxl.job.executor.c2in.mapper.SysStorageDirctoryMapper">

	<!-- 表名 -->
	<sql id="tableName">
        sys_storage_dirctory
    </sql>
    
    <sql id="columns">
        `id`,`storage_name`,`storage_id`,`account`,`password`,`type`,`authority_type`,`description`
    </sql>
    
    <sql id="where">
        <where>
	        <if test="null != id">
            AND `id` = #{id}
            </if>
	        <if test="null != storageName and '' != storageName">
            AND `storage_name` = #{storageName}
            </if>
	        <if test="null != storageId">
            AND `storage_id` = #{storageId}
            </if>
	        <if test="null != account and '' != account">
            AND `account` = #{account}
            </if>
	        <if test="null != password and '' != password">
            AND `password` = #{password}
            </if>
	        <if test="null != type">
            AND `type` = #{type}
            </if>
	        <if test="null != authorityType">
            AND `authority_type` = #{authorityType}
            </if>
	        <if test="null != description and '' != description">
            AND `description` = #{description}
            </if>
        </where>
    </sql>

</mapper>

