<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//ibatis.apache.org//DTD Mapper 3.0//EN" "http://ibatis.apache.org/dtd/ibatis-3-mapper.dtd">


<!-- 不使用namespace的话sql搜索定位会比较方便 -->
<mapper namespace="com.pukka.iptv.manage.mapper.CmsScheduleMapper">

	<!-- 表名 -->
	<sql id="tableName">
        cms_schedule
    </sql>
    
    <sql id="columns">
        `id`,`code`,`channel_code`,`channel_name`,`channel_id`,`program_name`,`start_date`,`start_time`,`duration`,`storage_duration`,`status`,`description`,`source`,`genre`,`cp_id`,`cp_name`,`create_time`,`update_time`,`creator_name`,`creator_id`
    </sql>
    
    <sql id="where">
        <where>
	        <if test="null != id">
            AND `id` = #{id}
            </if>
	        <if test="null != code and '' != code">
            AND `code` = #{code}
            </if>
	        <if test="null != channelCode and '' != channelCode">
            AND `channel_code` = #{channelCode}
            </if>
	        <if test="null != channelName and '' != channelName">
            AND `channel_name` = #{channelName}
            </if>
	        <if test="null != channelId">
            AND `channel_id` = #{channelId}
            </if>
	        <if test="null != programName and '' != programName">
            AND `program_name` = #{programName}
            </if>
	        <if test="null != startDate and '' != startDate">
            AND `start_date` = #{startDate}
            </if>
	        <if test="null != startTime and '' != startTime">
            AND `start_time` = #{startTime}
            </if>
	        <if test="null != duration and '' != duration">
            AND `duration` = #{duration}
            </if>
	        <if test="null != storageDuration">
            AND `storage_duration` = #{storageDuration}
            </if>
	        <if test="null != status">
            AND `status` = #{status}
            </if>
	        <if test="null != description and '' != description">
            AND `description` = #{description}
            </if>
	        <if test="null != source">
            AND `source` = #{source}
            </if>
	        <if test="null != genre and '' != genre">
            AND `genre` = #{genre}
            </if>
	        <if test="null != cpId">
            AND `cp_id` = #{cpId}
            </if>
	        <if test="null != cpName and '' != cpName">
            AND `cp_name` = #{cpName}
            </if>
	        <if test="null != createTime and '' != createTime">
            AND `create_time` = #{createTime}
            </if>
	        <if test="null != updateTime and '' != updateTime">
            AND `update_time` = #{updateTime}
            </if>
	        <if test="null != creatorName and '' != creatorName">
            AND `creator_name` = #{creatorName}
            </if>
	        <if test="null != creatorId">
            AND `creator_id` = #{creatorId}
            </if>
        </where>
    </sql>

</mapper>

