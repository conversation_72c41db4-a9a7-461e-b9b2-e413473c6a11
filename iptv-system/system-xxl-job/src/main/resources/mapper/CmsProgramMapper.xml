<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//ibatis.apache.org//DTD Mapper 3.0//EN" "http://ibatis.apache.org/dtd/ibatis-3-mapper.dtd">


<!-- 不使用namespace的话sql搜索定位会比较方便 -->
<mapper namespace="com.pukka.iptv.manage.mapper.CmsProgramMapper">

	<!-- 表名 -->
	<sql id="tableName">
        cms_program
    </sql>
    
    <sql id="columns">
        `id`,`code`,`name`,`order_number`,`original_name`,`release_status`,`preview_status`,`sort_name`,`search_name`,`actor_display`,`writer_display`,`original_country_id`,`original_country_name`,`language`,`release_year`,`org_air_date`,`licensing_window_start`,`licensing_window_end`,`display_as_new`,`display_as_last_chance`,`macrovision`,`description`,`pgm_category_id`,`pgm_category_name`,`pgm_snd_class_id`,`pgm_snd_class_name`,`price_tax_in`,`status`,`source_type`,`series_flag`,`episode_index`,`series_id`,`series_code`,`series_name`,`kpeople`,`director`,`script_writer`,`compere`,`guest`,`reporter`,`op_incharge`,`vsp_code`,`copy_right`,`content_provider`,`duration`,`rating`,`cp_id`,`cp_name`,`create_time`,`update_time`,`cp_check_status`,`cp_check_desc`,`cp_check_time`,`cp_checker`,`op_check_status`,`op_check_desc`,`op_checker`,`op_check_time`,`source`,`creator_name`,`creator_id`,`edit_lock_status`,`cucc_price`,`ctcc_price`,`cmcc_price`,`publisher`,`approval`,`definition_flag`,`resource_preview_code`,`movie_status`,`resource_release_code`,`movie_head_duration`,`movie_tail_duration`
    </sql>
    
    <sql id="where">
        <where>
	        <if test="null != id">
            AND `id` = #{id}
            </if>
	        <if test="null != code and '' != code">
            AND `code` = #{code}
            </if>
	        <if test="null != name and '' != name">
            AND `name` = #{name}
            </if>
	        <if test="null != orderNumber and '' != orderNumber">
            AND `order_number` = #{orderNumber}
            </if>
	        <if test="null != originalName and '' != originalName">
            AND `original_name` = #{originalName}
            </if>
	        <if test="null != releaseStatus">
            AND `release_status` = #{releaseStatus}
            </if>
	        <if test="null != previewStatus">
            AND `preview_status` = #{previewStatus}
            </if>
	        <if test="null != sortName and '' != sortName">
            AND `sort_name` = #{sortName}
            </if>
	        <if test="null != searchName and '' != searchName">
            AND `search_name` = #{searchName}
            </if>
	        <if test="null != actorDisplay and '' != actorDisplay">
            AND `actor_display` = #{actorDisplay}
            </if>
	        <if test="null != writerDisplay and '' != writerDisplay">
            AND `writer_display` = #{writerDisplay}
            </if>
	        <if test="null != originalCountryId">
            AND `original_country_id` = #{originalCountryId}
            </if>
	        <if test="null != originalCountryName and '' != originalCountryName">
            AND `original_country_name` = #{originalCountryName}
            </if>
	        <if test="null != language and '' != language">
            AND `language` = #{language}
            </if>
	        <if test="null != releaseYear and '' != releaseYear">
            AND `release_year` = #{releaseYear}
            </if>
	        <if test="null != orgAirDate and '' != orgAirDate">
            AND `org_air_date` = #{orgAirDate}
            </if>
	        <if test="null != licensingWindowStart and '' != licensingWindowStart">
            AND `licensing_window_start` = #{licensingWindowStart}
            </if>
	        <if test="null != licensingWindowEnd and '' != licensingWindowEnd">
            AND `licensing_window_end` = #{licensingWindowEnd}
            </if>
	        <if test="null != displayAsNew">
            AND `display_as_new` = #{displayAsNew}
            </if>
	        <if test="null != displayAsLastChance">
            AND `display_as_last_chance` = #{displayAsLastChance}
            </if>
	        <if test="null != macrovision">
            AND `macrovision` = #{macrovision}
            </if>
	        <if test="null != description and '' != description">
            AND `description` = #{description}
            </if>
	        <if test="null != pgmCategoryId">
            AND `pgm_category_id` = #{pgmCategoryId}
            </if>
	        <if test="null != pgmCategoryName and '' != pgmCategoryName">
            AND `pgm_category_name` = #{pgmCategoryName}
            </if>
	        <if test="null != pgmSndClassId">
            AND `pgm_snd_class_id` = #{pgmSndClassId}
            </if>
	        <if test="null != pgmSndClassName and '' != pgmSndClassName">
            AND `pgm_snd_class_name` = #{pgmSndClassName}
            </if>
	        <if test="null != priceTaxIn and '' != priceTaxIn">
            AND `price_tax_in` = #{priceTaxIn}
            </if>
	        <if test="null != status">
            AND `status` = #{status}
            </if>
	        <if test="null != sourceType">
            AND `source_type` = #{sourceType}
            </if>
	        <if test="null != seriesFlag">
            AND `series_flag` = #{seriesFlag}
            </if>
	        <if test="null != episodeIndex">
            AND `episode_index` = #{episodeIndex}
            </if>
	        <if test="null != seriesId">
            AND `series_id` = #{seriesId}
            </if>
	        <if test="null != seriesCode and '' != seriesCode">
            AND `series_code` = #{seriesCode}
            </if>
	        <if test="null != seriesName and '' != seriesName">
            AND `series_name` = #{seriesName}
            </if>
	        <if test="null != kpeople and '' != kpeople">
            AND `kpeople` = #{kpeople}
            </if>
	        <if test="null != director and '' != director">
            AND `director` = #{director}
            </if>
	        <if test="null != scriptWriter and '' != scriptWriter">
            AND `script_writer` = #{scriptWriter}
            </if>
	        <if test="null != compere and '' != compere">
            AND `compere` = #{compere}
            </if>
	        <if test="null != guest and '' != guest">
            AND `guest` = #{guest}
            </if>
	        <if test="null != reporter and '' != reporter">
            AND `reporter` = #{reporter}
            </if>
	        <if test="null != opIncharge and '' != opIncharge">
            AND `op_incharge` = #{opIncharge}
            </if>
	        <if test="null != vspCode and '' != vspCode">
            AND `vsp_code` = #{vspCode}
            </if>
	        <if test="null != copyRight and '' != copyRight">
            AND `copy_right` = #{copyRight}
            </if>
	        <if test="null != contentProvider and '' != contentProvider">
            AND `content_provider` = #{contentProvider}
            </if>
	        <if test="null != duration">
            AND `duration` = #{duration}
            </if>
	        <if test="null != rating and '' != rating">
            AND `rating` = #{rating}
            </if>
	        <if test="null != cpId">
            AND `cp_id` = #{cpId}
            </if>
	        <if test="null != cpName and '' != cpName">
            AND `cp_name` = #{cpName}
            </if>
	        <if test="null != createTime and '' != createTime">
            AND `create_time` = #{createTime}
            </if>
	        <if test="null != updateTime and '' != updateTime">
            AND `update_time` = #{updateTime}
            </if>
	        <if test="null != cpCheckStatus">
            AND `cp_check_status` = #{cpCheckStatus}
            </if>
	        <if test="null != cpCheckDesc and '' != cpCheckDesc">
            AND `cp_check_desc` = #{cpCheckDesc}
            </if>
	        <if test="null != cpCheckTime and '' != cpCheckTime">
            AND `cp_check_time` = #{cpCheckTime}
            </if>
	        <if test="null != cpChecker and '' != cpChecker">
            AND `cp_checker` = #{cpChecker}
            </if>
	        <if test="null != opCheckStatus">
            AND `op_check_status` = #{opCheckStatus}
            </if>
	        <if test="null != opCheckDesc and '' != opCheckDesc">
            AND `op_check_desc` = #{opCheckDesc}
            </if>
	        <if test="null != opChecker and '' != opChecker">
            AND `op_checker` = #{opChecker}
            </if>
	        <if test="null != opCheckTime and '' != opCheckTime">
            AND `op_check_time` = #{opCheckTime}
            </if>
	        <if test="null != source">
            AND `source` = #{source}
            </if>
	        <if test="null != creatorName and '' != creatorName">
            AND `creator_name` = #{creatorName}
            </if>
	        <if test="null != creatorId">
            AND `creator_id` = #{creatorId}
            </if>
	        <if test="null != editLockStatus">
            AND `edit_lock_status` = #{editLockStatus}
            </if>
	        <if test="null != cuccPrice and '' != cuccPrice">
            AND `cucc_price` = #{cuccPrice}
            </if>
	        <if test="null != ctccPrice and '' != ctccPrice">
            AND `ctcc_price` = #{ctccPrice}
            </if>
	        <if test="null != cmccPrice and '' != cmccPrice">
            AND `cmcc_price` = #{cmccPrice}
            </if>
	        <if test="null != publisher and '' != publisher">
            AND `publisher` = #{publisher}
            </if>
	        <if test="null != approval and '' != approval">
            AND `approval` = #{approval}
            </if>
            <if test="null != definitionFlag and '' != definitionFlag">
                AND `definition_flag` = #{definitionFlag}
            </if>
	        <if test="null != movieTrailerCode and '' != movieTrailerCode">
            AND `movie_trailer_code` = #{movieTrailerCode}
            </if>
	        <if test="null != movieStatus">
            AND `movie_status` = #{movieStatus}
            </if>
	        <if test="null != resourceCode and '' != resourceCode">
            AND `resource_code` = #{resourceCode}
            </if>
	        <if test="null != movieHeadDuration">
            AND `movie_head_duration` = #{movieHeadDuration}
            </if>
	        <if test="null != movieTailDuration">
            AND `movie_tail_duration` = #{movieTailDuration}
            </if>
        </where>
    </sql>

</mapper>

