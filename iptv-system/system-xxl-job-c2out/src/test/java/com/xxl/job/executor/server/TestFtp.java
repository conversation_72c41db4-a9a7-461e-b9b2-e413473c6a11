package com.xxl.job.executor.server;

import com.pukka.iptv.common.core.util.SafeUtil;
import com.xxl.job.executor.TestXxlJobExecutorApplication;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

/**
 * <AUTHOR>
 * @Date 2021/9/23 4:54 下午
 * @description:
 * @Version 1.0
 */
@RunWith(SpringRunner.class)
@SpringBootTest(classes = TestXxlJobExecutorApplication.class)
public class TestFtp {
    @Value("${ftp.category.path:ftp://xstore:iptv!#$xs@**************:6069/}")
    private String rootUrl;//存放文件的基本路径

    @Value("${ftp.category.filename:xstorage/Category/}")
    private String dir;//文件名
    

    @Test
    public void testFtp() {
        String outXmlContent = "test";
        String fileName = "";//dir + DateUtils.dateTimeNow();
        //Integer integer = SafeUtil.downloadByFtp(rootUrl, fileName);
        String s = SafeUtil.upByFtp(outXmlContent, fileName, rootUrl);
        System.out.println("ok");
    }
}
