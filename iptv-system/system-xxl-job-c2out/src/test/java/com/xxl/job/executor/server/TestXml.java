package com.xxl.job.executor.server;

import com.pukka.iptv.common.base.constant.RedisKeyConstants;
import com.pukka.iptv.common.base.enums.ResultStatusEnum;
import com.pukka.iptv.common.core.constant.SymbolConstant;
import com.pukka.iptv.common.data.model.cms.CmsProgram;
import com.pukka.iptv.common.data.model.sys.SysDictionaryItem;
import com.pukka.iptv.common.data.vo.sys.OutOrderItemVo;
import com.pukka.iptv.common.redis.service.RedisService;
import com.pukka.iptv.common.data.model.order.SubOrderMappingsEntity;
import com.pukka.iptv.common.data.model.order.SubOrderObjectsEntity;
import com.pukka.iptv.common.data.model.order.SubOrderXmlEntity;
import com.xxl.job.executor.c2out.dao.service.OutOrderItemService;
import com.xxl.job.executor.c2out.utils.OutOperateUtil;
import com.xxl.job.executor.c2out.xml.XmlRulesLoadStrategy;
import com.xxl.job.executor.common.content.FtpHandleConstant;
import com.xxl.job.executor.TestXxlJobExecutorApplication;
import org.dom4j.Element;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @Date 2021/9/18 10:16 上午
 * @description:
 * @Version 1.0
 */
@RunWith(SpringRunner.class)
@SpringBootTest(classes = TestXxlJobExecutorApplication.class)
public class TestXml {

    @Autowired
    OutOrderItemService outOrderItemService;
    @Autowired
    OutOperateUtil outOperateUtil;
    @Autowired
    private RedisService redisService;
    @Autowired
    private XmlRulesLoadStrategy xmlRulesLoadStrategy;

    @Test
    public void testXml() {

        String mediaPrefix = outOperateUtil.getMediaPrefix("10", "ftp://vstore:iptvvsR_E9@**************:21/SMP/hbgd/SMP_MOV8/cp/cphbxmt/videoFolder/202202/11/YKVMOV00000000003925000003226864/8325427ee10485a9f04e0fef2982027c.m3u8",
                FtpHandleConstant.OUT_MOVIE_FTP_PREFIX);

        Integer result = 0;
        Objects.equals(result, ResultStatusEnum.SUCCESS.getCode());

        String str = ";;;;";
        String s = str.split(SymbolConstant.SEMICOLON, -1)[0];
        Map<String, SysDictionaryItem> cacheMap = redisService.getCacheMap(RedisKeyConstants.SYS_DICTIONARY_ITEM_KEY);
        if (cacheMap != null && cacheMap.size() > 0) {
            Map<String, SysDictionaryItem> collect = cacheMap.entrySet().stream().filter((p) -> p.getKey().contains("6:"))
                    .collect(Collectors.toMap((e) -> e.getKey(), Map.Entry::getValue));
            List<SysDictionaryItem> sysSpList = collect.values().stream().collect(Collectors.toList());
            List<SysDictionaryItem> spList = sysSpList.stream().collect(Collectors.collectingAndThen(
                    Collectors.toCollection(() -> new TreeSet<>(Comparator.comparing(SysDictionaryItem::getId))), ArrayList::new));


            CmsProgram cmsProgram = new CmsProgram();
            cmsProgram.setCuccPrice("100;200;300");
            cmsProgram.setCmccPrice("200;100;300");
            cmsProgram.setCtccPrice("800;700");
            cmsProgram.setNewPrice("800;100");
            /*String cucc_channel = SpChannelCodeEnum.getPrice(cmsProgram, "");
            System.out.println(cucc_channel);*/


            outOperateUtil.getMediaPrefix("1", "2021/09/23/20210923163602_3175208.jpg", "out_picture_ftp_prefix");


            OutOrderItemVo bmsContentCodeAndName = outOrderItemService.getBmsContentCodeAndName("25", 21);

            SubOrderXmlEntity subOrderXmlEntity = new SubOrderXmlEntity();
            List<SubOrderObjectsEntity> subOrderObjectsEntityList = new ArrayList<>();
            List<SubOrderMappingsEntity> subOrderMappingsEntityList = new ArrayList<>();
            for (int i = 0; i < 10; i++) {
                SubOrderObjectsEntity subOrderObjectsEntity = new SubOrderObjectsEntity();
                subOrderObjectsEntity.setElementType("Program");
                subOrderObjectsEntity.setId("100" + i);
                subOrderObjectsEntity.setAction("REGIST");
                subOrderObjectsEntity.setSerialNumber(i);
                subOrderObjectsEntity.setCode("10086");
                Map<String, String> map = new Hashtable<>();
                map.put("Code", "00" + i);
                map.put("Name", String.valueOf(i));
                map.put("Language", "汉语");
                map.put("test1", "");
                map.put("test2", "");
                map.put("test5", " ");
                map.put("test6", "null");
                subOrderObjectsEntity.setPropertyDic(map);
                subOrderObjectsEntityList.add(subOrderObjectsEntity);

                SubOrderMappingsEntity subOrderMappingsEntity = new SubOrderMappingsEntity();
                subOrderMappingsEntity.setElementType("Movie" + i);
                subOrderMappingsEntity.setParentType("Program");
                subOrderMappingsEntity.setParentId("100" + i);
                subOrderMappingsEntity.setParentCode("100" + i);
                subOrderMappingsEntity.setAction("REGIST");
                subOrderMappingsEntity.setElementId("200" + i);
                subOrderMappingsEntity.setElementCode("200" + i);
                subOrderMappingsEntity.setSerialNumber(i);
                Map<String, String> maps = new Hashtable<>();
                maps.put("Type", String.valueOf(i));
                maps.put("test1", "");
                maps.put("test2", "");
                maps.put("test5", " ");
                maps.put("test6", "null");
                subOrderMappingsEntity.setPropertyDic(maps);
                subOrderMappingsEntityList.add(subOrderMappingsEntity);
            }
            subOrderXmlEntity.setSubOrderObjectsEntities(subOrderObjectsEntityList);
            subOrderXmlEntity.setSubOrderMappingsEntities(subOrderMappingsEntityList);
            Element xml = xmlRulesLoadStrategy.getXml(subOrderXmlEntity,1L);
            String content = xmlRulesLoadStrategy.getOutXmlContent(xml,1L);
            System.out.println(content);
        }
    }
}
