package com.xxl.job.executor.c2out.utils.unique;

import static org.junit.jupiter.api.Assertions.*;

import com.pukka.iptv.common.base.enums.CheckUniqueEnum;
import com.pukka.iptv.common.data.model.order.CategoryObjectEntity;
import java.util.HashMap;

import com.xxl.job.executor.service.jobhandler.outorder.OutOrderReport;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;

@SpringBootTest
class CheckUniqueUtilTest {
    @Autowired
    private CheckUniqueUtil checkUniqueUtil;

    @Autowired
    private OutOrderReport outOrderReport;
    @Test
    public void testCheckUniqueOptimized() {
        // 创建一个CategoryObjectEntity对象
        CategoryObjectEntity categoryObjectEntity = new CategoryObjectEntity();
        categoryObjectEntity.setCode("HBGD9272685792770703367355002999");
        categoryObjectEntity.setParentCode("HBGD9272444311673815043241193075");
        categoryObjectEntity.setPropertyDic(new HashMap<String, String>() {{
            put("parent_id", "HBGD9272444311673815043241193075");
        }});
        //完成初始化


        // 调用checkUniqueOptimized方法
        CheckUniqueResult result = checkUniqueUtil.checkUniqueOptimized(categoryObjectEntity, 152L,false);

        // 验证结果
        Assertions.assertEquals(CheckUniqueEnum.ERROR, result.getCheckUniqueEnum());
    }

    @Test
    public void test() {
        outOrderReport.outHandle("{\"action\":1,\"baseOrderId\":7154917,\"bmsContentCode\":\"HBGD9851639703353139204561057816\",\"bmsContentId\":1607458,\"bmsSpChannelCode\":\"CUCC_CHANNEL\",\"bmsSpChannelId\":589,\"bmsSpChannelName\":\"联通运营商\",\"childNodeExist\":2,\"cmdFileUrl\":\"ftp://xstore:iptv!#$xs@**************:6069/Out/Distribute/CategoryContent/20240507/985165653910093824.xml\",\"contentType\":\"18\",\"correlateId\":\"985165653910093824\",\"cpName\":\"二期通用CP\",\"createTime\":1715048261000,\"creatorId\":195,\"creatorName\":\"卢彦辰\",\"cspId\":\"HBUT\",\"duration\":8,\"feedbackTime\":1715048269000,\"id\":3410,\"lspId\":\"LTHW1\",\"outPassageCode\":\"4\",\"outPassageId\":4,\"outPassageName\":\"联通华为正式\",\"priority\":4,\"publishContentType\":\"18\",\"publishType\":\"1\",\"result\":0,\"resultFileUrl\":\"ftp://xstore:iptv!#$xs@**************:6069/Out/Distribute/Category/20240325/969608142346678272.xml\",\"retryCount\":3,\"showName\":\"(节目)单集0507上\n" +
                "线时间TEST -> (栏目)单集栏目0507TEST\",\"spName\":\"联通华为域\",\"status\":3,\"statusDescription\":\"处理成功\",\"updateTime\":1715048266000}");
    }
}