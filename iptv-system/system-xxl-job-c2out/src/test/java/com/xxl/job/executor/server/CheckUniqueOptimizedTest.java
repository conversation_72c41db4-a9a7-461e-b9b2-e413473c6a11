package com.xxl.job.executor.server;


import com.pukka.iptv.common.base.enums.CheckUniqueEnum;
import com.pukka.iptv.common.data.model.order.CategoryObjectEntity;
import com.xxl.job.executor.c2out.utils.unique.CheckUniqueResult;
import com.xxl.job.executor.c2out.utils.unique.CheckUniqueUtil;
import java.util.HashMap;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;

@SpringBootTest
public class CheckUniqueOptimizedTest {

    @Autowired
    private CheckUniqueUtil checkUniqueUtil;

    @Test
    public void testCheckUniqueOptimized() {
        // 创建一个CategoryObjectEntity对象
        CategoryObjectEntity categoryObjectEntity = new CategoryObjectEntity();
        categoryObjectEntity.setCode("HBGD9272685792770703367355002999");
        categoryObjectEntity.setParentCode("HBGD9272444311673815043241193075");
        categoryObjectEntity.setPropertyDic(new HashMap<String, String>() {{
            put("parent_id", "HBGD9272444311673815043241193075");
        }});
        //完成初始化


        // 调用checkUniqueOptimized方法
        CheckUniqueResult result = checkUniqueUtil.checkUniqueOptimized(categoryObjectEntity, 152L,false);

        // 验证结果
        Assertions.assertEquals(CheckUniqueEnum.ERROR, result.getCheckUniqueEnum());
    }
}
