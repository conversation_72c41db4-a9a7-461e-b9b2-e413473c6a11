<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<!-- 不使用namespace的话sql搜索定位会比较方便 -->
<mapper namespace="com.xxl.job.executor.c2out.dao.mapper.SysOutpassageStorageRelaMapper">

    <select id="getByOutPassageIdAndStorageId"
            resultType="com.pukka.iptv.common.data.vo.sys.SysOutpassageStorageRelaVO">
        select sosr.id ,
               sosr.out_passage_id ,
               sop.name as out_passage_name,
               sosr.storage_directory_id ,
               ssd.storage_name ,
               sosr.type as type,
               ssd.account as storage_account,
               ssd.password as storage_password,
               sosr.limit_speed ,
               sosr.create_time ,
               sosr.update_time
        from sys_outpassage_storage_rela sosr
                 left join sys_storage_directory ssd on sosr.storage_directory_id = ssd.id
                 left join sys_out_passage sop on sosr.out_passage_id = sop.id
        where sosr.status = 1 and sop.id = #{outPassageId} and ssd.storage_id = #{storageId}
    </select>
</mapper>

