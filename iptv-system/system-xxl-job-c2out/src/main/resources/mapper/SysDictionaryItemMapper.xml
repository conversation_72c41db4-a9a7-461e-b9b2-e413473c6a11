<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">


<!-- 不使用namespace的话sql搜索定位会比较方便 -->
<mapper namespace="com.xxl.job.executor.c2out.dao.mapper.SysDictionaryItemMapper">

	<!-- 表名 -->
	<sql id="tableName">
        sys_dictionary_item
    </sql>
    
    <sql id="columns">
        `id`,`code`,`name`,`sequence`,`type`,`status`,`parent_id`,`dictionary_base_id`,`tenant_id`,`creator_id`,`creator_name`,`create_time`,`update_time`,`description`
    </sql>
    
    <sql id="where">
        <where>
	        <if test="null != id">
            AND `id` = #{id}
            </if>
	        <if test="null != code and '' != code">
            AND `code` = #{code}
            </if>
	        <if test="null != name and '' != name">
            AND `name` = #{name}
            </if>
	        <if test="null != sequence">
            AND `sequence` = #{sequence}
            </if>
	        <if test="null != type">
            AND `type` = #{type}
            </if>
	        <if test="null != status">
            AND `status` = #{status}
            </if>
	        <if test="null != parentId">
            AND `parent_id` = #{parentId}
            </if>
	        <if test="null != dictionaryBaseId">
            AND `dictionary_base_id` = #{dictionaryBaseId}
            </if>
	        <if test="null != tenantId">
            AND `tenant_id` = #{tenantId}
            </if>
	        <if test="null != creatorId">
            AND `creator_id` = #{creatorId}
            </if>
	        <if test="null != creatorName and '' != creatorName">
            AND `creator_name` = #{creatorName}
            </if>
	        <if test="null != createTime and '' != createTime">
            AND `create_time` = #{createTime}
            </if>
	        <if test="null != updateTime and '' != updateTime">
            AND `update_time` = #{updateTime}
            </if>
	        <if test="null != description and '' != description">
            AND `description` = #{description}
            </if>
        </where>
    </sql>

</mapper>

