package com.xxl.job.executor.c2out.strategy.handler.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.pukka.iptv.common.api.feign.bms.BmsCategoryContentFeignClient;
import com.pukka.iptv.common.api.feign.bms.BmsCategoryFeignClient;
import com.pukka.iptv.common.base.constant.ObjectsTypeConstants;
import com.pukka.iptv.common.base.enums.ActionEnums;
import com.pukka.iptv.common.base.vo.CommonResponse;
import com.pukka.iptv.common.core.util.SafeUtil;
import com.pukka.iptv.common.data.model.bms.BmsCategory;
import com.pukka.iptv.common.data.model.bms.BmsCategoryContent;
import com.pukka.iptv.common.data.model.order.SubOrderMappingsEntity;
import com.pukka.iptv.common.data.model.order.SubOrderXmlEntity;
import com.pukka.iptv.common.data.vo.sys.OutOrderBaseVo;
import com.xxl.job.executor.c2out.dao.entity.ResultResponse;
import com.xxl.job.executor.c2out.utils.OutOperateUtil;
import com.xxl.job.executor.c2out.utils.SiteOperateUtil;
import com.xxl.job.executor.common.content.FtpHandleConstant;
import com.xxl.job.executor.common.enums.ContentPublishTypeEnum;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.ObjectUtils;

import java.util.*;
import java.util.concurrent.atomic.AtomicInteger;

/**
 * <AUTHOR>
 * @Date 2021/9/3 9:11 上午
 * @description: 栏目节目发布
 * @Version 1.0
 */
@Slf4j
@Component
public class CategoryContentHandler extends AbstractHandler {

    @Autowired
    BmsCategoryContentFeignClient bmsCategoryContentFeignClient;

    @Override
    public ContentPublishTypeEnum getContentType() {
        return ContentPublishTypeEnum.CATEGORY_PROGRAM;
    }

    @Autowired
    private OutOperateUtil outOperateUtil;
    @Autowired
    private BmsCategoryFeignClient bmsCategoryFeignClient;
    /**
     * 发布/回收操作
     *
     * @param outOrderBaseVo
     * @return
     */
    @Override
    public ResultResponse publishOrRecover(OutOrderBaseVo outOrderBaseVo) {
        ResultResponse resultResponse = new ResultResponse();
        try {
            //获取关联节目关系，组装内容，构成xml文件
            if (getProgramMapping(outOrderBaseVo, resultResponse)) {
                StringBuffer xmlDestination = SiteOperateUtil.getFileName(FtpHandleConstant.DIR_CATEGORY_CONTENT);
                resultResponse.getData().setXmlDestination(xmlDestination.toString());
            }
        } catch (Exception exception) {
            resultResponse.setErrorResult(String.format("执行栏目节目发布操作 ->>>>>> 发布类型:%s 内容ID:%s 执行发布工单操作失败！", getContentType().getContentTypeName(), outOrderBaseVo.getBmsContentId()));
            log.warn("执行栏目节目发布操作 ->>>>>> 发布类型:{} 内容ID:{}，执行发布工单操作失败,错误描述:{}！", getContentType().getContentTypeName(), outOrderBaseVo.getBmsContentId(),exception);
        } finally {
            return resultResponse;
        }
    }

    /**
     * 获取节目信息关系
     *
     * @return
     */
    private boolean getProgramMapping(OutOrderBaseVo outOrderBaseVo, ResultResponse resultResponse) {
        SubOrderXmlEntity subOrderXmlEntit = resultResponse.getData();
        CommonResponse<List<BmsCategoryContent>> bmsCategoryContentFeignClientByCategoryId = bmsCategoryContentFeignClient.getByCategoryContentId(outOrderBaseVo.getBmsContentId());
        if (bmsCategoryContentFeignClientByCategoryId.getData() == null) {
            resultResponse.setErrorResult(String.format("执行栏目节目发布操作 ->>>>>> 发布类型:%s 内容ID:%s 获取栏目节目关系失败，获取结果为空!", getContentType().getContentTypeName(), outOrderBaseVo.getBmsContentId()));
            return false;
        }
        List<BmsCategoryContent> bmsCategoryContentList = JSONArray.parseArray(JSON.toJSONString(bmsCategoryContentFeignClientByCategoryId.getData())).toJavaList(BmsCategoryContent.class);
        if (bmsCategoryContentList == null || bmsCategoryContentList.size() < 1) {
            resultResponse.setErrorResult(String.format("执行栏目节目发布操作 ->>>>>> 发布类型:%s 内容ID:%s 获取栏目节目关系失败，获取结果为空!", getContentType().getContentTypeName(), outOrderBaseVo.getBmsContentId()));
            return false;
        }
        try {
            List<SubOrderMappingsEntity> subOrderMappingsEntityList = subOrderXmlEntit.getSubOrderMappingsEntities() != null
                    ? subOrderXmlEntit.getSubOrderMappingsEntities() : new ArrayList<>();
            //实体序号
            AtomicInteger serialNumber = new AtomicInteger();
            for (BmsCategoryContent bmsCategoryContent : bmsCategoryContentList) {
                SubOrderMappingsEntity subOrderMappingsEntity = new SubOrderMappingsEntity();
                subOrderMappingsEntity.setId(UUID.randomUUID().toString());
                subOrderMappingsEntity.setElementType(ContentPublishTypeEnum.getContentTypeNameByContentType(outOrderBaseVo.getContentType()));

                subOrderMappingsEntity.setElementId(bmsCategoryContent.getCmsContentCode());
                subOrderMappingsEntity.setElementCode(bmsCategoryContent.getCmsContentCode());
                subOrderMappingsEntity.setAction(ActionEnums.getInfoByCode(outOrderBaseVo.getAction()));
                subOrderMappingsEntity.setParentType(ObjectsTypeConstants.CATEGORY);

                boolean extraCode = outOperateUtil.isExtraCode(String.valueOf(bmsCategoryContent.getSpId()));
                if (extraCode) {
                    //更新parentcode
                    CommonResponse<BmsCategory> extracodeById = bmsCategoryFeignClient.getExtracodeById(bmsCategoryContent.getCategoryId());
                    if (!ObjectUtils.isEmpty(extracodeById.getData())) {
                        BmsCategory bmsCategory1 = extracodeById.getData();
                        bmsCategoryContent.setCategoryCode(bmsCategory1.getExtraCode());
                    }
                }
                subOrderMappingsEntity.setParentId(bmsCategoryContent.getCategoryCode());
                subOrderMappingsEntity.setParentCode(bmsCategoryContent.getCategoryCode());
                subOrderMappingsEntity.setSerialNumber(serialNumber.incrementAndGet());
                Map<String, String> propertyDic = new HashMap<>();
                propertyDic.put("Sequence", SafeUtil.getString(bmsCategoryContent.getSequence()));
                subOrderMappingsEntity.setPropertyDic(propertyDic);
                subOrderMappingsEntityList.add(subOrderMappingsEntity);
            }
            subOrderXmlEntit.setSubOrderMappingsEntities(subOrderMappingsEntityList);
        } catch (Exception exception) {
            resultResponse.setErrorResult(String.format("执行栏目节目发布操作 ->>>>>> 发布类型:%s 内容ID:%s 组装mapping内容失败!", getContentType().getContentTypeName(), outOrderBaseVo.getBmsContentId()));
            log.error("执行栏目节目发布操作 ->>>>>> 发布类型:{} 内容ID:{} 栏目-节目关系发布，组装mapping内容失败,错误描述:{}!", getContentType().getContentTypeName(), outOrderBaseVo.getBmsContentId(),exception);
            return false;
        }
        return true;
    }

}
