package com.xxl.job.executor.service.jobhandler.outorder;

import com.pukka.iptv.common.data.vo.sys.OutOrderItemVo;

import java.util.List;

/**
 * <AUTHOR>
 * @Date 2021/10/14 4:13 下午
 * @description:
 * @Version 1.0
 */
public interface IOutOrder {

    /**
     * 类执行器名称
     * @return
     */
    String getClassPropertyName();

    /**
     * 错误描述
     * @return
     */
    String getErrorDescription();

    /**
     * 执行器逻辑
     * @param msg
     * @return
     * @throws Exception
     */
    boolean outHandle(String msg) throws Exception;

    /**
     * 反馈实体
     * @param msg
     * @return
     */
    List<OutOrderItemVo> getReportEntity(String msg);
}
