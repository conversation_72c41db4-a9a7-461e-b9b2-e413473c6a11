package com.xxl.job.executor.c2out.strategy.handler.impl;

import com.xxl.job.executor.common.enums.ContentPublishTypeEnum;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @Date 2021/11/18 2:42 下午
 * @description: 产品包剧集发布
 * @Version 1.0
 */
@Slf4j
@Component
public class PackageSeriesHandler extends PackageContentHandler {

    @Override
    public ContentPublishTypeEnum getContentType() {
        return ContentPublishTypeEnum.PACKAGE_SERIES;
    }

}
