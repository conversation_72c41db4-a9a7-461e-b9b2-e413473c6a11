package com.xxl.job.executor.common.config;

import com.xxl.job.executor.c2out.initialization.BuilderLogic;
import com.xxl.job.executor.c2out.initialization.IBuilderLogic;
import com.xxl.job.executor.c2out.xml.IXmlHandlerStrategy;
import com.xxl.job.executor.c2out.xml.XmlHandlerStrategy;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Primary;

/**
 * @author: chiron
 * Date: 2022/3/28 9:52 AM
 * Description:
 */
@Configuration
public class Config {
    @Bean
    @Primary
    public IBuilderLogic getIBuilderLogic(){
        return new BuilderLogic();
    }
    @Bean
    @Primary
    public IXmlHandlerStrategy getIXmlHandlerStrategy(){
        return new XmlHandlerStrategy();
    }
}