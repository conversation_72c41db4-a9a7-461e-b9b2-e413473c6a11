/**
 * CtmsSoapBindingImpl.java
 *
 * This file was auto-generated from WSDL
 * by the Apache Axis 1.4 Apr 22, 2006 (06:55:48 PDT) WSDL2Java emitter.
 */

package com.xxl.job.executor.c2out.webservice.Request;

import org.apache.commons.logging.Log;
import org.apache.commons.logging.LogFactory;
import org.springframework.stereotype.Component;

//import com.iptvInterface.external.ExeccmdInEx;

@Component
public class CtmsSoapBindingImpl implements CSPRequest{
	protected Log log = LogFactory.getLog(this.getClass().getName());
	public CSPResult execCmd(String CSPID, String LSPID, String correlateID, String cmdFileURL) throws java.rmi.RemoteException {
    	CSPResult cspr = new CSPResult();
    	try{	    	
    		log.info("ExecCmd--------:CSPID="+CSPID+";LSPID="+LSPID+";correlateID="+correlateID+";cmdFileURL="+cmdFileURL);
    		/*ResultClass rc = ExeccmdInEx.insert(CSPID, LSPID, correlateID, cmdFileURL);
    		cspr.setResult(rc.getResultCode());
    		cspr.setErrorDescription(rc.getResultDesc()); */
    		cspr.setResult(0);
			cspr.setErrorDescription("success");
    	}catch(Exception ex){
    		cspr.setResult(-1);
	    	cspr.setErrorDescription("CMS Task Treat Failure");
    	}
    	log.info("ExecCmd resp:"+cspr.toString());
    	return cspr;
    }

}
