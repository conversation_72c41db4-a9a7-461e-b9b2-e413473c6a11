package com.xxl.job.executor.c2out.initialization.handler;

import cn.hutool.core.collection.CollectionUtil;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.pukka.iptv.common.base.constant.ObjectsTypeConstants;
import com.pukka.iptv.common.base.vo.CommonResponse;
import com.pukka.iptv.common.core.constant.SymbolConstant;
import com.pukka.iptv.common.data.model.OutAuto;
import com.pukka.iptv.common.data.model.OutPublish;
import com.pukka.iptv.common.data.model.order.SubOrderObjectsEntity;
import com.pukka.iptv.common.data.model.order.SubOrderXmlEntity;
import com.pukka.iptv.common.data.vo.sys.OutOrderBaseVo;
import com.pukka.iptv.common.data.vo.sys.OutOrderItemVo;
import com.xxl.job.executor.c2out.dao.service.BmsChannelService;
import com.xxl.job.executor.common.enums.ContentPublishTypeEnum;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

/**
 * @Author: chiron
 * @Date: 2023/02/17/09:51
 * @Description:
 */

@Slf4j
@Component(ObjectsTypeConstants.PUBLISH_BEAN_PRE + ObjectsTypeConstants.BUILDERLOGIC_BEAN_PRE
    + ObjectsTypeConstants.SCHEDULEAUTOORDER)
public class ScheduleAutoOrderLogic extends AutoOrderLogic {

  @Autowired
  private BmsChannelService bmsChannelService;

  @Override
  public List<OutOrderItemVo> createOrderRecord(OutPublish outPublish) throws Exception {
    OutAuto outAuto = (OutAuto) outPublish;
    List<String> spIds = checkAuthorizationChannel(outAuto);
    //添加校验sp是否为空
    if (CollectionUtils.isEmpty(spIds)) {
      log.warn("自动下发 -----> 发布类型:{} 注入工单ID:{} 当前节目单未授权到spids为:{} 域,自动下发结束!",
          ContentPublishTypeEnum.getContentTypeNameByContentType(outAuto.getContentType()),
          outAuto.getCorrelateId(), outAuto.getSpIds());
      return new ArrayList<>();
    }
    outAuto.setSpIds(StringUtils.join(spIds.toArray(), SymbolConstant.COMMA));
    log.info("CreateOrderRecord,自动下发实体类OutAuto:{}", outAuto);
    List<OutOrderItemVo> outOrderItemVos = new ArrayList<>();
    List<OutOrderBaseVo> outOrderBaseVos = builderBaseOrder(outAuto);
    if (ObjectUtils.isEmpty(outOrderBaseVos)) {
      log.error(
          "自动下发 -----> 发布类型:{} 注入工单ID:{}，spids为:{} 生成主工单失败，主工单数据为空!",
          ContentPublishTypeEnum.getContentTypeNameByContentType(outAuto.getContentType()),
          outAuto.getCorrelateId(), outAuto.getSpIds());
      return outOrderItemVos;
    }
    List<OutOrderBaseVo> outOrderBaseVos1 = outOrderBaseService.updateBatchBase(outOrderBaseVos);
    if (ObjectUtils.isEmpty(outOrderBaseVos1)) {
      log.error("自动下发 -----> 发布类型:{} 注入工单ID:{}，spids为:{} 数据裤插入主工单失败!",
          ContentPublishTypeEnum.getContentTypeNameByContentType(outAuto.getContentType()),
          outAuto.getCorrelateId(), outAuto.getSpIds());
      return outOrderItemVos;
    }

    for (OutOrderBaseVo outOrderBaseVo : outOrderBaseVos1) {
      List<OutOrderItemVo> orderItemVos = builderItemOrder(outOrderBaseVo);
      outOrderItemVos.addAll(orderItemVos);
    }
    if (ObjectUtils.isEmpty(outOrderItemVos)) {
      log.warn("自动下发 ->>>>>> 发布类型:{} 注入工单ID:{}，获取子工单实体为空",
          ContentPublishTypeEnum.getContentTypeNameByContentType(outAuto.getContentType()),
          outAuto.getCorrelateId());
      return outOrderItemVos;
    }
    return outOrderItemService.insertBatchItem(outOrderItemVos);
  }

  /**
   * 检验频道是否授权
   *
   * @param outAuto
   * @return
   */
  private List<String> checkAuthorizationChannel(OutAuto outAuto) {
    List<String> spIds = new ArrayList<>();
    String[] list = outAuto.getSpIds().split(SymbolConstant.COMMA);
    for (String spId : list) {
      if (StringUtils.isEmpty(spId)) {
        continue;
      }
      CommonResponse<String> orderXmlEntity = null;
      try {
        orderXmlEntity = auto.getOrderXmlEntity(outAuto.getCorrelateId(), spId);
      } catch (Exception e) {
        log.error("自动下发 ->>>>>> cspId:{},correlateId:{},spId:{} 调用feign接口获取工单信息失败，错误信息:{}",
            outAuto.getCspId(), outAuto.getCorrelateId(), spId, e);
        continue;
      }
      if (ObjectUtils.isEmpty(orderXmlEntity.getData())) {
        log.error("自动下发 ->>>>>> cspId:{},correlateId:{},spId:{} 获取工单信息失败，接口调用返回为空",
            outAuto.getCspId(), outAuto.getCorrelateId(), spId);
        continue;
      }
      ObjectMapper objectMapper = new ObjectMapper();
      objectMapper.configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);
      SubOrderXmlEntity subOrderXmlEntity = null;
      try {
        subOrderXmlEntity = objectMapper.readValue(orderXmlEntity.getData(), new TypeReference<SubOrderXmlEntity>() {});
      } catch (Exception e) {
        log.error("自动下发 ->>>>>> cspId:{},correlateId:{},spId:{} JSON反序列化失败，错误信息:{}",
            outAuto.getCspId(), outAuto.getCorrelateId(), spId, e);
        continue;
      }
      List<SubOrderObjectsEntity> subOrderObjectsEntities = subOrderXmlEntity.getSubOrderObjectsEntities();
      if (CollectionUtil.isNotEmpty(subOrderObjectsEntities)) {
        List<String> channelCodeList = subOrderObjectsEntities.stream()
            .filter(ObjectUtils::isNotEmpty)
            .map(SubOrderObjectsEntity::getPropertyDic)
            .filter(ObjectUtils::isNotEmpty)
            .map(propertyDic -> propertyDic.get("ChannelCode"))
            .filter(ObjectUtils::isNotEmpty)
            .distinct()
            .collect(Collectors.toList());
        if (ObjectUtils.isNotEmpty(channelCodeList)) {
          channelCodeList.forEach(spId1 -> {
            if (bmsChannelService.checkAuthorizationChannel(spId1, spId)) {
              spIds.add(spId);
            }
          });
        }
      }
    }
    return spIds;
  }


}
