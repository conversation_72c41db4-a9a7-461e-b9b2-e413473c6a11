package com.xxl.job.executor.service.jobhandler.outorder;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.pukka.iptv.common.base.enums.ItemResultEnum;
import com.pukka.iptv.common.core.util.SpringUtils;
import com.pukka.iptv.common.data.model.OutOrderItem;
import com.pukka.iptv.common.data.model.sys.SysOutPassage;
import com.pukka.iptv.common.data.vo.sys.OutOrderItemVo;
import com.pukka.iptv.common.rabbitmq.constans.OutPublishConstant;
import com.rabbitmq.client.Channel;
import com.xxl.job.executor.c2out.dao.service.OutOrderItemService;
import com.xxl.job.executor.c2out.dao.service.SysOutPassageService;
import com.xxl.job.executor.common.retry.RetryException;
import com.xxl.job.executor.common.util.C2outUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.springframework.amqp.rabbit.annotation.Exchange;
import org.springframework.amqp.rabbit.annotation.Queue;
import org.springframework.amqp.rabbit.annotation.QueueBinding;
import org.springframework.amqp.rabbit.annotation.RabbitListener;
import org.springframework.amqp.rabbit.core.RabbitTemplate;
import org.springframework.amqp.support.AmqpHeaders;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.messaging.handler.annotation.Header;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 * @description: 工单反馈
 */
@Component
@Slf4j
public class OutOrderFeedback implements IOutOrder {
    private OutOrderItemService outOrderItemService = SpringUtils.getBean(OutOrderItemService.class);
    private SysOutPassageService sysOutPassageService = SpringUtils.getBean(SysOutPassageService.class);
    private RabbitTemplate rabbitTemplate = SpringUtils.getBean(RabbitTemplate.class);
    private String errorDescription;

    @Override
    public String getErrorDescription() {
        return errorDescription;
    }

    @Override
    public String getClassPropertyName() {
        return "工单反馈执行器";
    }

    @Autowired
    private C2outUtils c2outUtils;

    @RabbitListener(bindings = {
            @QueueBinding(value = @Queue(value = OutPublishConstant.OUT_BASE_FEEDBACK_QUEUE),
                    exchange = @Exchange(value = OutPublishConstant.OUT_EXCHANGE),
                    key = OutPublishConstant.OUT_BASE_FEEDBACK_ROUTING)}, containerFactory = "rabbitListenerContainerFactory")
    private void recieved(OutOrderItemVo message, Channel channel, @Header(AmqpHeaders.DELIVERY_TAG) Long deliveryTag) throws Exception {
        log.info("RabbitMQ 队列:{}, 开始监听", OutPublishConstant.OUT_BASE_FEEDBACK_QUEUE);
        try {
            String msgInfo = JSON.toJSONString(message);
            log.info("RabbitMQ 队列:{}, 获取信息:{}", OutPublishConstant.OUT_BASE_FEEDBACK_QUEUE, msgInfo);
            c2outUtils.retryOrderHandle(this, msgInfo);
        } catch (Exception exception) {
            log.info("RabbitMQ 队列:{},获取信息失败:{}", OutPublishConstant.OUT_BASE_FEEDBACK_QUEUE, exception);
        } finally {
            //执行是否成功，返回确认应答
            channel.basicAck(deliveryTag, false);
        }
    }

    @Override
    public boolean outHandle(String msg) {
        log.info("执行工单反馈Feedback任务操作 ->>>>>> 开始执行");
        //获取消息参数
        OutOrderItemVo orderItemVo = JSONObject.parseObject(msg, OutOrderItemVo.class);
        if (Objects.isNull(orderItemVo)) {
            log.warn("执行工单反馈Feedback任务操作 ->>>>>> 获取信息实体为空,工单反馈失败!");
            return false;
        }
        OutOrderItemVo item;
        try {
            //获取分发通道信息
            SysOutPassage sysOutPassage = sysOutPassageService.getByLspId(orderItemVo.getLspId());
            if (sysOutPassage == null || sysOutPassage.getCode() == null) {
                errorDescription = "执行工单反馈Feedback任务操作 ->>>>>> 获取分发通道信息为空,工单反馈失败!";
                log.error(errorDescription);
                return false;
            }
            orderItemVo.setOutPassageCode(sysOutPassage.getCode());
            orderItemVo.setOutPassageId(sysOutPassage.getId());
            //查询工单信息
            OutOrderItem outOrderItem = outOrderItemService.getItemOrderInfo(orderItemVo);
            if (com.baomidou.mybatisplus.core.toolkit.ObjectUtils.isEmpty(outOrderItem)) {
                log.warn("correlate_id:{} csp_id:{} lsp_id:{}，检查工单状态是否存在子节点 子工单数据为空!", orderItemVo.getCorrelateId(),
                        orderItemVo.getCspId(), orderItemVo.getLspId());
                return false;
            }
            BeanUtils.copyProperties(outOrderItem,orderItemVo,"cspId","lspId","result","correlateId","resultFileUrl","feedbackTime","errorDescription","errorInfo");
            //计算发布时长
            orderItemVo.setDuration(c2outUtils.calLastedTime(orderItemVo.getCreateTime(),orderItemVo.getFeedbackTime()));
            //检查是否存在子节点
            List<OutOrderItemVo> outOrderItems = outOrderItemService.checkOrderStatus(orderItemVo);
            if (ObjectUtils.isNotEmpty(outOrderItems)) {
                Boolean extracted = c2outUtils.extracted(outOrderItems);
                if (!extracted) {
                    log.error("执行工单反馈Feedback任务操作 ->>>>>> 发送子工单任务至分发mq失败,msg:{}", JSON.toJSONString(outOrderItems));
                }
            }
            //更新工单状态
            Boolean aBoolean = outOrderItemService.updateOutBaseStatus(orderItemVo);
            if (!aBoolean) {
                log.warn("执行工单反馈Feedback任务操作 ->>>>>> 更新工单状态失败,工单信息:{}!", JSON.toJSONString(orderItemVo));
            }
            //更新工单反馈结果
            item = outOrderItemService.callBackResult(orderItemVo);
        } catch (Exception exception) {
            errorDescription = "执行工单反馈Feedback任务操作 ->>>>>> 数据库工单反馈操作失败!";
            log.error(errorDescription + ",错误信息:{}", exception);
            throw new RetryException(JSON.toJSONString(errorDescription));
        }
        try {
            if (item != null && item.getBaseOrderId() != null) {
                rabbitTemplate.convertAndSend(OutPublishConstant.OUT_EXCHANGE, OutPublishConstant.OUT_BASE_REPORT_ROUTING, item);
                log.info("执行工单反馈Feedback任务操作 ->>>>>> 消息发送完毕,消息实体: {}", item);
            } else {
                log.warn("执行工单反馈Feedback任务操作 ->>>>>> 更新工单反馈结果失败,返回消息实体为空. {}", item);
            }
        } catch (Exception exception) {
            errorDescription = "执行工单反馈Feedback任务操作 ->>>>>> 工单反馈发送MQ失败!";
            log.error(errorDescription + ",错误信息:" + exception);
            throw new RetryException(JSON.toJSONString(errorDescription));
        }
        return true;
    }

    @Override
    public List<OutOrderItemVo> getReportEntity(String msg) {
        List<OutOrderItemVo> outOrderItemVoList = new ArrayList<>();
        OutOrderItemVo orderItemVo = JSONObject.parseObject(msg, OutOrderItemVo.class);
        if (Objects.isNull(orderItemVo)) {
            log.warn("执行工单反馈Feedback任务操作 ->>>>>> 获取信息实体为空,工单反馈失败!");
            return null;
        }
        orderItemVo.setResult(ItemResultEnum.Fail.getValue());
        orderItemVo.setErrorDescription(getErrorDescription());
        outOrderItemVoList.add(orderItemVo);
        return outOrderItemVoList;
    }
}
