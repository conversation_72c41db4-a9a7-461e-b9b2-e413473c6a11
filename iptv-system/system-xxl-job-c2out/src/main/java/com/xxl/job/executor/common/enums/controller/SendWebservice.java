package com.xxl.job.executor.common.enums.controller;

import com.pukka.iptv.common.base.vo.CommonResponse;
import com.xxl.job.executor.c2out.dao.entity.ResultResponse;
import com.xxl.job.executor.common.util.C2outUtils;
import io.swagger.annotations.Api;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.Valid;

/**
 * @author: chiron
 * Date: 2022/3/5 6:02 PM
 * Description:
 */

@RestController
@RequestMapping(value = "/sendwebservice", produces = MediaType.APPLICATION_JSON_VALUE)
@Api(tags = "发布下游接口")
@ConditionalOnProperty(prefix = "c2out", name = "SendWebservice", havingValue = "true")
@Slf4j
public class SendWebservice {
    @Autowired
    private C2outUtils c2outUtils;

    @PostMapping(value = "/send")
    public CommonResponse<Boolean> send(@Valid @RequestParam(name = "path", required = true) String path, @Valid @RequestParam(name = "cspId", required = true) String cspId, @Valid @RequestParam(name = "lspId", required = true) String lspId
            , @Valid @RequestParam(name = "correlateId", required = true) String correlateId, @Valid @RequestParam(name = "upFilePath", required = true) String upFilePath) {
        log.info("发布下游接口 ----->>> 开始执行,path:{},cspid:{},lspid:{},correlateId:{},cmdFilePath:{}", path, cspId, lspId, correlateId, upFilePath);
        ResultResponse resultResponse = c2outUtils.startSendCsp(path, cspId, lspId, correlateId, upFilePath);
        log.info("发布下游接口 ----->>> 执行结果:{} 结果描述:{}.执行参数，path:{},cspid:{},lspid:{},correlateId:{},cmdFilePath:{}", resultResponse.getResult().equals(true) ? "成功" : "失败",resultResponse.getDescription(), path, cspId, lspId, correlateId, upFilePath);
        return CommonResponse.success(true);
    }
}