package com.xxl.job.executor.c2out.initialization;

import com.alibaba.fastjson.JSON;
import com.pukka.iptv.common.base.constant.ObjectsTypeConstants;
import com.pukka.iptv.common.base.constant.PublishParamTypeConstants;
import com.pukka.iptv.common.base.constant.RedisKeyConstants;
import com.pukka.iptv.common.base.enums.*;
import com.pukka.iptv.common.base.util.UUID;
import com.pukka.iptv.common.core.constant.SymbolConstant;
import com.pukka.iptv.common.core.enums.LimitStatusEnum;
import com.pukka.iptv.common.core.util.SpringUtils;
import com.pukka.iptv.common.data.model.OutParamExpand;
import com.pukka.iptv.common.data.model.OutPublish;
import com.pukka.iptv.common.data.model.order.SubOrderXmlEntity;
import com.pukka.iptv.common.data.model.sys.SysDictionaryItem;
import com.pukka.iptv.common.data.model.sys.SysOutPassage;
import com.pukka.iptv.common.data.model.sys.SysSp;
import com.pukka.iptv.common.data.vo.sys.OutOrderBaseVo;
import com.pukka.iptv.common.data.vo.sys.OutOrderItemVo;
import com.pukka.iptv.common.redis.service.RedisService;
import com.xxl.job.executor.c2out.dao.entity.ResultResponse;
import com.xxl.job.executor.c2out.dao.mapper.OutOrderBaseMapper;
import com.xxl.job.executor.c2out.dao.service.OutOrderBaseService;
import com.xxl.job.executor.c2out.dao.service.OutOrderItemService;
import com.xxl.job.executor.c2out.dao.service.SysDictionaryItemService;
import com.xxl.job.executor.c2out.strategy.handler.IPublishStrategyManage;
import com.xxl.job.executor.c2out.utils.OutOperateUtil;
import com.xxl.job.executor.c2out.utils.SiteOperateUtil;
import com.xxl.job.executor.c2out.xml.XmlRulesLoadStrategy;
import com.xxl.job.executor.common.config.XmlGenerateRulesConfig;
import com.xxl.job.executor.common.config.XxlJobConfig;
import com.xxl.job.executor.common.enums.ChildNodeStatusEnum;
import com.xxl.job.executor.common.retry.RetryException;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.retry.RetryCallback;
import org.springframework.retry.RetryContext;
import org.springframework.retry.support.RetryTemplate;
import org.springframework.stereotype.Component;

import java.util.*;

/**
 * @author: chiron
 * Date: 2022/3/15 2:29 PM
 * Description: 构造工单任务
 */
@Slf4j
@Component(ObjectsTypeConstants.PUBLISH_BEAN_PRE + ObjectsTypeConstants.BUILDERLOGIC_BEAN_PRE)
public class BuilderLogic implements IBuilderLogic {

    @Autowired
    private RedisService redisService;
    @Autowired
    private OutOperateUtil outOperateUtil;
    @Autowired
    OutOrderBaseMapper outOrderBaseMapper;
    @Autowired
    private OutOrderItemService outOrderItemService;
    @Autowired
    private OutOrderBaseService outOrderBaseService;
    @Autowired
    private XmlRulesLoadStrategy xmlRulesLoadStrategy;
    @Autowired
    private XmlGenerateRulesConfig xmlGenerateRulesConfig;
    @Autowired
    private SysDictionaryItemService sysDictionaryItemService;
    @Autowired
    private XxlJobConfig xxlJobConfig;

    private RetryTemplate retryTemplate = SpringUtils.getBean("rabbitRetry");
    private IPublishStrategyManage iPublishStrategyManage = SpringUtils.getBean(
            IPublishStrategyManage.class);

    @Override
    public List<OutOrderItemVo> createOrderRecord(OutPublish outPublish) throws Exception {
        List<OutOrderItemVo> outOrderItemVos = new ArrayList<>();
        List<OutOrderBaseVo> outOrderBaseVos = builderBaseOrder(outPublish);
        if (ObjectUtils.isEmpty(outOrderBaseVos)) {
            log.error(
                    "生成工单任务 -----> 发布类型:{} 内容IDS:{}，spid为:{} 生成主工单失败，主工单数据为空!",
                    outPublish.getContentType(), outPublish.getContentIds(), outPublish.getSpId());
            return outOrderItemVos;
        }
        List<OutOrderBaseVo> outOrderBaseVos1 = outOrderBaseService.insertBatchBase(
                outOrderBaseVos);
        if (ObjectUtils.isEmpty(outOrderBaseVos1)) {
            log.error("生成工单任务 -----> 发布类型:{} 内容IDS:{}，spid为:{} 数据裤插入主工单失败!",
                    outPublish.getContentType(), outPublish.getContentIds(), outPublish.getSpId());
            return outOrderItemVos;
        }
        //ToDo: 普通发布工单埋点

        for (OutOrderBaseVo outOrderBaseVo : outOrderBaseVos1) {
            List<OutOrderItemVo> orderItemVos = builderItemOrder(outOrderBaseVo);
            outOrderItemVos.addAll(orderItemVos);
        }
        if (ObjectUtils.isEmpty(outOrderItemVos)) {
            log.warn("执行生成工单任务操作 ->>>>>> 发布类型:{} 内容ID:{}，获取子工单实体为空",
                    outPublish.getContentType(), outPublish.getContentIds());
            return outOrderItemVos;
        }
        List<OutOrderItemVo> outOrderItemVos1 = outOrderItemService.insertBatchItem(
                outOrderItemVos);
        return outOrderItemVos1;
    }


    /**
     * 创建主工单
     *
     * @return
     */
    @Override
    public List<OutOrderBaseVo> builderBaseOrder(OutPublish outPublish) throws Exception {
        List<OutOrderBaseVo> outOrderBaseVos = new ArrayList<>();
        String contentIds = outPublish.getContentIds();
        Integer contentType = outPublish.getContentType();
        String spId = outPublish.getSpId();
        //获取主工单所需信息
        SysSp sysSp = redisService.getCacheMapValue(RedisKeyConstants.SYS_SP, spId);
        if (ObjectUtils.isEmpty(sysSp)) {
            log.error(
                    "生成工单任务 -----> 发布类型:{} 内容IDS:{}，生成工单，Sp数据为空,未查询到id为:{}的sp数据",
                    contentType, contentIds, spId);
            return outOrderBaseVos;
        }
        if (ObjectUtils.isEmpty(sysSp.getBmsSpChannelId())) {
            log.error(
                    "生成工单任务 -----> 发布类型:{} 内容IDS:{}，生成工单，未查询到spId为:{}的渠道数据",
                    contentType, contentIds, spId);
            return outOrderBaseVos;
        }
        SysDictionaryItem sysDictionaryItem = sysDictionaryItemService.getById(
                sysSp.getBmsSpChannelId());
        String bmsSpChannelCode = sysDictionaryItem.getCode();
        if (ObjectUtils.isEmpty(bmsSpChannelCode)) {
            log.error(
                    "生成工单任务 -----> 发布类型:{} 内容IDS:{}，生成工单，bmsSpChannelCode数据为空,未查询到spId为:{}的渠道数据",
                    contentType, contentIds, spId);
            return outOrderBaseVos;
        }
        for (String id : contentIds.split(SymbolConstant.COMMA)) {
            try {
                OutOrderBaseVo outOrderBaseVo = new OutOrderBaseVo();
                BeanUtils.copyProperties(outPublish, outOrderBaseVo);
                //适配节目单组合发布，当类型为节目单时查询频道信息
                if (ContentTypeEnum.SCHEDULE.getValue().equals(contentType)) {
                    contentType = ContentTypeEnum.CHANNEL.getValue();
                }
                String outPassageIdArray = outOrderBaseService.getOutPassageIds(id, contentType);
                if (StringUtils.isEmpty(outPassageIdArray)) {
                    String errorDescription = String.format(
                            "生成工单任务 -----> 发布类型:%s 内容ID:%s，生成工单，分发通道数据为空,未查询到spId为:%s的分发通道数据",
                            contentType, id, spId);
                    log.error(errorDescription);
                    throw new RetryException(errorDescription);
                }
                OutOrderItemVo item = outOrderItemService.getBmsContentCodeAndName(id, contentType);
                if (Objects.isNull(item)) {
                    log.error("生成工单任务 -----> 发布类型:{} 内容ID:{}，生成工单 信息数据异常",
                            contentType,
                            id);
                    continue;
                }
                //组装主工单
                String correlateId = UUID.nextSnow().toString();
                //业务ID前缀组成，采用雪花算法
                outOrderBaseVo.setCorrelateId(correlateId);
                outOrderBaseVo.setBmsSpChannelCode(bmsSpChannelCode);
                outOrderBaseVo.setSpId(Long.valueOf(spId));
                outOrderBaseVo.setSpName(sysSp.getName());
                outOrderBaseVo.setBmsContentId(Long.valueOf(id));
                outOrderBaseVo.setStatus(ItemStatusEnum.StayHandle.getValue());
                outOrderBaseVo.setBmsSpChannelId(sysSp.getBmsSpChannelId());
                outOrderBaseVo.setBmsSpChannelName(sysSp.getBmsSpChannelName());
                outOrderBaseVo.setOutPassageIds(outPassageIdArray);
                outOrderBaseVo.setShowName(item.getShowName());
                outOrderBaseVo.setReportStatus(ReportStatusEnum.StayReport.getValue());
                outOrderBaseVo.setOrderType(OrderTypeEnums.GENERA.getCode());
                outOrderBaseVo.setCpId(item.getCpId());
                outOrderBaseVo.setCpName(item.getCpName());
                outOrderBaseVo.setBmsContentCode(item.getBmsContentCode());
                outOrderBaseVo.setRetryCount(outPublish.getRetryCount());
                if (!StringUtils.isEmpty(outPublish.getCreatorId())) {
                    try {
                        outOrderBaseVo.setCreatorId(Long.parseLong(outPublish.getCreatorId()));
                    } catch (Exception exception) {
                        log.error(
                                "生成工单任务 -----> 发布类型:{} 内容ID:{}，CreatorId:{} CreatorId数据转换异常!",
                                contentType, id, outPublish.getCreatorId());
                    }
                }
                //拓展字段填充
                resolutionOutParam(outOrderBaseVo, outPublish.getOutParam());
                outOrderBaseVo.setCreatorName(outPublish.getCreatorName());

                ResultResponse resultResponse = generateXmlEntity(outOrderBaseVo);
                if (resultResponse.getResult()) {
                    outOrderBaseVo.setSubOrderXmlEntity(resultResponse.getData());
                    if (xmlGenerateRulesConfig.generateBaseOrderEnable) {
                        String cmdFilePath = outOperateUtil.generateXmlUpload(
                                outOrderBaseVo.getSubOrderXmlEntity(),
                                outOrderBaseVo.getCorrelateId(),
                                outOrderBaseVo.getSpId(), outOrderBaseVo.getCpId(),
                                outOrderBaseVo.getBmsContentId(), outOrderBaseVo.getContentType(),
                                null);
                        if (StringUtils.isEmpty(cmdFilePath)) {
                            log.error(
                                    "生成工单任务 -----> 发布类型:{} 内容ID:{}，生成主工单 主工单生成xml文件并上传ftp失败!",
                                    contentType, id);
                        } else {
                            outOrderBaseVo.setCmdFileUrl(cmdFilePath);
                            outOrderBaseVo.getSubOrderXmlEntity().setCmdFilePath(cmdFilePath);
                        }
                    }
                    outOrderBaseVos.add(outOrderBaseVo);
                    log.debug("生成工单任务 -----> 发布类型:{} 内容ID:{}，添加主工单内容:{}!",
                            contentType, id,
                            outOrderBaseVo);
                } else {
                    log.error(
                            "生成工单任务 -----> 发布类型:{} 内容ID:{}，生成主工单 主工单生成xml文件实体失败!",
                            contentType, id);
                    outOrderBaseVo.setStatus(ItemStatusEnum.Fail.getValue());
                    outOrderBaseVo.setStatusDescription(ItemStatusEnum.Fail.toString());
                    outOrderBaseVo.setResult(ItemResultEnum.Fail.getValue());
                    outOrderBaseVo.setErrorDescription(
                            StringUtils.isNotEmpty(resultResponse.getDescription())
                                    ? resultResponse.getDescription()
                                    : String.format(
                                            "生成工单任务 -----> 发布类型:%s 内容ID:%s，生成主工单 主工单生成xml文件实体失败!",
                                            contentType, id));
                    //主工单异常需要更新工单状态,失败主工单直接插入数据库
                    boolean b = outOrderBaseMapper.insertBase(outOrderBaseVo);
                    //更新数据库并发布上报接口
                    if (b) {
                        Boolean aBoolean = outOperateUtil.transferReport(
                                outOrderBaseVo.getErrorDescription(),
                                outOrderBaseVo.getId());
                        if (!aBoolean) {
                            log.error(
                                    "生成工单任务 -----> 发布类型:{} 内容ID:{}，数据:{} 主工单生成失败信息上报反馈接口失败!",
                                    outOrderBaseVo.getContentType(),
                                    outOrderBaseVo.getBmsContentId(),
                                    outOrderBaseVo);
                        }
                    } else {
                        log.error(
                                "生成工单任务 -----> 发布类型:{} 内容ID:{}，数据:{} 生成主工单 主工单生成xml文件实体失败,处理结果入库失败!",
                                outOrderBaseVo.getContentType(), outOrderBaseVo.getBmsContentId(),
                                JSON.toJSONString(outOrderBaseVo));
                    }
                }

            } catch (Exception exception) {
                log.error("生成工单任务 -----> 发布类型:{} 内容ID:{} 添加主工单内容失败:{}",
                        contentType,
                        id, exception);
            }
        }
        return outOrderBaseVos;
    }

    /**
     * 创建子工单
     *
     * @return
     */
    @Override
    public List<OutOrderItemVo> builderItemOrder(OutOrderBaseVo outOrderBaseVo) throws Exception {
        List<OutOrderItemVo> outOrderItemVos = new ArrayList<>();
        String[] outPassageIds = outOrderBaseVo.getOutPassageIds().split(SymbolConstant.COMMA);
        for (String outPassageId : outPassageIds) {
            try {
                OutOrderItemVo outOrderItemVo = new OutOrderItemVo();
                //获取通道信息
                SysOutPassage sysOutPassage = redisService.getCacheMapValue(
                        RedisKeyConstants.SYS_OUT_PASSAGE, String.valueOf(outPassageId));
                if (Objects.isNull(sysOutPassage)) {
                    log.error(
                            "生成工单任务 -----> 发布类型:{} 内容ID:{}，生成工单，获取分发通道{} 信息数据异常",
                            outOrderBaseVo.getContentType(), outOrderBaseVo.getBmsContentId(),
                            outPassageId);
                    continue;
                }
                //业务ID前缀组成，采用雪花算法
                String correlateId = UUID.nextSnow().toString();
                outOrderItemVo.setPriority(outOrderBaseVo.getPriority());
                outOrderItemVo.setBmsSpChannelCode(outOrderBaseVo.getBmsSpChannelCode());
                outOrderItemVo.setCorrelateId(correlateId);
                outOrderItemVo.setBaseOrderId(outOrderBaseVo.getId());
                outOrderItemVo.setContentType(String.valueOf(outOrderBaseVo.getContentType()));
                outOrderItemVo.setAction(outOrderBaseVo.getAction());
                outOrderItemVo.setShowName(outOrderBaseVo.getShowName());
                outOrderItemVo.setBmsContentId(outOrderBaseVo.getBmsContentId());
                outOrderItemVo.setBmsContentCode(outOrderBaseVo.getBmsContentCode());
                String cspId = outOrderBaseService.getCspId(outOrderBaseVo.getContentType(),
                        outOrderBaseVo.getBmsContentId(), outPassageId, outOrderBaseVo.getCpId());
                outOrderItemVo.setCspId(cspId);
                outOrderItemVo.setLspId(sysOutPassage.getLspId());
                outOrderItemVo.setOutPassageId(Long.parseLong(outPassageId));
                outOrderItemVo.setOutPassageCode(sysOutPassage.getCode());
                outOrderItemVo.setOutPassageName(sysOutPassage.getName());
                outOrderItemVo.setStatus(ItemStatusEnum.StayHandle.getValue());
                outOrderItemVo.setPublishType(String.valueOf(PublishTypeEnum.Common.getValue()));
                outOrderItemVo.setSpId(outOrderBaseVo.getSpId());
                outOrderItemVo.setSpName(outOrderBaseVo.getSpName());
                outOrderItemVo.setCpId(outOrderBaseVo.getCpId());
                outOrderItemVo.setCpName(outOrderBaseVo.getCpName());
                outOrderItemVo.setRetryCount(outOrderBaseVo.getRetryCount());
                outOrderItemVo.setCreatorId(outOrderBaseVo.getCreatorId());
                outOrderItemVo.setCreatorName(outOrderBaseVo.getCreatorName());
                outOrderItemVo.setBmsSpChannelId(outOrderBaseVo.getBmsSpChannelId());
                outOrderItemVo.setBmsSpChannelName(outOrderBaseVo.getBmsSpChannelName());
                outOrderItemVo.setPublishContentType(outOrderBaseVo.getContentType().equals(11)
                        ? outOrderBaseService.getPublishContentType(
                        String.valueOf(outOrderBaseVo.getContentType()))
                        : String.valueOf(outOrderBaseVo.getContentType()));
                outOrderItemVo.setPublishWithMovie(sysOutPassage.getPublishMovie());
                outOrderItemVo.setPublishWithPicture(sysOutPassage.getPublishPicture());
                outOrderItemVo.setPath(sysOutPassage.getPath());
                outOrderItemVo.setChildNodeExist(ChildNodeStatusEnum.NOT_EXIST.getCode());
                //填充图片信息
                if (ObjectUtils.isNotEmpty(outOrderBaseVo.getParameters())) {
                    OutParamExpand outParamExpand = JSON.parseObject(outOrderBaseVo.getParameters(),
                            OutParamExpand.class);
                    outOrderItemVo.setPicIds(outParamExpand.getSpareMap());
                }
                if (ObjectUtils.isNotEmpty(outOrderBaseVo.getCmdFileUrl())
                        && LimitStatusEnum.NOT_LIMIT.getCode()
                        .equals(sysOutPassage.getLimitStatus())) {
                    //判断是否采用主工单文件
                    Boolean aBoolean = xmlRulesLoadStrategy.checkBaseStrategy(
                            sysOutPassage.getPublishPicture(), sysOutPassage.getPublishMovie(),
                            outOrderItemVo.getOutPassageId());
                    if (aBoolean) {
                        outOrderItemVo.setCmdFileUrl(outOrderBaseVo.getCmdFileUrl());
                        outOrderItemVos.add(outOrderItemVo);
                        log.debug(
                                "生成工单任务 -----> 发布类型:{} 内容ID:{} 分发通道id:{} 子工单信息:{}",
                                outOrderBaseVo.getContentType(), outOrderBaseVo.getBmsContentId(),
                                outOrderItemVo.getOutPassageId(), outOrderItemVo);
                        continue;
                    }
                }
                //填充是否需要携带子工单图片/视频介质
                outOrderBaseVo.getSubOrderXmlEntity()
                        .setPublishWithMovie(sysOutPassage.getPublishMovie());
                outOrderBaseVo.getSubOrderXmlEntity()
                        .setPublishWithPicture(sysOutPassage.getPublishPicture());
                SubOrderXmlEntity subOrderXmlEntity = SiteOperateUtil.deepClone(
                        outOrderBaseVo.getSubOrderXmlEntity());
                //是否限速
                if (xxlJobConfig.ftpLimitEnableGlobal
                        && LimitStatusEnum.LIMIT.getCode().equals(sysOutPassage.getLimitStatus())) {
                    Boolean isReplace = outOperateUtil.replaceMediaUsername(subOrderXmlEntity,
                            outOrderItemVo);
                    if (!isReplace) {
                        log.warn(
                                "xml规则集合策略 -----> 生成xml文件,替换视频存储账号 失败.消息实体:{}!",
                                subOrderXmlEntity);
                    }
                }
                //判断子工单是否需要单独生成xml
                String cmdFilePath = outOperateUtil.generateXmlUpload(subOrderXmlEntity,
                        outOrderItemVo.getCorrelateId(), outOrderItemVo.getSpId(),
                        outOrderItemVo.getCpId(),
                        outOrderItemVo.getBmsContentId(),
                        Integer.valueOf(outOrderItemVo.getContentType()),
                        outOrderItemVo.getOutPassageId());
                if (StringUtils.isEmpty(cmdFilePath)) {
                    log.error(
                            "生成工单任务 -----> 发布类型:{} 内容ID:{}，生成主工单 主工单生成xml文件并上传ftp失败!",
                            outOrderItemVo.getContentType(), outOrderItemVo.getBmsContentId());
                    outOrderItemVo.setStatus(ItemStatusEnum.Fail.getValue());
                    outOrderItemVo.setStatusDescription(ItemStatusEnum.Fail.toString());
                    outOrderItemVo.setResult(ItemResultEnum.Fail.getValue());
                    outOrderItemVo.setErrorDescription(
                            String.format(
                                    "生成工单任务 -----> 发布类型:%s 内容ID:%s,子工单生成xml文件实体失败!",
                                    outOrderItemVo.getContentType(),
                                    outOrderItemVo.getBmsContentId()));
                    //主工单异常需要更新工单状态,失败主工单直接插入数据库
                    boolean b = outOrderItemService.insertItem(outOrderItemVo);
                    //更新数据库并发布上报接口
                    if (!b) {
                        log.error(
                                "生成工单任务 -----> 发布类型:{} 内容ID:{}，数据:{} 子工单生成xml文件实体失败,处理结果入库失败!",
                                outOrderBaseVo.getContentType(), outOrderBaseVo.getBmsContentId(),
                                JSON.toJSONString(outOrderBaseVo));
                    }
                    Boolean aBoolean = outOperateUtil.transferReport(
                            outOrderItemVo.getErrorDescription(),
                            outOrderBaseVo.getId());
                    if (!aBoolean) {
                        log.error(
                                "生成工单任务 -----> 发布类型:{} 内容ID:{}，数据:{} 子工单生成失败信息上报反馈接口失败!",
                                outOrderBaseVo.getContentType(), outOrderBaseVo.getBmsContentId(),
                                outOrderBaseVo);
                    }
                    continue;
                } else {
                    outOrderItemVo.setCmdFileUrl(cmdFilePath);
                }
                outOrderItemVos.add(outOrderItemVo);
                log.debug("生成工单任务 -----> 发布类型:{} 内容ID:{} 分发通道id:{} 子工单信息:{}",
                        outOrderBaseVo.getContentType(), outOrderBaseVo.getBmsContentId(),
                        outOrderItemVo.getOutPassageId(), outOrderItemVo);
            } catch (Exception exception) {
                log.error("生成工单任务 -----> 发布类型:{} 内容ID:{} 添加子工单信息失败:{}",
                        outOrderBaseVo.getContentType(), outOrderBaseVo.getBmsContentId(),
                        exception);
            }
        }
        return outOrderItemVos;
    }

    /**
     * 生成xml实体
     *
     * @param outOrderBaseVo
     * @return
     */
    protected ResultResponse generateXmlEntity(OutOrderBaseVo outOrderBaseVo) {
        ResultResponse result = new ResultResponse();
        try {
            ResultResponse finalResult = result;
            RetryCallback<Object, Throwable> objectThrowableRetryCallback = new RetryCallback<Object, Throwable>() {
                @Override
                public Object doWithRetry(RetryContext retryContext) throws Throwable {
                    ResultResponse resultResponse = new ResultResponse();
                    try {
                        resultResponse = iPublishStrategyManage.startPublish(outOrderBaseVo);
                        if (!resultResponse.getResult()) {
                            String errorMsg = String.format(
                                    "生成主工单实体失败,错误信息:%s",
                                    resultResponse.getDescription());
                            finalResult.setErrorResult(errorMsg);
                            throw new RetryException(errorMsg);
                        }
                    } catch (Exception exception) {
                        String errorMsg = String.format(
                                "生成主工单实体失败,错误信息:%s",
                                exception.getMessage());
                        throw new RetryException(errorMsg);
                    }
                    return resultResponse;
                }
            };
            Object execute = retryTemplate.execute(objectThrowableRetryCallback);
            result = (ResultResponse) execute;
        } catch (Throwable throwable) {
            String errorMsg = result.getDescription() != null ? result.getDescription()
                    : "生成主工单实体失败,重试至最大次数!";
            result.setErrorResult(errorMsg);
            log.error(errorMsg);
        }
        return result;
    }

    /**
     * 解析分发实体拓展参数
     *
     * @param outOrderBaseVo
     * @param outParam
     * @return
     */
    protected void resolutionOutParam(OutOrderBaseVo outOrderBaseVo,
            Map<String, OutParamExpand> outParam) {
        if (ObjectUtils.isEmpty(outParam)) {
            log.warn("生成工单任务 -----> 发布类型:{} 内容id:{},spid为:{} 无拓展参数!",
                    outOrderBaseVo.getContentType(), outOrderBaseVo.getBmsContentId(),
                    outOrderBaseVo.getSpId());
            return;
        }
        for (Map.Entry<String, OutParamExpand> outParamExpandEntry : outParam.entrySet()) {
            switch (outParamExpandEntry.getKey()) {
                //频道和多节目单组合
                case PublishParamTypeConstants.ORDER_CHANNELSCHEDULE:
                    OutParamExpand paramExpand = outParamExpandEntry.getValue();
                    if (ObjectUtils.isEmpty(paramExpand)) {
                        log.warn(
                                "生成工单任务 -----> 发布类型:{} 内容id:{},spid为:{} 频道和多节目单组合 分发接口拓展实体为空!",
                                outOrderBaseVo.getContentType(), outOrderBaseVo.getBmsContentId(),
                                outOrderBaseVo.getSpId());
                        continue;
                    }
                    Map<String, String> paramExpandSpareMap = paramExpand.getSpareMap();
                    if (ObjectUtils.isEmpty(paramExpandSpareMap)) {
                        log.warn(
                                "生成工单任务 -----> 发布类型:{} 内容id:{},spid为:{} 频道和多节目单组合 分发接口拓展集合为空!",
                                outOrderBaseVo.getContentType(), outOrderBaseVo.getBmsContentId(),
                                outOrderBaseVo.getSpId());
                        continue;
                    }
                    outOrderBaseVo.setBmsRelations(JSON.toJSONString(paramExpand));
                    try {
                        Optional<String> first = paramExpandSpareMap.values().stream().findFirst();
                        if (ObjectUtils.isNotEmpty(first) && ObjectUtils.isNotEmpty(first.get())) {
                            outOrderBaseVo.setAction(Integer.valueOf(first.get()));
                        }
                    } catch (Exception exception) {
                        log.warn(
                                "生成工单任务 -----> 发布类型:{} 内容id:{},spid为:{} 频道和多节目单组合 获取节目单action失败,错误信息:{}!",
                                outOrderBaseVo.getContentType(), outOrderBaseVo.getBmsContentId(),
                                outOrderBaseVo.getSpId(), exception);
                    }
                    break;
                //频道携带物理频道
                case PublishParamTypeConstants.ORDER_PHYSICALANDCHANNEL:
                    OutParamExpand value = outParamExpandEntry.getValue();
                    if (ObjectUtils.isEmpty(value)) {
                        log.warn(
                                "生成工单任务 -----> 发布类型:{} 内容id:{},spid为:{} 频道携带物理频道 分发接口拓展实体为空!",
                                outOrderBaseVo.getContentType(), outOrderBaseVo.getBmsContentId(),
                                outOrderBaseVo.getSpId());
                        continue;
                    }
                    Map<String, String> spareMap = value.getSpareMap();
                    if (ObjectUtils.isEmpty(spareMap)) {
                        log.warn(
                                "生成工单任务 -----> 发布类型:{} 内容id:{},spid为:{} 频道携带物理频道 分发接口拓展集合为空!",
                                outOrderBaseVo.getContentType(), outOrderBaseVo.getBmsContentId(),
                                outOrderBaseVo.getSpId());
                        continue;
                    }
                    outOrderBaseVo.setBmsRelations(JSON.toJSONString(value));
                    break;
                //剧集和子集组合
                case PublishParamTypeConstants.ORDER_SERIESSUBSET:
                    OutParamExpand outParamExpand2 = outParamExpandEntry.getValue();
                    if (ObjectUtils.isEmpty(outParamExpand2)) {
                        log.warn(
                                "生成工单任务 -----> 发布类型:{} 内容id:{},spid为:{} 剧集和子集组合 分发接口拓展实体为空!",
                                outOrderBaseVo.getContentType(), outOrderBaseVo.getBmsContentId(),
                                outOrderBaseVo.getSpId());
                        continue;
                    }
                    Map<String, String> stringMap = outParamExpand2.getSpareMap();
                    if (ObjectUtils.isEmpty(stringMap)) {
                        log.warn(
                                "生成工单任务 -----> 发布类型:{} 内容id:{},spid为:{} 剧集和子集组合 分发接口拓展集合为空!",
                                outOrderBaseVo.getContentType(), outOrderBaseVo.getBmsContentId(),
                                outOrderBaseVo.getSpId());
                        continue;
                    }
                    outOrderBaseVo.setBmsRelations(JSON.toJSONString(outParamExpand2));
                    break;
                //图片和发布动作
                case PublishParamTypeConstants.ORDER_PICTUREACTION:
                    OutParamExpand outParamExpand = outParamExpandEntry.getValue();
                    if (ObjectUtils.isEmpty(outParamExpand)) {
                        log.warn(
                                "生成工单任务 -----> 发布类型:{} 内容id:{},spid为:{} 图片和发布动作 分发接口拓展实体为空!",
                                outOrderBaseVo.getContentType(), outOrderBaseVo.getBmsContentId(),
                                outOrderBaseVo.getSpId());
                        continue;
                    }
                    Map<String, String> outParamExpandSpareMap = outParamExpand.getSpareMap();
                    if (ObjectUtils.isEmpty(outParamExpandSpareMap)) {
                        log.warn(
                                "生成工单任务 -----> 发布类型:{} 内容id:{},spid为:{} 图片和发布动作 分发接口拓展集合为空!",
                                outOrderBaseVo.getContentType(), outOrderBaseVo.getBmsContentId(),
                                outOrderBaseVo.getSpId());
                        continue;
                    }
                    outOrderBaseVo.setParameters(JSON.toJSONString(outParamExpand));
                    break;
                //优先级
                case PublishParamTypeConstants.PRIORITY:
                    OutParamExpand outParamExpandEntryValue = outParamExpandEntry.getValue();
                    Integer priority = outParamExpandEntryValue.getPriority();
                    if (ObjectUtils.isEmpty(priority)) {
                        log.warn(
                                "生成工单任务 -----> 发布类型:{} 内容id:{},spid为:{} 优先级 分发接口拓展参数为空!",
                                outOrderBaseVo.getContentType(), outOrderBaseVo.getBmsContentId(),
                                outOrderBaseVo.getSpId());
                        continue;
                    }
                    outOrderBaseVo.setPriority(priority);
                    break;
                default:
                    log.warn(
                            "生成工单任务 -----> 发布类型:{} 内容id:{},spid为:{},分发接口拓展实体位置参数类别:{},暂不处理!",
                            outOrderBaseVo.getContentType(), outOrderBaseVo.getBmsContentId(),
                            outOrderBaseVo.getSpId(), outParamExpandEntry.getKey());
                    break;
            }
        }
    }

}