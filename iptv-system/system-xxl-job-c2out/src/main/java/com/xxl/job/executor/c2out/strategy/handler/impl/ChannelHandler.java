package com.xxl.job.executor.c2out.strategy.handler.impl;

import com.pukka.iptv.common.api.feign.bms.BmsChannelFeignClient;
import com.pukka.iptv.common.api.feign.cms.CmsChannelFeignClient;
import com.pukka.iptv.common.base.constant.ObjectsTypeConstants;
import com.pukka.iptv.common.base.enums.ActionEnums;
import com.pukka.iptv.common.base.vo.CommonResponse;
import com.pukka.iptv.common.core.util.SafeUtil;
import com.pukka.iptv.common.data.model.bms.BmsChannel;
import com.pukka.iptv.common.data.model.cms.CmsChannel;
import com.pukka.iptv.common.data.model.order.PictureObjectEntity;
import com.pukka.iptv.common.data.model.order.SubOrderMappingsEntity;
import com.pukka.iptv.common.data.model.order.SubOrderObjectsEntity;
import com.pukka.iptv.common.data.model.order.SubOrderXmlEntity;
import com.pukka.iptv.common.data.vo.sys.OutOrderBaseVo;
import com.xxl.job.executor.c2out.dao.entity.ResultResponse;
import com.xxl.job.executor.c2out.utils.SiteOperateUtil;
import com.xxl.job.executor.common.content.FtpHandleConstant;
import com.xxl.job.executor.common.enums.ContentPublishTypeEnum;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.atomic.AtomicInteger;

/**
 * <AUTHOR>
 * @description: 频道发布
 * @create 2021-09-13 9:56
 */
@Component
@Slf4j
public class ChannelHandler extends AbstractHandler {

    @Autowired
    private CmsChannelFeignClient cmsChannelFeignClient;
    @Autowired
    private BmsChannelFeignClient bmsChannelFeignClient;

    // 实体序号
    private int serialNumber = 0;

    private final ThreadLocal<BmsChannel> bmsChannelThreadLocal = new ThreadLocal<>();
    private final ThreadLocal<CmsChannel> cmsChannelThreadLocal = new ThreadLocal<>();


    public BmsChannel getBmsChannel() {
        return bmsChannelThreadLocal.get();
    }

    public CmsChannel getCmsChannel() {
        return cmsChannelThreadLocal.get();
    }

    public void setBmsChannel(BmsChannel bmsChannel) {
        bmsChannelThreadLocal.set(bmsChannel);
    }

    public void setCmsChannel(CmsChannel cmsChannel) {
        cmsChannelThreadLocal.set(cmsChannel);
    }

    public void clear() {
        if (ObjectUtils.isNotEmpty(bmsChannelThreadLocal.get())) {
            bmsChannelThreadLocal.remove();
        }
        if (ObjectUtils.isNotEmpty(cmsChannelThreadLocal.get())) {
            cmsChannelThreadLocal.remove();
        }
    }

    public CmsChannel getCmsChannelByBmsId(Long bmsChannelId) {
        BmsChannel bmsChannel = getBmsChannel(bmsChannelId);
        setBmsChannel(bmsChannel);
        // openfeign远程获取cms_中的数据
        CmsChannel cmsChannel = getCmsChannel(bmsChannel);
        setCmsChannel(cmsChannel);
        return getCmsChannel();
    }

    /**
     * 获取类型 contentType
     *
     * @return
     */
    @Override
    public ContentPublishTypeEnum getContentType() {
        return ContentPublishTypeEnum.CHANNEL;
    }

    /**
     * 频道发布/回收操作
     */
    @Override
    public ResultResponse publishOrRecover(OutOrderBaseVo outOrderBaseVo) {
        ResultResponse resultResponse = new ResultResponse();
        try {
            boolean flag = getObjectData(outOrderBaseVo, resultResponse);
            // 是否组装图片
            if (flag) {
                flag = getPictureData(outOrderBaseVo, resultResponse);
                if (!flag) {
                    return resultResponse;
                }
            }
            flag = getMappingData(outOrderBaseVo, resultResponse);
            if (!flag) {
                return resultResponse;
            }
            // 组装xml
            if (flag) {
                StringBuffer xmlDestination = SiteOperateUtil.getFileName(FtpHandleConstant.DIR_CHANNEL);
                resultResponse.getData().setXmlDestination(xmlDestination.toString());
            }
        } catch (Exception exception) {
            resultResponse.setErrorResult(String.format("执行频道发布操作 ->>>>>> 发布类型:%s 内容ID:%s 执行发布工单操作失败！", getContentType().getContentTypeName(), outOrderBaseVo.getBmsContentId()));
            log.warn("执行频道发布操作 ->>>>>> 发布类型:{} 内容ID:{}，执行发布工单操作失败,错误描述:{}！", getContentType().getContentTypeName(), outOrderBaseVo.getBmsContentId(), exception);
        } finally {
            //清理
            clear();
            return resultResponse;
        }
    }

    @Override
    public boolean getObjectData(OutOrderBaseVo outOrderBaseVo, ResultResponse resultResponse) {
        try {
            SubOrderXmlEntity subOrderXmlEntit = resultResponse.getData();
            // openfeign远程获取到bms_中的数据
            Long bmsChannelId = outOrderBaseVo.getBmsContentId();
            if (getCmsChannelByBmsId(bmsChannelId) == null) {
                resultResponse.setErrorResult(String.format("执行频道发布操作 ->>>>>> 发布类型:%s 内容ID:%s 获取频道信息失败，频道信息为空!", getContentType().getContentTypeName(), outOrderBaseVo.getBmsContentId()));
                return false;
            }

            // 获取特性(Attribute)
            SubOrderObjectsEntity subOrderObjectsEntity = getAttribute(ActionEnums.getInfoByCode(outOrderBaseVo.getAction()), getCmsChannel());

            // 获取属性(Property)
            Map<String, String> propertyDic = getProperty(getCmsChannel());

            subOrderObjectsEntity.setPropertyDic(propertyDic);
            subOrderObjectsEntity.setSerialNumber(serialNumber);

            List<SubOrderObjectsEntity> subOrderObjectsEntities = subOrderXmlEntit.getSubOrderObjectsEntities() != null
                    ? subOrderXmlEntit.getSubOrderObjectsEntities() : new ArrayList<>();
            subOrderObjectsEntities.add(subOrderObjectsEntity);
            subOrderXmlEntit.setSubOrderObjectsEntities(subOrderObjectsEntities);

        } catch (Exception e) {
            resultResponse.setErrorResult(String.format("执行频道发布操作 ->>>>>> 发布类型:%s 内容ID:%s 组装object内容失败!", getContentType().getContentTypeName(), outOrderBaseVo.getBmsContentId()));
            log.error("执行频道发布操作 ->>>>>>  发布类型:{} 内容ID:{} 频道发布，组装object内容失败,错误描述:{}!", getContentType().getContentTypeName(), outOrderBaseVo.getBmsContentId(), e);
            return false;
        }

        return true;
    }

    public Map<String, String> getProperty(CmsChannel cmsChannelParam) {
        BmsChannel bmsChannel = getBmsChannel();
        Map<String, String> propertyDic = new HashMap<>(32);
        propertyDic.put("ChannelNumber", cmsChannelParam.getChannelNumber());
        propertyDic.put("Name", cmsChannelParam.getName());
        propertyDic.put("CallSign", cmsChannelParam.getCallSign());
        propertyDic.put("TimeShift", SafeUtil.getString(cmsChannelParam.getTimeShift()));
        propertyDic.put("StorageDuration", SafeUtil.getString(cmsChannelParam.getStorageDuration()));
        propertyDic.put("TimeShiftDuration", SafeUtil.getString(cmsChannelParam.getTimeShiftDuration()));
        propertyDic.put("Description", cmsChannelParam.getDescription());
        propertyDic.put("Country", cmsChannelParam.getCountry());
        propertyDic.put("State", cmsChannelParam.getState());
        propertyDic.put("City", cmsChannelParam.getCity());
        propertyDic.put("ZipCode", cmsChannelParam.getZipCode());
        propertyDic.put("Type", SafeUtil.getString(cmsChannelParam.getType()));
        propertyDic.put("SubType", SafeUtil.getString(cmsChannelParam.getSubType()));
        propertyDic.put("Language", cmsChannelParam.getLanguage());
        propertyDic.put("Status", SafeUtil.getString(bmsChannel.getStatus()));
        propertyDic.put("StartTime", cmsChannelParam.getStartTime());
        propertyDic.put("EndTime", cmsChannelParam.getEndTime());
        propertyDic.put("Macrovision", SafeUtil.getString(cmsChannelParam.getMacrovision()));
        propertyDic.put("Bilingual", SafeUtil.getString(cmsChannelParam.getBilingual()));
        propertyDic.put("VSPCode", cmsChannelParam.getVspCode());
        return propertyDic;
    }

    public SubOrderObjectsEntity getAttribute(String action, CmsChannel cmsChannelParam) {
        SubOrderObjectsEntity subOrderObjectsEntity = new SubOrderObjectsEntity();
        //subOrderObjectsEntity.setId(SafeUtil.getString(bmsChannel.getId()));
        subOrderObjectsEntity.setElementType(ObjectsTypeConstants.CHANNEL);
        subOrderObjectsEntity.setId(cmsChannelParam.getCode());
        subOrderObjectsEntity.setCode(cmsChannelParam.getCode());
        subOrderObjectsEntity.setAction(action);
        return subOrderObjectsEntity;
    }


    @Override
    public boolean getMappingData(OutOrderBaseVo outOrderBaseVo, ResultResponse resultResponse) {
        SubOrderXmlEntity subOrderXmlEntit = resultResponse.getData();
        // mapping对象
        List<SubOrderMappingsEntity> subOrderMappingsEntityList = subOrderXmlEntit.getSubOrderMappingsEntities() != null
                ? subOrderXmlEntit.getSubOrderMappingsEntities() : new ArrayList<>();
        CmsChannel cmsChannel = getCmsChannel();
        // 组装图片-节目mapping
        try {
            // 图片
            List<SubOrderObjectsEntity> subOrderObjectsEntities = subOrderXmlEntit.getSubOrderObjectsEntities();
            //mapping实体序号
            AtomicInteger sequence = new AtomicInteger();
            for (SubOrderObjectsEntity subOrderObjectsEntity : subOrderObjectsEntities) {
                if (ObjectsTypeConstants.PICTURE.equals(subOrderObjectsEntity.getElementType())) {
                    SubOrderMappingsEntity subOrderMappingsEntity = new SubOrderMappingsEntity();
                    subOrderMappingsEntity.setElementType(ObjectsTypeConstants.CHANNEL);
                    subOrderMappingsEntity.setElementId(cmsChannel.getCode());
                    subOrderMappingsEntity.setElementCode(cmsChannel.getCode());
                    subOrderMappingsEntity.setSerialNumber(sequence.incrementAndGet());
                    PictureObjectEntity pictureObjectEntity = (PictureObjectEntity) subOrderObjectsEntity;
                    subOrderMappingsEntity.setAction(pictureObjectEntity.getAction());
                    subOrderMappingsEntity.setParentType(ObjectsTypeConstants.PICTURE);
                    subOrderMappingsEntity.setParentId(pictureObjectEntity.getCode());
                    subOrderMappingsEntity.setParentCode(pictureObjectEntity.getCode());
                    Map<String, String> propertyDic = new HashMap<>(2);
                    propertyDic.put("Type", pictureObjectEntity.getType());
                    propertyDic.put("Sequence", SafeUtil.getString(pictureObjectEntity.getSequence()));
                    subOrderMappingsEntity.setPropertyDic(propertyDic);
                    subOrderMappingsEntityList.add(subOrderMappingsEntity);
                }
            }
        } catch (Exception exception) {
            resultResponse.setErrorResult(String.format("执行频道发布操作 ->>>>>> 发布类型:%s 内容ID:%s 组装图片mapping内容失败!", getContentType().getContentTypeName(), outOrderBaseVo.getBmsContentId()));
            log.error("执行频道发布操作 ->>>>>> 发布类型:{} 内容ID:{} 频道发布，组装图片mapping内容失败,错误描述:{}!", getContentType().getContentTypeName(), outOrderBaseVo.getBmsContentId(), exception);
            return false;
        }
        subOrderXmlEntit.setSubOrderMappingsEntities(subOrderMappingsEntityList);


        return true;
    }

    /**
     * 通过bmsid获取bms的对象
     *
     * @param bmsChannelId
     * @return
     */
    private BmsChannel getBmsChannel(Long bmsChannelId) {
        CommonResponse<BmsChannel> bmsChannelCommonResponse = bmsChannelFeignClient.getById(bmsChannelId);
        BmsChannel bmsChannel = null;
        if (bmsChannelCommonResponse != null) {
            bmsChannel = bmsChannelCommonResponse.getData();
        }
        return bmsChannel;
    }

    /**
     * 通过bms对象的cmsId获取cms的对象
     *
     * @param bmsChannel
     * @return
     */
    private CmsChannel getCmsChannel(BmsChannel bmsChannel) {
        CmsChannel cmsChannel = null;
        if (bmsChannel != null) {
            CommonResponse<CmsChannel> cmsProgramCommonResponse = cmsChannelFeignClient.getById(bmsChannel.getCmsChannelId());
            if (cmsProgramCommonResponse != null) {
                cmsChannel = cmsProgramCommonResponse.getData();
            }
        }
        return cmsChannel;

    }

}
