package com.xxl.job.executor.service.jobhandler;

import com.pukka.iptv.common.base.constant.ObjectsTypeConstants;
import com.pukka.iptv.common.core.constant.SymbolConstant;
import com.pukka.iptv.common.core.util.SafeUtil;
import com.pukka.iptv.common.core.util.StringUtils;
import com.pukka.iptv.common.rabbitmq.constans.OutPublishConstant;
import com.xxl.job.core.context.XxlJobHelper;
import com.xxl.job.core.handler.annotation.XxlJob;
import com.xxl.job.executor.common.util.C2outUtils;
import com.xxl.job.executor.service.jobhandler.outorder.IOutOrder;
import com.xxl.job.executor.service.jobhandler.outorder.OutOrderAuto;
import com.xxl.job.executor.service.jobhandler.outorder.OutOrderConsume;
import com.xxl.job.executor.service.jobhandler.outorder.OutOrderCreate;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.io.IOException;


/**
 * <AUTHOR>
 * @description:
 * @create 2021-08-30 14:38
 */
@Component
@Slf4j
public class OutPublishXxlJob {

    /**
     * 消费工单执行器（Bean模式）
     */
    @XxlJob("OutOrderConsumeJobHandler")
    public void OutOrderConsumeJobHandler() {
        log.debug("开始处理消费工单执行器!");
        String param = SafeUtil.getString(XxlJobHelper.getJobParam());
        log.debug("消费工单执行器 -----> param:{}", param);
        Integer pullCount = 0;
        String queueName = OutPublishConstant.OUT_ORDER_SEND_QUEUE;
        try {
            if (StringUtils.isNotEmpty(param)) {
                String[] split = param.split(SymbolConstant.COMMA);
                if (split.length <= 1) {
                    pullCount = Integer.valueOf(param);
                    log.warn("消费工单执行器job -----> param:{} 消费工单执行器 队列信息参数不全!", param);
                    return;
                } else {
                    pullCount = StringUtils.isNotEmpty(split[0]) ? Integer.valueOf(split[0]) : 3;
                    if (StringUtils.isNotEmpty(split[1])) {
                        queueName = queueName + SymbolConstant.UNDER_SCORE + split[1];
                    }
                }
            }
        } catch (Exception exception) {
            log.error("消费工单执行器 getJobParam 有误!错误信息:{}", exception);
        }
        try {
            IOutOrder outOrder = new OutOrderConsume();
            C2outUtils c2outUtils = new C2outUtils();
            c2outUtils.receive(pullCount, outOrder, queueName + ObjectsTypeConstants.OTHER_PRIORITY_QUEUE);
            c2outUtils.receive(pullCount, outOrder, queueName + ObjectsTypeConstants.REGIST_PRIORITY_QUEUE);
        } catch (IOException e) {
            log.error("消费工单执行器job失败.错误信息:{}", e);
        }
    }

    @XxlJob("OutAutoJobHandler")
    public void OutAutoJobHandler() {
        log.debug("开始处理自动发布执行器!");
        String param = SafeUtil.getString(XxlJobHelper.getJobParam());
        log.debug("自动发布工单执行器 -----> param:{}", param);
        Integer pullCount = 0;
        String queueName = OutPublishConstant.OUT_AUTO_SEND_QUEUE;
        try {
            pullCount = Integer.valueOf(param);
        } catch (Exception exception) {
            log.error("自动发布工单执行器 getJobParam 有误!错误信息:{}", exception);
        }
        try {
            IOutOrder outOrder = new OutOrderAuto();
            C2outUtils c2outUtils = new C2outUtils();
            c2outUtils.receive(pullCount, outOrder, queueName);
        } catch (IOException e) {
            log.error("自动发布工单执行器job失败.错误信息:{}", e);
        }
    }

}
