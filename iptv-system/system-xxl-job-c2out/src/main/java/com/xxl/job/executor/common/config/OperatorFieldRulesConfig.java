package com.xxl.job.executor.common.config;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Configuration;

/**
 * @Author: chiron
 * @Date: 2023/02/13/15:16
 * @Description:
 */
@Configuration
public class OperatorFieldRulesConfig {
    /** 电信侧 **/
    /**
     * 是否显示预留字段
     */
    @Value("${operator-field-rules.ctc.extend-info-list.visual:false}")
    public Boolean extendInfoListVisual;
    /**
     * 预留字段默认值
     */
    @Value("${operator-field-rules.ctc.extend-info-list.default}")
    public String extendInfoListDefault;
    /**
     * 是否显示节目试看(秒)
     */
    @Value("${operator-field-rules.ctc.pre-duration.visual:false}")
    public Boolean preDurationVisual;
    /**
     * 节目试看(秒)默认值
     */
    @Value("${operator-field-rules.ctc.pre-duration.default}")
    public String preDurationDefault;

    /**
     * 央视SMP属性特殊处理
     */
    @Value("${operator-field-rules.cctv.smp.property-name:爱上总平台}")
    public String cctvPropertyName;
}
