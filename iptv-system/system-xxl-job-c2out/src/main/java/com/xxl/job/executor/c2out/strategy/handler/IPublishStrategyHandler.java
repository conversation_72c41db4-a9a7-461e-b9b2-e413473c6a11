package com.xxl.job.executor.c2out.strategy.handler;

import com.pukka.iptv.common.data.vo.sys.OutOrderBaseVo;
import com.xxl.job.executor.c2out.dao.entity.ResultResponse;
import com.xxl.job.executor.common.enums.ContentPublishTypeEnum;

import java.io.Serializable;
import java.net.MalformedURLException;

/**
 * <AUTHOR>
 * @description: 媒资类型
 * 1：单集   2：子集   3：电视剧   4：系列片   
 * 5：片花6：直播   7：物理频道   8：栏目   
 * 9：产品包   10：节目单   11：图片  12：视频介质   
 * 13：节目图片   14：剧集图片   15：产品包图片   16：栏目图片   17：频道图片
 * 18：栏目节目   19：栏目剧集   20：栏目频道   21：产品包节目   22：产品包剧集
 * @create 2021-08-30 16:11
 */
public interface IPublishStrategyHandler<T extends Serializable> {

    /**
     * 策略类型的方法
     * @return
     */
    ContentPublishTypeEnum getContentType();

    /**
     * 处理发布/回收/更新
     */
    ResultResponse publishOrRecover(OutOrderBaseVo outOrderBaseVo) throws MalformedURLException;

    /**
     * 组装object
     * @param outOrderBaseVo
     * @param resultResponse
     * @return
     */
    boolean getObjectData(OutOrderBaseVo outOrderBaseVo, ResultResponse resultResponse);

    /**
     * 组装mapping
     * @param outOrderBaseVo
     * @param resultResponse
     * @return
     */
    boolean getMappingData(OutOrderBaseVo outOrderBaseVo, ResultResponse resultResponse);

    /**
     * 获取图片信息
     * @param outOrderBaseVo
     * @param resultResponse
     * @return
     */
    boolean getPictureData(OutOrderBaseVo outOrderBaseVo, ResultResponse resultResponse);

    /**
     * 获取视频信息
     * @param outOrderBaseVo
     * @param resultResponse
     * @param cmsProgramId
     * @return
     */
    boolean getMovieData(OutOrderBaseVo outOrderBaseVo, ResultResponse resultResponse,Long cmsProgramId);
}
