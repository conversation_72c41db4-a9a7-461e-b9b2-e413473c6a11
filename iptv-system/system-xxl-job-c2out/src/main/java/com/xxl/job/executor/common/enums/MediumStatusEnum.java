package com.xxl.job.executor.common.enums;

public enum MediumStatusEnum {

    CARRY(1, "携带"),
    NOT_CARRY(2, "不携带");

    private final Integer code;
    private final String info;

    MediumStatusEnum(Integer code, String info)
    {
        this.code = code;
        this.info = info;
    }

    public Integer getCode()
    {
        return code;
    }

    public String getInfo()
    {
        return info;
    }
}
