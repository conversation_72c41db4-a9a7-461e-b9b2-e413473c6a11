package com.xxl.job.executor.service.jobhandler.outorder;

import com.alibaba.fastjson.JSON;
import com.pukka.iptv.common.base.constant.PublishParamTypeConstants;
import com.pukka.iptv.common.base.enums.ItemResultEnum;
import com.pukka.iptv.common.core.constant.SymbolConstant;
import com.pukka.iptv.common.core.util.SpringUtils;
import com.pukka.iptv.common.data.model.OutParamExpand;
import com.pukka.iptv.common.data.model.OutPublish;
import com.pukka.iptv.common.data.vo.sys.OutOrderItemVo;
import com.pukka.iptv.common.rabbitmq.constans.OutPublishConstant;
import com.pukka.iptv.common.rabbitmq.vo.MessageBody;
import com.rabbitmq.client.Channel;
import com.xxl.job.executor.c2out.initialization.IBuilderLogic;
import com.xxl.job.executor.c2out.initialization.IBuilderStrategyManager;
import com.xxl.job.executor.common.config.XxlJobConfig;
import com.xxl.job.executor.common.enums.ContentPublishTypeEnum;
import com.xxl.job.executor.common.util.C2outUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.springframework.amqp.rabbit.annotation.Exchange;
import org.springframework.amqp.rabbit.annotation.Queue;
import org.springframework.amqp.rabbit.annotation.QueueBinding;
import org.springframework.amqp.rabbit.annotation.RabbitListener;
import org.springframework.amqp.support.AmqpHeaders;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.messaging.handler.annotation.Header;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @description: 生成工单任务
 */
@Component
@Slf4j
public class OutOrderCreate implements IOutOrder {
    private XxlJobConfig xxlJobConfig = SpringUtils.getBean(XxlJobConfig.class);
    private IBuilderStrategyManager iBuilderStrategyManager = SpringUtils.getBean(IBuilderStrategyManager.class);

    private String errorDescription;

    @Override
    public String getErrorDescription() {
        return this.errorDescription;
    }

    @Override
    public String getClassPropertyName() {
        return "工单创建执行器";
    }

    @Autowired
    private C2outUtils c2outUtils;

    @RabbitListener(bindings = {
            @QueueBinding(value = @Queue(value = OutPublishConstant.OUT_BASE_ORDER_QUEUE),
                    exchange = @Exchange(value = OutPublishConstant.OUT_EXCHANGE),
                    key = OutPublishConstant.OUT_BASE_ORDER_ROUTING)}, containerFactory = "rabbitListenerContainerFactory")
    private void recieved(MessageBody messageBody, Channel channel, @Header(AmqpHeaders.DELIVERY_TAG) Long deliveryTag) throws Exception {
        log.debug("RabbitMQ 队列:{}, 开始监听", OutPublishConstant.OUT_BASE_ORDER_QUEUE);
        try {
            if (ObjectUtils.isEmpty(messageBody) || ObjectUtils.isEmpty(messageBody.getMsg())) {
                log.warn("执行生成工单任务操作 ->>>>>> 生成工单失败,获取信息为空!");
                return;
            }
            Map<String, Object> msg = messageBody.getMsg();
            String message = JSON.toJSONString(msg);
            log.info("RabbitMQ 队列:{}, 获取信息:{}", OutPublishConstant.OUT_BASE_ORDER_QUEUE, message);
            c2outUtils.retryOrderHandle(this, message);
        } catch (Exception exception) {
            log.info("RabbitMQ 队列:{},获取信息失败:{}", OutPublishConstant.OUT_BASE_ORDER_QUEUE, exception);
        } finally {
            //执行是否成功，返回确认应答
            channel.basicAck(deliveryTag, false);
        }
    }

    @Override
    public List<OutOrderItemVo> getReportEntity(String msg) {
        List<OutOrderItemVo> outOrderItemVoList = new ArrayList<>();
        OutPublish outPublish = JSON.parseObject(msg, OutPublish.class);
        String[] contentIds = outPublish.getContentIds().split(SymbolConstant.COMMA);
        for (String id : contentIds) {
            OutOrderItemVo outOrderItemVo = new OutOrderItemVo();
            BeanUtils.copyProperties(outPublish, outOrderItemVo);
            outOrderItemVo.setBmsContentId(Long.valueOf(id));
            outOrderItemVo.setContentType(String.valueOf(outPublish.getContentType()));
            outOrderItemVo.setResult(ItemResultEnum.Fail.getValue());
            outOrderItemVo.setErrorDescription(errorDescription);
            if (ObjectUtils.isNotEmpty(outPublish.getOutParam())) {
                if (outPublish.getOutParam().containsKey(PublishParamTypeConstants.ORDER_PICTUREACTION)) {
                    OutParamExpand outParamExpand = outPublish.getOutParam().get(PublishParamTypeConstants.ORDER_PICTUREACTION);
                    if (ObjectUtils.isNotEmpty(outParamExpand)) {
                        outOrderItemVo.setPicIds(outParamExpand.getSpareMap());
                    }
                }
            }
            outOrderItemVoList.add(outOrderItemVo);
        }
        return outOrderItemVoList;
    }

    @Override
    public boolean outHandle(String msg) throws Exception {
        log.info("执行生成工单任务操作 ->>>>>> 开始执行");
        //获取消息参数
        OutPublish outPublish = JSON.parseObject(msg, OutPublish.class);
        List<OutOrderItemVo> orderItemVos = null;
        try {
            //填充重试次数
            outPublish.setRetryCount(xxlJobConfig.maxAttempts);
            IBuilderLogic builderStrategy = iBuilderStrategyManager.createBuilderStrategy(ContentPublishTypeEnum.getContentTypeNameByContentType(outPublish.getContentType()));
            orderItemVos = builderStrategy.createOrderRecord(outPublish);
        } catch (Exception exception) {
            errorDescription = "执行生成工单任务操作 ->>>>>> 数据库写入工单操作失败";
            log.error("执行生成工单任务操作 ->>>>>> 数据库写入工单操作失败,工单数据 {} 错误信息:{}", msg, exception);
        }
        try {
            if (ObjectUtils.isNotEmpty(orderItemVos)) {
                log.info("发布类型:{} 内容ID:{} 生成工单成功", outPublish.getContentType(), outPublish.getContentIds());
                Boolean extracted = c2outUtils.extracted(orderItemVos);
                return extracted;
            }
            log.warn("执行生成工单任务操作 ->>>>>> 发布类型:{} 内容ID:{}，生成工单实体为空", outPublish.getContentType(), outPublish.getContentIds());
        } catch (Exception exception) {
            log.error("执行生成工单任务操作 ->>>>>> 工单任务发送MQ失败,工单数据 {} 错误信息:{}", msg, exception);
        }
        return false;
    }

}
