package com.xxl.job.executor.c2out.strategy.handler.impl;

import com.alibaba.fastjson.JSON;
import com.pukka.iptv.common.api.feign.bms.BmsScheduleFeignClient;
import com.pukka.iptv.common.api.feign.cms.CmsScheduleFeignClient;
import com.pukka.iptv.common.base.constant.ObjectsTypeConstants;
import com.pukka.iptv.common.base.enums.ActionEnums;
import com.pukka.iptv.common.base.vo.CommonResponse;
import com.pukka.iptv.common.core.util.SafeUtil;
import com.pukka.iptv.common.data.model.OutParamExpand;
import com.pukka.iptv.common.data.model.bms.BmsSchedule;
import com.pukka.iptv.common.data.model.cms.CmsSchedule;
import com.pukka.iptv.common.data.model.order.SubOrderObjectsEntity;
import com.pukka.iptv.common.data.model.order.SubOrderXmlEntity;
import com.pukka.iptv.common.data.vo.sys.OutOrderBaseVo;
import com.xxl.job.executor.c2out.dao.entity.ResultResponse;
import com.xxl.job.executor.c2out.utils.SiteOperateUtil;
import com.xxl.job.executor.common.content.FtpHandleConstant;
import com.xxl.job.executor.common.enums.ContentPublishTypeEnum;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;


/**
 * <AUTHOR>
 * @description: 节目单发布
 * @create 2021-09-17 11:25
 */
@Component
@Slf4j
public class ScheduleHandler extends AbstractHandler {

    @Autowired
    private CmsScheduleFeignClient cmsScheduleFeignClient;
    @Autowired
    private BmsScheduleFeignClient bmsScheduleFeignClient;

    // 实体序号
    private int serialNumber = 0;

    @Override
    public ContentPublishTypeEnum getContentType() {
        return ContentPublishTypeEnum.SCHEDULE;
    }

    /**
     * 发布/回收操作
     *
     * @param outOrderBaseVo
     * @return
     */
    @Override
    public ResultResponse publishOrRecover(OutOrderBaseVo outOrderBaseVo) {
        ResultResponse resultResponse = new ResultResponse();
        try {
            boolean flag = getObjectData(outOrderBaseVo, resultResponse);
            // 组装xml
            if (flag) {
                StringBuffer xmlDestination = SiteOperateUtil.getFileName(FtpHandleConstant.DIR_SCHEDULE);
                resultResponse.getData().setXmlDestination(xmlDestination.toString());
            }
        } catch (Exception exception) {
            resultResponse.setErrorResult(String.format("执行节目单发布操作 ->>>>>> 发布类型:%s 内容ID:%s 执行发布工单操作失败！", getContentType().getContentTypeName(), outOrderBaseVo.getBmsContentId()));
            log.warn("执行节目单发布操作 ->>>>>> 发布类型:{} 内容ID:{}，执行发布工单操作失败,错误描述:{}！", getContentType().getContentTypeName(), outOrderBaseVo.getBmsContentId(), exception);
        } finally {
            return resultResponse;
        }
    }

    /**
     * 设置Object
     *
     * @param outOrderBaseVo
     * @param resultResponse
     * @return
     */
    @Override
    public boolean getObjectData(OutOrderBaseVo outOrderBaseVo, ResultResponse resultResponse) {
        try {
            SubOrderXmlEntity subOrderXmlEntit = resultResponse.getData();
            List<SubOrderObjectsEntity> subOrderObjectsEntities = subOrderXmlEntit.getSubOrderObjectsEntities() != null
                    ? subOrderXmlEntit.getSubOrderObjectsEntities() : new ArrayList<>();
            //获取bmsRelations信息
            String bmsRelations = outOrderBaseVo.getBmsRelations();
            OutParamExpand outParamExpand = JSON.parseObject(bmsRelations, OutParamExpand.class);
            if (ObjectUtils.isEmpty(outParamExpand) || ObjectUtils.isEmpty(outParamExpand.getSpareMap())) {
                resultResponse.setErrorResult(String.format("执行节目单发布操作 ->>>>>> 发布类型:%s 内容ID:%s ParamExpand:%s 获取节目单信息失败，频道和多节目单组合为空!"
                        , getContentType().getContentTypeName(), outOrderBaseVo.getBmsContentId(), JSON.toJSONString(outParamExpand)));
                return false;
            }
            Map<String, String> spareMap = outParamExpand.getSpareMap();
            for (Map.Entry<String, String> entry : spareMap.entrySet()) {
                Long bmsScheduleId = Long.parseLong(entry.getKey());
                // openfeign远程获取到bms_中的数据
                BmsSchedule bmsSchedule = getBmsSchedule(bmsScheduleId);
                // openfeign远程获取cms_中的数据
                CmsSchedule cmsSchedule = getCmsSchedule(bmsSchedule);
                if (ObjectUtils.isEmpty(cmsSchedule)) {
                    resultResponse.setErrorResult(String.format("执行节目单发布操作 ->>>>>> 发布类型:%s 内容ID:%s 获取节目单信息失败，节目单信息为空!", getContentType().getContentTypeName(), outOrderBaseVo.getBmsContentId()));
                    return false;
                }
                // 获取特性(Attribute)
                SubOrderObjectsEntity subOrderObjectsEntity = getAttribute(ActionEnums.getInfoByCode(Integer.parseInt(entry.getValue())), cmsSchedule);
                // 获取属性(Property)
                Map<String, String> propertyDic = getProperty(cmsSchedule, bmsSchedule);
                subOrderObjectsEntity.setPropertyDic(propertyDic);
                subOrderObjectsEntity.setSerialNumber(++serialNumber);
                subOrderObjectsEntities.add(subOrderObjectsEntity);
            }
            subOrderXmlEntit.setSubOrderObjectsEntities(subOrderObjectsEntities);
        } catch (Exception exception) {
            resultResponse.setErrorResult(String.format("执行节目单发布操作 ->>>>>> 发布类型:%s 内容ID:%s 组装object内容失败!", getContentType().getContentTypeName(), outOrderBaseVo.getBmsContentId()));
            log.error("执行节目单发布操作 ->>>>>> 发布类型:{} 内容ID:{} 节目单发布，组装object内容失败,错误描述:{}!", getContentType().getContentTypeName(), outOrderBaseVo.getBmsContentId(), exception);
            return false;
        }
        return true;
    }

    public SubOrderObjectsEntity getAttribute(String action, CmsSchedule cmsSchedule) {
        SubOrderObjectsEntity subOrderObjectsEntity = new SubOrderObjectsEntity();
        subOrderObjectsEntity.setElementType(ObjectsTypeConstants.SCHEDULE);
        subOrderObjectsEntity.setId(cmsSchedule.getCode());
        subOrderObjectsEntity.setCode(cmsSchedule.getCode());
        subOrderObjectsEntity.setAction(action);
        return subOrderObjectsEntity;
    }

    public Map<String, String> getProperty(CmsSchedule cmsSchedule, BmsSchedule bmsSchedule) {
        Map<String, String> propertyDic = new HashMap<>(8);
        propertyDic.put("ChannelCode", cmsSchedule.getChannelCode());
        propertyDic.put("ChannelID", SafeUtil.getString(cmsSchedule.getChannelCode()));
        propertyDic.put("ProgramName", cmsSchedule.getProgramName());
        propertyDic.put("StartDate", cmsSchedule.getStartDate());
        propertyDic.put("StartTime", cmsSchedule.getStartTime());
        propertyDic.put("Duration", cmsSchedule.getDuration());
        propertyDic.put("Status", SafeUtil.getString(bmsSchedule.getStatus()));
        return propertyDic;
    }


    private BmsSchedule getBmsSchedule(Long bmsScheduleId) {
        CommonResponse<BmsSchedule> bmsScheduleCommonResponse = bmsScheduleFeignClient.getById(bmsScheduleId);
        BmsSchedule bmsSchedule = null;
        if (bmsScheduleCommonResponse != null) {
            bmsSchedule = bmsScheduleCommonResponse.getData();
        }
        return bmsSchedule;
    }


    private CmsSchedule getCmsSchedule(BmsSchedule bmsSchedule) {
        CmsSchedule cmsSchedule = null;
        if (bmsSchedule != null) {
            CommonResponse<CmsSchedule> cmsScheduleCommonResponse = cmsScheduleFeignClient.getById(bmsSchedule.getCmsScheduleId());
            if (cmsScheduleCommonResponse != null) {
                cmsSchedule = cmsScheduleCommonResponse.getData();
            }
        }
        return cmsSchedule;

    }


}
