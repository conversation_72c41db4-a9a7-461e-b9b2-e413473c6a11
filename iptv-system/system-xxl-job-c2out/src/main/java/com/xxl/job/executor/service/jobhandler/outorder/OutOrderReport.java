package com.xxl.job.executor.service.jobhandler.outorder;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.pukka.iptv.common.base.enums.*;
import com.pukka.iptv.common.core.util.SpringUtils;
import com.pukka.iptv.common.data.dto.PublishParamsDto;
import com.pukka.iptv.common.data.model.OutOrderBase;
import com.pukka.iptv.common.data.model.OutOrderItem;
import com.pukka.iptv.common.data.vo.sys.OutOrderItemVo;
import com.pukka.iptv.common.rabbitmq.constans.FeedbackConstant;
import com.pukka.iptv.common.rabbitmq.constans.OutPublishConstant;
import com.rabbitmq.client.Channel;
import com.xxl.job.executor.c2out.dao.service.OutOrderBaseService;
import com.xxl.job.executor.c2out.dao.service.OutOrderItemService;
import com.xxl.job.executor.common.util.C2outUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.springframework.amqp.rabbit.annotation.Exchange;
import org.springframework.amqp.rabbit.annotation.Queue;
import org.springframework.amqp.rabbit.annotation.QueueBinding;
import org.springframework.amqp.rabbit.annotation.RabbitListener;
import org.springframework.amqp.rabbit.connection.CorrelationData;
import org.springframework.amqp.rabbit.core.RabbitTemplate;
import org.springframework.amqp.support.AmqpHeaders;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.messaging.handler.annotation.Header;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.UUID;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @description: 工单上报
 */
@Component
@Slf4j
public class OutOrderReport implements IOutOrder {

    private OutOrderBaseService outOrderBaseService = SpringUtils.getBean(
            OutOrderBaseService.class);
    private OutOrderItemService outOrderItemService = SpringUtils.getBean(
            OutOrderItemService.class);
    private RabbitTemplate rabbitTemplate = SpringUtils.getBean(RabbitTemplate.class);

    @Override
    public String getErrorDescription() {
        return null;
    }

    @Override
    public String getClassPropertyName() {
        return "工单上报执行器";
    }

    @Autowired
    private C2outUtils c2outUtils;

    @RabbitListener(bindings = {
            @QueueBinding(value = @Queue(value = OutPublishConstant.OUT_BASE_REPORT_QUEUE),
                    exchange = @Exchange(value = OutPublishConstant.OUT_EXCHANGE),
                    key = OutPublishConstant.OUT_BASE_REPORT_ROUTING)}, containerFactory = "rabbitListenerContainerFactory")
    private void recieved(OutOrderItemVo message, Channel channel,
            @Header(AmqpHeaders.DELIVERY_TAG) Long deliveryTag) throws Exception {
        log.info("RabbitMQ 队列:{}, 开始监听", OutPublishConstant.OUT_BASE_REPORT_QUEUE);
        try {
            String msgInfo = JSON.toJSONString(message);
            log.info("RabbitMQ 队列:{}, 获取信息:{}", OutPublishConstant.OUT_BASE_REPORT_QUEUE,
                    msgInfo);
            c2outUtils.retryOrderHandle(this, msgInfo);
        } catch (Exception exception) {
            log.info("RabbitMQ 队列:{},获取信息失败:{}", OutPublishConstant.OUT_BASE_REPORT_QUEUE,
                    exception);
        } finally {
            //执行是否成功，返回确认应答
            channel.basicAck(deliveryTag, false);
        }
    }

    @Override
    public boolean outHandle(String msg) {
        log.info("执行工单上报Report任务操作 ->>>>>> 开始执行 msg:{}",msg);
        //获取消息参数
        OutOrderItemVo orderItemVo = JSONObject.parseObject(msg, OutOrderItemVo.class);
        if (Objects.isNull(orderItemVo)) {
            log.error("执行工单上报Report任务操作 ->>>>>> 工单上报获取信息实体为空");
            return false;
        }
        Boolean reportResult = true;
        PublishParamsDto publishParamsDto = new PublishParamsDto();
        BeanUtils.copyProperties(orderItemVo, publishParamsDto);
        //查询主工单结果
        OutOrderBase outOrderBase = new OutOrderBase();
        OutOrderBase orderBase = outOrderBaseService.getById(orderItemVo.getBaseOrderId());
        BeanUtils.copyProperties(orderBase, outOrderBase);
        publishParamsDto.setResult(outOrderBase.getResult());
        publishParamsDto.setErrorDescription(outOrderBase.getErrorDescription());
        publishParamsDto.setContentId(outOrderBase.getBmsContentId());
        publishParamsDto.setBaseOrderId(outOrderBase.getId().toString());
        publishParamsDto.setOrderType(outOrderBase.getOrderType());
        publishParamsDto.setSpId(outOrderBase.getSpId());
        if (ObjectUtils.isNotEmpty(outOrderBase.getContentType())) {
            publishParamsDto.setContentType(outOrderBase.getContentType());
        }
        if (ObjectUtils.isEmpty(publishParamsDto.getAction()) && ObjectUtils.isNotEmpty(
                outOrderBase.getAction())) {
            publishParamsDto.setAction(outOrderBase.getAction());
        }

        //判断下发内容是否属于自动下发
        if (OrderTypeEnums.AUTO.getValue().equals(outOrderBase.getOrderType())) {
            //自动下发，根据OrderCorrelateId判断当前base任务所属cp是否完成
            List<OutOrderBase> baseOrderByCorrelateId = outOrderBaseService.getBaseOrderByCorrelateId(
                    outOrderBase.getOrderCorrelateId());
            Integer resultValue = null;
            //判断cp任务是否全部处理完毕
            if (baseOrderByCorrelateId != null
                    && baseOrderByCorrelateId.stream()
                    .allMatch(outOrderBase1 -> ObjectUtils.isNotEmpty(outOrderBase1.getResult()))
                    && baseOrderByCorrelateId.stream().noneMatch(
                    outOrderBase1 -> ItemResultEnum.InHandle.getValue()
                            .equals(outOrderBase1.getResult()))) {
                try {
                    //调用反馈接口,反馈状态
                    updateReportStatus(publishParamsDto, outOrderBase);
                    ArrayList<String> collect = baseOrderByCorrelateId.stream()
                            .map(OutOrderBase::getSpId)
                            .map(String::valueOf).collect(Collectors.toCollection(ArrayList::new));
                    if (ObjectUtils.isEmpty(collect) || collect.size() < 1) {
                        log.warn(
                                "执行工单上报Report任务操作 ->>>>>> 自动下发任务:{}，获取所属cp下spids失败，错误信息:{}",
                                msg);
                    } else {
                        String spIds = String.join(",", collect);
                        //判断cp任务是否全部成功
                        if (baseOrderByCorrelateId.stream().allMatch(
                                outOrderBase2 -> ItemResultEnum.Success.getValue()
                                        .equals(outOrderBase2.getResult()))) {
                            //设置cp任务成功
                            resultValue = ItemResultEnum.Success.getValue();
                        } //判断cp任务是否有失败
                        else if (baseOrderByCorrelateId.stream().anyMatch(outOrderBase3 ->
                                !ItemResultEnum.InHandle.getValue()
                                        .equals(outOrderBase3.getResult())
                                        && !ItemResultEnum.Success.getValue()
                                        .equals(outOrderBase3.getResult()))) {
                            //设置cp任务失败
                            resultValue = ItemResultEnum.Fail.getValue();
                        }
                        //******** 组建反馈实体
                        publishParamsDto.setCorrelateId(outOrderBase.getOrderCorrelateId());
                        publishParamsDto.setSpIds(spIds);
                        publishParamsDto.setIsFinish(true);
                        publishParamsDto.setResult(resultValue);
                        CorrelationData correlationData = new CorrelationData(
                                UUID.randomUUID().toString());
                        log.info("推送消息到自动发布反馈上报RabbitMQ队列：{}", publishParamsDto);
                        rabbitTemplate.convertAndSend(FeedbackConstant.FEEDBACK_EXCHANGE,
                                FeedbackConstant.FEEDBACK_AUTO_PUBLISH_ROUTING, publishParamsDto,
                                correlationData);
                        log.info(
                                "执行工单上报Report任务操作 ->>>>>> 自动下发任务CorrelateId:{} 自动下发任务:{}，调用分发上报反馈接口，结果:{}",
                                outOrderBase.getOrderCorrelateId(), msg, resultValue);
                    }
                } catch (Exception exception) {
                    log.error(
                            "执行工单上报Report任务操作 ->>>>>> 自动下发任务:{}，调用下发反馈失败，错误信息:{}",
                            msg, exception);
                }
            } else {
                log.info(
                        "执行工单上报Report任务操作 ->>>>>> 当前自动下发任务Id:{},OrderCorrelateId:{}.未执行完毕，等待后续执行结果!",
                        outOrderBase.getId(), outOrderBase.getOrderCorrelateId());
                //调用反馈接口,反馈状态
                updateReportStatus(publishParamsDto, outOrderBase);
            }
        } else {
            //查询当前子任务是否全部完成
            List<OutOrderItem> itemOrder = outOrderItemService.getItemOrderByBaseOrderId(
                    outOrderBase.getId());
            if (ObjectUtils.isEmpty(itemOrder)) {
                log.warn(
                        "执行工单上报Report任务操作 ->>>>>> 当前普通下发任务:{}.子工单任务为空，直接反馈主工单执行结果上报!",
                        outOrderBase);
                extracted(publishParamsDto, outOrderBase);
            } else {
                //判断当前任务是否全部完成
                if (itemOrder.stream()
                        .allMatch(outOrderItem -> ObjectUtils.isNotEmpty(outOrderItem.getResult()))
                        && itemOrder.stream().noneMatch(
                        outOrderItem -> ItemResultEnum.InHandle.getValue()
                                .equals(outOrderItem.getResult()))) {
                    // 更新首次上线时间
                    if (c2outUtils.checkMappingPublic(outOrderBase)) {
                        outOrderBaseService.updateFirstLaunchTime(orderBase);
                    }
                    extracted(publishParamsDto, outOrderBase);
                } else {
                    log.info(
                            "执行工单上报Report任务操作 ->>>>>> 当前普通下发任务:{}.子工单任务未执行完毕，等待后续执行结果!",
                            outOrderBase);
                    return true;
                }
            }
        }
        return reportResult;
    }

    private void extracted(PublishParamsDto publishParamsDto, OutOrderBase outOrderBase) {
        //调用下发反馈接口
        try {
            log.info("执行工单上报Report任务操作 ->>>>>> 调用下发反馈实体:{}",
                    JSON.toJSONString(publishParamsDto));
            //修改主工单状态为上报中
            outOrderBase.setReportStatus(ReportStatusEnum.InReport.getValue());
            if (ObjectUtils.isNotEmpty(outOrderBase.getId())) {
                outOrderBaseService.updateById(outOrderBase);
            }
            //******** 反馈信息修改为发送mq
            CorrelationData correlationData = new CorrelationData(UUID.randomUUID().toString());
            rabbitTemplate.convertAndSend(FeedbackConstant.FEEDBACK_EXCHANGE,
                    FeedbackConstant.FEEDBACK_GENERAL_PUBLISH_ROUTING, publishParamsDto,
                    correlationData);

        } catch (Exception exception) {
            outOrderBase.setReportStatus(ReportStatusEnum.FailReport.getValue());
            outOrderBaseService.updateById(outOrderBase);
            log.error("执行工单上报Report任务操作 ->>>>>> 调用下发反馈失败，错误信息:{}", exception);
        }
    }

    @Override
    public List<OutOrderItemVo> getReportEntity(String msg) {
        return null;
    }

    /**
     * 自动发布 单sp上报
     *
     * @param publishParamsDto
     * @param outOrderBase
     * @return
     */
    private void updateReportStatus(PublishParamsDto publishParamsDto, OutOrderBase outOrderBase) {
        PublishParamsDto paramsDto = new PublishParamsDto();
        BeanUtils.copyProperties(publishParamsDto, paramsDto);
        try {
            if (ObjectUtils.isNotEmpty(outOrderBase.getResult())) {
                //修改主工单状态为上报中
                outOrderBase.setReportStatus(ReportStatusEnum.InReport.getValue());
                if (ObjectUtils.isNotEmpty(outOrderBase.getId())) {
                    outOrderBaseService.updateById(outOrderBase);
                }
                //组建反馈实体
                paramsDto.setCorrelateId(outOrderBase.getOrderCorrelateId());
                paramsDto.setSpIds(String.valueOf(outOrderBase.getSpId()));
                paramsDto.setIsFinish(false);
                paramsDto.setResult(outOrderBase.getResult());
                CorrelationData correlationData = new CorrelationData(UUID.randomUUID().toString());
                rabbitTemplate.convertAndSend(FeedbackConstant.FEEDBACK_EXCHANGE,
                        FeedbackConstant.FEEDBACK_AUTO_PUBLISH_ROUTING, paramsDto, correlationData);
                log.info(
                        "执行工单上报Report任务操作 ->>>>>> 当前自动下发任务Id:{},publishParamsDto:{} 上报执行结果状态反馈队列!",
                        outOrderBase.getId(), JSON.toJSONString(paramsDto));
            }
        } catch (Exception exception) {
            outOrderBase.setReportStatus(ReportStatusEnum.FailReport.getValue());
            outOrderBaseService.updateById(outOrderBase);
            log.error(
                    "执行工单上报Report任务操作 ->>>>>> 自动下发任务Id:{},publishParamsDto:{}，上报执行结果状态反馈队列失败，错误信息:{}",
                    outOrderBase.getId(), JSON.toJSONString(paramsDto), exception);
        }
    }
}
