package com.xxl.job.executor.common.config;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Configuration;

/**
 * @author: chiron
 * Date: 2022/3/22 4:20 PM
 * Description:
 */
@Configuration
public class XmlGenerateRulesConfig {
    /**
     * 是否开启赋值BizDomain
     */
    @Value("${xml-generate-rules.bizdomain.enable:true}")
    public Boolean bizEnable;
    /**
     * 是否全局所有分发域赋值BizDomain
     */
    @Value("${xml-generate-rules.bizdomain.enable-global:false}")
    public Boolean bizEnableGlobal;
    /**
     * 操作对象类型
     */
    @Value("${xml-generate-rules.bizdomain.content-type:1,2,3,4}")
    public String bizDomainType;
    /**
     * 赋值BizDomain的名称 与实现类后缀对应
     */
    @Value("${xml-generate-rules.bizdomain.name:setBizDomain}")
    public String bizDomainName;
    /**
     * 赋值BizDomain的分发通道
     */
    @Value("${xml-generate-rules.bizdomain.outpassage-ids:2}")
    public String bizDomainIds;


    /**
     * 生成xml规则
     * 去除空字符标签是否开启
     */
    @Value("${xml-generate-rules.remove-empty-labels.enable:true}")
    public Boolean removeEmptyEnable;
    /**
     * 生成xml规则
     * 去除空字符标签
     * 全局分发通道配置
     */
    @Value("${xml-generate-rules.remove-empty-labels.enable-global:false}")
    public Boolean removeEmptyEnableGlobal;
    /**
     * 赋值去除空字符标签的名称 与实现类后缀对应
     */
    @Value("${xml-generate-rules.remove-empty-labels.name:removeEmptyLabels}")
    public String removeEmptyName;
    /**
     * 生成xml规则
     * 去除空字符标签
     */
    @Value("${xml-generate-rules.remove-empty-labels.outpassage-ids:1}")
    public String ruleRemoveEmptyLabelsIds;


    /**
     * 生成xml规则
     * 生成主工单xml文件
     */
    @Value("${xml-generate-rules.generate-base-order-xml.enable:true}")
    public Boolean generateBaseOrderEnable;
    /**
     * 创建主工单xml的名称 与实现类后缀对应
     */
    @Value("${xml-generate-rules.generate-base-order-xml.name:generateBaseOrderXml}")
    public String generateBaseOrderName;
}