package com.xxl.job.executor.c2out.strategy.handler.impl;

import com.pukka.iptv.common.data.vo.sys.OutOrderBaseVo;
import com.xxl.job.executor.c2out.dao.entity.ResultResponse;
import com.xxl.job.executor.common.enums.ContentPublishTypeEnum;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @Date 2021/11/18 2:36 下午
 * @description:
 * @Version 1.0
 */
@Slf4j
@Component
public class CategorySeriesHandler extends AbstractHandler {

    @Autowired
    private CategoryContentHandler categoryContentHandler;

    @Override
    public ContentPublishTypeEnum getContentType() {
        return ContentPublishTypeEnum.CATEGORY_SERIES;
    }

    /**
     * 发布/回收操作
     *
     * @param outOrderBaseVo
     * @return
     */
    @Override
    public ResultResponse publishOrRecover(OutOrderBaseVo outOrderBaseVo) {
        ResultResponse resultResponse = categoryContentHandler.publishOrRecover(outOrderBaseVo);
        return resultResponse;
    }
}
