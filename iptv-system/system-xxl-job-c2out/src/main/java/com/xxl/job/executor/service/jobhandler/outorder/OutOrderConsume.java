package com.xxl.job.executor.service.jobhandler.outorder;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.pukka.iptv.common.base.enums.ItemResultEnum;
import com.pukka.iptv.common.base.enums.ItemStatusEnum;
import com.pukka.iptv.common.core.util.SpringUtils;
import com.pukka.iptv.common.data.vo.sys.OutOrderItemVo;
import com.xxl.job.executor.c2out.dao.entity.ResultResponse;
import com.xxl.job.executor.c2out.dao.service.OutOrderBaseService;
import com.xxl.job.executor.c2out.dao.service.OutOrderItemService;
import com.xxl.job.executor.common.retry.RetryException;
import com.xxl.job.executor.common.util.C2outUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @description: 消费工单任务
 */
@Component
@Slf4j
public class OutOrderConsume implements IOutOrder {
    private C2outUtils c2outUtils = SpringUtils.getBean(C2outUtils.class);
    private OutOrderItemService outOrderItemService = SpringUtils.getBean(OutOrderItemService.class);
    private OutOrderBaseService outOrderBaseService = SpringUtils.getBean(OutOrderBaseService.class);
    private String errorDescription;

    @Override
    public String getClassPropertyName() {
        return "工单消费执行器";
    }

    @Override
    public String getErrorDescription() {
        return errorDescription;
    }


    @Override
    public boolean outHandle(String msg) {
        log.debug("执行消费工单任务操作 ->>>>>> 开始执行");
        OutOrderItemVo outOrderItemVo = JSONObject.parseObject(msg, OutOrderItemVo.class);
        OutOrderItemVo vo = new OutOrderItemVo();
        BeanUtils.copyProperties(outOrderItemVo, vo);
        try {
            //下发下游csp
            ResultResponse resultResponse = c2outUtils.startSendCsp(outOrderItemVo.getPath(), outOrderItemVo.getCspId(), outOrderItemVo.getLspId(), outOrderItemVo.getCorrelateId(), outOrderItemVo.getCmdFileUrl());
            if (resultResponse.getResult()) {
                //设置工单结果为处理中
                log.info("执行消费工单任务操作 ->>>>>> 工单消费成功,发送至下游csp:{} 工单CorrelateId:{} 工单信息:{}", outOrderItemVo.getPath(), outOrderItemVo.getCorrelateId(), JSON.toJSONString(outOrderItemVo));
                vo.setStatus(ItemStatusEnum.Success.getValue());
                vo.setResult(ItemResultEnum.InHandle.getValue());
                vo.setErrorDescription("");
                vo.setStatusDescription(ItemStatusEnum.Success.toString());
                outOrderItemService.updateResult(vo);
            } else {
                //消费失败，更新工单状态
                errorDescription = resultResponse.getDescription();
                return false;
            }
            return true;
        } catch (Exception e) {
            errorDescription = "执行消费工单任务操作 ->>>>>> 工单消费失败,工单CorrelateId:" + outOrderItemVo.getCorrelateId();
            log.error(errorDescription + ",错误信息:" + e);
            throw new RetryException(errorDescription);
        }
    }

    @Override
    public List<OutOrderItemVo> getReportEntity(String msg) {
        List<OutOrderItemVo> outOrderItemVoList = new ArrayList<>();
        OutOrderItemVo outOrderItemVo = JSONObject.parseObject(msg, OutOrderItemVo.class);
        outOrderItemVo.setStatus(ItemStatusEnum.Fail.getValue());
        outOrderItemVo.setResult(ItemResultEnum.Fail.getValue());
        outOrderItemVo.setStatusDescription(ItemStatusEnum.Fail.toString());
        outOrderItemVo.setErrorDescription(getErrorDescription());
        //计算发布时长
        outOrderItemVo.setFeedbackTime(outOrderItemVo.getCreateTime());
        outOrderItemVo.setDuration(-1);
        //更新主工单状态
        boolean b = outOrderBaseService.updateBaseOrderByItem(outOrderItemVo);
        if (!b) {
            log.error("执行消费工单任务操作 ->>>>>> 根据子工单更新主工单状态失败,消息实体:{}", msg);
        }
        //子工单异常需要更新工单状态
        outOrderItemService.updateResult(outOrderItemVo);
        outOrderItemVoList.add(outOrderItemVo);
        return outOrderItemVoList;
    }

}
