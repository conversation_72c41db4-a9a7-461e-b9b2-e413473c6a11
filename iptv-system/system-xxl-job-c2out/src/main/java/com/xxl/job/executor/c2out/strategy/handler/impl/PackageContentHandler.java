package com.xxl.job.executor.c2out.strategy.handler.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.pukka.iptv.common.api.feign.bms.BmsPackageContentFeignClient;
import com.pukka.iptv.common.api.feign.bms.BmsPackageFeignClient;
import com.pukka.iptv.common.base.constant.ObjectsTypeConstants;
import com.pukka.iptv.common.base.enums.ActionEnums;
import com.pukka.iptv.common.base.vo.CommonResponse;
import com.pukka.iptv.common.data.model.bms.BmsPackage;
import com.pukka.iptv.common.data.model.bms.BmsPackageContent;
import com.pukka.iptv.common.data.model.order.SubOrderMappingsEntity;
import com.pukka.iptv.common.data.model.order.SubOrderXmlEntity;
import com.pukka.iptv.common.data.vo.sys.OutOrderBaseVo;
import com.xxl.job.executor.c2out.dao.entity.ResultResponse;
import com.xxl.job.executor.c2out.utils.OutOperateUtil;
import com.xxl.job.executor.c2out.utils.SiteOperateUtil;
import com.xxl.job.executor.common.content.FtpHandleConstant;
import com.xxl.job.executor.common.enums.ContentPublishTypeEnum;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.ObjectUtils;

import java.net.MalformedURLException;
import java.util.*;
import java.util.concurrent.atomic.AtomicInteger;

/**
 * <AUTHOR>
 * @description: 产品包节目发布
 * @create 2021-09-13 9:56
 */
@Slf4j
@Component
public class PackageContentHandler extends AbstractHandler {

    @Autowired
    BmsPackageContentFeignClient bmsPackageContentFeignClient;

    @Autowired
    BmsPackageFeignClient bmsPackageFeignClient;

    @Override
    public ContentPublishTypeEnum getContentType() {
        return ContentPublishTypeEnum.PACKAGE_PROGRAM;
    }

    @Autowired
    private OutOperateUtil outOperateUtil;
    @Override
    public ResultResponse publishOrRecover(OutOrderBaseVo outOrderBaseVo) throws MalformedURLException {
        ResultResponse resultResponse = new ResultResponse();
        try {
            //获取关联节目关系,组装内容，构成xml文件
            if (getMappingData(outOrderBaseVo, resultResponse)) {
                StringBuffer xmlDestination = SiteOperateUtil.getFileName(FtpHandleConstant.DIR_PACKAGE_CONTENT);
                resultResponse.getData().setXmlDestination(xmlDestination.toString());
            }
        } catch (Exception exception) {
            resultResponse.setErrorResult(String.format("执行产品包节目发布操作 ->>>>>> 发布类型:%s 内容ID:%s 执行发布工单操作失败！", getContentType().getContentTypeName(), outOrderBaseVo.getBmsContentId()));
            log.warn("执行产品包节目发布操作 ->>>>>> 发布类型:{} 内容ID:{}，执行发布工单操作失败,错误描述:{}！", getContentType().getContentTypeName(), outOrderBaseVo.getBmsContentId(),exception);
        } finally {
            return resultResponse;
        }
    }

    /**
     * 获取节目信息关系
     *
     * @param outOrderBaseVo
     * @param resultResponse
     * @return
     */
    @Override
    public boolean getMappingData(OutOrderBaseVo outOrderBaseVo, ResultResponse resultResponse) {
        SubOrderXmlEntity subOrderXmlEntit = resultResponse.getData();
        CommonResponse<List<BmsPackageContent>> bmsPackageContentFeignClientByContentId = bmsPackageContentFeignClient.getByPackageContentId(outOrderBaseVo.getBmsContentId());

        if (bmsPackageContentFeignClientByContentId.getData() == null) {
            resultResponse.setErrorResult(String.format("执行产品包节目发布操作 ->>>>>> 发布类型:%s 内容ID:%s 获取产品包信息失败，产品包信息为空!", getContentType().getContentTypeName(), outOrderBaseVo.getBmsContentId()));
            return false;
        }
        List<BmsPackageContent> bmsPackageContentList = JSONArray.parseArray(JSON.toJSONString(bmsPackageContentFeignClientByContentId.getData())).toJavaList(BmsPackageContent.class);
        if (bmsPackageContentList == null || bmsPackageContentList.size() < 1) {
            resultResponse.setErrorResult(String.format("执行产品包节目发布操作 ->>>>>> 发布类型:%s 内容ID:%s 获取产品包信息失败，产品包信息为空!", getContentType().getContentTypeName(), outOrderBaseVo.getBmsContentId()));
            return false;
        }
        try {
            List<SubOrderMappingsEntity> subOrderMappingsEntityList = subOrderXmlEntit.getSubOrderMappingsEntities() != null
                    ? subOrderXmlEntit.getSubOrderMappingsEntities() : new ArrayList<>();
            //实体序号
            AtomicInteger serialNumber = new AtomicInteger();
            for (BmsPackageContent bmsPackageContent : bmsPackageContentList) {
                SubOrderMappingsEntity subOrderMappingsEntity = new SubOrderMappingsEntity();
                subOrderMappingsEntity.setId(UUID.randomUUID().toString());
                subOrderMappingsEntity.setElementType(ContentPublishTypeEnum.getContentTypeNameByContentType(outOrderBaseVo.getContentType()));
                //subOrderMappingsEntity.setElementId(SafeUtil.getString(bmsPackageContent.getBmsContentId()));
                subOrderMappingsEntity.setElementId(bmsPackageContent.getCmsContentCode());
                subOrderMappingsEntity.setElementCode(bmsPackageContent.getCmsContentCode());
                subOrderMappingsEntity.setAction(ActionEnums.getInfoByCode(outOrderBaseVo.getAction()));
                subOrderMappingsEntity.setParentType(ObjectsTypeConstants.PACKAGE);
                //subOrderMappingsEntity.setParentId(SafeUtil.getString(bmsPackageContent.getPackageId()));
                boolean extraCode = outOperateUtil.isExtraCode(String.valueOf(bmsPackageContent.getSpId()));
                if (extraCode) {
                    //更新parentcode
                    CommonResponse<BmsPackage> bmsPackageCommonResponse = bmsPackageFeignClient.getById(bmsPackageContent.getPackageId());
                    if (!ObjectUtils.isEmpty(bmsPackageCommonResponse.getData())) {
                        BmsPackage bmsPackage = bmsPackageCommonResponse.getData();
                        bmsPackageContent.setPackageCode(bmsPackage.getExtraCode());
                    }
                }
                subOrderMappingsEntity.setParentId(bmsPackageContent.getPackageCode());
                subOrderMappingsEntity.setParentCode(bmsPackageContent.getPackageCode());
                subOrderMappingsEntity.setSerialNumber(serialNumber.incrementAndGet());
                Map<String, String> propertyDic = new HashMap<>();
                subOrderMappingsEntity.setPropertyDic(propertyDic);
                subOrderMappingsEntityList.add(subOrderMappingsEntity);
            }
            subOrderXmlEntit.setSubOrderMappingsEntities(subOrderMappingsEntityList);
        } catch (Exception exception) {
            resultResponse.setErrorResult(String.format("执行产品包节目发布操作 ->>>>>> 发布类型:%s 内容ID:%s 组装mapping内容失败!", getContentType().getContentTypeName(), outOrderBaseVo.getBmsContentId()));
            log.error("执行产品包节目发布操作 ->>>>>> 发布类型:{} 内容ID:{} 产品包-节目关系发布，组装mapping内容失败,错误描述:{}!", getContentType().getContentTypeName(), outOrderBaseVo.getBmsContentId(),exception);
            return false;
        }
        return true;
    }
}
