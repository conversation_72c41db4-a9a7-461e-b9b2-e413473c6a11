package com.xxl.job.executor.c2out.initialization;

import com.pukka.iptv.common.base.constant.ObjectsTypeConstants;

/**
 * @author: chiron
 * Date: 2022/3/29 11:57 AM
 * Description:
 */
public interface IBuilderStrategyManager {
    /**
     * 根据spid获取生成xml工单实体类
     *
     * @param beanPrefix
     * @return
     */
    IBuilderLogic createBuilderStrategy(String beanPrefix);

    /**
     * 根据spid获取beanName
     *
     * @param beanPrefix
     * @return
     */
    default String getBuilderStrategyBeanName(String beanPrefix) {
        String beanName = ObjectsTypeConstants.PUBLISH_BEAN_PRE + ObjectsTypeConstants.BUILDERLOGIC_BEAN_PRE + beanPrefix;
        return beanName;
    }

    /**
     * 获取beanName
     *
     * @return
     */
    default String getBuilderStrategyBeanName() {
        String beanName = ObjectsTypeConstants.PUBLISH_BEAN_PRE + ObjectsTypeConstants.BUILDERLOGIC_BEAN_PRE;
        return beanName;
    }
}
