package com.xxl.job.executor.c2out.strategy.handler.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.pukka.iptv.common.api.feign.bms.BmsPictureFeignClient;
import com.pukka.iptv.common.api.feign.cms.CmsMovieFeignClient;
import com.pukka.iptv.common.base.constant.ObjectsTypeConstants;
import com.pukka.iptv.common.base.enums.ActionEnums;
import com.pukka.iptv.common.base.vo.CommonResponse;
import com.pukka.iptv.common.core.util.SafeUtil;
import com.pukka.iptv.common.data.model.OutParamExpand;
import com.pukka.iptv.common.data.model.bms.BmsPicture;
import com.pukka.iptv.common.data.model.cms.CmsMovie;
import com.pukka.iptv.common.data.model.order.MovieObjectEntity;
import com.pukka.iptv.common.data.model.order.PictureObjectEntity;
import com.pukka.iptv.common.data.model.order.SubOrderObjectsEntity;
import com.pukka.iptv.common.data.model.order.SubOrderXmlEntity;
import com.pukka.iptv.common.data.vo.bms.BmsPictureVO;
import com.pukka.iptv.common.data.vo.sys.OutOrderBaseVo;
import com.xxl.job.executor.c2out.dao.entity.ResultResponse;
import com.xxl.job.executor.c2out.strategy.handler.IPublishStrategyHandler;
import com.xxl.job.executor.c2out.utils.OutOperateUtil;
import com.xxl.job.executor.c2out.utils.SiteOperateUtil;
import com.xxl.job.executor.common.content.FtpHandleConstant;
import com.xxl.job.executor.common.enums.ContentPublishTypeEnum;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.springframework.beans.factory.annotation.Autowired;

import java.net.MalformedURLException;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.atomic.AtomicInteger;

/**
 * <AUTHOR>
 * @Date 2021/11/17 9:34 上午
 * @description: 发布类
 * @Version 1.0
 */
@Slf4j
public class AbstractHandler implements IPublishStrategyHandler {

    @Autowired
    private BmsPictureFeignClient bmsPictureFeignClient;
    @Autowired
    private CmsMovieFeignClient cmsMovieFeignClient;
    @Autowired
    private OutOperateUtil outOperateUtil;

    @Override
    public ContentPublishTypeEnum getContentType() {
        return null;
    }

    @Override
    public ResultResponse publishOrRecover(OutOrderBaseVo outOrderBaseVo) throws MalformedURLException {
        ResultResponse resultResponse = new ResultResponse();
        resultResponse.setErrorResult(String.format("执行发布执行器操作 ->>>>>> 发布类型:%s 内容ID:%s 获取媒资处理对象失败，创建执行器错误，发布结束!"
                , outOrderBaseVo.getContentType(), outOrderBaseVo.getBmsContentId()));
        return null;
    }

    @Override
    public boolean getObjectData(OutOrderBaseVo outOrderBaseVo, ResultResponse resultResponse) {
        return false;
    }

    @Override
    public boolean getMappingData(OutOrderBaseVo outOrderBaseVo, ResultResponse resultResponse) {
        return false;
    }

    /**
     * 获取图片对象
     *
     * @param outOrderBaseVo
     * @param resultResponse
     * @return
     */
    @Override
    public boolean getPictureData(OutOrderBaseVo outOrderBaseVo, ResultResponse resultResponse) {
        try {
            SubOrderXmlEntity subOrderXmlEntity = resultResponse.getData();
            List<SubOrderObjectsEntity> subOrderObjectsEntities = subOrderXmlEntity.getSubOrderObjectsEntities() != null
                    ? subOrderXmlEntity.getSubOrderObjectsEntities() : new ArrayList<SubOrderObjectsEntity>();
            subOrderXmlEntity.setSubOrderObjectsEntities(subOrderObjectsEntities);
            //获取图片信息
            CommonResponse<List<BmsPicture>> bmsPictureListResponse = bmsPictureFeignClient.getByContentIdContentType(outOrderBaseVo.getBmsContentId(), getContentType().getContentType(), outOrderBaseVo.getSpId());

            if (bmsPictureListResponse.getData() == null) {
                log.warn("执行获取图片对象操作 ->>>>>> 发布类型:{} 内容ID:{}.当前对象,查询bmsPicture表无关联图片信息!", getContentType().getContentTypeName(), outOrderBaseVo.getBmsContentId());
                return true;
            }
            List<BmsPicture> pictureList = JSONArray.parseArray(JSON.toJSONString(bmsPictureListResponse.getData())).toJavaList(BmsPicture.class);
            if (pictureList == null || pictureList.size() < 1) {
                log.warn("执行获取图片对象操作 ->>>>>> 发布类型:{} 内容ID:{}.当前对象,查询bmsPicture表返回data为空!", getContentType().getContentTypeName(), outOrderBaseVo.getBmsContentId());
                return true;
            }
            if (ObjectUtils.isEmpty(outOrderBaseVo.getParameters())) {
                log.warn("执行获取图片对象操作 ->>>>>> 发布类型:{} 内容ID:{}.当前对象,分发接口未传输对应图片信息!", getContentType().getContentTypeName(), outOrderBaseVo.getBmsContentId());
                return true;
            }
            OutParamExpand outParamExpand = JSON.parseObject(outOrderBaseVo.getParameters(), OutParamExpand.class);
            Map<String, String> outParamExpandSpareMap = outParamExpand.getSpareMap();
            List<BmsPictureVO> pictureCollect = SiteOperateUtil.getBmsPictureCollect(pictureList,outParamExpandSpareMap);
            if (pictureCollect == null || pictureCollect.size() < 1) {
                log.warn("执行获取图片对象操作 ->>>>>> 发布类型:{} 内容ID:{}.当前对象,对比图片信息结果为空!", getContentType().getContentTypeName(), outOrderBaseVo.getBmsContentId());
                return true;
            }
            //实体序号
            AtomicInteger serialNumber = new AtomicInteger();
            //获取图片特性(Attribute),属性(Property)
            pictureCollect.forEach(bmsPictureVO -> {
                PictureObjectEntity pictureObjectEntity = new PictureObjectEntity();
                pictureObjectEntity.setElementType(ObjectsTypeConstants.PICTURE);
                pictureObjectEntity.setId(bmsPictureVO.getCmsPictureCode());
                pictureObjectEntity.setAction(ActionEnums.getInfoByCode(bmsPictureVO.getAction()));
                pictureObjectEntity.setCode(bmsPictureVO.getCmsPictureCode());
                Map<String, String> propertyDic = new HashMap<>();
                String mediaPrefix = outOperateUtil.getMediaPrefix(SafeUtil.getString(bmsPictureVO.getStorageId()), bmsPictureVO.getFileUrl(), FtpHandleConstant.OUT_PICTURE_FTP_PREFIX);
                propertyDic.put("FileURL", mediaPrefix);
                propertyDic.put("Description", bmsPictureVO.getDescription());
                pictureObjectEntity.setPropertyDic(propertyDic);
                pictureObjectEntity.setSerialNumber(serialNumber.incrementAndGet());
                pictureObjectEntity.setType(SafeUtil.getString(bmsPictureVO.getType()));
                pictureObjectEntity.setSequence(SafeUtil.getString(bmsPictureVO.getSequence()));
                subOrderObjectsEntities.add(pictureObjectEntity);
            });
        } catch (Exception exception) {
            resultResponse.setErrorResult(String.format("执行获取图片对象操作 ->>>>>> 发布类型:%s 内容ID:%s 获取图片对象失败!", getContentType().getContentTypeName(), outOrderBaseVo.getBmsContentId()));
            log.error("执行获取图片对象操作 ->>>>>> 发布类型:{} 内容ID:{}，获取图片对象失败! 错误信息:{}", getContentType().getContentTypeName(), outOrderBaseVo.getBmsContentId(), exception);
            return false;
        }
        return true;
    }

    /**
     * 获取视频介质对象
     *
     * @param outOrderBaseVo
     * @param resultResponse
     * @param cmsProgramId
     * @return
     */
    @Override
    public boolean getMovieData(OutOrderBaseVo outOrderBaseVo, ResultResponse resultResponse, Long cmsProgramId) {
        try {
            SubOrderXmlEntity subOrderXmlEntity = resultResponse.getData();
            List<SubOrderObjectsEntity> subOrderObjectsEntities = subOrderXmlEntity.getSubOrderObjectsEntities() == null
                    ? new ArrayList<>() : subOrderXmlEntity.getSubOrderObjectsEntities();
            subOrderXmlEntity.setSubOrderObjectsEntities(subOrderObjectsEntities);
            //获取视频信息
            CommonResponse<List<CmsMovie>> cmsMovieListResponse = cmsMovieFeignClient.getByContentIdContentType(cmsProgramId, getContentType().getContentType(), outOrderBaseVo.getCpId());
            if (cmsMovieListResponse.getData() == null) {
                log.warn("执行获取视频介质对象操作 ->>>>>> 当前发布类型:{} 内容ID:{} cpId:{} 发布，获取视频信息为空！", getContentType().getContentTypeName(), outOrderBaseVo.getBmsContentId(),outOrderBaseVo.getCpId());
                return true;
            }
            List<CmsMovie> movieList = JSONArray.parseArray(JSON.toJSONString(cmsMovieListResponse.getData())).toJavaList(CmsMovie.class);
            if (movieList == null || movieList.size() == 0) {
                log.warn("执行获取视频介质对象操作 ->>>>>> 当前发布类型:{} 内容ID:{} 发布，视频介质对象为空！", getContentType().getContentTypeName(), outOrderBaseVo.getBmsContentId());
                return true;
            }
            //实体序号
            AtomicInteger serialNumber = new AtomicInteger();
            for (CmsMovie cmsMovie : movieList) {
                // 视频介质att属性
                MovieObjectEntity movieObjectEntity = new MovieObjectEntity();
                movieObjectEntity.setElementType(ObjectsTypeConstants.MOVIE);
                movieObjectEntity.setId(cmsMovie.getCode());
                movieObjectEntity.setAction(ActionEnums.getInfoByCode(outOrderBaseVo.getAction()));
                movieObjectEntity.setCode(cmsMovie.getCode());
                // 视频介质pn属性
                Map<String, String> propertyDic = new HashMap<>();
                propertyDic.put("Type", SafeUtil.getString(cmsMovie.getType()));
                String mediaPrefix = outOperateUtil.getMediaPrefix(SafeUtil.getString(cmsMovie.getStorageId()), cmsMovie.getFileUrl(), FtpHandleConstant.OUT_MOVIE_FTP_PREFIX);
                propertyDic.put("FileURL", mediaPrefix);
                propertyDic.put("SourceDRMType", SafeUtil.getString(cmsMovie.getSourceDrmType()));
                propertyDic.put("DestDRMType", SafeUtil.getString(cmsMovie.getDestDrmType()));
                propertyDic.put("AudioType", SafeUtil.getString(cmsMovie.getAudioType()));
                propertyDic.put("ScreenFormat", SafeUtil.getString(cmsMovie.getScreenFormat()));
                propertyDic.put("ClosedCaptioning", SafeUtil.getString(cmsMovie.getClosedCaptioning()));
                propertyDic.put("MediaSpec", cmsMovie.getMediaSpec());
                propertyDic.put("BitRateType", SafeUtil.getString(cmsMovie.getBitRateType()));
                propertyDic.put("MovieHeadDuration", SafeUtil.getString(cmsMovie.getMovieHeadDuration()));
                propertyDic.put("MovieTailDuration", SafeUtil.getString(cmsMovie.getMovieTailDuration()));
                movieObjectEntity.setPropertyDic(propertyDic);
                movieObjectEntity.setSerialNumber(serialNumber.incrementAndGet());
                subOrderObjectsEntities.add(movieObjectEntity);
            }

        } catch (Exception exception) {
            resultResponse.setErrorResult(String.format("执行获取视频介质对象操作 ->>>>>> 发布类型:%s 内容ID:%s 获取视频对象失败!", getContentType().getContentTypeName(), outOrderBaseVo.getBmsContentId()));
            log.error("执行获取视频介质对象操作 ->>>>>> 发布类型:{}, 内容ID:{}，获取视频对象失败!错误信息:{}", getContentType().getContentTypeName(), outOrderBaseVo.getBmsContentId(), exception);
            return false;
        }
        return true;
    }
}
