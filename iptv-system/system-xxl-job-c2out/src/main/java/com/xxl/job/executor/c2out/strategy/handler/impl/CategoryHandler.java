package com.xxl.job.executor.c2out.strategy.handler.impl;

import com.pukka.iptv.common.api.feign.bms.BmsCategoryFeignClient;
import com.pukka.iptv.common.base.constant.ObjectsTypeConstants;
import com.pukka.iptv.common.base.enums.ActionEnums;
import com.pukka.iptv.common.base.enums.CheckUniqueEnum;
import com.pukka.iptv.common.base.vo.CommonResponse;
import com.pukka.iptv.common.core.util.SafeUtil;
import com.pukka.iptv.common.data.model.bms.BmsCategory;
import com.pukka.iptv.common.data.model.order.CategoryObjectEntity;
import com.pukka.iptv.common.data.model.order.PictureObjectEntity;
import com.pukka.iptv.common.data.model.order.SubOrderMappingsEntity;
import com.pukka.iptv.common.data.model.order.SubOrderObjectsEntity;
import com.pukka.iptv.common.data.model.order.SubOrderXmlEntity;
import com.pukka.iptv.common.data.vo.sys.OutOrderBaseVo;
import com.xxl.job.executor.c2out.dao.entity.ResultResponse;
import com.xxl.job.executor.c2out.utils.OutOperateUtil;
import com.xxl.job.executor.c2out.utils.SiteOperateUtil;
import com.xxl.job.executor.c2out.utils.unique.CheckUniqueResult;
import com.xxl.job.executor.c2out.utils.unique.CheckUniqueUtil;
import com.xxl.job.executor.common.content.FtpHandleConstant;
import com.xxl.job.executor.common.enums.ContentPublishTypeEnum;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.UUID;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.Collectors;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.ObjectUtils;

/**
 * <AUTHOR>
 * @description: 栏目发布
 * @create 2021-08-30 16:15
 */
@Slf4j
@Component
public class CategoryHandler extends AbstractHandler {

    //实体序号
    private int serialNumber = 0;

    @Autowired
    private BmsCategoryFeignClient bmsCategoryFeignClient;

    @Autowired
    private OutOperateUtil outOperateUtil;

    @Autowired
    private CheckUniqueUtil checkUniqueUtil;

    /**
     * 获取类型 contentType
     *
     * @return
     */
    @Override
    public ContentPublishTypeEnum getContentType() {
        return ContentPublishTypeEnum.CATEGORY;
    }

    /**
     * 发布/回收操作
     */
    @Override
    public ResultResponse publishOrRecover(OutOrderBaseVo outOrderBaseVo) {
        ResultResponse resultResponse = new ResultResponse();
        try {
            //获取object文件内容
            boolean flag = getObjectData(outOrderBaseVo, resultResponse);
            //查询附加条件（发布内容带图片下发，发布内容带视频下发）
            if (flag) {
                flag = getPictureData(outOrderBaseVo, resultResponse);
            }
            //获取Mapping文件关系
            if (flag) {
                //组装内容，构成xml文件
                if (getMappingData(outOrderBaseVo, resultResponse)) {
                    StringBuffer xmlDestination = SiteOperateUtil.getFileName(
                            FtpHandleConstant.DIR_CATEGORY);
                    resultResponse.getData().setXmlDestination(xmlDestination.toString());
                }
            }
        } catch (Exception exception) {
            resultResponse.setErrorResult("构造xml文件失败");
            log.warn("执行栏目发布操作 ->>>>>> 发布类型:{} 内容ID:{}，构造xml文件失败,错误描述",
                    getContentType().getContentTypeName(), outOrderBaseVo.getBmsContentId(),
                    exception);
        } finally {
            return resultResponse;
        }
    }

    /**
     * 组装object内容
     *
     * @param outOrderBaseVo
     * @param resultResponse
     * @return
     */
    @Override
    public boolean getObjectData(OutOrderBaseVo outOrderBaseVo, ResultResponse resultResponse) {
        try {
            SubOrderXmlEntity subOrderXmlEntit = resultResponse.getData();
            List<SubOrderObjectsEntity> subOrderObjectsEntities =
                    subOrderXmlEntit.getSubOrderObjectsEntities() != null
                            ? subOrderXmlEntit.getSubOrderObjectsEntities() : new ArrayList<>();
            //调用openfeign查询栏目信息
            BmsCategory bmsCategory = getBmsCategoryByBmsId(outOrderBaseVo.getBmsContentId());

            if (bmsCategory == null) {
                resultResponse.setErrorResult("获取栏目信息结果为空!");
                return false;
            }
            //赋值extracode
            outOrderBaseVo.setExtraCode(bmsCategory.getExtraCode());
            //更新parentcode
            boolean extraCode = outOperateUtil.isExtraCode(String.valueOf(bmsCategory.getSpId()));
            if (extraCode) {
                if (ObjectUtils.isEmpty(bmsCategory.getParentId())) {
                    resultResponse.setErrorResult("父栏目信息为空!");
                    log.error(
                            "执行栏目发布操作 ->>>>>> 发布类型:{} 内容ID:{} 父栏目信息为空,组装object内容失败!",
                            getContentType().getContentTypeName(), outOrderBaseVo.getBmsContentId());
                    return false;
                }
                //校验是否为父栏目
                if (0L == bmsCategory.getParentId()) {
                    bmsCategory.setParentCode(SafeUtil.getString(bmsCategory.getParentId()));
                } else {
                    //更新parentcode
                    CommonResponse<BmsCategory> extracodeById = bmsCategoryFeignClient.getExtracodeById(
                            bmsCategory.getParentId());
                    if (!ObjectUtils.isEmpty(extracodeById.getData())) {
                        BmsCategory bmsCategory1 = extracodeById.getData();
                        bmsCategory.setParentCode(bmsCategory1.getExtraCode());
                    }else{
                        resultResponse.setErrorResult("父栏目信息查询结果为空!");
                        log.error(
                                "执行栏目发布操作 ->>>>>> 发布类型:{} 内容ID:{} 父栏目信息查询结果为空,组装object内容失败!",
                                getContentType().getContentTypeName(), outOrderBaseVo.getBmsContentId());
                        return false;
                    }
                }
            }
            //获取栏目特性(Attribute)
            CategoryObjectEntity categoryObjectEntity = getAttribute(
                    ActionEnums.getInfoByCode(outOrderBaseVo.getAction()), bmsCategory);

            //获取栏目属性(Property)
            Map<String, String> propertyDic = getProperty(bmsCategory);

            categoryObjectEntity.setPropertyDic(propertyDic);
            categoryObjectEntity.setSerialNumber(serialNumber);
            subOrderObjectsEntities.add(categoryObjectEntity);
            subOrderXmlEntit.setSubOrderObjectsEntities(subOrderObjectsEntities);
            //校验code唯一性
            CheckUniqueResult checkUniqueResult = checkUniqueUtil.checkUniqueOptimized(
                    categoryObjectEntity, outOrderBaseVo.getSpId(), extraCode);
            if (CheckUniqueEnum.NOTUNIQUE.equals(checkUniqueResult.getCheckUniqueEnum())) {
                resultResponse.setErrorResult("栏目code唯一性校验不通过!");
                log.error(
                        "执行栏目发布操作 ->>>>>> 发布类型:{} 内容ID:{} 栏目code唯一性校验不通过,组装object内容失败!",
                        getContentType().getContentTypeName(), outOrderBaseVo.getBmsContentId());
                return false;
            }
        } catch (Exception exception) {
            resultResponse.setErrorResult("组装object内容失败");
            log.error(
                    "执行栏目发布操作 ->>>>>> 发布类型:{} 内容ID:{} 栏目发布，组装object内容失败,错误描述",
                    getContentType().getContentTypeName(), outOrderBaseVo.getBmsContentId(),
                    exception);
            return false;
        }
        return true;
    }

    public Map<String, String> getProperty(BmsCategory bmsCategory) {
        Map<String, String> propertyDic = new HashMap<>();
        propertyDic.put("ParentID", SafeUtil.getString(bmsCategory.getParentCode()));
        propertyDic.put("Name", SafeUtil.getString(bmsCategory.getName()));
        propertyDic.put("Sequence", SafeUtil.getString(bmsCategory.getSequence()));
        propertyDic.put("Status", SafeUtil.getString(bmsCategory.getStatus()));
        propertyDic.put("Description", SafeUtil.getString(bmsCategory.getDescription()));
        return propertyDic;
    }

    public CategoryObjectEntity getAttribute(String action, BmsCategory bmsCategory) {
        CategoryObjectEntity categoryObjectEntity = new CategoryObjectEntity();
        categoryObjectEntity.setElementType(ObjectsTypeConstants.CATEGORY);
        boolean extraCode = outOperateUtil.isExtraCode(String.valueOf(bmsCategory.getSpId()));
        if (extraCode) {
            categoryObjectEntity.setCode(bmsCategory.getExtraCode());
        } else {
            categoryObjectEntity.setCode(bmsCategory.getCode());
        }
        categoryObjectEntity.setId(categoryObjectEntity.getCode());
        categoryObjectEntity.setAction(action);
        //父节点
        categoryObjectEntity.setParentCode(bmsCategory.getParentCode());
        return categoryObjectEntity;
    }

    public BmsCategory getBmsCategoryByBmsId(Long bmsContentId) {
        CommonResponse<BmsCategory> categoryFeignClientById = bmsCategoryFeignClient.getById(
                bmsContentId);
        return categoryFeignClientById.getData();
    }

    /**
     * 组装mapping内容
     *
     * @param outOrderBaseVo
     * @param resultResponse
     * @return
     */
    @Override
    public boolean getMappingData(OutOrderBaseVo outOrderBaseVo, ResultResponse resultResponse) {
        try {
            SubOrderXmlEntity subOrderXmlEntit = resultResponse.getData();
            List<SubOrderMappingsEntity> subOrderMappingsEntityList =
                    subOrderXmlEntit.getSubOrderMappingsEntities() != null
                            ? subOrderXmlEntit.getSubOrderMappingsEntities() : new ArrayList<>();
            //图片
            List<SubOrderObjectsEntity> subOrderObjectsEntities = subOrderXmlEntit.getSubOrderObjectsEntities();
            //需考虑栏目是否可能不带图片情况
            List<SubOrderObjectsEntity> collect = subOrderObjectsEntities.stream().filter(
                            p -> ObjectsTypeConstants.PICTURE.equals(p.getElementType()))
                    .collect(Collectors.toList());
            //当不带图片时，直接返回结果
            if (collect == null || collect.size() == 0) {
                return true;
            }
            String elementCode = outOrderBaseVo.getBmsContentCode();
            //判断code是否取extraCode
            boolean extraCode = outOperateUtil.isExtraCode(
                    String.valueOf(outOrderBaseVo.getSpId()));
            if (extraCode) {
                elementCode = outOrderBaseVo.getExtraCode();
            }
            //mapping实体序号
            AtomicInteger sequence = new AtomicInteger();
            for (SubOrderObjectsEntity subOrderObjectsEntity : collect) {
                SubOrderMappingsEntity subOrderMappingsEntity = new SubOrderMappingsEntity();
                subOrderMappingsEntity.setId(UUID.randomUUID().toString());
                subOrderMappingsEntity.setElementType(ObjectsTypeConstants.CATEGORY);
                subOrderMappingsEntity.setElementId(elementCode);
                subOrderMappingsEntity.setElementCode(elementCode);
                subOrderMappingsEntity.setSerialNumber(sequence.incrementAndGet());
                PictureObjectEntity pictureObjectEntity = (PictureObjectEntity) subOrderObjectsEntity;
                subOrderMappingsEntity.setParentType(ObjectsTypeConstants.PICTURE);
                subOrderMappingsEntity.setAction(pictureObjectEntity.getAction());
                subOrderMappingsEntity.setParentId(pictureObjectEntity.getCode());
                subOrderMappingsEntity.setParentCode(pictureObjectEntity.getCode());
                Map<String, String> propertyDic = new HashMap<>();
                propertyDic.put("Type", pictureObjectEntity.getType());
                propertyDic.put("Sequence", SafeUtil.getString(pictureObjectEntity.getSequence()));
                subOrderMappingsEntity.setPropertyDic(propertyDic);
                subOrderMappingsEntityList.add(subOrderMappingsEntity);
            }
            subOrderXmlEntit.setSubOrderMappingsEntities(subOrderMappingsEntityList);
        } catch (Exception exception) {
            resultResponse.setErrorResult("组装mapping内容失败!");
            log.error(
                    "执行栏目发布操作 ->>>>>> 发布类型:{} 内容ID:{} 栏目发布，组装mapping内容失败,错误描述",
                    getContentType().getContentTypeName(), outOrderBaseVo.getBmsContentId(),
                    exception);
            return false;
        }
        return true;
    }

}
