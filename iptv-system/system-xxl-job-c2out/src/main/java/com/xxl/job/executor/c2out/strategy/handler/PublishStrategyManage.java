package com.xxl.job.executor.c2out.strategy.handler;

import com.pukka.iptv.common.data.model.order.SubOrderXmlEntity;
import com.pukka.iptv.common.data.vo.sys.OutOrderBaseVo;
import com.xxl.job.executor.c2out.dao.entity.ResultResponse;
import com.xxl.job.executor.c2out.strategy.factory.PublishStrategyHandlerFactory;
import com.xxl.job.executor.c2out.utils.OutOperateUtil;
import com.xxl.job.executor.common.enums.ContentPublishTypeEnum;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.io.Serializable;

/**
 * @author: chiron
 * Date: 2022/3/24 9:32 AM
 * Description:
 */
@Slf4j
@Service
public class PublishStrategyManage implements IPublishStrategyManage {

    @Autowired
    private PublishStrategyHandlerFactory publishStrategyHandlerFactory;
    @Autowired
    private OutOperateUtil outOperateUtil;

    /**
     * 执行发布操作
     *
     * @param outOrderBaseVo
     * @return
     */
    @Override
    public ResultResponse startPublish(OutOrderBaseVo outOrderBaseVo) {
        log.info("执行发布操作 ->>>>>> 开始执行，发布类型:{} 内容ID:{}",
                ContentPublishTypeEnum.getContentTypeNameByContentType(Integer.valueOf(
                        outOrderBaseVo.getContentType())), outOrderBaseVo.getBmsContentId());
        ResultResponse resultResponse = new ResultResponse();
        try {
            //获取工单媒资类型
            Integer contentType = outOrderBaseVo.getContentType();
            ContentPublishTypeEnum anEnum = ContentPublishTypeEnum.getEnum(contentType);
            IPublishStrategyHandler<Serializable> handler = publishStrategyHandlerFactory.getHandler(
                    anEnum);

            resultResponse = handler.publishOrRecover(outOrderBaseVo);
            //需判断是否返回成功
            if (!resultResponse.getResult()) {
                return resultResponse;
            }
            SubOrderXmlEntity subOrderXmlEntity = resultResponse.getData();
            if (ObjectUtils.isNotEmpty(subOrderXmlEntity)) {
                subOrderXmlEntity.setCpId(outOrderBaseVo.getCpId());
                subOrderXmlEntity.setContentType(outOrderBaseVo.getContentType());
            } else {
                log.warn(
                        "执行发布操作 ->>>>>> 发布类型:{} 内容ID:{}，组装工单实体为空，不填充主工单CpId属性！",
                        ContentPublishTypeEnum.getContentTypeNameByContentType(Integer.valueOf(
                                outOrderBaseVo.getContentType())),
                        outOrderBaseVo.getBmsContentId());
            }
            return resultResponse;
        } catch (Exception exception) {
            resultResponse.setErrorResult(
                    String.format("执行发布操作失败，错误原因:%s", exception.getMessage()));
            log.warn("执行发布操作 ->>>>>> 发布类型:{} 内容ID:{}，执行发布操作失败.错误:{}",
                    ContentPublishTypeEnum.getContentTypeNameByContentType(Integer.valueOf(
                            outOrderBaseVo.getContentType())), outOrderBaseVo.getBmsContentId(),
                    exception.getMessage());
            return resultResponse;
        }
    }
}