package com.xxl.job.executor.common.enums;


import com.pukka.iptv.common.base.constant.ObjectsTypeConstants;

/**
 * <AUTHOR>
 * @description: 媒资类型-发布类型 映射
 * @create 2021-08-30 15:02
 */
public enum ContentPublishTypeEnum {

    // 单集
    PROGRAM(1, 1, ObjectsTypeConstants.PROGRAM),
    // 子集
    SUBSET(2, 1, ObjectsTypeConstants.PROGRAM),
    // 片花
    FLOWER(5, 1, ObjectsTypeConstants.PROGRAM),
    // 连续剧
    SERIES3(3, 3, ObjectsTypeConstants.SERIES),
    // 系列片
    SERIES4(4, 3, ObjectsTypeConstants.SERIES),
    // 频道
    CHANNEL(6, 6, ObjectsTypeConstants.CHANNEL),
    // 物理频道
    PHYSICAL_CHANNEL(7, 7, ObjectsTypeConstants.PHYSICAL_CHANNEL),
    // 栏目
    CATEGORY(8, 8, ObjectsTypeConstants.CATEGORY),
    // 产品包
    PACKAGE(9, 9, ObjectsTypeConstants.PACKAGE),
    // 节目单
    SCHEDULE(10, 10, ObjectsTypeConstants.SCHEDULE),
    // 图片
    PICTURE(11, 11, ObjectsTypeConstants.PICTURE),
    // 视频介质
    MOVIE(12, 12, ObjectsTypeConstants.MOVIE),
    // 节目图片
    PICTURE_PROGRAM(13, 13, ""),
    // 剧集图片
    PICTURE_SERIES(14, 14, ""),
    // 产品包图片
    PICTURE_PACKAGE(15, 15, ""),
    // 栏目图片
    PICTURE_CATEGORY(16, 16, ""),
    // 频道图片
    PICTURE_CHANNEL(17, 17, ""),
    // 栏目节目
    CATEGORY_PROGRAM(18, 18, ObjectsTypeConstants.PROGRAM),
    // 栏目剧集
    CATEGORY_SERIES(19, 18, ObjectsTypeConstants.SERIES),
    // 栏目频道
    CATEGORY_CHANNEL(20, 20, ObjectsTypeConstants.CHANNEL),
    // 产品包节目
    PACKAGE_PROGRAM(21, 21, ObjectsTypeConstants.PROGRAM),
    // 产品包剧集
    PACKAGE_SERIES(22, 22, ObjectsTypeConstants.SERIES),
    // 频道+物理频道
    PHYSICAL_AND_CHANNEL(24, 24, ObjectsTypeConstants.CHANNEL_AND_PHYSICAL),
    //剧集+子集
    SERIES_AND_SUBSET(25, 25, ObjectsTypeConstants.SERIES_AND_SUBSET),
    //关系
    RELATIONSHIP(30,30,"");


    /**
     * contentType
     */
    private Integer contentType;

    /**
     * publishType
     */
    private Integer publishType;
    /**
     * contentTypeName
     */
    private String contentTypeName;

    ContentPublishTypeEnum(int contentType, int publishType, String contentTypeName) {
        this.contentType = contentType;
        this.publishType = publishType;
        this.contentTypeName = contentTypeName;
    }

    public Integer getContentType() {
        return contentType;
    }

    public Integer getPublishType() {
        return publishType;
    }

    public String getContentTypeName() {
        return contentTypeName;
    }

    public void setContentTypeName(String contentTypeName) {
        this.contentTypeName = contentTypeName;
    }

    public void setContentType(Integer contentType) {
        this.contentType = contentType;
    }

    public void setPublishType(Integer publishType) {
        this.publishType = publishType;
    }

    public static Integer getPublishTypeByContentType(Integer contentType) {
        ContentPublishTypeEnum[] contentPublishTypeEnums = values();
        for (ContentPublishTypeEnum element : contentPublishTypeEnums) {
            if (contentType.intValue() == element.getContentType().intValue()) {
                return element.getPublishType();
            }
        }
        return null;
    }

    public static Integer getContentTypeByPublishType(Integer publishType) {
        ContentPublishTypeEnum[] contentPublishTypeEnums = values();
        for (ContentPublishTypeEnum element : contentPublishTypeEnums) {
            if (publishType.intValue() == element.getContentType().intValue()) {
                return element.getContentType();
            }
        }
        return null;
    }

    public static String getContentTypeNameByContentType(Integer contentType) {
        ContentPublishTypeEnum[] contentPublishTypeEnums = values();
        for (ContentPublishTypeEnum element : contentPublishTypeEnums) {
            if (contentType.intValue() == element.getContentType().intValue()) {
                return element.getContentTypeName();
            }
        }
        return null;
    }


    public static ContentPublishTypeEnum getEnum(int contentType) {
        for (ContentPublishTypeEnum typeEnum : values()) {
            if (typeEnum.getContentType() == contentType) {
                return typeEnum;
            }
        }
        return null;

    }

}
