package com.xxl.job.executor.c2out.strategy.handler.impl;

import com.pukka.iptv.common.api.feign.bms.BmsPackageFeignClient;
import com.pukka.iptv.common.base.constant.ObjectsTypeConstants;
import com.pukka.iptv.common.base.enums.ActionEnums;
import com.pukka.iptv.common.base.vo.CommonResponse;
import com.pukka.iptv.common.core.util.SafeUtil;
import com.pukka.iptv.common.data.model.bms.BmsPackage;
import com.pukka.iptv.common.data.model.order.*;
import com.pukka.iptv.common.data.vo.sys.OutOrderBaseVo;
import com.xxl.job.executor.c2out.dao.entity.ResultResponse;
import com.xxl.job.executor.c2out.utils.OutOperateUtil;
import com.xxl.job.executor.c2out.utils.SiteOperateUtil;
import com.xxl.job.executor.common.content.FtpHandleConstant;
import com.xxl.job.executor.common.enums.ContentPublishTypeEnum;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.net.MalformedURLException;
import java.util.*;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @description: 产品包发布
 * @create 2021-09-13 9:56
 */
@Slf4j
@Component
public class PackageHandler extends AbstractHandler {
    /**
     * 实体序号
     */
    private int serialNumber = 0;

    @Autowired
    private BmsPackageFeignClient bmsPackageFeignClient;
    @Autowired
    private OutOperateUtil outOperateUtil;
    /**
     * 获取类型 contentType
     *
     * @return
     */
    @Override
    public ContentPublishTypeEnum getContentType() {
        return ContentPublishTypeEnum.PACKAGE;
    }

    /**
     * 发布/回收操作
     */
    @Override
    public ResultResponse publishOrRecover(OutOrderBaseVo outOrderBaseVo) throws MalformedURLException {
        ResultResponse resultResponse=new ResultResponse();
        try {
            //获取文件内容（object）
            boolean flag = getObjectData(outOrderBaseVo, resultResponse);
            //产品包不包含图片 无需组装

            //获取文件关系（Mapping）
            if (flag) {
                //组装内容，构成xml文件
                if (getMappingData(outOrderBaseVo, resultResponse)) {
                    StringBuffer xmlDestination = SiteOperateUtil.getFileName(FtpHandleConstant.DIR_PACKAGE);
                    resultResponse.getData().setXmlDestination(xmlDestination.toString());
                }
            }
        } catch (Exception exception) {
            resultResponse.setErrorResult(String.format("执行产品包发布操作 ->>>>>> 发布类型:%s 内容ID:%s 执行发布工单操作失败！", getContentType().getContentTypeName(), outOrderBaseVo.getBmsContentId()));
            log.warn("执行产品包发布操作 ->>>>>> 发布类型:{} 内容ID:{}，执行发布工单操作失败,错误描述:{}！", getContentType().getContentTypeName(), outOrderBaseVo.getBmsContentId(),exception);
        } finally {
            return resultResponse;
        }
    }

    /**
     * 组装object内容
     *
     * @param outOrderBaseVo
     * @param resultResponse
     * @return
     */
    @Override
    public boolean getObjectData(OutOrderBaseVo outOrderBaseVo, ResultResponse resultResponse) {
        try {
            SubOrderXmlEntity subOrderXmlEntit = resultResponse.getData();
            //调用openfeign查询产品包信息
            Long bmsContentId = outOrderBaseVo.getBmsContentId();
            BmsPackage bmsPackage = getBmsPackageByBmsId(bmsContentId);
            if (bmsPackage == null) {
                resultResponse.setErrorResult(String.format("执行产品包发布操作 ->>>>>> 发布类型:%s 内容ID:%s 获取产品包信息失败，获取信息为空!",getContentType().getContentTypeName(),outOrderBaseVo.getBmsContentId()));
                return false;
            }
            //赋值extracode
            outOrderBaseVo.setExtraCode(bmsPackage.getExtraCode());
            List<SubOrderObjectsEntity> subOrderObjectsEntities = subOrderXmlEntit.getSubOrderObjectsEntities() != null
                    ? subOrderXmlEntit.getSubOrderObjectsEntities() : new ArrayList<>();
            subOrderXmlEntit.setSubOrderObjectsEntities(subOrderObjectsEntities);

            //获取产品包特性(Attribute)
            PackageObjectEntity packageObjectEntity = getAttribute(ActionEnums.getInfoByCode(outOrderBaseVo.getAction()), bmsPackage);
            //获取产品包属性(Property)
            Map<String, String> propertyDic = getProperty(bmsPackage);

            packageObjectEntity.setPropertyDic(propertyDic);
            packageObjectEntity.setSerialNumber(serialNumber);
            subOrderObjectsEntities.add(packageObjectEntity);

        } catch (Exception exception) {
            resultResponse.setErrorResult(String.format("执行产品包发布操作 ->>>>>> 发布类型:%s 内容ID:%s 组装object内容失败!",getContentType().getContentTypeName(),outOrderBaseVo.getBmsContentId()));
            log.error("执行产品包发布操作 ->>>>>> 发布类型:{} 内容ID:{} 产品包发布，组装object内容失败,错误描述:{}!",getContentType().getContentTypeName(),outOrderBaseVo.getBmsContentId(), exception);
            return false;
        }
        return true;
    }

    public Map<String, String> getProperty(BmsPackage bmsPackage) {
        Map<String, String> propertyDic = new HashMap<>();
        propertyDic.put("Name", SafeUtil.getString(bmsPackage.getName()));
        propertyDic.put("Type", SafeUtil.getString(bmsPackage.getType()));
        propertyDic.put("SortName", SafeUtil.getString(bmsPackage.getSortName()));
        propertyDic.put("SearchName", SafeUtil.getString(bmsPackage.getSearchName()));
        propertyDic.put("RentalPeriod", SafeUtil.getString(bmsPackage.getRentalPeriod()));
        propertyDic.put("OrderNumber", SafeUtil.getString(bmsPackage.getOrderNumber()));
        propertyDic.put("LicensingWindowStart", SafeUtil.getString(bmsPackage.getLicensingWindowStart()));
        propertyDic.put("LicensingWindowEnd", SafeUtil.getString(bmsPackage.getLicensingWindowEnd()));
        propertyDic.put("Price", SafeUtil.getString(bmsPackage.getPrice()));
        propertyDic.put("Description", SafeUtil.getString(bmsPackage.getDescription()));
        propertyDic.put("Status", SafeUtil.getString(bmsPackage.getStatus()));
        return propertyDic;
    }

    public PackageObjectEntity getAttribute(String action, BmsPackage bmsPackage) {
        PackageObjectEntity packageObjectEntity = new PackageObjectEntity();
        packageObjectEntity.setElementType(ObjectsTypeConstants.PACKAGE);
        boolean extraCode = outOperateUtil.isExtraCode(String.valueOf(bmsPackage.getSpId()));
        if (extraCode) {
            packageObjectEntity.setCode(bmsPackage.getExtraCode());
        } else {
            packageObjectEntity.setCode(bmsPackage.getCode());
        }
        packageObjectEntity.setId(packageObjectEntity.getCode());
        packageObjectEntity.setAction(action);
        return packageObjectEntity;
    }

    public BmsPackage getBmsPackageByBmsId(Long bmsContentId) {
        CommonResponse<BmsPackage> packageFeignClientById = bmsPackageFeignClient.getById(bmsContentId);
        return packageFeignClientById.getData();
    }

    /**
     * 组装mapping内容
     *
     * @param outOrderBaseVo
     * @param resultResponse
     * @return
     */
    @Override
    public boolean getMappingData(OutOrderBaseVo outOrderBaseVo, ResultResponse resultResponse) {
        try {
            SubOrderXmlEntity subOrderXmlEntit = resultResponse.getData();
            List<SubOrderMappingsEntity> subOrderMappingsEntityList = subOrderXmlEntit.getSubOrderMappingsEntities() != null
                    ? subOrderXmlEntit.getSubOrderMappingsEntities() : new ArrayList<>();
            subOrderXmlEntit.setSubOrderMappingsEntities(subOrderMappingsEntityList);
            //图片
            List<SubOrderObjectsEntity> subOrderObjectsEntities = subOrderXmlEntit.getSubOrderObjectsEntities();
            //需考虑产品包是否可能不带图片情况
            List<SubOrderObjectsEntity> collect = subOrderObjectsEntities.stream().filter(
                    p -> ObjectsTypeConstants.PICTURE.equals(p.getElementType())).collect(Collectors.toList());
            //当不带图片时，直接返回结果
            if (collect == null || collect.size() == 0) {
                return true;
            }
            String elementCode = outOrderBaseVo.getBmsContentCode();
            //判断code是否取extraCode
            boolean extraCode = outOperateUtil.isExtraCode(String.valueOf(outOrderBaseVo.getSpId()));
            if(extraCode){
                elementCode = outOrderBaseVo.getExtraCode();
            }
            //mapping实体序号
            AtomicInteger sequence = new AtomicInteger();
            for (SubOrderObjectsEntity subOrderObjectsEntity : collect) {
                SubOrderMappingsEntity subOrderMappingsEntity = new SubOrderMappingsEntity();
                subOrderMappingsEntity.setId(UUID.randomUUID().toString());
                subOrderMappingsEntity.setElementType(ObjectsTypeConstants.PACKAGE);
                subOrderMappingsEntity.setElementId(elementCode);
                subOrderMappingsEntity.setElementCode(elementCode);
                subOrderMappingsEntity.setSerialNumber(sequence.incrementAndGet());
                PictureObjectEntity pictureObjectEntity = (PictureObjectEntity) subOrderObjectsEntity;
                subOrderMappingsEntity.setAction(pictureObjectEntity.getAction());
                subOrderMappingsEntity.setParentType(ObjectsTypeConstants.PICTURE);
                subOrderMappingsEntity.setParentId(pictureObjectEntity.getCode());
                subOrderMappingsEntity.setParentCode(pictureObjectEntity.getCode());
                Map<String, String> propertyDic = new HashMap<>();
                propertyDic.put("Type", pictureObjectEntity.getType());
                propertyDic.put("Sequence", SafeUtil.getString(pictureObjectEntity.getSequence()));
                subOrderMappingsEntity.setPropertyDic(propertyDic);
                subOrderMappingsEntityList.add(subOrderMappingsEntity);
            }
            subOrderXmlEntit.setSubOrderMappingsEntities(subOrderMappingsEntityList);
        } catch (Exception exception) {
            resultResponse.setErrorResult(String.format("执行产品包发布操作 ->>>>>> 发布类型:%s 内容ID:%s 组装mapping内容失败!",getContentType().getContentTypeName(),outOrderBaseVo.getBmsContentId()));
            log.error("执行产品包发布操作 ->>>>>> 发布类型:{} 内容ID:{} 产品包发布，组装mapping内容失败,错误描述:{}!",getContentType().getContentTypeName(),outOrderBaseVo.getBmsContentId(), exception);
            return false;
        }
        return true;
    }

}
