package com.xxl.job.executor.common.config;

import com.pukka.iptv.common.base.constant.ObjectsTypeConstants;
import com.pukka.iptv.common.base.constant.RedisKeyConstants;
import com.pukka.iptv.common.core.constant.SymbolConstant;
import com.pukka.iptv.common.core.util.SafeUtil;
import com.pukka.iptv.common.data.model.sys.SysOutPassage;
import com.pukka.iptv.common.rabbitmq.constans.OutPublishConstant;
import com.pukka.iptv.common.redis.service.RedisService;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.amqp.core.BindingBuilder;
import org.springframework.amqp.core.DirectExchange;
import org.springframework.amqp.core.Queue;
import org.springframework.amqp.rabbit.core.RabbitAdmin;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 工单消费队列
 * 根据spchannelid绑定queue、routingkey
 */
@Configuration
public class RabbitQueueConfig {

    @Autowired
    private RedisService redisService;
    @Autowired
    RabbitAdmin rabbitAdmin;

    /**
     * 交换机
     *
     * @return
     */
    @Bean
    public DirectExchange outPublishlExchange() {
        return new DirectExchange(OutPublishConstant.OUT_EXCHANGE);
    }

    /**
     * Binding,将该routing key的消息通过交换机转发到该队列
     * 根据分发通道创建队列
     */
    @Bean
    public void initializeBinding() {
        Map<String, SysOutPassage> cacheList = redisService.getCacheMap(RedisKeyConstants.SYS_OUT_PASSAGE);
        Map<String, Object> args = new HashMap<String, Object>();
        args.put(ObjectsTypeConstants.PRIORITY_QUEUE, 10);
        if (ObjectUtils.isNotEmpty(cacheList)) {
            List<SysOutPassage> sysOutPassageList = cacheList.values().stream().collect(Collectors.toList());
            List<String> stringList = sysOutPassageList.stream().map(SysOutPassage::getCode).filter(code -> StringUtils.isNotEmpty(code)).distinct().collect(Collectors.toList());
            stringList.forEach((item) -> {
                rabbitAdmin.declareQueue(
                        new Queue(OutPublishConstant.OUT_ORDER_SEND_QUEUE + SymbolConstant.UNDER_SCORE + SafeUtil.getString(item) + ObjectsTypeConstants.REGIST_PRIORITY_QUEUE
                                , true, false, false, args));
                rabbitAdmin.declareBinding(BindingBuilder.bind(new Queue(OutPublishConstant.OUT_ORDER_SEND_QUEUE + SymbolConstant.UNDER_SCORE + SafeUtil.getString(item) + ObjectsTypeConstants.REGIST_PRIORITY_QUEUE)
                ).to(outPublishlExchange()).with(OutPublishConstant.OUT_ORDER_SEND_ROUTING + SymbolConstant.UNDER_SCORE + SafeUtil.getString(item) + ObjectsTypeConstants.REGIST_PRIORITY_QUEUE));
                rabbitAdmin.declareQueue(
                        new Queue(OutPublishConstant.OUT_ORDER_SEND_QUEUE + SymbolConstant.UNDER_SCORE + SafeUtil.getString(item) + ObjectsTypeConstants.OTHER_PRIORITY_QUEUE
                                , true, false, false, args));
                rabbitAdmin.declareBinding(BindingBuilder.bind(new Queue(OutPublishConstant.OUT_ORDER_SEND_QUEUE + SymbolConstant.UNDER_SCORE + SafeUtil.getString(item) + ObjectsTypeConstants.OTHER_PRIORITY_QUEUE)
                ).to(outPublishlExchange()).with(OutPublishConstant.OUT_ORDER_SEND_ROUTING + SymbolConstant.UNDER_SCORE + SafeUtil.getString(item) + ObjectsTypeConstants.OTHER_PRIORITY_QUEUE));
            });
        }
    }

}
