//package com.pukka.iptv.system.monitor.config;
//
//import com.pukka.iptv.common.core.constant.SpringSecurityConstant;
//import org.springframework.context.annotation.Bean;
//import org.springframework.context.annotation.Configuration;
//
//@Configuration
//@EnableWebFluxSecurity
//public class SecurityWebFluxConfig {
//
//    /**
//     * 访问权限授权
//     *
//     * @param http
//     * @return
//     */
//    @Bean
//    SecurityWebFilterChain webFluxSecurityFilterChain(ServerHttpSecurity http) {
//        http.headers().frameOptions().disable()
//                .and().authorizeExchange(exchange -> exchange // 请求拦截处理
//                .pathMatchers(SpringSecurityConstant.NONE_SECURITY_URL_PATTERNS).permitAll()
//                //.pathMatchers(HttpMethod.OPTIONS).permitAll()
//                .anyExchange().access(defaultAuthorizationManager)
//                                //.anyExchange().authenticated() //自定义的鉴权服务，通过鉴权的才能继续访问某个请求
//                )
//                //.addFilterAfter(new LoginFilter(), SecurityWebFiltersOrder.AUTHORIZATION)//拦截处理
//                .exceptionHandling().accessDeniedHandler(defaultAccessDeniedHandler)//权限认证失败处理
//                .and().exceptionHandling().authenticationEntryPoint(defaultAuthenticationEntryPoint)//匿名认证失败
//                .and().httpBasic()
//                .and().formLogin().loginPage("/auth/login")
//                .securityContextRepository(defaultSecurityContextRepository)//放在filter之后
//                .authenticationManager(defaultAuthenticationManager)//自定义登录验证。自动扫描注入，无需手动注入
//                .authenticationSuccessHandler(loginSuccessHandlerWebFlux) //认证成功
//                .authenticationFailureHandler(loginFailedHandlerWebFlux) //登陆验证失败
//                .and().csrf().disable()//必须支持跨域
//                .logout().logoutUrl("/auth/logout")
////                .logoutHandler()
//                .logoutSuccessHandler(logoutSuccessHandlerWebFlux); //成功登出时调用的自定义处理类
//
//        //自定义的session操作过滤器
////        http.addFilterAt(new MySessionManageFilter(), SecurityWebFiltersOrder.FIRST);
//        return http.build();
//    }
//}