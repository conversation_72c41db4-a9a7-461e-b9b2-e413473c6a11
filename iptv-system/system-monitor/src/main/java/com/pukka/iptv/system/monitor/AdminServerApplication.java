package com.pukka.iptv.system.monitor;

import de.codecentric.boot.admin.server.config.AdminServerHazelcastAutoConfiguration;
import de.codecentric.boot.admin.server.config.EnableAdminServer;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.autoconfigure.jdbc.DataSourceAutoConfiguration;

import java.util.Map;

/**
 * <AUTHOR>
 * @date: 2021/7/23
 */

@EnableAdminServer
@SpringBootApplication(exclude = {AdminServerHazelcastAutoConfiguration.class,DataSourceAutoConfiguration.class})
public class AdminServerApplication {
    public static void main(String[] args) {
        SpringApplication.run(AdminServerApplication.class,args);
    }

}
