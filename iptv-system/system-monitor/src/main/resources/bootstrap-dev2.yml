server:
  port: 7010
#  servlet:
#    context-path: /@project.artifactId@
#nacos服务地址及账号密码
spring:
  cloud:
    nacos:
      discovery:
        ip: **************
nacos:
  server-addr: **************:8848
  namespace: bokong_dev
  username: nacos
  password: LCW6KLEAHa_vectvznfv
  #password: nacos
  group: iptv
  file-extension: yaml
  # app-name: ${spring.application.name}
hostname: ${COMPUTERNAME:logs}
