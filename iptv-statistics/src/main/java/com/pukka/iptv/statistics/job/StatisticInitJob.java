package com.pukka.iptv.statistics.job;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.pukka.iptv.common.data.model.statistics.StatisticsIn;
import com.pukka.iptv.common.data.model.statistics.StatisticsInInit;
import com.pukka.iptv.common.data.util.TimeUtil;
import com.pukka.iptv.common.data.vo.req.StatisticsInVo;
import com.pukka.iptv.statistics.service.StatisticsInInitService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.time.LocalDateTime;
import java.time.LocalTime;

import java.util.Date;
import java.util.List;

/**
 * @ClassName StatisticInit
 * @Description TODO
 * <AUTHOR>
 * @Date 2022/7/21 15:57
 * @Version
 */
@Component
@Slf4j
public class StatisticInitJob implements IStatisticJob{

    @Autowired
    StatisticsInInitService statisticsInInitService;

    @Autowired
    StatisticProgramJob statisticProgramJob;
    @Autowired
    StatisticResourceJob statisticResourceJob;
    @Autowired
    StatisticSeriesJob statisticSeriesJob;
    @Autowired
    StatisticSubJob statisticSubJob;

    @Override
    public void executeJob() {
        log.info("StatisticInitJob start");
        LambdaQueryWrapper<StatisticsInInit> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.eq(StatisticsInInit::getStatus, 1);
        List<StatisticsInInit> statisticInitList = statisticsInInitService.list(queryWrapper);
        for(StatisticsInInit e : statisticInitList){
            e.setStatus(2);
            statisticsInInitService.updateById(e);
        }

        statisticInitList.forEach(e -> {

            SimpleDateFormat formater = new SimpleDateFormat("yyyy-MM-dd");
            SimpleDateFormat formater2 = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
            Date start = null;
            Date end = null;
            try {
                start = formater2.parse(formater.format(TimeUtil.getDate(e.getStatisticDate()))+ " 00:00:00");
                end = formater2.parse(formater.format(TimeUtil.getDate(e.getStatisticDate()))+ " 23:59:59");
            } catch (ParseException parseException) {
                parseException.printStackTrace();
            }

            StatisticsInVo statisticsInVo = new StatisticsInVo();
            statisticsInVo.setStartTime(TimeUtil.formatDate(start));

            statisticsInVo.setEndTime(TimeUtil.formatDate(end));
            statisticProgramJob.executeJob(statisticsInVo, true);
            statisticResourceJob.executeJob(statisticsInVo, true);
            statisticSubJob.executeJob(statisticsInVo, true);
            statisticSeriesJob.executeJob(statisticsInVo, true);

            //完成后修改
            e.setStatus(0);
            e.setUpdateTime( LocalDateTime.now().toString());
            statisticsInInitService.updateById(e);

            log.info("statisticInitList,date:{}, enddate:{}",TimeUtil.formatDate(start),TimeUtil.formatDate(end) );
        });
        log.info("StatisticInitJob finish");
    }
}
