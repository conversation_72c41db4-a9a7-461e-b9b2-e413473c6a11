package com.pukka.iptv.statistics.vo;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

/**
 *
 * @author: zhengcl
 * @date: 2022年7月13日 下午5:23:51
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@JsonIgnoreProperties(value = { "handler" })
@ApiModel("StatisticsOutOrderItemVo")
@Accessors(chain = true)
public class StatisticsOutOrderItemVo implements java.io.Serializable{

    @ApiModelProperty(value="所属渠道id",dataType="Long",name="bmsSpChannelId")
    private Long bmsSpChannelId;

    @ApiModelProperty(value="所属渠道",dataType="String",name="bmsSpChannelName")
    private String bmsSpChannelName;

    @ApiModelProperty(value="spId",dataType="Long",name="spId")
    private Long spId;

    @ApiModelProperty(value="SP名称",dataType="String",name="spName")
    private String spName;

    @ApiModelProperty(value = "分发通道ID，", dataType = "Long", name = "outPassageId")
    private Long outPassageId;

    @ApiModelProperty(value = "分发通道code", dataType = "String", name = "outPassageCode")
    private String outPassageCode;

    @ApiModelProperty(value = "分发通道名称", dataType = "String", name = "outPassageName")
    private String outPassageName;

    @ApiModelProperty(value="状态 1：待处理 2：处理中 3：处理成功 4：处理失败",dataType="Integer",name="status")
    private Integer status;

    @ApiModelProperty(value="接口结果 {0:成功,-1:失败}",dataType="Integer",name="result")
    private Integer result;

    @ApiModelProperty(value="动作 1：REGIST，2：UPDATE，3：DELETE",dataType="Integer",name="action")
    private Integer action;

    @ApiModelProperty(value="全部工单工单数量",dataType="Integer",name="countAll")
    private Integer countAll = 0;

    @ApiModelProperty(value="全部工单下发成功工单数量",dataType="Integer",name="successCount")
    private Integer successCountAll = 0;

    @ApiModelProperty(value="全部工单下发失败工单数量",dataType="Integer",name="failCount")
    private Integer failCountAll = 0;

    @ApiModelProperty(value="全部工单发布平均时长",dataType="Integer",name="duration")
    private Integer durationAll = 0;

    @ApiModelProperty(value="全部工单成功率",dataType="Double",name="successRate")
    private Double successRateAll = 0d;

    @ApiModelProperty(value="新增工单工单数量",dataType="Integer",name="countRegist")
    private Integer countRegist = 0;

    @ApiModelProperty(value="新增工单下发成功工单数量",dataType="Integer",name="successCountRegist")
    private Integer successCountRegist = 0;

    @ApiModelProperty(value="新增工单下发失败工单数量",dataType="Integer",name="failCountRegist")
    private Integer failCountRegist = 0;

    @ApiModelProperty(value="新增工单发布平均时长",dataType="Integer",name="durationRegist")
    private Integer durationRegist = 0;

    @ApiModelProperty(value="新增工单成功率",dataType="Double",name="successRateRegist")
    private Double successRateRegist = 0d;

    @ApiModelProperty(value="更新工单工单数量",dataType="Integer",name="countUpdate")
    private Integer countUpdate = 0;

    @ApiModelProperty(value="更新工单下发成功工单数量",dataType="Integer",name="successCountUpdate")
    private Integer successCountUpdate = 0;

    @ApiModelProperty(value="更新工单下发失败工单数量",dataType="Integer",name="failCountUpdate")
    private Integer failCountUpdate = 0;

    @ApiModelProperty(value="更新工单发布平均时长",dataType="Integer",name="durationUpdate")
    private Integer durationUpdate = 0;

    @ApiModelProperty(value="更新工单成功率",dataType="Double",name="successRateUpdate")
    private Double successRateUpdate = 0d;

    @ApiModelProperty(value="删除工单工单数量",dataType="Integer",name="countDelete")
    private Integer countDelete = 0;

    @ApiModelProperty(value="删除工单下发成功工单数量",dataType="Integer",name="successCountDelete")
    private Integer successCountDelete = 0;

    @ApiModelProperty(value="删除工单下发失败工单数量",dataType="Integer",name="failCountDelete")
    private Integer failCountDelete = 0;

    @ApiModelProperty(value="删除工单发布平均时长",dataType="Integer",name="durationDelete")
    private Integer durationDelete = 0;

    @ApiModelProperty(value="删除工单成功率",dataType="Double",name="successRateDelete")
    private Double successRateDelete = 0d;

}
