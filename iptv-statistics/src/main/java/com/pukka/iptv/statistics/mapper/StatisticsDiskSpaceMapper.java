package com.pukka.iptv.statistics.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.pukka.iptv.statistics.model.StatisticsDiskSpace;
import com.pukka.iptv.statistics.vo.StatisticsDiskSpaceCpRespVo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * @author: zhengcl
 * @date: 2022年7月13日 下午5:23:51
 */
@Mapper
public interface StatisticsDiskSpaceMapper extends BaseMapper<StatisticsDiskSpace> {

    Integer batchInsert(@Param("statisticsDiskSpaces") List<StatisticsDiskSpace> statisticsDiskSpaces);

    List<String> selectDiskSpaceOverThreshold(@Param("cpIds") List<Long> cpIds);

    StatisticsDiskSpace selectDiskSpaceTotal(@Param("storageId") Long storageId, @Param("beginOfDay") String beginOfDay,
                                             @Param("endOfDay") String endOfDay);

    List<StatisticsDiskSpaceCpRespVo> getDiskSpacePieChart(@Param("storageId") Long storageId, @Param("cpId") Long cpId,
                                                           @Param("beginOfDay") String beginOfDay, @Param("endOfDay") String endOfDay);

    List<String> selectDiskSpaceStop(@Param("cpIds") List<Long> cpIds);
}
