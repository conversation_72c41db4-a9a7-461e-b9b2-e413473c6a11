package com.pukka.iptv.statistics.controller;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.pukka.iptv.common.base.vo.CommonResponse;
import com.pukka.iptv.statistics.model.OutOrderItemESModel;
import com.pukka.iptv.statistics.service.es.ESService;
import com.pukka.iptv.statistics.vo.StatisticsOutOrderItemReqVo;
import com.pukka.iptv.statistics.vo.StatisticsOutOrderItemVo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.AllArgsConstructor;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.validation.Valid;

/**
 * @author: zhengcl
 * @date: 2022年7月13日 下午5:23:51
 */
@RestController
@AllArgsConstructor
@RequestMapping(value = "/statisticsOutOrderItem", produces = MediaType.APPLICATION_JSON_VALUE)
@Api(tags = "工单统计管理")
public class StatisticsOutOrderItemController {

    @Resource
    private ESService esService;

    @ApiOperation(value = "工单总量汇总查询")
    @GetMapping("/getOutOrderItemTotal")
    public CommonResponse<StatisticsOutOrderItemVo> getOutOrderItemTotal(@Valid StatisticsOutOrderItemReqVo statisticsOutOrderItemVo) {
        return CommonResponse.success(esService.getOutOrderItemTotal(statisticsOutOrderItemVo));
    }

    @ApiOperation(value = "工单明细分页查询")
    @GetMapping("/getOutOrderItemPage")
    public CommonResponse<Page<OutOrderItemESModel>> getOutOrderItemPage(@Valid Page page, StatisticsOutOrderItemReqVo statisticsOutOrderItemVo) {
        return CommonResponse.success(esService.getOutOrderItemPage(page, statisticsOutOrderItemVo));
    }

}
