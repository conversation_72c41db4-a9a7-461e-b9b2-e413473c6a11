package com.pukka.iptv.statistics.controller;


import javax.validation.Valid;

import com.pukka.iptv.common.base.vo.CommonResponse;
import com.pukka.iptv.common.data.model.statistics.StatisticsInCheckInit;
import com.pukka.iptv.common.data.model.statistics.StatisticsInFinalInit;
import com.pukka.iptv.common.data.vo.req.StatisticsInInitVo;
import com.pukka.iptv.statistics.service.StatisticsInFinalInitService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.*;
import org.springframework.beans.factory.annotation.Autowired;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.AllArgsConstructor;
import com.pukka.iptv.common.data.vo.*;

import java.util.List;


/**
 *
 * @author: tan
 * @date: 2022-8-11 17:05:24
 */

@RestController
@AllArgsConstructor
@RequestMapping(value = "/statisticsInFinalInit", produces = MediaType.APPLICATION_JSON_VALUE)
@Api(tags="statisticsInFinalInit管理")
public class StatisticsInFinalInitController {

    @Autowired
    private StatisticsInFinalInitService statisticsInFinalInitService;

    @ApiOperation(value = "初始化统计基础数据")
    @PostMapping("/initData")
    public CommonResponse<List<StatisticsInFinalInit>> initData(@Valid @RequestBody StatisticsInInitVo statisticsInInitVo) {
        return  statisticsInFinalInitService.getAllStatistics(statisticsInInitVo);
    }

    @ApiOperation(value = "分页")
    @GetMapping("/page" )
    public CommonResponse<Page> page(@Valid Page page, StatisticsInFinalInit statisticsInFinalInit) {
        return  CommonResponse.success(statisticsInFinalInitService.page(page, Wrappers.query(statisticsInFinalInit)));
    }

    @ApiOperation(value = "详情")
    @GetMapping("/getById")
    public CommonResponse<StatisticsInFinalInit> getById(@Valid @RequestParam(name = "id", required = true)  Long id) {
        return CommonResponse.success(statisticsInFinalInitService.getById(id));
    }

    @ApiOperation(value = "新增")
    @PostMapping("/save")
    public CommonResponse<Boolean> save(@Valid @RequestBody StatisticsInFinalInit statisticsInFinalInit) {
        return  CommonResponse.success(statisticsInFinalInitService.save(statisticsInFinalInit));
    }

    @ApiOperation(value = "修改")
    @PutMapping("/update")
    public CommonResponse<Boolean> updateById(@Valid @RequestBody StatisticsInFinalInit statisticsInFinalInit) {
        return CommonResponse.success(statisticsInFinalInitService.updateById(statisticsInFinalInit));
    }

    @ApiOperation(value = "删除")
    @DeleteMapping("/deleteById" )
    public CommonResponse<Boolean> deleteById(@Valid @RequestParam(name = "id", required = true) Long id) {
        return  CommonResponse.success(statisticsInFinalInitService.removeById(id));
    }

    @ApiOperation(value = "批量删除")
    @DeleteMapping("/deleteByIds" )
    public CommonResponse<Boolean> deleteByIds(@Valid @RequestBody IdList idList) {
        return  CommonResponse.success(statisticsInFinalInitService.removeByIds(idList.getIds()));
    }

}
