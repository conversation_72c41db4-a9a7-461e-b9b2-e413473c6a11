package com.pukka.iptv.statistics.service.es.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateUtil;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.pukka.iptv.common.base.enums.*;
import com.pukka.iptv.common.base.exception.CommonResponseException;
import com.pukka.iptv.common.base.util.JwtTokenUtil;
import com.pukka.iptv.common.base.vo.SecurityUser;
import com.pukka.iptv.common.core.util.DateUtils;
import com.pukka.iptv.common.core.util.StringUtils;
import com.pukka.iptv.common.data.model.sys.SysCp;
import com.pukka.iptv.common.data.model.sys.SysOutPassage;
import com.pukka.iptv.common.data.model.sys.SysSp;
import com.pukka.iptv.statistics.mapper.SysOutPassageMapper;
import com.pukka.iptv.statistics.mapper.sys.SysCpMapper;
import com.pukka.iptv.statistics.mapper.sys.SysSpMapper;
import com.pukka.iptv.statistics.model.*;
import com.pukka.iptv.statistics.service.es.ESService;
import com.pukka.iptv.statistics.vo.*;
import lombok.extern.slf4j.Slf4j;
import org.apache.lucene.search.TotalHits;
import org.elasticsearch.action.bulk.BulkRequest;
import org.elasticsearch.action.bulk.BulkResponse;
import org.elasticsearch.action.index.IndexRequest;
import org.elasticsearch.action.search.SearchRequest;
import org.elasticsearch.action.search.SearchResponse;
import org.elasticsearch.action.support.WriteRequest;
import org.elasticsearch.action.update.UpdateRequest;
import org.elasticsearch.action.update.UpdateResponse;
import org.elasticsearch.client.HttpAsyncResponseConsumerFactory;
import org.elasticsearch.client.RequestOptions;
import org.elasticsearch.client.RestHighLevelClient;
import org.elasticsearch.index.query.*;
import org.elasticsearch.index.reindex.DeleteByQueryRequest;
import org.elasticsearch.index.reindex.UpdateByQueryRequest;
import org.elasticsearch.script.Script;
import org.elasticsearch.script.ScriptType;
import org.elasticsearch.search.SearchHit;
import org.elasticsearch.search.SearchHits;
import org.elasticsearch.search.aggregations.*;
import org.elasticsearch.search.aggregations.bucket.terms.ParsedStringTerms;
import org.elasticsearch.search.aggregations.bucket.terms.Terms;
import org.elasticsearch.search.aggregations.bucket.terms.TermsAggregationBuilder;
import org.elasticsearch.search.aggregations.metrics.*;
import org.elasticsearch.search.aggregations.pipeline.BucketSelectorPipelineAggregationBuilder;
import org.elasticsearch.search.aggregations.pipeline.ParsedStatsBucket;
import org.elasticsearch.search.builder.SearchSourceBuilder;
import org.elasticsearch.search.collapse.CollapseBuilder;
import org.elasticsearch.search.sort.FieldSortBuilder;
import org.elasticsearch.search.sort.SortOrder;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.io.IOException;
import java.text.DecimalFormat;
import java.util.*;
import java.util.stream.Collectors;

/**
 * @author: wangrh
 * @description: 用户ES操作类
 * @date: 2022-7-13
 **/
@Slf4j
@Service
@RefreshScope
public class ESServiceImpl implements ESService {

    @Value("${index.outOrderItem}")
    private String outOrderItem;
    @Value("${index.bmsContent}")
    private String bmsContent;
    @Value("${index.bmsProgram}")
    private String bmsProgram;
    @Value("${index.bmsPackageContent}")
    private String bmsPackageContent;
    @Value("${index.bmsCategoryContent}")
    private String bmsCategoryContent;
    @Value("${newOnline.export.size}")
    private Integer size;

    @Resource
    private RestHighLevelClient restHighLevelClient;
    @Resource
    private ObjectMapper objectMapper;
    @Resource
    private SysSpMapper sysSpMapper;
    @Resource
    private SysCpMapper sysCpMapper;
    @Resource
    private SysOutPassageMapper sysOutPassageMapper;

    private static final RequestOptions options = RequestOptions.DEFAULT;

    @Override
    public <T> Boolean insert(T esBody, String index) {
        if (null == esBody || null == index) {
            return false;
        }
        Map<String, Object> esMap = JSON.parseObject(JSON.toJSONString(esBody), Map.class);
        IndexRequest indexRequest = new IndexRequest(index);
        indexRequest.source(esMap);
        indexRequest.setRefreshPolicy(WriteRequest.RefreshPolicy.IMMEDIATE);
        try {
            restHighLevelClient.index(indexRequest, options);
            Thread.sleep(1000);
            log.info("es.insert.esMap={}", JSON.toJSONString(esBody));
            return true;
        } catch (Exception e) {
            if (!(e.getMessage()).contains("Created")) {
                log.error("ESServiceImpl.insert.esBody={}", JSON.toJSONString(esBody));
                log.error("ESServiceImpl.insert", e);
                return false;
            }
            return true;
        }
    }

    @Override
    public <T> Boolean upsert(T esBody, String index) {
        try {
            Map<String, Object> esMap = BeanUtil.beanToMap(esBody);
            UpdateByQueryRequest request = updateByQuery(index, new TermQueryBuilder("_id", esMap.get("id")), esMap);
            restHighLevelClient.updateByQuery(request, options);
            return true;
        } catch (Exception e) {
            log.error("upsert失败，esBody={}", JSON.toJSONString(esBody), e);
            return false;
        }
    }

    @Override
    public <T> T selectByIdAndStatusFlag(Long id, String index, Class<T> clz) {
        SearchRequest searchRequest;
        searchRequest = new SearchRequest(index);
        BoolQueryBuilder boolQuery = QueryBuilders.boolQuery();
        boolQuery.must(QueryBuilders.termQuery("id", id));
        boolQuery.must(QueryBuilders.termQuery("statusFlag", BmsContentStatusFlagEnum.NEW.getCode()));
        SearchSourceBuilder searchSourceBuilder = new SearchSourceBuilder();
        searchSourceBuilder.query(boolQuery);
        searchSourceBuilder.from(0);
        searchSourceBuilder.size(1);
        searchRequest.source(searchSourceBuilder);
        SearchResponse searchResponse = search(searchRequest);
        SearchHit[] hits = searchResponse.getHits().getHits();
        if (hits.length > 0) {
            Map<String, Object> sourceAsMap = hits[0].getSourceAsMap();
            return objectMapper.convertValue(sourceAsMap, clz);
        }
        return null;
    }

    @Override
    public <T> Boolean updateByCode(T esBody, String index) {
        try {
            Map<String, Object> esMap = JSON.parseObject(JSON.toJSONString(esBody), Map.class);
            UpdateByQueryRequest request = updateByQuery(index, new TermQueryBuilder("code", esMap.get("code")), esMap);
            restHighLevelClient.updateByQuery(request, options);
            return true;
        } catch (Exception e) {
            log.error("es更新失败，esBody={}", JSON.toJSONString(esBody), e);
            return false;
        }
    }

    @Override
    public <T> Boolean updateById(T esBody, String index) {
        try {
            Map<String, Object> esMap = JSON.parseObject(JSON.toJSONString(esBody), Map.class);
            SearchRequest searchRequest;
            searchRequest = new SearchRequest(index);
            BoolQueryBuilder boolQuery = QueryBuilders.boolQuery();
            boolQuery.must(QueryBuilders.termQuery("id", esMap.get("id")));
            boolQuery.must(QueryBuilders.termQuery("statusFlag", BmsContentStatusFlagEnum.NEW.getCode()));
            SearchSourceBuilder searchSourceBuilder = new SearchSourceBuilder();
            searchSourceBuilder.query(boolQuery);
            searchSourceBuilder.from(0);
            searchSourceBuilder.size(1);
            searchRequest.source(searchSourceBuilder);
            SearchResponse searchResponse = search(searchRequest);
            SearchHit[] hits = searchResponse.getHits().getHits();
            if (hits.length == 0) {
                return true;
            }
            String docId = hits[0].getId();
            UpdateRequest updateRequest = new UpdateRequest();
            updateRequest.index(index);
            updateRequest.id(docId);
            updateRequest.doc(esMap);
            updateRequest.setRefreshPolicy(WriteRequest.RefreshPolicy.IMMEDIATE);
            UpdateResponse update = restHighLevelClient.update(updateRequest, options);
            Thread.sleep(1000);
            log.info("updateById.docId={}", docId);
            log.info("updateById.esMap={}", JSON.toJSONString(esMap));
            log.info("updateById.UpdateResponse={}", JSON.toJSONString(update));
            return true;
        } catch (Exception e) {
            log.error("es更新失败，esBody={}", JSON.toJSONString(esBody), e);
            return true;
        }
    }

    @Override
    public <T> Boolean updateByField(T esBody, String field, String index) {
        try {
            Map<String, Object> esMap = JSON.parseObject(JSON.toJSONString(esBody), Map.class);
            SearchRequest searchRequest;
            searchRequest = new SearchRequest(index);
            BoolQueryBuilder boolQuery = QueryBuilders.boolQuery();
            boolQuery.must(QueryBuilders.termQuery(field, esMap.get(field)));
            SearchSourceBuilder searchSourceBuilder = new SearchSourceBuilder();
            searchSourceBuilder.query(boolQuery);
            searchRequest.source(searchSourceBuilder);
            SearchResponse searchResponse = search(searchRequest);
            SearchHit[] hits = searchResponse.getHits().getHits();
            if (hits.length == 0) {
                return true;
            }
            BulkRequest bulkRequest = new BulkRequest();
            for (SearchHit hit : hits) {
                UpdateRequest updateRequest = new UpdateRequest();
                updateRequest.index(index);
                updateRequest.id(hit.getId());
                updateRequest.doc(esMap);
                bulkRequest.add(updateRequest);
                log.info("updateById.docId={}", hit.getId());
            }
            bulkRequest.setRefreshPolicy(WriteRequest.RefreshPolicy.IMMEDIATE);
            BulkResponse bulk = restHighLevelClient.bulk(bulkRequest, options);
            Thread.sleep(1000);
            log.info("updateById.esMap={}", JSON.toJSONString(esMap));
            log.info("updateById.BulkResponse={}", JSON.toJSONString(bulk));
            return true;
        } catch (Exception e) {
            log.error("es更新失败，esBody={}", JSON.toJSONString(esBody), e);
            return true;
        }
    }

    @Override
    public Boolean deleteByIdAndStatusFlag(Long id, String index) {
        try {
            DeleteByQueryRequest deleteByQueryRequest = new DeleteByQueryRequest(index);
            BoolQueryBuilder boolQueryBuilder = new BoolQueryBuilder();
            boolQueryBuilder.must(QueryBuilders.termQuery("id", id));
            boolQueryBuilder.must(QueryBuilders.termQuery("statusFlag", BmsContentStatusFlagEnum.NEW.getCode()));
            deleteByQueryRequest.setQuery(boolQueryBuilder);
            restHighLevelClient.deleteByQuery(deleteByQueryRequest, options);
            return true;
        } catch (Exception e) {
            log.error("deleteByIdAndStatusFlag.es删除失败，id={},index={}", id, index, e);
            return false;
        }
    }

    @Override
    public Boolean updateByIdAndStatusFlag(List<Long> ids, String index) {
        try {
            BoolQueryBuilder boolQuery = new BoolQueryBuilder();
            boolQuery.must(QueryBuilders.termsQuery("id", ids));
            boolQuery.must(QueryBuilders.termQuery("statusFlag", BmsContentStatusFlagEnum.NEW.getCode()));
            Map<String, Object> esMap = new HashMap<>();
            esMap.put("publishStatus", PublishStatusEnum.ROLLBACK.getCode());
            esMap.put("statusFlag", BmsContentStatusFlagEnum.OLD.getCode());
            UpdateByQueryRequest request = updateByQuery(index, boolQuery, esMap);
            restHighLevelClient.updateByQuery(request, options);
            return true;
        } catch (Exception e) {
            log.error("updateByIdAndStatusFlag.es更新失败，ids={}", JSON.toJSONString(ids), e);
            return false;
        }
    }

    /**
     * 根据条件进行搜索
     *
     * @param searchRequest 搜索条件
     * @return searchResponse 搜索结果
     */
    private SearchResponse search(SearchRequest searchRequest) {
        RequestOptions.Builder builder = RequestOptions.DEFAULT.toBuilder();
        builder.setHttpAsyncResponseConsumerFactory(
                new HttpAsyncResponseConsumerFactory
                        //修改为500MB
                        .HeapBufferedResponseConsumerFactory(10000 * 1024 * 1024));
        SearchResponse searchResponse = null;
        try {
            searchResponse = restHighLevelClient.search(searchRequest, builder.build());
        } catch (IOException e) {
            e.printStackTrace();
        }
        return searchResponse;
    }

    private UpdateByQueryRequest updateByQuery(String index, QueryBuilder query, Map<String, Object> document) {
        UpdateByQueryRequest updateByQueryRequest = new UpdateByQueryRequest(index);
        updateByQueryRequest.setQuery(query);
        StringBuilder script = new StringBuilder();
        Set<String> keys = document.keySet();
        for (String key : keys) {
            String appendValue;
            Object value = document.get(key);
            if (null != value) {
                if (value instanceof Number) {
                    appendValue = value.toString();
                } else if (value instanceof String) {
                    appendValue = "'" + value.toString() + "'";
                } else if (value instanceof List) {
                    appendValue = JSON.toJSONString(value);
                } else {
                    appendValue = value.toString();
                }
                script.append("ctx._source.").append(key).append("=").append(appendValue).append(";");
            }
        }
        updateByQueryRequest.setScript(new Script(script.toString()));
        return updateByQueryRequest;
    }

    @Override
    public StatisticsOutOrderItemVo getOutOrderItemTotal(StatisticsOutOrderItemReqVo statisticsOutOrderItemVo) {
        int durationRegistFail = 0;
        int durationUpdateFail = 0;
        int durationDeleteFail = 0;
        StatisticsOutOrderItemVo vo = new StatisticsOutOrderItemVo();
        List<String> lspIdList = getLspPermission();
        if (CollUtil.isEmpty(lspIdList)) {
            return vo;
        }
        long l = System.currentTimeMillis();
        SearchRequest searchRequest = new SearchRequest(outOrderItem);
        BoolQueryBuilder boolQuery = QueryBuilders.boolQuery();
        Long outPassageId = statisticsOutOrderItemVo.getOutPassageId();
        Long bmsSpChannelId = statisticsOutOrderItemVo.getBmsSpChannelId();
        Long spId = statisticsOutOrderItemVo.getSpId();
        String startTime = statisticsOutOrderItemVo.getStartTime();
        String endTime = statisticsOutOrderItemVo.getEndTime();
        String scriptString = "doc['action'] +'#'+ doc['result']";
        if (StringUtils.isNotEmpty(startTime) && StringUtils.isNotEmpty(endTime)) {
            String startTimeFormat = DateUtil.format(DateUtil.beginOfDay(DateUtil.parse(startTime, DateUtils.YYYY_MM_DD)), DateUtils.YYYY_MM_DD_HH_MM_SS);
            String endTimeFormat = DateUtil.format(DateUtil.endOfDay(DateUtil.parse(endTime, DateUtils.YYYY_MM_DD)), DateUtils.YYYY_MM_DD_HH_MM_SS);
            boolQuery.must(QueryBuilders.rangeQuery("createTime").gte(startTimeFormat).lte(endTimeFormat));
        }
        if (null != outPassageId) {
            SysOutPassage sysOutPassage = sysOutPassageMapper.selectById(outPassageId);
            boolQuery.must(QueryBuilders.matchQuery("tableName", "out_order_item_" + sysOutPassage.getCode()));
            vo.setOutPassageId(outPassageId);
        }
        if (null != bmsSpChannelId) {
            boolQuery.must(QueryBuilders.matchQuery("bmsSpChannelId", bmsSpChannelId));
            vo.setBmsSpChannelId(bmsSpChannelId);
        }
        if (null != spId) {
            List<String> lspIds = sysSpMapper.selectLspIdListBySp(spId);
            if (CollUtil.isNotEmpty(lspIdList)) {
                lspIdList.retainAll(lspIds);
            } else {
                lspIdList.addAll(lspIds);
            }
            log.info("==========SQL耗时{}ms", System.currentTimeMillis() - l);
            lspIdList = lspIdList.parallelStream().filter(Objects::nonNull).collect(Collectors.toList());
            if (CollUtil.isEmpty(lspIdList)) {
                return vo;
            }
            vo.setSpId(spId);
        }
        boolQuery.must(QueryBuilders.termsQuery("lspId", lspIdList));
        //根据多字段进行分组查询
        TermsAggregationBuilder aggBuilder = AggregationBuilders.terms("out_order_item_group")
                .script(new Script(scriptString));
        //计算发布时间平均值
        aggBuilder.subAggregation(AggregationBuilders.avg("durationAvg").field("duration"));
        aggBuilder.size(1000);
        SearchSourceBuilder searchSourceBuilder = new SearchSourceBuilder();
        searchSourceBuilder.aggregation(aggBuilder);
        searchSourceBuilder.query(boolQuery);
        searchSourceBuilder.size(0);
        searchRequest.source(searchSourceBuilder);
        SearchResponse searchResponse = search(searchRequest);
        log.info("ES搜索语句={}", searchRequest.source().toString());
        Aggregations aggregations = searchResponse.getAggregations();
        Terms outOrderItemGroup = aggregations.get("out_order_item_group");
        for (Terms.Bucket bucket : outOrderItemGroup.getBuckets()) {
            //获取数量
            Integer count = Integer.parseInt(String.valueOf(bucket.getDocCount()));
            //获取分组信息
            String keyAsString = bucket.getKeyAsString();
            String[] split = keyAsString.split("#");
            String substring = split[0].substring(1, split[0].length() - 1);
            int action = ActionEnums.OTHERS.getCode();
            if (StringUtils.isNotEmpty(substring)) {
                action = Integer.parseInt(substring);
            }
            String resultString = split[1].substring(1, split[1].length() - 1);
            int result = OrderBaseResultEnum.OTHERS.getCode();
            if (StringUtils.isNotEmpty(resultString)) {
                result = Integer.parseInt(resultString);
            }
            //获取平均发布时长
            ParsedAvg aggregation = bucket.getAggregations().get("durationAvg");
            double value = aggregation.getValue();
            int durationAvg = 0;
            if (Double.POSITIVE_INFINITY != value) {
                durationAvg = (int) Math.round(value);
            }
            if (ActionEnums.REGIST.getCode().equals(action)) {
                if (ItemResultEnum.Success.getValue().equals(result)) {
                    vo.setSuccessCountRegist(vo.getSuccessCountRegist() + count);
                    vo.setDurationRegist(durationAvg);
                } else if (ItemResultEnum.Fail.getValue().equals(result)) {
                    vo.setFailCountRegist(vo.getFailCountRegist() + count);
                    durationRegistFail = durationAvg;
                } else {
                    vo.setCountRegist(vo.getCountRegist() + count);
                }
            } else if (ActionEnums.UPDATE.getCode().equals(action)) {
                if (ItemResultEnum.Success.getValue().equals(result)) {
                    vo.setSuccessCountUpdate(vo.getSuccessCountUpdate() + count);
                    vo.setDurationUpdate(durationAvg);
                } else if (ItemResultEnum.Fail.getValue().equals(result)) {
                    vo.setFailCountUpdate(vo.getFailCountUpdate() + count);
                    durationUpdateFail = durationAvg;
                } else {
                    vo.setCountUpdate(vo.getCountUpdate() + count);
                }
            } else if (ActionEnums.DELETE.getCode().equals(action)) {
                if (ItemResultEnum.Success.getValue().equals(result)) {
                    vo.setSuccessCountDelete(vo.getSuccessCountDelete() + count);
                    vo.setDurationDelete(durationAvg);
                } else if (ItemResultEnum.Fail.getValue().equals(result)) {
                    vo.setFailCountDelete(vo.getFailCountDelete() + count);
                    durationDeleteFail = durationAvg;
                } else {
                    vo.setCountDelete(vo.getCountDelete() + count);
                }
            }
        }
        getOutOrderItemTotalAftertreatment(vo, durationRegistFail, durationUpdateFail, durationDeleteFail);
        log.info("==========getOutOrderItemTotal整体耗时{}ms", System.currentTimeMillis() - l);
        return vo;
    }

    @Override
    public Page<OutOrderItemESModel> getOutOrderItemPage(Page page, StatisticsOutOrderItemReqVo vo) {
        List<String> lspIdList = getLspPermission();
        if (CollUtil.isEmpty(lspIdList)) {
            return page;
        }
        long l = System.currentTimeMillis();
        long startCount = (page.getCurrent() - 1) * page.getSize();
        SearchRequest searchRequest;
        searchRequest = new SearchRequest(outOrderItem);
        BoolQueryBuilder boolQuery = QueryBuilders.boolQuery();
        Long bmsSpChannelId = vo.getBmsSpChannelId();
        if (null != bmsSpChannelId) {
            boolQuery.must(QueryBuilders.matchQuery("bmsSpChannelId", bmsSpChannelId));
            vo.setBmsSpChannelId(bmsSpChannelId);
        }
        Long spId = vo.getSpId();
        if (null != spId) {
            List<String> lspIds = sysSpMapper.selectLspIdListBySp(spId);
            if (CollUtil.isNotEmpty(lspIdList)) {
                lspIdList.retainAll(lspIds);
            } else {
                lspIdList.addAll(lspIds);
            }
            lspIdList = lspIdList.parallelStream().filter(Objects::nonNull).collect(Collectors.toList());
            if (CollUtil.isEmpty(lspIdList)) {
                return page;
            }
            vo.setSpId(spId);
        }
        boolQuery.must(QueryBuilders.termsQuery("lspId", lspIdList));
        Long outPassageId = vo.getOutPassageId();
        if (null != outPassageId) {
            SysOutPassage sysOutPassage = sysOutPassageMapper.selectById(outPassageId);
            boolQuery.must(QueryBuilders.matchQuery("tableName", "out_order_item_" + sysOutPassage.getCode()));
            vo.setOutPassageId(outPassageId);
        }
        Integer result = vo.getResult();
        if (null != result) {
            boolQuery.must(QueryBuilders.matchQuery("result", result));
            vo.setResult(result);
        }
        Integer action = vo.getAction();
        if (null != action) {
            boolQuery.must(QueryBuilders.matchQuery("action", action));
            vo.setAction(action);
        }
        String startTime = vo.getStartTime();
        String endTime = vo.getEndTime();
        if (StringUtils.isNotEmpty(startTime) && StringUtils.isNotEmpty(endTime)) {
            String startTimeFormat = DateUtil.format(DateUtil.beginOfDay(DateUtil.parse(startTime, DateUtils.YYYY_MM_DD)), DateUtils.YYYY_MM_DD_HH_MM_SS);
            String endTimeFormat = DateUtil.format(DateUtil.endOfDay(DateUtil.parse(endTime, DateUtils.YYYY_MM_DD)), DateUtils.YYYY_MM_DD_HH_MM_SS);
            boolQuery.must(QueryBuilders.rangeQuery("createTime").gte(startTimeFormat).lte(endTimeFormat));
        }
        SearchSourceBuilder searchSourceBuilder = new SearchSourceBuilder();
        searchSourceBuilder.query(boolQuery);
        searchSourceBuilder.sort(new FieldSortBuilder("createTime").unmappedType("date").order(SortOrder.DESC));
        searchSourceBuilder.from(Integer.parseInt(String.valueOf(startCount)));
        searchSourceBuilder.size(Integer.parseInt(String.valueOf(page.getSize())));
        searchSourceBuilder.trackTotalHits(true);
        searchRequest.source(searchSourceBuilder);
        log.info("ES搜索语句={}", searchRequest.source().toString());
        SearchResponse searchResponse = search(searchRequest);
        TotalHits totalHits = searchResponse.getHits().getTotalHits();
        long total = totalHits.value;
        SearchHit[] hits = searchResponse.getHits().getHits();
        List<OutOrderItemESModel> list = new ArrayList<>();
        for (SearchHit hit : hits) {
            OutOrderItemESModel t = objectMapper.convertValue(hit.getSourceAsMap(), OutOrderItemESModel.class);
            list.add(t);
        }
        page.setTotal(total);
        page.setRecords(list);
        log.info("==========getOutOrderItemPage整体耗时{}ms", System.currentTimeMillis() - l);
        return page;
    }

    @Override
    public StatisticsOnlineRespVo getOnlineContentTotal(StatisticsOnlineReqVo statisticsOnlineReqVo, Integer contentType) {
        if (StatisticsMediaBindStatusEnum.UNBIND.getValue().equals(statisticsOnlineReqVo.getMediaBindStatus())) {
            statisticsOnlineReqVo.setMediaBindStatus(StatisticsMediaBindStatusEnum.ALL.getValue());
            StatisticsOnlineRespVo voAll = getOnlineContentCommon(bmsContent, contentType, statisticsOnlineReqVo, true, null);
            statisticsOnlineReqVo.setMediaBindStatus(StatisticsMediaBindStatusEnum.BIND.getValue());
            StatisticsOnlineRespVo voBind = getOnlineContentCommon(bmsContent, contentType, statisticsOnlineReqVo, true, null);
            voAll.setMediaBindStatus(StatisticsMediaBindStatusEnum.UNBIND.getValue());
            voAll.setDurationProgram(voAll.getDurationProgram() - voBind.getDurationProgram());
            voAll.setCountProgram(voAll.getCountProgram() - voBind.getCountProgram());
            voAll.setFileSizeProgram(voAll.getFileSizeProgram() - voBind.getFileSizeProgram());
            voAll.setCountSeries(voAll.getCountSeries() - voBind.getCountSeries());
            statisticsOnlineReqVo.setMediaBindStatus(StatisticsMediaBindStatusEnum.UNBIND.getValue());
            return voAll;
        } else {
            return getOnlineContentCommon(bmsContent, contentType, statisticsOnlineReqVo, true, null);
        }
    }

    @Override
    public StatisticsOnlineRespVo getOnlineSubsetTotal(StatisticsOnlineRespVo statisticsOnlineRespVo, StatisticsOnlineReqVo statisticsOnlineReqVo) {
        if (StatisticsMediaBindStatusEnum.UNBIND.getValue().equals(statisticsOnlineReqVo.getMediaBindStatus())) {
            statisticsOnlineReqVo.setMediaBindStatus(StatisticsMediaBindStatusEnum.ALL.getValue());
            StatisticsOnlineRespVo voAll = getOnlineSubsetCommon(bmsProgram, statisticsOnlineReqVo, statisticsOnlineRespVo, true, null, false);
            StatisticsOnlineRespVo voAllCopy = new StatisticsOnlineRespVo();
            BeanUtil.copyProperties(voAll, voAllCopy);
            statisticsOnlineReqVo.setMediaBindStatus(StatisticsMediaBindStatusEnum.BIND.getValue());
            StatisticsOnlineRespVo voBind = getOnlineSubsetCommon(bmsProgram, statisticsOnlineReqVo, statisticsOnlineRespVo, true, null, false);
            voAll.setMediaBindStatus(StatisticsMediaBindStatusEnum.UNBIND.getValue());
            voAll.setDurationSubset(voAllCopy.getDurationSubset() - voBind.getDurationSubset());
            voAll.setCountSubset(voAllCopy.getCountSubset() - voBind.getCountSubset());
            voAll.setFileSizeSubset(voAllCopy.getFileSizeSubset() - voBind.getFileSizeSubset());
            return voAll;
        } else {
            return getOnlineSubsetCommon(bmsProgram, statisticsOnlineReqVo, statisticsOnlineRespVo, true, null, false);
        }
    }

    @Override
    public StatisticsOnlineRespVo getNewOnlineContentTotal(StatisticsOnlineReqVo statisticsOnlineReqVo, Integer contentType, Boolean flag) {
        return getNewOnlineContentCommon(bmsContent, contentType, statisticsOnlineReqVo, true, null, flag);
    }

    @Override
    public List<BmsContentESModel> getNewOnlineContentList(StatisticsOnlineReqVo statisticsOnlineReqVo, Integer contentType) {
        List<BmsContentESModel> vos = new ArrayList<>();
        long l = System.currentTimeMillis();
        HashMap<String, List<Long>> permissionMap = getPermission();
        List<Long> spIdList = permissionMap.get("spIdList");
        List<Long> cpIdList = permissionMap.get("cpIdList");
        SearchRequest searchRequest = new SearchRequest(bmsContent);
        BoolQueryBuilder boolQuery = QueryBuilders.boolQuery();
        Long bmsSpChannelId = statisticsOnlineReqVo.getBmsSpChannelId();
        Long spId = statisticsOnlineReqVo.getSpId();
        Long cpId = statisticsOnlineReqVo.getCpId();
        Integer definitionFlag = statisticsOnlineReqVo.getDefinitionFlag();
        Integer mediaBindStatus = statisticsOnlineReqVo.getMediaBindStatus();
        String startTime = statisticsOnlineReqVo.getStartTime();
        String endTime = statisticsOnlineReqVo.getEndTime();
        StatisticsOnlineRespVo vo = new StatisticsOnlineRespVo();
        ArrayList<Integer> contentTypes = new ArrayList<>();
        if (ContentTypeEnum.TELEPLAY.getValue().equals(contentType)) {
            contentTypes.add(ContentTypeEnum.TELEPLAY.getValue());
            contentTypes.add(ContentTypeEnum.EPISODES.getValue());
            boolQuery.must(QueryBuilders.termsQuery("contentType", contentTypes));
        } else if (ContentTypeEnum.FILM.getValue().equals(contentType)) {
            boolQuery.must(QueryBuilders.termQuery("contentType", contentType));
        }
        if (StringUtils.isNotEmpty(startTime) && StringUtils.isNotEmpty(endTime)) {
            String startTimeFormat = DateUtil.format(DateUtil.beginOfDay(DateUtil.parse(startTime, DateUtils.YYYY_MM_DD)), DateUtils.YYYY_MM_DD_HH_MM_SS);
            String endTimeFormat = DateUtil.format(DateUtil.endOfDay(DateUtil.parse(endTime, DateUtils.YYYY_MM_DD)), DateUtils.YYYY_MM_DD_HH_MM_SS);
            if (StatisticsMediaBindStatusEnum.ALL.getValue().equals(mediaBindStatus)) {
                boolQuery.must(QueryBuilders.rangeQuery("publishTime").gte(startTimeFormat).lte(endTimeFormat));
            } else if (StatisticsMediaBindStatusEnum.BIND.getValue().equals(mediaBindStatus)) {
                boolQuery.must(QueryBuilders.rangeQuery("relationPublishTime").gte(startTimeFormat).lte(endTimeFormat));
                boolQuery.must(QueryBuilders.termQuery("bindStatus", StatisticsMediaBindStatusEnum.BIND.getValue()));
            } else if (StatisticsMediaBindStatusEnum.UNBIND.getValue().equals(mediaBindStatus)) {
                boolQuery.must(QueryBuilders.rangeQuery("publishTime").gte(startTimeFormat).lte(endTimeFormat));
            } else {
                log.error("错误的绑定状态bindStatus={}", mediaBindStatus);
            }
        } else {
            if (StatisticsMediaBindStatusEnum.BIND.getValue().equals(mediaBindStatus)) {
                boolQuery.must(QueryBuilders.termQuery("bindStatus", StatisticsMediaBindStatusEnum.BIND.getValue()));
            }
        }
        TermsAggregationBuilder termsAggregationBuilder =
                AggregationBuilders.terms("bms_content_group").field("cmsContentCode").size(Integer.MAX_VALUE);
        if (null != bmsSpChannelId) {
            boolQuery.must(QueryBuilders.matchQuery("bmsSpChannelId", bmsSpChannelId));
            vo.setBmsSpChannelId(bmsSpChannelId);
            List<SysSp> sysSps = sysSpMapper.selectList(new LambdaQueryWrapper<SysSp>().eq(SysSp::getBmsSpChannelId, bmsSpChannelId));
            List<Long> spIdChannelList = sysSps.stream().map(SysSp::getId).collect(Collectors.toList());
            spIdList.retainAll(spIdChannelList);
        }
        if (null != spId) {
            boolQuery.must(QueryBuilders.termQuery("spId", spId));
            vo.setSpId(spId);
        } else {
            boolQuery.must(QueryBuilders.termsQuery("spId", spIdList));
        }
        if (null != cpId) {
            boolQuery.must(QueryBuilders.termQuery("cpId", cpId));
            vo.setCpId(cpId);
        } else {
            boolQuery.must(QueryBuilders.termsQuery("cpId", cpIdList));
        }
        if (null != definitionFlag) {
            boolQuery.must(QueryBuilders.termQuery("definitionFlag", definitionFlag));
            vo.setDefinitionFlag(definitionFlag);
        }
        //限制发布状态
        ArrayList<Integer> integers = new ArrayList<>();
        integers.add(PublishStatusEnum.PUBLISH.getCode());
        integers.add(PublishStatusEnum.WAITUPDATE.getCode());
        integers.add(PublishStatusEnum.UPDATING.getCode());
        integers.add(PublishStatusEnum.FAILUPDATE.getCode());
        boolQuery.must(QueryBuilders.termsQuery("publishStatus", integers));
        vo.setMediaBindStatus(mediaBindStatus);
        boolQuery.must(QueryBuilders.termQuery("statusFlag", BmsContentStatusFlagEnum.NEW.getCode()));
        SearchSourceBuilder searchSourceBuilder = new SearchSourceBuilder();
        searchSourceBuilder.collapse(new CollapseBuilder("cmsContentCode"));
        if (StatisticsMediaBindStatusEnum.ALL.getValue().equals(mediaBindStatus)) {
            searchSourceBuilder.sort("publishTime");
        } else if (StatisticsMediaBindStatusEnum.BIND.getValue().equals(mediaBindStatus)) {
            searchSourceBuilder.sort("relationPublishTime");
        }
        if (StatisticsMediaBindStatusEnum.UNBIND.getValue().equals(statisticsOnlineReqVo.getMediaBindStatus())) {
            termsAggregationBuilder.subAggregation(AggregationBuilders.min("min_bindStatus").field("bindStatus"));
            HashMap<String, Object> map = new HashMap<>(2);
            map.put("v0", "INTEGER");
            map.put("v1", 2);
            String scriptString = "InternalQlScriptUtils.nullSafeFilter(InternalQlScriptUtils.gt(InternalQlScriptUtils.nullSafeCastNumeric(params.minBindStatus,params.v0),params.v1))";
            Script script = new Script(ScriptType.INLINE, "painless", scriptString, map);
            Map<String, String> bucketsPathsMap = new HashMap<>(2);
            bucketsPathsMap.put("minBindStatus", "min_bindStatus");
            BucketSelectorPipelineAggregationBuilder bs = PipelineAggregatorBuilders.bucketSelector("having", bucketsPathsMap, script);
            termsAggregationBuilder.subAggregation(bs);
            AggregationBuilder top = AggregationBuilders.topHits("cmsContentCode").size(1);
            termsAggregationBuilder.subAggregation(top);
        }
        searchSourceBuilder.query(boolQuery);
        searchSourceBuilder.aggregation(termsAggregationBuilder);
        searchSourceBuilder.from(0);
        searchSourceBuilder.size(size);
        searchRequest.source(searchSourceBuilder);
        SearchResponse searchResponse = search(searchRequest);
        log.info("ES搜索语句={}", searchRequest.source().toString());
        if (StatisticsMediaBindStatusEnum.UNBIND.getValue().equals(statisticsOnlineReqVo.getMediaBindStatus())) {
            Aggregations aggregations = searchResponse.getAggregations();
            Terms bms_content_group = aggregations.get("bms_content_group");
            List<? extends Terms.Bucket> buckets = bms_content_group.getBuckets();
            for (Terms.Bucket bucket : buckets) {
                Aggregations aggregations1 = bucket.getAggregations();
                ParsedTopHits cmsContentCode = aggregations1.get("cmsContentCode");
                SearchHits hits1 = cmsContentCode.getHits();
                BmsContentESModel t = objectMapper.convertValue(hits1.getHits()[0].getSourceAsMap(), BmsContentESModel.class);
                vos.add(t);
            }
        } else {
            SearchHit[] hits = searchResponse.getHits().getHits();
            for (SearchHit hit : hits) {
                BmsContentESModel t = objectMapper.convertValue(hit.getSourceAsMap(), BmsContentESModel.class);
                vos.add(t);
            }
        }
        log.info("==========getNewOnlineContentList整体耗时{}ms", System.currentTimeMillis() - l);
        return vos;
    }

    @Override
    public List<BmsProgramESModel> getNewOnlineSubsetList(StatisticsOnlineReqVo statisticsOnlineReqVo) {
        List<BmsProgramESModel> vos = new ArrayList<>();
        long l = System.currentTimeMillis();
        HashMap<String, List<Long>> permissionMap = getPermission();
        List<Long> spIdList = permissionMap.get("spIdList");
        List<Long> cpIdList = permissionMap.get("cpIdList");
        SearchRequest searchRequest = new SearchRequest(bmsProgram);
        BoolQueryBuilder boolQuery = QueryBuilders.boolQuery();
        Long bmsSpChannelId = statisticsOnlineReqVo.getBmsSpChannelId();
        Long spId = statisticsOnlineReqVo.getSpId();
        Long cpId = statisticsOnlineReqVo.getCpId();
        Integer definitionFlag = statisticsOnlineReqVo.getDefinitionFlag();
        Integer mediaBindStatus = statisticsOnlineReqVo.getMediaBindStatus();
        String startTime = statisticsOnlineReqVo.getStartTime();
        String endTime = statisticsOnlineReqVo.getEndTime();
        StatisticsOnlineRespVo vo = new StatisticsOnlineRespVo();
        if (StringUtils.isNotEmpty(startTime) && StringUtils.isNotEmpty(endTime)) {
            String startTimeFormat = DateUtil.format(DateUtil.beginOfDay(DateUtil.parse(startTime, DateUtils.YYYY_MM_DD)), DateUtils.YYYY_MM_DD_HH_MM_SS);
            String endTimeFormat = DateUtil.format(DateUtil.endOfDay(DateUtil.parse(endTime, DateUtils.YYYY_MM_DD)), DateUtils.YYYY_MM_DD_HH_MM_SS);
            if (StatisticsMediaBindStatusEnum.ALL.getValue().equals(mediaBindStatus)) {
                boolQuery.must(QueryBuilders.rangeQuery("publishTime").gte(startTimeFormat).lte(endTimeFormat));
            } else if (StatisticsMediaBindStatusEnum.BIND.getValue().equals(mediaBindStatus)) {
                boolQuery.must(QueryBuilders.rangeQuery("relationPublishTime").gte(startTimeFormat).lte(endTimeFormat));
                boolQuery.must(QueryBuilders.termQuery("bindStatus", StatisticsMediaBindStatusEnum.BIND.getValue()));
            } else if (StatisticsMediaBindStatusEnum.UNBIND.getValue().equals(mediaBindStatus)) {
                boolQuery.must(QueryBuilders.rangeQuery("publishTime").gte(startTimeFormat).lte(endTimeFormat));
//                boolQuery.must(QueryBuilders.termQuery("bindStatus", StatisticsMediaBindStatusEnum.UNBIND.getValue()));
            } else {
                log.error("错误的绑定状态bindStatus={}", mediaBindStatus);
            }
        } else {
            if (StatisticsMediaBindStatusEnum.ALL.getValue().equals(mediaBindStatus)) {
                log.info("单节目上线无需考虑绑定关系");
            } else if (StatisticsMediaBindStatusEnum.BIND.getValue().equals(mediaBindStatus)) {
                boolQuery.must(QueryBuilders.termQuery("bindStatus", StatisticsMediaBindStatusEnum.BIND.getValue()));
            } else if (StatisticsMediaBindStatusEnum.UNBIND.getValue().equals(mediaBindStatus)) {
//                boolQuery.must(QueryBuilders.termQuery("bindStatus", StatisticsMediaBindStatusEnum.UNBIND.getValue()));
            } else {
                log.error("错误的绑定状态bindStatus={}", mediaBindStatus);
            }
        }
        if (null != bmsSpChannelId) {
            boolQuery.must(QueryBuilders.matchQuery("bmsSpChannelId", bmsSpChannelId));
            vo.setBmsSpChannelId(bmsSpChannelId);
            List<SysSp> sysSps = sysSpMapper.selectList(new LambdaQueryWrapper<SysSp>().eq(SysSp::getBmsSpChannelId, bmsSpChannelId));
            List<Long> spIdChannelList = sysSps.stream().map(SysSp::getId).collect(Collectors.toList());
            spIdList.retainAll(spIdChannelList);
        }
        if (null != spId) {
            boolQuery.must(QueryBuilders.termQuery("spId", spId));
            vo.setSpId(spId);
        } else {
            boolQuery.must(QueryBuilders.termsQuery("spId", spIdList));
        }
        if (null != cpId) {
            boolQuery.must(QueryBuilders.termQuery("cpId", cpId));
            vo.setCpId(cpId);
        } else {
            boolQuery.must(QueryBuilders.termsQuery("cpId", cpIdList));
        }
        if (null != definitionFlag) {
            boolQuery.must(QueryBuilders.termQuery("definitionFlag", definitionFlag));
            vo.setDefinitionFlag(definitionFlag);
        }
        //限制发布状态
        ArrayList<Integer> integers = new ArrayList<>();
        integers.add(PublishStatusEnum.PUBLISH.getCode());
        integers.add(PublishStatusEnum.WAITUPDATE.getCode());
        integers.add(PublishStatusEnum.UPDATING.getCode());
        integers.add(PublishStatusEnum.FAILUPDATE.getCode());
        boolQuery.must(QueryBuilders.termsQuery("publishStatus", integers));
        vo.setMediaBindStatus(mediaBindStatus);
        boolQuery.must(QueryBuilders.termQuery("statusFlag", BmsContentStatusFlagEnum.NEW.getCode()));
        SearchSourceBuilder searchSourceBuilder = new SearchSourceBuilder();
        searchSourceBuilder.collapse(new CollapseBuilder("cmsContentCode"));
        if (StatisticsMediaBindStatusEnum.ALL.getValue().equals(mediaBindStatus)) {
            searchSourceBuilder.sort("publishTime");
        } else if (StatisticsMediaBindStatusEnum.BIND.getValue().equals(mediaBindStatus)) {
            searchSourceBuilder.sort("relationPublishTime");
        }

        TermsAggregationBuilder termsAggregationBuilder = AggregationBuilders.terms("bms_program_group").field("cmsContentCode").size(Integer.MAX_VALUE);
        if (StatisticsMediaBindStatusEnum.UNBIND.getValue().equals(statisticsOnlineReqVo.getMediaBindStatus())) {
            termsAggregationBuilder.subAggregation(AggregationBuilders.min("min_bindStatus").field("bindStatus"));
            HashMap<String, Object> map = new HashMap<>(2);
            map.put("v0", "INTEGER");
            map.put("v1", 2);
            String scriptString = "InternalQlScriptUtils.nullSafeFilter(InternalQlScriptUtils.gt(InternalQlScriptUtils.nullSafeCastNumeric(params.minBindStatus,params.v0),params.v1))";
            Script script = new Script(ScriptType.INLINE, "painless", scriptString, map);
            Map<String, String> bucketsPathsMap = new HashMap<>(2);
            bucketsPathsMap.put("minBindStatus", "min_bindStatus");
            BucketSelectorPipelineAggregationBuilder bs = PipelineAggregatorBuilders.bucketSelector("having", bucketsPathsMap, script);
            termsAggregationBuilder.subAggregation(bs);
            AggregationBuilder top = AggregationBuilders.topHits("cmsContentCode").size(1);
            termsAggregationBuilder.subAggregation(top);
        }
        searchSourceBuilder.query(boolQuery);
        searchSourceBuilder.aggregation(termsAggregationBuilder);
        searchSourceBuilder.from(0);
        searchSourceBuilder.size(size);
        searchRequest.source(searchSourceBuilder);
        SearchResponse searchResponse = search(searchRequest);
        log.info("ES搜索语句={}", searchRequest.source().toString());
        if (StatisticsMediaBindStatusEnum.UNBIND.getValue().equals(statisticsOnlineReqVo.getMediaBindStatus())) {
            Aggregations aggregations = searchResponse.getAggregations();
            Terms bms_content_group = aggregations.get("bms_program_group");
            List<? extends Terms.Bucket> buckets = bms_content_group.getBuckets();
            for (Terms.Bucket bucket : buckets) {
                Aggregations aggregations1 = bucket.getAggregations();
                ParsedTopHits cmsContentCode = aggregations1.get("cmsContentCode");
                SearchHits hits1 = cmsContentCode.getHits();
                BmsProgramESModel t = objectMapper.convertValue(hits1.getHits()[0].getSourceAsMap(), BmsProgramESModel.class);
                vos.add(t);
            }
        } else {
            SearchHit[] hits = searchResponse.getHits().getHits();
            for (SearchHit hit : hits) {
                BmsProgramESModel t = objectMapper.convertValue(hit.getSourceAsMap(), BmsProgramESModel.class);
                vos.add(t);
            }
        }
        log.info("==========getNewOnlineSubsetList整体耗时{}ms", System.currentTimeMillis() - l);
        return vos;
    }

    @Override
    public StatisticsOnlineRespVo getNewOnlineSubsetTotal(StatisticsOnlineRespVo statisticsOnlineRespVo, StatisticsOnlineReqVo statisticsOnlineReqVo, Boolean flag) {
        return getNewOnlineSubsetCommon(bmsProgram, statisticsOnlineReqVo, statisticsOnlineRespVo, true, null, flag);
    }

    @Override
    public StatisticsOnlineRespVo getNewOnlineContentTotalAsync(StatisticsOnlineReqVo vo, Integer contentType, HashMap<String, List<Long>> permissionMap, Boolean flag) {
        return getNewOnlineContentCommon(bmsContent, contentType, vo, true, permissionMap, flag);
    }

    @Override
    public StatisticsOnlineRespVo getNewOnlineSubsetTotalAsync(StatisticsOnlineRespVo onlineContentTotal, StatisticsOnlineReqVo vo, HashMap<String, List<Long>> permissionMap, Boolean flag) {
        return getNewOnlineSubsetCommon(bmsProgram, vo, onlineContentTotal, true, permissionMap, flag);
    }

    @Override
    public StatisticsOnlineRespVo getNewOfflineContentTotal(StatisticsOnlineReqVo statisticsOnlineReqVo, Integer contentType) {
        long l = System.currentTimeMillis();
        HashMap<String, List<Long>> permissionMap = getPermission();
        List<Long> spIdList = permissionMap.get("spIdList");
        List<Long> cpIdList = permissionMap.get("cpIdList");
        SearchRequest searchRequest = new SearchRequest(bmsContent);
        BoolQueryBuilder boolQuery = QueryBuilders.boolQuery();
        Long bmsSpChannelId = statisticsOnlineReqVo.getBmsSpChannelId();
        Long spId = statisticsOnlineReqVo.getSpId();
        Long cpId = statisticsOnlineReqVo.getCpId();
        Integer definitionFlag = statisticsOnlineReqVo.getDefinitionFlag();
        String startTime = statisticsOnlineReqVo.getStartTime();
        String endTime = statisticsOnlineReqVo.getEndTime();
        StatisticsOnlineRespVo vo = new StatisticsOnlineRespVo();
        SearchSourceBuilder searchSourceBuilder = new SearchSourceBuilder();
        //根据多字段进行分组查询
        boolQuery.must(QueryBuilders.termQuery("contentType", contentType));
        if (StringUtils.isNotEmpty(startTime) && StringUtils.isNotEmpty(endTime)) {
            String startTimeFormat = DateUtil.format(DateUtil.beginOfDay(DateUtil.parse(startTime, DateUtils.YYYY_MM_DD)), DateUtils.YYYY_MM_DD_HH_MM_SS);
            String endTimeFormat = DateUtil.format(DateUtil.endOfDay(DateUtil.parse(endTime, DateUtils.YYYY_MM_DD)), DateUtils.YYYY_MM_DD_HH_MM_SS);
            boolQuery.must(QueryBuilders.rangeQuery("publishTime").gte(startTimeFormat).lte(endTimeFormat));
        }
        if (null != bmsSpChannelId) {
            boolQuery.must(QueryBuilders.matchQuery("bmsSpChannelId", bmsSpChannelId));
            vo.setBmsSpChannelId(bmsSpChannelId);
            List<SysSp> sysSps = sysSpMapper.selectList(new LambdaQueryWrapper<SysSp>().eq(SysSp::getBmsSpChannelId, bmsSpChannelId));
            List<Long> spIdChannelList = sysSps.stream().map(SysSp::getId).collect(Collectors.toList());
            spIdList.retainAll(spIdChannelList);
        }
        TermsAggregationBuilder size = AggregationBuilders.terms("bms_content_group").field("cmsContentCode").size(Integer.MAX_VALUE);
        size.subAggregation(AggregationBuilders.max("durationSum").field("duration"));
        size.subAggregation(AggregationBuilders.max("fileSizeSum").field("fileSize"));
        searchSourceBuilder.aggregation(size);
        //计算介质时长及文件大小
        searchSourceBuilder.aggregation(PipelineAggregatorBuilders.statsBucket("stats_durationSum", "bms_content_group.durationSum"));
        searchSourceBuilder.aggregation(PipelineAggregatorBuilders.statsBucket("stats_fileSizeSum", "bms_content_group.fileSizeSum"));
        searchSourceBuilder.aggregation(PipelineAggregatorBuilders.statsBucket("stats_count", "bms_content_group._count"));
        if (null != spId) {
            boolQuery.must(QueryBuilders.termQuery("spId", spId));
            vo.setSpId(spId);
        } else {
            //针对某个渠道下全部SP，则按SP去重统计。例如，同一个节目在移动中兴和移动华为平台均已上线，在移动侧统计时，算一个节目上线
            boolQuery.must(QueryBuilders.termsQuery("spId", spIdList));
        }
        if (null != cpId) {
            boolQuery.must(QueryBuilders.termQuery("cpId", cpId));
            vo.setCpId(cpId);
        } else {
            boolQuery.must(QueryBuilders.termsQuery("cpId", cpIdList));
        }
        if (null != definitionFlag) {
            boolQuery.must(QueryBuilders.termQuery("definitionFlag", definitionFlag));
            vo.setDefinitionFlag(definitionFlag);
        }
        ArrayList<Integer> integers = new ArrayList<>();
        integers.add(PublishStatusEnum.ROLLBACK.getCode());
        boolQuery.must(QueryBuilders.termsQuery("publishStatus", integers));
        searchSourceBuilder.query(boolQuery);
        searchSourceBuilder.size(0);
        searchRequest.source(searchSourceBuilder);
        SearchResponse searchResponse = search(searchRequest);
        log.info("ES搜索语句={}", searchRequest.source().toString());
        Aggregations aggregations = searchResponse.getAggregations();
        long count;
        long durationSum;
        long fileSizeSum;
        ParsedStatsBucket statsDurationSum = aggregations.get("stats_durationSum");
        ParsedStatsBucket statsFileSizeSum = aggregations.get("stats_fileSizeSum");
        ParsedStatsBucket statsCount = aggregations.get("stats_count");
        count = statsCount.getCount();
        durationSum = (long) statsDurationSum.getSum();
        fileSizeSum = (long) statsFileSizeSum.getSum();
        if (ContentTypeEnum.FILM.getValue().equals(contentType)) {
            vo.setCountProgram(count);
            vo.setDurationProgram(durationSum);
            vo.setFileSizeProgram(fileSizeSum);
        } else if (ContentTypeEnum.TELEPLAY.getValue().equals(contentType)) {
            vo.setCountSeries(count);
        } else if (ContentTypeEnum.EPISODES.getValue().equals(contentType)) {
            vo.setCountSeries(count);
        }
        log.info("==========getNewOfflineContentTotal整体耗时{}ms", System.currentTimeMillis() - l);
        return vo;
    }

    @Override
    public StatisticsOnlineRespVo getNewOfflineSubsetTotal(StatisticsOnlineRespVo vo, StatisticsOnlineReqVo statisticsOnlineReqVo) {
        long l = System.currentTimeMillis();
        HashMap<String, List<Long>> permissionMap = getPermission();
        List<Long> spIdList = permissionMap.get("spIdList");
        List<Long> cpIdList = permissionMap.get("cpIdList");
        SearchRequest searchRequest = new SearchRequest(bmsProgram);
        SearchSourceBuilder searchSourceBuilder = new SearchSourceBuilder();
        BoolQueryBuilder boolQuery = QueryBuilders.boolQuery();
        Long bmsSpChannelId = statisticsOnlineReqVo.getBmsSpChannelId();
        Long spId = statisticsOnlineReqVo.getSpId();
        Long cpId = statisticsOnlineReqVo.getCpId();
        Integer definitionFlag = statisticsOnlineReqVo.getDefinitionFlag();
        String startTime = statisticsOnlineReqVo.getStartTime();
        String endTime = statisticsOnlineReqVo.getEndTime();
        if (StringUtils.isNotEmpty(startTime) && StringUtils.isNotEmpty(endTime)) {
            String startTimeFormat = DateUtil.format(DateUtil.beginOfDay(DateUtil.parse(startTime, DateUtils.YYYY_MM_DD)), DateUtils.YYYY_MM_DD_HH_MM_SS);
            String endTimeFormat = DateUtil.format(DateUtil.endOfDay(DateUtil.parse(endTime, DateUtils.YYYY_MM_DD)), DateUtils.YYYY_MM_DD_HH_MM_SS);
            boolQuery.must(QueryBuilders.rangeQuery("publishTime").gte(startTimeFormat).lte(endTimeFormat));
        }
        if (null != bmsSpChannelId) {
            boolQuery.must(QueryBuilders.matchQuery("bmsSpChannelId", bmsSpChannelId));
            vo.setBmsSpChannelId(bmsSpChannelId);
            List<SysSp> sysSps = sysSpMapper.selectList(new LambdaQueryWrapper<SysSp>().eq(SysSp::getBmsSpChannelId, bmsSpChannelId));
            List<Long> spIdChannelList = sysSps.stream().map(SysSp::getId).collect(Collectors.toList());
            spIdList.retainAll(spIdChannelList);
        }
        TermsAggregationBuilder size = AggregationBuilders.terms("bms_content_group").field("cmsContentCode").size(Integer.MAX_VALUE);
        size.subAggregation(AggregationBuilders.max("durationSum").field("duration"));
        size.subAggregation(AggregationBuilders.max("fileSizeSum").field("fileSize"));
        searchSourceBuilder.aggregation(size);
        //计算介质时长及文件大小
        searchSourceBuilder.aggregation(PipelineAggregatorBuilders.statsBucket("stats_durationSum", "bms_content_group.durationSum"));
        searchSourceBuilder.aggregation(PipelineAggregatorBuilders.statsBucket("stats_fileSizeSum", "bms_content_group.fileSizeSum"));
        searchSourceBuilder.aggregation(PipelineAggregatorBuilders.statsBucket("stats_count", "bms_content_group._count"));
        if (null != spId) {
            boolQuery.must(QueryBuilders.termQuery("spId", spId));
            vo.setSpId(spId);
        } else {
            //针对某个渠道下全部SP，则按SP去重统计。例如，同一个节目在移动中兴和移动华为平台均已上线，在移动侧统计时，算一个节目上线
            boolQuery.must(QueryBuilders.termsQuery("spId", spIdList));
        }
        if (null != cpId) {
            boolQuery.must(QueryBuilders.termQuery("cpId", cpId));
            vo.setCpId(cpId);
        } else {
            boolQuery.must(QueryBuilders.termsQuery("cpId", cpIdList));
        }
        if (null != definitionFlag) {
            boolQuery.must(QueryBuilders.termQuery("definitionFlag", definitionFlag));
            vo.setDefinitionFlag(definitionFlag);
        }
        ArrayList<Integer> integers = new ArrayList<>();
        integers.add(PublishStatusEnum.ROLLBACK.getCode());
        boolQuery.must(QueryBuilders.termsQuery("publishStatus", integers));
        searchSourceBuilder.query(boolQuery);
        searchSourceBuilder.size(0);
        searchRequest.source(searchSourceBuilder);
        SearchResponse searchResponse = search(searchRequest);
        log.info("ES搜索语句={}", searchRequest.source().toString());
        Aggregations aggregations = searchResponse.getAggregations();
        long count;
        long durationSum;
        long fileSizeSum;
        ParsedStatsBucket statsDurationSum = aggregations.get("stats_durationSum");
        ParsedStatsBucket statsFileSizeSum = aggregations.get("stats_fileSizeSum");
        ParsedStatsBucket statsCount = aggregations.get("stats_count");
        count = statsCount.getCount();
        durationSum = (long) statsDurationSum.getSum();
        fileSizeSum = (long) statsFileSizeSum.getSum();
        vo.setCountSubset(count);
        vo.setDurationSubset(durationSum);
        vo.setFileSizeSubset(fileSizeSum);
        log.info("==========getNewOfflineSubsetTotal整体耗时{}ms", System.currentTimeMillis() - l);
        return vo;
    }

    @Override
    public StatisticsOnlineRespVo getNewOfflineContentTotalAsync(StatisticsOnlineReqVo statisticsOnlineReqVo, HashMap<String, List<Long>> permissionMap) {
        long l = System.currentTimeMillis();
        List<Long> spIdList = permissionMap.get("spIdList");
        List<Long> cpIdList = permissionMap.get("cpIdList");
        SearchRequest searchRequest = new SearchRequest(bmsContent);
        BoolQueryBuilder boolQuery = QueryBuilders.boolQuery();
        Long bmsSpChannelId = statisticsOnlineReqVo.getBmsSpChannelId();
        Long spId = statisticsOnlineReqVo.getSpId();
        Long cpId = statisticsOnlineReqVo.getCpId();
        Integer definitionFlag = statisticsOnlineReqVo.getDefinitionFlag();
        String startTime = statisticsOnlineReqVo.getStartTime();
        String endTime = statisticsOnlineReqVo.getEndTime();
        StatisticsOnlineRespVo vo = new StatisticsOnlineRespVo();
        //根据多字段进行分组查询
        TermsAggregationBuilder aggBuilder = AggregationBuilders.terms("bms_content_group").field("contentType");
        if (StringUtils.isNotEmpty(startTime) && StringUtils.isNotEmpty(endTime)) {
            String startTimeFormat = DateUtil.format(DateUtil.beginOfDay(DateUtil.parse(startTime, DateUtils.YYYY_MM_DD)), DateUtils.YYYY_MM_DD_HH_MM_SS);
            String endTimeFormat = DateUtil.format(DateUtil.endOfDay(DateUtil.parse(endTime, DateUtils.YYYY_MM_DD)), DateUtils.YYYY_MM_DD_HH_MM_SS);
            boolQuery.must(QueryBuilders.rangeQuery("publishTime").gte(startTimeFormat).lte(endTimeFormat));
        }
        if (null != bmsSpChannelId) {
            boolQuery.must(QueryBuilders.matchQuery("bmsSpChannelId", bmsSpChannelId));
            vo.setBmsSpChannelId(bmsSpChannelId);
            List<SysSp> sysSps = sysSpMapper.selectList(new LambdaQueryWrapper<SysSp>().eq(SysSp::getBmsSpChannelId, bmsSpChannelId));
            List<Long> spIdChannelList = sysSps.stream().map(SysSp::getId).collect(Collectors.toList());
            spIdList.retainAll(spIdChannelList);
        }
        aggBuilder.subAggregation(AggregationBuilders.cardinality("cmsContentIdRepeat").field("cmsContentCode").precisionThreshold(40000L));
        if (null != spId) {
            boolQuery.must(QueryBuilders.termQuery("spId", spId));
            vo.setSpId(spId);
        } else {
            //针对某个渠道下全部SP，则按SP去重统计。例如，同一个节目在移动中兴和移动华为平台均已上线，在移动侧统计时，算一个节目上线
            boolQuery.must(QueryBuilders.termsQuery("spId", spIdList));
        }
        if (null != cpId) {
            boolQuery.must(QueryBuilders.termQuery("cpId", cpId));
            vo.setCpId(cpId);
        } else {
            boolQuery.must(QueryBuilders.termsQuery("cpId", cpIdList));
        }
        if (null != definitionFlag) {
            boolQuery.must(QueryBuilders.termQuery("definitionFlag", definitionFlag));
            vo.setDefinitionFlag(definitionFlag);
        }
        ArrayList<Integer> integers = new ArrayList<>();
        integers.add(PublishStatusEnum.ROLLBACK.getCode());
        boolQuery.must(QueryBuilders.termsQuery("publishStatus", integers));
        //计算介质时长及文件大小
        aggBuilder.subAggregation(AggregationBuilders.sum("durationSum").field("duration"));
        aggBuilder.subAggregation(AggregationBuilders.sum("fileSizeSum").field("fileSize"));
        SearchSourceBuilder searchSourceBuilder = new SearchSourceBuilder();
        searchSourceBuilder.aggregation(aggBuilder);
        searchSourceBuilder.query(boolQuery);
        searchSourceBuilder.size(0);
        searchRequest.source(searchSourceBuilder);
        SearchResponse searchResponse = search(searchRequest);
        log.info("ES搜索语句={}", searchRequest.source().toString());
        Aggregations aggregations = searchResponse.getAggregations();
        Terms outOrderItemGroup = aggregations.get("bms_content_group");
        for (Terms.Bucket bucket : outOrderItemGroup.getBuckets()) {
            //获取数量
            Long count;
            Cardinality cmsContentIdRepeat = bucket.getAggregations().get("cmsContentIdRepeat");
            count = Long.parseLong(String.valueOf(cmsContentIdRepeat.getValue()));
            Sum cmsResourceDurationSumSum = bucket.getAggregations().get("durationSum");
            Sum fileSizeSumSum = bucket.getAggregations().get("fileSizeSum");
            long durationSum = (long) cmsResourceDurationSumSum.getValue();
            long fileSizeSum = (long) fileSizeSumSum.getValue();
            //获取分组信息
            String contentTypeString = bucket.getKeyAsString();
            Integer contentType = Integer.parseInt(contentTypeString);
            if (ContentTypeEnum.FILM.getValue().equals(contentType)) {
                vo.setCountProgram(count);
                vo.setDurationProgram(durationSum);
                vo.setFileSizeProgram(fileSizeSum);
            } else if (ContentTypeEnum.TELEPLAY.getValue().equals(contentType)) {
                vo.setCountSeries(vo.getCountSeries() + count);
            } else if (ContentTypeEnum.EPISODES.getValue().equals(contentType)) {
                vo.setCountSeries(vo.getCountSeries() + count);
            }
        }
        log.info("==========getNewOfflineContentTotalAsync整体耗时{}ms", System.currentTimeMillis() - l);
        return vo;
    }

    @Override
    public StatisticsOnlineRespVo getNewOfflineSubsetTotalAsync(StatisticsOnlineRespVo vo, StatisticsOnlineReqVo statisticsOnlineReqVo, HashMap<String, List<Long>> permissionMap) {
        long l = System.currentTimeMillis();
        List<Long> spIdList = permissionMap.get("spIdList");
        List<Long> cpIdList = permissionMap.get("cpIdList");
        SearchRequest searchRequest = new SearchRequest(bmsProgram);
        BoolQueryBuilder boolQuery = QueryBuilders.boolQuery();
        Long bmsSpChannelId = statisticsOnlineReqVo.getBmsSpChannelId();
        Long spId = statisticsOnlineReqVo.getSpId();
        Long cpId = statisticsOnlineReqVo.getCpId();
        Integer definitionFlag = statisticsOnlineReqVo.getDefinitionFlag();
        String startTime = statisticsOnlineReqVo.getStartTime();
        String endTime = statisticsOnlineReqVo.getEndTime();
        TermsAggregationBuilder aggBuilder = AggregationBuilders.terms("bms_program_group").field("statusFlag");
        if (StringUtils.isNotEmpty(startTime) && StringUtils.isNotEmpty(endTime)) {
            String startTimeFormat = DateUtil.format(DateUtil.beginOfDay(DateUtil.parse(startTime, DateUtils.YYYY_MM_DD)), DateUtils.YYYY_MM_DD_HH_MM_SS);
            String endTimeFormat = DateUtil.format(DateUtil.endOfDay(DateUtil.parse(endTime, DateUtils.YYYY_MM_DD)), DateUtils.YYYY_MM_DD_HH_MM_SS);
            boolQuery.must(QueryBuilders.rangeQuery("publishTime").gte(startTimeFormat).lte(endTimeFormat));
        }
        if (null != bmsSpChannelId) {
            boolQuery.must(QueryBuilders.matchQuery("bmsSpChannelId", bmsSpChannelId));
            vo.setBmsSpChannelId(bmsSpChannelId);
            List<SysSp> sysSps = sysSpMapper.selectList(new LambdaQueryWrapper<SysSp>().eq(SysSp::getBmsSpChannelId, bmsSpChannelId));
            List<Long> spIdChannelList = sysSps.stream().map(SysSp::getId).collect(Collectors.toList());
            spIdList.retainAll(spIdChannelList);
        }
        aggBuilder.subAggregation(AggregationBuilders.cardinality("cmsContentIdRepeat").field("cmsContentCode").precisionThreshold(40000L));
        if (null != spId) {
            boolQuery.must(QueryBuilders.termQuery("spId", spId));
            vo.setSpId(spId);
        } else {
            //针对某个渠道下全部SP，则按SP去重统计。例如，同一个节目在移动中兴和移动华为平台均已上线，在移动侧统计时，算一个节目上线
            boolQuery.must(QueryBuilders.termsQuery("spId", spIdList));
        }
        if (null != cpId) {
            boolQuery.must(QueryBuilders.termQuery("cpId", cpId));
            vo.setCpId(cpId);
        } else {
            boolQuery.must(QueryBuilders.termsQuery("cpId", cpIdList));
        }
        if (null != definitionFlag) {
            boolQuery.must(QueryBuilders.termQuery("definitionFlag", definitionFlag));
            vo.setDefinitionFlag(definitionFlag);
        }
        ArrayList<Integer> integers = new ArrayList<>();
        integers.add(PublishStatusEnum.ROLLBACK.getCode());
        boolQuery.must(QueryBuilders.termsQuery("publishStatus", integers));
        //计算介质时长及文件大小
        aggBuilder.subAggregation(AggregationBuilders.sum("durationSum").field("duration"));
        aggBuilder.subAggregation(AggregationBuilders.sum("fileSizeSum").field("fileSize"));
        SearchSourceBuilder searchSourceBuilder = new SearchSourceBuilder();
        searchSourceBuilder.aggregation(aggBuilder);
        searchSourceBuilder.query(boolQuery);
        searchSourceBuilder.size(0);
        searchRequest.source(searchSourceBuilder);
        SearchResponse searchResponse = search(searchRequest);
        log.info("ES搜索语句={}", searchRequest.source().toString());
        Aggregations aggregations = searchResponse.getAggregations();
        Terms outOrderItemGroup = aggregations.get("bms_program_group");
        for (Terms.Bucket bucket : outOrderItemGroup.getBuckets()) {
            //获取数量
            long count;
            Cardinality cmsContentIdRepeat = bucket.getAggregations().get("cmsContentIdRepeat");
            count = Long.parseLong(String.valueOf(cmsContentIdRepeat.getValue()));
            Sum durationSum = bucket.getAggregations().get("durationSum");
            Sum fileSizeSum = bucket.getAggregations().get("fileSizeSum");
            vo.setCountSubset(count);
            vo.setDurationSubset((long) durationSum.getValue());
            vo.setFileSizeSubset((long) fileSizeSum.getValue());
        }
        log.info("==========getNewOfflineSubsetTotalAsync整体耗时{}ms", System.currentTimeMillis() - l);
        return vo;
    }

    @Override
    public StatisticsPublishRespVo getPublishContentTotal(StatisticsPublishReqVo reqVo) {
        long l = System.currentTimeMillis();
        String scriptString = "doc['contentType'] +'#'+ doc['publishStatus']";
        StatisticsPublishRespVo respVo = new StatisticsPublishRespVo();
        Terms outOrderItemGroup = getPublishTotalCommon(bmsContent, reqVo, respVo, scriptString);
        for (Terms.Bucket bucket : outOrderItemGroup.getBuckets()) {
            //获取数量
            Cardinality cmsContentIdRepeat = bucket.getAggregations().get("cardinalityCount");
            long count = cmsContentIdRepeat.getValue();
            //获取分组信息
            String keyAsString = bucket.getKeyAsString();
            String[] split = keyAsString.split("#");
            String contentTypeString = split[0].substring(1, split[0].length() - 1);
            Integer contentType = null;
            if (StringUtils.isNotEmpty(contentTypeString)) {
                contentType = Integer.parseInt(contentTypeString);
            }
            String publishStatusString = split[1].substring(1, split[1].length() - 1);
            Integer publishStatus = null;
            if (StringUtils.isNotEmpty(publishStatusString)) {
                publishStatus = Integer.parseInt(publishStatusString);
            }
            //获取分组信息
            if (ContentTypeEnum.FILM.getValue().equals(contentType)) {
                if (1 == reqVo.getType()) {
                    if (PublishStatusEnum.PUBLISH.getCode().equals(publishStatus)) {
                        respVo.setCountSuccessProgram(respVo.getCountSuccessProgram() + count);
                    } else if (PublishStatusEnum.FAILPUBLISH.getCode().equals(publishStatus)) {
                        respVo.setCountFailProgram(respVo.getCountFailProgram() + count);
                    } else {
                        respVo.setCountProgram(respVo.getCountProgram() + count);
                    }
                } else if (2 == reqVo.getType()) {
                    if (PublishStatusEnum.ROLLBACK.getCode().equals(publishStatus)) {
                        respVo.setCountSuccessProgram(respVo.getCountSuccessProgram() + count);
                    } else if (PublishStatusEnum.FAILROLLBACK.getCode().equals(publishStatus)) {
                        respVo.setCountFailProgram(respVo.getCountFailProgram() + count);
                    } else {
                        respVo.setCountProgram(respVo.getCountProgram() + count);
                    }
                }
            } else if (ContentTypeEnum.TELEPLAY.getValue().equals(contentType)) {
                if (1 == reqVo.getType()) {
                    if (PublishStatusEnum.PUBLISH.getCode().equals(publishStatus)) {
                        respVo.setCountSuccessSeries(respVo.getCountSuccessSeries() + count);
                    } else if (PublishStatusEnum.FAILPUBLISH.getCode().equals(publishStatus)) {
                        respVo.setCountFailSeries(respVo.getCountFailSeries() + count);
                    } else {
                        respVo.setCountSeries(respVo.getCountSeries() + count);
                    }
                } else if (2 == reqVo.getType()) {
                    if (PublishStatusEnum.ROLLBACK.getCode().equals(publishStatus)) {
                        respVo.setCountSuccessSeries(respVo.getCountSuccessSeries() + count);
                    } else if (PublishStatusEnum.FAILROLLBACK.getCode().equals(publishStatus)) {
                        respVo.setCountFailSeries(respVo.getCountFailSeries() + count);
                    } else {
                        respVo.setCountSeries(respVo.getCountSeries() + count);
                    }
                }
            }
        }
        log.info("==========getPublishContentTotal整体耗时{}ms", System.currentTimeMillis() - l);
        return respVo;
    }

    @Override
    public StatisticsPublishRespVo getPublishSubsetTotal(StatisticsPublishRespVo vo, StatisticsPublishReqVo reqVo) {
        long l = System.currentTimeMillis();
        String scriptString = "doc['publishStatus']";
        StatisticsPublishRespVo respVo = new StatisticsPublishRespVo();
        Terms outOrderItemGroup = getPublishTotalCommon(bmsProgram, reqVo, respVo, scriptString);
        for (Terms.Bucket bucket : outOrderItemGroup.getBuckets()) {
            //获取数量
            Cardinality cmsContentIdRepeat = bucket.getAggregations().get("cardinalityCount");
            long count = cmsContentIdRepeat.getValue();
            //获取分组信息
            String publishStatusString = bucket.getKeyAsString();
            Integer publishStatus = Integer.parseInt(publishStatusString);
            if (1 == reqVo.getType()) {
                if (PublishStatusEnum.PUBLISH.getCode().equals(publishStatus)) {
                    vo.setCountSuccessSubset(vo.getCountSuccessSubset() + count);
                } else if (PublishStatusEnum.FAILPUBLISH.getCode().equals(publishStatus)) {
                    vo.setCountFailSubset(vo.getCountFailSubset() + count);
                } else {
                    vo.setCountSubset(vo.getCountSubset() + count);
                }
            } else if (2 == reqVo.getType()) {
                if (PublishStatusEnum.ROLLBACK.getCode().equals(publishStatus)) {
                    vo.setCountSuccessSubset(vo.getCountSuccessSubset() + count);
                } else if (PublishStatusEnum.FAILROLLBACK.getCode().equals(publishStatus)) {
                    vo.setCountFailSubset(vo.getCountFailSubset() + count);
                } else {
                    vo.setCountSubset(vo.getCountSubset() + count);
                }
            }
        }
        log.info("==========getPublishSubsetTotal整体耗时{}ms", System.currentTimeMillis() - l);
        return vo;
    }

    @Override
    public StatisticsPublishRespVo getPublishPackageContentTotal(StatisticsPublishReqVo reqVo) {
        long l = System.currentTimeMillis();
        String scriptString = "doc['contentType'] +'#'+ doc['publishStatus']";
        StatisticsPublishRespVo respVo = new StatisticsPublishRespVo();
        Terms outOrderItemGroup = getPublishTotalCommon(bmsPackageContent, reqVo, respVo, scriptString);
        for (Terms.Bucket bucket : outOrderItemGroup.getBuckets()) {
            //获取数量
            Cardinality cmsContentIdRepeat = bucket.getAggregations().get("cardinalityCount");
            long count = cmsContentIdRepeat.getValue();
            //获取分组信息
            String keyAsString = bucket.getKeyAsString();
            String[] split = keyAsString.split("#");
            String contentTypeString = split[0].substring(1, split[0].length() - 1);
            Integer contentType = null;
            if (StringUtils.isNotEmpty(contentTypeString)) {
                contentType = Integer.parseInt(contentTypeString);
            }
            String publishStatusString = split[1].substring(1, split[1].length() - 1);
            Integer publishStatus = null;
            if (StringUtils.isNotEmpty(publishStatusString)) {
                publishStatus = Integer.parseInt(publishStatusString);
            }
            if (ContentTypeEnum.FILM.getValue().equals(contentType)) {
                if (1 == reqVo.getType()) {
                    if (PublishStatusEnum.PUBLISH.getCode().equals(publishStatus)) {
                        respVo.setCountSuccessProgram(respVo.getCountSuccessProgram() + count);
                    } else if (PublishStatusEnum.FAILPUBLISH.getCode().equals(publishStatus)) {
                        respVo.setCountFailProgram(respVo.getCountFailProgram() + count);
                    } else {
                        respVo.setCountProgram(respVo.getCountProgram() + count);
                    }
                } else if (2 == reqVo.getType()) {
                    if (PublishStatusEnum.ROLLBACK.getCode().equals(publishStatus)) {
                        respVo.setCountSuccessProgram(respVo.getCountSuccessProgram() + count);
                    } else if (PublishStatusEnum.FAILROLLBACK.getCode().equals(publishStatus)) {
                        respVo.setCountFailProgram(respVo.getCountFailProgram() + count);
                    } else {
                        respVo.setCountProgram(respVo.getCountProgram() + count);
                    }
                }
            } else if (ContentTypeEnum.TELEPLAY.getValue().equals(contentType)) {
                if (1 == reqVo.getType()) {
                    if (PublishStatusEnum.PUBLISH.getCode().equals(publishStatus)) {
                        respVo.setCountSuccessSeries(respVo.getCountSuccessSeries() + count);
                    } else if (PublishStatusEnum.FAILPUBLISH.getCode().equals(publishStatus)) {
                        respVo.setCountFailSeries(respVo.getCountFailSeries() + count);
                    } else {
                        respVo.setCountSeries(respVo.getCountSeries() + count);
                    }
                } else if (2 == reqVo.getType()) {
                    if (PublishStatusEnum.ROLLBACK.getCode().equals(publishStatus)) {
                        respVo.setCountSuccessSeries(respVo.getCountSuccessSeries() + count);
                    } else if (PublishStatusEnum.FAILROLLBACK.getCode().equals(publishStatus)) {
                        respVo.setCountFailSeries(respVo.getCountFailSeries() + count);
                    } else {
                        respVo.setCountSeries(respVo.getCountSeries() + count);
                    }
                }
            }
        }
        log.info("==========getPublishPackageContentTotal整体耗时{}ms", System.currentTimeMillis() - l);
        return respVo;
    }

    @Override
    public StatisticsPublishRespVo getPublishCategoryContentTotal(StatisticsPublishReqVo reqVo) {
        long l = System.currentTimeMillis();
        String scriptString = "doc['contentType'] +'#'+ doc['publishStatus']";
        StatisticsPublishRespVo respVo = new StatisticsPublishRespVo();
        Terms outOrderItemGroup = getPublishTotalCommon(bmsCategoryContent, reqVo, respVo, scriptString);
        for (Terms.Bucket bucket : outOrderItemGroup.getBuckets()) {
            //获取数量
            Cardinality cmsContentIdRepeat = bucket.getAggregations().get("cardinalityCount");
            long count = cmsContentIdRepeat.getValue();
            //获取分组信息
            String keyAsString = bucket.getKeyAsString();
            String[] split = keyAsString.split("#");
            String contentTypeString = split[0].substring(1, split[0].length() - 1);
            Integer contentType = null;
            if (StringUtils.isNotEmpty(contentTypeString)) {
                contentType = Integer.parseInt(contentTypeString);
            }
            String publishStatusString = split[1].substring(1, split[1].length() - 1);
            Integer publishStatus = null;
            if (StringUtils.isNotEmpty(publishStatusString)) {
                publishStatus = Integer.parseInt(publishStatusString);
            }
            if (ContentTypeEnum.FILM.getValue().equals(contentType)) {
                if (1 == reqVo.getType()) {
                    if (PublishStatusEnum.PUBLISH.getCode().equals(publishStatus)) {
                        respVo.setCountSuccessProgram(respVo.getCountSuccessProgram() + count);
                    } else if (PublishStatusEnum.FAILPUBLISH.getCode().equals(publishStatus)) {
                        respVo.setCountFailProgram(respVo.getCountFailProgram() + count);
                    } else {
                        respVo.setCountProgram(respVo.getCountProgram() + count);
                    }
                } else if (2 == reqVo.getType()) {
                    if (PublishStatusEnum.ROLLBACK.getCode().equals(publishStatus)) {
                        respVo.setCountSuccessProgram(respVo.getCountSuccessProgram() + count);
                    } else if (PublishStatusEnum.FAILROLLBACK.getCode().equals(publishStatus)) {
                        respVo.setCountFailProgram(respVo.getCountFailProgram() + count);
                    } else {
                        respVo.setCountProgram(respVo.getCountProgram() + count);
                    }
                }
            } else if (ContentTypeEnum.TELEPLAY.getValue().equals(contentType)) {
                if (1 == reqVo.getType()) {
                    if (PublishStatusEnum.PUBLISH.getCode().equals(publishStatus)) {
                        respVo.setCountSuccessSeries(respVo.getCountSuccessSeries() + count);
                    } else if (PublishStatusEnum.FAILPUBLISH.getCode().equals(publishStatus)) {
                        respVo.setCountFailSeries(respVo.getCountFailSeries() + count);
                    } else {
                        respVo.setCountSeries(respVo.getCountSeries() + count);
                    }
                } else if (2 == reqVo.getType()) {
                    if (PublishStatusEnum.ROLLBACK.getCode().equals(publishStatus)) {
                        respVo.setCountSuccessSeries(respVo.getCountSuccessSeries() + count);
                    } else if (PublishStatusEnum.FAILROLLBACK.getCode().equals(publishStatus)) {
                        respVo.setCountFailSeries(respVo.getCountFailSeries() + count);
                    } else {
                        respVo.setCountSeries(respVo.getCountSeries() + count);
                    }
                }
            }
        }
        log.info("==========getPublishCategoryContentTotal整体耗时{}ms", System.currentTimeMillis() - l);
        return respVo;
    }

    @Override
    public Page getPublishContentPage(Page page, StatisticsPublishReqVo reqVo) {
        return getPublishPageCommon(page, bmsContent, reqVo);
    }

    @Override
    public Page getPublishSubsetPage(Page page, StatisticsPublishReqVo reqVo) {
        return getPublishPageCommon(page, bmsProgram, reqVo);
    }

    @Override
    public Page getPublishPackageContentPage(Page page, StatisticsPublishReqVo reqVo) {
        return getPublishPageCommon(page, bmsPackageContent, reqVo);
    }

    @Override
    public Page getPublishCategoryContentPage(Page page, StatisticsPublishReqVo reqVo) {
        return getPublishPageCommon(page, bmsCategoryContent, reqVo);
    }

    private void getOutOrderItemTotalAftertreatment(StatisticsOutOrderItemVo vo, int durationRegistFail, int durationUpdateFail, int durationDeleteFail) {
        DecimalFormat df = new DecimalFormat("0.0000");
        //新增工单处理
        Integer successCountRegist = null != vo.getSuccessCountRegist() ? vo.getSuccessCountRegist() : 0;
        Integer failCountRegist = null != vo.getFailCountRegist() ? vo.getFailCountRegist() : 0;
        double durationRegist = null != vo.getDurationRegist() ? vo.getDurationRegist() : 0;
        int countRegist = successCountRegist + failCountRegist;
        double regist = 0;
        if (0 != countRegist) {
            regist = (double) successCountRegist / (vo.getCountRegist() + countRegist);
            durationRegist = (durationRegist * successCountRegist + durationRegistFail * failCountRegist) / countRegist;
        }
        vo.setSuccessRateRegist(Double.parseDouble(df.format(regist)));
        vo.setCountRegist(vo.getCountRegist() + countRegist);
        vo.setDurationRegist((int) Math.round(durationRegist));
        //更新工单处理
        Integer successCountUpdate = null != vo.getSuccessCountUpdate() ? vo.getSuccessCountUpdate() : 0;
        Integer failCountUpdate = null != vo.getFailCountUpdate() ? vo.getFailCountUpdate() : 0;
        double durationUpdate = null != vo.getDurationUpdate() ? vo.getDurationUpdate() : 0;
        int countUpdate = successCountUpdate + failCountUpdate;
        double update = 0;
        if (0 != countUpdate) {
            update = (double) successCountUpdate / (vo.getCountUpdate() + countUpdate);
            durationUpdate = (durationUpdate * successCountUpdate + durationUpdateFail * failCountUpdate) / countUpdate;
        }
        vo.setSuccessRateUpdate(Double.parseDouble(df.format(update)));
        vo.setCountUpdate(vo.getCountUpdate() + countUpdate);
        vo.setDurationUpdate((int) Math.round(durationUpdate));
        //删除工单处理
        Integer successCountDelete = null != vo.getSuccessCountDelete() ? vo.getSuccessCountDelete() : 0;
        Integer failCountDelete = null != vo.getFailCountDelete() ? vo.getFailCountDelete() : 0;
        double durationDelete = null != vo.getDurationDelete() ? vo.getDurationDelete() : 0;
        int countDelete = successCountDelete + failCountDelete;
        double delete = 0;
        if (0 != countDelete) {
            delete = (double) successCountDelete / (vo.getCountDelete() + countDelete);
            durationDelete = (durationDelete * successCountDelete + durationDeleteFail * failCountDelete) / countDelete;
        }
        vo.setSuccessRateDelete(Double.parseDouble(df.format(delete)));
        vo.setCountDelete(vo.getCountDelete() + countDelete);
        vo.setDurationDelete((int) Math.round(durationDelete));
        //全部工单处理
        Integer successCountAll = successCountRegist + successCountUpdate + successCountDelete;
        Integer failCountAll = failCountRegist + failCountUpdate + failCountDelete;
        int countAll = vo.getCountRegist() + vo.getCountUpdate() + vo.getCountDelete();
        double all = 0;
        if (0 != countAll) {
            all = (double) successCountAll / (countAll);
        }
        double durationAll = 0;
        int countAllSuccessOrFail = vo.getSuccessCountRegist() + vo.getFailCountRegist() + vo.getSuccessCountUpdate()
                + vo.getFailCountUpdate() + vo.getSuccessCountDelete() + vo.getFailCountDelete();
        if (0 != countAllSuccessOrFail) {
            durationAll = ((successCountRegist + failCountRegist) * durationRegist + (successCountUpdate + failCountUpdate)
                    * durationUpdate + (successCountDelete + failCountDelete) * durationDelete) / countAllSuccessOrFail;
        }
        vo.setSuccessCountAll(successCountAll);
        vo.setFailCountAll(failCountAll);
        vo.setSuccessRateAll(Double.parseDouble(df.format(all)));
        vo.setCountAll(countAll);
        vo.setDurationAll((int) durationAll);
    }

    private Terms getPublishTotalCommon(String index, StatisticsPublishReqVo reqVo, StatisticsPublishRespVo vo, String scriptString) {
        HashMap<String, List<Long>> permissionMap = getPermission();
        List<Long> spIdList = permissionMap.get("spIdList");
        List<Long> cpIdList = permissionMap.get("cpIdList");
        SearchRequest searchRequest = new SearchRequest(index);
        BoolQueryBuilder boolQuery = QueryBuilders.boolQuery();
        Long bmsSpChannelId = reqVo.getBmsSpChannelId();
        Long spId = reqVo.getSpId();
        Long cpId = reqVo.getCpId();
        String startTime = reqVo.getStartTime();
        String endTime = reqVo.getEndTime();
        //根据多字段进行分组查询
        TermsAggregationBuilder aggBuilder = AggregationBuilders.terms("group")
                .script(new Script(scriptString));
        if (StringUtils.isNotEmpty(startTime) && StringUtils.isNotEmpty(endTime)) {
            String startTimeFormat = DateUtil.format(DateUtil.beginOfDay(DateUtil.parse(startTime, DateUtils.YYYY_MM_DD)), DateUtils.YYYY_MM_DD_HH_MM_SS);
            String endTimeFormat = DateUtil.format(DateUtil.endOfDay(DateUtil.parse(endTime, DateUtils.YYYY_MM_DD)), DateUtils.YYYY_MM_DD_HH_MM_SS);
            boolQuery.must(QueryBuilders.rangeQuery("publishTime").gte(startTimeFormat).lte(endTimeFormat));
        }
        if (null != bmsSpChannelId) {
            boolQuery.must(QueryBuilders.matchQuery("bmsSpChannelId", bmsSpChannelId));
            vo.setBmsSpChannelId(bmsSpChannelId);
            List<SysSp> sysSps = sysSpMapper.selectList(new LambdaQueryWrapper<SysSp>().eq(SysSp::getBmsSpChannelId, bmsSpChannelId));
            List<Long> spIdChannelList = sysSps.stream().map(SysSp::getId).collect(Collectors.toList());
            spIdList.retainAll(spIdChannelList);
        }
        if (null != spId) {
            boolQuery.must(QueryBuilders.termQuery("spId", spId));
            reqVo.setSpId(spId);
        } else {
            boolQuery.must(QueryBuilders.termsQuery("spId", spIdList));
        }
        if (bmsContent.equals(index) || bmsProgram.equals(index)) {
            if (null != cpId) {
                boolQuery.must(QueryBuilders.termQuery("cpId", cpId));
                reqVo.setCpId(cpId);
            } else {
                boolQuery.must(QueryBuilders.termsQuery("cpId", cpIdList));
            }
        }
        ArrayList<Integer> publishStatusList = new ArrayList<>();
        if (1 == reqVo.getType()) {
            publishStatusList.add(PublishStatusEnum.PUBLISH.getCode());
            publishStatusList.add(PublishStatusEnum.FAILPUBLISH.getCode());
            boolQuery.must(QueryBuilders.termsQuery("publishStatus", publishStatusList));
        } else if (2 == reqVo.getType()) {
            publishStatusList.add(PublishStatusEnum.ROLLBACK.getCode());
            publishStatusList.add(PublishStatusEnum.FAILROLLBACK.getCode());
            boolQuery.must(QueryBuilders.termsQuery("publishStatus", publishStatusList));
        }
        if (bmsContent.equals(index) || bmsProgram.equals(index)) {
            aggBuilder.subAggregation(AggregationBuilders.cardinality("cardinalityCount").field("cmsContentCodePublishStatusSpId").precisionThreshold(40000L));
        } else if (bmsPackageContent.equals(index)) {
            aggBuilder.subAggregation(AggregationBuilders.cardinality("cardinalityCount").field("packageCodeCmsContentCodePublishStatusSpId").precisionThreshold(40000L));
        } else if (bmsCategoryContent.equals(index)) {
            aggBuilder.subAggregation(AggregationBuilders.cardinality("cardinalityCount").field("categoryCodeCmsContentCodePublishStatusSpId").precisionThreshold(40000L));
        }
        //计算介质时长及文件大小
        SearchSourceBuilder searchSourceBuilder = new SearchSourceBuilder();
        if (bmsContent.equals(index) || bmsProgram.equals(index)) {
            searchSourceBuilder.collapse(new CollapseBuilder("cmsContentCodePublishStatusSpId"));
        } else if (bmsPackageContent.equals(index)) {
            searchSourceBuilder.collapse(new CollapseBuilder("packageCodeCmsContentCodePublishStatusSpId"));
        } else if (bmsCategoryContent.equals(index)) {
            searchSourceBuilder.collapse(new CollapseBuilder("categoryCodeCmsContentCodePublishStatusSpId"));
        }
        aggBuilder.size(1000);
        searchSourceBuilder.aggregation(aggBuilder);
        searchSourceBuilder.query(boolQuery);
        searchSourceBuilder.size(0);
        searchRequest.source(searchSourceBuilder);
        SearchResponse searchResponse = search(searchRequest);
        log.info("ES搜索语句={}", searchRequest.source().toString());
        Aggregations aggregations = searchResponse.getAggregations();
        return aggregations.get("group");
    }

    private Page getPublishPageCommon(Page page, String index, StatisticsPublishReqVo reqVo) {
        long l = System.currentTimeMillis();
        HashMap<String, List<Long>> permissionMap = getPermission();
        List<Long> spIdList = permissionMap.get("spIdList");
        List<Long> cpIdList = permissionMap.get("cpIdList");
        Integer contentType = reqVo.getContentType();
        Long spId = reqVo.getSpId();
        Long cpId = reqVo.getCpId();
        long startCount = (page.getCurrent() - 1) * page.getSize();
        SearchRequest searchRequest;
        searchRequest = new SearchRequest(index);
        BoolQueryBuilder boolQuery = QueryBuilders.boolQuery();
        if (null != contentType && !ContentTypeEnum.SUBSET.getValue().equals(contentType)) {
            boolQuery.must(QueryBuilders.matchQuery("contentType", contentType));
        }
        Long bmsSpChannelId = reqVo.getBmsSpChannelId();
        if (1 == reqVo.getType()) {
            //限制发布状态
            ArrayList<Integer> integers = new ArrayList<>();
            integers.add(PublishStatusEnum.PUBLISH.getCode());
            integers.add(PublishStatusEnum.FAILPUBLISH.getCode());
            boolQuery.must(QueryBuilders.termsQuery("publishStatus", integers));
        } else if (2 == reqVo.getType()) {
            //限制发布状态
            ArrayList<Integer> integers = new ArrayList<>();
            integers.add(PublishStatusEnum.ROLLBACK.getCode());
            integers.add(PublishStatusEnum.FAILROLLBACK.getCode());
            boolQuery.must(QueryBuilders.termsQuery("publishStatus", integers));
        }
        if (null != bmsSpChannelId) {
            reqVo.setBmsSpChannelId(bmsSpChannelId);
            List<SysSp> sysSps = sysSpMapper.selectList(new LambdaQueryWrapper<SysSp>().eq(SysSp::getBmsSpChannelId, bmsSpChannelId));
            List<Long> spIdChannelList = sysSps.stream().map(SysSp::getId).collect(Collectors.toList());
            spIdList.retainAll(spIdChannelList);
            if (1 == reqVo.getWorkOrderType()) {
                boolQuery.must(QueryBuilders.matchQuery("bmsSpChannelId", bmsSpChannelId));
            } else {
                boolQuery.must(QueryBuilders.termsQuery("spId", spIdList));
            }
        }
        if (null != spId) {
            boolQuery.must(QueryBuilders.termQuery("spId", spId));
            reqVo.setSpId(spId);
        } else {
            boolQuery.must(QueryBuilders.termsQuery("spId", spIdList));
        }
        if (bmsContent.equals(index) || bmsProgram.equals(index)) {
            if (null != cpId) {
                boolQuery.must(QueryBuilders.termQuery("cpId", cpId));
                reqVo.setCpId(cpId);
            } else {
                boolQuery.must(QueryBuilders.termsQuery("cpId", cpIdList));
            }
        }
        String startTime = reqVo.getStartTime();
        String endTime = reqVo.getEndTime();
        if (StringUtils.isNotEmpty(startTime) && StringUtils.isNotEmpty(endTime)) {
            String startTimeFormat = DateUtil.format(DateUtil.beginOfDay(DateUtil.parse(startTime, DateUtils.YYYY_MM_DD)), DateUtils.YYYY_MM_DD_HH_MM_SS);
            String endTimeFormat = DateUtil.format(DateUtil.endOfDay(DateUtil.parse(endTime, DateUtils.YYYY_MM_DD)), DateUtils.YYYY_MM_DD_HH_MM_SS);
            boolQuery.must(QueryBuilders.rangeQuery("publishTime").gte(startTimeFormat).lte(endTimeFormat));
        }
        SearchSourceBuilder searchSourceBuilder = new SearchSourceBuilder();
        if (bmsContent.equals(index) || bmsProgram.equals(index)) {
            searchSourceBuilder.collapse(new CollapseBuilder("cmsContentCodePublishStatusSpId"));
            searchSourceBuilder.aggregation(AggregationBuilders.cardinality("cardinalityCount").field("cmsContentCodePublishStatusSpId").precisionThreshold(40000L));
        } else if (bmsCategoryContent.equals(index)) {
            searchSourceBuilder.collapse(new CollapseBuilder("categoryCodeCmsContentCodePublishStatusSpId"));
            searchSourceBuilder.aggregation(AggregationBuilders.cardinality("cardinalityCount").field("categoryCodeCmsContentCodePublishStatusSpId").precisionThreshold(40000L));
        } else if (bmsPackageContent.equals(index)) {
            searchSourceBuilder.collapse(new CollapseBuilder("packageCodeCmsContentCodePublishStatusSpId"));
            searchSourceBuilder.aggregation(AggregationBuilders.cardinality("cardinalityCount").field("packageCodeCmsContentCodePublishStatusSpId").precisionThreshold(40000L));
        } else {
            throw new CommonResponseException("index不能为空");
        }
        searchSourceBuilder.query(boolQuery);
        searchSourceBuilder.sort(new FieldSortBuilder("publishTime").unmappedType("date").order(SortOrder.DESC));
        searchSourceBuilder.from(Integer.parseInt(String.valueOf(startCount)));
        searchSourceBuilder.size(Integer.parseInt(String.valueOf(page.getSize())));
        searchSourceBuilder.trackTotalHits(true);
        searchRequest.source(searchSourceBuilder);
        log.info("ES搜索语句={}", searchRequest.source().toString());
        SearchResponse searchResponse = search(searchRequest);
        Cardinality cardinalityCount = searchResponse.getAggregations().get("cardinalityCount");
        long total = cardinalityCount.getValue();
        SearchHit[] hits = searchResponse.getHits().getHits();
        List<HashMap<String, Object>> list = new ArrayList<>();
        for (SearchHit hit : hits) {
            HashMap<String, Object> t = objectMapper.convertValue(hit.getSourceAsMap(), HashMap.class);
            list.add(t);
        }
        page.setTotal(total);
        page.setRecords(list);
        log.info("getPublishPageCommon耗时+{}ms", System.currentTimeMillis() - l);
        return page;
    }

    private StatisticsOnlineRespVo getNewOnlineContentCommonAsyn(String index,
                                                                 StatisticsOnlineReqVo statisticsOnlineReqVo,
                                                                 Boolean statusFlag, HashMap<String, List<Long>> permissionMap) {
        long l = System.currentTimeMillis();
        if (null == permissionMap) {
            permissionMap = getPermission();
        }
        List<Long> spIdList = permissionMap.get("spIdList");
        List<Long> cpIdList = permissionMap.get("cpIdList");
        SearchRequest searchRequest = new SearchRequest(index);
        BoolQueryBuilder boolQuery = QueryBuilders.boolQuery();
        Long bmsSpChannelId = statisticsOnlineReqVo.getBmsSpChannelId();
        Long spId = statisticsOnlineReqVo.getSpId();
        Long cpId = statisticsOnlineReqVo.getCpId();
        Integer definitionFlag = statisticsOnlineReqVo.getDefinitionFlag();
        Integer mediaBindStatus = statisticsOnlineReqVo.getMediaBindStatus();
        String startTime = statisticsOnlineReqVo.getStartTime();
        String endTime = statisticsOnlineReqVo.getEndTime();
        StatisticsOnlineRespVo vo = new StatisticsOnlineRespVo();
        if (Boolean.TRUE.equals(statusFlag)) {
            boolQuery.must(QueryBuilders.termQuery("statusFlag", 1));
        }
        //根据多字段进行分组查询
        TermsAggregationBuilder aggBuilder = AggregationBuilders.terms("bms_content_group").field("contentType");
        if (StringUtils.isNotEmpty(startTime) && StringUtils.isNotEmpty(endTime)) {
            String startTimeFormat = DateUtil.format(DateUtil.beginOfDay(DateUtil.parse(startTime, DateUtils.YYYY_MM_DD)), DateUtils.YYYY_MM_DD_HH_MM_SS);
            String endTimeFormat = DateUtil.format(DateUtil.endOfDay(DateUtil.parse(endTime, DateUtils.YYYY_MM_DD)), DateUtils.YYYY_MM_DD_HH_MM_SS);
            if (StatisticsMediaBindStatusEnum.ALL.getValue().equals(mediaBindStatus)) {
                boolQuery.must(QueryBuilders.rangeQuery("publishTime").gte(startTimeFormat).lte(endTimeFormat));
            } else if (StatisticsMediaBindStatusEnum.BIND.getValue().equals(mediaBindStatus)) {
                boolQuery.must(QueryBuilders.rangeQuery("relationPublishTime").gte(startTimeFormat).lte(endTimeFormat));
                boolQuery.must(QueryBuilders.termQuery("bindStatus", StatisticsMediaBindStatusEnum.BIND.getValue()));
            } else if (StatisticsMediaBindStatusEnum.UNBIND.getValue().equals(mediaBindStatus)) {
                boolQuery.must(QueryBuilders.rangeQuery("publishTime").gte(startTimeFormat).lte(endTimeFormat));
                boolQuery.must(QueryBuilders.termQuery("bindStatus", StatisticsMediaBindStatusEnum.UNBIND.getValue()));
            } else {
                log.error("错误的绑定状态bindStatus={}", mediaBindStatus);
            }
        } else {
            if (StatisticsMediaBindStatusEnum.ALL.getValue().equals(mediaBindStatus)) {
                log.info("单节目上线无需考虑绑定关系");
            } else if (StatisticsMediaBindStatusEnum.BIND.getValue().equals(mediaBindStatus)) {
                boolQuery.must(QueryBuilders.termQuery("bindStatus", StatisticsMediaBindStatusEnum.BIND.getValue()));
            } else if (StatisticsMediaBindStatusEnum.UNBIND.getValue().equals(mediaBindStatus)) {
                boolQuery.must(QueryBuilders.termQuery("bindStatus", StatisticsMediaBindStatusEnum.UNBIND.getValue()));
            } else {
                log.error("错误的绑定状态bindStatus={}", mediaBindStatus);
            }
        }
        if (null != bmsSpChannelId) {
            boolQuery.must(QueryBuilders.matchQuery("bmsSpChannelId", bmsSpChannelId));
            vo.setBmsSpChannelId(bmsSpChannelId);
            List<SysSp> sysSps = sysSpMapper.selectList(new LambdaQueryWrapper<SysSp>().eq(SysSp::getBmsSpChannelId, bmsSpChannelId));
            List<Long> spIdChannelList = sysSps.stream().map(SysSp::getId).collect(Collectors.toList());
            spIdList.retainAll(spIdChannelList);
        }
        aggBuilder.subAggregation(AggregationBuilders.terms("cmsContentIdRepeat").field("cmsContentCode").size(Integer.MAX_VALUE));
        if (null != spId) {
            boolQuery.must(QueryBuilders.termQuery("spId", spId));
            vo.setSpId(spId);
        } else {
            //针对某个渠道下全部SP，则按SP去重统计。例如，同一个节目在移动中兴和移动华为平台均已上线，在移动侧统计时，算一个节目上线
            boolQuery.must(QueryBuilders.termsQuery("spId", spIdList));
        }
        if (null != cpId) {
            boolQuery.must(QueryBuilders.termQuery("cpId", cpId));
            vo.setCpId(cpId);
        } else {
            boolQuery.must(QueryBuilders.termsQuery("cpId", cpIdList));
        }
        if (null != definitionFlag) {
            boolQuery.must(QueryBuilders.termQuery("definitionFlag", definitionFlag));
            vo.setDefinitionFlag(definitionFlag);
        }
        //限制发布状态
        ArrayList<Integer> integers = new ArrayList<>();
        integers.add(PublishStatusEnum.PUBLISH.getCode());
        integers.add(PublishStatusEnum.WAITUPDATE.getCode());
        integers.add(PublishStatusEnum.UPDATING.getCode());
        integers.add(PublishStatusEnum.FAILUPDATE.getCode());
        boolQuery.must(QueryBuilders.termsQuery("publishStatus", integers));
        vo.setMediaBindStatus(mediaBindStatus);
        //计算介质时长及文件大小
        aggBuilder.subAggregation(AggregationBuilders.sum("durationSum").field("duration"));
        aggBuilder.subAggregation(AggregationBuilders.sum("fileSizeSum").field("fileSize"));
        SearchSourceBuilder searchSourceBuilder = new SearchSourceBuilder();
        searchSourceBuilder.aggregation(aggBuilder);
        searchSourceBuilder.query(boolQuery);
        searchSourceBuilder.size(0);
        searchRequest.source(searchSourceBuilder);
        SearchResponse searchResponse = search(searchRequest);
        log.info("ES搜索语句={}", searchRequest.source().toString());
        Aggregations aggregations = searchResponse.getAggregations();
        Terms outOrderItemGroup = aggregations.get("bms_content_group");
        for (Terms.Bucket bucket : outOrderItemGroup.getBuckets()) {
            //获取数量
            long count;
            Aggregation cmsContentIdRepeat = bucket.getAggregations().get("cmsContentIdRepeat");
            count = ((ParsedStringTerms) cmsContentIdRepeat).getBuckets().size();
            Sum cmsResourceDurationSumSum = bucket.getAggregations().get("durationSum");
            Sum fileSizeSumSum = bucket.getAggregations().get("fileSizeSum");
            long durationSum = (long) cmsResourceDurationSumSum.getValue();
            long fileSizeSum = (long) fileSizeSumSum.getValue();
            //获取分组信息
            String contentTypeString = bucket.getKeyAsString();
            Integer contentType = Integer.parseInt(contentTypeString);
            if (ContentTypeEnum.FILM.getValue().equals(contentType)) {
                vo.setCountProgram(count);
                vo.setDurationProgram(durationSum);
                vo.setFileSizeProgram(fileSizeSum);
            } else if (ContentTypeEnum.TELEPLAY.getValue().equals(contentType)) {
                vo.setCountSeries(vo.getCountSeries() + count);
            } else if (ContentTypeEnum.EPISODES.getValue().equals(contentType)) {
                vo.setCountSeries(vo.getCountSeries() + count);
            }
        }
        log.info("==========getNewOnlineContentCommonAsyn整体耗时{}ms", System.currentTimeMillis() - l);
        return vo;
    }

    private StatisticsOnlineRespVo getOnlineContentCommon(String index,
                                                          Integer contentType,
                                                          StatisticsOnlineReqVo statisticsOnlineReqVo,
                                                          Boolean statusFlag, HashMap<String, List<Long>> permissionMap) {
        long l = System.currentTimeMillis();
        if (null == permissionMap) {
            permissionMap = getPermission();
        }
        List<Long> spIdList = permissionMap.get("spIdList");
        List<Long> cpIdList = permissionMap.get("cpIdList");
        SearchRequest searchRequest = new SearchRequest(index);
        BoolQueryBuilder boolQuery = QueryBuilders.boolQuery();
        Long bmsSpChannelId = statisticsOnlineReqVo.getBmsSpChannelId();
        Long spId = statisticsOnlineReqVo.getSpId();
        Long cpId = statisticsOnlineReqVo.getCpId();
        Integer definitionFlag = statisticsOnlineReqVo.getDefinitionFlag();
        Integer mediaBindStatus = statisticsOnlineReqVo.getMediaBindStatus();
        String startTime = statisticsOnlineReqVo.getStartTime();
        String endTime = statisticsOnlineReqVo.getEndTime();
        StatisticsOnlineRespVo vo = new StatisticsOnlineRespVo();
        SearchSourceBuilder searchSourceBuilder = new SearchSourceBuilder();
        if (Boolean.TRUE.equals(statusFlag)) {
            boolQuery.must(QueryBuilders.termQuery("statusFlag", 1));
        }
        boolQuery.must(QueryBuilders.termQuery("contentType", contentType));
        if (StringUtils.isNotEmpty(startTime) && StringUtils.isNotEmpty(endTime)) {
            String startTimeFormat = DateUtil.format(DateUtil.beginOfDay(DateUtil.parse(startTime, DateUtils.YYYY_MM_DD)), DateUtils.YYYY_MM_DD_HH_MM_SS);
            String endTimeFormat = DateUtil.format(DateUtil.endOfDay(DateUtil.parse(endTime, DateUtils.YYYY_MM_DD)), DateUtils.YYYY_MM_DD_HH_MM_SS);
            if (StatisticsMediaBindStatusEnum.ALL.getValue().equals(mediaBindStatus)) {
                boolQuery.must(QueryBuilders.rangeQuery("publishTime").gte(startTimeFormat).lte(endTimeFormat));
            } else if (StatisticsMediaBindStatusEnum.BIND.getValue().equals(mediaBindStatus)) {
                boolQuery.must(QueryBuilders.rangeQuery("relationPublishTime").gte(startTimeFormat).lte(endTimeFormat));
                boolQuery.must(QueryBuilders.termQuery("bindStatus", StatisticsMediaBindStatusEnum.BIND.getValue()));
            } else if (StatisticsMediaBindStatusEnum.UNBIND.getValue().equals(mediaBindStatus)) {
                boolQuery.must(QueryBuilders.rangeQuery("publishTime").gte(startTimeFormat).lte(endTimeFormat));
                boolQuery.must(QueryBuilders.termQuery("bindStatus", StatisticsMediaBindStatusEnum.UNBIND.getValue()));
            } else {
                log.error("错误的绑定状态bindStatus={}", mediaBindStatus);
            }
        } else {
            if (StatisticsMediaBindStatusEnum.ALL.getValue().equals(mediaBindStatus)) {
                log.info("单节目上线无需考虑绑定关系");
            } else if (StatisticsMediaBindStatusEnum.BIND.getValue().equals(mediaBindStatus)) {
                boolQuery.must(QueryBuilders.termQuery("bindStatus", StatisticsMediaBindStatusEnum.BIND.getValue()));
            } else if (StatisticsMediaBindStatusEnum.UNBIND.getValue().equals(mediaBindStatus)) {
                boolQuery.must(QueryBuilders.termQuery("bindStatus", StatisticsMediaBindStatusEnum.UNBIND.getValue()));
            } else {
                log.error("错误的绑定状态bindStatus={}", mediaBindStatus);
            }
        }
        if (null != bmsSpChannelId) {
            boolQuery.must(QueryBuilders.matchQuery("bmsSpChannelId", bmsSpChannelId));
            vo.setBmsSpChannelId(bmsSpChannelId);
            List<SysSp> sysSps = sysSpMapper.selectList(new LambdaQueryWrapper<SysSp>().eq(SysSp::getBmsSpChannelId, bmsSpChannelId));
            List<Long> spIdChannelList = sysSps.stream().map(SysSp::getId).collect(Collectors.toList());
            spIdList.retainAll(spIdChannelList);
        }
        TermsAggregationBuilder size = AggregationBuilders.terms("bms_content_group").field("cmsContentCode").size(Integer.MAX_VALUE);
        size.subAggregation(AggregationBuilders.max("durationSum").field("duration"));
        size.subAggregation(AggregationBuilders.max("fileSizeSum").field("fileSize"));
        searchSourceBuilder.aggregation(size);
        //计算介质时长及文件大小
        searchSourceBuilder.aggregation(PipelineAggregatorBuilders.statsBucket("stats_durationSum", "bms_content_group.durationSum"));
        searchSourceBuilder.aggregation(PipelineAggregatorBuilders.statsBucket("stats_fileSizeSum", "bms_content_group.fileSizeSum"));
        searchSourceBuilder.aggregation(PipelineAggregatorBuilders.statsBucket("stats_count", "bms_content_group._count"));
        if (null != spId) {
            boolQuery.must(QueryBuilders.termQuery("spId", spId));
            vo.setSpId(spId);
        } else {
            //针对某个渠道下全部SP，则按SP去重统计。例如，同一个节目在移动中兴和移动华为平台均已上线，在移动侧统计时，算一个节目上线
            boolQuery.must(QueryBuilders.termsQuery("spId", spIdList));
        }
        if (null != cpId) {
            boolQuery.must(QueryBuilders.termQuery("cpId", cpId));
            vo.setCpId(cpId);
        } else {
            boolQuery.must(QueryBuilders.termsQuery("cpId", cpIdList));
        }
        if (null != definitionFlag) {
            boolQuery.must(QueryBuilders.termQuery("definitionFlag", definitionFlag));
            vo.setDefinitionFlag(definitionFlag);
        }
        //限制发布状态
        ArrayList<Integer> integers = new ArrayList<>();
        integers.add(PublishStatusEnum.PUBLISH.getCode());
        integers.add(PublishStatusEnum.WAITUPDATE.getCode());
        integers.add(PublishStatusEnum.UPDATING.getCode());
        integers.add(PublishStatusEnum.FAILUPDATE.getCode());
        boolQuery.must(QueryBuilders.termsQuery("publishStatus", integers));
        vo.setMediaBindStatus(mediaBindStatus);
        searchSourceBuilder.query(boolQuery);
        searchSourceBuilder.size(0);
        searchSourceBuilder.trackTotalHits(true);
        searchRequest.source(searchSourceBuilder);
        SearchResponse searchResponse = search(searchRequest);
        log.info("ES搜索语句={}", searchRequest.source().toString());
        Aggregations aggregations = searchResponse.getAggregations();
        long count;
        long durationSum;
        long fileSizeSum;
        ParsedStatsBucket statsDurationSum = aggregations.get("stats_durationSum");
        ParsedStatsBucket statsFileSizeSum = aggregations.get("stats_fileSizeSum");
        ParsedStatsBucket statsCount = aggregations.get("stats_count");
        count = statsCount.getCount();
        durationSum = (long) statsDurationSum.getSum();
        fileSizeSum = (long) statsFileSizeSum.getSum();
        if (ContentTypeEnum.FILM.getValue().equals(contentType)) {
            vo.setCountProgram(count);
            vo.setDurationProgram(durationSum);
            vo.setFileSizeProgram(fileSizeSum);
        } else if (ContentTypeEnum.TELEPLAY.getValue().equals(contentType)) {
            vo.setCountSeries(count);
        } else if (ContentTypeEnum.EPISODES.getValue().equals(contentType)) {
            vo.setCountSeries(count);
        }
        log.info("==========getOnlineContentCommon整体耗时{}ms", System.currentTimeMillis() - l);
        return vo;
    }

    private StatisticsOnlineRespVo getNewOnlineContentCommon(String index,
                                                             Integer contentType,
                                                             StatisticsOnlineReqVo statisticsOnlineReqVo,
                                                             Boolean statusFlag,
                                                             HashMap<String, List<Long>> permissionMap, Boolean flag) {
        long l = System.currentTimeMillis();
        List<Long> spIdList;
        List<Long> cpIdList;
        if (flag) {
            if (null == permissionMap) {
                permissionMap = getPermission();
            }
            spIdList = permissionMap.get("spIdList");
            cpIdList = permissionMap.get("cpIdList");
        } else {
            spIdList = sysSpMapper.selectList(new LambdaQueryWrapper<SysSp>().ne(SysSp::getStatus, StatusEnum.DELETE.getCode())).stream().map(SysSp::getId).collect(Collectors.toList());
            cpIdList = sysCpMapper.selectList(new LambdaQueryWrapper<SysCp>().ne(SysCp::getStatus, StatusEnum.DELETE.getCode())).stream().map(SysCp::getId).collect(Collectors.toList());
        }
        SearchRequest searchRequest = new SearchRequest(index);
        BoolQueryBuilder boolQuery = QueryBuilders.boolQuery();
        Long bmsSpChannelId = statisticsOnlineReqVo.getBmsSpChannelId();
        Long spId = statisticsOnlineReqVo.getSpId();
        Long cpId = statisticsOnlineReqVo.getCpId();
        Integer definitionFlag = statisticsOnlineReqVo.getDefinitionFlag();
        Integer mediaBindStatus = statisticsOnlineReqVo.getMediaBindStatus();
        String startTime = statisticsOnlineReqVo.getStartTime();
        String endTime = statisticsOnlineReqVo.getEndTime();
        StatisticsOnlineRespVo vo = new StatisticsOnlineRespVo();
        SearchSourceBuilder searchSourceBuilder = new SearchSourceBuilder();
        if (Boolean.TRUE.equals(statusFlag)) {
            boolQuery.must(QueryBuilders.termQuery("statusFlag", 1));
        }
        boolQuery.must(QueryBuilders.termQuery("contentType", contentType));
        if (StringUtils.isNotEmpty(startTime) && StringUtils.isNotEmpty(endTime)) {
            String startTimeFormat = DateUtil.format(DateUtil.beginOfDay(DateUtil.parse(startTime, DateUtils.YYYY_MM_DD)), DateUtils.YYYY_MM_DD_HH_MM_SS);
            String endTimeFormat = DateUtil.format(DateUtil.endOfDay(DateUtil.parse(endTime, DateUtils.YYYY_MM_DD)), DateUtils.YYYY_MM_DD_HH_MM_SS);
            if (StatisticsMediaBindStatusEnum.ALL.getValue().equals(mediaBindStatus)) {
                boolQuery.must(QueryBuilders.rangeQuery("publishTime").gte(startTimeFormat).lte(endTimeFormat));
            } else if (StatisticsMediaBindStatusEnum.BIND.getValue().equals(mediaBindStatus)) {
                boolQuery.must(QueryBuilders.rangeQuery("relationPublishTime").gte(startTimeFormat).lte(endTimeFormat));
                boolQuery.must(QueryBuilders.termQuery("bindStatus", StatisticsMediaBindStatusEnum.BIND.getValue()));
            } else if (StatisticsMediaBindStatusEnum.UNBIND.getValue().equals(mediaBindStatus)) {
                boolQuery.must(QueryBuilders.rangeQuery("publishTime").gte(startTimeFormat).lte(endTimeFormat));
            } else {
                log.error("错误的绑定状态bindStatus={}", mediaBindStatus);
            }
        } else {
            if (StatisticsMediaBindStatusEnum.ALL.getValue().equals(mediaBindStatus)) {
                log.info("单节目上线无需考虑绑定关系");
            } else if (StatisticsMediaBindStatusEnum.BIND.getValue().equals(mediaBindStatus)) {
                boolQuery.must(QueryBuilders.termQuery("bindStatus", StatisticsMediaBindStatusEnum.BIND.getValue()));
            } else if (StatisticsMediaBindStatusEnum.UNBIND.getValue().equals(mediaBindStatus)) {
                log.info("节目未绑栏目上线无需考虑绑定关系");
            } else {
                log.error("错误的绑定状态bindStatus={}", mediaBindStatus);
            }
        }
        if (null != bmsSpChannelId) {
            boolQuery.must(QueryBuilders.matchQuery("bmsSpChannelId", bmsSpChannelId));
            vo.setBmsSpChannelId(bmsSpChannelId);
            List<SysSp> sysSps = sysSpMapper.selectList(new LambdaQueryWrapper<SysSp>().eq(SysSp::getBmsSpChannelId, bmsSpChannelId));
            List<Long> spIdChannelList = sysSps.stream().map(SysSp::getId).collect(Collectors.toList());
            spIdList.retainAll(spIdChannelList);
        }
        TermsAggregationBuilder size = AggregationBuilders.terms("bms_content_group").field("cmsContentCode").size(Integer.MAX_VALUE);
        size.subAggregation(AggregationBuilders.max("durationSum").field("duration"));
        size.subAggregation(AggregationBuilders.max("fileSizeSum").field("fileSize"));
        if (StatisticsMediaBindStatusEnum.UNBIND.getValue().equals(statisticsOnlineReqVo.getMediaBindStatus())) {
            size.subAggregation(AggregationBuilders.min("min_bindStatus").field("bindStatus"));
            HashMap<String, Object> map = new HashMap<>(2);
            map.put("v0", "INTEGER");
            map.put("v1", 2);
            String scriptString = "InternalQlScriptUtils.nullSafeFilter(InternalQlScriptUtils.gt(InternalQlScriptUtils.nullSafeCastNumeric(params.minBindStatus,params.v0),params.v1))";
            Script script = new Script(ScriptType.INLINE, "painless", scriptString, map);
            Map<String, String> bucketsPathsMap = new HashMap<>(2);
            bucketsPathsMap.put("minBindStatus", "min_bindStatus");
            BucketSelectorPipelineAggregationBuilder bs = PipelineAggregatorBuilders.bucketSelector("having", bucketsPathsMap, script);
            size.subAggregation(bs);
        }
        searchSourceBuilder.aggregation(size);
        //计算介质时长及文件大小
        searchSourceBuilder.aggregation(PipelineAggregatorBuilders.statsBucket("stats_durationSum", "bms_content_group.durationSum"));
        searchSourceBuilder.aggregation(PipelineAggregatorBuilders.statsBucket("stats_fileSizeSum", "bms_content_group.fileSizeSum"));
        searchSourceBuilder.aggregation(PipelineAggregatorBuilders.statsBucket("stats_count", "bms_content_group._count"));
        if (null != spId) {
            boolQuery.must(QueryBuilders.termQuery("spId", spId));
            vo.setSpId(spId);
        } else {
            //针对某个渠道下全部SP，则按SP去重统计。例如，同一个节目在移动中兴和移动华为平台均已上线，在移动侧统计时，算一个节目上线
            boolQuery.must(QueryBuilders.termsQuery("spId", spIdList));
        }
        if (null != cpId) {
            boolQuery.must(QueryBuilders.termQuery("cpId", cpId));
            vo.setCpId(cpId);
        } else {
            boolQuery.must(QueryBuilders.termsQuery("cpId", cpIdList));
        }
        if (null != definitionFlag) {
            boolQuery.must(QueryBuilders.termQuery("definitionFlag", definitionFlag));
            vo.setDefinitionFlag(definitionFlag);
        }
        //限制发布状态
        ArrayList<Integer> integers = new ArrayList<>();
        integers.add(PublishStatusEnum.PUBLISH.getCode());
        integers.add(PublishStatusEnum.WAITUPDATE.getCode());
        integers.add(PublishStatusEnum.UPDATING.getCode());
        integers.add(PublishStatusEnum.FAILUPDATE.getCode());
        boolQuery.must(QueryBuilders.termsQuery("publishStatus", integers));
        vo.setMediaBindStatus(mediaBindStatus);
        searchSourceBuilder.query(boolQuery);
        searchSourceBuilder.size(0);
        searchSourceBuilder.trackTotalHits(true);
        searchRequest.source(searchSourceBuilder);
        SearchResponse searchResponse = search(searchRequest);
        log.info("ES搜索语句={}", searchRequest.source().toString());
        Aggregations aggregations = searchResponse.getAggregations();
        long count;
        long durationSum;
        long fileSizeSum;
        ParsedStatsBucket statsDurationSum = aggregations.get("stats_durationSum");
        ParsedStatsBucket statsFileSizeSum = aggregations.get("stats_fileSizeSum");
        ParsedStatsBucket statsCount = aggregations.get("stats_count");
        count = statsCount.getCount();
        durationSum = (long) statsDurationSum.getSum();
        fileSizeSum = (long) statsFileSizeSum.getSum();
        if (ContentTypeEnum.FILM.getValue().equals(contentType)) {
            vo.setCountProgram(count);
            vo.setDurationProgram(durationSum);
            vo.setFileSizeProgram(fileSizeSum);
        } else if (ContentTypeEnum.TELEPLAY.getValue().equals(contentType)) {
            vo.setCountSeries(count);
        } else if (ContentTypeEnum.EPISODES.getValue().equals(contentType)) {
            vo.setCountSeries(count);
        }
        log.info("==========getNewOnlineContentCommon整体耗时{}ms", System.currentTimeMillis() - l);
        return vo;
    }

    private StatisticsOnlineRespVo getOnlineSubsetCommon(String index, StatisticsOnlineReqVo statisticsOnlineReqVo,
                                                         StatisticsOnlineRespVo vo, Boolean statusFlag, HashMap<String, List<Long>> permissionMap, Boolean flag) {
        long l = System.currentTimeMillis();
        if (null == permissionMap) {
            permissionMap = getPermission();
        }
        List<Long> spIdList = permissionMap.get("spIdList");
        List<Long> cpIdList = permissionMap.get("cpIdList");
        SearchRequest searchRequest = new SearchRequest(index);
        BoolQueryBuilder boolQuery = QueryBuilders.boolQuery();
        Long bmsSpChannelId = statisticsOnlineReqVo.getBmsSpChannelId();
        Long spId = statisticsOnlineReqVo.getSpId();
        Long cpId = statisticsOnlineReqVo.getCpId();
        Integer definitionFlag = statisticsOnlineReqVo.getDefinitionFlag();
        Integer mediaBindStatus = statisticsOnlineReqVo.getMediaBindStatus();
        String startTime = statisticsOnlineReqVo.getStartTime();
        String endTime = statisticsOnlineReqVo.getEndTime();
        SearchSourceBuilder searchSourceBuilder = new SearchSourceBuilder();
        if (StringUtils.isNotEmpty(startTime) && StringUtils.isNotEmpty(endTime)) {
            String startTimeFormat = DateUtil.format(DateUtil.beginOfDay(DateUtil.parse(startTime, DateUtils.YYYY_MM_DD)), DateUtils.YYYY_MM_DD_HH_MM_SS);
            String endTimeFormat = DateUtil.format(DateUtil.endOfDay(DateUtil.parse(endTime, DateUtils.YYYY_MM_DD)), DateUtils.YYYY_MM_DD_HH_MM_SS);
            if (StatisticsMediaBindStatusEnum.ALL.getValue().equals(statisticsOnlineReqVo.getMediaBindStatus())) {
                boolQuery.must(QueryBuilders.rangeQuery("publishTime").gte(startTimeFormat).lte(endTimeFormat));
            } else if (StatisticsMediaBindStatusEnum.BIND.getValue().equals(statisticsOnlineReqVo.getMediaBindStatus())) {
                boolQuery.must(QueryBuilders.rangeQuery("relationPublishTime").gte(startTimeFormat).lte(endTimeFormat));
            }
        }
        if (Boolean.TRUE.equals(statusFlag)) {
            boolQuery.must(QueryBuilders.termQuery("statusFlag", BmsContentStatusFlagEnum.NEW.getCode()));
        }
        if (null != bmsSpChannelId) {
            boolQuery.must(QueryBuilders.matchQuery("bmsSpChannelId", bmsSpChannelId));
            vo.setBmsSpChannelId(bmsSpChannelId);
            List<SysSp> sysSps = sysSpMapper.selectList(new LambdaQueryWrapper<SysSp>().eq(SysSp::getBmsSpChannelId, bmsSpChannelId));
            List<Long> spIdChannelList = sysSps.stream().map(SysSp::getId).collect(Collectors.toList());
            spIdList.retainAll(spIdChannelList);
        }
        TermsAggregationBuilder size = AggregationBuilders.terms("bms_program_group").field("cmsContentCode").size(Integer.MAX_VALUE);
        size.subAggregation(AggregationBuilders.max("durationSum").field("duration"));
        size.subAggregation(AggregationBuilders.max("fileSizeSum").field("fileSize"));
        searchSourceBuilder.aggregation(size);
        //计算介质时长及文件大小
        searchSourceBuilder.aggregation(PipelineAggregatorBuilders.statsBucket("stats_durationSum", "bms_program_group.durationSum"));
        searchSourceBuilder.aggregation(PipelineAggregatorBuilders.statsBucket("stats_fileSizeSum", "bms_program_group.fileSizeSum"));
        searchSourceBuilder.aggregation(PipelineAggregatorBuilders.statsBucket("stats_count", "bms_program_group._count"));
        if (null != spId) {
            boolQuery.must(QueryBuilders.termQuery("spId", spId));
            vo.setSpId(spId);
        } else {
            //针对某个渠道下全部SP，则按SP去重统计。例如，同一个节目在移动中兴和移动华为平台均已上线，在移动侧统计时，算一个节目上线
            boolQuery.must(QueryBuilders.termsQuery("spId", spIdList));
        }
        if (null != cpId) {
            boolQuery.must(QueryBuilders.termQuery("cpId", cpId));
            vo.setCpId(cpId);
        } else {
            boolQuery.must(QueryBuilders.termsQuery("cpId", cpIdList));
        }
        if (null != definitionFlag) {
            boolQuery.must(QueryBuilders.termQuery("definitionFlag", definitionFlag));
            vo.setDefinitionFlag(definitionFlag);
        }
        if (!StatisticsMediaBindStatusEnum.ALL.getValue().equals(mediaBindStatus)) {
            boolQuery.must(QueryBuilders.termQuery("bindStatus", mediaBindStatus));
        }
        //限制发布状态
        ArrayList<Integer> integers = new ArrayList<>();
        integers.add(PublishStatusEnum.PUBLISH.getCode());
        integers.add(PublishStatusEnum.WAITUPDATE.getCode());
        integers.add(PublishStatusEnum.UPDATING.getCode());
        integers.add(PublishStatusEnum.FAILUPDATE.getCode());
        boolQuery.must(QueryBuilders.termsQuery("publishStatus", integers));
        searchSourceBuilder.query(boolQuery);
        searchSourceBuilder.size(0);
        searchSourceBuilder.trackTotalHits(true);
        searchRequest.source(searchSourceBuilder);
        SearchResponse searchResponse = search(searchRequest);
        log.info("ES搜索语句={}", searchRequest.source().toString());
        //获取数量
        long count;
        long durationSum;
        long fileSizeSum;
        Aggregations aggregations = searchResponse.getAggregations();
        ParsedStatsBucket statsDurationSum = aggregations.get("stats_durationSum");
        ParsedStatsBucket statsFileSizeSum = aggregations.get("stats_fileSizeSum");
        ParsedStatsBucket statsCount = aggregations.get("stats_count");
        count = statsCount.getCount();
        durationSum = (long) statsDurationSum.getSum();
        fileSizeSum = (long) statsFileSizeSum.getSum();
        vo.setCountSubset(count);
        vo.setDurationSubset(durationSum);
        vo.setFileSizeSubset(fileSizeSum);
        log.info("==========getOnlineSubsetCommon整体耗时{}ms", System.currentTimeMillis() - l);
        return vo;
    }


    private StatisticsOnlineRespVo getNewOnlineSubsetCommon(String index, StatisticsOnlineReqVo statisticsOnlineReqVo,
                                                            StatisticsOnlineRespVo vo, Boolean statusFlag, HashMap<String, List<Long>> permissionMap, Boolean flag) {
        long l = System.currentTimeMillis();
        List<Long> spIdList;
        List<Long> cpIdList;
        if (flag) {
            if (null == permissionMap) {
                permissionMap = getPermission();
            }
            spIdList = permissionMap.get("spIdList");
            cpIdList = permissionMap.get("cpIdList");
        } else {
            spIdList = sysSpMapper.selectList(new LambdaQueryWrapper<SysSp>().ne(SysSp::getStatus, StatusEnum.DELETE.getCode())).stream().map(SysSp::getId).collect(Collectors.toList());
            cpIdList = sysCpMapper.selectList(new LambdaQueryWrapper<SysCp>().ne(SysCp::getStatus, StatusEnum.DELETE.getCode())).stream().map(SysCp::getId).collect(Collectors.toList());
        }
        SearchRequest searchRequest = new SearchRequest(index);
        BoolQueryBuilder boolQuery = QueryBuilders.boolQuery();
        Long bmsSpChannelId = statisticsOnlineReqVo.getBmsSpChannelId();
        Long spId = statisticsOnlineReqVo.getSpId();
        Long cpId = statisticsOnlineReqVo.getCpId();
        Integer definitionFlag = statisticsOnlineReqVo.getDefinitionFlag();
        Integer mediaBindStatus = statisticsOnlineReqVo.getMediaBindStatus();
        String startTime = statisticsOnlineReqVo.getStartTime();
        String endTime = statisticsOnlineReqVo.getEndTime();
        SearchSourceBuilder searchSourceBuilder = new SearchSourceBuilder();
        if (StringUtils.isNotEmpty(startTime) && StringUtils.isNotEmpty(endTime)) {
            String startTimeFormat = DateUtil.format(DateUtil.beginOfDay(DateUtil.parse(startTime, DateUtils.YYYY_MM_DD)), DateUtils.YYYY_MM_DD_HH_MM_SS);
            String endTimeFormat = DateUtil.format(DateUtil.endOfDay(DateUtil.parse(endTime, DateUtils.YYYY_MM_DD)), DateUtils.YYYY_MM_DD_HH_MM_SS);
            if (StatisticsMediaBindStatusEnum.ALL.getValue().equals(statisticsOnlineReqVo.getMediaBindStatus())) {
                boolQuery.must(QueryBuilders.rangeQuery("publishTime").gte(startTimeFormat).lte(endTimeFormat));
            } else if (StatisticsMediaBindStatusEnum.BIND.getValue().equals(statisticsOnlineReqVo.getMediaBindStatus())) {
                boolQuery.must(QueryBuilders.termQuery("bindStatus", StatisticsMediaBindStatusEnum.BIND.getValue()));
                boolQuery.must(QueryBuilders.rangeQuery("relationPublishTime").gte(startTimeFormat).lte(endTimeFormat));
            } else if (StatisticsMediaBindStatusEnum.UNBIND.getValue().equals(mediaBindStatus)) {
                boolQuery.must(QueryBuilders.rangeQuery("publishTime").gte(startTimeFormat).lte(endTimeFormat));
            } else {
                log.error("错误的绑定状态bindStatus={}", mediaBindStatus);
            }
        } else {
            if (StatisticsMediaBindStatusEnum.ALL.getValue().equals(mediaBindStatus)) {
                log.info("单节目上线无需考虑绑定关系");
            } else if (StatisticsMediaBindStatusEnum.BIND.getValue().equals(mediaBindStatus)) {
                boolQuery.must(QueryBuilders.termQuery("bindStatus", StatisticsMediaBindStatusEnum.BIND.getValue()));
            } else if (StatisticsMediaBindStatusEnum.UNBIND.getValue().equals(mediaBindStatus)) {
                log.info("节目未绑栏目上线无需考虑绑定关系");
            } else {
                log.error("错误的绑定状态bindStatus={}", mediaBindStatus);
            }
        }
        if (Boolean.TRUE.equals(statusFlag)) {
            boolQuery.must(QueryBuilders.termQuery("statusFlag", BmsContentStatusFlagEnum.NEW.getCode()));
        }
        if (null != bmsSpChannelId) {
            boolQuery.must(QueryBuilders.matchQuery("bmsSpChannelId", bmsSpChannelId));
            vo.setBmsSpChannelId(bmsSpChannelId);
            List<SysSp> sysSps = sysSpMapper.selectList(new LambdaQueryWrapper<SysSp>().eq(SysSp::getBmsSpChannelId, bmsSpChannelId));
            List<Long> spIdChannelList = sysSps.stream().map(SysSp::getId).collect(Collectors.toList());
            spIdList.retainAll(spIdChannelList);
        }
        TermsAggregationBuilder size = AggregationBuilders.terms("bms_program_group").field("cmsContentCode").size(Integer.MAX_VALUE);
        size.subAggregation(AggregationBuilders.max("durationSum").field("duration"));
        size.subAggregation(AggregationBuilders.max("fileSizeSum").field("fileSize"));
        if (StatisticsMediaBindStatusEnum.UNBIND.getValue().equals(statisticsOnlineReqVo.getMediaBindStatus())) {
            size.subAggregation(AggregationBuilders.min("min_bindStatus").field("bindStatus"));
            HashMap<String, Object> map = new HashMap<>(2);
            map.put("v0", "INTEGER");
            map.put("v1", 2);
            String scriptString = "InternalQlScriptUtils.nullSafeFilter(InternalQlScriptUtils.gt(InternalQlScriptUtils.nullSafeCastNumeric(params.minBindStatus,params.v0),params.v1))";
            Script script = new Script(ScriptType.INLINE, "painless", scriptString, map);
            Map<String, String> bucketsPathsMap = new HashMap<>(2);
            bucketsPathsMap.put("minBindStatus", "min_bindStatus");
            BucketSelectorPipelineAggregationBuilder bs = PipelineAggregatorBuilders.bucketSelector("having", bucketsPathsMap, script);
            size.subAggregation(bs);
        }
        searchSourceBuilder.aggregation(size);
        //计算介质时长及文件大小
        searchSourceBuilder.aggregation(PipelineAggregatorBuilders.statsBucket("stats_durationSum", "bms_program_group.durationSum"));
        searchSourceBuilder.aggregation(PipelineAggregatorBuilders.statsBucket("stats_fileSizeSum", "bms_program_group.fileSizeSum"));
        searchSourceBuilder.aggregation(PipelineAggregatorBuilders.statsBucket("stats_count", "bms_program_group._count"));
        if (null != spId) {
            boolQuery.must(QueryBuilders.termQuery("spId", spId));
            vo.setSpId(spId);
        } else {
            //针对某个渠道下全部SP，则按SP去重统计。例如，同一个节目在移动中兴和移动华为平台均已上线，在移动侧统计时，算一个节目上线
            boolQuery.must(QueryBuilders.termsQuery("spId", spIdList));
        }
        if (null != cpId) {
            boolQuery.must(QueryBuilders.termQuery("cpId", cpId));
            vo.setCpId(cpId);
        } else {
            boolQuery.must(QueryBuilders.termsQuery("cpId", cpIdList));
        }
        if (null != definitionFlag) {
            boolQuery.must(QueryBuilders.termQuery("definitionFlag", definitionFlag));
            vo.setDefinitionFlag(definitionFlag);
        }
        //限制发布状态
        ArrayList<Integer> integers = new ArrayList<>();
        integers.add(PublishStatusEnum.PUBLISH.getCode());
        integers.add(PublishStatusEnum.WAITUPDATE.getCode());
        integers.add(PublishStatusEnum.UPDATING.getCode());
        integers.add(PublishStatusEnum.FAILUPDATE.getCode());
        boolQuery.must(QueryBuilders.termsQuery("publishStatus", integers));
        searchSourceBuilder.query(boolQuery);
        searchSourceBuilder.size(0);
        searchSourceBuilder.trackTotalHits(true);
        searchRequest.source(searchSourceBuilder);
        SearchResponse searchResponse = search(searchRequest);
        log.info("ES搜索语句={}", searchRequest.source().toString());
        //获取数量
        long count;
        long durationSum;
        long fileSizeSum;
        Aggregations aggregations = searchResponse.getAggregations();
        ParsedStatsBucket statsDurationSum = aggregations.get("stats_durationSum");
        ParsedStatsBucket statsFileSizeSum = aggregations.get("stats_fileSizeSum");
        ParsedStatsBucket statsCount = aggregations.get("stats_count");
        count = statsCount.getCount();
        durationSum = (long) statsDurationSum.getSum();
        fileSizeSum = (long) statsFileSizeSum.getSum();
        vo.setCountSubset(count);
        vo.setDurationSubset(durationSum);
        vo.setFileSizeSubset(fileSizeSum);
        log.info("==========getNewOnlineSubsetCommon整体耗时{}ms", System.currentTimeMillis() - l);
        return vo;
    }

    public HashMap<String, List<Long>> getPermission() {
        HashMap<String, List<Long>> map = new HashMap<>();
        List<Long> spIdList = new ArrayList<>();
        List<Long> cpIdList = new ArrayList<>();
        SecurityUser securityUser = JwtTokenUtil.getSecurityUser();
        String spIds = securityUser.getSpIds();
        String cpIds = securityUser.getCpIds();
        Integer type = securityUser.getType();
        if (SysUserTypeEnum.SUPER_ADMIN.getCode().equals(type) || SysUserTypeEnum.ADMIN.getCode().equals(type)) {
            List<SysCp> sysCps = sysCpMapper.selectList(new LambdaQueryWrapper<SysCp>().ne(SysCp::getStatus, StatusEnum.DELETE.getCode()).select(SysCp::getId));
            cpIdList = sysCps.stream().map(SysCp::getId).collect(Collectors.toList());
            List<SysSp> sysSps = sysSpMapper.selectList(new LambdaQueryWrapper<SysSp>().ne(SysSp::getStatus, StatusEnum.DELETE.getCode()).select(SysSp::getId));
            spIdList = sysSps.stream().map(SysSp::getId).collect(Collectors.toList());
        }
        if (StringUtils.isNotEmpty(spIds)) {
            String[] split = spIds.split(",");
            spIdList = Arrays.stream(split).map(s -> Long.parseLong(s.trim())).collect(Collectors.toList());
        }
        if (StringUtils.isNotEmpty(cpIds)) {
            String[] split = cpIds.split(",");
            cpIdList = Arrays.stream(split).map(s -> Long.parseLong(s.trim())).collect(Collectors.toList());
        }
        map.put("spIdList", spIdList);
        map.put("cpIdList", cpIdList);
        return map;
    }

    @Override
    public BmsContentESModel selectBmsContentByIdAndStatusFlag(Long bmsContentId) {
        SearchRequest searchRequest = new SearchRequest(bmsContent);
        SearchSourceBuilder searchSourceBuilder = new SearchSourceBuilder();
        BoolQueryBuilder boolQuery = QueryBuilders.boolQuery();
        boolQuery.must(QueryBuilders.termQuery("id", bmsContentId));
        boolQuery.must(QueryBuilders.termQuery("statusFlag", BmsContentStatusFlagEnum.NEW.getCode()));
        searchSourceBuilder.query(boolQuery);
        searchRequest.source(searchSourceBuilder);
        log.info("ES搜索语句={}", searchRequest.source().toString());
        SearchResponse searchResponse = search(searchRequest);
        SearchHit[] hits = searchResponse.getHits().getHits();
        if (hits.length <= 0) {
            return null;
        }
        return objectMapper.convertValue(hits[0].getSourceAsMap(), BmsContentESModel.class);
    }

    @Override
    public List<BmsProgramESModel> selectBmsProgramByIdsAndStatusFlag(List<Long> programIds) {
        SearchRequest searchRequest = new SearchRequest(bmsProgram);
        SearchSourceBuilder searchSourceBuilder = new SearchSourceBuilder();
        BoolQueryBuilder boolQuery = QueryBuilders.boolQuery();
        boolQuery.must(QueryBuilders.termsQuery("id", programIds));
        boolQuery.must(QueryBuilders.termQuery("statusFlag", BmsContentStatusFlagEnum.NEW.getCode()));
        searchSourceBuilder.query(boolQuery);
        searchRequest.source(searchSourceBuilder);
        log.info("ES搜索语句={}", searchRequest.source().toString());
        SearchResponse searchResponse = search(searchRequest);
        SearchHit[] hits = searchResponse.getHits().getHits();
        List<BmsProgramESModel> list = new ArrayList<>();
        if (hits.length <= 0) {
            return list;
        }
        for (SearchHit hit : hits) {
            BmsProgramESModel t = objectMapper.convertValue(hit.getSourceAsMap(), BmsProgramESModel.class);
            list.add(t);
        }
        return list;
    }

    @Override
    public List<BmsProgramESModel> selectBmsProgramByCondition(Long cmsContentId, Long spId) {
        SearchRequest searchRequest = new SearchRequest(bmsProgram);
        SearchSourceBuilder searchSourceBuilder = new SearchSourceBuilder();
        BoolQueryBuilder boolQuery = QueryBuilders.boolQuery();
        boolQuery.must(QueryBuilders.termQuery("cmsSeriesId", cmsContentId));
        boolQuery.must(QueryBuilders.termQuery("statusFlag", BmsContentStatusFlagEnum.NEW.getCode()));
        boolQuery.mustNot(QueryBuilders.termQuery("publishStatus", PublishStatusEnum.ROLLBACK.getCode()));
        boolQuery.must(QueryBuilders.termQuery("spId", spId));
        searchSourceBuilder.query(boolQuery);
        searchRequest.source(searchSourceBuilder);
        log.info("ES搜索语句={}", searchRequest.source().toString());
        SearchResponse searchResponse = search(searchRequest);
        SearchHit[] hits = searchResponse.getHits().getHits();
        List<BmsProgramESModel> list = new ArrayList<>();
        if (hits.length <= 0) {
            return list;
        }
        for (SearchHit hit : hits) {
            BmsProgramESModel t = objectMapper.convertValue(hit.getSourceAsMap(), BmsProgramESModel.class);
            list.add(t);
        }
        return list;
    }

    @Override
    public List<BmsPackageContentESModel> selectBmsPackageContentByCondition(Long bmsContentId) {
        SearchRequest searchRequest = new SearchRequest(bmsPackageContent);
        SearchSourceBuilder searchSourceBuilder = new SearchSourceBuilder();
        BoolQueryBuilder boolQuery = QueryBuilders.boolQuery();
        boolQuery.must(QueryBuilders.termQuery("bmsContentId", bmsContentId));
        boolQuery.must(QueryBuilders.termQuery("statusFlag", BmsContentStatusFlagEnum.NEW.getCode()));
        boolQuery.mustNot(QueryBuilders.termQuery("publishStatus", PublishStatusEnum.ROLLBACK.getCode()));
        searchSourceBuilder.query(boolQuery);
        searchRequest.source(searchSourceBuilder);
        log.info("ES搜索语句={}", searchRequest.source().toString());
        SearchResponse searchResponse = search(searchRequest);
        SearchHit[] hits = searchResponse.getHits().getHits();
        List<BmsPackageContentESModel> list = new ArrayList<>();
        if (hits.length <= 0) {
            return list;
        }
        for (SearchHit hit : hits) {
            BmsPackageContentESModel t = objectMapper.convertValue(hit.getSourceAsMap(), BmsPackageContentESModel.class);
            list.add(t);
        }
        return list;
    }

    @Override
    public List<BmsCategoryContentESModel> selectBmsCategoryContentByCondition(Long bmsContentId) {
        SearchRequest searchRequest = new SearchRequest(bmsCategoryContent);
        SearchSourceBuilder searchSourceBuilder = new SearchSourceBuilder();
        BoolQueryBuilder boolQuery = QueryBuilders.boolQuery();
        boolQuery.must(QueryBuilders.termQuery("bmsContentId", bmsContentId));
        boolQuery.must(QueryBuilders.termQuery("statusFlag", BmsContentStatusFlagEnum.NEW.getCode()));
        boolQuery.mustNot(QueryBuilders.termQuery("publishStatus", PublishStatusEnum.ROLLBACK.getCode()));
        searchSourceBuilder.query(boolQuery);
        searchRequest.source(searchSourceBuilder);
        log.info("ES搜索语句={}", searchRequest.source().toString());
        SearchResponse searchResponse = search(searchRequest);
        SearchHit[] hits = searchResponse.getHits().getHits();
        List<BmsCategoryContentESModel> list = new ArrayList<>();
        if (hits.length <= 0) {
            return list;
        }
        for (SearchHit hit : hits) {
            BmsCategoryContentESModel t = objectMapper.convertValue(hit.getSourceAsMap(), BmsCategoryContentESModel.class);
            list.add(t);
        }
        return list;
    }

    @Override
    public BmsContentESModel selectBmsContentById(Long bmsContentId) {
        if (null == bmsContentId) {
            return null;
        }
        SearchRequest searchRequest = new SearchRequest(bmsContent);
        SearchSourceBuilder searchSourceBuilder = new SearchSourceBuilder();
        BoolQueryBuilder boolQuery = QueryBuilders.boolQuery();
        boolQuery.must(QueryBuilders.termQuery("id", bmsContentId));
        searchSourceBuilder.query(boolQuery);
        searchSourceBuilder.size(1);
        searchSourceBuilder.sort("updateTime", SortOrder.DESC);
        searchRequest.source(searchSourceBuilder);
        log.info("ES搜索语句={}", searchRequest.source().toString());
        SearchResponse searchResponse = search(searchRequest);
        SearchHit[] hits = searchResponse.getHits().getHits();
        if (hits.length <= 0) {
            return null;
        }
        return objectMapper.convertValue(hits[0].getSourceAsMap(), BmsContentESModel.class);
    }

    public List<String> getLspPermission() {
        List<String> lspIdList = new ArrayList<>();
        SecurityUser securityUser = JwtTokenUtil.getSecurityUser();
        String spIds = securityUser.getSpIds();
        Integer type = securityUser.getType();
        if (StringUtils.isNotEmpty(spIds)) {
            String[] split = spIds.split(",");
            List<Long> spIdLongList = Arrays.stream(split).map(s -> Long.parseLong(s.trim())).collect(Collectors.toList());
            lspIdList = sysSpMapper.selectLspIdListBySpIds(spIdLongList);
        }
        if (SysUserTypeEnum.SUPER_ADMIN.getCode().equals(type) || SysUserTypeEnum.ADMIN.getCode().equals(type)) {
            List<SysOutPassage> sysOutPassages = sysOutPassageMapper.selectList(new LambdaQueryWrapper<SysOutPassage>().ne(SysOutPassage::getStatus, StatusEnum.DELETE.getCode()));
            lspIdList = sysOutPassages.stream().map(SysOutPassage::getLspId).collect(Collectors.toList());
        }
        return lspIdList;
    }
}