package com.pukka.iptv.statistics.job;

import com.pukka.iptv.statistics.service.job.JobService;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Date;

/**
 * <AUTHOR>
 * @description 定时任务
 * @date 2022/7/13
 */
@Slf4j
@Component
@RefreshScope
public class StatisticsXxlJob {

    private final Logger logger = LoggerFactory.getLogger(StatisticsXxlJob.class);

    @Resource
    private JobService jobService;

    /**
     * 定时统计每个存储磁盘总容量
     */
    @XxlJob("statisticDiskSpaceHandler")
    public ReturnT<String> statisticDiskSpaceHandler(String param) {
        logger.info("XXL-JOB, statisticDiskSpaceHandler.time={}", new Date());
        try {
            jobService.statisticDiskSpace();
            return ReturnT.SUCCESS;
        } catch (Exception e) {
            log.error("定时统计ftp磁盘总容量异常,e={}", e.fillInStackTrace());
            return ReturnT.FAIL;
        }
    }

    /**
     * 定时统计每个CP的磁盘使用量
     */
    @XxlJob("statisticDiskSpaceByCpHandler")
    public ReturnT<String> statisticDiskSpaceByCpHandler(String param) {
        logger.info("XXL-JOB, statisticDiskSpaceByCpHandler.time={}", new Date());
        try {
            jobService.statisticDiskSpaceByCp();
            return ReturnT.SUCCESS;
        } catch (Exception e) {
            log.error("定时统计每个CP的磁盘使用量异常,e={}", e.fillInStackTrace());
            return ReturnT.FAIL;
        }
    }

    /**
     * 定时统计刷新每个CP的磁盘使用量redis缓存
     */
    @XxlJob("statisticDiskSpaceRefreshByCpHandler")
    public ReturnT<String> statisticDiskSpaceRefreshByCpHandler(String param) {
        logger.info("XXL-JOB, statisticDiskSpaceRefreshByCpHandler.time={}", new Date());
        try {
            jobService.statisticDiskSpaceRefreshByCp();
            return ReturnT.SUCCESS;
        } catch (Exception e) {
            e.printStackTrace();
            log.error("定时统计刷新每个CP的磁盘使用量redis缓存,e={}", e.getMessage());
            return ReturnT.FAIL;
        }
    }

}