package com.pukka.iptv.statistics.model;

import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.fastjson.annotation.JSONField;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springframework.data.elasticsearch.annotations.Document;

import java.io.Serializable;
import java.util.Date;

/**
 * 栏目内容
 *
 * <AUTHOR>
 * @date 2021-08-26 16:38:55
 */
@Data
@EqualsAndHashCode
@ApiModel("BmsContentESModel")
@Document(indexName = "bms_category_content")
@JsonIgnoreProperties(value = {"handler"})
public class BmsCategoryContentESModel implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "id", dataType = "Long", name = "id")
    private Long id;

    @ApiModelProperty(value = "bms content表ID", dataType = "Long", name = "bmsContentId")
    private Long bmsContentId;

    @ApiModelProperty(value = "cms 表code", dataType = "String", name = "cmsContentCode")
    private String cmsContentCode;

    @ApiModelProperty(value = "媒资类型", dataType = "Integer", name = "contentType")
    private Integer contentType;

    @ApiModelProperty(value = "category表的ID", dataType = "Long", name = "categoryId")
    private Long categoryId;

    @ApiModelProperty(value = "category表的Code", dataType = "String", name = "categoryCode")
    private String categoryCode;

    @ApiModelProperty(value = "栏目名称", dataType = "String", name = "categoryName")
    private String categoryName;

    @ApiModelProperty(value = "spId", dataType = "Long", name = "spId")
    private Long spId;

    @ApiModelProperty(value = "SP名称", dataType = "String", name = "spName")
    private String spName;

    @ApiModelProperty(value = "cpId", dataType = "Long", name = "cpId")
    private Long cpId;

    @ApiModelProperty(value = "CP名称", dataType = "String", name = "cpName")
    private String cpName;

    @ApiModelProperty(value = "状态 0：失效 1：有效", dataType = "Integer", name = "status")
    private Integer status;

    @ApiModelProperty(value = "创建时间", dataType = "String", name = "createTime")
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @JSONField(format = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;

    @ApiModelProperty(value = "更新时间", dataType = "String", name = "updateTime")
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @JSONField(format = "yyyy-MM-dd HH:mm:ss")
    private Date updateTime;

    @ApiModelProperty(value = "排序号", dataType = "Long", name = "sequence")
    private Integer sequence;

    @ApiModelProperty(value = "媒资名称", dataType = "String", name = "contentName")
    private String contentName;

    @ApiModelProperty(value = "publishStatus", dataType = "Integer", name = "publishStatus")
    private Integer publishStatus;

    @ApiModelProperty(value = "关系发布通道名称，用逗号分隔", dataType = "String", name = "outPassageNames")
    private String outPassageNames;

    @ApiModelProperty(value = "发布通道ids集合，用逗号分隔", dataType = "String", name = "outPassageIds")
    private String outPassageIds;

    @ApiModelProperty(value = "来源 1：专线注入 2：人工绑定", dataType = "Integer", name = "source")
    private Integer source;

    @ApiModelProperty(value = "关系发布时间", dataType = "String", name = "publishTime")
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @JSONField(format = "yyyy-MM-dd HH:mm:ss")
    private Date publishTime;

    @ApiModelProperty(value = "关系发布描述", dataType = "String", name = "publishDescription")
    private String publishDescription;

    @ApiModelProperty(value = "定时发布，时间存在则代表已经设定为定时发布", dataType = "String", name = "timedPublish")
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @JSONField(format = "yyyy-MM-dd HH:mm:ss")
    private Date timedPublish;

    @ApiModelProperty(value = "定时发布状态", dataType = "String", name = "timedPublishStatus")
    private Integer timedPublishStatus;

    @ApiModelProperty(value = "定时发布描述", dataType = "String", name = "timedPublishDescription")
    private String timedPublishDescription;

    @ApiModelProperty(value = "绑定人员", dataType = "String", name = "creatorName")
    private String creatorName;

    @ApiModelProperty(value = "creatorId", dataType = "Long", name = "creatorId")
    private Long creatorId;

    @ApiModelProperty(value = "记录状态（1=最新记录，2=历史记录）", dataType = "Integer", name = "statusFlag")
    private Integer statusFlag;

    @ApiModelProperty(value = "categoryCode、cmsContentCode、发布状态及spId通过_组合，仅用于发布回收统计的去重", dataType = "String", name = "categoryCodeCmsContentCodePublishStatusSpId")
    @ExcelIgnore
    private String categoryCodeCmsContentCodePublishStatusSpId;
}
