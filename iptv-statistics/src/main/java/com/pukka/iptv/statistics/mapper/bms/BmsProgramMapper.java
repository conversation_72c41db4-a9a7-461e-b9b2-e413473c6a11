package com.pukka.iptv.statistics.mapper.bms;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.pukka.iptv.common.data.model.bms.BmsProgram;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 *
 * @author: liuli
 * @date: 2021年9月2日 下午3:35:55
 */

@Mapper
public interface BmsProgramMapper extends BaseMapper<BmsProgram>{

    List<Long> selectIdListByBmsContentId(Long bmsContentId);

    List<Long> selectIdsByCondition(@Param("bmsContentId") Long bmsContentId);
}
