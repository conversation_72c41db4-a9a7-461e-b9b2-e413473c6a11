package com.pukka.iptv.statistics.mapper.bms;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.pukka.iptv.common.data.model.bms.BmsCategoryContent;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

/**
 * @author: liuli
 * @date: 2021年9月2日 下午3:34:52
 */

@Mapper
public interface BmsCategoryContentMapper extends BaseMapper<BmsCategoryContent> {

    List<BmsCategoryContent> selectListMy();

    List<BmsCategoryContent> selectListContentMy();

    List<Map<String, Object>> selectProgramListMy(@Param("bmsContentIds") List<Long> bmsContentIds);

    String selectNewRelationPublishTime(Long bmsContentId);
}
