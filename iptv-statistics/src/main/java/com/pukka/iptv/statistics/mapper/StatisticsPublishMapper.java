package com.pukka.iptv.statistics.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.pukka.iptv.statistics.model.StatisticsPublish;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * @author: zhengcl
 * @date: 2022年7月13日 上午9:55:36
 */

@Mapper
public interface StatisticsPublishMapper extends BaseMapper<StatisticsPublish> {

    Integer batchInsert(@Param("statisticsPublishes") List<StatisticsPublish> statisticsPublishes);
}
