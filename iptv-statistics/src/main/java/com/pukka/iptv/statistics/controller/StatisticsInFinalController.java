package com.pukka.iptv.statistics.controller;

import com.pukka.iptv.common.base.vo.CommonResponse;
import com.pukka.iptv.common.data.model.statistics.StatisticsInCheck;
import com.pukka.iptv.common.data.model.statistics.StatisticsInFinal;
import javax.validation.Valid;

import com.pukka.iptv.common.data.vo.req.StatisticsInVo;
import com.pukka.iptv.common.data.vo.req.StatisticsInVoD;
import com.pukka.iptv.statistics.job.StatisticFinalInitJob;
import com.pukka.iptv.statistics.service.StatisticsInFinalService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.*;
import org.springframework.beans.factory.annotation.Autowired;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.AllArgsConstructor;
import com.pukka.iptv.common.data.vo.*;

import java.util.List;

/**
 *
 * @author: tan
 * @date: 2022-8-2 9:15:21
 */

@RestController
@AllArgsConstructor
@RequestMapping(value = "/statisticsInFinal", produces = MediaType.APPLICATION_JSON_VALUE)
@Api(tags="statisticsInFinal管理")
public class StatisticsInFinalController {

    @Autowired
    private StatisticsInFinalService statisticsInFinalService;

    @Autowired
    private StatisticFinalInitJob statisticFinalInitJob;

    @ApiOperation(value = "获取所有审核统计")
    @PostMapping("/all" )
    public CommonResponse<List<StatisticsInFinal>> all(@RequestBody StatisticsInVo statisticsInVo) {
        return  statisticsInFinalService.getSelfAllStatistics(statisticsInVo);
    }

    @ApiOperation(value = "获取最近一个月所有审核统计")
    @PostMapping("/lastMonth" )
    public CommonResponse<List<StatisticsInFinal>> lastMonth(@RequestBody StatisticsInVoD statisticsInVo) {
        return  statisticsInFinalService.getSelfAllStatisticsLastMonth(statisticsInVo);
    }

    @ApiOperation(value = "任务")
    @GetMapping("/job" )
    public CommonResponse job() {
        statisticFinalInitJob.executeJob();
        return  CommonResponse.success(true);
    }

}
