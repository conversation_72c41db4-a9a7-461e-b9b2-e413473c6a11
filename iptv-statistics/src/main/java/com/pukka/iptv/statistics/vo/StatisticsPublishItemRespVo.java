package com.pukka.iptv.statistics.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.util.Date;

/**
 * @author: zhengcl
 * @date: 2022年7月13日 上午9:55:27
 */

@Data
@EqualsAndHashCode
@JsonIgnoreProperties(value = {"handler"})
@Accessors(chain = true)
public class StatisticsPublishItemRespVo implements java.io.Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value="统计类型 1=发布 2=回收",dataType="Integer",name="type")
    private Integer type;

    @ApiModelProperty(value="工单类型 1=内容、2=产品包、3=栏目关系",dataType="Integer",name="workOrderType")
    private Integer workOrderType;

    @ApiModelProperty(value = "节目id", dataType = "Long", name = "bmsContentId")
    private Long bmsContentId;

    @ApiModelProperty(value = "节目名称", dataType = "String", name = "name")
    private String contentName;

    @ApiModelProperty(value = "产品包名称", dataType = "String", name = "packageName")
    private String packageName;

    @ApiModelProperty(value = "栏目名称", dataType = "String", name = "categoryName")
    private String categoryName;

    @ApiModelProperty(value = "媒资分类ID，来自pgm_category表", dataType = "Long", name = "pgmCategoryId")
    private Long pgmCategoryId;

    @ApiModelProperty(value = "媒资分类，节目形态，如：新闻，电影", dataType = "String", name = "pgmCategory")
    private String pgmCategory;

    @ApiModelProperty(value = "发布状态  1：待发布 2：发布中 3：发布成功 4：发布失败 5：待更新 6：更新中 7：更新失败 8：回收中 9：回收成功 10：回收失败", dataType = "Integer", name = "publishStatus")
    private Integer publishStatus;

    @ApiModelProperty(value = "发布时间", dataType = "String", name = "publishTime")
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date publishTime;

    @ApiModelProperty(value = "集数，连续剧第几集", dataType = "Long", name = "episodeIndex")
    private Integer episodeIndex;

    @ApiModelProperty(value = "cpId", dataType = "Long", name = "cpId")
    private Long cpId;

    @ApiModelProperty(value = "CP名称", dataType = "String", name = "cpName")
    private String cpName;

    @ApiModelProperty(value = "spId", dataType = "Long", name = "spId")
    private Long spId;

    @ApiModelProperty(value = "SP名称", dataType = "String", name = "spName")
    private String spName;

    @ApiModelProperty(value = "所属渠道id", dataType = "Long", name = "bmsSpChannelId")
    private Long bmsSpChannelId;

    @ApiModelProperty(value = "所属渠道", dataType = "String", name = "bmsSpChannelName")
    private String bmsSpChannelName;

    @ApiModelProperty(value = "发布通道ID，多个ID以英文逗号隔开", dataType = "String", name = "outPassageIds")
    private String outPassageIds;

    @ApiModelProperty(value = "分发通道名称以英文逗号 隔开", dataType = "String", name = "outPassageNames")
    private String outPassageNames;

}
