package com.pukka.iptv.statistics.controller;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.pukka.iptv.statistics.model.FTPDiskModel;
import com.pukka.iptv.statistics.service.statistics.StatisticsDiskSpaceService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.AllArgsConstructor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * @author: wangrh
 * @description: 磁盘空间查询接口，面向Xupload上传工具
 * @date: 2022-07-14
 **/
@RestController
@AllArgsConstructor
@RequestMapping(value = "/FTPDisk", produces = MediaType.APPLICATION_JSON_VALUE)
@Api(tags = "磁盘空间查询接口，面向Xupload上传工具")
public class FtpDiskController {

    @Autowired
    private StatisticsDiskSpaceService statisticsDiskSpaceService;

    @ApiOperation(value = "磁盘使用情况列表")
    @GetMapping("/getAll")
    public JSONObject getDiskSpaceByCp(long cpId) {
        FTPDiskModel diskSpaceByCp = statisticsDiskSpaceService.getDiskSpaceByCp(cpId);
        return JSON.parseObject(JSON.toJSONString(diskSpaceByCp));
    }

}
