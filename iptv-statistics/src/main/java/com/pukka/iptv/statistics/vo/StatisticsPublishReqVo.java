package com.pukka.iptv.statistics.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 *
 * @author: zhengcl
 * @description: 发布回收统计入参模型
 * @date: 2022年7月13日 上午9:55:36
 */
@Data
@EqualsAndHashCode
@ApiModel("发布回收统计相关接口入参模型")
@Accessors(chain = true)
public class StatisticsPublishReqVo implements java.io.Serializable{

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value="cpId",dataType="Long",name="cpId")
    private Long cpId;

    @ApiModelProperty(value="spId",dataType="Long",name="spId")
    private Long spId;

    @ApiModelProperty(value="所属渠道id",dataType="Long",name="bmsSpChannelId")
    private Long bmsSpChannelId;

    @ApiModelProperty(value="开始时间",dataType="String",name="startTime")
    private String startTime;

    @ApiModelProperty(value="结束时间",dataType="String",name="endTime")
    private String endTime;

    @ApiModelProperty(value="内容类型 1：电影  2：子集 3：电视剧 4：系列片 5：片花(仅明细查询需传)",dataType="Integer",name="contentType")
    private Integer contentType;

    @ApiModelProperty(value="工单类型 1=内容、2=产品包、3=栏目关系",dataType="Integer",name="workOrderType")
    private Integer workOrderType;

    @ApiModelProperty(value="发布回收类型 1=发布、2=回收",dataType="Integer",name="type")
    private Integer type;

}
