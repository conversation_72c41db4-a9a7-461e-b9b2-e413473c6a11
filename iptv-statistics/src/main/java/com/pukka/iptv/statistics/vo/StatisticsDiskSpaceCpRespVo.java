package com.pukka.iptv.statistics.vo;

import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelProperty;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 *
 * @author: zhengcl
 * @date: 2022年7月13日 下午5:23:51
 */
@Data
@EqualsAndHashCode
@JsonIgnoreProperties(value = { "handler" })
@Accessors(chain = true)
public class StatisticsDiskSpaceCpRespVo implements java.io.Serializable{

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value="cpId",dataType="Long",name="cpId")
    @ExcelIgnore
    private Long cpId;

    @ApiModelProperty(value="CP名称",dataType="String",name="cpName")
    @ExcelProperty(value = "CP名称", order = 1)
    private String cpName;

    @ApiModelProperty(value="总空间大小",dataType="Long",name="totalSpace")
    @ExcelProperty(value = "分配存储额度(GB)", order = 2)
    private Long totalSpace;

    @ApiModelProperty(value="已使用磁盘空间大小",dataType="Double",name="usedSpace")
    @ExcelProperty(value = "存储使用量(GB)", order = 3)
    private Double usedSpace;

    @ApiModelProperty(value="未使用磁盘空间大小",dataType="Long",name="freeSpace")
    @ExcelIgnore
    private Long freeSpace;

    @ApiModelProperty(value="当前已使用占被分配比例",dataType="java.lang.Double",name="usedProportion")
    @ExcelProperty(value = "使用占比", order = 4)
    private Double usedProportion;

    @ApiModelProperty(value="当前未使用占被分配比例",dataType="java.lang.Double",name="freeProportion")
    @ExcelIgnore
    private Double freeProportion;

    @ApiModelProperty(value="当前已使用占全部存储比例",dataType="java.lang.Double",name="usedProportionTotal")
    @ExcelProperty(value = "整体占比", order = 5)
    private Double usedProportionTotal;

    @ApiModelProperty(value="磁盘分配阈值",dataType="java.lang.Double",name="spaceThreshold")
    @ExcelIgnore
    private Double spaceThreshold;

    @ApiModelProperty(value="存储盘id",dataType="Long",name="storageId")
    @ExcelIgnore
    private Long storageId;

    @ApiModelProperty(value="统计时间",dataType="String",name="statisticDate")
    @ExcelProperty(value = "统计时间", order = 6)
    private String statisticDate;
}
