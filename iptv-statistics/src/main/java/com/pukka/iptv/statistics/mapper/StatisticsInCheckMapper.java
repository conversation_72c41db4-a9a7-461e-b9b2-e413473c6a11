package com.pukka.iptv.statistics.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.pukka.iptv.common.data.annotation.DataPermission;
import com.pukka.iptv.common.data.model.statistics.StatisticsIn;
import com.pukka.iptv.common.data.model.statistics.StatisticsInCheck;
import com.pukka.iptv.common.data.vo.req.StatisticsInVo;
import com.pukka.iptv.common.data.vo.req.StatisticsInVoD;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;


/**
 *
 * @author: tan
 * @date: 2022-7-21 9:15:48
 */

@Mapper
@DataPermission(config = "cp_id=cpIds")
public interface StatisticsInCheckMapper extends BaseMapper<StatisticsInCheck>{

    List<StatisticsInCheck> getSelfAllStatistics(@Param("param") StatisticsInVo statisticsInVo);

    List<StatisticsInCheck> getSelfAllStatisticsByAuth(@Param("param") StatisticsInVo statisticsInVo, @Param("cpIdList") List<Long> cpIdList);

    List<StatisticsInCheck> getSelfAllStatisticsLastMonth(@Param("param") StatisticsInVoD statisticsInVo);

    List<StatisticsInCheck> getSelfAllStatisticsLastMonthByAuth(@Param("param") StatisticsInVoD statisticsInVo, @Param("cpIdList") List<Long> cpIdList);
}
