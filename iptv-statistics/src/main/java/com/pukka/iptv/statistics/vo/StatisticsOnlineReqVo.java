package com.pukka.iptv.statistics.vo;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 *
 * @author: zhengcl
 * @description: 下发统计入参模型
 * @date: 2022年7月13日 上午9:55:27
 */
@Data
@EqualsAndHashCode
@ApiModel("下发统计相关接口入参模型")
@Accessors(chain = true)
public class StatisticsOnlineReqVo implements java.io.Serializable{

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value="节目清晰度标识 0：标清 1：高清 2：超清 3. 4K 4. 杜比（4K+杜比） 5.H264 6.H264(标清) 7.H264(高清) 8.H264(4K) 9.H265(高清) 10.H265(4K)",dataType="Integer",name="definitionFlag")
    private Integer definitionFlag;

    @ApiModelProperty(value="节目状态 1.单节目发布 2.节目已绑栏目发布 3.节目未绑栏目发布",dataType="Integer",name="mediaBindStatus")
    private Integer mediaBindStatus;

    @ApiModelProperty(value="cpId",dataType="Long",name="cpId")
    private Long cpId;

    @ApiModelProperty(value="spId",dataType="Long",name="spId")
    private Long spId;

    @ApiModelProperty(value="所属渠道id",dataType="Long",name="bmsSpChannelId")
    private Long bmsSpChannelId;

    @ApiModelProperty(value="开始时间",dataType="String",name="startTime")
    private String startTime;

    @ApiModelProperty(value="结束时间",dataType="String",name="endTime")
    private String endTime;

    @ApiModelProperty(value="柱状图查询类型（1=最近7天，2=最近30天），仅柱状图查询使用",dataType="Integer",name="flag")
    private Integer flag;

    @ApiModelProperty(value="统计维度（1=按CP维度，2=按SP维度），仅下发排行使用",dataType="Integer",name="type")
    private Integer type;

    @ApiModelProperty(value="媒资类型（1=单集，2=子集，3=剧头），仅下发排行使用",dataType="Integer",name="contentType")
    private Integer contentType;
}
