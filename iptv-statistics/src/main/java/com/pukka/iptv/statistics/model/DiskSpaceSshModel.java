package com.pukka.iptv.statistics.model;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * @author: wangrh
 * @description: ssh命令获取磁盘空间返回结果模型
 * @date: 2022-07-18
 **/
@Data
@EqualsAndHashCode
@JsonIgnoreProperties(value = { "handler" })
@Accessors(chain = true)
@ApiModel("ssh命令获取磁盘空间返回结果模型")
public class DiskSpaceSshModel {

    @ApiModelProperty(value="文件系统名称",dataType="String",name="fileSystem")
    private String fileSystem;

    @ApiModelProperty(value="总空间大小",dataType="Long",name="totalSize")
    private Long totalSize;

    @ApiModelProperty(value="已使用磁盘空间大小",dataType="Long",name="usedSize")
    private Long usedSize;

    @ApiModelProperty(value="未使用磁盘空间大小",dataType="Long",name="freeSize")
    private Long freeSize;

    @ApiModelProperty(value="已使用比例",dataType="Double",name="usedProportion")
    private Double usedProportion;

}
