package com.pukka.iptv.statistics.model;

import com.alibaba.excel.annotation.ExcelProperty;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * @author: wangrh
 * @description: Xupload工具请求磁盘情况返回模型
 * @date: 2022-07-14
 **/
@Data
@EqualsAndHashCode
@JsonIgnoreProperties(value = { "handler" })
@Accessors(chain = true)
public class FTPDiskModel implements java.io.Serializable{

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value="存储总大小，单位G",dataType="Integer",name="totalSpace")
    private String totalSpace;

    @ApiModelProperty(value="当前CP可用最大空间，单位G",dataType="Integer",name="maxSpace")
    private String maxSpace;

    @ApiModelProperty(value="当前CP已使用空间，单位G",dataType="Integer",name="usedSpace")
    private String usedSpace;
}
