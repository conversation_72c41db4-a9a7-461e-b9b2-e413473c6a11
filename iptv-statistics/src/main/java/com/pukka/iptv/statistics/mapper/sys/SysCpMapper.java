package com.pukka.iptv.statistics.mapper.sys;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.pukka.iptv.common.data.annotation.DataPermission;
import com.pukka.iptv.common.data.model.sys.SysCp;
import org.apache.ibatis.annotations.Mapper;

/**
 *
 * @author: luo
 * @date: 2021-9-9 8:36:53
 */

@Mapper
@DataPermission(config = "id=cpIds")
public interface SysCpMapper extends BaseMapper<SysCp>{

    Double selectSumTotalSpace(Long storageId);
}
