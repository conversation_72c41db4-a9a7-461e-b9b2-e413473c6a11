package com.pukka.iptv.statistics.handler;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateUtil;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.pukka.iptv.common.base.enums.*;
import com.pukka.iptv.common.core.util.DateUtils;
import com.pukka.iptv.common.core.util.StringUtils;
import com.pukka.iptv.common.data.model.bms.BmsCategoryContent;
import com.pukka.iptv.common.data.model.bms.BmsContent;
import com.pukka.iptv.common.data.model.bms.BmsPackageContent;
import com.pukka.iptv.common.data.model.bms.BmsProgram;
import com.pukka.iptv.common.data.model.cms.CmsResource;
import com.pukka.iptv.common.data.model.cms.CmsSeries;
import com.pukka.iptv.common.rabbitmq.config.CmsContentRenameMQConfig;
import com.pukka.iptv.common.rabbitmq.config.StatisticsOnlineMQConfig;
import com.pukka.iptv.statistics.mapper.bms.BmsCategoryContentMapper;
import com.pukka.iptv.statistics.mapper.bms.BmsContentMapper;
import com.pukka.iptv.statistics.mapper.cms.CmsResourceMapper;
import com.pukka.iptv.statistics.mapper.cms.CmsSeriesMapper;
import com.pukka.iptv.statistics.model.BmsCategoryContentESModel;
import com.pukka.iptv.statistics.model.BmsContentESModel;
import com.pukka.iptv.statistics.model.BmsPackageContentESModel;
import com.pukka.iptv.statistics.model.BmsProgramESModel;
import com.pukka.iptv.statistics.service.es.ESService;
import com.rabbitmq.client.Channel;
import lombok.extern.slf4j.Slf4j;
import org.springframework.amqp.core.Message;
import org.springframework.amqp.rabbit.annotation.Exchange;
import org.springframework.amqp.rabbit.annotation.Queue;
import org.springframework.amqp.rabbit.annotation.QueueBinding;
import org.springframework.amqp.rabbit.annotation.RabbitListener;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.io.IOException;
import java.util.*;

/**
 * @author: wangrh
 **/
@Component
@RefreshScope
@Slf4j
public class StatisticsMQHandler {

    @Value("${index.bmsContent}")
    private String bmsContent;
    @Value("${index.bmsProgram}")
    private String bmsProgram;
    @Value("${index.bmsCategoryContent}")
    private String bmsCategoryContent;
    @Value("${index.bmsPackageContent}")
    private String bmsPackageContent;
    @Value("${handler.flag}")
    private Boolean flag;

    @Resource
    private ESService esService;
    @Resource
    private BmsContentMapper bmsContentMapper;
    @Resource
    private BmsCategoryContentMapper bmsCategoryContentMapper;
    @Resource
    private CmsResourceMapper cmsResourceMapper;
    @Resource
    private CmsSeriesMapper cmsSeriesMapper;

    /**
     * 发布回收成功后埋点消费者
     */
    @RabbitListener(bindings = @QueueBinding(
            value = @Queue(StatisticsOnlineMQConfig.STATISTIC_ONLINE_QUEUE),
            exchange = @Exchange(StatisticsOnlineMQConfig.STATISTIC_ONLINE_EXCHANGE)
    ))
    public void publishEndHandler(Message mqMessage, Channel channel) {
        if (Boolean.TRUE.equals(flag)) {
            return;
        }
        log.info("发布回收成功后埋点消费者mqMessage={}", mqMessage);
        //确认接收消息
        try {
            channel.basicAck(mqMessage.getMessageProperties().getDeliveryTag(), false);
        } catch (IOException e) {
            e.printStackTrace();
        }
        byte[] body = mqMessage.getBody();
        Map<String, Object> map = JSON.parseObject(JSON.toJSONString(JSON.parse(body)), Map.class);
        Set<Map.Entry<String, Object>> entries = map.entrySet();
        for (Map.Entry<String, Object> entry : entries) {
            String contentType = entry.getKey();
            if (StatisticsMediaMQEnum.BMS_CONTENT.getValue().equals(contentType)) {
                dealBmsContent(JSON.parseObject(JSON.toJSONString(entry.getValue()), BmsContent.class));
            } else if (StatisticsMediaMQEnum.BMS_PROGRAM.getValue().equals(contentType)) {
                dealBmsProgram(JSON.parseObject(JSON.toJSONString(entry.getValue()), BmsProgram.class));
            } else if (StatisticsMediaMQEnum.BMS_CATEGORY_CONTENT.getValue().equals(contentType)) {
                dealBmsCategoryContent(JSON.parseObject(JSON.toJSONString(entry.getValue()), BmsCategoryContent.class));
            } else if (StatisticsMediaMQEnum.BMS_PACKAGE_CONTENT.getValue().equals(contentType)) {
                dealBmsPackageContent(JSON.parseObject(JSON.toJSONString(entry.getValue()), BmsPackageContent.class));
            } else if (StatisticsMediaMQEnum.BMS_CONTENT_RECYCLE.getValue().equals(contentType)) {
                dealBmsContentRecycle(JSON.parseObject(JSON.toJSONString(entry.getValue()), BmsContent.class));
            } else if (StatisticsMediaMQEnum.BMS_PROGRAM_RECYCLE.getValue().equals(contentType)) {
                dealBmsProgramRecycle(JSON.parseObject(JSON.toJSONString(entry.getValue()), BmsProgram.class));
            } else if (StatisticsMediaMQEnum.BMS_CATEGORY_CONTENT_RECYCLE.getValue().equals(contentType)) {
                dealBmsCategoryContentRecycle(JSON.parseObject(JSON.toJSONString(entry.getValue()), BmsCategoryContent.class));
            } else if (StatisticsMediaMQEnum.BMS_PACKAGE_CONTENT_RECYCLE.getValue().equals(contentType)) {
                dealBmsPackageContentRecycle(JSON.parseObject(JSON.toJSONString(entry.getValue()), BmsPackageContent.class));
            } else if (StatisticsMediaMQEnum.BMS_CONTENT_WAITPUBLISH.getValue().equals(contentType)) {
                dealBmsContentWaitPublish(JSON.parseObject(JSON.toJSONString(entry.getValue()), BmsContent.class));
            } else if (StatisticsMediaMQEnum.BMS_PROGRAM_WAITPUBLISH.getValue().equals(contentType)) {
                dealBmsProgramWaitPublish(JSON.parseObject(JSON.toJSONString(entry.getValue()), BmsProgram.class));
            } else if (StatisticsMediaMQEnum.BMS_CATEGORY_CONTENT_SUCCESS.getValue().equals(contentType)) {
                dealBmsCategoryContentSuccess(JSON.parseObject(JSON.toJSONString(entry.getValue()), BmsCategoryContent.class));
            } else if (StatisticsMediaMQEnum.BMS_CATEGORY_CONTENT_WAITPUBLISH.getValue().equals(contentType)) {
                dealBmsCategoryContentWaitPublish(JSON.parseObject(JSON.toJSONString(entry.getValue()), BmsCategoryContent.class));
            } else if (StatisticsMediaMQEnum.BMS_PACKAGE_CONTENT_WAITPUBLISH.getValue().equals(contentType)) {
                dealBmsPackageContentWaitPublish(JSON.parseObject(JSON.toJSONString(entry.getValue()), BmsPackageContent.class));
            } else {
                log.error("错误的消息类型。map={}", JSON.toJSONString(entry));
                return;
            }
        }
    }

    /**
     * CP侧媒资更新后埋点消费者
     */
    @RabbitListener(bindings = @QueueBinding(
            value = @Queue(CmsContentRenameMQConfig.CMS_CONTENT_RENAME_QUEUE),
            exchange = @Exchange(CmsContentRenameMQConfig.CMS_CONTENT_RENAME_EXCHANGE)
    ))
    public void cmsContentUpdateHandler(Message mqMessage, Channel channel) {
        log.info("CP侧媒资更新后埋点消费者mqMessage={}", mqMessage);
        //确认接收消息
        try {
            channel.basicAck(mqMessage.getMessageProperties().getDeliveryTag(), false);
        } catch (IOException e) {
            e.printStackTrace();
        }
        byte[] body = mqMessage.getBody();
        Map<String, Object> map = JSON.parseObject(JSON.toJSONString(JSON.parse(body)), Map.class);
        Set<Map.Entry<String, Object>> entries = map.entrySet();
        for (Map.Entry<String, Object> entry : entries) {
            String contentType = entry.getKey();
            if (StatisticsMediaMQEnum.BMS_CONTENT.getValue().equals(contentType)) {
                dealCmsContentUpdate(JSON.parseObject(JSON.toJSONString(entry.getValue()), BmsContent.class));
            } else if (StatisticsMediaMQEnum.BMS_PROGRAM.getValue().equals(contentType)) {
                dealCmsProgramUpdate(JSON.parseObject(JSON.toJSONString(entry.getValue()), BmsProgram.class));
            } else if (StatisticsMediaMQEnum.BMS_CATEGORY_CONTENT.getValue().equals(contentType)) {
                dealBmsCategoryUpdate(JSON.parseObject(JSON.toJSONString(entry.getValue()), BmsCategoryContent.class));
            } else if (StatisticsMediaMQEnum.BMS_PACKAGE_CONTENT.getValue().equals(contentType)) {
                dealBmsPackageUpdate(JSON.parseObject(JSON.toJSONString(entry.getValue()), BmsPackageContent.class));
            } else {
                log.error("错误的消息类型。map={}", JSON.toJSONString(entry));
                return;
            }
        }
    }

    private void dealBmsContent(BmsContent bmsContentMysql) {
        log.info("进入dealBmsContent，bmsContentMysql={}", JSON.toJSONString(bmsContentMysql));
        BmsContentESModel model = JSON.parseObject(JSON.toJSONString(bmsContentMysql), BmsContentESModel.class);
        String relationPublishTime = bmsCategoryContentMapper.selectNewRelationPublishTime(bmsContentMysql.getId());
        Date date = null;
        if (StringUtils.isEmpty(relationPublishTime)) {
            model.setBindStatus(StatisticsMediaBindStatusEnum.UNBIND.getValue());
        } else {
            model.setBindStatus(StatisticsMediaBindStatusEnum.BIND.getValue());
            date = DateUtil.parseDateTime(relationPublishTime);
        }
        model.setRelationPublishTime(date);
        if (ContentTypeEnum.FILM.getValue().equals(model.getContentType())) {
            CmsResource cmsResource = cmsResourceMapper.selectFileSizeSumByBmsContentId(model.getCmsContentId(), model.getContentType());
            if (null != cmsResource) {
                model.setFileSize(cmsResource.getFileSize());
                model.setDuration(cmsResource.getDuration());
            }
        }
        model.setStatusFlag(BmsContentStatusFlagEnum.NEW.getCode());
        //ES中原数据标识改为历史记录
        BmsContentESModel modelOld = new BmsContentESModel();
        modelOld.setStatusFlag(BmsContentStatusFlagEnum.OLD.getCode());
        modelOld.setId(model.getId());
        if (Boolean.FALSE.equals(esService.updateById(modelOld, bmsContent))) {
            log.info("dealBmsContent,ES中原数据标识改为历史记录失败");
            return;
        }
        //ES中插入最新记录
        model.setCmsContentCodePublishStatusSpId(model.getCmsContentCode() + "_" + model.getPublishStatus() + "_" + model.getSpId());
        if (Boolean.FALSE.equals(esService.insert(model, bmsContent))) {
            log.info("dealBmsContent,ES中插入最新记录失败");
        }
        //回收需做特殊处理
        if (PublishStatusEnum.ROLLBACK.getCode().equals(bmsContentMysql.getPublishStatus())) {
            dealBmsContentAfterTreatment(model);
        }
        log.info("结束dealBmsContent");
    }

    private void dealBmsProgram(BmsProgram bmsProgramMysql) {
        log.info("进入dealBmsProgram，bmsProgramMysql={}", JSON.toJSONString(bmsProgramMysql));
        if (PublishStatusEnum.ROLLBACK.getCode().equals(bmsProgramMysql.getPublishStatus())) {
            BmsProgramESModel model = esService.selectByIdAndStatusFlag(bmsProgramMysql.getId(), bmsProgram, BmsProgramESModel.class);
            if (PublishStatusEnum.ROLLBACK.getCode().equals(model.getPublishStatus())) {
                return;
            }
        }
        BmsProgramESModel model = JSON.parseObject(JSON.toJSONString(bmsProgramMysql), BmsProgramESModel.class);
        ArrayList<Integer> integers = new ArrayList<>();
        integers.add(ContentTypeEnum.TELEPLAY.getValue());
        integers.add(ContentTypeEnum.EPISODES.getValue());
        BmsContent bmsContentMysql = bmsContentMapper.selectOne(new LambdaQueryWrapper<BmsContent>().eq(BmsContent::getCmsContentId, bmsProgramMysql.getCmsSeriesId())
                .in(BmsContent::getContentType, integers)
                .eq(BmsContent::getSpId, bmsProgramMysql.getSpId()));
        String relationPublishTime = null;
        if (null != bmsContentMysql) {
            relationPublishTime = bmsCategoryContentMapper.selectNewRelationPublishTime(bmsContentMysql.getId());
        }
        Date date = null;
        if (StringUtils.isEmpty(relationPublishTime)) {
            model.setBindStatus(StatisticsMediaBindStatusEnum.UNBIND.getValue());
        } else {
            model.setBindStatus(StatisticsMediaBindStatusEnum.BIND.getValue());
            date = DateUtil.parseDateTime(relationPublishTime);
        }
        model.setRelationPublishTime(date);
        CmsSeries cmsSeries = cmsSeriesMapper.selectOne(new LambdaQueryWrapper<CmsSeries>().select(CmsSeries::getDefinitionFlag)
                .eq(CmsSeries::getId, model.getCmsSeriesId()));
        CmsResource cmsResource = cmsResourceMapper.selectFileSizeSumByBmsContentId(model.getCmsContentId(), ContentTypeEnum.SUBSET.getValue());
        if (null != cmsResource) {
            model.setFileSize(cmsResource.getFileSize());
            model.setDuration(cmsResource.getDuration());
        }
        if (null != cmsSeries) {
            model.setDefinitionFlag(cmsSeries.getDefinitionFlag());
        }
        model.setStatusFlag(BmsContentStatusFlagEnum.NEW.getCode());
        //ES中原数据标识改为历史记录
        BmsProgramESModel modelOld = new BmsProgramESModel();
        modelOld.setStatusFlag(BmsContentStatusFlagEnum.OLD.getCode());
        modelOld.setId(model.getId());
        if (Boolean.FALSE.equals(esService.updateById(modelOld, bmsProgram))) {
            log.info("dealBmsProgram,ES中原数据标识改为历史记录失败");
            return;
        }
        //ES中插入最新记录
        model.setCmsContentCodePublishStatusSpId(model.getCmsContentCode() + "_" + model.getPublishStatus() + "_" + model.getSpId());
        if (Boolean.FALSE.equals(esService.insert(model, bmsProgram))) {
            log.info("dealBmsProgram,ES中插入最新记录失败");
        }
        log.info("结束dealBmsProgram");
    }

    private void dealBmsCategoryContent(BmsCategoryContent bmsCategoryContentMysql) {
        log.info("进入dealBmsCategoryContent，bmsCategoryContentMysql={}", JSON.toJSONString(bmsCategoryContentMysql));
        if (PublishStatusEnum.ROLLBACK.getCode().equals(bmsCategoryContentMysql.getPublishStatus())) {
            BmsCategoryContentESModel model = esService.selectByIdAndStatusFlag(bmsCategoryContentMysql.getId(), bmsCategoryContent, BmsCategoryContentESModel.class);
            if (PublishStatusEnum.ROLLBACK.getCode().equals(model.getPublishStatus())) {
                return;
            }
        }
        BmsCategoryContentESModel model = JSON.parseObject(JSON.toJSONString(bmsCategoryContentMysql), BmsCategoryContentESModel.class);
        //ES中原数据标识改为历史记录
        BmsCategoryContentESModel modelOld = new BmsCategoryContentESModel();
        modelOld.setId(model.getId());
        modelOld.setStatusFlag(BmsContentStatusFlagEnum.OLD.getCode());
        if (Boolean.FALSE.equals(esService.updateById(modelOld, bmsCategoryContent))) {
            log.info("dealBmsCategoryContent,ES中原数据标识改为历史记录失败");
            return;
        }
        //ES中插入最新记录
        model.setStatusFlag(BmsContentStatusFlagEnum.NEW.getCode());
        model.setCategoryCodeCmsContentCodePublishStatusSpId(model.getCategoryCode() + "_" + model.getCmsContentCode() + "_" + model.getPublishStatus() + "_" + model.getSpId());
        if (Boolean.FALSE.equals(esService.insert(model, bmsCategoryContent))) {
            log.info("dealBmsCategoryContent,ES中插入最新记录失败");
        }
        Long bmsContentId = bmsCategoryContentMysql.getBmsContentId();
        BmsContentESModel bmsContentESModel = esService.selectBmsContentByIdAndStatusFlag(bmsContentId);
        if (null == bmsContentESModel) {
            return;
        }
        String relationPublishTime = bmsCategoryContentMapper.selectNewRelationPublishTime(bmsContentId);
        String format = DateUtil.format(bmsContentESModel.getRelationPublishTime(), DateUtils.YYYY_MM_DD_HH_MM_SS);
        if (Objects.equals(relationPublishTime, format)) {
            return;
        }
        Integer bindStatus;
        Date date = null;
        if (StringUtils.isEmpty(relationPublishTime)) {
            bindStatus = StatisticsMediaBindStatusEnum.UNBIND.getValue();
            bmsContentESModel.setRelationPublishTime(null);
        } else {
            bindStatus = StatisticsMediaBindStatusEnum.BIND.getValue();
            date = DateUtil.parse(relationPublishTime, DateUtils.YYYY_MM_DD_HH_MM_SS);
        }
        bmsContentESModel.setRelationPublishTime(date);
        bmsContentESModel.setBindStatus(bindStatus);
        //ES中更新媒资记录
        if (Boolean.FALSE.equals(esService.updateById(bmsContentESModel, bmsContent))) {
            log.info("dealBmsContent,ES中更新媒资记录失败，入参bmsContentESModel={}", JSON.toJSONString(bmsContentESModel));
        }
        if (ContentTypeEnum.FILM.getValue().equals(bmsContentESModel.getContentType())) {
            return;
        }
        List<BmsProgramESModel> bmsProgramESModelOlds =
                esService.selectBmsProgramByCondition(bmsContentESModel.getCmsContentId(), bmsContentESModel.getSpId());
        if (CollUtil.isEmpty(bmsProgramESModelOlds)) {
            return;
        }
        //ES中更新子集
        for (BmsProgramESModel bmsProgramESModel : bmsProgramESModelOlds) {
            bmsProgramESModel.setBindStatus(bindStatus);
            bmsProgramESModel.setRelationPublishTime(date);
            //ES中插入最新记录
            if (Boolean.FALSE.equals(esService.updateById(bmsProgramESModel, bmsProgram))) {
                log.info("dealBmsContent,ES中更新子集记录失败");
            }
        }
        log.info("结束dealBmsCategoryContent");
    }

    private void dealBmsPackageContent(BmsPackageContent bmsPackageContentMysql) {
        log.info("进入dealBmsPackageContent，bmsPackageContentMysql={}", JSON.toJSONString(bmsPackageContentMysql));
        if (PublishStatusEnum.ROLLBACK.getCode().equals(bmsPackageContentMysql.getPublishStatus())) {
            BmsPackageContentESModel model = esService.selectByIdAndStatusFlag(bmsPackageContentMysql.getId(), bmsPackageContent, BmsPackageContentESModel.class);
            if (PublishStatusEnum.ROLLBACK.getCode().equals(model.getPublishStatus())) {
                return;
            }
        }
        BmsPackageContentESModel model = JSON.parseObject(JSON.toJSONString(bmsPackageContentMysql), BmsPackageContentESModel.class);
        //ES中原数据标识改为历史记录
        BmsPackageContentESModel modelOld = new BmsPackageContentESModel();
        modelOld.setId(model.getId());
        modelOld.setStatusFlag(BmsContentStatusFlagEnum.OLD.getCode());
        if (Boolean.FALSE.equals(esService.updateById(modelOld, bmsPackageContent))) {
            log.info("dealBmsPackageContent,ES中原数据标识改为历史记录失败");
            return;
        }
        //ES中插入最新记录
        model.setStatusFlag(BmsContentStatusFlagEnum.NEW.getCode());
        model.setPackageCodeCmsContentCodePublishStatusSpId(model.getPackageCode() + "_" + model.getCmsContentCode() + "_" + model.getPublishStatus() + "_" + model.getSpId());
        if (Boolean.FALSE.equals(esService.insert(model, bmsPackageContent))) {
            log.info("dealBmsPackageContent,ES中插入最新记录失败");
        }
    }

    private void dealBmsContentRecycle(BmsContent bmsContentMysql) {
        log.info("进入dealBmsContentRecycle，bmsContentMysql={}", JSON.toJSONString(bmsContentMysql));
        BmsContentESModel model = esService.selectBmsContentByIdAndStatusFlag(bmsContentMysql.getId());
        //ES中原数据标识改为历史记录
        BmsContentESModel modelOld = new BmsContentESModel();
        modelOld.setStatusFlag(BmsContentStatusFlagEnum.OLD.getCode());
        modelOld.setId(bmsContentMysql.getId());
        if (Boolean.FALSE.equals(esService.updateById(modelOld, bmsContent))) {
            log.info("dealBmsContentRecycle,ES中原数据标识改为历史记录失败");
            return;
        }
        model.setPublishStatus(PublishStatusEnum.ROLLBACK.getCode());
        model.setStatusFlag(BmsContentStatusFlagEnum.NEW.getCode());
        Date date = new Date();
        model.setPublishTime(date);
        model.setCmsContentCodePublishStatusSpId(model.getCmsContentCode() + "_" + model.getPublishStatus() + "_" + model.getSpId());
        if (Boolean.FALSE.equals(esService.insert(model, bmsContent))) {
            log.info("dealBmsContentRecycle,ES中插入新数据失败");
            return;
        }
        dealBmsContentAfterTreatment(model);
        log.info("结束dealBmsContentRecycle");
    }

    private void dealBmsProgramRecycle(BmsProgram bmsProgramMysql) {
        log.info("进入dealBmsProgramRecycle，bmsProgramMysql={}", JSON.toJSONString(bmsProgramMysql));
        BmsProgramESModel model = esService.selectByIdAndStatusFlag(bmsProgramMysql.getId(), bmsProgram, BmsProgramESModel.class);
        if (PublishStatusEnum.ROLLBACK.getCode().equals(model.getPublishStatus())) {
            return;
        }
        BmsProgramESModel modelOld = new BmsProgramESModel();
        modelOld.setStatusFlag(BmsContentStatusFlagEnum.OLD.getCode());
        modelOld.setId(bmsProgramMysql.getId());
        if (Boolean.FALSE.equals(esService.updateById(modelOld, bmsProgram))) {
            log.info("dealBmsProgramRecycle,ES中原数据标识改为历史记录失败");
            return;
        }
        model.setStatusFlag(BmsContentStatusFlagEnum.NEW.getCode());
        model.setPublishStatus(PublishStatusEnum.ROLLBACK.getCode());
        model.setPublishTime(new Date());
        model.setCmsContentCodePublishStatusSpId(model.getCmsContentCode() + "_" + model.getPublishStatus() + "_" + model.getSpId());
        if (Boolean.FALSE.equals(esService.insert(model, bmsProgram))) {
            log.info("dealBmsProgramRecycle,ES中插入新数据失败");
            return;
        }
        log.info("结束dealBmsProgramRecycle");
    }

    private void dealBmsCategoryContentRecycle(BmsCategoryContent bmsCategoryContentMysql) {
        log.info("进入dealBmsCategoryContentRecycle，bmsCategoryContentMysql={}", JSON.toJSONString(bmsCategoryContentMysql));
        BmsCategoryContentESModel model = esService.selectByIdAndStatusFlag(bmsCategoryContentMysql.getId(), bmsCategoryContent, BmsCategoryContentESModel.class);
        if (PublishStatusEnum.ROLLBACK.getCode().equals(model.getPublishStatus())) {
            return;
        }
        BmsCategoryContentESModel modelOld = new BmsCategoryContentESModel();
        modelOld.setId(bmsCategoryContentMysql.getId());
        modelOld.setStatusFlag(BmsContentStatusFlagEnum.OLD.getCode());
        if (Boolean.FALSE.equals(esService.updateById(modelOld, bmsCategoryContent))) {
            log.info("dealBmsCategoryContentRecycle,ES中原数据标识改为历史记录失败");
            return;
        }
        model.setStatusFlag(BmsContentStatusFlagEnum.NEW.getCode());
        model.setPublishStatus(PublishStatusEnum.ROLLBACK.getCode());
        model.setPublishTime(new Date());
        model.setCategoryCodeCmsContentCodePublishStatusSpId(model.getCategoryCode() + "_" + model.getCmsContentCode() + "_" + model.getPublishStatus() + "_" + model.getSpId());
        if (Boolean.FALSE.equals(esService.insert(model, bmsCategoryContent))) {
            log.info("dealBmsCategoryContentRecycle,ES中插入新数据失败");
            return;
        }
        log.info("结束dealBmsCategoryContentRecycle");
    }

    private void dealBmsPackageContentRecycle(BmsPackageContent bmsPackageContentMysql) {
        log.info("进入dealBmsPackageContentRecycle，bmsPackageContentMysql={}", JSON.toJSONString(bmsPackageContentMysql));
        BmsPackageContentESModel model = esService.selectByIdAndStatusFlag(bmsPackageContentMysql.getId(), bmsPackageContent, BmsPackageContentESModel.class);
        if (PublishStatusEnum.ROLLBACK.getCode().equals(model.getPublishStatus())) {
            return;
        }
        BmsPackageContentESModel modelOld = new BmsPackageContentESModel();
        modelOld.setId(bmsPackageContentMysql.getId());
        modelOld.setStatusFlag(BmsContentStatusFlagEnum.OLD.getCode());
        if (Boolean.FALSE.equals(esService.updateById(modelOld, bmsPackageContent))) {
            log.info("dealBmsPackageContentRecycle,ES中原数据标识改为历史记录失败");
            return;
        }
        model.setStatusFlag(BmsContentStatusFlagEnum.NEW.getCode());
        model.setPublishStatus(PublishStatusEnum.ROLLBACK.getCode());
        model.setPublishTime(new Date());
        model.setPackageCodeCmsContentCodePublishStatusSpId(model.getPackageCode() + "_" + model.getCmsContentCode() + "_" + model.getPublishStatus() + "_" + model.getSpId());
        if (Boolean.FALSE.equals(esService.insert(model, bmsPackageContent))) {
            log.info("dealBmsPackageContentRecycle,ES中插入新数据失败");
            return;
        }
        log.info("结束dealBmsPackageContentRecycle");
    }

    private void dealBmsContentWaitPublish(BmsContent bmsContentMysql) {
        log.info("dealBmsContentWaitPublish，bmsContentMysql={}", JSON.toJSONString(bmsContentMysql));
        if (Boolean.FALSE.equals(esService.deleteByIdAndStatusFlag(bmsContentMysql.getId(), bmsContent))) {
            log.error("dealBmsContentWaitPublish,ES中原数据删除失败");
            return;
        }
        log.info("结束dealBmsContentWaitPublish");
    }

    private void dealBmsProgramWaitPublish(BmsProgram bmsProgramMysql) {
        log.info("dealBmsProgramWaitPublish，bmsProgramMysql={}", JSON.toJSONString(bmsProgramMysql));
        if (Boolean.FALSE.equals(esService.deleteByIdAndStatusFlag(bmsProgramMysql.getId(), bmsProgram))) {
            log.error("dealBmsProgramWaitPublish,ES中原数据删除失败");
            return;
        }
        log.info("结束dealBmsProgramWaitPublish");
    }

    private void dealBmsCategoryContentSuccess(BmsCategoryContent bmsCategoryContentMysql) {
        log.info("进入dealBmsCategoryContentSuccess，bmsCategoryContentMysql={}", JSON.toJSONString(bmsCategoryContentMysql));
        if (PublishStatusEnum.ROLLBACK.getCode().equals(bmsCategoryContentMysql.getPublishStatus())) {
            BmsCategoryContentESModel model = esService.selectByIdAndStatusFlag(bmsCategoryContentMysql.getId(), bmsCategoryContent, BmsCategoryContentESModel.class);
            if (PublishStatusEnum.ROLLBACK.getCode().equals(model.getPublishStatus())) {
                return;
            }
        }
        BmsCategoryContentESModel model = JSON.parseObject(JSON.toJSONString(bmsCategoryContentMysql), BmsCategoryContentESModel.class);
        //ES中原数据标识改为历史记录
        BmsCategoryContentESModel modelOld = new BmsCategoryContentESModel();
        modelOld.setId(model.getId());
        modelOld.setStatusFlag(BmsContentStatusFlagEnum.OLD.getCode());
        if (Boolean.FALSE.equals(esService.updateById(modelOld, bmsCategoryContent))) {
            log.info("dealBmsCategoryContent,ES中原数据标识改为历史记录失败");
            return;
        }
        //ES中插入最新记录
        model.setStatusFlag(BmsContentStatusFlagEnum.NEW.getCode());
        model.setCategoryCodeCmsContentCodePublishStatusSpId(model.getCategoryCode() + "_" + model.getCmsContentCode() + "_" + model.getPublishStatus() + "_" + model.getSpId());
        if (Boolean.FALSE.equals(esService.insert(model, bmsCategoryContent))) {
            log.info("dealBmsCategoryContent,ES中插入最新记录失败");
        }
        Long bmsContentId = bmsCategoryContentMysql.getBmsContentId();
        BmsContentESModel bmsContentESModel = esService.selectBmsContentByIdAndStatusFlag(bmsContentId);
        if (null == bmsContentESModel) {
            return;
        }
        String relationPublishTime = bmsCategoryContentMapper.selectNewRelationPublishTime(bmsContentId);
        if (StringUtils.isEmpty(relationPublishTime)){
            relationPublishTime = bmsCategoryContentMysql.getPublishTime();
        }
        String format = DateUtil.format(bmsContentESModel.getRelationPublishTime(), DateUtils.YYYY_MM_DD_HH_MM_SS);
        if (Objects.equals(relationPublishTime, format)) {
            return;
        }
        Integer bindStatus;
        Date date = null;
        if (StringUtils.isEmpty(relationPublishTime)) {
            bindStatus = StatisticsMediaBindStatusEnum.UNBIND.getValue();
            bmsContentESModel.setRelationPublishTime(null);
        } else {
            bindStatus = StatisticsMediaBindStatusEnum.BIND.getValue();
            date = DateUtil.parse(relationPublishTime, DateUtils.YYYY_MM_DD_HH_MM_SS);
        }
        bmsContentESModel.setRelationPublishTime(date);
        bmsContentESModel.setBindStatus(bindStatus);
        //ES中更新媒资记录
        if (Boolean.FALSE.equals(esService.updateById(bmsContentESModel, bmsContent))) {
            log.info("dealBmsContent,ES中更新媒资记录失败，入参bmsContentESModel={}", JSON.toJSONString(bmsContentESModel));
        }
        if (ContentTypeEnum.FILM.getValue().equals(bmsContentESModel.getContentType())) {
            return;
        }
        List<BmsProgramESModel> bmsProgramESModelOlds =
                esService.selectBmsProgramByCondition(bmsContentESModel.getCmsContentId(), bmsContentESModel.getSpId());
        if (CollUtil.isEmpty(bmsProgramESModelOlds)) {
            return;
        }
        //ES中更新子集
        for (BmsProgramESModel bmsProgramESModel : bmsProgramESModelOlds) {
            bmsProgramESModel.setBindStatus(bindStatus);
            bmsProgramESModel.setRelationPublishTime(date);
            //ES中插入最新记录
            if (Boolean.FALSE.equals(esService.updateById(bmsProgramESModel, bmsProgram))) {
                log.info("dealBmsContent,ES中更新子集记录失败");
            }
        }
        log.info("结束dealBmsCategoryContentSuccess");
    }

    private void dealBmsCategoryContentWaitPublish(BmsCategoryContent bmsCategoryContentMysql) {
        log.info("dealBmsCategoryContentWaitPublish，bmsCategoryContentMysql={}", JSON.toJSONString(bmsCategoryContentMysql));
        if (Boolean.FALSE.equals(esService.deleteByIdAndStatusFlag(bmsCategoryContentMysql.getId(), bmsCategoryContent))) {
            log.error("dealBmsCategoryContentWaitPublish,ES中原数据删除失败");
            return;
        }
        //修改媒资的关系发布情况
        Long bmsContentId = bmsCategoryContentMysql.getBmsContentId();
        BmsContentESModel bmsContentESModel = esService.selectBmsContentByIdAndStatusFlag(bmsContentId);
        if (null == bmsContentESModel) {
            return;
        }
        String relationPublishTime = bmsCategoryContentMapper.selectNewRelationPublishTime(bmsContentId);
        String format = DateUtil.format(bmsContentESModel.getRelationPublishTime(), DateUtils.YYYY_MM_DD_HH_MM_SS);
        if (Objects.equals(relationPublishTime, format)) {
            return;
        }
        Integer bindStatus;
        Date date = null;
        if (StringUtils.isEmpty(relationPublishTime)) {
            bindStatus = StatisticsMediaBindStatusEnum.UNBIND.getValue();
            bmsContentESModel.setRelationPublishTime(null);
        } else {
            bindStatus = StatisticsMediaBindStatusEnum.BIND.getValue();
            date = DateUtil.parse(relationPublishTime, DateUtils.YYYY_MM_DD_HH_MM_SS);
        }
        bmsContentESModel.setRelationPublishTime(date);
        bmsContentESModel.setBindStatus(bindStatus);
        //ES中更新媒资记录
        if (Boolean.FALSE.equals(esService.updateById(bmsContentESModel, bmsContent))) {
            log.info("dealBmsContent,ES中更新媒资记录失败，入参bmsContentESModel={}", JSON.toJSONString(bmsContentESModel));
        }
        if (ContentTypeEnum.FILM.getValue().equals(bmsContentESModel.getContentType())) {
            return;
        }
        List<BmsProgramESModel> bmsProgramESModelOlds =
                esService.selectBmsProgramByCondition(bmsContentESModel.getCmsContentId(), bmsContentESModel.getSpId());
        if (CollUtil.isEmpty(bmsProgramESModelOlds)) {
            return;
        }
        //ES中更新子集
        for (BmsProgramESModel bmsProgramESModel : bmsProgramESModelOlds) {
            bmsProgramESModel.setBindStatus(bindStatus);
            bmsProgramESModel.setRelationPublishTime(date);
            //ES中插入最新记录
            if (Boolean.FALSE.equals(esService.updateById(bmsProgramESModel, bmsProgram))) {
                log.info("dealBmsContent,ES中更新子集记录失败");
            }
        }
        log.info("结束dealBmsCategoryContentWaitPublish");
    }

    private void dealBmsPackageContentWaitPublish(BmsPackageContent bmsPackageContentMysql) {
        log.info("dealBmsPackageContentWaitPublish，bmsPackageContentMysql={}", JSON.toJSONString(bmsPackageContentMysql));
        if (Boolean.FALSE.equals(esService.deleteByIdAndStatusFlag(bmsPackageContentMysql.getId(), bmsPackageContent))) {
            log.error("dealBmsPackageContentWaitPublish,ES中原数据删除失败");
            return;
        }
        log.info("结束dealBmsPackageContentWaitPublish");
    }

    private void dealBmsContentAfterTreatment(BmsContentESModel model) {
        log.info("dealBmsContentAfterTreatment.BmsContentESModel={}", JSON.toJSONString(model));
        //栏目关系处理
        List<BmsCategoryContentESModel> bmsCategoryContentESModels = esService.selectBmsCategoryContentByCondition(model.getId());
        if (CollUtil.isNotEmpty(bmsCategoryContentESModels)) {
            for (BmsCategoryContentESModel bmsCategoryContentESModel : bmsCategoryContentESModels) {
                BmsCategoryContentESModel bmsCategoryContentESModelOld = new BmsCategoryContentESModel();
                bmsCategoryContentESModelOld.setStatusFlag(BmsContentStatusFlagEnum.OLD.getCode());
                bmsCategoryContentESModelOld.setId(bmsCategoryContentESModel.getId());
                if (Boolean.FALSE.equals(esService.updateById(bmsCategoryContentESModelOld, bmsCategoryContent))) {
                    log.info("dealBmsContentRecycle.dealBmsCategoryContent,ES中原数据标识改为历史记录失败");
                    continue;
                }
                bmsCategoryContentESModel.setStatusFlag(BmsContentStatusFlagEnum.NEW.getCode());
                bmsCategoryContentESModel.setPublishStatus(PublishStatusEnum.ROLLBACK.getCode());
                bmsCategoryContentESModel.setPublishTime(model.getPublishTime());
                bmsCategoryContentESModel.setCategoryCodeCmsContentCodePublishStatusSpId(bmsCategoryContentESModel.getCategoryCode()
                        + "_" + bmsCategoryContentESModel.getCmsContentCode() + "_" + bmsCategoryContentESModel.getPublishStatus()
                        + "_" + bmsCategoryContentESModel.getSpId());
                if (Boolean.FALSE.equals(esService.insert(bmsCategoryContentESModel, bmsCategoryContent))) {
                    log.info("dealBmsContentRecycle.dealBmsCategoryContent,ES中插入新数据失败");
                }
            }
        }
        //包关系处理
        List<BmsPackageContentESModel> bmsPackageContentESModels = esService.selectBmsPackageContentByCondition(model.getId());
        if (CollUtil.isNotEmpty(bmsPackageContentESModels)) {
            for (BmsPackageContentESModel bmsPackageContentESModel : bmsPackageContentESModels) {
                BmsPackageContentESModel bmsPackageContentESModelOld = new BmsPackageContentESModel();
                bmsPackageContentESModelOld.setStatusFlag(BmsContentStatusFlagEnum.OLD.getCode());
                bmsPackageContentESModelOld.setId(bmsPackageContentESModel.getId());
                if (Boolean.FALSE.equals(esService.updateById(bmsPackageContentESModelOld, bmsPackageContent))) {
                    log.info("dealBmsContentRecycle.dealBmsPackageContentESModel,ES中原数据标识改为历史记录失败");
                    continue;
                }
                bmsPackageContentESModel.setStatusFlag(BmsContentStatusFlagEnum.NEW.getCode());
                bmsPackageContentESModel.setPublishStatus(PublishStatusEnum.ROLLBACK.getCode());
                bmsPackageContentESModel.setPublishTime(model.getPublishTime());
                bmsPackageContentESModel.setPackageCodeCmsContentCodePublishStatusSpId(bmsPackageContentESModel.getPackageCode()
                        + "_" + bmsPackageContentESModel.getCmsContentCode() + "_" + bmsPackageContentESModel.getPublishStatus()
                        + "_" + bmsPackageContentESModel.getSpId());
                if (Boolean.FALSE.equals(esService.insert(bmsPackageContentESModel, bmsPackageContent))) {
                    log.info("dealBmsContentRecycle.dealBmsPackageContentESModel,ES中插入新数据失败");
                }
            }
        }
        //子集处理
        if (ContentTypeEnum.FILM.getValue().equals(model.getContentType())) {
            log.info("结束dealBmsContent");
            return;
        }
        List<BmsProgramESModel> bmsProgramESModels = esService.selectBmsProgramByCondition(model.getCmsContentId(), model.getSpId());
        if (CollUtil.isNotEmpty(bmsProgramESModels)) {
            for (BmsProgramESModel bmsProgramESModel : bmsProgramESModels) {
                if (!PublishStatusEnum.ROLLBACK.getCode().equals(bmsProgramESModel.getPublishStatus())) {
                    BmsProgramESModel bmsProgramESModelOld = new BmsProgramESModel();
                    bmsProgramESModelOld.setId(bmsProgramESModel.getId());
                    bmsProgramESModelOld.setStatusFlag(BmsContentStatusFlagEnum.OLD.getCode());
                    if (Boolean.FALSE.equals(esService.updateById(bmsProgramESModelOld, bmsProgram))) {
                        log.info("dealBmsContent.dealProgram,ES中原数据标识改为历史记录失败");
                        continue;
                    }
                    bmsProgramESModel.setStatusFlag(BmsContentStatusFlagEnum.NEW.getCode());
                    bmsProgramESModel.setPublishStatus(PublishStatusEnum.ROLLBACK.getCode());
                    bmsProgramESModel.setPublishTime(model.getPublishTime());
                    bmsProgramESModel.setCmsContentCodePublishStatusSpId(bmsProgramESModel.getCmsContentCode() + "_" + bmsProgramESModel.getPublishStatus() + "_" + bmsProgramESModel.getSpId());
                    if (Boolean.FALSE.equals(esService.insert(bmsProgramESModel, bmsProgram))) {
                        log.info("dealBmsContent.dealProgram,ES中插入新数据失败");
                    }
                }
            }
        }
    }

    private void dealCmsContentUpdate(BmsContent bmsContentMysql) {
        log.info("进入dealCmsContentUpdate，bmsContentMysql={}", JSON.toJSONString(bmsContentMysql));
        BmsContentESModel model = JSON.parseObject(JSON.toJSONString(bmsContentMysql), BmsContentESModel.class);
        if (Boolean.FALSE.equals(esService.updateByField(model, "cmsContentCode", bmsContent))) {
            log.error("dealCmsContentUpdate,修改bmsContent记录失败");
            return;
        }
        HashMap<String, String> map = new HashMap<>();
        map.put("cmsContentCode", model.getCmsContentCode());
        map.put("contentName", model.getName());
        if (Boolean.FALSE.equals(esService.updateByField(map, "cmsContentCode", bmsCategoryContent))) {
            log.error("dealCmsContentUpdate,修改bmsCategoryContent记录失败");
            return;
        }
        if (Boolean.FALSE.equals(esService.updateByField(map, "cmsContentCode", bmsPackageContent))) {
            log.error("dealCmsContentUpdate,修改bmsPackageContent记录失败");
            return;
        }
        log.info("结束dealCmsContentUpdate");
    }

    private void dealCmsProgramUpdate(BmsProgram bmsProgramMysql) {
        log.info("进入dealCmsProgramUpdate，bmsProgramMysql={}", JSON.toJSONString(bmsProgramMysql));
        BmsProgramESModel model = JSON.parseObject(JSON.toJSONString(bmsProgramMysql), BmsProgramESModel.class);
        if (Boolean.FALSE.equals(esService.updateByField(model, "cmsContentCode", bmsProgram))) {
            log.error("dealCmsProgramUpdate,修改bms记录失败");
            return;
        }
        HashMap<String, String> map = new HashMap<>();
        map.put("cmsContentCode", model.getCmsContentCode());
        map.put("contentName", model.getName());
        if (Boolean.FALSE.equals(esService.updateByField(map, "cmsContentCode", bmsCategoryContent))) {
            log.error("dealCmsProgramUpdate,修改bmsCategoryContent记录失败");
            return;
        }
        if (Boolean.FALSE.equals(esService.updateByField(map, "cmsContentCode", bmsPackageContent))) {
            log.error("dealCmsProgramUpdate,修改bmsPackageContent记录失败");
            return;
        }
        log.info("结束dealCmsProgramUpdate");
    }

    private void dealBmsCategoryUpdate(BmsCategoryContent bmsCategoryContentMysql) {
        log.info("进入dealBmsCategoryUpdate，bmsCategoryContentMysql={}", JSON.toJSONString(bmsCategoryContentMysql));
        BmsCategoryContentESModel model = JSON.parseObject(JSON.toJSONString(bmsCategoryContentMysql), BmsCategoryContentESModel.class);
        if (Boolean.FALSE.equals(esService.updateByField(model, "categoryCode", bmsCategoryContent))) {
            log.error("dealCmsContentUpdate,修改bmsCategoryContent记录失败");
            return;
        }
        log.info("结束dealBmsCategoryUpdate");
    }

    private void dealBmsPackageUpdate(BmsPackageContent bmsPackageContentMysql) {
        log.info("进入BmsPackageContent，bmsPackageContentMysql={}", JSON.toJSONString(bmsPackageContentMysql));
        BmsPackageContentESModel model = JSON.parseObject(JSON.toJSONString(bmsPackageContentMysql), BmsPackageContentESModel.class);
        if (Boolean.FALSE.equals(esService.updateByField(model, "packageCode", bmsPackageContent))) {
            log.error("dealCmsProgramUpdate,修改bmsPackageContent记录失败");
            return;
        }
        log.info("结束dealBmsPackageUpdate");
    }
}
