package com.pukka.iptv.statistics.controller;

import com.pukka.iptv.common.base.exception.CommonResponseException;
import com.pukka.iptv.common.base.vo.CommonResponse;
import com.pukka.iptv.statistics.service.statistics.StatisticsPublishService;
import com.pukka.iptv.statistics.vo.StatisticsPublishItemRespVo;
import com.pukka.iptv.statistics.vo.StatisticsPublishReqVo;
import com.pukka.iptv.statistics.vo.StatisticsPublishRespVo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.*;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.AllArgsConstructor;

import javax.annotation.Resource;
import javax.validation.Valid;

/**
 * @author: zhengcl
 * @date: 2022年7月13日 上午9:55:36
 */
@RestController
@AllArgsConstructor
@RequestMapping(value = "/statisticsPublish", produces = MediaType.APPLICATION_JSON_VALUE)
@Api(tags = "发布回收统计管理")
public class StatisticsPublishController {

    @Resource
    private StatisticsPublishService statisticsPublishService;

    @ApiOperation(value = "发布回收统计总量")
    @GetMapping("/getPublishTotal")
    public CommonResponse<StatisticsPublishRespVo> getPublishTotal(@Valid StatisticsPublishReqVo vo) {
        paramVerifyLess(vo);
        return CommonResponse.success(statisticsPublishService.getPublishTotal(vo));
    }

    @ApiOperation(value = "发布回收明细分页查询")
    @GetMapping("/getPublishPage")
    public CommonResponse<Page<StatisticsPublishItemRespVo>> getPublishPage(@Valid Page page, StatisticsPublishReqVo vo) {
        paramVerify(vo);
        return CommonResponse.success(statisticsPublishService.getPublishPage(page, vo));
    }

    private void paramVerify(StatisticsPublishReqVo vo){
        if (null == vo.getContentType()) {
            throw new CommonResponseException("内容类型不能为空");
        }
        if (null == vo.getWorkOrderType()) {
            throw new CommonResponseException("工单类型不能为空");
        }
        if (null == vo.getType()) {
            throw new CommonResponseException("发布回收类型不能为空");
        }
    }

    private void paramVerifyLess(StatisticsPublishReqVo vo){
        if (null == vo.getWorkOrderType()) {
            throw new CommonResponseException("工单类型不能为空");
        }
        if (null == vo.getType()) {
            throw new CommonResponseException("发布回收类型不能为空");
        }
    }
}
