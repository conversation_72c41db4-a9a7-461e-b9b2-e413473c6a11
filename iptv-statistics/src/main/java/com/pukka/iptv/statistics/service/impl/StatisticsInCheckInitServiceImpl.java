package com.pukka.iptv.statistics.service.impl;


import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.pukka.iptv.common.base.vo.CommonResponse;
import com.pukka.iptv.common.data.model.statistics.StatisticsInCheckInit;
import com.pukka.iptv.common.data.model.statistics.StatisticsInInit;
import com.pukka.iptv.common.data.util.TimeUtil;
import com.pukka.iptv.common.data.vo.req.StatisticsInInitVo;
import com.pukka.iptv.statistics.mapper.StatisticsInCheckInitMapper;
import com.pukka.iptv.statistics.service.StatisticsInCheckInitService;
import com.pukka.iptv.statistics.util.DateUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.text.ParseException;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;


/**
 *
 * @author: tan
 * @date: 2022-8-11 16:46:05
 */

@Service
public class StatisticsInCheckInitServiceImpl extends ServiceImpl<StatisticsInCheckInitMapper, StatisticsInCheckInit> implements StatisticsInCheckInitService {

    @Autowired
    private StatisticsInCheckInitService statisticsInCheckInitService;

    @Override
    public CommonResponse<List<StatisticsInCheckInit>> getAllStatistics(StatisticsInInitVo statisticsInInitVo) {
        List<Date> dateList = null;
        try {
            dateList = DateUtil.findDates(TimeUtil.getDate(statisticsInInitVo.getStartTime()), TimeUtil.getDate(statisticsInInitVo.getEndTime()));
        } catch (ParseException e) {
            e.printStackTrace();
        }
        List<StatisticsInCheckInit> statisticsInCheckInitList = new ArrayList<>();
        dateList.forEach(e -> {
            StatisticsInCheckInit statisticsInCheckInit = new StatisticsInCheckInit();
            statisticsInCheckInit.setStatus(1);
            statisticsInCheckInit.setStatisticDate(TimeUtil.formatDate2Str(e));
            statisticsInCheckInitList.add(statisticsInCheckInit);
        });
        if(statisticsInCheckInitList.size() > 0){
            statisticsInCheckInitService.saveBatch(statisticsInCheckInitList);
        }

        return CommonResponse.success(statisticsInCheckInitList);
    }
}


