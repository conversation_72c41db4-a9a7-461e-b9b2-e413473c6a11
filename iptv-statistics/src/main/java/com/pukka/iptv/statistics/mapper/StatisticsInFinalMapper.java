package com.pukka.iptv.statistics.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.pukka.iptv.common.data.annotation.DataPermission;
import com.pukka.iptv.common.data.model.statistics.StatisticsInCheck;
import com.pukka.iptv.common.data.model.statistics.StatisticsInFinal;
import com.pukka.iptv.common.data.vo.req.StatisticsInVo;
import com.pukka.iptv.common.data.vo.req.StatisticsInVoD;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 *
 * @author: tan
 * @date: 2022-8-2 9:15:21
 */

@Mapper
@DataPermission(config = "cp_id=cpIds")
public interface StatisticsInFinalMapper extends BaseMapper<StatisticsInFinal>{

    List<StatisticsInFinal> getSelfAllStatistics(@Param("param") StatisticsInVo statisticsInVo);

    List<StatisticsInFinal> getSelfAllStatisticsByAuth(@Param("param") StatisticsInVo statisticsInVo, @Param("cpIdList") List<Long> cpIdList);

    List<StatisticsInFinal> getSelfAllStatisticsLastMonth(@Param("param") StatisticsInVoD statisticsInVo);

    List<StatisticsInFinal> getSelfAllStatisticsLastMonthByAuth(@Param("param") StatisticsInVoD statisticsInVo,  @Param("cpIdList") List<Long> cpIdList);
}
