package com.pukka.iptv.statistics.job;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.pukka.iptv.common.api.feign.cms.CmsProgramFeignClient;
import com.pukka.iptv.common.api.feign.cms.CmsSeriesFeignClient;
import com.pukka.iptv.common.base.enums.StatisticsTypeEnum;
import com.pukka.iptv.common.base.vo.CommonResponse;
import com.pukka.iptv.common.data.model.statistics.StatisticsInCheck;
import com.pukka.iptv.common.data.vo.req.StatisticsInVo;
import com.pukka.iptv.statistics.service.StatisticsInCheckService;
import com.pukka.iptv.statistics.util.DateUtil;
import feign.Request;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.concurrent.TimeUnit;

/**
 * @ClassName StatisticSelfJob
 * @Description TODO
 * <AUTHOR>
 * @Date 2022/7/14 11:49
 * @Version
 */
@Component
@Slf4j
public class StatisticSelfSeriesJob implements IStatisticJob {
    @Autowired
    CmsSeriesFeignClient cmsSeriesFeignClient;

    @Autowired
    StatisticsInCheckService statisticsInCheckService;

    @Override
    public void executeJob() {
        StatisticsInVo statisticsInVo = new StatisticsInVo();

        Map map = DateUtil.getYesterdayTime();
        statisticsInVo.setStartTime((String) map.get("startDate"));
        statisticsInVo.setEndTime((String) map.get("endDate"));

        Request.Options options = new Request.Options(5, TimeUnit.SECONDS, 160, TimeUnit.SECONDS, true);
        CommonResponse<List<StatisticsInCheck>> commonResponse =  cmsSeriesFeignClient.selfSeriesStatistic(statisticsInVo, options);

        List<StatisticsInCheck> statisticsInCheckList = (List<StatisticsInCheck>) commonResponse.getData();
        List<StatisticsInCheck> statisticsInsListAdd = new ArrayList<>();
        LambdaQueryWrapper<StatisticsInCheck> queryWrapper = Wrappers.lambdaQuery();

        queryWrapper.clear();
        queryWrapper.eq(StatisticsInCheck::getStatisticDate, statisticsInVo.getStartTime())
                .eq(StatisticsInCheck::getType, StatisticsTypeEnum.Series.getValue());
        boolean flagRemove = statisticsInCheckService.remove(queryWrapper);

        statisticsInCheckList.forEach(e ->
        {
            statisticsInsListAdd.add(e);
        });
        if(statisticsInsListAdd.size() >0 ){
            Boolean isSave = statisticsInCheckService.saveBatch(statisticsInsListAdd);
            if(isSave){
                log.info("StatisticSelfSeriesJob ->>>>> time = {},size = {}", statisticsInVo.getStartTime(),statisticsInsListAdd.size());
            }else{
                log.info("StatisticSelfSeriesJob is fail ->>>>> time = {}",statisticsInVo.getStartTime() );
            }
        }
        log.info("StatisticSelfSeriesJob ->>>>> time = {},size = {}", statisticsInVo.getStartTime(), statisticsInsListAdd.size());

    }

    public void executeJob(  StatisticsInVo statisticsInVo) {

        Request.Options options = new Request.Options(5, TimeUnit.SECONDS, 160, TimeUnit.SECONDS, true);
        CommonResponse<List<StatisticsInCheck>> commonResponse =  cmsSeriesFeignClient.selfSeriesStatistic(statisticsInVo, options);

        List<StatisticsInCheck> statisticsInCheckList = (List<StatisticsInCheck>) commonResponse.getData();
        List<StatisticsInCheck> statisticsInsListAdd = new ArrayList<>();
        List<StatisticsInCheck> statisticsInsListUpdate = new ArrayList<>();
        LambdaQueryWrapper<StatisticsInCheck> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.clear();
        queryWrapper.eq(StatisticsInCheck::getStatisticDate, statisticsInVo.getStartTime())
                .eq(StatisticsInCheck::getType, StatisticsTypeEnum.Series.getValue());
        boolean flagRemove = statisticsInCheckService.remove(queryWrapper);

        for(StatisticsInCheck e : statisticsInCheckList){
           statisticsInsListAdd.add(e);
        }
        if(statisticsInsListAdd.size() >0 ){
            Boolean isSave = statisticsInCheckService.saveBatch(statisticsInsListAdd);
            if(isSave){
                log.info("StatisticSelfSeriesJob ->>>>> time = {},size = {}", statisticsInVo.getStartTime(),statisticsInsListAdd.size());
            }else{
                log.info("StatisticSelfSeriesJob is fail ->>>>> time = {}",statisticsInVo.getStartTime() );
            }
        }
        log.info("StatisticSelfSeriesJob ->>>>> time = {},size = {}", statisticsInVo.getStartTime(), statisticsInsListAdd.size());

    }
}
