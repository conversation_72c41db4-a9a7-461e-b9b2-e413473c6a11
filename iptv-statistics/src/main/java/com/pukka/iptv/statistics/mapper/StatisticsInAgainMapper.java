package com.pukka.iptv.statistics.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.pukka.iptv.common.data.annotation.DataPermission;
import com.pukka.iptv.common.data.model.statistics.StatisticsInAgain;
import com.pukka.iptv.common.data.model.statistics.StatisticsInFinal;
import com.pukka.iptv.common.data.vo.req.StatisticsInVo;
import com.pukka.iptv.common.data.vo.req.StatisticsInVoD;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 *
 * @author: tan
 * @date: 2022-8-2 9:17:35
 */

@Mapper
@DataPermission(config = "cp_id=cpIds")
public interface StatisticsInAgainMapper extends BaseMapper<StatisticsInAgain>{

    List<StatisticsInAgain> getSelfAllStatistics(@Param("param") StatisticsInVo statisticsInVo);

    List<StatisticsInAgain> getSelfAllStatisticsByAuth(@Param("param") StatisticsInVo statisticsInVo, @Param("cpIdList") List<Long> cpIdList);

    List<StatisticsInAgain> getSelfAllStatisticsLastMonth(@Param("param") StatisticsInVoD statisticsInVo);

    List<StatisticsInAgain> getSelfAllStatisticsLastMonthByAuth(@Param("param") StatisticsInVoD statisticsInVo, @Param("cpIdList") List<Long> cpIdList);

}
