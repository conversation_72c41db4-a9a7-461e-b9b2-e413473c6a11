package com.pukka.iptv.statistics.vo;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * @author: zhengcl
 * @date: 2022年7月13日 上午9:55:27
 */

@Data
@EqualsAndHashCode
@JsonIgnoreProperties(value = {"handler"})
@Accessors(chain = true)
public class StatisticsOnlineRespVo implements java.io.Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "主键", dataType = "Long", name = "id")
    private Long id;

    @ApiModelProperty(value = "剧头媒资数量", dataType = "Long", name = "countSeries")
    private Long countSeries = 0L;

    @ApiModelProperty(value = "单集媒资数量", dataType = "Long", name = "countProgram")
    private Long countProgram = 0L;

    @ApiModelProperty(value = "单集视频长度(H)", dataType = "Long", name = "durationProgram")
    private Long durationProgram = 0L;

    @ApiModelProperty(value = "单集视频大小(G)", dataType = "Long", name = "fileSizeProgram")
    private Long fileSizeProgram = 0L;

    @ApiModelProperty(value = "子集媒资数量", dataType = "Long", name = "countSubset")
    private Long countSubset = 0L;

    @ApiModelProperty(value = "子集视频长度(H)", dataType = "Long", name = "durationSubset")
    private Long durationSubset = 0L;

    @ApiModelProperty(value = "子集视频大小(G)", dataType = "Long", name = "fileSizeSubset")
    private Long fileSizeSubset = 0L;

    @ApiModelProperty(value = "统计类型 1线上节目汇总 2节目上新 3节目下线", dataType = "Integer", name = "mediaOnlineStatus")
    private Integer mediaOnlineStatus;

    @ApiModelProperty(value = "节目清晰度标识 0：标清 1：高清 2：超清 3. 4K 4. 杜比（4K+杜比） 5.H264 6.H264(标清) 7.H264(高清) 8.H264(4K) 9.H265(高清) 10.H265(4K)", dataType = "Integer", name = "definitionFlag")
    private Integer definitionFlag;

    @ApiModelProperty(value = "节目状态 1.单节目发布 2.节目已绑栏目发布 3.节目未绑栏目发布", dataType = "Integer", name = "mediaBindStatus")
    private Integer mediaBindStatus;

    @ApiModelProperty(value = "cpId", dataType = "Long", name = "cpId")
    private Long cpId;

    @ApiModelProperty(value = "CP名称", dataType = "String", name = "cpName")
    private String cpName;

    @ApiModelProperty(value = "spId", dataType = "Long", name = "spId")
    private Long spId;

    @ApiModelProperty(value = "SP名称", dataType = "String", name = "spName")
    private String spName;

    @ApiModelProperty(value = "所属渠道id", dataType = "Long", name = "bmsSpChannelId")
    private Long bmsSpChannelId;

    @ApiModelProperty(value = "所属渠道", dataType = "String", name = "bmsSpChannelName")
    private String bmsSpChannelName;

    @ApiModelProperty(value="统计日期",dataType="String",name="statisticDate")
    private String statisticDate;

}
