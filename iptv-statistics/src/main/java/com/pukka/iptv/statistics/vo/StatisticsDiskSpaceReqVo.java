package com.pukka.iptv.statistics.vo;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 *
 * @author: zhengcl
 * @description: 存储统计入参模型
 * @date: 2022年7月13日 下午5:23:51
 */
@Data
@EqualsAndHashCode
@JsonIgnoreProperties(value = { "handler" })
@ApiModel("存储统计相关接口入参模型")
@Accessors(chain = true)
public class StatisticsDiskSpaceReqVo  implements java.io.Serializable{

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value="存储盘id",dataType="Long",name="storageId")
    private Long storageId;

    @ApiModelProperty(value="cpId",dataType="Long",name="cpId")
    private Long cpId;

}
