package com.pukka.iptv.statistics.controller;

import com.pukka.iptv.common.base.enums.StatisticsTypeEnum;
import com.pukka.iptv.common.base.vo.CommonResponse;

import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;

import com.pukka.iptv.common.core.util.StringUtils;
import com.pukka.iptv.common.data.model.statistics.StatisticsIn;
import com.pukka.iptv.common.data.model.statistics.StatisticsInExport;
import com.pukka.iptv.common.data.model.statistics.StatisticsInExportD;
import com.pukka.iptv.common.data.model.statistics.StatisticsInExportDSeries;
import com.pukka.iptv.common.data.vo.req.StatisticsInVo;
import com.pukka.iptv.common.data.vo.req.StatisticsInVoD;
import com.pukka.iptv.common.data.vo.req.StatisticsInVoP;
import com.pukka.iptv.common.data.vo.resp.R;
import com.pukka.iptv.statistics.easyexcel.EasyExcelUtil;
import com.pukka.iptv.statistics.job.StatisticInDeleteJob;
import com.pukka.iptv.statistics.job.StatisticInitJob;
import com.pukka.iptv.statistics.service.StatisticsInService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.apache.poi.hpsf.Decimal;
import org.springframework.http.MediaType;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.web.bind.annotation.*;
import org.springframework.beans.factory.annotation.Autowired;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.AllArgsConstructor;
import com.pukka.iptv.common.data.vo.*;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
/**
 *
 * @author: tan
 * @date: 2022-7-14 11:02:04
 */

@RestController
@AllArgsConstructor
@RequestMapping(value = "/statisticsIn", produces = MediaType.APPLICATION_JSON_VALUE)
@Api(tags="statisticsIn管理")
public class StatisticsInController {

    @Autowired
    private StatisticsInService statisticsInService;

    @Autowired
    private StatisticInDeleteJob statisticInDeleteJob;

    @Autowired
    private StatisticInitJob statisticInitJob;

    @ApiOperation(value = "分页")
    @GetMapping("/page" )
    public CommonResponse<Page> page(@Valid Page page, StatisticsIn statisticsIn) {
        return  CommonResponse.success(statisticsInService.page(page, Wrappers.query(statisticsIn)));
    }

    @ApiOperation(value = "任务")
    @GetMapping("/jobDel" )
    @Scheduled(cron = "0/50 * * * * ?" )
    public CommonResponse jobDel() {
        statisticInDeleteJob.executeJob();
        return  CommonResponse.success(true);
    }

    @ApiOperation(value = "任务")
    @GetMapping("/job" )
//    @Scheduled(cron = "0/30 * * * * ?" )
    public CommonResponse job() {
        statisticInitJob.executeJob();
        return  CommonResponse.success(true);
    }

    @ApiOperation(value = "获取所有注入统计")
    @PostMapping("/all" )
    public CommonResponse<List<StatisticsIn>> all(@RequestBody StatisticsInVo statisticsInVo) {
        //增加时间校验
        if(StringUtils.isEmpty(statisticsInVo.getStartTime()) || StringUtils.isEmpty(statisticsInVo.getEndTime()) ){
            return CommonResponse.fail("时间不能为空！");
        }
        return  statisticsInService.getAllStatistics(statisticsInVo);
    }

    @ApiOperation(value = "获取过去一个月节目注入量")
    @PostMapping("/lastMonth" )
    public CommonResponse<List<StatisticsIn>> lastMonth(@RequestBody StatisticsInVoD statisticsInVo) {
        //增加时间校验
        if(StringUtils.isEmpty(statisticsInVo.getStartTime()) || StringUtils.isEmpty(statisticsInVo.getEndTime()) ){
            return CommonResponse.fail("时间不能为空！");
        }
        return  statisticsInService.getAllStatisticsByLastMonth(statisticsInVo);
    }

    @ApiOperation(value = "获取单集排行")
    @PostMapping("/programRank" )
    public CommonResponse programRank(@RequestBody StatisticsInVoP statisticsInVo) {
        //增加时间校验
        if(StringUtils.isEmpty(statisticsInVo.getStartTime()) || StringUtils.isEmpty(statisticsInVo.getEndTime()) ){
            return CommonResponse.fail("时间不能为空！");
        }
        return  R.page(statisticsInService.getProgramRankStatistics(statisticsInVo));
    }

    @ApiOperation(value = "获取子集排行")
    @PostMapping("/subRank" )
    public CommonResponse subRank(@RequestBody StatisticsInVoP statisticsInVo) {
        //增加时间校验
        if(StringUtils.isEmpty(statisticsInVo.getStartTime()) || StringUtils.isEmpty(statisticsInVo.getEndTime()) ){
            return CommonResponse.fail("时间不能为空！");
        }
        return  R.page(statisticsInService.getProgramRankStatistics(statisticsInVo));
    }

    @ApiOperation(value = "获取剧集排行")
    @PostMapping("/seriesRank" )
    public CommonResponse seriesRank(@RequestBody StatisticsInVoP statisticsInVo) {
        //增加时间校验
        if(StringUtils.isEmpty(statisticsInVo.getStartTime()) || StringUtils.isEmpty(statisticsInVo.getEndTime()) ){
            return CommonResponse.fail("时间不能为空！");
        }
        return  R.page(statisticsInService.getProgramRankStatistics(statisticsInVo));
    }

    @ApiOperation(value = "获取介质排行")
    @PostMapping("/resourceRank" )
    public CommonResponse resourceRank(@RequestBody StatisticsInVoP statisticsInVo) {
        //增加时间校验
        if(StringUtils.isEmpty(statisticsInVo.getStartTime()) || StringUtils.isEmpty(statisticsInVo.getEndTime()) ){
            return CommonResponse.fail("时间不能为空！");
        }
        return  R.page(statisticsInService.getProgramRankStatistics(statisticsInVo));
    }

    @ApiOperation(value = "获取单集、子集、剧集、媒资介质排行导出")
    @PostMapping("/programRankExport" )
    public void programRankExport(@RequestBody StatisticsInVoD statisticsInVoD, HttpServletResponse response) {
        String fileName = null;
        String sheetName = null;
        if(statisticsInVoD.getType().equals(StatisticsTypeEnum.SimpleSet.getValue())){
            fileName = "单集排行";
            sheetName = "单集排行";
        }else if(statisticsInVoD.getType().equals(StatisticsTypeEnum.Subset.getValue())){
            fileName = "子集排行";
            sheetName = "子集排行";
        }else if(statisticsInVoD.getType().equals(StatisticsTypeEnum.Series.getValue())){
            fileName = "剧集排行";
            sheetName = "剧集排行";
        }else{
            fileName = "媒资介质排行";
            sheetName = "媒资介质排行";
        }

        if(statisticsInVoD.getType().equals(StatisticsTypeEnum.Series.getValue())){
            List<StatisticsIn> records = statisticsInService.getProgramRankStatisticsList(statisticsInVoD).getData();
            List<StatisticsInExportDSeries> statisticsInExportList = new ArrayList<>();
            int i = 1;
            float durationValue = 0.00f;
            for(StatisticsIn statisticsIn : records){
                StatisticsInExportDSeries statisticsInExportDSeries = new StatisticsInExportDSeries();
                statisticsInExportDSeries.setCpName(statisticsIn.getCpName());
                statisticsInExportDSeries.setCount(statisticsIn.getCount());

                if( statisticsIn.getDuration() == null){
                    statisticsIn.setDuration(0L);
                }

                statisticsInExportDSeries.setRankId((int) ( i));
                i++;
                statisticsInExportList.add(statisticsInExportDSeries);
            }
            EasyExcelUtil.exportExcel(response, fileName, sheetName, StatisticsInExportDSeries.class, statisticsInExportList);
        }else{
            List<StatisticsIn> records = statisticsInService.getProgramRankStatisticsList(statisticsInVoD).getData();
            List<StatisticsInExportD> statisticsInExportList = new ArrayList<>();
            int i = 1;
            double durationValue = 0.00d;
            for(StatisticsIn statisticsIn : records){
                StatisticsInExportD statisticsInExportD = new StatisticsInExportD();
                statisticsInExportD.setCpName(statisticsIn.getCpName());
                statisticsInExportD.setCount(statisticsIn.getCount());

                if( statisticsIn.getDuration() == null){
                    statisticsIn.setDuration(0L);
                }

                durationValue = statisticsIn.getDuration().doubleValue() / 3600.00;

                statisticsInExportD.setDurationD(String.format("%.2f", durationValue));
                statisticsInExportD.setRankId((int) ( i));
                i++;
                statisticsInExportList.add(statisticsInExportD);
            }
            EasyExcelUtil.exportExcel(response, fileName, sheetName, StatisticsInExportD.class, statisticsInExportList);
        }


    }

}
