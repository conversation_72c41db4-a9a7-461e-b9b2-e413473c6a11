package com.pukka.iptv.statistics.service.impl;

import com.baomidou.mybatisplus.core.toolkit.ObjectUtils;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.pukka.iptv.common.data.model.OutOrderItem;
import com.pukka.iptv.common.data.vo.sys.OutOrderItemVo;
import com.pukka.iptv.statistics.mapper.OutOrderItemMapper;
import com.pukka.iptv.statistics.service.OutOrderItemService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

/**
 * <AUTHOR>
 * @Date 2021/9/9 10:22 上午
 * @description: 子工单
 * @Version 1.0
 */
@Slf4j
@Service
@Transactional
public class OutOrderItemServiceImpl extends ServiceImpl<OutOrderItemMapper, OutOrderItem> implements OutOrderItemService {
    @Autowired
    OutOrderItemMapper outOrderItemMapper;
    /**
     * 查询子工单信息
     *
     * @param outOrderItemVo
     * @return
     */
    @Override
    public OutOrderItem getItemOrderInfo(OutOrderItemVo outOrderItemVo) {
        OutOrderItem outOrderItem1 = null;
        try {
            outOrderItem1 = outOrderItemMapper.getItem(outOrderItemVo);
        } catch (Exception exception) {
            log.error("outPassageCode:{} correlateId:{} ->>>>>> 查询子工单信息失败,错误信息:{}"
                    , outOrderItemVo.getOutPassageCode(), outOrderItemVo.getCorrelateId(), exception);
        }
        if (ObjectUtils.isEmpty(outOrderItem1)) {
            String msg = String.format("outPassageCode:%s correlateId:%s ->>>>>> 查询子工单信息为空,开始重试!"
                    , outOrderItemVo.getOutPassageCode(), outOrderItemVo.getCorrelateId());
            log.error(msg);
        }
        return outOrderItem1;
    }

}
