package com.pukka.iptv.statistics.job.handler;

import com.pukka.iptv.statistics.job.*;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Date;

/**
 * @ClassName StatisticsXxlJob
 * @Description TODO
 * <AUTHOR>
 * @Date 2022/7/15 15:02
 * @Version
 */
@Component
@Slf4j
public class StatisticsInXxlJob {

    @Autowired
    StatisticProgramJob statisticProgramJob;

    @Autowired
    StatisticFinalInitJob statisticFinalJob;

    @Autowired
    StatisticResourceJob statisticResourceJob;

    @Autowired
    StatisticSelfProgramJob statisticSelfProgramJob;

    @Autowired
    StatisticSelfSubJob statisticSelfSubJob;

    @Autowired
    StatisticSelfSeriesJob statisticSelfSeriesJob;

    @Autowired
    StatisticSeriesJob statisticSeriesJob;

    @Autowired
    StatisticSubJob statisticSubJob;

    @Autowired
    StatisticInitJob statisticInitJob;

    @Autowired
    StatisticInDeleteJob statisticInDeleteJob;

    @Autowired
    StatisticCheckInitJob statisticCheckInitJob;

    @Autowired
    StatisticCheckInitSubJob statisticCheckInitSubJob;

    @Autowired
    StatisticCheckInitSeriesJob statisticCheckInitSeriesJob;

    @Autowired
    StatisticFinalInitJob statisticFinalInitJob;

    @Autowired
    StatisticFinalProgramJob statisticFinalProgramJob;

    @Autowired
    StatisticFinalSubJob statisticFinalSubJob;

    @Autowired
    StatisticFinalSeriesJob statisticFinalSeriesJob;
    @Autowired
    StatisticAgainProgramJob statisticAgainProgramJob;

    @Autowired
    StatisticAgainSubJob statisticAgainSubJob;

    @Autowired
    StatisticAgainSeriesJob statisticAgainSeriesJob;


    @Autowired
    StatisticAgainInitJob statisticAgainInitJob;



    /**
     * 根据每日时间 统计单集任务
     * @throws Exception
     */
    @XxlJob("statisticProgramJobHandler")
    public ReturnT<String> statisticProgramJobHandler() throws Exception{

        log.info("XXL-JOB, statisticProgramJobHandler.time={}", new Date());
        try {
            statisticProgramJob.executeJob();
            return ReturnT.SUCCESS;
        } catch (Exception e) {
            log.error("定时统计单集,e={}", e.getMessage());
            return ReturnT.FAIL;
        }
    }

    /**
     *  根据每日时间  统计终审任务
     * @throws Exception
     */
    @XxlJob("statisticFinalJobHandler")
    public void statisticFinalJobHandler() throws Exception{

        log.info("XXL-JOB, statisticFinalJobHandler.time={}", new Date());
        try {
            statisticProgramJob.executeJob();
        } catch (Exception e) {
            log.error("定时终审任务,e={}", e.getMessage());
        }
    }

    @XxlJob("statisticResourceJobHandler")
    public ReturnT<String> statisticResourceJobHandler() throws Exception{
        log.info("XXL-JOB, statisticProgramJobHandler.time={}", new Date());
        try {
            statisticResourceJob.executeJob();
            return ReturnT.SUCCESS;
        } catch (Exception e) {
            log.error("定时统计单集,e={}", e.getMessage());
            return ReturnT.FAIL;
        }
    }

    @XxlJob("statisticSelfProgramJobHandler")
    public ReturnT<String> statisticSelfProgramJobHandler() throws Exception{
        log.info("XXL-JOB, statisticSelfProgramJobHandler.time={}", new Date());
        try {
            statisticSelfProgramJob.executeJob();
            return ReturnT.SUCCESS;
        } catch (Exception e) {
            log.error("定时统计审核单集,e={}", e.getMessage());
            return ReturnT.FAIL;
        }
    }

    @XxlJob("statisticSelfSubJobHandler")
    public ReturnT<String> statisticSelfSubJobHandler() throws Exception{
        log.info("XXL-JOB, statisticSelfSubJobHandler.time={}", new Date());
        try {
            statisticSelfSubJob.executeJob();
            return ReturnT.SUCCESS;
        } catch (Exception e) {
            log.error("定时统计审核子集,e={}", e.getMessage());
            return ReturnT.FAIL;
        }
    }

    @XxlJob("statisticSelfSeriesJobHandler")
    public ReturnT<String> statisticSelfSeriesJobHandler() throws Exception{
        log.info("XXL-JOB, statisticSelfSeriesJobHandler.time={}", new Date());
        try {
            statisticSelfSeriesJob.executeJob();
            return ReturnT.SUCCESS;
        } catch (Exception e) {
            log.error("定时统计审核剧集,e={}", e.getMessage());
            return ReturnT.FAIL;
        }
    }

    @XxlJob("statisticFinalProgramJobHandler")
    public ReturnT<String> statisticFinalProgramJobHandler() throws Exception{
        log.info("XXL-JOB, statisticFinalProgramJobHandler.time={}", new Date());
        try {
            statisticFinalProgramJob.executeJob();
            return ReturnT.SUCCESS;
        } catch (Exception e) {
            log.error("定时统计审核单集,e={}", e.getMessage());
            return ReturnT.FAIL;
        }
    }

    @XxlJob("statisticFinalSubJobHandler")
    public ReturnT<String> statisticFinalSubJobHandler() throws Exception{
        log.info("XXL-JOB, statisticFinalSubJobHandler.time={}", new Date());
        try {
            statisticFinalSubJob.executeJob();
            return ReturnT.SUCCESS;
        } catch (Exception e) {
            log.error("定时统计终审子集,e={}", e.getMessage());
            return ReturnT.FAIL;
        }
    }

    @XxlJob("statisticFinalSeriesJobHandler")
    public ReturnT<String> statisticFinalSeriesJobHandler() throws Exception{
        log.info("XXL-JOB, statisticFinalSeriesJobHandler.time={}", new Date());
        try {
            statisticFinalSeriesJob.executeJob();
            return ReturnT.SUCCESS;
        } catch (Exception e) {
            log.error("定时统计终审剧集,e={}", e.getMessage());
            return ReturnT.FAIL;
        }
    }

    @XxlJob("statisticAgainProgramJobHandler")
    public ReturnT<String> statisticAgainProgramJobHandler() throws Exception{
        log.info("XXL-JOB, statisticAgainProgramJobHandler.time={}", new Date());
        try {
            statisticAgainProgramJob.executeJob();
            return ReturnT.SUCCESS;
        } catch (Exception e) {
            log.error("定时统计重审单集,e={}", e.getMessage());
            return ReturnT.FAIL;
        }
    }

    @XxlJob("statisticAgainSubJobHandler")
    public ReturnT<String> statisticAgainSubJobHandler() throws Exception{
        log.info("XXL-JOB, statisticAgainSubJobHandler.time={}", new Date());
        try {
            statisticAgainSubJob.executeJob();
            return ReturnT.SUCCESS;
        } catch (Exception e) {
            log.error("定时统计重审子集,e={}", e.getMessage());
            return ReturnT.FAIL;
        }
    }

    @XxlJob("statisticAgainSeriesJobHandler")
    public ReturnT<String> statisticAgainSeriesJobHandler() throws Exception{
        log.info("XXL-JOB, statisticAgainSeriesJobHandler.time={}", new Date());
        try {
            statisticAgainSeriesJob.executeJob();
            return ReturnT.SUCCESS;
        } catch (Exception e) {
            log.error("定时统计重审剧集,e={}", e.getMessage());
            return ReturnT.FAIL;
        }
    }

    @XxlJob("statisticSeriesJobHandler")
    public ReturnT<String> statisticSeriesJobHandler() throws Exception{
        log.info("XXL-JOB, statisticSubJobHandler.time={}", new Date());
        try {
            statisticSeriesJob.executeJob();
            return ReturnT.SUCCESS;
        } catch (Exception e) {
            log.error("定时统计剧集,e={}", e.getMessage());
            return ReturnT.FAIL;
        }
    }

    @XxlJob("statisticSubJobHandler")
    public ReturnT<String> statisticSubJobHandler() throws Exception{
        log.info("XXL-JOB, statisticSubJobHandler.time={}", new Date());
        try {
            statisticSubJob.executeJob();
            return ReturnT.SUCCESS;
        } catch (Exception e) {
            log.error("定时统计子集,e={}", e.getMessage());
            return ReturnT.FAIL;
        }

    }

    @XxlJob("statisticInitJobHandler")
    public ReturnT<String> statisticInitJobHandler() throws Exception{
        log.info("XXL-JOB, statisticSubJobHandler.time={}", new Date());
        try {
            statisticInitJob.executeJob();
            return ReturnT.SUCCESS;
        } catch (Exception e) {
            log.error("定时统计子集,e={}", e.getMessage());
            return ReturnT.FAIL;
        }

    }

    @XxlJob("statisticInDeleteJobHandler")
    public ReturnT<String> statisticInDeleteJobHandler() throws Exception{
        log.info("XXL-JOB, statisticInDeleteJobHandler.time={}", new Date());
        try {
            statisticInDeleteJob.executeJob();
            return ReturnT.SUCCESS;
        } catch (Exception e) {
            log.error("定时统计子集,e={}", e.getMessage());
            return ReturnT.FAIL;
        }

    }

    @XxlJob("statisticInitSelfJobHandler")
    public ReturnT<String> statisticInitSelfJobHandler() throws Exception{
        log.info("XXL-JOB, statisticInitSelfJobHandler.time={}", new Date());
        try {
            statisticCheckInitJob.executeJob();
            return ReturnT.SUCCESS;
        } catch (Exception e) {
            log.error("初始化自审,e={}", e.getMessage());
            return ReturnT.FAIL;
        }

    }

    @XxlJob("statisticInitSelfSubJobHandler")
    public ReturnT<String> statisticInitSelfSubJobHandler() throws Exception{
        log.info("XXL-JOB, statisticInitSelfSubJobHandler.time={}", new Date());
        try {
            statisticCheckInitSubJob.executeJob();
            return ReturnT.SUCCESS;
        } catch (Exception e) {
            log.error("初始化自审,e={}", e.getMessage());
            return ReturnT.FAIL;
        }

    }

    @XxlJob("statisticInitSelfSeriesJobHandler")
    public ReturnT<String> statisticInitSelfSeriesJobHandler() throws Exception{
        log.info("XXL-JOB, statisticInitSelfSeriesJobHandler.time={}", new Date());
        try {
            statisticCheckInitSeriesJob.executeJob();
            return ReturnT.SUCCESS;
        } catch (Exception e) {
            log.error("初始化自审,e={}", e.getMessage());
            return ReturnT.FAIL;
        }

    }

    @XxlJob("statisticInitFinalJobHandler")
    public ReturnT<String> statisticInitFinalJobHandler() throws Exception{
        log.info("XXL-JOB, statisticInitFinalJobHandler.time={}", new Date());
        try {
            statisticFinalInitJob.executeJob();
            return ReturnT.SUCCESS;
        } catch (Exception e) {
            log.error("初始化终审,e={}", e.getMessage());
            return ReturnT.FAIL;
        }

    }

    @XxlJob("statisticInitAgainJobHandler")
    public ReturnT<String> statisticInitAgainJobHandler() throws Exception{
        log.info("XXL-JOB, statisticInitAgainJobHandler.time={}", new Date());
        try {
            statisticAgainInitJob.executeJob();
            return ReturnT.SUCCESS;
        } catch (Exception e) {
            log.error("初始化重审,e={}", e.getMessage());
            return ReturnT.FAIL;
        }

    }
}
