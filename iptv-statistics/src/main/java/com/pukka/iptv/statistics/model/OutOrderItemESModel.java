package com.pukka.iptv.statistics.model;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.Setter;
import org.springframework.data.elasticsearch.annotations.Document;

import java.io.Serializable;
import java.util.Date;

/**
 * @author: wangrh
 * @description: 工单ES模型
 * @date: 2022/7/14
 **/
@Data
@Setter
@Getter
@Document(indexName = "out_order_item")
@EqualsAndHashCode
@ApiModel("OutOrderItemESModel")
public class OutOrderItemESModel implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "任务分发", dataType = "Long", name = "id")
    private Long id;

    @ApiModelProperty(value = "分表表名", dataType = "String", name = "tableName")
    private String tableName;

    @ApiModelProperty(value = "baseOrderId", dataType = "Long", name = "baseOrderId")
    private Long baseOrderId;

    @ApiModelProperty(value = "关联ID", dataType = "String", name = "correlateId")
    private String correlateId;

    @ApiModelProperty(value = "动作 1：REGIST，2：UPDATE，3：DELETE", dataType = "Integer", name = "action")
    private Integer action;

    @ApiModelProperty(value = "显示名称 ##IsSearch", dataType = "String", name = "showName")
    private String showName;

    @ApiModelProperty(value = "bms的内容ID", dataType = "Long", name = "bmsContentId")
    private Long bmsContentId;

    @ApiModelProperty(value = "content_code全局唯一标识", dataType = "String", name = "bmsContentCode")
    private String bmsContentCode;

    @ApiModelProperty(value = "1：电影 2：子集 3：电视剧 4：系列片 5：片花 6：直播 7：物理频道 8：栏目 9：产品包 10：节目单 11：图片 12：视频介质 13：节目图片 14：剧集图片 15：产品包图片 16：栏目图片 17：频道图片 18：栏目节目 19：栏目剧集 20：栏目频道 21：产品包节目 22：产品包剧集", dataType = "String", name = "contentType")
    private String contentType;

    @ApiModelProperty(value = "CSPID", dataType = "String", name = "cspId")
    private String cspId;

    @ApiModelProperty(value = "LSPID", dataType = "String", name = "lspId")
    private String lspId;

    @ApiModelProperty(value = "分发通道ID，", dataType = "Long", name = "outPassageId")
    private Long outPassageId;

    @ApiModelProperty(value = "分发通道code", dataType = "String", name = "outPassageCode")
    private String outPassageCode;

    @ApiModelProperty(value = "分发通道", dataType = "String", name = "outPassageName")
    private String outPassageName;

    @ApiModelProperty(value = "工单文件", dataType = "String", name = "cmdFileUrl")
    private String cmdFileUrl;

    @ApiModelProperty(value = "状态 1：待处理 2：处理中 3：处理成功 4：处理失败", dataType = "Integer", name = "status")
    private Integer status;

    @ApiModelProperty(value = "状态描述", dataType = "String", name = "statusDescription")
    private String statusDescription;

    @ApiModelProperty(value = "优先级 ##SelectList{1:低,5:中:8:高:10:紧急}", dataType = "Integer", name = "priority")
    private Integer priority;

    @ApiModelProperty(value = "1：普通发布 2：队列发布", dataType = "String", name = "publishType")
    private String publishType;

    @ApiModelProperty(value = "接口结果 {0:成功,-1:失败,1=处理中}", dataType = "Integer", name = "result")
    private Integer result;

    @ApiModelProperty(value = "错误描述", dataType = "String", name = "errorDescription")
    private String errorDescription;

    @ApiModelProperty(value = "错误反馈", dataType = "String", name = "errorInfo")
    private String errorInfo;

    @ApiModelProperty(value = "创建时间", dataType = "String", name = "createTime")
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date createTime;

    @ApiModelProperty(value = "重发次数", dataType = "Integer", name = "retryCount")
    private Integer retryCount;

    @ApiModelProperty(value = "更新时间", dataType = "String", name = "updateTime")
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date updateTime;

    @ApiModelProperty(value = "操作员id", dataType = "Long", name = "creatorId")
    private Long creatorId;

    @ApiModelProperty(value = "操作员name", dataType = "String", name = "creatorName")
    private String creatorName;

    @ApiModelProperty(value = "渠道id，来自sys _dictionary_itemsl表", dataType = "Long", name = "bmsSpChannelId")
    private Long bmsSpChannelId;

    @ApiModelProperty(value = "所属渠道 ", dataType = "String", name = "bmsSpChannelName")
    private String bmsSpChannelName;

    @ApiModelProperty(value = "所属渠道 ", dataType = "String", name = "bmsSpChannelCode")
    private String bmsSpChannelCode;

    @ApiModelProperty(value = "类型 ", dataType = "String", name = "publishContentType")
    private String publishContentType;

    @ApiModelProperty(value = "类型 ", dataType = "String", name = "spName")
    private String spName;

    @ApiModelProperty(value = "类型 ", dataType = "String", name = "cpName")
    private String cpName;

    @ApiModelProperty(value = "重发时间", dataType = "String", name = "retryTime")
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date retryTime;

    @ApiModelProperty(value="是否存在子节点",dataType="Integer",name="childNodeExist")
    private Integer childNodeExist;

    @ApiModelProperty(value="父节点唯一ID ",dataType="String",name="parentCorrelateId")
    private String parentCorrelateId;

    @ApiModelProperty(value="发布耗时（从开始发布到收到回调通知）",dataType="Integer",name="duration")
    private Integer duration;

    @ApiModelProperty(value = "工单下游反馈完成时间", dataType = "String", name = "feedbackTime")
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date feedbackTime;
}
