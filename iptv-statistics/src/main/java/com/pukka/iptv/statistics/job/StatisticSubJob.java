package com.pukka.iptv.statistics.job;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.pukka.iptv.common.api.feign.cms.CmsProgramFeignClient;
import com.pukka.iptv.common.base.enums.StatisticsTypeEnum;
import com.pukka.iptv.common.base.vo.CommonResponse;
import com.pukka.iptv.common.data.model.statistics.StatisticsIn;
import com.pukka.iptv.common.data.vo.req.StatisticsInVo;
import com.pukka.iptv.statistics.service.StatisticsInService;
import com.pukka.iptv.statistics.util.DateUtil;
import feign.Request;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.concurrent.TimeUnit;

/**
 * @ClassName StatisticSubJob
 * @Description TODO
 * <AUTHOR>
 * @Date 2022/7/14 11:45
 * @Version
 */
@Slf4j
@Component
public class StatisticSubJob implements IStatisticJob{

    @Autowired
    CmsProgramFeignClient cmsProgramFeignClient;

    @Autowired
    StatisticsInService statisticsInService;

    @Override
    public void executeJob() {
        StatisticsInVo statisticsInVo = new StatisticsInVo();
        Map map = DateUtil.getYesterdayTime();
        statisticsInVo.setStartTime((String) map.get("startDate"));
        statisticsInVo.setEndTime((String) map.get("endDate"));

        Request.Options options = new Request.Options(5, TimeUnit.SECONDS, 60, TimeUnit.SECONDS, true);
        CommonResponse<List<StatisticsIn>> commonResponse =  cmsProgramFeignClient.subStatistic(statisticsInVo, options);
        List<StatisticsIn> statisticsInList = (List<StatisticsIn>) commonResponse.getData();
        List<StatisticsIn> statisticsInsListAdd = new ArrayList<>();
        List<StatisticsIn> statisticsInListUpDate = new ArrayList<>();
        LambdaQueryWrapper<StatisticsIn> queryWrapper = Wrappers.lambdaQuery();
        //删除这天的数据
        queryWrapper.clear();
        queryWrapper.eq(StatisticsIn::getStatisticDate, statisticsInVo.getStartTime())
                .eq(StatisticsIn::getType, StatisticsTypeEnum.Subset.getValue());
        boolean flagRemove = statisticsInService.remove(queryWrapper);
        if(true){
            statisticsInList.forEach(e ->
            {
                statisticsInsListAdd.add(e);
            });
        }

        if(statisticsInsListAdd.size() >0 ){
            Boolean isSave = statisticsInService.saveBatch(statisticsInList);
            if(isSave){
                log.info("StatisticSubJob ->>>>> time = {},size = {}", statisticsInVo.getStartTime(),statisticsInList.size());
            }else{
                log.info("StatisticSubJob is fail ->>>>> time = {}",statisticsInVo.getStartTime() );
            }
        }
        if(statisticsInListUpDate.size() >0 ){
            Boolean isSave = statisticsInService.saveOrUpdateBatch(statisticsInList);
            if(isSave){
                log.info("StatisticSubJob statisticsInListUpDate->>>>> time = {},size = {}", statisticsInVo.getStartTime(),statisticsInList.size());
            }else{
                log.info("StatisticSubJob is statisticsInListUpDate fail ->>>>> time = {}",statisticsInVo.getStartTime() );
            }
        }
        log.info("StatisticSubJob getYesterdayTime finish->>>>> time = {},size = {}", statisticsInVo.getStartTime(), statisticsInsListAdd.size());

    }

    public boolean executeJob(StatisticsInVo statisticsInVo, boolean flag) {

        Request.Options options = new Request.Options(5, TimeUnit.SECONDS, 600, TimeUnit.SECONDS, true);
        CommonResponse<List<StatisticsIn>> commonResponse =  cmsProgramFeignClient.subStatistic(statisticsInVo, options);
        List<StatisticsIn> statisticsInList = (List<StatisticsIn>) commonResponse.getData();
        List<StatisticsIn> statisticsInsListAdd = new ArrayList<>();
        List<StatisticsIn> statisticsInListUpDate = new ArrayList<>();



        LambdaQueryWrapper<StatisticsIn> queryWrapper = Wrappers.lambdaQuery();

        //删除这天的数据
        queryWrapper.clear();
        queryWrapper.eq(StatisticsIn::getStatisticDate, statisticsInVo.getStartTime())
                .eq(StatisticsIn::getType, StatisticsTypeEnum.Subset.getValue());
        boolean flagRemove = statisticsInService.remove(queryWrapper);

        if(true){
            statisticsInList.forEach(e ->
            {
                statisticsInsListAdd.add(e);
            });
        }

        if(statisticsInsListAdd.size() >0 ){
            Boolean isSave = statisticsInService.saveBatch(statisticsInsListAdd);
            if(isSave){
                log.info("StatisticSubJob ->>>>> time = {},size = {}", statisticsInVo.getStartTime(),statisticsInList.size());
            }else{
                log.info("StatisticSubJob is fail ->>>>> time = {}",statisticsInVo.getStartTime() );
            }
        }
        if(statisticsInListUpDate.size() >0 ){
            Boolean isSave = statisticsInService.saveOrUpdateBatch(statisticsInListUpDate);
            if(isSave){
                log.info("StatisticProgramJob ->>>>> time = {},size = {}", statisticsInVo.getStartTime(),statisticsInList.size());
            }else{
                log.info("StatisticProgramJob is fail ->>>>> time = {}",statisticsInVo.getStartTime() );
            }
        }
        log.info("StatisticSubJob ->>>>> time = {},size = {}", statisticsInVo.getStartTime(), statisticsInsListAdd.size());

        return true;
    }
}
