package com.pukka.iptv.statistics.vo;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * @author: zhengcl
 * @date: 2022年7月13日 上午9:55:27
 */

@Data
@EqualsAndHashCode
@JsonIgnoreProperties(value = {"handler"})
@Accessors(chain = true)
public class StatisticsOnlineRankRespVo implements java.io.Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "排名", dataType = "Integer", name = "rank")
    private Integer rankId;

    @ApiModelProperty(value = "spId", dataType = "Long", name = "spId")
    private Long spId;

    @ApiModelProperty(value = "spName", dataType = "String", name = "spName")
    private String spName;

    @ApiModelProperty(value = "cpId", dataType = "Long", name = "cpId")
    private Long cpId;

    @ApiModelProperty(value = "cpName", dataType = "String", name = "cpName")
    private String cpName;

    @ApiModelProperty(value = "视频长度(H)", dataType = "Long", name = "duration")
    private Double duration;

    @ApiModelProperty(value = "视频大小(G)", dataType = "Double", name = "fileSize")
    private Double fileSize;

    @ApiModelProperty(value = "媒资数量", dataType = "Long", name = "count")
    private Long count;

}
