package com.pukka.iptv.statistics.controller;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;
import com.pukka.iptv.common.base.enums.ContentTypeEnum;
import com.pukka.iptv.common.base.exception.CommonResponseException;
import com.pukka.iptv.common.base.vo.CommonResponse;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;

import com.pukka.iptv.common.core.util.DateUtils;
import com.pukka.iptv.statistics.easyexcel.EasyExcelUtil;
import com.pukka.iptv.statistics.model.BmsContentESModel;
import com.pukka.iptv.statistics.model.BmsProgramESModel;
import com.pukka.iptv.statistics.service.es.ESService;
import com.pukka.iptv.statistics.service.statistics.StatisticsOnlineService;
import com.pukka.iptv.statistics.vo.*;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.*;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.AllArgsConstructor;

import java.math.BigDecimal;
import java.util.*;
import java.util.concurrent.CountDownLatch;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * @author: zhengcl
 * @date: 2022年7月13日 上午9:55:27
 */
@RestController
@AllArgsConstructor
@Slf4j
@RequestMapping(value = "/statisticsOnline", produces = MediaType.APPLICATION_JSON_VALUE)
@Api(tags = "下发统计管理")
public class StatisticsOnlineController {

    @Resource
    private StatisticsOnlineService statisticsOnlineService;
    @Resource
    private ESService esService;

    @ApiOperation(value = "线上节目汇总")
    @GetMapping("/getOnlineContentTotal")
    public CommonResponse<StatisticsOnlineRespVo> getOnlineContentTotal(@Valid StatisticsOnlineReqVo statisticsOnline) {
        return CommonResponse.success(statisticsOnlineService.getOnlineContentTotal(statisticsOnline));
    }

    @ApiOperation(value = "节目上新总量查询")
    @GetMapping("/getNewOnlineContentTotal")
    public CommonResponse<StatisticsOnlineRespVo> getNewOnlineContentTotal(@Valid StatisticsOnlineReqVo statisticsOnline) {
        return CommonResponse.success(statisticsOnlineService.getNewOnlineContentTotal(statisticsOnline));
    }

    @ApiOperation(value = "节目上新柱状图查询")
    @GetMapping("/getNewOnlineContentHistogram")
    public CommonResponse<List<StatisticsOnlineRespVo>> getNewOnlineContentHistogram(@Valid StatisticsOnlineReqVo vo) {
        Integer flag = vo.getFlag();
        if (null == flag) {
            throw new CommonResponseException("参数flag不能为空");
        }
        int downLatchCount = 0;
        if (1 == flag) {
            downLatchCount = 7;
        } else if (2 == flag) {
            downLatchCount = 30;
        }
        CountDownLatch downLatch = new CountDownLatch(downLatchCount);
        List<StatisticsOnlineRespVo> list = new ArrayList<>();
        HashMap<String, List<Long>> permissionMap = esService.getPermission();
        for (int i = 0; i < downLatchCount; i++) {
            StatisticsOnlineReqVo statisticsOnlineReqVo = BeanUtil.copyProperties(vo, StatisticsOnlineReqVo.class);
            Date startTime = DateUtil.beginOfDay(DateUtils.getBeforeDateDay(i));
            statisticsOnlineReqVo.setStartTime(DateUtil.format(startTime, DateUtils.YYYY_MM_DD));
            statisticsOnlineReqVo.setEndTime(DateUtil.format(startTime, DateUtils.YYYY_MM_DD));
            statisticsOnlineService.getNewOnlineContentAsync(list, statisticsOnlineReqVo, downLatch, permissionMap);
        }
        try {
            boolean await = downLatch.await(1, TimeUnit.MINUTES);
        } catch (Exception e) {
            e.printStackTrace();
        }
        list = list.stream().sorted(Comparator.comparing(StatisticsOnlineRespVo::getStatisticDate)).collect(Collectors.toList());
        return CommonResponse.success(list);
    }

    @ApiOperation(value = "节目下线总量查询")
    @GetMapping("/getOfflineContentTotal")
    public CommonResponse<StatisticsOnlineRespVo> getOfflineContentTotal(@Valid StatisticsOnlineReqVo statisticsOnline) {
        return CommonResponse.success(statisticsOnlineService.getOfflineContentTotal(statisticsOnline));
    }

    @ApiOperation(value = "节目下线柱状图查询")
    @GetMapping("/getOfflineContentHistogram")
    public CommonResponse<List<StatisticsOnlineRespVo>> getOfflineContentHistogram(StatisticsOnlineReqVo vo) {
        Integer flag = vo.getFlag();
        if (null == flag) {
            throw new CommonResponseException("参数flag不能为空");
        }
        int downLatchCount = 0;
        if (1 == flag) {
            downLatchCount = 7;
        } else if (2 == flag) {
            downLatchCount = 30;
        }
        CountDownLatch downLatch = new CountDownLatch(downLatchCount);
        List<StatisticsOnlineRespVo> list = new ArrayList<>();
        HashMap<String, List<Long>> permissionMap = esService.getPermission();
        for (int i = 0; i < downLatchCount; i++) {
            StatisticsOnlineReqVo statisticsOnlineReqVo = BeanUtil.copyProperties(vo, StatisticsOnlineReqVo.class);
            Date startTime = DateUtil.beginOfDay(DateUtils.getBeforeDateDay(i));
            statisticsOnlineReqVo.setStartTime(DateUtil.format(startTime, DateUtils.YYYY_MM_DD));
            statisticsOnlineReqVo.setEndTime(DateUtil.format(startTime, DateUtils.YYYY_MM_DD));
            statisticsOnlineService.getOfflineContentAsync(list, statisticsOnlineReqVo, downLatch, permissionMap);
        }
        try {
            boolean await = downLatch.await(1, TimeUnit.MINUTES);
        } catch (Exception e) {
            e.printStackTrace();
        }
        list = list.stream().sorted(Comparator.comparing(StatisticsOnlineRespVo::getStatisticDate)).collect(Collectors.toList());
        return CommonResponse.success(list);
    }

    @ApiOperation(value = "下发排行查询")
    @GetMapping("/getNewOnlineContentTotalRank")
    public CommonResponse<Page<StatisticsOnlineRankRespVo>> getNewOnlineContentTotalRank(@Valid Page page, StatisticsOnlineReqVo statisticsOnline) {
        if (1 != statisticsOnline.getType() && 2 != statisticsOnline.getType()) {
            throw new CommonResponseException("参数type错误");
        }
        return CommonResponse.success(statisticsOnlineService.getNewOnlineContentTotalRank(page, statisticsOnline));
    }

    @GetMapping("/exportExcelNewOnlineContent")
    @ApiOperation(value = "下发排行导出Excel")
    public void exportExcelNewOnlineContent(@Valid Page page, StatisticsOnlineReqVo statisticsOnline, HttpServletResponse response) {
        String fileName = "下发排行";
        String sheetName = "";
        Integer type = statisticsOnline.getType();
        Page newOnlineContentTotalRank = statisticsOnlineService.getNewOnlineContentTotalRank(page, statisticsOnline);
        List<StatisticsOnlineRankRespVo> records = newOnlineContentTotalRank.getRecords();
        if (CollUtil.isNotEmpty(records)) {
            for (StatisticsOnlineRankRespVo record : records) {
                record.setDuration(new BigDecimal(record.getDuration()).divide(new BigDecimal(3600d),2,BigDecimal.ROUND_HALF_UP).doubleValue());
                record.setFileSize(new BigDecimal(record.getFileSize()).divide(new BigDecimal(1073741824d),2, BigDecimal.ROUND_HALF_UP).doubleValue());
            }
        }
        if (1 == type) {
            if (ContentTypeEnum.FILM.getValue().equals(statisticsOnline.getContentType())) {
                sheetName = ContentTypeEnum.FILM.getDesc();
                List<StatisticsOnlineProgramCpVo> recordList = JSON.parseObject(JSON.toJSONString(records),
                        new TypeReference<List<StatisticsOnlineProgramCpVo>>() {
                        });
                EasyExcelUtil.exportExcel(response, fileName, sheetName, StatisticsOnlineProgramCpVo.class, recordList);
            } else if (ContentTypeEnum.TELEPLAY.getValue().equals(statisticsOnline.getContentType())) {
                sheetName = ContentTypeEnum.TELEPLAY.getDesc();
                List<StatisticsOnlineSeriesCpVo> recordList = JSON.parseObject(JSON.toJSONString(records),
                        new TypeReference<List<StatisticsOnlineSeriesCpVo>>() {
                        });
                EasyExcelUtil.exportExcel(response, fileName, sheetName, StatisticsOnlineSeriesCpVo.class, recordList);
            } else if (ContentTypeEnum.SUBSET.getValue().equals(statisticsOnline.getContentType())) {
                sheetName = ContentTypeEnum.SUBSET.getDesc();
                List<StatisticsOnlineSubsetCpVo> recordList = JSON.parseObject(JSON.toJSONString(records),
                        new TypeReference<List<StatisticsOnlineSubsetCpVo>>() {
                        });
                EasyExcelUtil.exportExcel(response, fileName, sheetName, StatisticsOnlineSubsetCpVo.class, recordList);
            }
        } else {
            if (ContentTypeEnum.FILM.getValue().equals(statisticsOnline.getContentType())) {
                sheetName = ContentTypeEnum.FILM.getDesc();
                List<StatisticsOnlineProgramSpVo> recordList = JSON.parseObject(JSON.toJSONString(records),
                        new TypeReference<List<StatisticsOnlineProgramSpVo>>() {
                        });
                EasyExcelUtil.exportExcel(response, fileName, sheetName, StatisticsOnlineProgramSpVo.class, recordList);
            } else if (ContentTypeEnum.TELEPLAY.getValue().equals(statisticsOnline.getContentType())) {
                sheetName = ContentTypeEnum.TELEPLAY.getDesc();
                List<StatisticsOnlineSeriesSpVo> recordList = JSON.parseObject(JSON.toJSONString(records),
                        new TypeReference<List<StatisticsOnlineSeriesSpVo>>() {
                        });
                EasyExcelUtil.exportExcel(response, fileName, sheetName, StatisticsOnlineSeriesSpVo.class, recordList);
            } else if (ContentTypeEnum.SUBSET.getValue().equals(statisticsOnline.getContentType())) {
                sheetName = ContentTypeEnum.SUBSET.getDesc();
                List<StatisticsOnlineSubsetSpVo> recordList = JSON.parseObject(JSON.toJSONString(records),
                        new TypeReference<List<StatisticsOnlineSubsetSpVo>>() {
                        });
                EasyExcelUtil.exportExcel(response, fileName, sheetName, StatisticsOnlineSubsetSpVo.class, recordList);
            }
        }
    }

    @GetMapping("/exportDetailList")
    @ApiOperation(value = "节目上新明细导出Excel")
    public void exportDetailList(StatisticsOnlineReqVo statisticsOnline, HttpServletResponse response) {
        String fileName = "节目上新";
        Integer mediaBindStatus = statisticsOnline.getMediaBindStatus();
        if (null == mediaBindStatus) {
            throw new CommonResponseException("参数mediaBindStatus不能为空");
        }
        List<BmsContentESModel> bmsContentESModels = esService.getNewOnlineContentList(statisticsOnline,
                ContentTypeEnum.TELEPLAY.getValue());
        List<BmsContentESModel> bmsContentESModels1 = esService.getNewOnlineContentList(statisticsOnline,
                ContentTypeEnum.FILM.getValue());
        List<BmsProgramESModel> bmsProgramESModels = esService.getNewOnlineSubsetList(statisticsOnline);
        List<String> sheetNames = new ArrayList<>();
        sheetNames.add(ContentTypeEnum.FILM.getDesc());
        sheetNames.add("剧集");
        sheetNames.add(ContentTypeEnum.SUBSET.getDesc());

        List<List> initDatas = new ArrayList<>();
        initDatas.add(bmsContentESModels1);
        initDatas.add(bmsContentESModels);
        initDatas.add(bmsProgramESModels);

        List<Class> clzs = new ArrayList<>();
        clzs.add(BmsContentESModel.class);
        clzs.add(BmsContentESModel.class);
        clzs.add(BmsProgramESModel.class);
        EasyExcelUtil.exportExcel(response, fileName, sheetNames, clzs, initDatas);

    }

    @GetMapping("/clearCache")
    @ApiOperation(value = "清除缓存接口")
    public void clearCache() {
        statisticsOnlineService.clearCache();
    }

}
