package com.pukka.iptv.statistics.model;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModelProperty;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.EqualsAndHashCode;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 *
 * @author: zhengcl
 * @date: 2022年7月13日 上午9:55:36
 */

@Data
@EqualsAndHashCode
@JsonIgnoreProperties(value = { "handler" })
@TableName(value = "statistics_publish",autoResultMap=true)
@Accessors(chain = true)
public class StatisticsPublish extends Model<StatisticsPublish> implements java.io.Serializable{

    private static final long serialVersionUID = 1L;
	/**主键*/
	@TableField(value = "id")
    @ApiModelProperty(value="主键",dataType="Long",name="id")
    private Long id;

	/**内容类型\ 1：电影  2：子集 3：电视剧 4：系列片 5：片花*/
	@TableField(value = "content_type")
    @ApiModelProperty(value="内容类型 1：电影  2：子集 3：电视剧 4：系列片 5：片花",dataType="Integer",name="contentType")
    private Integer contentType;

	/**媒资数量*/
	@TableField(value = "count")
    @ApiModelProperty(value="媒资数量",dataType="Long",name="count")
    private Long count;

	/**统计类型 1=发布 2=回收*/
	@TableField(value = "type")
    @ApiModelProperty(value="统计类型 1=发布 3=回收",dataType="Integer",name="type")
    private Integer type;

	/**cpId*/
	@TableField(value = "cp_id")
    @ApiModelProperty(value="cpId",dataType="Long",name="cpId")
    private Long cpId;

	/**CP名称*/
	@TableField(value = "cp_name")
    @ApiModelProperty(value="CP名称",dataType="String",name="cpName")
    private String cpName;

	/**spId*/
	@TableField(value = "sp_id")
    @ApiModelProperty(value="spId",dataType="Long",name="spId")
    private Long spId;

	/**SP名称*/
	@TableField(value = "sp_name")
    @ApiModelProperty(value="SP名称",dataType="String",name="spName")
    private String spName;

	/**所属渠道*/
	@TableField(value = "bms_sp_channel_name")
    @ApiModelProperty(value="所属渠道",dataType="String",name="bmsSpChannelName")
    private String bmsSpChannelName;

	/**所属渠道id*/
	@TableField(value = "bms_sp_channel_id")
    @ApiModelProperty(value="所属渠道id",dataType="Long",name="bmsSpChannelId")
    private Long bmsSpChannelId;

    /**所属渠道code*/
    @TableField(value = "bms_sp_channel_code")
    @ApiModelProperty(value="所属渠道code",dataType="String",name="bmsSpChannelCode")
    private String bmsSpChannelCode;

    /**统计时间*/
    @TableField(value = "statistic_date")
    @ApiModelProperty(value="统计时间",dataType="String",name="statisticDate")
    private String statisticDate;

	/**创建时间*/
	@TableField(value = "create_time")
    @ApiModelProperty(value="创建时间",dataType="String",name="createTime")
    private String createTime;

	/**更新时间*/
	@TableField(value = "update_time")
    @ApiModelProperty(value="更新时间",dataType="String",name="updateTime")
    private String updateTime;

}
