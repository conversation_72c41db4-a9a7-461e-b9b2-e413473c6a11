package com.pukka.iptv.statistics.job;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.pukka.iptv.common.data.model.statistics.StatisticsInCheckInit;
import com.pukka.iptv.common.data.util.TimeUtil;
import com.pukka.iptv.common.data.vo.req.StatisticsInVo;
import com.pukka.iptv.statistics.service.StatisticsInCheckInitService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.List;

/**
 * @ClassName StatisticInit
 * @Description TODO
 * <AUTHOR>
 * @Date 2022/7/21 15:57
 * @Version
 */
@Component
public class StatisticCheckInitSubJob implements IStatisticJob{

    @Autowired
    StatisticsInCheckInitService statisticsInCheckInitService;

    @Autowired
    StatisticSelfProgramJob statisticSelfProgramJob;

    @Autowired
    StatisticSelfSeriesJob statisticSelfSeriesJob;
    @Autowired
    StatisticSelfSubJob statisticSelfSubJobSubJob;

    @Override
    public void executeJob() {

        LambdaQueryWrapper<StatisticsInCheckInit> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.eq(StatisticsInCheckInit::getStatus, 1);
        List<StatisticsInCheckInit> statisticInitList = statisticsInCheckInitService.list(queryWrapper);
        statisticInitList.forEach(e -> {

            SimpleDateFormat formater = new SimpleDateFormat("yyyy-MM-dd");
            SimpleDateFormat formater2 = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
            Date start = null;
            Date end = null;
            try {
                start = formater2.parse(formater.format(TimeUtil.getDate(e.getStatisticDate()))+ " 00:00:00");
                end = formater2.parse(formater.format(TimeUtil.getDate(e.getStatisticDate()))+ " 23:59:59");
            } catch (ParseException parseException) {
                parseException.printStackTrace();
            }

            StatisticsInVo statisticsInVo = new StatisticsInVo();
            statisticsInVo.setStartTime(TimeUtil.formatDate(start));

            statisticsInVo.setEndTime(TimeUtil.formatDate(end));
            statisticSelfSubJobSubJob.executeJob(statisticsInVo);
        });


    }
}
