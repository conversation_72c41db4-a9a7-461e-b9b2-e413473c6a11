package com.pukka.iptv.statistics.mapper.sys;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.pukka.iptv.common.data.model.sys.SysSp;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * @author: tan
 * @date: 2021-9-2 22:54:04
 */

@Mapper
public interface SysSpMapper extends BaseMapper<SysSp> {

    List<String> selectLspIdListBySp(@Param("spId") Long spId);

    List<String> selectLspIdListBySpIds(@Param("spIdList") List<Long> spIdList);
}
