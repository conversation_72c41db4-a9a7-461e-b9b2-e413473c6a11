package com.pukka.iptv.statistics.service.statistics;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.pukka.iptv.statistics.model.FTPDiskModel;
import com.pukka.iptv.statistics.model.StatisticsDiskSpace;
import com.pukka.iptv.statistics.vo.StatisticsDiskSpaceCpRespVo;
import com.pukka.iptv.statistics.vo.StatisticsDiskSpaceReqVo;
import com.pukka.iptv.statistics.vo.StatisticsDiskSpaceRespVo;
import com.pukka.iptv.statistics.vo.StatisticsDiskSpaceWarnRespVo;

import java.util.List;

/**
 *
 * @author: zhengcl
 * @date: 2022年7月13日 下午5:23:51
 */

public interface StatisticsDiskSpaceService extends IService<StatisticsDiskSpace> {

    StatisticsDiskSpaceRespVo getDiskSpaceTotal(StatisticsDiskSpaceReqVo statisticsDiskSpace);

    Page<StatisticsDiskSpaceCpRespVo> getDiskSpacePage(Page page, StatisticsDiskSpaceReqVo statisticsDiskSpace);

    List<StatisticsDiskSpaceWarnRespVo> getDiskSpaceWarn();

    FTPDiskModel getDiskSpaceByCp(long cpId);

    List<StatisticsDiskSpaceCpRespVo> getDiskSpaceList(StatisticsDiskSpaceReqVo statisticsDiskSpace);

    List<StatisticsDiskSpaceCpRespVo> getDiskSpacePieChart(StatisticsDiskSpaceReqVo statisticsDiskSpace);
}


