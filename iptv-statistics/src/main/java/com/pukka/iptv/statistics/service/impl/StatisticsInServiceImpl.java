package com.pukka.iptv.statistics.service.impl;

import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.pukka.iptv.common.base.constant.RedisKeyConstants;
import com.pukka.iptv.common.base.enums.StatisticsTypeEnum;
import com.pukka.iptv.common.base.enums.StatusEnum;
import com.pukka.iptv.common.base.enums.SysUserTypeEnum;
import com.pukka.iptv.common.base.util.JwtTokenUtil;
import com.pukka.iptv.common.base.vo.CommonResponse;
import com.pukka.iptv.common.base.vo.SecurityUser;
import com.pukka.iptv.common.core.util.DateUtils;
import com.pukka.iptv.common.core.util.StringUtils;
import com.pukka.iptv.common.data.model.cms.CmsProgram;
import com.pukka.iptv.common.data.model.statistics.StatisticsIn;
import com.pukka.iptv.common.data.model.sys.SysCp;
import com.pukka.iptv.common.data.model.sys.SysSp;
import com.pukka.iptv.common.data.util.TimeUtil;
import com.pukka.iptv.common.data.vo.req.StatisticsInVo;
import com.pukka.iptv.common.data.vo.req.StatisticsInVoD;
import com.pukka.iptv.common.data.vo.req.StatisticsInVoP;
import com.pukka.iptv.statistics.mapper.StatisticsInMapper;
import com.pukka.iptv.statistics.mapper.sys.SysCpMapper;
import com.pukka.iptv.statistics.mapper.sys.SysSpMapper;
import com.pukka.iptv.statistics.service.StatisticsInInitService;
import com.pukka.iptv.statistics.service.StatisticsInService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.NumberUtils;

import javax.annotation.Resource;
import java.io.ByteArrayOutputStream;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.stream.Collectors;

/**
 *
 * @author: tan
 * @date: 2022-7-14 11:02:04
 */

@Service
public class StatisticsInServiceImpl extends ServiceImpl<StatisticsInMapper, StatisticsIn> implements StatisticsInService {

    @Autowired
    StatisticsInMapper statisticsInMapper;

    @Autowired
    StatisticsInInitService statisticsInInitService;

    @Resource
    private SysCpMapper sysCpMapper;


    @Override
    public CommonResponse<List<StatisticsIn>> getAllStatistics(StatisticsInVo statisticsInVo) {

        //初始化基础数据
        List<StatisticsIn> statisticsInTemplate = new ArrayList<>();

        StatisticsIn statisticsIn = new StatisticsIn();
        statisticsIn.setType(StatisticsTypeEnum.SimpleSet.getValue());
        statisticsIn.setCount(0);
        statisticsIn.setDuration(0L);
        statisticsInTemplate.add(statisticsIn);


        StatisticsIn statisticsInSub = new StatisticsIn();
        statisticsInSub.setType(StatisticsTypeEnum.Subset.getValue());
        statisticsInSub.setCount(0);
        statisticsInSub.setDuration(0L);
        statisticsInTemplate.add(statisticsInSub);



        StatisticsIn statisticsInSeries = new StatisticsIn();
        statisticsInSeries.setType(StatisticsTypeEnum.Series.getValue());
        statisticsInSeries.setCount(0);
        statisticsInSeries.setDuration(0L);
        statisticsInTemplate.add(statisticsInSeries);

        StatisticsIn statisticsInResource = new StatisticsIn();
        statisticsInResource.setType(StatisticsTypeEnum.Resource.getValue());
        statisticsInResource.setCount(0);
        statisticsInResource.setDuration(0L);
        statisticsInTemplate.add(statisticsInResource);


        SimpleDateFormat formater = new SimpleDateFormat("yyyy-MM-dd");
        SimpleDateFormat formater2 = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        Date start = null;
        Date end = null;
        try {
            start = formater2.parse(formater.format(formater.parse(statisticsInVo.getStartTime()))+ " 00:00:00");
            end = formater2.parse(formater.format(formater.parse(statisticsInVo.getEndTime()))+ " 23:59:59");
        } catch (ParseException parseException) {
            parseException.printStackTrace();
        }

        statisticsInVo.setStartTime(TimeUtil.formatDate(start));
        statisticsInVo.setEndTime(TimeUtil.formatDate(end));

        List<StatisticsIn> statisticsInList =  statisticsInMapper.getAllStatisticsByAuthAll(statisticsInVo,
                getPermission().get("cpIdList"));

        for(StatisticsIn e : statisticsInTemplate){

            for(StatisticsIn info : statisticsInList){
                if( info.getType().equals(e.getType())){
                    if((info.getDuration()) != null){
                        e.setDuration(info.getDuration());
                    }

                    e.setCount(info.getCount());
                }
            }
        }
        return CommonResponse.success(statisticsInTemplate);
    }

    @Override
    public HashMap<String, List<Long>> getPermission() {
        HashMap<String, List<Long>> map = new HashMap<>();
        List<Long> spIdList = new ArrayList<>();
        List<Long> cpIdList = new ArrayList<>();
        SecurityUser securityUser = JwtTokenUtil.getSecurityUser();
        if(securityUser == null){
            map.put("cpIdList", cpIdList);
            return map;
        }
        String spIds = securityUser.getSpIds();
        String cpIds = securityUser.getCpIds();
        Integer type = securityUser.getType();
        if (SysUserTypeEnum.SUPER_ADMIN.getCode().equals(type) || SysUserTypeEnum.ADMIN.getCode().equals(type)) {
//            List<SysCp> sysCps = sysCpMapper.selectList(new LambdaQueryWrapper<SysCp>().ne(SysCp::getStatus, StatusEnum.DELETE.getCode()).select(SysCp::getId));
//            cpIdList = sysCps.stream().map(SysCp::getId).collect(Collectors.toList());
            map.put("cpIdList", cpIdList);
            return map;
        }
        if (StringUtils.isNotEmpty(spIds)) {
            String[] split = spIds.split(",");
            spIdList = Arrays.stream(split).map(s -> Long.parseLong(s.trim())).collect(Collectors.toList());
        }
        if (StringUtils.isNotEmpty(cpIds)) {
            String[] split = cpIds.split(",");
            cpIdList = Arrays.stream(split).map(s -> Long.parseLong(s.trim())).collect(Collectors.toList());
        }
        map.put("spIdList", spIdList);
        map.put("cpIdList", cpIdList);
        return map;
    }

    @Override
    public CommonResponse<List<StatisticsIn>> getAllStatisticsByLastMonth(StatisticsInVoD statisticsInVo) {
        SimpleDateFormat formater = new SimpleDateFormat("yyyy-MM-dd");
        SimpleDateFormat formater2 = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        Date start = null;
        Date end = null;
        try {
            start = formater2.parse(formater.format(formater.parse(statisticsInVo.getStartTime()))+ " 00:00:00");
            end = formater2.parse(formater.format(formater.parse(statisticsInVo.getEndTime()))+ " 23:59:59");
        } catch (ParseException parseException) {
            parseException.printStackTrace();
        }

        statisticsInVo.setStartTime(TimeUtil.formatDate(start));
        statisticsInVo.setEndTime(TimeUtil.formatDate(end));
        List<StatisticsIn> statisticsInList = statisticsInMapper.getAllStatisticsByLastMonthByAuthAll(statisticsInVo,
                getPermission().get("cpIdList")
                );

        List<Date> dateList = null;
        try {
            dateList = statisticsInInitService.findDates(TimeUtil.getDate(statisticsInVo.getStartTime()), TimeUtil.getDate(statisticsInVo.getEndTime()));
        } catch (ParseException e) {
            e.printStackTrace();
        }

        List<StatisticsIn> statisticsInListMonth = new ArrayList<>();
        //初始化基础数据（一个月）
        for(Date date : dateList){
            StatisticsIn statisticsIn = new StatisticsIn();
            statisticsIn.setStatisticDate(DateUtils.parseDateToStr(date));
            statisticsIn.setType(StatisticsTypeEnum.SimpleSet.getValue());
            statisticsIn.setCount(0);
            statisticsIn.setDuration(0L);
            statisticsInListMonth.add(statisticsIn);

            StatisticsIn statisticsInSubset = new StatisticsIn();
            statisticsInSubset.setStatisticDate(DateUtils.parseDateToStr(date));
            statisticsInSubset.setType(StatisticsTypeEnum.Subset.getValue());
            statisticsInSubset.setCount(0);
            statisticsInSubset.setDuration(0L);
            statisticsInListMonth.add(statisticsInSubset);

            StatisticsIn statisticsSeries = new StatisticsIn();
            statisticsSeries.setStatisticDate(DateUtils.parseDateToStr(date));
            statisticsSeries.setType(StatisticsTypeEnum.Series.getValue());
            statisticsSeries.setCount(0);
            statisticsSeries.setDuration(0L);
            statisticsInListMonth.add(statisticsSeries);

            StatisticsIn statisticsResource = new StatisticsIn();
            statisticsResource.setStatisticDate(DateUtils.parseDateToStr(date));
            statisticsResource.setType(StatisticsTypeEnum.Resource.getValue());
            statisticsResource.setCount(0);
            statisticsResource.setDuration(0L);
            statisticsInListMonth.add(statisticsResource);
        }

        for(StatisticsIn statisticsIn : statisticsInListMonth){

            for(StatisticsIn info : statisticsInList){
                if(DateUtils.dateTime(info.getStatisticDate()).equals(DateUtils.dateTime2(statisticsIn.getStatisticDate()))
                    && info.getType().equals(statisticsIn.getType())){
                    statisticsIn.setDuration(info.getDuration());
                    statisticsIn.setCount(info.getCount());
                }
            }
        }

        return CommonResponse.success(statisticsInListMonth);
    }

    @Override
    public IPage<StatisticsIn> getProgramRankStatistics(StatisticsInVoP statisticsInVo) {
        SimpleDateFormat formater = new SimpleDateFormat("yyyy-MM-dd");
        SimpleDateFormat formater2 = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        Date start = null;
        Date end = null;
        try {
            start = formater2.parse(formater.format(formater.parse(statisticsInVo.getStartTime()))+ " 00:00:00");
            end = formater2.parse(formater.format(formater.parse(statisticsInVo.getEndTime()))+ " 23:59:59");
        } catch (ParseException parseException) {
            parseException.printStackTrace();
        }

        statisticsInVo.setStartTime(TimeUtil.formatDate(start));
        statisticsInVo.setEndTime(TimeUtil.formatDate(end));

        IPage<StatisticsIn> iPage =  statisticsInMapper.getProgramAndAllRankStatistics(statisticsInVo);
//        IPage<StatisticsIn> iPage =  statisticsInMapper.getProgramAndAllRankStatisticsByAuth(statisticsInVo,   getPermission().get("cpIdList"));
        List<StatisticsIn> statisticsInList = iPage.getRecords();

        long page = statisticsInVo.getCurrent() -1;
        long number = statisticsInVo.getSize();
        int i = 1;
        for(StatisticsIn statisticsIn : statisticsInList){
            if(statisticsIn.getDuration() == null){
                statisticsIn.setDuration(0L);
            }
            statisticsIn.setRankId((int) (page*number + i));
            i++;
        }
        return iPage;
    }

    @Override
    public CommonResponse<List<StatisticsIn>> getProgramRankStatisticsList(StatisticsInVoD statisticsInVoD) {

        SimpleDateFormat formater = new SimpleDateFormat("yyyy-MM-dd");
        SimpleDateFormat formater2 = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        Date start = null;
        Date end = null;
        try {
            start = formater2.parse(formater.format(formater.parse(statisticsInVoD.getStartTime()))+ " 00:00:00");
            end = formater2.parse(formater.format(formater.parse(statisticsInVoD.getEndTime()))+ " 23:59:59");
        } catch (ParseException parseException) {
            parseException.printStackTrace();
        }

        statisticsInVoD.setStartTime(TimeUtil.formatDate(start));
        statisticsInVoD.setEndTime(TimeUtil.formatDate(end));
//        return CommonResponse.success(statisticsInMapper.getProgramAndAllRankStatisticsListByAuth(statisticsInVoD,  getPermission().get("cpIdList")));
        return CommonResponse.success(statisticsInMapper.getProgramAndAllRankStatisticsList(statisticsInVoD));
    }
}


