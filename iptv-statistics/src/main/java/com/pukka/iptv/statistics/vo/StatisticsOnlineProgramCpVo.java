package com.pukka.iptv.statistics.vo;

import com.alibaba.excel.annotation.ExcelProperty;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 *
 * @author: zhengcl
 * @date: 2022年7月13日 上午9:55:27
 */

@Data
@EqualsAndHashCode
@JsonIgnoreProperties(value = { "handler" })
@Accessors(chain = true)
public class StatisticsOnlineProgramCpVo extends Model<StatisticsOnlineProgramCpVo> implements java.io.Serializable{

    private static final long serialVersionUID = 1L;

    @ExcelProperty(value = "排行",order = 1)
    @ApiModelProperty(value="排行",dataType="Long",name="rankId")
    private Long rankId;

    @ExcelProperty(value = "下发量",order = 3)
    @ApiModelProperty(value="媒资数量",dataType="Long",name="count")
    private Long count;

    @ExcelProperty(value = "时长（H）",order = 4)
    @ApiModelProperty(value="视频长度（s）",dataType="Double",name="duration")
    private Double duration;

    @ExcelProperty(value = "容量（G）",order = 5)
    @ApiModelProperty(value="视频大小",dataType="Double",name="fileSize")
    private Double fileSize;

    @ExcelProperty(value = "CP名称",order = 2)
    @ApiModelProperty(value="CP名称",dataType="String",name="cpName")
    private String cpName;

}
