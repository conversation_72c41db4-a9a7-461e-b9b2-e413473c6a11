package com.pukka.iptv.statistics.service.impl;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.pukka.iptv.common.data.model.sys.SysOutPassage;
import com.pukka.iptv.statistics.mapper.SysOutPassageMapper;
import com.pukka.iptv.statistics.service.SysOutPassageService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * @author: tan
 * @date: 2021-9-2 23:02:27
 * @description: OutPassage表信息
 */

@Service
public class SysOutPassageServiceImpl extends ServiceImpl<SysOutPassageMapper, SysOutPassage> implements SysOutPassageService {

    @Autowired
    private SysOutPassageMapper sysOutPassageMapper;


    /**
     * 通过lspId查询
     *
     * @param lspId
     * @return
     */
    @Override
    public SysOutPassage getByLspId(String lspId) {
        return sysOutPassageMapper.selectOne(Wrappers.<SysOutPassage>query().eq("lsp_id", lspId));
    }

}


