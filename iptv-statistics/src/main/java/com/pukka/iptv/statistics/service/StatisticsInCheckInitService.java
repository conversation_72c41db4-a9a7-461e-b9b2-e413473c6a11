package com.pukka.iptv.statistics.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.pukka.iptv.common.base.vo.CommonResponse;
import com.pukka.iptv.common.data.model.statistics.StatisticsInCheckInit;
import com.pukka.iptv.common.data.model.statistics.StatisticsInInit;
import com.pukka.iptv.common.data.vo.req.StatisticsInInitVo;

import java.util.List;

/**
 *
 * @author: tan
 * @date: 2022-8-11 16:46:05
 */

public interface StatisticsInCheckInitService extends IService<StatisticsInCheckInit> {

    CommonResponse<List<StatisticsInCheckInit>> getAllStatistics(StatisticsInInitVo statisticsInInitVo);
}


