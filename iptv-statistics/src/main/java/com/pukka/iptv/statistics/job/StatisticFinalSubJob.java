package com.pukka.iptv.statistics.job;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.pukka.iptv.common.api.feign.cms.CmsProgramFeignClient;
import com.pukka.iptv.common.base.enums.StatisticsTypeEnum;
import com.pukka.iptv.common.base.vo.CommonResponse;
import com.pukka.iptv.common.data.model.statistics.StatisticsInFinal;
import com.pukka.iptv.common.data.vo.req.StatisticsInVo;
import com.pukka.iptv.statistics.service.StatisticsInFinalService;
import com.pukka.iptv.statistics.util.DateUtil;
import feign.Request;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.concurrent.TimeUnit;

/**
 * @ClassName StatisticFinalProgramJob
 * @Description TODO
 * <AUTHOR>
 * @Date 2022/7/14 11:49
 * @Version
 */
@Component
@Slf4j
public class StatisticFinalSubJob implements IStatisticJob {
    @Autowired
    CmsProgramFeignClient cmsProgramFeignClient;

    @Autowired
    StatisticsInFinalService statisticsInFinalService;

    @Override
    public void executeJob() {
        StatisticsInVo statisticsInVo = new StatisticsInVo();
//        statisticsInVo.setStartTime("2022-04-11 00:00:00");
//        statisticsInVo.setEndTime("2022-04-15 23:59:59");

        Map map = DateUtil.getYesterdayTime();
        statisticsInVo.setStartTime((String) map.get("startDate"));
        statisticsInVo.setEndTime((String) map.get("endDate"));

        Request.Options options = new Request.Options(5, TimeUnit.SECONDS, 160, TimeUnit.SECONDS, true);
        CommonResponse<List<StatisticsInFinal>> commonResponse =  cmsProgramFeignClient.finalSubStatistic(statisticsInVo, options);
        List<StatisticsInFinal> statisticsInFinalList = (List<StatisticsInFinal>) commonResponse.getData();
        List<StatisticsInFinal> statisticsInsListAdd = new ArrayList<>();
        LambdaQueryWrapper<StatisticsInFinal> queryWrapper = Wrappers.lambdaQuery();
        statisticsInFinalList.forEach(e ->
        {
            queryWrapper.clear();
            queryWrapper.eq(StatisticsInFinal::getStatisticDate, statisticsInVo.getStartTime())
                    .eq(StatisticsInFinal::getCpId, e.getCpId())
                    .eq(StatisticsInFinal::getType, StatisticsTypeEnum.Subset.getValue())
                    .eq(StatisticsInFinal::getPassType, e.getPassType());

            if(Objects.isNull(e.getContentType())){
                queryWrapper.isNull(Objects.isNull(e.getContentType()),StatisticsInFinal::getContentType);
            }else{
                queryWrapper.eq(StatisticsInFinal::getContentType, e.getContentType());
            }
            List<StatisticsInFinal> statisticsInFinals = statisticsInFinalService.list(queryWrapper);
            if(statisticsInFinals.size() == 0){
                statisticsInsListAdd.add(e);
            }
        });
        if(statisticsInsListAdd.size() >0 ){
            Boolean isSave = statisticsInFinalService.saveBatch(statisticsInsListAdd);
            if(isSave){
                log.info("StatisticFinalSubJob ->>>>> time = {},size = {}", statisticsInVo.getStartTime(),statisticsInsListAdd.size());
            }else{
                log.info("StatisticFinalSubJob is fail ->>>>> time = {}",statisticsInVo.getStartTime() );
            }
        }
        log.info("StatisticFinalSubJob ->>>>> time = {},size = {}", statisticsInVo.getStartTime(), statisticsInsListAdd.size());

    }


    public void executeJob(StatisticsInVo statisticsInVo) {

        Request.Options options = new Request.Options(5, TimeUnit.SECONDS, 160, TimeUnit.SECONDS, true);
        CommonResponse<List<StatisticsInFinal>> commonResponse =  cmsProgramFeignClient.finalSubStatistic(statisticsInVo, options);
        List<StatisticsInFinal> statisticsInFinalList = (List<StatisticsInFinal>) commonResponse.getData();
        List<StatisticsInFinal> statisticsInsListAdd = new ArrayList<>();
        LambdaQueryWrapper<StatisticsInFinal> queryWrapper = Wrappers.lambdaQuery();
        statisticsInFinalList.forEach(e ->
        {
            queryWrapper.clear();
            queryWrapper.eq(StatisticsInFinal::getStatisticDate, statisticsInVo.getStartTime())
                    .eq(StatisticsInFinal::getCpId, e.getCpId())
                    .eq(StatisticsInFinal::getType, StatisticsTypeEnum.Subset.getValue())
                    .eq(StatisticsInFinal::getPassType, e.getPassType());

            if(Objects.isNull(e.getContentType())){
                queryWrapper.isNull(Objects.isNull(e.getContentType()),StatisticsInFinal::getContentType);
            }else{
                queryWrapper.eq(StatisticsInFinal::getContentType, e.getContentType());
            }
            List<StatisticsInFinal> statisticsInFinals = statisticsInFinalService.list(queryWrapper);
            if(statisticsInFinals.size() == 0){
                statisticsInsListAdd.add(e);
            }
        });
        if(statisticsInsListAdd.size() >0 ){
            Boolean isSave = statisticsInFinalService.saveBatch(statisticsInsListAdd);
            if(isSave){
                log.info("StatisticFinalSubJob ->>>>> time = {},size = {}", statisticsInVo.getStartTime(),statisticsInsListAdd.size());
            }else{
                log.info("StatisticFinalSubJob is fail ->>>>> time = {}",statisticsInVo.getStartTime() );
            }
        }
        log.info("StatisticFinalSubJob ->>>>> time = {},size = {}", statisticsInVo.getStartTime(), statisticsInsListAdd.size());

    }
}
