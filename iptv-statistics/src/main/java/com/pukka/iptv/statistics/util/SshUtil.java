package com.pukka.iptv.statistics.util;

import java.io.BufferedReader;
import java.io.IOException;
import java.io.InputStream;
import java.io.InputStreamReader;
import java.util.*;

import com.jcraft.jsch.Channel;
import com.jcraft.jsch.ChannelExec;
import com.jcraft.jsch.JSch;
import com.jcraft.jsch.JSchException;
import com.jcraft.jsch.Session;
import com.pukka.iptv.common.core.util.StringUtils;
import com.pukka.iptv.statistics.model.DiskSpaceSshModel;

/**
 * 远程调用Linux shell 命令
 *
 * <AUTHOR> by 14-9-2.
 */
public class SshUtil {


    public static final String CPU_MEM_SHELL = "top -b -n 1";
    public static final String FILES_SHELL = "df";
    public static final String[] COMMANDS = {FILES_SHELL};
    public static final String LINE_SEPARATOR = System.getProperty("line.separator");
    private static Session session;

    /**
     * 连接到指定的HOST
     *
     * @return isConnect
     * @throws JSchException JSchException
     */
    private static boolean connect(String user, String passwd, String host) {
        JSch jsch = new JSch();
        try {
            session = jsch.getSession(user, host, 22);
            session.setPassword(passwd);

            java.util.Properties config = new java.util.Properties();
            config.put("StrictHostKeyChecking", "no");
            session.setConfig(config);

            session.connect();
        } catch (JSchException e) {
            e.printStackTrace();
            System.out.println("connect error !");
            return false;
        }
        return true;
    }

    /**
     * 远程连接Linux 服务器 执行相关的命令
     *
     * @param commands 执行的脚本
     * @param user     远程连接的用户名
     * @param passwd   远程连接的密码
     * @param host     远程连接的主机IP
     * @return 最终命令返回信息
     */
    public static Map<String, String> runDistanceShell(String[] commands, String user, String passwd, String host) {
        if (!connect(user, passwd, host)) {
            return null;
        }
        Map<String, String> map = new HashMap<>();
        StringBuilder stringBuffer;

        BufferedReader reader = null;
        Channel channel = null;
        try {
            for (String command : commands) {
                stringBuffer = new StringBuilder();
                channel = session.openChannel("exec");
                ((ChannelExec) channel).setCommand(command);

                channel.setInputStream(null);
                ((ChannelExec) channel).setErrStream(System.err);

                channel.connect();
                InputStream in = channel.getInputStream();
                reader = new BufferedReader(new InputStreamReader(in));
                String buf;
                while ((buf = reader.readLine()) != null) {

                    //舍弃PID 进程信息
                    if (buf.contains("PID")) {
                        break;
                    }
                    stringBuffer.append(buf.trim()).append(LINE_SEPARATOR);
                }
                //每个命令存储自己返回数据-用于后续对返回数据进行处理
                map.put(command, stringBuffer.toString());
            }
        } catch (IOException | JSchException e) {
            e.printStackTrace();
        } finally {
            try {
                if (reader != null) {
                    reader.close();
                }
            } catch (IOException e) {
                e.printStackTrace();
            }
            if (channel != null) {
                channel.disconnect();
            }
            session.disconnect();
        }
        return map;
    }


    /**
     * 直接在本地执行 shell
     *
     * @param commands 执行的脚本
     * @return 执行结果信息
     */
    public static Map<String, String> runLocalShell(String[] commands) {
        Runtime runtime = Runtime.getRuntime();

        Map<String, String> map = new HashMap<>();
        StringBuilder stringBuffer;

        BufferedReader reader;
        Process process;
        for (String command : commands) {
            stringBuffer = new StringBuilder();
            try {
                process = runtime.exec(command);
                InputStream inputStream = process.getInputStream();
                reader = new BufferedReader(new InputStreamReader(inputStream));
                String buf;
                while ((buf = reader.readLine()) != null) {
                    //舍弃PID 进程信息
                    if (buf.contains("PID")) {
                        break;
                    }
                    stringBuffer.append(buf.trim()).append(LINE_SEPARATOR);
                }

            } catch (IOException e) {
                e.printStackTrace();
                return null;
            }
            //每个命令存储自己返回数据-用于后续对返回数据进行处理
            map.put(command, stringBuffer.toString());
        }
        return map;
    }

    /**
     * 处理 shell 返回的信息
     * <p>
     * 具体处理过程以服务器返回数据格式为准
     * 不同的Linux 版本返回信息格式不同
     *
     * @param result shell 返回的信息
     * @return 最终处理后的信息
     */
    public static List<DiskSpaceSshModel> disposeResultMessage(Map<String, String> result) {
        List<DiskSpaceSshModel> diskSpaceSshModels = new ArrayList<>();
        for (String command : COMMANDS) {
            String commandResult = result.get(command);
            if (null == commandResult) continue;
            if (command.equals(FILES_SHELL)) {
                //处理系统磁盘状态
                try {
                    diskSpaceSshModels = disposeFilesSystem(commandResult);
                } catch (Exception e) {
                    e.printStackTrace();
                }
            }
        }
        return diskSpaceSshModels;
    }

    /**
     * 处理 shell 返回的信息
     * <p>
     * 具体处理过程以服务器返回数据格式为准
     * 不同的Linux 版本返回信息格式不同
     *
     * @param result shell 返回的信息
     * @return 最终处理后的信息
     */
    public static List<DiskSpaceSshModel> disposeResultMessageCp(Map<String, String> result) {
        List<DiskSpaceSshModel> diskSpaceSshModels = new ArrayList<>();
        //处理系统磁盘状态
        try {
            diskSpaceSshModels = disposeFilesSystemCp(result);
        } catch (Exception e) {
            e.printStackTrace();
        }
        return diskSpaceSshModels;
    }

    //处理系统磁盘状态

    /**
     * Filesystem            Size  Used Avail Use% Mounted on
     * /dev/sda3             442G  327G   93G  78% /
     * tmpfs                  32G     0   32G   0% /dev/shm
     * /dev/sda1             788M   60M  689M   8% /boot
     * /dev/md0              1.9T  483G  1.4T  26% /ezsonar
     *
     * @param commandResult 处理系统磁盘状态shell执行结果
     * @return 处理后的结果
     */
    public static List<DiskSpaceSshModel> disposeFilesSystem(String commandResult) {
        String[] strings = commandResult.split(LINE_SEPARATOR);
        List<DiskSpaceSshModel> diskSpaceSshModels = new ArrayList<>();
        for (int i = 0; i < strings.length - 1; i++) {
            if (i == 0) continue;
            String[] split = strings[i].split("\\s+");
            ArrayList<String> stringList = new ArrayList<>();
            for (String s : split) {
                if (StringUtils.isNotEmpty(s)) {
                    stringList.add(s);
                }
            }
            DiskSpaceSshModel diskSpaceSshModel = new DiskSpaceSshModel();
            diskSpaceSshModel.setFileSystem(stringList.get(0));
            diskSpaceSshModel.setTotalSize(Long.parseLong(stringList.get(1)));
            diskSpaceSshModel.setUsedSize(Long.parseLong(stringList.get(2)));
            diskSpaceSshModel.setFreeSize(Long.parseLong(stringList.get(3)));
            diskSpaceSshModels.add(diskSpaceSshModel);
        }
        return diskSpaceSshModels;
    }

    /**
     * 空间大小    目录
     * 4       /data/ftpdata/vstorage/m3u8/FFZYTestCP/
     *
     * @param commandResultMap 处理系统磁盘状态shell执行结果
     * @return 处理后的结果
     */
    public static List<DiskSpaceSshModel> disposeFilesSystemCp(Map<String, String> commandResultMap) {
        Set<String> keySet = commandResultMap.keySet();
        List<DiskSpaceSshModel> diskSpaceSshModels = new ArrayList<>();
        for (String command : keySet) {
            String result = commandResultMap.get(command);
            if (StringUtils.isNotEmpty(result)){
                String[] split = result.split("\t");
                DiskSpaceSshModel diskSpaceSshModel = new DiskSpaceSshModel();
                diskSpaceSshModel.setUsedSize(Long.parseLong(split[0]));
                diskSpaceSshModels.add(diskSpaceSshModel);
            }
        }
        return diskSpaceSshModels;
    }

    /**
     * 处理单位转换
     * K/KB/M/T 最终转换为G 处理
     *
     * @param s 带单位的数据字符串
     * @return 以G 为单位处理后的数值
     */
    private static long disposeUnit(String s) {

        try {
            s = s.toUpperCase();
            String lastIndex = s.substring(s.length() - 1);
            String num = "";
            if ("T".equals(lastIndex) || "G".equals(lastIndex) || "M".equals(lastIndex) || "K".equals(lastIndex) || "KB".equals(lastIndex)) {
                num = s.substring(0, s.length() - 1);
            } else {
                num = s;
            }
            long parseLong = Long.parseLong(num);
            if (lastIndex.equals("G")) {
                return parseLong * 1024 * 1024 * 1024;
            } else if (lastIndex.equals("T")) {
                return parseLong * 1024 * 1024 * 1024 * 1024;
            } else if (lastIndex.equals("M")) {
                return parseLong * 1024 * 1024;
            } else if (lastIndex.equals("K") || lastIndex.equals("KB")) {
                return parseLong * 1024;
            } else {
                return parseLong;
            }
        } catch (NumberFormatException e) {
            e.printStackTrace();
            return 0;
        }
    }

    public static void main(String[] args) {
        Map<String, String> result = runDistanceShell(COMMANDS, "root", "Newbokong@2022", "172.25.235.147");
        List<DiskSpaceSshModel> diskSpaceSshModels = SshUtil.disposeResultMessage(result);
    }

}