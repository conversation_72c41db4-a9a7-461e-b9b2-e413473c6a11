package com.pukka.iptv.statistics.model;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.EqualsAndHashCode;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 *
 * @author: zhengcl
 * @date: 2022年7月13日 下午5:23:51
 */
@Data
@EqualsAndHashCode
@JsonIgnoreProperties(value = { "handler" })
@TableName(value = "statistics_disk_space",autoResultMap=true)
@Accessors(chain = true)
@ApiModel("存储统计数据库模型")
public class StatisticsDiskSpace extends Model<StatisticsDiskSpace> implements java.io.Serializable{

    private static final long serialVersionUID = 1L;

	/**主键*/
	@TableField(value = "id")
    @ApiModelProperty(value="主键",dataType="Long",name="id")
    private Long id;

	/**总空间大小*/
	@TableField(value = "total_space")
    @ApiModelProperty(value="总空间大小",dataType="Long",name="totalSpace")
    private Long totalSpace;

	/**已使用磁盘空间大小*/
	@TableField(value = "used_space")
    @ApiModelProperty(value="已使用磁盘空间大小",dataType="Long",name="usedSpace")
    private Long usedSpace;

	/**已使用比例*/
	@TableField(value = "used_proportion")
    @ApiModelProperty(value="已使用比例",dataType="java.lang.Double",name="usedProportion")
    private java.lang.Double usedProportion;

	/**存储盘id*/
	@TableField(value = "storage_id")
    @ApiModelProperty(value="存储盘id",dataType="Long",name="storageId")
    private Long storageId;

    /**存储盘名称*/
    @TableField(value = "storage_name")
    @ApiModelProperty(value="存储盘名称",dataType="String",name="storageName")
    private String storageName;

	/**cpId*/
	@TableField(value = "cp_id")
    @ApiModelProperty(value="cpId",dataType="Long",name="cpId")
    private Long cpId;

	/**CP名称*/
	@TableField(value = "cp_name")
    @ApiModelProperty(value="CP名称",dataType="String",name="cpName")
    private String cpName;

    /**统计时间*/
    @TableField(value = "statistic_date")
    @ApiModelProperty(value="统计时间",dataType="String",name="statisticDate")
    private String statisticDate;

	/**创建时间*/
	@TableField(value = "create_time")
    @ApiModelProperty(value="创建时间",dataType="String",name="createTime")
    private String createTime;

	/**更新时间*/
	@TableField(value = "update_time")
    @ApiModelProperty(value="更新时间",dataType="String",name="updateTime")
    private String updateTime;

}
