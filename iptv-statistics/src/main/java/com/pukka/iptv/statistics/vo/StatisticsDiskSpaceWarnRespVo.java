package com.pukka.iptv.statistics.vo;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.util.List;

/**
 * @author: zhengcl
 * @date: 2022年7月13日 下午5:23:51
 */
@Data
@EqualsAndHashCode
@JsonIgnoreProperties(value = {"handler"})
@Accessors(chain = true)
public class StatisticsDiskSpaceWarnRespVo implements java.io.Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "预警类型1=预警，2=停止注入", dataType = "Integer", name = "type")
    private Integer type;

    @ApiModelProperty(value = "预警的co名称列表", dataType = "List<String>", name = "cpNames")
    private List<String> cpNames;

}
