package com.pukka.iptv.statistics.service;

import com.baomidou.mybatisplus.extension.service.IService;

import com.pukka.iptv.common.base.vo.CommonResponse;
import com.pukka.iptv.common.data.model.statistics.StatisticsInCheckInit;
import com.pukka.iptv.common.data.model.statistics.StatisticsInFinalInit;
import com.pukka.iptv.common.data.vo.req.StatisticsInInitVo;

import java.util.List;

/**
 *
 * @author: tan
 * @date: 2022-8-11 17:05:24
 */

public interface StatisticsInFinalInitService extends IService<StatisticsInFinalInit> {

    CommonResponse<List<StatisticsInFinalInit>> getAllStatistics(StatisticsInInitVo statisticsInInitVo);
}


