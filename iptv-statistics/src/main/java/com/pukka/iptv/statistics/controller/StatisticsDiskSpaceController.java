package com.pukka.iptv.statistics.controller;

import com.pukka.iptv.common.base.vo.CommonResponse;

import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;

import com.pukka.iptv.statistics.easyexcel.EasyExcelUtil;
import com.pukka.iptv.statistics.service.statistics.StatisticsDiskSpaceService;
import com.pukka.iptv.statistics.vo.StatisticsDiskSpaceCpRespVo;
import com.pukka.iptv.statistics.vo.StatisticsDiskSpaceReqVo;
import com.pukka.iptv.statistics.vo.StatisticsDiskSpaceRespVo;
import com.pukka.iptv.statistics.vo.StatisticsDiskSpaceWarnRespVo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.*;
import org.springframework.beans.factory.annotation.Autowired;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.AllArgsConstructor;

import java.util.List;

/**
 * @author: zhengcl
 * @date: 2022年7月13日 下午5:23:51
 */
@RestController
@AllArgsConstructor
@RequestMapping(value = "/statisticsDiskSpace", produces = MediaType.APPLICATION_JSON_VALUE)
@Api(tags = "存储统计管理")
public class StatisticsDiskSpaceController {

    @Autowired
    private final StatisticsDiskSpaceService statisticsDiskSpaceService;

    @ApiOperation(value = "存储使用情况概览")
    @GetMapping("/getDiskSpaceTotal")
    public CommonResponse<StatisticsDiskSpaceRespVo> getDiskSpaceTotal(@Valid StatisticsDiskSpaceReqVo statisticsDiskSpace) {
        return CommonResponse.success(statisticsDiskSpaceService.getDiskSpaceTotal(statisticsDiskSpace));
    }

    @ApiOperation(value = "存储使用情况饼图")
    @GetMapping("/getDiskSpacePieChart")
    public CommonResponse<List<StatisticsDiskSpaceCpRespVo>> getDiskSpacePieChart(@Valid StatisticsDiskSpaceReqVo statisticsDiskSpace) {
        return CommonResponse.success(statisticsDiskSpaceService.getDiskSpacePieChart(statisticsDiskSpace));
    }
    
    @ApiOperation(value = "存储使用情况分页查询")
    @GetMapping("/getDiskSpacePage")
    public CommonResponse<Page<StatisticsDiskSpaceCpRespVo>> getDiskSpacePage(@Valid Page page, StatisticsDiskSpaceReqVo statisticsDiskSpace) {
        return CommonResponse.success(statisticsDiskSpaceService.getDiskSpacePage(page, statisticsDiskSpace));
    }

    @GetMapping("/exportExcelDiskSpace")
    @ApiOperation(value = "存储使用情况列表导出Excel")
    public void exportExcelDiskSpace(@Valid StatisticsDiskSpaceReqVo statisticsDiskSpace, HttpServletResponse response) {
        String fileName = "各CP存储使用情况";
        String sheetName = "各CP存储使用情况";
        List<StatisticsDiskSpaceCpRespVo> records = statisticsDiskSpaceService.getDiskSpaceList(statisticsDiskSpace);
        EasyExcelUtil.exportExcel(response, fileName, sheetName, StatisticsDiskSpaceCpRespVo.class, records);
    }

    @ApiOperation(value = "首页存储容量预警")
    @GetMapping("/getDiskSpaceWarn")
    public CommonResponse<List<StatisticsDiskSpaceWarnRespVo>> getDiskSpaceWarn() {
        return CommonResponse.success(statisticsDiskSpaceService.getDiskSpaceWarn());
    }

}
