package com.pukka.iptv.statistics.vo;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * @author: zhengcl
 * @date: 2022年7月13日 上午9:55:27
 */

@Data
@EqualsAndHashCode
@JsonIgnoreProperties(value = {"handler"})
@Accessors(chain = true)
public class StatisticsPublishRespVo implements java.io.Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value="统计类型 1=发布 2=回收",dataType="Integer",name="type")
    private Integer type;

    @ApiModelProperty(value="工单类型 1=内容、2=产品包、3=栏目关系",dataType="Integer",name="workOrderType")
    private Integer workOrderType;

    @ApiModelProperty(value = "剧头媒资数量", dataType = "Long", name = "countSeries")
    private Long countSeries = 0L;

    @ApiModelProperty(value = "剧头媒资成功数量", dataType = "Long", name = "countSeries")
    private Long countSuccessSeries = 0L;

    @ApiModelProperty(value = "剧头媒资失败数量", dataType = "Long", name = "countSeries")
    private Long countFailSeries = 0L;

    @ApiModelProperty(value = "单集媒资数量", dataType = "Long", name = "countProgram")
    private Long countProgram = 0L;

    @ApiModelProperty(value = "单集媒资成功数量", dataType = "Long", name = "countProgram")
    private Long countSuccessProgram = 0L;

    @ApiModelProperty(value = "单集媒资失败数量", dataType = "Long", name = "countProgram")
    private Long countFailProgram = 0L;

    @ApiModelProperty(value = "子集媒资数量", dataType = "Long", name = "countSubset")
    private Long countSubset = 0L;

    @ApiModelProperty(value = "子集媒资成功数量", dataType = "Long", name = "countSubset")
    private Long countSuccessSubset = 0L;

    @ApiModelProperty(value = "子集媒资失败数量", dataType = "Long", name = "countSubset")
    private Long countFailSubset = 0L;

    @ApiModelProperty(value = "cpId", dataType = "Long", name = "cpId")
    private Long cpId;

    @ApiModelProperty(value = "CP名称", dataType = "String", name = "cpName")
    private String cpName;

    @ApiModelProperty(value = "spId", dataType = "Long", name = "spId")
    private Long spId;

    @ApiModelProperty(value = "SP名称", dataType = "String", name = "spName")
    private String spName;

    @ApiModelProperty(value = "所属渠道id", dataType = "Long", name = "bmsSpChannelId")
    private Long bmsSpChannelId;

    @ApiModelProperty(value = "所属渠道", dataType = "String", name = "bmsSpChannelName")
    private String bmsSpChannelName;

}
