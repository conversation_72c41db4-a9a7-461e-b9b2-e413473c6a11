package com.pukka.iptv.statistics.model;

import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.fastjson.annotation.JSONField;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.pukka.iptv.statistics.easyexcel.BindStatusConverter;
import com.pukka.iptv.statistics.easyexcel.PublishStatusConverter;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springframework.data.elasticsearch.annotations.Document;

import java.util.Date;

/**
 * @author: luo
 */
@Data
@EqualsAndHashCode
@ApiModel("BmsContentESModel")
@Document(indexName = "bms_content")
@JsonIgnoreProperties(value = {"handler"})
public class BmsContentESModel implements java.io.Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "主键", dataType = "Long", name = "id")
    @ExcelIgnore
    private Long id;

    @ApiModelProperty(value = "全局唯一标识", dataType = "String", name = "code")
    @ExcelIgnore
    private String code;

    @ApiModelProperty(value = "节目名称", dataType = "String", name = "name")
    @ExcelProperty(value = "媒资名称", order = 1)
    private String name;

    @ApiModelProperty(value = "媒资分类ID，来自pgm_category表", dataType = "Long", name = "pgmCategoryId")
    @ExcelIgnore
    private Long pgmCategoryId;

    @ApiModelProperty(value = "媒资分类，节目形态，如：新闻，电影", dataType = "String", name = "pgmCategory")
    @ExcelProperty(value = "媒资分类", order = 2)
    private String pgmCategory;

    @ApiModelProperty(value = "媒资类型ID，来自pgm_snd_class表", dataType = "Long", name = "pgmSndClassId")
    @ExcelIgnore
    private String pgmSndClassId;

    @ApiModelProperty(value = "二级标签，如：动作，科幻", dataType = "String", name = "pgmSndClass")
    @ExcelProperty(value = "二级分类", order = 3)
    private String pgmSndClass;

    @ApiModelProperty(value = "状态标志 1:生效 0:失效", dataType = "Integer", name = "status")
    @ExcelIgnore
    private Integer status;

    @ApiModelProperty(value = "内容提供商标识", dataType = "String", name = "contentProvider")
    @ExcelProperty(value = "内容提供商", order = 6)
    private String contentProvider;

    @ApiModelProperty(value = "cpId", dataType = "Long", name = "cpId")
    @ExcelIgnore
    private Long cpId;

    @ApiModelProperty(value = "CP名称", dataType = "String", name = "cpName")
    @ExcelProperty(value = "所属CP", order = 4)
    private String cpName;

    @ApiModelProperty(value = "创建时间", dataType = "String", name = "createTime")
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @JSONField(format = "yyyy-MM-dd HH:mm:ss")
    @ExcelProperty(value = "创建时间", order = 7)
    private Date createTime;

    @ApiModelProperty(value = "更新时间", dataType = "String", name = "updateTime")
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @JSONField(format = "yyyy-MM-dd HH:mm:ss")
    @ExcelIgnore
    private Date updateTime;

    @ApiModelProperty(value = "关系发布时间（取最早的一次）", dataType = "String", name = "relationPublishTime")
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @JSONField(format = "yyyy-MM-dd HH:mm:ss")
    @ExcelProperty(value = "最早关系发布时间", order = 10)
    private Date relationPublishTime;

    @ApiModelProperty(value = "spId", dataType = "Long", name = "spId")
    @ExcelIgnore
    private Long spId;

    @ApiModelProperty(value = "SP名称", dataType = "String", name = "spName")
    @ExcelProperty(value = "所属SP", order = 5)
    private String spName;

    @ApiModelProperty(value = "1：电影 3：电视剧 4：系列片 5：片花 此处没有子集，子集由program表单独保存", dataType = "Integer", name = "contentType")
    @ExcelIgnore
    private Integer contentType;

    @ApiModelProperty(value = "发布通道ID，多个ID以英文逗号隔开", dataType = "String", name = "outPassageIds")
    @ExcelIgnore
    private String outPassageIds;

    @ApiModelProperty(value = "分发通道名称以英文逗号 隔开", dataType = "String", name = "outPassageNames")
    @ExcelIgnore
    private String outPassageNames;

    @ApiModelProperty(value = "发布状态  1：待发布 2：发布中 3：发布成功 4：发布失败 5：待更新 6：更新中 7：更新失败 8：回收中 9：回收成功 10：回收失败", dataType = "Integer", name = "publishStatus")
    @ExcelIgnore
    private Integer publishStatus;

    @ApiModelProperty(value = "发布时间", dataType = "Date", name = "publishTime")
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @JSONField(format = "yyyy-MM-dd HH:mm:ss")
    @ExcelProperty(value = "媒资发布时间", order = 9)
    private Date publishTime;

    @ApiModelProperty(value = "publishDescription", dataType = "String", name = "publishDescription")
    @ExcelIgnore
    private String publishDescription;

    @ApiModelProperty(value = "定时发布，时间存在则代表已经设定为定时发布", dataType = "String", name = "timedPublish")
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @JSONField(format = "yyyy-MM-dd HH:mm:ss")
    @ExcelIgnore
    private Date timedPublish;

    @ApiModelProperty(value = "定时发布状态", dataType = "String", name = "timedPublishStatus")
    @ExcelIgnore
    private Integer timedPublishStatus;

    @ApiModelProperty(value = "定时发布描述", dataType = "String", name = "timedPublishDescription")
    @ExcelIgnore
    private String timedPublishDescription;

    @ApiModelProperty(value = "CP内容编码", dataType = "String", name = "cmsContentCode")
    @ExcelProperty(value = "CP侧全局唯一标识", order = 8)
    private String cmsContentCode;

    @ApiModelProperty(value = "内容ID", dataType = "Long", name = "cmsContentId")
    @ExcelIgnore
    private Long cmsContentId;

    @ApiModelProperty(value = "所属渠道", dataType = "String", name = "bmsSpChannelName")
    @ExcelIgnore
    private String bmsSpChannelName;

    @ApiModelProperty(value = "所属渠道id", dataType = "Long", name = "bmsSpChannelId")
    @ExcelIgnore
    private Long bmsSpChannelId;

    @ApiModelProperty(value = "产品包ID集合，以英文逗号分割", dataType = "String", name = "packageIds")
    @ExcelIgnore
    private String packageIds;

    @ApiModelProperty(value = "产品包name集合，以英文逗号分割", dataType = "String", name = "packageNames")
    @ExcelIgnore
    private String packageNames;

    @ApiModelProperty(value = "栏目ID集合，以英文逗号分割", dataType = "String", name = "categoryIds")
    @ExcelIgnore
    private String categoryIds;

    @ApiModelProperty(value = "栏目Name集合，以英文逗号分割", dataType = "String", name = "categoryNames")
    @ExcelIgnore
    private String categoryNames;

    @ApiModelProperty(value = "正片关联状态 1：待关联 2：已关联", dataType = "Integer", name = "releaseStatus")
    @ExcelIgnore
    private Integer releaseStatus;

    @ApiModelProperty(value = "预览片关联状态 1：待关联 2：已关联", dataType = "Integer", name = "previewStatus")
    @ExcelIgnore
    private Integer previewStatus;

    @ApiModelProperty(value = "节目清晰度标识 0：标清 1：高清  2：超清 3: 4K  4: 杜比（4K+杜比）", dataType = "Integer", name = "definitionFlag")
    @ExcelIgnore
    private Integer definitionFlag;

    @ApiModelProperty(value = "节目时长（秒）", dataType = "Long", name = "duration")
    @ExcelIgnore
    private Long duration;

    @ApiModelProperty(value = "首次下发时间", dataType = "String", name = "firstPublishTime")
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @JSONField(format = "yyyy-MM-dd HH:mm:ss")
    @ExcelIgnore
    private String firstPublishTime;

    //新增字段
    @ApiModelProperty(value = "节目绑定栏目情况（2=已绑定，3=未绑定）", dataType = "Integer", name = "bindStatus")
    @ExcelProperty(value = "节目绑定栏目情况", order = 11, converter = BindStatusConverter.class)
    private Integer bindStatus;

    @ApiModelProperty(value = "通过cms_resource表获取的节目时长（分钟）", dataType = "Integer", name = "cmsResourceDuration")
    @ExcelIgnore
    private Integer cmsResourceDuration;

    @ApiModelProperty(value = "对应视频介质文件大小，单位B", dataType = "Long", name = "fileSize")
    @ExcelIgnore
    private Long fileSize;

    @ApiModelProperty(value = "记录状态（1=最新记录，2=历史记录）", dataType = "Integer", name = "statusFlag")
    @ExcelIgnore
    private Integer statusFlag;

    @ApiModelProperty(value = "cmsContentCode、发布状态及spId通过_组合，仅用于发布回收统计的去重", dataType = "String", name = "cmsContentCodePublishStatusSpId")
    @ExcelIgnore
    private String cmsContentCodePublishStatusSpId;
}
