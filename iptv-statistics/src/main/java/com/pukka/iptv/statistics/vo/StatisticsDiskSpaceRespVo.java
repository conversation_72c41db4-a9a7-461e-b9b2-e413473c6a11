package com.pukka.iptv.statistics.vo;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 *
 * @author: zhengcl
 * @date: 2022年7月13日 下午5:23:51
 */
@Data
@EqualsAndHashCode
@JsonIgnoreProperties(value = { "handler" })
@Accessors(chain = true)
public class StatisticsDiskSpaceRespVo implements java.io.Serializable{

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value="总空间大小",dataType="Double",name="totalSpace")
    private Double totalSpace;

    @ApiModelProperty(value="已使用磁盘空间大小",dataType="Double",name="usedSpace")
    private Double usedSpace;

    @ApiModelProperty(value="空闲磁盘空间大小",dataType="Double",name="freeSpace")
    private Double freeSpace;

    @ApiModelProperty(value="待分配磁盘空间大小",dataType="Double",name="toBeDistributedSpace")
    private Double toBeDistributedSpace;

    @ApiModelProperty(value="已使用比例",dataType="java.lang.Double",name="usedProportion")
    private Double usedProportion;

    @ApiModelProperty(value="存储盘id",dataType="Long",name="storageId")
    private Long storageId;

    @ApiModelProperty(value="存储盘名称",dataType="String",name="storageName")
    private String storageName;

    @ApiModelProperty(value="cpId",dataType="Long",name="cpId")
    private Long cpId;

    @ApiModelProperty(value="cp名称",dataType="String",name="cpName")
    private String cpName;

    @ApiModelProperty(value="统计时间",dataType="String",name="statisticDate")
    private String statisticDate;
}
