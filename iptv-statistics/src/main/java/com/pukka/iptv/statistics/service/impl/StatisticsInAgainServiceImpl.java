package com.pukka.iptv.statistics.service.impl;


import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.pukka.iptv.common.base.enums.StatisticsTypeEnum;
import com.pukka.iptv.common.base.vo.CommonResponse;
import com.pukka.iptv.common.core.util.DateUtils;
import com.pukka.iptv.common.data.model.statistics.StatisticsInAgain;
import com.pukka.iptv.common.data.model.statistics.StatisticsInFinal;
import com.pukka.iptv.common.data.util.TimeUtil;
import com.pukka.iptv.common.data.vo.req.StatisticsInVo;
import com.pukka.iptv.common.data.vo.req.StatisticsInVoD;
import com.pukka.iptv.statistics.mapper.StatisticsInAgainMapper;
import com.pukka.iptv.statistics.service.StatisticsInAgainService;
import com.pukka.iptv.statistics.service.StatisticsInInitService;
import com.pukka.iptv.statistics.service.StatisticsInService;
import com.pukka.iptv.statistics.util.DateUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;


/**
 *
 * @author: tan
 * @date: 2022-8-2 9:17:35
 */

@Service
public class StatisticsInAgainServiceImpl extends ServiceImpl<StatisticsInAgainMapper, StatisticsInAgain> implements StatisticsInAgainService {

    @Autowired
    private StatisticsInAgainMapper statisticsInAgainMapper;

    @Autowired
    StatisticsInService statisticsInService;

    @Override
    public CommonResponse<List<StatisticsInAgain>> getAgainAllStatistics(StatisticsInVo statisticsInVo) {
        List<StatisticsInAgain> statisticsInAgainTemplate = new ArrayList<>();

        StatisticsInAgain statisticsInAgain = new StatisticsInAgain();
        statisticsInAgain.setType(StatisticsTypeEnum.SimpleSet.getValue());
        statisticsInAgain.setCount(0);
        statisticsInAgain.setDuration(0L);
        statisticsInAgain.setPassType(0);
        statisticsInAgainTemplate.add(statisticsInAgain);

        StatisticsInAgain statisticsInAgainNoPass = new StatisticsInAgain();
        statisticsInAgainNoPass.setType(StatisticsTypeEnum.SimpleSet.getValue());
        statisticsInAgainNoPass.setCount(0);
        statisticsInAgainNoPass.setDuration(0L);
        statisticsInAgainNoPass.setPassType(1);
        statisticsInAgainTemplate.add(statisticsInAgainNoPass);

        StatisticsInAgain statisticsInAgainSub = new StatisticsInAgain();
        statisticsInAgainSub.setType(StatisticsTypeEnum.Subset.getValue());
        statisticsInAgainSub.setCount(0);
        statisticsInAgainSub.setDuration(0L);
        statisticsInAgainSub.setPassType(0);
        statisticsInAgainTemplate.add(statisticsInAgainSub);

        StatisticsInAgain statisticsInAgainSubNoPass = new StatisticsInAgain();
        statisticsInAgainSubNoPass.setType(StatisticsTypeEnum.Subset.getValue());
        statisticsInAgainSubNoPass.setCount(0);
        statisticsInAgainSubNoPass.setDuration(0L);
        statisticsInAgainSubNoPass.setPassType(1);
        statisticsInAgainTemplate.add(statisticsInAgainSubNoPass);


        StatisticsInAgain statisticsInAgainSeries = new StatisticsInAgain();
        statisticsInAgainSeries.setType(StatisticsTypeEnum.Series.getValue());
        statisticsInAgainSeries.setCount(0);
        statisticsInAgainSeries.setDuration(0L);
        statisticsInAgainSeries.setPassType(0);
        statisticsInAgainTemplate.add(statisticsInAgainSeries);

        StatisticsInAgain statisticsInAgainSeriesNoPass = new StatisticsInAgain();
        statisticsInAgainSeriesNoPass.setType(StatisticsTypeEnum.Series.getValue());
        statisticsInAgainSeriesNoPass.setCount(0);
        statisticsInAgainSeriesNoPass.setDuration(0L);
        statisticsInAgainSeriesNoPass.setPassType(1);
        statisticsInAgainTemplate.add(statisticsInAgainSeriesNoPass);

        List<StatisticsInAgain> statisticsInAgainList =  statisticsInAgainMapper.getSelfAllStatisticsByAuth(statisticsInVo,
                statisticsInService.getPermission().get("cpIdList"));

        for(StatisticsInAgain e : statisticsInAgainTemplate){

            for(StatisticsInAgain info : statisticsInAgainList){
                if(info.getPassType().equals(e.getPassType())
                        && info.getType().equals(e.getType())){
                    e.setDuration(info.getDuration());
                    e.setCount(info.getCount());
                }
            }
        }

        return CommonResponse.success(statisticsInAgainTemplate);
    }

    @Override
    public CommonResponse<List<StatisticsInAgain>> getAgainAllStatisticsLastMonth(StatisticsInVoD statisticsInVo) {
        SimpleDateFormat formater = new SimpleDateFormat("yyyy-MM-dd");
        SimpleDateFormat formater2 = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        Date start = null;
        Date end = null;
        try {
            start = formater2.parse(formater.format(formater.parse(statisticsInVo.getStartTime()))+ " 00:00:00");
            end = formater2.parse(formater.format(formater.parse(statisticsInVo.getEndTime()))+ " 23:59:59");
        } catch (ParseException parseException) {
            parseException.printStackTrace();
        }

        statisticsInVo.setStartTime(TimeUtil.formatDate(start));
        statisticsInVo.setEndTime(TimeUtil.formatDate(end));
        List<StatisticsInAgain> statisticsInAgainList = statisticsInAgainMapper.getSelfAllStatisticsLastMonthByAuth(statisticsInVo,
                statisticsInService.getPermission().get("cpIdList"));

        List<Date> dateList = null;
        try {
            dateList = DateUtil.findDates(TimeUtil.getDate(statisticsInVo.getStartTime()), TimeUtil.getDate(statisticsInVo.getEndTime()));
        } catch (ParseException e) {
            e.printStackTrace();
        }

        List<StatisticsInAgain> statisticsInAgainListMonth = new ArrayList<>();
        //初始化基础数据（最近一周）
        for(Date date : dateList){
            StatisticsInAgain statisticsInAgain = new StatisticsInAgain();
            statisticsInAgain.setStatisticDate(DateUtils.parseDateToStr(date));
            statisticsInAgain.setType(StatisticsTypeEnum.SimpleSet.getValue());
            statisticsInAgain.setCount(0);
            statisticsInAgain.setDuration(0L);
            statisticsInAgainListMonth.add(statisticsInAgain);

            StatisticsInAgain statisticsInAgainSubset = new StatisticsInAgain();
            statisticsInAgainSubset.setStatisticDate(DateUtils.parseDateToStr(date));
            statisticsInAgainSubset.setType(StatisticsTypeEnum.Subset.getValue());
            statisticsInAgainSubset.setCount(0);
            statisticsInAgainSubset.setDuration(0L);
            statisticsInAgainListMonth.add(statisticsInAgainSubset);


            StatisticsInAgain statisticsInAgainSeries = new StatisticsInAgain();
            statisticsInAgainSeries.setStatisticDate(DateUtils.parseDateToStr(date));
            statisticsInAgainSeries.setType(StatisticsTypeEnum.Series.getValue());
            statisticsInAgainSeries.setCount(0);
            statisticsInAgainSeries.setDuration(0L);
            statisticsInAgainListMonth.add(statisticsInAgainSeries);
        }

        for(StatisticsInAgain statisticsInAgain : statisticsInAgainListMonth){

            for(StatisticsInAgain info : statisticsInAgainList){
                if(DateUtils.dateTime(info.getStatisticDate()).equals(DateUtils.dateTime2(statisticsInAgain.getStatisticDate()))
                        && info.getType().equals(statisticsInAgain.getType())){
                    statisticsInAgain.setDuration(info.getDuration());
                    statisticsInAgain.setCount(info.getCount());
                }
            }
        }
        return  CommonResponse.success(statisticsInAgainListMonth);
    }
}


