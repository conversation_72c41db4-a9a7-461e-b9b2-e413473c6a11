package com.pukka.iptv.statistics.service.job.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateUtil;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.pukka.iptv.common.base.constant.RedisKeyConstants;
import com.pukka.iptv.common.base.enums.StatusEnum;
import com.pukka.iptv.common.core.util.DateUtils;
import com.pukka.iptv.common.data.model.sys.SysCp;
import com.pukka.iptv.common.data.model.sys.SysStorage;
import com.pukka.iptv.common.redis.service.RedisService;
import com.pukka.iptv.statistics.config.StoreConfig;
import com.pukka.iptv.statistics.mapper.StatisticsDiskSpaceMapper;
import com.pukka.iptv.statistics.mapper.sys.SysCpMapper;
import com.pukka.iptv.statistics.mapper.sys.SysStorageMapper;
import com.pukka.iptv.statistics.model.DiskSpaceSshModel;
import com.pukka.iptv.statistics.model.StatisticsDiskSpace;
import com.pukka.iptv.statistics.service.job.JobService;
import com.pukka.iptv.statistics.util.SshUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.*;

/**
 * @author: wangrh
 * @description: 统计相关定时任务实现类
 * @date: 2022/7/13
 **/
@Service("jobService")
@Slf4j
@RefreshScope
public class JobServiceImpl implements JobService {

    @Value("${ssh.host}")
    public String host;
    @Value("${ssh.username}")
    public String username;
    @Value("${ssh.password}")
    public String password;
    @Value("${statistics.spaceThreshold}")
    private Double maxSpaceThreshold;

    @Resource
    private StatisticsDiskSpaceMapper statisticsDiskSpaceMapper;
    @Resource
    private SysCpMapper sysCpMapper;
    @Resource
    private SysStorageMapper sysStorageMapper;
    @Resource
    private StoreConfig storeConfig;
    @Resource
    private RedisService redisService;

    @Override
    public void statisticDiskSpace() {
        Date date = new Date();
        long start = System.currentTimeMillis();
        List<SysStorage> sysStorages = sysStorageMapper.selectList(new LambdaQueryWrapper<SysStorage>().ne(SysStorage::getStatus, -1));
        if (CollUtil.isEmpty(sysStorages)) {
            return;
        }
        ArrayList<StatisticsDiskSpace> statisticsDiskSpaces = new ArrayList<>();
        String[] commands = new String[]{storeConfig.getDefaultStorageCommandPrefix()};
        Map<String, String> result = SshUtil.runDistanceShell(commands, username, password, host);
        List<DiskSpaceSshModel> diskSpaceSshModels = SshUtil.disposeResultMessage(result);
        if (CollUtil.isEmpty(diskSpaceSshModels)) {
            return;
        }
        for (SysStorage sysStorage : sysStorages) {
            String fileSystem = storeConfig.getFileSystem(sysStorage.getId());
            for (DiskSpaceSshModel diskSpaceSshModel : diskSpaceSshModels) {
                log.info("定时统计ftp磁盘总容量,diskSpaceSshModel={}", JSON.toJSON(diskSpaceSshModel));
                if (fileSystem.equals(diskSpaceSshModel.getFileSystem())) {
                    double usedProportion;
                    long usedSpace = diskSpaceSshModel.getUsedSize();
                    StatisticsDiskSpace statisticsDiskSpace = new StatisticsDiskSpace();
                    statisticsDiskSpace.setStorageId(sysStorage.getId());
                    statisticsDiskSpace.setStorageName(sysStorage.getName());
                    Long totalSpace = diskSpaceSshModel.getTotalSize();
                    statisticsDiskSpace.setTotalSpace(totalSpace);
                    statisticsDiskSpace.setUsedSpace(usedSpace);
                    if (!totalSpace.equals(sysStorage.getTotalSpace())) {
                        sysStorage.setTotalSpace(totalSpace);
                        sysStorageMapper.updateById(sysStorage);
                    }
                    if (totalSpace == 0) {
                        usedProportion = 0;
                    } else {
                        usedProportion = (double) usedSpace / totalSpace;
                    }
                    statisticsDiskSpace.setUsedProportion(usedProportion);
                    statisticsDiskSpace.setStatisticDate(DateUtil.format(date, DateUtils.YYYY_MM_DD_HH_MM_SS));
                    statisticsDiskSpaces.add(statisticsDiskSpace);
                }
            }

        }
        if (CollUtil.isEmpty(statisticsDiskSpaces)) {
            log.error("定时统计ftp磁盘总容量查询结果为空");
        }
        //删除当天数据，避免重复触发产生脏数据
        statisticsDiskSpaceMapper.delete(new LambdaQueryWrapper<StatisticsDiskSpace>()
                .isNull(StatisticsDiskSpace::getCpId)
                .between(StatisticsDiskSpace::getStatisticDate
                        , DateUtil.format(DateUtil.beginOfDay(date), DateUtils.YYYY_MM_DD_HH_MM_SS)
                        , DateUtil.format(DateUtil.endOfDay(date), DateUtils.YYYY_MM_DD_HH_MM_SS))
        );
        statisticsDiskSpaceMapper.batchInsert(statisticsDiskSpaces);
        log.error("定时统计每个存储的磁盘使用量耗时{}ms", System.currentTimeMillis() - start);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void statisticDiskSpaceByCp() {
        Date date = new Date();
        long start = System.currentTimeMillis();
        List<SysCp> sysCps = sysCpMapper.selectList(new LambdaQueryWrapper<SysCp>().ne(SysCp::getStatus,
                StatusEnum.DELETE.getCode()));
        if (CollUtil.isEmpty(sysCps)) {
            return;
        }
        ArrayList<StatisticsDiskSpace> statisticsDiskSpaces = new ArrayList<>();
        HashMap<String, Boolean> map = new HashMap<>();
        for (SysCp sysCp : sysCps) {
            String prefix = storeConfig.getDefaultCommandPrefix() + storeConfig.getPrefixById(sysCp.getStorageId()) + "vstorage/";
            String commandM3u8 = prefix + "m3u8/" + sysCp.getCode() + "/";
            String commandMovie = prefix + "Movie/" + sysCp.getCode() + "/";
            String commandMp4 = prefix + "mp4/" + sysCp.getCode() + "/";
            String commandTs = prefix + "ts/" + sysCp.getCode() + "/";
            String[] commands = new String[]{commandM3u8, commandMovie, commandMp4, commandTs};
            Map<String, String> result = SshUtil.runDistanceShell(commands, username, password, host);
            List<DiskSpaceSshModel> diskSpaceSshModels = SshUtil.disposeResultMessageCp(result);
            long usedSpace = 0;
            double usedProportion = 0;
            for (DiskSpaceSshModel diskSpaceSshModel : diskSpaceSshModels) {
                usedSpace += diskSpaceSshModel.getUsedSize();
                log.info("定时统计每个CP的磁盘使用量,diskSpaceSshModel={}", JSON.toJSON(diskSpaceSshModel));
            }
            usedSpace += sysCp.getUsedSpace();
            StatisticsDiskSpace statisticsDiskSpace = new StatisticsDiskSpace();
            statisticsDiskSpace.setCpId(sysCp.getId());
            statisticsDiskSpace.setCpName(sysCp.getName());
            statisticsDiskSpace.setStorageId(sysCp.getStorageId());
            statisticsDiskSpace.setStorageName(sysCp.getStorageName());
            long totalSpace = null != sysCp.getTotalSpace() ? sysCp.getTotalSpace() : 0;
            statisticsDiskSpace.setTotalSpace(totalSpace);
            statisticsDiskSpace.setUsedSpace(usedSpace);
            if (totalSpace != 0) {
                usedProportion = (double) usedSpace / totalSpace;
            }
            statisticsDiskSpace.setUsedProportion(usedProportion);
            statisticsDiskSpace.setStatisticDate(DateUtil.format(date, DateUtils.YYYY_MM_DD_HH_MM_SS));
            statisticsDiskSpaces.add(statisticsDiskSpace);
            map.put(String.valueOf(sysCp.getId()), usedProportion < maxSpaceThreshold);
        }
        if (CollUtil.isEmpty(statisticsDiskSpaces)) {
            log.error("定时统计每个CP的磁盘使用量查询结果为空");
            return;
        }
        //删除当天数据，避免重复触发产生脏数据
        statisticsDiskSpaceMapper.delete(new LambdaQueryWrapper<StatisticsDiskSpace>()
                .isNotNull(StatisticsDiskSpace::getCpId)
                .between(StatisticsDiskSpace::getStatisticDate
                        , DateUtil.format(DateUtil.beginOfDay(date), DateUtils.YYYY_MM_DD_HH_MM_SS)
                        , DateUtil.format(DateUtil.endOfDay(date), DateUtils.YYYY_MM_DD_HH_MM_SS))
        );
        statisticsDiskSpaceMapper.batchInsert(statisticsDiskSpaces);
        redisService.setCacheMap(RedisKeyConstants.CP_DISK_SPACE, map);
        // 清除上传介质或删除介质的缓存
        redisService.deleteObjects(RedisKeyConstants.STATISTIC_STORAGE);
        log.error("定时统计每个CP的磁盘使用量耗时{}ms", System.currentTimeMillis() - start);
    }

    @Override
    public void statisticDiskSpaceRefreshByCp() {
        Date date = new Date();
        HashMap<String, Boolean> map = new HashMap<>();
        LambdaQueryWrapper<StatisticsDiskSpace> wrapper = new LambdaQueryWrapper<StatisticsDiskSpace>()
                .isNotNull(StatisticsDiskSpace::getCpId)
                .between(StatisticsDiskSpace::getStatisticDate, DateUtil.format(DateUtil.beginOfDay(date), DateUtils.YYYY_MM_DD_HH_MM_SS),
                        DateUtil.format(DateUtil.endOfDay(date), DateUtils.YYYY_MM_DD_HH_MM_SS));

        List<StatisticsDiskSpace> statisticsDiskSpaceMysql = statisticsDiskSpaceMapper.selectList(wrapper);
        if (CollectionUtils.isEmpty(statisticsDiskSpaceMysql)) {
            return;
        }
        for (StatisticsDiskSpace statisticsDiskSpace : statisticsDiskSpaceMysql) {
            long totalSpace = null != statisticsDiskSpace.getTotalSpace() ? statisticsDiskSpace.getTotalSpace() : 0;
            double usedProportion = 0;
            if (totalSpace != 0) {
                Boolean aBoolean = redisService.hasHashKey(RedisKeyConstants.STATISTIC_STORAGE, statisticsDiskSpace.getCpId().toString());
                if (!aBoolean) {
                    usedProportion = (double) statisticsDiskSpace.getUsedSpace() / totalSpace;
                } else {
                    Long value = redisService.getCacheMapValue(RedisKeyConstants.STATISTIC_STORAGE, statisticsDiskSpace.getCpId().toString());
                    usedProportion = (double) (statisticsDiskSpace.getUsedSpace() + (value / 1024d)) / totalSpace;
                }
            }
            map.put(String.valueOf(statisticsDiskSpace.getCpId()), usedProportion < maxSpaceThreshold);
        }
        redisService.setCacheMap(RedisKeyConstants.CP_DISK_SPACE, map);
    }

}
