package com.pukka.iptv.statistics.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.pukka.iptv.common.base.vo.CommonResponse;
import com.pukka.iptv.common.data.model.statistics.StatisticsIn;
import com.pukka.iptv.common.data.vo.req.StatisticsInVo;
import com.pukka.iptv.common.data.vo.req.StatisticsInVoD;
import com.pukka.iptv.common.data.vo.req.StatisticsInVoP;

import java.util.HashMap;
import java.util.List;

/**
 *
 * @author: tan
 * @date: 2022-7-14 11:02:04
 */

public interface StatisticsInService extends IService<StatisticsIn> {

    CommonResponse<List<StatisticsIn>> getAllStatistics(StatisticsInVo statisticsInVo);

    CommonResponse<List<StatisticsIn>> getAllStatisticsByLastMonth(StatisticsInVoD statisticsInVo);

    IPage<StatisticsIn> getProgramRankStatistics(StatisticsInVoP statisticsInVo);

    CommonResponse<List<StatisticsIn>> getProgramRankStatisticsList(StatisticsInVoD statisticsInVoD);

     HashMap<String, List<Long>> getPermission();

}


