package com.pukka.iptv.statistics.config;

import cn.hutool.core.util.RandomUtil;
import com.baomidou.mybatisplus.core.incrementer.DefaultIdentifierGenerator;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * @ClassName MybatisPlusConfig
 * @Description TODO
 * <AUTHOR>
 * @Date 2022/8/15 15:37
 * @Version
 */
@Configuration
public class MybatisPlusConfig {

    /**
     * description: 自定义ID主键生成器 <br>
     * date: 2022/6/1 11:38 <br>
     * author: flygo <br>
     *
     * @return com.baomidou.mybatisplus.core.incrementer.DefaultIdentifierGenerator
     */
    @Bean
    public DefaultIdentifierGenerator defaultIdentifierGenerator() {
        // 以免多线程批量插入，ID主键生成有重复，主键冲突错误
        // 1-31 随机数
        long workerId = RandomUtil.randomLong(1, 31);
        // 1-31 随机数
        long dataCenterId = RandomUtil.randomLong(1, 31);

        return new DefaultIdentifierGenerator(workerId, dataCenterId);
    }

}
