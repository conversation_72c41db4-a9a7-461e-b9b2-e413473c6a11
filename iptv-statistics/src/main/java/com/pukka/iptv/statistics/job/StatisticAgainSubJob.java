package com.pukka.iptv.statistics.job;

import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.pukka.iptv.common.api.feign.cms.CmsProgramFeignClient;
import com.pukka.iptv.common.base.enums.StatisticsTypeEnum;
import com.pukka.iptv.common.base.vo.CommonResponse;
import com.pukka.iptv.common.data.model.statistics.StatisticsInAgain;
import com.pukka.iptv.common.data.model.statistics.StatisticsInFinal;
import com.pukka.iptv.common.data.vo.req.StatisticsInVo;
import com.pukka.iptv.statistics.service.StatisticsInAgainService;
import com.pukka.iptv.statistics.service.StatisticsInFinalService;
import com.pukka.iptv.statistics.util.DateUtil;
import feign.Request;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.concurrent.TimeUnit;

/**
 * @ClassName StatisticAgainSubJob
 * @Description TODO
 * <AUTHOR>
 * @Date 2022/7/14 11:49
 * @Version
 */
@Component
@Slf4j
public class StatisticAgainSubJob implements IStatisticJob {
    @Autowired
    CmsProgramFeignClient cmsProgramFeignClient;

    @Autowired
    StatisticsInAgainService statisticsInAgainService;

    @Override
    public void executeJob() {
        StatisticsInVo statisticsInVo = new StatisticsInVo();
//        statisticsInVo.setStartTime("2022-04-11 00:00:00");
//        statisticsInVo.setEndTime("2022-04-15 23:59:59");

        Map map = DateUtil.getYesterdayTime();
        statisticsInVo.setStartTime((String) map.get("startDate"));
        statisticsInVo.setEndTime((String) map.get("endDate"));

        Request.Options options = new Request.Options(5, TimeUnit.SECONDS, 160, TimeUnit.SECONDS, true);
        CommonResponse<List<StatisticsInAgain>> commonResponse =  cmsProgramFeignClient.againSubStatistic(statisticsInVo, options);
        List<StatisticsInAgain> statisticsInAgainList = (List<StatisticsInAgain>) commonResponse.getData();
        List<StatisticsInAgain> statisticsInsAgainListAdd = new ArrayList<>();
        LambdaQueryWrapper<StatisticsInAgain> queryWrapper = Wrappers.lambdaQuery();
        statisticsInAgainList.forEach(e ->
        {
            queryWrapper.clear();
            queryWrapper.eq(StatisticsInAgain::getStatisticDate, statisticsInVo.getStartTime())
                    .eq(StatisticsInAgain::getCpId, e.getCpId())
                    .eq(StatisticsInAgain::getType, StatisticsTypeEnum.SimpleSet.getValue())
                    .eq(StatisticsInAgain::getPassType, e.getPassType());

            if(Objects.isNull(e.getContentType())){
                queryWrapper.isNull(Objects.isNull(e.getContentType()),StatisticsInAgain::getContentType);
            }else{
                queryWrapper.eq(StatisticsInAgain::getContentType, e.getContentType());
            }
            List<StatisticsInAgain> statisticsInAgains = statisticsInAgainService.list(queryWrapper);
            if(statisticsInAgains.size() == 0){
                statisticsInsAgainListAdd.add(e);
            }
        });
        if(statisticsInsAgainListAdd.size() >0 ){
            Boolean isSave = statisticsInAgainService.saveBatch(statisticsInsAgainListAdd);
            if(isSave){
                log.info("StatisticAgainSubJob ->>>>> time = {},size = {}", statisticsInVo.getStartTime(),statisticsInsAgainListAdd.size());
            }else{
                log.info("StatisticAgainSubJob is fail ->>>>> time = {}",statisticsInVo.getStartTime() );
            }
        }
        log.info("StatisticAgainSubJob ->>>>> time = {},size = {}", statisticsInVo.getStartTime(), statisticsInsAgainListAdd.size());

    }


    public void executeJob(StatisticsInVo statisticsInVo) {

        Request.Options options = new Request.Options(5, TimeUnit.SECONDS, 160, TimeUnit.SECONDS, true);
        CommonResponse<List<StatisticsInAgain>> commonResponse =  cmsProgramFeignClient.againSubStatistic(statisticsInVo, options);
        List<StatisticsInAgain> statisticsInAgainList = (List<StatisticsInAgain>) commonResponse.getData();
        List<StatisticsInAgain> statisticsInsAgainListAdd = new ArrayList<>();
        LambdaQueryWrapper<StatisticsInAgain> queryWrapper = Wrappers.lambdaQuery();

        if(ObjectUtil.isEmpty(statisticsInAgainList)){
            return;
        }
        statisticsInAgainList.forEach(e ->
        {
            queryWrapper.clear();
            queryWrapper.eq(StatisticsInAgain::getStatisticDate, statisticsInVo.getStartTime())
                    .eq(StatisticsInAgain::getCpId, e.getCpId())
                    .eq(StatisticsInAgain::getType, StatisticsTypeEnum.SimpleSet.getValue())
                    .eq(StatisticsInAgain::getPassType, e.getPassType());

            if(Objects.isNull(e.getContentType())){
                queryWrapper.isNull(Objects.isNull(e.getContentType()),StatisticsInAgain::getContentType);
            }else{
                queryWrapper.eq(StatisticsInAgain::getContentType, e.getContentType());
            }
            List<StatisticsInAgain> statisticsInFinals = statisticsInAgainService.list(queryWrapper);
            if(statisticsInFinals.size() == 0){
                statisticsInsAgainListAdd.add(e);
            }
        });
        if(statisticsInsAgainListAdd.size() >0 ){
            Boolean isSave = statisticsInAgainService.saveBatch(statisticsInsAgainListAdd);
            if(isSave){
                log.info("StatisticAgainSubJob ->>>>> time = {},size = {}", statisticsInVo.getStartTime(),statisticsInsAgainListAdd.size());
            }else{
                log.info("StatisticAgainSubJob is fail ->>>>> time = {}",statisticsInVo.getStartTime() );
            }
        }
        log.info("StatisticAgainSubJob ->>>>> time = {},size = {}", statisticsInVo.getStartTime(), statisticsInsAgainListAdd.size());

    }
}
