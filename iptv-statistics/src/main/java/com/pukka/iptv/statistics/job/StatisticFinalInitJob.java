package com.pukka.iptv.statistics.job;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.pukka.iptv.common.data.model.statistics.StatisticsInCheckInit;
import com.pukka.iptv.common.data.model.statistics.StatisticsInFinal;
import com.pukka.iptv.common.data.model.statistics.StatisticsInFinalInit;
import com.pukka.iptv.common.data.util.TimeUtil;
import com.pukka.iptv.common.data.vo.req.StatisticsInVo;
import com.pukka.iptv.statistics.service.StatisticsInCheckInitService;
import com.pukka.iptv.statistics.service.StatisticsInFinalInitService;
import com.pukka.iptv.statistics.service.StatisticsInFinalService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.List;

/**
 * @ClassName StatisticFinalJob
 * @Description TODO
 * <AUTHOR>
 * @Date 2022/7/14 11:51
 * @Version
 */
@Component
@Slf4j
public class StatisticFinalInitJob implements IStatisticJob{

    @Autowired
    StatisticsInFinalInitService statisticsInFinalInitService;

    @Autowired
    StatisticFinalProgramJob statisticFinalProgramJob;

    @Autowired
    StatisticFinalSeriesJob statisticFinalSeriesJob;
    @Autowired
    StatisticFinalSubJob statisticFinalSubJob;

    @Override
    public void executeJob() {
        LambdaQueryWrapper<StatisticsInFinalInit> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.eq(StatisticsInFinalInit::getStatus, 1);
        List<StatisticsInFinalInit> statisticsInFinalInitList = statisticsInFinalInitService.list(queryWrapper);
        statisticsInFinalInitList.forEach(e -> {

            SimpleDateFormat formater = new SimpleDateFormat("yyyy-MM-dd");
            SimpleDateFormat formater2 = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
            Date start = null;
            Date end = null;
            try {
                start = formater2.parse(formater.format(TimeUtil.getDate(e.getStatisticDate()))+ " 00:00:00");
                end = formater2.parse(formater.format(TimeUtil.getDate(e.getStatisticDate()))+ " 23:59:59");
            } catch (ParseException parseException) {
                parseException.printStackTrace();
            }

            StatisticsInVo statisticsInVo = new StatisticsInVo();
            statisticsInVo.setStartTime(TimeUtil.formatDate(start));

            statisticsInVo.setEndTime(TimeUtil.formatDate(end));
            statisticFinalProgramJob.executeJob(statisticsInVo);
            statisticFinalSeriesJob.executeJob(statisticsInVo);
            statisticFinalSubJob.executeJob(statisticsInVo);

            //完成后修改
            e.setStatus(0);
            statisticsInFinalInitService.updateById(e);
        });
        log.info("StatisticFinalInitJob finish");
    }
}
