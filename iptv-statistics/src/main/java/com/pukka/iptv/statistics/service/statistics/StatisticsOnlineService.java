package com.pukka.iptv.statistics.service.statistics;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.pukka.iptv.statistics.model.StatisticsOnline;
import com.pukka.iptv.statistics.vo.StatisticsOnlineRankRespVo;
import com.pukka.iptv.statistics.vo.StatisticsOnlineReqVo;
import com.pukka.iptv.statistics.vo.StatisticsOnlineRespVo;

import java.util.HashMap;
import java.util.List;
import java.util.concurrent.CountDownLatch;

/**
 * @author: zhengcl
 * @date: 2022年7月13日 上午9:55:27
 */
public interface StatisticsOnlineService extends IService<StatisticsOnline> {

    StatisticsOnlineRespVo getOnlineContentTotal(StatisticsOnlineReqVo statisticsOnline);

    StatisticsOnlineRespVo getNewOnlineContentTotal(StatisticsOnlineReqVo statisticsOnline);

    List<StatisticsOnlineRespVo> getNewOnlineContentAsync(List<StatisticsOnlineRespVo> list, StatisticsOnlineReqVo statisticsOnline, CountDownLatch downLatch, HashMap<String, List<Long>> permissionMap);

    List<StatisticsOnlineRespVo> getOfflineContentAsync(List<StatisticsOnlineRespVo> list, StatisticsOnlineReqVo statisticsOnline, CountDownLatch downLatch, HashMap<String, List<Long>> permissionMap);

    StatisticsOnlineRespVo getOfflineContentTotal(StatisticsOnlineReqVo statisticsOnline);

    Page<StatisticsOnlineRankRespVo> getNewOnlineContentTotalRank(Page page, StatisticsOnlineReqVo statisticsOnline);

    void clearCache();
}


