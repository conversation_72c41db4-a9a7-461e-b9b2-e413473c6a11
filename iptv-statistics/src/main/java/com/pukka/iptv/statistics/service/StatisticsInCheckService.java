package com.pukka.iptv.statistics.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.pukka.iptv.common.base.vo.CommonResponse;
import com.pukka.iptv.common.data.model.*;
import com.pukka.iptv.common.data.model.statistics.StatisticsIn;
import com.pukka.iptv.common.data.model.statistics.StatisticsInCheck;
import com.pukka.iptv.common.data.vo.req.StatisticsInVo;
import com.pukka.iptv.common.data.vo.req.StatisticsInVoD;

import java.util.List;

/**
 *
 * @author: tan
 * @date: 2022-7-21 9:15:48
 */

public interface StatisticsInCheckService extends IService<StatisticsInCheck> {

    CommonResponse<List<StatisticsInCheck>> getSelfAllStatistics(StatisticsInVo statisticsInVo);

    CommonResponse<List<StatisticsInCheck>> getSelfAllStatisticsLastMonth(StatisticsInVoD statisticsInVo);

}


