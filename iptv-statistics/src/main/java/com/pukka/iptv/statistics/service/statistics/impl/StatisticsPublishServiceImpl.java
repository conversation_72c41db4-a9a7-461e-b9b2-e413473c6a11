package com.pukka.iptv.statistics.service.statistics.impl;

import cn.hutool.core.collection.CollUtil;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.pukka.iptv.common.base.constant.RedisKeyConstants;
import com.pukka.iptv.common.base.enums.ContentTypeEnum;
import com.pukka.iptv.common.data.model.cms.CmsProgram;
import com.pukka.iptv.common.data.model.cms.CmsSeries;
import com.pukka.iptv.common.data.model.sys.SysSp;
import com.pukka.iptv.common.redis.service.RedisService;
import com.pukka.iptv.statistics.mapper.StatisticsPublishMapper;
import com.pukka.iptv.statistics.mapper.cms.CmsProgramMapper;
import com.pukka.iptv.statistics.mapper.cms.CmsSeriesMapper;
import com.pukka.iptv.statistics.model.StatisticsPublish;
import com.pukka.iptv.statistics.service.es.ESService;
import com.pukka.iptv.statistics.service.statistics.StatisticsPublishService;
import com.pukka.iptv.statistics.vo.StatisticsPublishItemRespVo;
import com.pukka.iptv.statistics.vo.StatisticsPublishReqVo;
import com.pukka.iptv.statistics.vo.StatisticsPublishRespVo;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;

/**
 * @author: zhengcl
 * @date: 2022年7月13日 上午9:55:36
 */
@Service
public class StatisticsPublishServiceImpl extends ServiceImpl<StatisticsPublishMapper, StatisticsPublish> implements StatisticsPublishService {

    @Resource
    private ESService esService;
    @Resource
    private RedisService redisService;
    @Resource
    private CmsSeriesMapper cmsSeriesMapper;
    @Resource
    private CmsProgramMapper cmsProgramMapper;

    @Override
    public StatisticsPublishRespVo getPublishTotal(StatisticsPublishReqVo vo) {
        StatisticsPublishRespVo model = new StatisticsPublishRespVo();
        model.setType(vo.getType());
        model.setWorkOrderType(vo.getWorkOrderType());
        if (1 == vo.getWorkOrderType()) {
            model = esService.getPublishContentTotal(vo);
            model = esService.getPublishSubsetTotal(model, vo);
        } else if (2 == vo.getWorkOrderType()) {
            model = esService.getPublishPackageContentTotal(vo);
        } else if (3 == vo.getWorkOrderType()) {
            model = esService.getPublishCategoryContentTotal(vo);
        }
        getPublishTotalAftertreatment(model);
        return model;
    }

    @Override
    public Page<StatisticsPublishItemRespVo> getPublishPage(Page page, StatisticsPublishReqVo reqVo) {
        if (1 == reqVo.getWorkOrderType()) {
            if (ContentTypeEnum.SUBSET.getValue().equals(reqVo.getContentType())) {
                page = esService.getPublishSubsetPage(page, reqVo);
            } else {
                page = esService.getPublishContentPage(page, reqVo);
            }
        } else if (2 == reqVo.getWorkOrderType()) {
            page = esService.getPublishPackageContentPage(page, reqVo);
        } else if (3 == reqVo.getWorkOrderType()) {
            page = esService.getPublishCategoryContentPage(page, reqVo);
        }
        List records = page.getRecords();
        if (CollUtil.isNotEmpty(records)) {
            ArrayList<StatisticsPublishItemRespVo> respVos = new ArrayList<>();
            for (Object record : records) {
                HashMap hashMap = JSON.parseObject(JSON.toJSONString(record), HashMap.class);
                StatisticsPublishItemRespVo respVo = JSON.parseObject(JSON.toJSONString(record), StatisticsPublishItemRespVo.class);
                if (1 == reqVo.getWorkOrderType()) {
                    respVo.setContentName(String.valueOf(hashMap.get("name")));
                } else {
                    SysSp sysSp = redisService.getCacheMapValue(RedisKeyConstants.SYS_SP, String.valueOf(respVo.getSpId()));
                    if (null != sysSp) {
                        respVo.setBmsSpChannelId(sysSp.getBmsSpChannelId());
                        respVo.setBmsSpChannelName(sysSp.getBmsSpChannelName());
                    }
                    int contentType = Integer.parseInt(String.valueOf(hashMap.get("contentType")));
                    if (ContentTypeEnum.FILM.getValue().equals(contentType)) {
                        CmsProgram cmsProgram = cmsProgramMapper.selectOne(new LambdaQueryWrapper<CmsProgram>().eq(CmsProgram::getCode, String.valueOf(hashMap.get("cmsContentCode"))));
                        if (null != cmsProgram) {
                            respVo.setPgmCategoryId(cmsProgram.getPgmCategoryId());
                            respVo.setPgmCategory(cmsProgram.getPgmCategory());
                        }
                    } else {
                        CmsSeries cmsSeries = cmsSeriesMapper.selectOne(new LambdaQueryWrapper<CmsSeries>().eq(CmsSeries::getCode, String.valueOf(hashMap.get("cmsContentCode"))));
                        if (null != cmsSeries) {
                            respVo.setPgmCategoryId(cmsSeries.getPgmCategoryId());
                            respVo.setPgmCategory(cmsSeries.getPgmCategory());
                        }
                    }
                }
                respVos.add(respVo);
            }
            page.setRecords(respVos);
        }
        return page;
    }

    private void getPublishTotalAftertreatment(StatisticsPublishRespVo model) {
        long countFailProgram = model.getCountFailProgram();
        long countSuccessProgram = model.getCountSuccessProgram();
        long countFailSeries = model.getCountFailSeries();
        long countSuccessSeries = model.getCountSuccessSeries();
        long countFailSubset = model.getCountFailSubset();
        long countSuccessSubset = model.getCountSuccessSubset();
        long countProgram = model.getCountProgram();
        long countSeries = model.getCountSeries();
        long countSubset = model.getCountSubset();
        model.setCountSeries(countFailSeries + countSuccessSeries + countSeries);
        model.setCountProgram(countFailProgram + countSuccessProgram + countProgram);
        model.setCountSubset(countFailSubset + countSuccessSubset + countSubset);
    }
}


