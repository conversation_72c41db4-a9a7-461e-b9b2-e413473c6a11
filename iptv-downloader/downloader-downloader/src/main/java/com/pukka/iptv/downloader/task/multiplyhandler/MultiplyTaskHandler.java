package com.pukka.iptv.downloader.task.multiplyhandler;


import com.pukka.iptv.downloader.config.NodeConfig;
import com.pukka.iptv.downloader.model.DownloadTask;
import com.pukka.iptv.downloader.task.downloader.AbstractDownloader;
import com.pukka.iptv.downloader.task.downloader.Downloader;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.concurrent.locks.Lock;
import java.util.concurrent.locks.ReentrantLock;


/**
 * @Author: wz
 * @Date: 2022/1/4 18:40
 * @Description: 多任务处理器
 */
@Slf4j
@Component
public class MultiplyTaskHandler extends AbstractMultiplyTaskPool<DownloadTask> {
    //此处由于多个下载器是共享的 多任务处理器 所以在没有给各自分配 队列限制前，队列和锁也必须共享
    private final static ReentrantLock lock = new ReentrantLock();

    @Autowired
    private NodeConfig config;

    @Resource(name = "downloaderThreadPool")
    private ThreadPoolTaskExecutor executor;

    @Override
    protected ThreadPoolTaskExecutor getExecutor() {
        return executor;
    }

    @Override
    protected Lock getLock() {
        return lock;
    }


    @Override
    protected boolean doWork(DownloadTask task) {
        if (task == null) {
            return false;
        }
        //选择下载器
        Downloader<DownloadTask> downloader = AbstractDownloader.selectDownloader(task);
        assert downloader != null;
        downloader.preHandler(task);
        log.info("下载任务filecode:{},下载状态：{}，前置处理完成", task.getFileTask().getFileCode(), task.getStatus());
        try {
            downloader.download(task);
            log.info("下载任务filecode:{},下载状态：{}，下载完成", task.getFileTask().getFileCode(), task.getStatus());
        } catch (Exception e) {
            log.error(e.getMessage(), e);
        }
        downloader.postHandler(task);
        log.info("下载任务filecode:{},下载状态：{}，后置处理完成", task.getFileTask().getFileCode(), task.getStatus());

        return true;
    }

    @Override
    public int getLimit() {
        return config.getConcurrentLimit();
    }

}



