package com.pukka.iptv.downloader.util;

import cn.hutool.core.io.FileUtil;
import cn.hutool.core.lang.Assert;
import com.pukka.iptv.downloader.config.HttpClient;
import com.pukka.iptv.downloader.model.ConnectHead;
import com.pukka.iptv.downloader.model.HttpInfo;
import com.pukka.iptv.downloader.model.Proxy;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.codec.digest.DigestUtils;
import org.apache.commons.io.FileUtils;
import org.apache.commons.io.IOUtils;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.http.HttpRange;
import org.springframework.http.MediaType;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.util.FileCopyUtils;
import org.springframework.util.StringUtils;
import org.springframework.web.client.RequestCallback;
import org.springframework.web.client.RestTemplate;

import java.io.*;
import java.net.HttpURLConnection;
import java.net.URL;
import java.nio.file.Files;
import java.util.Arrays;
import java.util.Collections;
import java.util.concurrent.Callable;
import java.util.concurrent.CountDownLatch;


@Slf4j
/**
 * @Description: 文件分片下载，开启多线程，每个小城下载其中一部分文件到本地，全部分片下载完毕后，进行分片文件的合并
 *  优势：1.多线程下载提高效率 2.可以天然支持断点下载
 *  缺点：1.因为存在文件合并，所以会多一次IO操作
 * @auther: wz
 * @date: 2022/6/23 16:21
 */
public class HttpSlice {


    private static volatile HttpSlice instance;

    public static HttpSlice getInstance() {
        if (instance == null) {
            synchronized (HttpSlice.class) {
                if (instance == null) {
                    instance = new HttpSlice();
                }
            }
        }
        return instance;
    }

    //返回http客户端
    private static RestTemplate getHttp() {
        return HttpClient.getRestHttp();
    }

    //返回带代理的http客户端
    private static RestTemplate getProxyHttp(Proxy proxy) {
        return HttpClient.getProxyHttp(proxy);
    }

    public boolean httpDownFile(ConnectHead head, String url, File file) throws Exception {
        Proxy proxy = head.proxy();
        RestTemplate httpClient = proxy == null ? getHttp() : getProxyHttp(proxy);
        Assert.notNull(httpClient);
        RequestCallback callback = request -> request.getHeaders()
                .setAccept(Arrays.asList(MediaType.APPLICATION_OCTET_STREAM, MediaType.ALL));
        FileOutputStream outputStream = null;
        try {
            outputStream = new FileOutputStream(file);
            FileOutputStream finalout = outputStream;
            httpClient.execute(url, HttpMethod.GET, callback, resp -> {
                FileCopyUtils.copy(resp.getBody(), finalout);
                //log.info("下载完成:{}", resp.getStatusCode());
                return true;
            });
        } finally {
            IoUtil.closeStream(outputStream);
        }
        return true;
    }

    private static RestTemplate getHttpClient(ConnectHead head) {
        Proxy proxy = head.proxy();
        return proxy == null ? getHttp() : getProxyHttp(proxy);
    }

    private static ThreadPoolTaskExecutor getExecutor() {
        ThreadPoolTaskExecutor threadPoolTaskExecutor = new ThreadPoolTaskExecutor();
        threadPoolTaskExecutor.setCorePoolSize(16);
        threadPoolTaskExecutor.setMaxPoolSize(50);
        threadPoolTaskExecutor.setQueueCapacity(1);
        threadPoolTaskExecutor.initialize();
        return threadPoolTaskExecutor;
    }

    //文件完成性校验 ETag: "1FFD36BD1B06EB6C287AF8D788458808"
    public static void main(String[] args) throws Exception {


//        ConnectHead head = new ConnectHead();
//        HttpInfo httpInfo = new HttpInfo();
//        httpInfo.setRemoteUrl("http://localhost:8800/file/111.mkv");
//        httpInfo.setLocalFilePath("D:/data/file/testSlice.ts");
//        sliceDownloadHttp(head, httpInfo, 5);


//        long begin = System.currentTimeMillis();
//        appendFile("http://localhost:8800/file/111.mkv", new File("D:/data/file/final.ts"), 0, 3272035833L);
//        log.info("单线程下载耗时：{}", System.currentTimeMillis() - begin);
    }

    public static boolean sliceDownloadHttp(ConnectHead head, HttpInfo httpInfo, int threadCount) throws Exception {
        RestTemplate httpClient = getHttpClient(head);
        long beginTime = System.currentTimeMillis();
        //判断url是否支持分片下载
        long fileSize = isSupportRange(head, httpInfo.getRemoteUrl());
        if (fileSize > 0) {
            log.info("远程文件大小:{}", fileSize);
            segmentDownload(httpClient, httpInfo.getRemoteUrl(), httpInfo.getLocalFilePath(), fileSize, threadCount);
        } else if (fileSize == -1) {
            log.error("文件不支持分片下载 url={}", httpInfo.getRemoteUrl());
        } else {
            log.error("文件支持分片下载,但获取的文件大小为0 url={}", httpInfo.getRemoteUrl());
        }
        log.info("分片下载总耗时：{}", System.currentTimeMillis() - beginTime);
        return false;
    }

    /**
     * 判断连接是否支持断点下载
     */
    private static long isSupportRange(ConnectHead head, String netUrl) throws Exception {
        RestTemplate httpClient = getHttpClient(head);
        HttpURLConnection httpURLConnection = getHttpUrlConnection(netUrl);
        String acceptRanges = httpURLConnection.getHeaderField("Accept-Ranges");
        String contentLen = httpURLConnection.getHeaderField("content-length");
        Long fileSize = null;
        if (!StringUtils.hasLength(acceptRanges)) {
            return -1;
        }
        if (StringUtils.hasLength(contentLen)) {
            fileSize = Long.parseLong(contentLen);
        }
//        Integer fileSize = httpClient.execute(netUrl, HttpMethod.GET, null, response -> {
//            HttpHeaders headers = response.getHeaders();
//            String acceptRanges = headers.getFirst("Accept-Ranges");
//            String contentLen = headers.getFirst("content-length");
//            if (!StringUtils.hasLength(acceptRanges)) {
//                return -1;
//            }
//            if (StringUtils.hasLength(contentLen)) {
//                return Integer.parseInt(contentLen);
//            }
////            if ("bytes".equalsIgnoreCase(acceptRanges)) {
////                return 1;
////            }
//            response.close();
//            return 0;
//        });
        if (fileSize == null) {
            throw new RuntimeException("文件获取失败! url=" + netUrl);
        }
        return fileSize;
    }


    /**
     * 获取连接
     */
    private static HttpURLConnection getHttpUrlConnection(String netUrl) throws Exception {
        URL url = new URL(netUrl);
        HttpURLConnection httpURLConnection = (HttpURLConnection) url.openConnection();
        // 设置超时间为3秒
        httpURLConnection.setConnectTimeout(3 * 1000);
        // 防止屏蔽程序抓取而返回403错误
        httpURLConnection.setRequestProperty("User-Agent", "Mozilla/4.0 (compatible; MSIE 5.0; Windows NT; DigExt)");
        return httpURLConnection;
    }

    @Setter
    @Getter
    @AllArgsConstructor
    static class SliceFile implements Callable<String> {
        private RestTemplate httpClient;
        private int num;
        private String url;
        private File sliceFile;
        private long start;
        private long end;
        //最终文件
        private File finalFile;
        private CountDownLatch latch;

        @Override
        public String call() throws Exception {
            try {
//                boolean done = download(httpClient, url, sliceFile, start, end);
                boolean done = appendFile(url, sliceFile, start, end);
                if (done) {
                    //计算MD5文件完成性
                    //使用随机读写合并进文件
                    mergeFile(sliceFile, finalFile, start, end);
                }
            } finally {
                latch.countDown();
            }
            return null;
        }
    }

    /**
     * @param totalFileSize 文件总大小
     * @param N             串行下载分段次数
     */
    private static void segmentDownload(RestTemplate httpClient, String url, String localFilePath, long totalFileSize, int N) throws Exception {
        ThreadPoolTaskExecutor executor = getExecutor();
        CountDownLatch latch = new CountDownLatch(N);
        // 最终本地文件
        File finalFile = new File(localFilePath);
        FileUtil.mkParentDirs(finalFile);
        if (!finalFile.exists()) {
            finalFile.createNewFile();
        }
        // 文件我们分N次来下载
        long eachFileSize = totalFileSize / N;
        long start, end = 0;
        for (int i = 1; i <= N; i++) {
            File slice = new File(localFilePath + "_" + i);
            // 获取本地文件，如果为空，则start=0，不为空则为该本地文件的大小作为断点下载开始位置
            if (i == 1) {
                start = 0;
                end = eachFileSize;
            } else if (i == N) {
                start = end;
                end = totalFileSize;
            } else {
                start = end;
                end = eachFileSize * i;
            }
            //断点计算
            if (slice.length() != 0) {
                // findEndIndex(localFilePath,start)
                start += slice.length();
            }
            executor.submit(new SliceFile(httpClient, i, url, slice, start, end, finalFile, latch));
            log.info(String.format("我是第%s次下载，下载片段范围start=%s,end=%s", i, start, end));
        }
        //主线程等待
        latch.await();
        //计算最终文件完整性 MD5
        log.info("远程文件大小：{} 本地文件大小：{}", totalFileSize, finalFile.length());
    }


    /**
     * 文件尾部追加
     *
     * @param netUrl    地址
     * @param localFile 本地文件
     * @param start     分段开始位置
     * @param end       分段结束位置
     */
    private static boolean appendFile(String netUrl, File localFile, long start, long end) throws Exception {
        HttpURLConnection httpURLConnection = getHttpUrlConnection(netUrl);
        httpURLConnection.setRequestProperty("Range", "bytes=" + start + "-" + end);
        // 获取远程文件流信息
        InputStream inputStream = httpURLConnection.getInputStream();
        // 本地文件写入流，支持文件追加
        FileOutputStream fos = FileUtils.openOutputStream(localFile, true);
        IOUtils.copy(inputStream, fos);
        IoUtil.closeStream(inputStream, fos);
        httpURLConnection.disconnect();
        return true;
    }


    /**
     * 校验文件一致性，我们判断Etag和本地文件的md5是否一致
     * 注:Etag携带了双引号
     *
     * @param localFilePath
     * @param netUrl
     */
    private static void validateCompleteness(String localFilePath, String netUrl) throws Exception {
        File file = new File(localFilePath);
        InputStream data = Files.newInputStream(file.toPath());
        String md5 = DigestUtils.md5Hex(data);
        HttpURLConnection httpURLConnection = getHttpUrlConnection(netUrl);
        String etag = httpURLConnection.getHeaderField("Etag");
        if (etag.toUpperCase().contains(md5.toUpperCase())) {
            System.out.println(String.format("本地文件和远程文件一致，md5 = %s, Etag = %s", md5.toUpperCase(), etag));
        } else {
            System.out.println(String.format("本地文件和远程文件不一致，md5 = %s, Etag = %s", md5.toUpperCase(), etag));
        }
        IoUtil.closeStream(data);
        httpURLConnection.disconnect();
    }

    /**
     * 文件尾部追加
     *
     * @param netUrl    地址
     * @param localFile 本地文件
     * @param start     分段开始位置
     * @param end       分段结束位置
     */
    private static boolean download(RestTemplate httpClient, String netUrl, File localFile, long start, long end) throws Exception {
        Assert.notNull(httpClient);
        RequestCallback callback = request -> {
            HttpHeaders headers = request.getHeaders();
            headers.setAccept(Arrays.asList(MediaType.APPLICATION_OCTET_STREAM, MediaType.ALL));
            HttpRange byteRange = HttpRange.createByteRange(start, end);
            headers.setRange(Collections.singletonList(byteRange));
        };
        String fileAbsolutePath = localFile.getAbsolutePath();
        FileOutputStream outputStream = null;
        try {
            outputStream = new FileOutputStream(localFile);
            FileOutputStream finalout = outputStream;
            //log.info("下载完成:{}", resp.getStatusCode());
            return Boolean.TRUE.equals(httpClient.execute(netUrl, HttpMethod.GET, callback, resp -> {
                HttpHeaders headers = resp.getHeaders();
                String eTag = headers.getFirst("ETag");
                log.info("localFile={} eTag={}", fileAbsolutePath, eTag);
                FileCopyUtils.copy(resp.getBody(), finalout);
                //log.info("下载完成:{}", resp.getStatusCode());
                return true;
            }));
        } finally {
            IoUtil.closeStream(outputStream);
        }
    }


    /**
     * 文件下载、合并
     *
     * @param localFilePath 本地文件路径
     * @param start         范围请求开始位置
     * @param end           范围请求结束位置
     */
    private static void mergeFile(File sliceFile, File localFilePath, long start, long end) {
        log.info("开始合并文件{}", localFilePath.getAbsolutePath());
        Thread thread = Thread.currentThread();
        FileInputStream fileInputStream = null;
        RandomAccessFile randomAccessFile = null;
        try {
            fileInputStream = new FileInputStream(sliceFile);
            randomAccessFile = new RandomAccessFile(localFilePath, "rw");
            // 文件写入开始位置指针移动到已经下载位置
            randomAccessFile.seek(start);
            byte[] buffer = new byte[1024 * 10];
            int len = -1;
            while ((len = fileInputStream.read(buffer)) != -1) {
                randomAccessFile.write(buffer, 0, len);
            }
            log.info(String.format("下载完成时间%s, 线程：%s, 下载完成: start=%s, end = %s", System.currentTimeMillis(), thread.getName() + thread.getId(), start, end));
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            log.info(String.format("片段合并异常：线程：%s, start=%s, end = %s", thread.getName() + thread.getId(), start, end));
        } finally {
            IoUtil.closeStream(randomAccessFile, fileInputStream);
        }
    }


    /**
     * 文件下载、合并
     */
    private static void downloadAndMerge(int threadNum, String netUrl, File finalFile, int start, int end) {
        try {
            HttpURLConnection httpURLConnection = getHttpUrlConnection(netUrl);
            httpURLConnection.setRequestProperty("Range", "bytes=" + start + "-" + end);
            // 获取远程文件流信息
            InputStream inputStream = httpURLConnection.getInputStream();
            RandomAccessFile randomAccessFile = new RandomAccessFile(finalFile, "rw");
            // 文件写入开始位置指针移动到已经下载位置
            randomAccessFile.seek(start);
            byte[] buffer = new byte[1024 * 10];
            int len = -1;
            while ((len = inputStream.read(buffer)) != -1) {
                randomAccessFile.write(buffer, 0, len);
            }

            System.out.println(String.format("下载完成时间%s, 线程：%s, 下载完成: start=%s, end = %s", System.currentTimeMillis(), threadNum, start, end));
        } catch (Exception e) {
            System.out.println(String.format("片段下载异常：线程：%s, start=%s, end = %s", threadNum, start, end));
            e.printStackTrace();
        }

    }

    //获取本地文件每段的长度
    private static long findEndIndex(String localFilePath, long startIndex) {
        FileInputStream fileInputStream = null;
        RandomAccessFile randomAccessFile = null;
        long localSize = startIndex;
        try {
            randomAccessFile = new RandomAccessFile(localFilePath, "r");
            // 文件写入开始位置指针移动到已经下载位置
            randomAccessFile.seek(localSize);
            byte[] buffer = new byte[1024 * 1024];
            int len = -1;
            while ((len = randomAccessFile.read(buffer)) != -1) {
                localSize += len;
            }
            log.info("startIndex={} endLocalIndex={}", startIndex, localSize);
            return localSize;
        } catch (Exception e) {
            log.error(e.getMessage(), e);
        } finally {
            IoUtil.closeStream(randomAccessFile, fileInputStream);
        }
        return -1;
    }

}
