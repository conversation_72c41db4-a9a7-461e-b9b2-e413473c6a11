package com.pukka.iptv.downloader.task.process;


import com.pukka.iptv.downloader.model.*;
import com.pukka.iptv.downloader.mq.config.QueueConfig;
import com.pukka.iptv.downloader.mq.model.*;
import com.pukka.iptv.downloader.mq.pool.MqUtil;
import com.pukka.iptv.downloader.task.SpringMQListener;
import com.pukka.iptv.downloader.task.downloader.AbstractDownloader;
import com.pukka.iptv.downloader.task.multiplyhandler.MultiplyTaskHandler;
import com.pukka.iptv.downloader.util.SpringUtils;
import com.rabbitmq.client.Channel;
import lombok.extern.slf4j.Slf4j;
import org.springframework.amqp.core.Message;
import org.springframework.amqp.rabbit.core.RabbitTemplate;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Component;

import java.nio.charset.StandardCharsets;
import java.util.concurrent.TimeUnit;

/**
 * @Auther: wz
 * @Date: 2021/10/15 11:04
 * @Description: 异步下载任务执行处理器
 */
@Slf4j
@Component
@Order(2)
public class AsyncDownloadProcess {

    @Autowired
    private SpringMQListener springMQListener;
    @Autowired
    private MultiplyTaskHandler taskHandler;


    public boolean download(MsgTask msgTask, FileTask fileTask) {
        //前置简单处理
        downloadPreProcess(msgTask, fileTask);
        //下载处理器
        try {
            downloadProcess(msgTask, fileTask);
        } catch (InterruptedException e) {
            throw new RuntimeException(e);
        }
        return true;
    }

    //任务前置处理
    private void downloadPreProcess(MsgTask msgTask, FileTask task) {
        //重试次数+1
        task.setRetryCount(task.getRetryCount() + 1);
        //剔除地址中的文件名
        // task.setTargetFTPUrl(filterFilename(task.getTargetFTPUrl()));
        //获取当前节点的nacos唯一id
        String instanceId = springMQListener.getDNode().getInstanceId();
        if (instanceId != null) {
            //设置当前处理消息的节点 (路由key)
            task.setTaskServerInstanceId(instanceId);
        } else {
            //  listener.nackTask(msgTask);
        }
    }


    //下载任务处理器
    private void downloadProcess(MsgTask msgTask, FileTask fileTask) throws InterruptedException {
        QueueChannel queueChannel = msgTask.getQueueChannel();
        Channel channel = queueChannel.channelWeakReference().get();
        long deliveryTag = msgTask.getDeliveryTag();
        //创建下载任务
        DownloadTask task = AbstractDownloader.generalTask(fileTask);
        //任务执行完毕的 对消息进行ack的回调方法
        task.setFinishedNotify(t ->
        {
            boolean ack = MqUtil.ack(channel, deliveryTag);
            log.info("下载完成进行ack，ack状态：{}，filecode：{}", ack, fileTask.getFileCode());
        });
        //设置后置处理器
        //添加下载任务
        log.info("下载器接收消息，filecode:{} mqchannel:{} deliveryTag:{}", fileTask.getFileCode(),
                channel.getChannelNumber(), deliveryTag);
        try {
            if (!taskHandler.submitTask(task)) {
                //添加失败，退回任务
                TimeUnit.MILLISECONDS.sleep(500);
                MqUtil.ack(channel, deliveryTag);
                backMsgToDispatcher(msgTask.getMsg(), fileTask.getRetryCount());
                //MqUtil.nack(channel, deliveryTag);
                log.warn("消息添加失败！nack channel num={} tag={}", msgTask.getQueueChannel(),
                        msgTask.getDeliveryTag());
            }
        } catch (Exception e) {
            log.error("下载任务添加失败，filecode:{},错误原因:{}", fileTask.getFileCode(),e.getMessage());
            MqUtil.ack(channel, deliveryTag);
            backMsgToDispatcher(msgTask.getMsg(), fileTask.getRetryCount());
            log.warn("消息添加失败！nack channel num={} tag={}", msgTask.getQueueChannel(),
                    msgTask.getDeliveryTag());
        }
    }

    //将消息退回给调度器
    private void backMsgToDispatcher(String msg, int retryCount) {
        RabbitTemplate template = SpringUtils.getBean(RabbitTemplate.class);
        Message sendMsg = new Message(msg.getBytes(StandardCharsets.UTF_8));
        Thread.yield();
        try {
            QueueConfig config = SpringUtils.getBean(QueueConfig.class);
            TimeUnit.MILLISECONDS.sleep(300);
            if (retryCount > 1) {
                log.info("下载器回退消息，回退给重试队列,消息体：{}", sendMsg);
                //回退给重试队列
                template.send(config.getRetryExchange(), config.getRetryRoutingKey(), sendMsg);
            } else {
                log.info("下载器回退消息，回退给待下载队列,消息体：{}", sendMsg);
                //回退给待下载队列
                template.send(config.getTaskExchange(), config.getTaskRoutingKey(), sendMsg);
            }
        } catch (InterruptedException e) {
            throw new RuntimeException(e);
        }
    }

    //获取每个下载器节点的任务数量
    public int getAllDownloaderQueueLen() {
        //out 获取所有 内置的下载器
        //由于队列是共享的，所以找其中一个下载器即可
        //log.info("getAliveTaskCount={}", aliveTaskCount);
        return taskHandler.getAliveTaskCount();
    }

}
