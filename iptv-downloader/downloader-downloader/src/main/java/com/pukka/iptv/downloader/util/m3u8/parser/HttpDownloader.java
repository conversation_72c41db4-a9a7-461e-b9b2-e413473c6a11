package com.pukka.iptv.downloader.util.m3u8.parser;

import cn.hutool.core.lang.Assert;
import com.pukka.iptv.downloader.model.ConnectHead;
import com.pukka.iptv.downloader.util.HttpDownLoderUtil;

import java.io.File;

/**
 * @Author: wz
 * @Date: 2021/11/13 18:00
 * @Description: http下载器
 */
class HttpDownloader extends M3u8AbsDownloader {
    private final static HttpDownloader instance = new HttpDownloader();

    public static HttpDownloader getInstance() {
        return instance;
    }


    @Override
    public boolean downloadFile(String url, File file) throws Exception {
        Assert.notNull(file);
        Assert.notNull(url);
        //获取头部信息
        ConnectHead head = M3u8Parser.getCurrentHead();
        HttpDownLoderUtil.getInstance().httpDownFile(head, url, file);
        return true;
    }
}
