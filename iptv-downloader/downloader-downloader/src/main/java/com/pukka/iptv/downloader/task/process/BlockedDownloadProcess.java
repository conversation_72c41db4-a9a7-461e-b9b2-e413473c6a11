package com.pukka.iptv.downloader.task.process;


import com.alibaba.fastjson.JSON;
import com.pukka.iptv.downloader.model.DownloadTask;
import com.pukka.iptv.downloader.model.FileTask;
import com.pukka.iptv.downloader.model.resp.DownloadNotifyResp;
import com.pukka.iptv.downloader.task.downloader.*;
import com.pukka.iptv.downloader.task.process.post.FtpPostProcess;
import com.pukka.iptv.downloader.task.process.post.HttpPostProcess;
import com.pukka.iptv.downloader.task.process.post.M3u8PostProcess;
import com.pukka.iptv.downloader.task.process.post.TaskFailedProcess;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Component;


/**
 * @Auther: wz
 * @Date: 2021/10/15 11:04
 * @Description: 阻塞下载任务执行处理器
 */
@Slf4j
@Component
@Order(2)
public class BlockedDownloadProcess {

    @Autowired
    private M3u8PostProcess m3u8PostProcess;
    @Autowired
    private FtpPostProcess ftpPostProcess;
    @Autowired
    private HttpPostProcess httpPostProcess;
    @Autowired
    private TaskFailedProcess taskFailedProcess;

    //同步下载m3u8 不干扰内部下载线程，采用调用者的线程进行下载
    public DownloadNotifyResp download(FileTask fileTask) {
        //创建下载任务
        DownloadTask task = AbstractDownloader.generalTask(fileTask);
        //选择下载器进行下载
        Downloader<DownloadTask> downloader = AbstractDownloader.selectDownloader(task);
        //任务完成ack 此处的ack可以有很多种处理
        // 1.只要下载到本地完毕就ack 2.不仅下载完毕，而且后置处理都处理完毕
        assert downloader != null;
        downloader.preHandler(task);
        log.info("同步下载前置处理完成，文件信息：{}，", JSON.toJSONString(fileTask));
        try {
            downloader.download(task);
            log.info("同步下载完成，文件信息：{}，", JSON.toJSONString(fileTask));
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return taskFailedProcess.handler(task, false);
        }
        //后置处理器
        if (M3u8Downloader.class.equals(downloader.getClass())) {
            log.info("m3u8文件同步下载完成，进行后置处理，文件源路径：{}，文件目标路径：{}",fileTask.getSourceUrl(),fileTask.getTargetUrl());
            return m3u8PostProcess.handler(task, false);
        } else if (FtpDownloader.class.equals(downloader.getClass())) {
            log.info("ftp文件同步下载完成，进行后置处理，文件源路径：{}，文件目标路径：{}",fileTask.getSourceUrl(),fileTask.getTargetUrl());

            return ftpPostProcess.handler(task, false);
        }else if (HttpDownloader.class.equals(downloader.getClass())){
            log.info("http文件同步下载完成，进行后置处理，文件源路径：{}，文件目标路径：{}",fileTask.getSourceUrl(),fileTask.getTargetUrl());

            return httpPostProcess.handler(task,false);
        }
        return null;
    }


}
