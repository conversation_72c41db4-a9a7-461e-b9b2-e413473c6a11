package com.pukka.iptv.downloader.task;

import cn.hutool.core.date.SystemClock;
import cn.hutool.core.thread.ThreadUtil;
import com.alibaba.fastjson.JSON;
import com.pukka.iptv.downloader.config.NodeConfig;
import com.pukka.iptv.downloader.model.DNode;
import com.pukka.iptv.downloader.model.Downloading;
import com.pukka.iptv.downloader.model.FileTask;
import com.pukka.iptv.downloader.mq.model.MsgTask;
import com.pukka.iptv.downloader.mq.model.QueueChannel;
import com.pukka.iptv.downloader.mq.model.QueueInfo;
import com.pukka.iptv.downloader.mq.pool.MqUtil;
import com.pukka.iptv.downloader.nacos.listener.NacosNotify;
import com.pukka.iptv.downloader.task.process.AsyncDownloadProcess;
import com.pukka.iptv.downloader.threadpool.ThreadUtils;
import com.pukka.iptv.downloader.util.SpringUtils;
import com.rabbitmq.client.Channel;
import lombok.extern.slf4j.Slf4j;
import org.springframework.amqp.core.AcknowledgeMode;
import org.springframework.amqp.core.MessageProperties;
import org.springframework.amqp.rabbit.connection.CachingConnectionFactory;
import org.springframework.amqp.rabbit.core.RabbitAdmin;
import org.springframework.amqp.rabbit.listener.DirectMessageListenerContainer;
import org.springframework.amqp.rabbit.listener.api.ChannelAwareMessageListener;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Bean;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import java.lang.ref.WeakReference;
import java.nio.charset.StandardCharsets;
import java.util.Optional;
import java.util.concurrent.ScheduledFuture;
import java.util.concurrent.ScheduledThreadPoolExecutor;
import java.util.concurrent.TimeUnit;

/**
 * @Author: wz
 * @Date: 2022/1/4 20:54
 * @Description: 根据配置动态创建或销毁mq监听
 */
@Slf4j
@Component
public class SpringMQListener implements NacosNotify {
    @Resource(name = "downloaderScheduleThreadPool")
    private ScheduledThreadPoolExecutor downloaderScheduleThreadPool;
    @Autowired
    private AutoNode autoDNode;
    @Autowired
    private NodeConfig nodeConfig;
    @Autowired
    private AsyncDownloadProcess asyncDownloadProcess;
    @Autowired
    private CachingConnectionFactory mqConnection;
    @Autowired
    private RabbitAdmin rabbitAdmin;

    private static volatile DNode node;
    private volatile boolean hasListener = false;
    private WeakReference<DirectMessageListenerContainer> weakReference;

    private volatile ScheduledFuture<?> scheduledFuture = null;

    private volatile ScheduledFuture<?> tmpIndexScheduleFuture = null;

    private final static long BALANCE_TIMEOUT = 60000L;//配置调整超时时间30s


    //下载节点初始化
    @PostConstruct
    private void init() {
        //主线程定时执行 每秒执行一次
        if (nodeConfig.isEnable()) {
            loopSchedule();
        }
        tmpIndexSchedule();
    }


    @Override//NACOS 配置改变进行处理
    public void configRefreshEvent() {
        if (nodeConfig.isEnable()) {
            //根据配置调整当前key对应的连接池限制
            loopSchedule();
        }
        tmpIndexSchedule();
    }

    //定时执行
    private void loopSchedule() {
        if (ThreadUtils.isNullOrDone(scheduledFuture)) {
            scheduledFuture = downloaderScheduleThreadPool.scheduleAtFixedRate(() -> {
                loopHandlerWithTimeout(BALANCE_TIMEOUT);
            }, 1, 2000, TimeUnit.MILLISECONDS);
        }
    }

    //带超时时间的初始化下载节点
    private boolean loopInitNodeWithTimeout(long timeout) {
        long time = SystemClock.now();
        //此处做超时等待处理，可能存在nacos注册有延时问题
        while (getConsumerQueue() == null && notTimeout(time, timeout)) {
            ThreadUtil.sleep(500);
        }
        if (getConsumerQueue() == null) {
            log.error("生成下载节点失败！请更新nacos配置触发 节点自动生成{}", getDNode());
            return false;
        }
        return true;
    }

    //监听mq消息并执行下载任务
    public void loopHandlerWithTimeout(long timeout) {
        if (!nodeConfig.isEnable()) {
            clearListener();
            return;
        }
        if (loopInitNodeWithTimeout(timeout)) {
            if (!hasListener && beanReady) {//&&beanReady
                listen(getConsumerQueue());
            }
        }
    }

    private volatile boolean beanReady = false;

    @Bean("beanListener")
    public DirectMessageListenerContainer beanListener() {
        DirectMessageListenerContainer container = new DirectMessageListenerContainer();
        container.setConnectionFactory(mqConnection);
        container.setAmqpAdmin(rabbitAdmin);
        //ack超时时间
        container.setAckTimeout(3 * 60 * 6000L);//3h
        //心跳检测时间
        container.setIdleEventInterval(2000L);
        //设置为手动ack
        container.setAcknowledgeMode(AcknowledgeMode.MANUAL);
        beanReady = true;
        log.info("beanListener初始化完成");
        return container;
    }

    private void listen(QueueInfo queue) {
        if (queue == null) return;
        log.info("queue info={}", queue.queue());
        DirectMessageListenerContainer container = SpringUtils.getBean("beanListener");
        log.info("开始监听下载队列：{}",queue.queue());
        container.setMessageListener((ChannelAwareMessageListener) (message, channel) -> {
            MessageProperties properties = message.getMessageProperties();
            long deliveryTag = properties.getDeliveryTag();
            //如果下载节点被关闭，直接nack
            if (!nodeConfig.isEnable()) {
                nack(channel, deliveryTag);
                return;
            }
            MsgTask msgTask = new MsgTask();
            QueueChannel queueChannel = new QueueChannel(queue, channel);
            msgTask.setQueueChannel(queueChannel);
            msgTask.setDeliveryTag(deliveryTag);

            msgTask.setMsg(new String(message.getBody(), StandardCharsets.UTF_8));
            //解析数据
            FileTask fileTask = parseMsg(msgTask);
            //开启下载
            asyncDownloadProcess.download(msgTask, fileTask);
        });
        container.addQueueNames(queue.queue());
        container.setErrorHandler(t -> log.error(t.getMessage(), t));
        // hasListener = container.isActive();
        hasListener = true;
        if (hasListener) {
            weakReference = new WeakReference<>(container);
        }
    }

    private void clearListener() {
        DirectMessageListenerContainer container = weakReference.get();
        if (container != null) {
            String[] queueNames = container.getQueueNames();
            for (String queueName : queueNames) {
                container.removeQueueNames(queueName);
            }
            log.info("下载队列监听线程以清除完毕");
        }
        hasListener = false;
    }

    //解析msg
    private FileTask parseMsg(MsgTask msgTask) {
        String msg = msgTask.getMsg();
        return Optional.ofNullable(JSON.parseObject(msg, FileTask.class)).orElseThrow(() -> {
            log.error("RabbitMQ消息解析失败-{}", msg);
            return new RuntimeException("RabbitMQ消息解析失败");
        });
    }

    private boolean notTimeout(long begin, long timeout) {
        return SystemClock.now() - begin < timeout;
    }

    public void nack(Channel channel, Long deliveryTag) {
        try {
            //防止频繁nack造成资源浪费
            Thread.sleep(500);
        } catch (Exception e) {
            log.error(e.getMessage(), e);
        }
        //节点没有初始化，需要退回消息
        MqUtil.nack(channel, deliveryTag);
    }

    //初始化当前下载节点
    public DNode getDNode() {
        return node == null ? node = autoDNode.defaultDownloadNode() : node;
    }

    //获取下载器的监听队列
    private QueueInfo getConsumerQueue() {
        //消费者不需要知道交换机和路由
        DNode dNode = getDNode();
        return dNode != null ? dNode.getQueueInfo().exchange(null).routeKey(null) : null;
    }

    //临时下载文件的 定时清理器
    private void tmpIndexSchedule() {
        // log.info("getTmpIndexTimeCron={}", nodeConfig.getTmpIndexTimeCron());
        log.info("【tmpIndexSchedule】设置清理临时下载文件的周期时间={}", nodeConfig.getTmpIndexLiveTime());
        Long liveTime = nodeConfig.getTmpIndexLiveTime();
        if (tmpIndexScheduleFuture != null) {
            ThreadUtils.cancelSchedule(tmpIndexScheduleFuture);
        }
        tmpIndexScheduleFuture = downloaderScheduleThreadPool.scheduleAtFixedRate(() -> Downloading.clearTmpIndex(liveTime), 1, liveTime, TimeUnit.MILLISECONDS);
    }

}
