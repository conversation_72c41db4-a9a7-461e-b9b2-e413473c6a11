package com.pukka.iptv.downloader.util;

import cn.hutool.core.lang.Assert;
import cn.hutool.core.thread.ThreadUtil;
import com.pukka.iptv.downloader.config.HttpClient;
import com.pukka.iptv.downloader.model.ConnectHead;
import com.pukka.iptv.downloader.model.HttpInfo;
import com.pukka.iptv.downloader.model.Proxy;
import com.pukka.iptv.downloader.threadpool.ThreadUtils;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.io.FileUtils;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.http.HttpRange;
import org.springframework.http.MediaType;
import org.springframework.http.client.ClientHttpRequest;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.util.FileCopyUtils;
import org.springframework.util.StringUtils;
import org.springframework.web.client.RequestCallback;
import org.springframework.web.client.RestTemplate;

import java.io.*;
import java.net.HttpURLConnection;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.concurrent.Callable;
import java.util.concurrent.CountDownLatch;
import java.util.concurrent.Future;
import java.util.concurrent.FutureTask;

/**
 * @Author: wangbo
 * @Date: 2022/4/6 16:58
 */
@Slf4j
public class HttpDownLoderUtil {


    private static volatile HttpDownLoderUtil httpDownLoderUtil;

    public static HttpDownLoderUtil getInstance() {
        if (httpDownLoderUtil == null) {
            synchronized (HttpDownLoderUtil.class) {
                if (httpDownLoderUtil == null) {
                    httpDownLoderUtil = new HttpDownLoderUtil();
                }
            }
        }
        return httpDownLoderUtil;
    }

    //返回http客户端
    private static RestTemplate getHttp() {
        return HttpClient.getRestHttp();
    }

    //返回带代理的http客户端
    private static RestTemplate getProxyHttp(Proxy proxy) {
        return HttpClient.getProxyHttp(proxy);
    }

    public boolean httpDownFile(ConnectHead head, String url, File file) throws Exception {
        Proxy proxy = head.proxy();
        RestTemplate httpClient = proxy == null ? getHttp() : getProxyHttp(proxy);
        Assert.notNull(httpClient);
        RequestCallback callback = request -> request.getHeaders()
                .setAccept(Arrays.asList(MediaType.APPLICATION_OCTET_STREAM, MediaType.ALL));
        FileOutputStream outputStream = null;
        try {
            outputStream = new FileOutputStream(file);
            FileOutputStream finalout = outputStream;
            httpClient.execute(url, HttpMethod.GET, callback, resp -> {
                FileCopyUtils.copy(resp.getBody(), finalout);
                //log.info("下载完成:{}", resp.getStatusCode());
                return true;
            });
        } finally {
            IoUtil.closeStream(outputStream);
        }
        return true;
    }

    private static RestTemplate getHttpClient(ConnectHead head) {
        Proxy proxy = head.proxy();
        return proxy == null ? getHttp() : getProxyHttp(proxy);
    }


}
