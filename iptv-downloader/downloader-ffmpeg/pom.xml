<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <artifactId>iptv-downloader</artifactId>
        <groupId>com.pukka.iptv</groupId>
        <version>1.0.0</version>
    </parent>
    <modelVersion>4.0.0</modelVersion>

    <artifactId>downloader-ffmpeg</artifactId>
    <description>基于ffmpeg工具的多媒体解析器</description>
    <properties>
        <maven.compiler.source>8</maven.compiler.source>
        <maven.compiler.target>8</maven.compiler.target>
    </properties>


    <build>
        <resources>
            <resource>

                <!--  排除window和mac工具 -->
                <directory>src/main/resources/bin</directory>
                <excludes>
                    <exclude>*</exclude>
                    <!--                    <exclude>**/*.exe</exclude>-->
                    <!--                    <exclude>**/pthreadGC2.dll</exclude>-->
                    <!--                    <exclude>**/ffmpeg-mac</exclude>-->
                    <!--                    <exclude>**/ffprobe</exclude>-->
                </excludes>
                <filtering>true</filtering>
            </resource>
        </resources>
    </build>
</project>