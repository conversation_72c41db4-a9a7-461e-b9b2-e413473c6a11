package com.pukka.iptv.downloader.mq.pool;

import cn.hutool.core.thread.ThreadFactoryBuilder;
import com.rabbitmq.client.*;
import lombok.extern.slf4j.Slf4j;

import java.io.IOException;
import java.nio.charset.StandardCharsets;
import java.util.concurrent.*;

/**
 * @Auther: wz
 * @Date: 2022/6/15 17:02
 * @Description:
 */
@Slf4j
public class Test {
    private static ConnectionFactory factory;

    static {
        initConnectFactory();
    }

    private static void initConnectFactory() {

        try {
            log.info("初始化mq连接池");
            factory = new ConnectionFactory();
            factory.setHost("localhost");
            factory.setPort(5672);
            factory.setUsername("downloader");
            factory.setPassword("downloader");
            factory.setVirtualHost("download");

            factory.setRequestedChannelMax(0);
            factory.setRequestedFrameMax(0);
            factory.setAutomaticRecoveryEnabled(true);
            factory.setNetworkRecoveryInterval(10000); //attempt recovery every 10 seconds
            factory.setConnectionTimeout(1000 * 30);
            // 设置心跳为60秒
            factory.setRequestedHeartbeat(60);

            //支持自动清理的线程池 超过60s没有使用就会被回收
            ThreadFactory threadFactory = ThreadFactoryBuilder.create()
                    .setDaemon(false).setNamePrefix("bbq-test-mq-").build();
            ExecutorService executorService = Executors.newCachedThreadPool();
            factory.setSharedExecutor(executorService);
            factory.setThreadFactory(threadFactory);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }


    public static void main(String[] args) throws IOException, TimeoutException, InterruptedException {
        Connection connection = factory.newConnection();
        Channel channel1 = connection.createChannel();
        Channel channel2 = connection.createChannel();
        GetResponse response = channel1.basicGet("downloader_execute_10_0_9_68_7002_DEFAULT_iptv__downloaderserver", false);


        GetResponse response2 = channel1.basicGet("downloader_task_queue", false);
        if (response2 != null) {
            String data = new String(response2.getBody(), StandardCharsets.UTF_8);
            log.info("get mq msg={}", data);
            Envelope envelope = response2.getEnvelope();
            MqUtil.ack(channel1, envelope.getDeliveryTag());
        }

        if (response != null) {
            String data = new String(response.getBody(), StandardCharsets.UTF_8);
            log.info("get mq msg={}", data);
            Envelope envelope = response.getEnvelope();
            MqUtil.ack(channel1, envelope.getDeliveryTag());
        }

        TimeUnit.SECONDS.sleep(5);
        log.info("关闭channel1");
        channel1.close();

        while (true) {
            TimeUnit.SECONDS.sleep(2000);
        }
    }
}
