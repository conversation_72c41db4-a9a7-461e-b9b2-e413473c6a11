package com.pukka.iptv.downloader.mq.pool;

import com.pukka.iptv.downloader.util.SpringUtils;
import com.rabbitmq.client.Channel;
import lombok.extern.slf4j.Slf4j;
import org.springframework.amqp.rabbit.connection.ConnectionFactory;
import org.springframework.amqp.rabbit.core.RabbitTemplate;



/**
 * @Author: wz
 * @Date: 2021/10/19 15:19
 * @Description: mq连接类
 */
@Slf4j
public class MqConnection {

    private static volatile org.springframework.amqp.rabbit.connection.Connection mqConnection;
    private static volatile Channel weakChannel;
    private final static Object lock = new Object();

    public static org.springframework.amqp.rabbit.connection.Connection getMqConnection() {
        if (mqConnection == null || !mqConnection.isOpen()) {
            synchronized (lock) {
                if (mqConnection == null) {
                    RabbitTemplate template = SpringUtils.getBean(RabbitTemplate.class);
                    ConnectionFactory connectionFactory = template.getConnectionFactory();
                    mqConnection = connectionFactory.createConnection();
                }
            }
        }
        return mqConnection;
    }

    private static Channel _getChannel(Channel channel) {
        try {
            if (channel == null || !channel.isOpen()) {
                synchronized (lock) {
                    if (channel == null) {
                        org.springframework.amqp.rabbit.connection.Connection connection = getMqConnection();
                        channel = connection.createChannel(false);
//                        weakChannel = new WeakReference<>(channel);
                        weakChannel = channel;
                    }
                }
            }
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            closeMqConnection();
        }
        return channel;
    }

    public static Channel getChannel() {
        if (weakChannel == null) {
            return _getChannel(null);
        } else {
//            return _getChannel(weakChannel.get());
            return _getChannel(weakChannel);
        }
    }

    public static void closeMqConnection() {
        try {
            weakChannel.close();
            mqConnection.close();
        } catch (Exception e) {
            log.error(e.getMessage(), e);
        }
        weakChannel = null;
        mqConnection = null;
    }

}
