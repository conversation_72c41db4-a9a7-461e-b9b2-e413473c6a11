package com.pukka.iptv.basic.plugins.repository.seriesMonitor.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.pukka.iptv.common.api.feign.bms.BmsContentFeignClient;
import com.pukka.iptv.common.api.feign.bms.BmsProgramFeignClient;
import com.pukka.iptv.common.base.vo.CommonResponse;
import com.pukka.iptv.common.data.model.bms.BmsContent;
import com.pukka.iptv.common.data.model.bms.BmsProgram;
import com.pukka.iptv.common.data.vo.CodeList;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;
import org.springframework.test.util.ReflectionTestUtils;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;

import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

/**
 * @projectName: iptv-cloud
 * @package: com.pukka.iptv.basic.plugins.repository.seriesMonitor.service
 * @className: CompensationScannerTest
 * @author: chiron
 * @description: 补偿扫描服务测试类
 * @date: 2025/1/16 15:45
 * @version: 1.0
 */
@RunWith(MockitoJUnitRunner.class)
public class CompensationScannerTest {

    @Mock
    private BmsContentFeignClient bmsContentFeignClient;

    @Mock
    private BmsProgramFeignClient bmsProgramFeignClient;

    @InjectMocks
    private CompensationScanner compensationScanner;

    private Page<BmsContent> contentPage;
    private BmsContent content1;
    private BmsContent content2;
    private List<BmsProgram> programs;

    @Before
    public void setUp() {
        // 初始化测试数据
        content1 = new BmsContent();
        content1.setId(1L);
        content1.setCmsContentCode("SERIES001");
        content1.setSpId(1L);
        content1.setSubRemind(0);

        content2 = new BmsContent();
        content2.setId(2L);
        content2.setCmsContentCode("SERIES002");
        content2.setSpId(1L);
        content2.setSubRemind(-1);

        // 创建50个content来模拟满页
        List<BmsContent> contentList = new ArrayList<>();
        contentList.add(content1);
        contentList.add(content2);
        // 添加48个额外的content来达到50个（PAGE_SIZE）
        for (int i = 3; i <= 50; i++) {
            BmsContent extraContent = new BmsContent();
            extraContent.setId((long) i);
            extraContent.setCmsContentCode("SERIES" + String.format("%03d", i));
            extraContent.setSpId(1L);
            extraContent.setSubRemind(0);
            contentList.add(extraContent);
        }

        contentPage = new Page<>();
        contentPage.setRecords(contentList);
        contentPage.setSize(50);
        contentPage.setTotal(50);

        // 初始化子集数据
        BmsProgram program1 = new BmsProgram();
        program1.setId(1L);
        program1.setPublishStatus(3); // 成功状态

        BmsProgram program2 = new BmsProgram();
        program2.setId(2L);
        program2.setPublishStatus(4); // 失败状态

        programs = Arrays.asList(program1, program2);
    }

    @Test
    public void testScanAndFixSeriesStatus_Success() {
        // Given
        // 设置content1需要更新（有失败子集但未标记为-1）
        content1.setSubRemind(0);
        // 设置content2需要更新（没有失败子集但标记为-1）
        content2.setSubRemind(-1);

        // 为content1设置失败的子集
        BmsProgram failedProgram = new BmsProgram();
        failedProgram.setId(1L);
        failedProgram.setPublishStatus(4); // 失败状态

        // 为content2设置成功的子集
        BmsProgram successProgram = new BmsProgram();
        successProgram.setId(2L);
        successProgram.setPublishStatus(3); // 成功状态

        when(bmsContentFeignClient.listContentByStatusAndType(anyList(), anyList(), eq(1), eq(50)))
                .thenReturn(CommonResponse.success(contentPage));

        // 第二页返回空数据，结束循环
        Page<BmsContent> emptyPage = new Page<>();
        emptyPage.setRecords(Collections.emptyList());
        when(bmsContentFeignClient.listContentByStatusAndType(anyList(), anyList(), eq(2), eq(50)))
                .thenReturn(CommonResponse.success(emptyPage));

        // 为所有content设置默认的成功programs（不需要更新）
        when(bmsProgramFeignClient.getByProgramId(any(CodeList.class)))
                .thenReturn(CommonResponse.success(Arrays.asList(successProgram)));

        // 为特定的content设置不同的programs
        when(bmsProgramFeignClient.getByProgramId(argThat(codeList ->
                codeList != null && codeList.getCodes() != null && codeList.getCodes().contains("SERIES001"))))
                .thenReturn(CommonResponse.success(Arrays.asList(failedProgram)));
        when(bmsProgramFeignClient.getByProgramId(argThat(codeList ->
                codeList != null && codeList.getCodes() != null && codeList.getCodes().contains("SERIES002"))))
                .thenReturn(CommonResponse.success(Arrays.asList(successProgram)));

        when(bmsContentFeignClient.updateById(any(BmsContent.class)))
                .thenReturn(CommonResponse.success(true));

        // When
        compensationScanner.scanAndFixSeriesStatus();

        // Then
        // 实际会调用2次：第一页有数据，第二页为空
        verify(bmsContentFeignClient, times(2)).listContentByStatusAndType(anyList(), anyList(), anyInt(), anyInt());
        // 每个content都会调用一次getByProgramId，第一页有50个content
        verify(bmsProgramFeignClient, times(50)).getByProgramId(any(CodeList.class));
        verify(bmsContentFeignClient, times(2)).updateById(any(BmsContent.class));
    }

    @Test
    public void testScanAndFixSeriesStatus_WithFailedPrograms() {
        // Given
        BmsProgram failedProgram = new BmsProgram();
        failedProgram.setId(1L);
        failedProgram.setPublishStatus(4); // 失败状态

        List<BmsProgram> failedPrograms = Arrays.asList(failedProgram);

        when(bmsContentFeignClient.listContentByStatusAndType(anyList(), anyList(), eq(1), eq(50)))
                .thenReturn(CommonResponse.success(contentPage));

        Page<BmsContent> emptyPage = new Page<>();
        emptyPage.setRecords(Collections.emptyList());
        when(bmsContentFeignClient.listContentByStatusAndType(anyList(), anyList(), eq(2), eq(50)))
                .thenReturn(CommonResponse.success(emptyPage));

        when(bmsProgramFeignClient.getByProgramId(any(CodeList.class)))
                .thenReturn(CommonResponse.success(failedPrograms));

        when(bmsContentFeignClient.updateById(any(BmsContent.class)))
                .thenReturn(CommonResponse.success(true));

        // When
        compensationScanner.scanAndFixSeriesStatus();

        // Then
        verify(bmsContentFeignClient, times(1))
                .updateById(argThat(content -> content.getId().equals(1L) && content.getSubRemind().equals(-1)));
    }

    @Test
    public void testScanAndFixSeriesStatus_WithSuccessPrograms() {
        // Given
        BmsProgram successProgram = new BmsProgram();
        successProgram.setId(1L);
        successProgram.setPublishStatus(3); // 成功状态

        List<BmsProgram> successPrograms = Arrays.asList(successProgram);

        content1.setSubRemind(-1); // 当前标记为失败，但实际没有失败子集
        content2.setSubRemind(-1); // 当前标记为失败，但实际没有失败子集

        when(bmsContentFeignClient.listContentByStatusAndType(anyList(), anyList(), eq(1), eq(50)))
                .thenReturn(CommonResponse.success(contentPage));

        Page<BmsContent> emptyPage = new Page<>();
        emptyPage.setRecords(Collections.emptyList());
        when(bmsContentFeignClient.listContentByStatusAndType(anyList(), anyList(), eq(2), eq(50)))
                .thenReturn(CommonResponse.success(emptyPage));

        when(bmsProgramFeignClient.getByProgramId(any(CodeList.class)))
                .thenReturn(CommonResponse.success(successPrograms));

        when(bmsContentFeignClient.updateById(any(BmsContent.class)))
                .thenReturn(CommonResponse.success(true));

        // When
        compensationScanner.scanAndFixSeriesStatus();

        // Then
        verify(bmsContentFeignClient, times(2)).updateById(argThat(content -> content.getSubRemind().equals(0)));
    }

    @Test
    public void testScanAndFixSeriesStatus_NoUpdateNeeded() {
        // Given
        BmsProgram successProgram = new BmsProgram();
        successProgram.setId(1L);
        successProgram.setPublishStatus(3); // 成功状态

        List<BmsProgram> successPrograms = Arrays.asList(successProgram);

        content1.setSubRemind(0); // 状态已经正确
        content2.setSubRemind(0); // 状态已经正确

        when(bmsContentFeignClient.listContentByStatusAndType(anyList(), anyList(), eq(1), eq(50)))
                .thenReturn(CommonResponse.success(contentPage));

        Page<BmsContent> emptyPage = new Page<>();
        emptyPage.setRecords(Collections.emptyList());
        when(bmsContentFeignClient.listContentByStatusAndType(anyList(), anyList(), eq(2), eq(50)))
                .thenReturn(CommonResponse.success(emptyPage));

        when(bmsProgramFeignClient.getByProgramId(any(CodeList.class)))
                .thenReturn(CommonResponse.success(successPrograms));

        // When
        compensationScanner.scanAndFixSeriesStatus();

        // Then
        verify(bmsContentFeignClient, never()).updateById(any(BmsContent.class));
    }

    @Test
    public void testScanAndFixSeriesStatus_GetContentDataFailed() {
        // Given
        when(bmsContentFeignClient.listContentByStatusAndType(anyList(), anyList(), eq(1), eq(50)))
                .thenReturn(null);

        // When
        compensationScanner.scanAndFixSeriesStatus();

        // Then
        verify(bmsProgramFeignClient, never()).getByProgramId(any(CodeList.class));
        verify(bmsContentFeignClient, never()).updateById(any(BmsContent.class));
    }

    @Test
    public void testScanAndFixSeriesStatus_GetProgramDataFailed() {
        // Given
        when(bmsContentFeignClient.listContentByStatusAndType(anyList(), anyList(), eq(1), eq(50)))
                .thenReturn(CommonResponse.success(contentPage));

        Page<BmsContent> emptyPage = new Page<>();
        emptyPage.setRecords(Collections.emptyList());
        when(bmsContentFeignClient.listContentByStatusAndType(anyList(), anyList(), eq(2), eq(50)))
                .thenReturn(CommonResponse.success(emptyPage));

        when(bmsProgramFeignClient.getByProgramId(any(CodeList.class)))
                .thenReturn(null);

        // When
        compensationScanner.scanAndFixSeriesStatus();

        // Then
        verify(bmsContentFeignClient, never()).updateById(any(BmsContent.class));
    }

    @Test
    public void testScanAndFixSeriesStatus_UpdateFailed() {
        // Given
        // 设置两个content都需要更新
        content1.setSubRemind(0); // 需要更新为-1
        content2.setSubRemind(-1); // 需要更新为0

        when(bmsContentFeignClient.listContentByStatusAndType(anyList(), anyList(), eq(1), eq(50)))
                .thenReturn(CommonResponse.success(contentPage));

        Page<BmsContent> emptyPage = new Page<>();
        emptyPage.setRecords(Collections.emptyList());
        when(bmsContentFeignClient.listContentByStatusAndType(anyList(), anyList(), eq(2), eq(50)))
                .thenReturn(CommonResponse.success(emptyPage));

        // 为不同的content设置不同的programs
        BmsProgram failedProgram = new BmsProgram();
        failedProgram.setId(1L);
        failedProgram.setPublishStatus(4); // 失败状态

        BmsProgram successProgram = new BmsProgram();
        successProgram.setId(2L);
        successProgram.setPublishStatus(3); // 成功状态

        when(bmsProgramFeignClient.getByProgramId(argThat(codeList ->
                codeList != null && codeList.getCodes() != null && codeList.getCodes().contains("SERIES001"))))
                .thenReturn(CommonResponse.success(Arrays.asList(failedProgram)));
        when(bmsProgramFeignClient.getByProgramId(argThat(codeList ->
                codeList != null && codeList.getCodes() != null && codeList.getCodes().contains("SERIES002"))))
                .thenReturn(CommonResponse.success(Arrays.asList(successProgram)));

        // 模拟更新失败
        when(bmsContentFeignClient.updateById(any(BmsContent.class)))
                .thenReturn(CommonResponse.success(false));

        // When
        compensationScanner.scanAndFixSeriesStatus();

        // Then
        verify(bmsContentFeignClient, times(2)).updateById(any(BmsContent.class));
    }

    @Test
    public void testScanAndFixSeriesStatus_ProcessException() {
        // Given
        when(bmsContentFeignClient.listContentByStatusAndType(anyList(), anyList(), eq(1), eq(50)))
                .thenReturn(CommonResponse.success(contentPage));

        when(bmsProgramFeignClient.getByProgramId(any(CodeList.class)))
                .thenThrow(new RuntimeException("Test exception"));

        // When
        compensationScanner.scanAndFixSeriesStatus();

        // Then
        verify(bmsContentFeignClient, never()).updateById(any(BmsContent.class));
    }

    @Test
    public void testScanAndFixSeriesStatus_EmptyFirstPage() {
        // Given
        Page<BmsContent> emptyPage = new Page<>();
        emptyPage.setRecords(Collections.emptyList());

        when(bmsContentFeignClient.listContentByStatusAndType(anyList(), anyList(), eq(1), eq(50)))
                .thenReturn(CommonResponse.success(emptyPage));

        // When
        compensationScanner.scanAndFixSeriesStatus();

        // Then
        verify(bmsProgramFeignClient, never()).getByProgramId(any(CodeList.class));
        verify(bmsContentFeignClient, never()).updateById(any(BmsContent.class));
    }

    @Test
    public void testScanAndFixSeriesStatus_LargeDataSet() {
        // Given - 模拟大数据集，超过一页
        // 设置需要更新的状态
        content1.setSubRemind(0); // 需要更新为-1
        content2.setSubRemind(-1); // 需要更新为0

        // 创建第一页：50个content（满页）
        List<BmsContent> page1Contents = new ArrayList<>();
        for (int i = 1; i <= 50; i++) {
            BmsContent content = new BmsContent();
            content.setId((long) i);
            content.setCmsContentCode("SERIES" + String.format("%03d", i));
            content.setSpId(1L);
            content.setSubRemind(0);
            page1Contents.add(content);
        }
        Page<BmsContent> page1 = new Page<>();
        page1.setRecords(page1Contents);
        page1.setSize(50);

        // 创建第二页：30个content（不满页，触发结束）
        List<BmsContent> page2Contents = new ArrayList<>();
        for (int i = 51; i <= 80; i++) {
            BmsContent content = new BmsContent();
            content.setId((long) i);
            content.setCmsContentCode("SERIES" + String.format("%03d", i));
            content.setSpId(1L);
            content.setSubRemind(0);
            page2Contents.add(content);
        }
        Page<BmsContent> page2 = new Page<>();
        page2.setRecords(page2Contents);
        page2.setSize(50);

        when(bmsContentFeignClient.listContentByStatusAndType(anyList(), anyList(), eq(1), eq(50)))
                .thenReturn(CommonResponse.success(page1));
        when(bmsContentFeignClient.listContentByStatusAndType(anyList(), anyList(), eq(2), eq(50)))
                .thenReturn(CommonResponse.success(page2));

        // 为所有content设置成功的programs
        BmsProgram successProgram = new BmsProgram();
        successProgram.setId(1L);
        successProgram.setPublishStatus(3); // 成功状态

        when(bmsProgramFeignClient.getByProgramId(any(CodeList.class)))
                .thenReturn(CommonResponse.success(Arrays.asList(successProgram)));

        // When
        compensationScanner.scanAndFixSeriesStatus();

        // Then
        // 实际会调用2次：第一页50个content，第二页30个content（不满页，结束）
        verify(bmsContentFeignClient, times(2)).listContentByStatusAndType(anyList(), anyList(), anyInt(), anyInt());
        // 第一页50个content + 第二页30个content = 80次调用
        verify(bmsProgramFeignClient, times(80)).getByProgramId(any(CodeList.class));
    }

    @Test
    public void testScanAndFixSeriesStatus_SubRemindNull() {
        // Given
        content1.setSubRemind(null); // subRemind为null，有失败子集时需要更新为-1
        content2.setSubRemind(null); // subRemind为null，没有失败子集时需要更新为0

        BmsProgram failedProgram = new BmsProgram();
        failedProgram.setId(1L);
        failedProgram.setPublishStatus(4); // 失败状态

        BmsProgram successProgram = new BmsProgram();
        successProgram.setId(2L);
        successProgram.setPublishStatus(3); // 成功状态

        when(bmsContentFeignClient.listContentByStatusAndType(anyList(), anyList(), eq(1), eq(50)))
                .thenReturn(CommonResponse.success(contentPage));

        Page<BmsContent> emptyPage = new Page<>();
        emptyPage.setRecords(Collections.emptyList());
        when(bmsContentFeignClient.listContentByStatusAndType(anyList(), anyList(), eq(2), eq(50)))
                .thenReturn(CommonResponse.success(emptyPage));

        // 根据不同的content返回不同的programs
        when(bmsProgramFeignClient.getByProgramId(argThat(codeList ->
                codeList != null && codeList.getCodes() != null && codeList.getCodes().contains("SERIES001"))))
                .thenReturn(CommonResponse.success(Arrays.asList(failedProgram)));
        when(bmsProgramFeignClient.getByProgramId(argThat(codeList ->
                codeList != null && codeList.getCodes() != null && codeList.getCodes().contains("SERIES002"))))
                .thenReturn(CommonResponse.success(Arrays.asList(successProgram)));

        when(bmsContentFeignClient.updateById(any(BmsContent.class)))
                .thenReturn(CommonResponse.success(true));

        // When
        compensationScanner.scanAndFixSeriesStatus();

        // Then
        verify(bmsContentFeignClient, times(2)).updateById(any(BmsContent.class));
    }

    @Test
    public void testScheduledCompensationScan() {
        // Given
        ReflectionTestUtils.setField(compensationScanner, "bmsContentFeignClient", bmsContentFeignClient);
        ReflectionTestUtils.setField(compensationScanner, "bmsProgramFeignClient", bmsProgramFeignClient);

        Page<BmsContent> emptyPage = new Page<>();
        emptyPage.setRecords(Collections.emptyList());

        when(bmsContentFeignClient.listContentByStatusAndType(anyList(), anyList(), eq(1), eq(50)))
                .thenReturn(CommonResponse.success(emptyPage));

        // When
        compensationScanner.scheduledCompensationScan();

        // Then
        verify(bmsContentFeignClient, times(1)).listContentByStatusAndType(anyList(), anyList(), anyInt(), anyInt());
    }
}