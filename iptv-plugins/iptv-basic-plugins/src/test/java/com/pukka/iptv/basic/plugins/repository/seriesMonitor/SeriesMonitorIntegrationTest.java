package com.pukka.iptv.basic.plugins.repository.seriesMonitor;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.pukka.iptv.basic.plugins.repository.seriesMonitor.service.CompensationScanner;
import com.pukka.iptv.basic.plugins.repository.seriesMonitor.service.ProgramStatusAnalyzer;
import com.pukka.iptv.common.api.feign.bms.BmsContentFeignClient;
import com.pukka.iptv.common.api.feign.bms.BmsProgramFeignClient;
import com.pukka.iptv.common.base.enums.ResultStatusEnum;
import com.pukka.iptv.common.base.vo.CommonResponse;
import com.pukka.iptv.common.data.dto.PublishParamsDto;
import com.pukka.iptv.common.data.model.bms.BmsContent;
import com.pukka.iptv.common.data.model.bms.BmsProgram;
import com.pukka.iptv.common.data.vo.CodeList;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

import java.util.Arrays;
import java.util.Collections;
import java.util.List;

import static org.junit.Assert.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

/**
 * @projectName: iptv-cloud
 * @package: com.pukka.iptv.basic.plugins.repository.seriesMonitor
 * @className: SeriesMonitorIntegrationTest
 * @author: chiron
 * @description: 剧集监控模块集成测试类 - 使用Mock方式避免Spring上下文加载问题
 * @date: 2025/1/16 16:30
 * @version: 1.0
 */
@RunWith(MockitoJUnitRunner.class)
public class SeriesMonitorIntegrationTest {

    @InjectMocks
    private ProgramStatusAnalyzer programStatusAnalyzer;

    @Mock
    private CompensationScanner compensationScanner;

    @Mock
    private BmsProgramFeignClient bmsProgramFeignClient;

    @Mock
    private BmsContentFeignClient bmsContentFeignClient;

    private PublishParamsDto publishParams;
    private BmsProgram program;
    private BmsContent content;
    private List<BmsProgram> programs;

    @Before
    public void setUp() {
        // 初始化测试数据
        publishParams = new PublishParamsDto();
        publishParams.setContentId(1L);
        publishParams.setResult(ResultStatusEnum.FAIL.getCode());

        program = new BmsProgram();
        program.setId(1L);
        program.setCmsSeriesCode("SERIES001");
        program.setSpId(1L);
        program.setPublishStatus(4); // 失败状态

        content = new BmsContent();
        content.setId(2L);
        content.setCmsContentCode("SERIES001");
        content.setSpId(1L);
        content.setSubRemind(0);

        programs = Arrays.asList(program);
    }

    @Test
    public void testEndToEndFailureStatusProcessing() {
        // Given
        when(bmsProgramFeignClient.getById(1L))
                .thenReturn(CommonResponse.success(program));
        when(bmsContentFeignClient.getByCode(1L, "SERIES001"))
                .thenReturn(CommonResponse.success(content));
        when(bmsContentFeignClient.updateById(any(BmsContent.class)))
                .thenReturn(CommonResponse.success(true));

        // When
        boolean result = programStatusAnalyzer.processStatusChange(publishParams);

        // Then
        assertTrue(result);
        verify(bmsContentFeignClient, times(1))
                .updateById(argThat(c -> c.getId().equals(2L) && c.getSubRemind().equals(-1)));
    }

    @Test
    public void testEndToEndSuccessStatusProcessing() {
        // Given
        publishParams.setResult(ResultStatusEnum.SUCCESS.getCode());

        // 设置剧头初始状态为-1（有失败子集），这样检测到没有失败子集时会更新为0
        content.setSubRemind(-1);

        BmsProgram successProgram = new BmsProgram();
        successProgram.setId(1L);
        successProgram.setPublishStatus(3); // 成功状态

        when(bmsProgramFeignClient.getById(1L))
                .thenReturn(CommonResponse.success(program));
        when(bmsContentFeignClient.getByCode(1L, "SERIES001"))
                .thenReturn(CommonResponse.success(content));
        when(bmsProgramFeignClient.getByProgramId(any(CodeList.class)))
                .thenReturn(CommonResponse.success(Arrays.asList(successProgram)));
        when(bmsContentFeignClient.updateById(any(BmsContent.class)))
                .thenReturn(CommonResponse.success(true));

        // When
        boolean result = programStatusAnalyzer.processStatusChange(publishParams);

        // Then
        assertTrue(result);
        verify(bmsContentFeignClient, times(1))
                .updateById(argThat(c -> c.getId().equals(2L) && c.getSubRemind().equals(0)));
    }

    @Test
    public void testCompensationScannerIntegration() {
        // Given - 使用Mock的CompensationScanner，避免DateUtils依赖问题
        doNothing().when(compensationScanner).scanAndFixSeriesStatus();

        // When
        compensationScanner.scanAndFixSeriesStatus();

        // Then
        verify(compensationScanner, times(1)).scanAndFixSeriesStatus();
    }

    @Test
    public void testBothServicesWorkingTogether() {
        // Given - 模拟程序状态分析器处理失败状态
        when(bmsProgramFeignClient.getById(1L))
                .thenReturn(CommonResponse.success(program));
        when(bmsContentFeignClient.getByCode(1L, "SERIES001"))
                .thenReturn(CommonResponse.success(content));
        when(bmsContentFeignClient.updateById(any(BmsContent.class)))
                .thenReturn(CommonResponse.success(true));

        // When - 首先处理状态变更
        boolean analyzerResult = programStatusAnalyzer.processStatusChange(publishParams);

        // 然后模拟补偿扫描器执行
        doNothing().when(compensationScanner).scanAndFixSeriesStatus();
        compensationScanner.scanAndFixSeriesStatus();

        // Then
        assertTrue(analyzerResult);
        verify(compensationScanner, times(1)).scanAndFixSeriesStatus();
        verify(bmsContentFeignClient, times(1)).updateById(any(BmsContent.class));
    }

    @Test
    public void testErrorHandlingIntegration() {
        // Given - 模拟各种错误情况
        when(bmsProgramFeignClient.getById(1L))
                .thenThrow(new RuntimeException("Network error"));

        // When
        boolean result = programStatusAnalyzer.processStatusChange(publishParams);

        // Then
        assertFalse(result);
        verify(bmsContentFeignClient, never()).updateById(any(BmsContent.class));
    }

    @Test
    public void testNullDataHandlingIntegration() {
        // Given
        when(bmsProgramFeignClient.getById(1L))
                .thenReturn(CommonResponse.success(null));

        // When
        boolean result = programStatusAnalyzer.processStatusChange(publishParams);

        // Then
        assertFalse(result);
        verify(bmsContentFeignClient, never()).updateById(any(BmsContent.class));
    }

    @Test
    public void testPerformanceUnderLoad() throws InterruptedException {
        // Given
        when(bmsProgramFeignClient.getById(anyLong()))
                .thenReturn(CommonResponse.success(program));
        when(bmsContentFeignClient.getByCode(anyLong(), anyString()))
                .thenReturn(CommonResponse.success(content));
        when(bmsContentFeignClient.updateById(any(BmsContent.class)))
                .thenReturn(CommonResponse.success(true));

        // When - 模拟并发处理
        int threadCount = 10;
        Thread[] threads = new Thread[threadCount];
        boolean[] results = new boolean[threadCount];

        for (int i = 0; i < threadCount; i++) {
            final int index = i;
            threads[i] = new Thread(() -> {
                PublishParamsDto params = new PublishParamsDto();
                params.setContentId((long) (index + 1));
                params.setResult(ResultStatusEnum.FAIL.getCode());
                results[index] = programStatusAnalyzer.processStatusChange(params);
            });
        }

        // 启动所有线程
        for (Thread thread : threads) {
            thread.start();
        }

        // 等待所有线程完成
        for (Thread thread : threads) {
            thread.join();
        }

        // Then
        for (boolean result : results) {
            assertTrue(result);
        }
        verify(bmsContentFeignClient, times(threadCount)).updateById(any(BmsContent.class));
    }
}