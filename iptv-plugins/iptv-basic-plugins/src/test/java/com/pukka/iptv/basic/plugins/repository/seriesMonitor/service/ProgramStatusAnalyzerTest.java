package com.pukka.iptv.basic.plugins.repository.seriesMonitor.service;

import com.pukka.iptv.common.api.feign.bms.BmsContentFeignClient;
import com.pukka.iptv.common.api.feign.bms.BmsProgramFeignClient;
import com.pukka.iptv.common.base.enums.ResultStatusEnum;
import com.pukka.iptv.common.base.vo.CommonResponse;
import com.pukka.iptv.common.data.dto.PublishParamsDto;
import com.pukka.iptv.common.data.model.bms.BmsContent;
import com.pukka.iptv.common.data.model.bms.BmsProgram;
import com.pukka.iptv.common.data.vo.CodeList;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

import java.util.Arrays;
import java.util.List;

import static org.junit.Assert.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

/**
 * @projectName: iptv-cloud
 * @package: com.pukka.iptv.basic.plugins.repository.seriesMonitor.service
 * @className: ProgramStatusAnalyzerTest
 * @author: chiron
 * @description: 节目状态分析服务测试类
 * @date: 2025/1/16 15:30
 * @version: 1.0
 */
@RunWith(MockitoJUnitRunner.class)
public class ProgramStatusAnalyzerTest {

    @Mock
    private BmsProgramFeignClient bmsProgramFeignClient;

    @Mock
    private BmsContentFeignClient bmsContentFeignClient;

    @InjectMocks
    private ProgramStatusAnalyzer programStatusAnalyzer;

    private PublishParamsDto params;
    private BmsProgram program;
    private BmsContent series;

    @Before
    public void setUp() {
        // 初始化测试数据
        params = new PublishParamsDto();
        params.setContentId(123L);
        params.setResult(ResultStatusEnum.FAIL.getCode());

        program = new BmsProgram();
        program.setId(123L);
        program.setCmsSeriesCode("SERIES001");
        program.setSpId(1L);

        series = new BmsContent();
        series.setId(456L);
        series.setCmsContentCode("SERIES001");
        series.setSubRemind(0);
    }

    @Test
    public void testProcessStatusChange_Success_FailureStatus() {
        // Given
        when(bmsProgramFeignClient.getById(123L))
                .thenReturn(CommonResponse.success(program));
        when(bmsContentFeignClient.getByCode(1L, "SERIES001"))
                .thenReturn(CommonResponse.success(series));
        when(bmsContentFeignClient.updateById(any(BmsContent.class)))
                .thenReturn(CommonResponse.success(true));

        // When
        boolean result = programStatusAnalyzer.processStatusChange(params);

        // Then
        assertTrue(result);
        verify(bmsProgramFeignClient, times(1)).getById(123L);
        verify(bmsContentFeignClient, times(1)).getByCode(1L, "SERIES001");
        verify(bmsContentFeignClient, times(1))
                .updateById(argThat(content -> content.getId().equals(456L) && content.getSubRemind().equals(-1)));
    }

    @Test
    public void testProcessStatusChange_Success_SuccessStatus() {
        // Given
        params.setResult(ResultStatusEnum.SUCCESS.getCode());
        series.setSubRemind(-1); // 设置初始状态为-1，这样检测到没有失败子集时会更新为0
        List<BmsProgram> programs = Arrays.asList(
                createProgram(1L, 3), // 成功状态
                createProgram(2L, 5) // 成功状态
        );

        when(bmsProgramFeignClient.getById(123L))
                .thenReturn(CommonResponse.success(program));
        when(bmsContentFeignClient.getByCode(1L, "SERIES001"))
                .thenReturn(CommonResponse.success(series));
        when(bmsProgramFeignClient.getByProgramId(any(CodeList.class)))
                .thenReturn(CommonResponse.success(programs));
        when(bmsContentFeignClient.updateById(any(BmsContent.class)))
                .thenReturn(CommonResponse.success(true));

        // When
        boolean result = programStatusAnalyzer.processStatusChange(params);

        // Then
        assertTrue(result);
        verify(bmsContentFeignClient, times(1))
                .updateById(argThat(content -> content.getId().equals(456L) && content.getSubRemind().equals(0)));
    }

    @Test
    public void testProcessStatusChange_HasFailedPrograms() {
        // Given
        params.setResult(ResultStatusEnum.SUCCESS.getCode());
        List<BmsProgram> programs = Arrays.asList(
                createProgram(1L, 3), // 成功状态
                createProgram(2L, 4) // 失败状态
        );

        when(bmsProgramFeignClient.getById(123L))
                .thenReturn(CommonResponse.success(program));
        when(bmsContentFeignClient.getByCode(1L, "SERIES001"))
                .thenReturn(CommonResponse.success(series));
        when(bmsProgramFeignClient.getByProgramId(any(CodeList.class)))
                .thenReturn(CommonResponse.success(programs));
        when(bmsContentFeignClient.updateById(any(BmsContent.class)))
                .thenReturn(CommonResponse.success(true));

        // When
        boolean result = programStatusAnalyzer.processStatusChange(params);

        // Then
        assertTrue(result);
        verify(bmsContentFeignClient, times(1))
                .updateById(argThat(content -> content.getId().equals(456L) && content.getSubRemind().equals(-1)));
    }

    @Test
    public void testProcessStatusChange_NullParams() {
        // Given
        params.setContentId(null);

        // When
        boolean result = programStatusAnalyzer.processStatusChange(params);

        // Then
        assertFalse(result);
        verifyNoInteractions(bmsProgramFeignClient);
        verifyNoInteractions(bmsContentFeignClient);
    }

    @Test
    public void testProcessStatusChange_ProgramInfoNotFound() {
        // Given
        when(bmsProgramFeignClient.getById(123L))
                .thenReturn(CommonResponse.success(null));

        // When
        boolean result = programStatusAnalyzer.processStatusChange(params);

        // Then
        assertFalse(result);
        verify(bmsProgramFeignClient, times(1)).getById(123L);
        verifyNoInteractions(bmsContentFeignClient);
    }

    @Test
    public void testProcessStatusChange_SeriesInfoNotFound() {
        // Given
        when(bmsProgramFeignClient.getById(123L))
                .thenReturn(CommonResponse.success(program));
        when(bmsContentFeignClient.getByCode(1L, "SERIES001"))
                .thenReturn(CommonResponse.success(null));

        // When
        boolean result = programStatusAnalyzer.processStatusChange(params);

        // Then
        assertFalse(result);
        verify(bmsProgramFeignClient, times(1)).getById(123L);
        verify(bmsContentFeignClient, times(1)).getByCode(1L, "SERIES001");
    }

    @Test
    public void testProcessStatusChange_UpdateFailure() {
        // Given
        when(bmsProgramFeignClient.getById(123L))
                .thenReturn(CommonResponse.success(program));
        when(bmsContentFeignClient.getByCode(1L, "SERIES001"))
                .thenReturn(CommonResponse.success(series));
        when(bmsContentFeignClient.updateById(any(BmsContent.class)))
                .thenReturn(CommonResponse.success(false));

        // When
        boolean result = programStatusAnalyzer.processStatusChange(params);

        // Then
        assertFalse(result);
        verify(bmsContentFeignClient, times(1)).updateById(any(BmsContent.class));
    }

    @Test
    public void testProcessStatusChange_SeriesAlreadyMarkedAsFailed() {
        // Given
        series.setSubRemind(-1); // 已经标记为失败
        when(bmsProgramFeignClient.getById(123L))
                .thenReturn(CommonResponse.success(program));
        when(bmsContentFeignClient.getByCode(1L, "SERIES001"))
                .thenReturn(CommonResponse.success(series));

        // When
        boolean result = programStatusAnalyzer.processStatusChange(params);

        // Then
        assertTrue(result);
        verify(bmsContentFeignClient, never()).updateById(any(BmsContent.class));
    }

    @Test
    public void testProcessStatusChange_FeignClientException() {
        // Given
        when(bmsProgramFeignClient.getById(123L))
                .thenThrow(new RuntimeException("Feign client error"));

        // When
        boolean result = programStatusAnalyzer.processStatusChange(params);

        // Then
        assertFalse(result);
    }

    @Test
    public void testProcessStatusChange_CheckProgramsFailure() {
        // Given
        params.setResult(ResultStatusEnum.SUCCESS.getCode());
        when(bmsProgramFeignClient.getById(123L))
                .thenReturn(CommonResponse.success(program));
        when(bmsContentFeignClient.getByCode(1L, "SERIES001"))
                .thenReturn(CommonResponse.success(series));
        when(bmsProgramFeignClient.getByProgramId(any(CodeList.class)))
                .thenReturn(CommonResponse.success(null));

        // When
        boolean result = programStatusAnalyzer.processStatusChange(params);

        // Then
        assertFalse(result);
    }

    @Test
    public void testProcessStatusChange_SeriesAlreadyCorrectStatus() {
        // Given
        params.setResult(ResultStatusEnum.SUCCESS.getCode());
        series.setSubRemind(0); // 状态已经是0
        List<BmsProgram> programs = Arrays.asList(
                createProgram(1L, 3), // 成功状态
                createProgram(2L, 5) // 成功状态
        );

        when(bmsProgramFeignClient.getById(123L))
                .thenReturn(CommonResponse.success(program));
        when(bmsContentFeignClient.getByCode(1L, "SERIES001"))
                .thenReturn(CommonResponse.success(series));
        when(bmsProgramFeignClient.getByProgramId(any(CodeList.class)))
                .thenReturn(CommonResponse.success(programs));

        // When
        boolean result = programStatusAnalyzer.processStatusChange(params);

        // Then
        assertTrue(result);
        verify(bmsContentFeignClient, never()).updateById(any(BmsContent.class));
    }

    @Test
    public void testProcessStatusChange_NullSeriesCode() {
        // Given
        program.setCmsSeriesCode(null);
        when(bmsProgramFeignClient.getById(123L))
                .thenReturn(CommonResponse.success(program));

        // When
        boolean result = programStatusAnalyzer.processStatusChange(params);

        // Then
        assertFalse(result);
        verifyNoInteractions(bmsContentFeignClient);
    }

    @Test
    public void testProcessStatusChange_NullSpId() {
        // Given
        program.setSpId(null);
        when(bmsProgramFeignClient.getById(123L))
                .thenReturn(CommonResponse.success(program));

        // When
        boolean result = programStatusAnalyzer.processStatusChange(params);

        // Then
        assertFalse(result);
        verifyNoInteractions(bmsContentFeignClient);
    }

    @Test
    public void testProcessStatusChange_MultipleFailedStatuses() {
        // Given
        params.setResult(ResultStatusEnum.SUCCESS.getCode());
        List<BmsProgram> programs = Arrays.asList(
                createProgram(1L, 4), // 失败状态
                createProgram(2L, 7), // 失败状态
                createProgram(3L, 10) // 失败状态
        );

        when(bmsProgramFeignClient.getById(123L))
                .thenReturn(CommonResponse.success(program));
        when(bmsContentFeignClient.getByCode(1L, "SERIES001"))
                .thenReturn(CommonResponse.success(series));
        when(bmsProgramFeignClient.getByProgramId(any(CodeList.class)))
                .thenReturn(CommonResponse.success(programs));
        when(bmsContentFeignClient.updateById(any(BmsContent.class)))
                .thenReturn(CommonResponse.success(true));

        // When
        boolean result = programStatusAnalyzer.processStatusChange(params);

        // Then
        assertTrue(result);
        verify(bmsContentFeignClient, times(1))
                .updateById(argThat(content -> content.getId().equals(456L) && content.getSubRemind().equals(-1)));
    }

    /**
     * 创建测试用的BmsProgram对象
     */
    private BmsProgram createProgram(Long id, Integer publishStatus) {
        BmsProgram program = new BmsProgram();
        program.setId(id);
        program.setPublishStatus(publishStatus);
        return program;
    }
}