package com.pukka.iptv.basic.plugins.rabbitmq.listener;

import com.pukka.iptv.basic.plugins.repository.seriesMonitor.service.ProgramStatusAnalyzer;
import com.pukka.iptv.common.data.dto.PublishParamsDto;
import com.rabbitmq.client.Channel;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.*;

/**
 * @projectName: iptv-cloud
 * @package: com.pukka.iptv.basic.plugins.rabbitmq.listener
 * @className: SeriesMonitorListenerTest
 * @author: chiron
 * @description: 剧集监控消息监听器测试类
 * @date: 2025/1/16 16:15
 * @version: 1.0
 */
@RunWith(MockitoJUnitRunner.class)
public class SeriesMonitorListenerTest {

    @Mock
    private ProgramStatusAnalyzer programStatusAnalyzer;

    @Mock
    private Channel channel;

    @InjectMocks
    private SeriesMonitorListener seriesMonitorListener;

    private PublishParamsDto params;
    private Long deliveryTag = 123456L;

    @Before
    public void setUp() {
        params = new PublishParamsDto();
        params.setContentId(1L);
        params.setContentType(2); // 子集类型
        params.setStatus(1);
        params.setResult(1);
        params.setIsFinish(false);
    }

    @Test
    public void testSeriesMonitorReceived_Success() throws Exception {
        // Given
        when(programStatusAnalyzer.processStatusChange(any(PublishParamsDto.class)))
                .thenReturn(true);

        // When
        seriesMonitorListener.seriesMonitorReceived(params, channel, deliveryTag);

        // Then
        verify(programStatusAnalyzer, times(1)).processStatusChange(params);
        verify(channel, times(1)).basicAck(deliveryTag, false);
    }

    @Test
    public void testSeriesMonitorReceived_ProcessFailure() throws Exception {
        // Given
        when(programStatusAnalyzer.processStatusChange(any(PublishParamsDto.class)))
                .thenReturn(false);

        // When
        seriesMonitorListener.seriesMonitorReceived(params, channel, deliveryTag);

        // Then
        verify(programStatusAnalyzer, times(1)).processStatusChange(params);
        verify(channel, times(1)).basicAck(deliveryTag, false);
    }

    @Test
    public void testSeriesMonitorReceived_NullParams() throws Exception {
        // When
        seriesMonitorListener.seriesMonitorReceived(null, channel, deliveryTag);

        // Then
        verify(programStatusAnalyzer, never()).processStatusChange(any(PublishParamsDto.class));
        verify(channel, times(1)).basicAck(deliveryTag, false);
    }

    @Test
    public void testSeriesMonitorReceived_IsFinishedTrue() throws Exception {
        // Given
        params.setIsFinish(true);

        // When
        seriesMonitorListener.seriesMonitorReceived(params, channel, deliveryTag);

        // Then
        verify(programStatusAnalyzer, never()).processStatusChange(any(PublishParamsDto.class));
        verify(channel, times(1)).basicAck(deliveryTag, false);
    }

    @Test
    public void testSeriesMonitorReceived_NotSubsetContentType() throws Exception {
        // Given
        params.setContentType(1); // 非子集类型

        // When
        seriesMonitorListener.seriesMonitorReceived(params, channel, deliveryTag);

        // Then
        verify(programStatusAnalyzer, never()).processStatusChange(any(PublishParamsDto.class));
        verify(channel, times(1)).basicAck(deliveryTag, false);
    }

    @Test
    public void testSeriesMonitorReceived_NullContentType() throws Exception {
        // Given
        params.setContentType(null);

        // When
        seriesMonitorListener.seriesMonitorReceived(params, channel, deliveryTag);

        // Then
        verify(programStatusAnalyzer, never()).processStatusChange(any(PublishParamsDto.class));
        verify(channel, times(1)).basicAck(deliveryTag, false);
    }

    @Test
    public void testSeriesMonitorReceived_ProcessException() throws Exception {
        // Given
        when(programStatusAnalyzer.processStatusChange(any(PublishParamsDto.class)))
                .thenThrow(new RuntimeException("Processing error"));

        // When
        seriesMonitorListener.seriesMonitorReceived(params, channel, deliveryTag);

        // Then
        verify(programStatusAnalyzer, times(1)).processStatusChange(params);
        verify(channel, times(1)).basicAck(deliveryTag, false);
    }

    @Test(expected = RuntimeException.class)
    public void testSeriesMonitorReceived_ChannelException() throws Exception {
        // Given
        when(programStatusAnalyzer.processStatusChange(any(PublishParamsDto.class)))
                .thenReturn(true);
        doThrow(new RuntimeException("Channel error")).when(channel).basicAck(deliveryTag, false);

        // When - 这里会抛出异常
        seriesMonitorListener.seriesMonitorReceived(params, channel, deliveryTag);

        // Then - 由于异常，这些验证不会执行
    }

    @Test
    public void testSeriesMonitorReceived_MultipleContentTypes() throws Exception {
        // Given
        when(programStatusAnalyzer.processStatusChange(any(PublishParamsDto.class)))
                .thenReturn(true);

        // Test contentType = 2 (should process)
        params.setContentType(2);
        seriesMonitorListener.seriesMonitorReceived(params, channel, deliveryTag);
        verify(programStatusAnalyzer, times(1)).processStatusChange(params);
        reset(programStatusAnalyzer);

        // Test contentType = 3 (should not process)
        params.setContentType(3);
        seriesMonitorListener.seriesMonitorReceived(params, channel, deliveryTag);
        verify(programStatusAnalyzer, never()).processStatusChange(any(PublishParamsDto.class));
    }

    @Test
    public void testSeriesMonitorReceived_CompleteWorkflow() throws Exception {
        // Given
        PublishParamsDto validParams = new PublishParamsDto();
        validParams.setContentId(100L);
        validParams.setContentType(2);
        validParams.setStatus(5);
        validParams.setResult(1);
        validParams.setIsFinish(false);

        when(programStatusAnalyzer.processStatusChange(eq(validParams)))
                .thenReturn(true);

        // When
        seriesMonitorListener.seriesMonitorReceived(validParams, channel, deliveryTag);

        // Then
        verify(programStatusAnalyzer, times(1)).processStatusChange(validParams);
        verify(channel, times(1)).basicAck(deliveryTag, false);
        verifyNoMoreInteractions(programStatusAnalyzer);
        verifyNoMoreInteractions(channel);
    }
}