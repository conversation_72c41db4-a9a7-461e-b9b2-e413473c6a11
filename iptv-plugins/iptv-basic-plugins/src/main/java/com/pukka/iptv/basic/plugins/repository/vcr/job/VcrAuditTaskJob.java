package com.pukka.iptv.basic.plugins.repository.vcr.job;

import com.pukka.iptv.common.core.util.SafeUtil;
import com.xxl.job.core.context.XxlJobHelper;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @Description TODO
 * @Date 2024/11/5 10:15
 * @Version V1.0
 **/
@Slf4j
@Component
public class VcrAuditTaskJob {

    private final VcrAuditTaskActuator vcrAuditTaskActuator;

    @Autowired
    public VcrAuditTaskJob(VcrAuditTaskActuator vcrAuditTaskActuator) {
        this.vcrAuditTaskActuator = vcrAuditTaskActuator;
    }


    /**
     * 创建智审任务
     * <p>
     * 此方法被标记为XXL-JOB的任务执行方法，任务名称为"createVcrAuditTask"。
     * 当该任务被触发时，此方法会被执行。
     * <p>
     * 方法流程：
     * 1. 记录日志，表示开始创建智审任务。
     * 2. 从XXL-JOB的任务参数中获取参数值，使用SafeUtil.getString方法确保获取到的参数为字符串类型。
     * 3. 记录日志，输出获取到的参数值。
     * 4. 调用vcrJobHandler的executeAuditTask方法，传入获取到的参数值，执行智审任务的创建。
     * 5. 如果在执行智审任务创建过程中发生异常，记录错误日志并输出异常信息。
     */
    @XxlJob("createVcrAuditTask")
    public void createVcrAuditTask() {
        log.debug("开始创建智审任务");
        String param = SafeUtil.getString(XxlJobHelper.getJobParam());
        log.debug("创建智审任务执行器 -----> param:{}", param);
        try {
            vcrAuditTaskActuator.executeAuditTask(param);
            log.info("智审任务执行成功");
        } catch (Exception exception) {
            log.error("智审任务执行失败，异常类型: {}, 异常信息: {}", exception.getClass().getSimpleName(), exception.getMessage());
        }
    }
}
