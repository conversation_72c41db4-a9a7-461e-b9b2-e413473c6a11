package com.pukka.iptv.basic.plugins.repository.dualScreen.service.impl;

import com.pukka.iptv.basic.plugins.repository.dualScreen.service.DualScreenInteractionService;
import com.pukka.iptv.basic.plugins.utils.OutOperateUtil;
import com.pukka.iptv.common.base.constant.RedisKeyConstants;
import com.pukka.iptv.common.core.util.SafeUtil;
import com.pukka.iptv.common.data.model.sys.SysSp;
import com.pukka.iptv.common.data.model.sys.SysStorage;
import com.pukka.iptv.common.redis.service.RedisService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Arrays;
import java.util.List;

/**
 * <AUTHOR>
 * @Description TODO
 * @Date 2024/12/9 10:05
 * @Version V1.0
 **/
@Service
@Slf4j
public class DualScreenInteractionServiceImpl implements DualScreenInteractionService {

    @Autowired
    private RedisService redisService;
    @Autowired
    private OutOperateUtil outOperateUtil;

    private final static String SPHD_PATH = "SPHD-Media/";

    @Override
    public String getFtpMediaUrlForOperator(String operator) {
        String result;
        String spIds = "ctcc".equals(operator) ? "69,67" : "57";
        List<String> spList = Arrays.asList(spIds.split(","));
        // 通过sp获取存储id
        SysSp sp = redisService.getCacheMapValue(RedisKeyConstants.SYS_SP, spList.get(0));
        SysStorage storage = redisService.getCacheMapValue(RedisKeyConstants.SYS_STORAGE_KEY,
                String.valueOf(sp.getStorageId()));
        // 生成上传fpt前缀
        String uploadFtp = outOperateUtil.getUploadFtp(storage);
        // 获取目标路径下最新的一个文件路径
        String sphdMadiaFile = SafeUtil.getNewFile(uploadFtp, SPHD_PATH + operator);
        if (StringUtils.isEmpty(sphdMadiaFile)) {
            result = "";
        } else {
            // 替换前置机地址
            result = outOperateUtil.getMediaPrefix(String.valueOf(storage.getId()), sphdMadiaFile, OutOperateUtil.OUT_PICTURE_FTP_PREFIX);
        }
        return result;
    }
}
