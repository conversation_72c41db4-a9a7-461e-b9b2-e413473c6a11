package com.pukka.iptv.basic.plugins.controller.vcr;

import com.pukka.iptv.basic.plugins.repository.vcr.service.VcrAuditTaskService;
import com.pukka.iptv.common.base.vo.CommonResponse;
import com.pukka.iptv.common.data.model.baidu.vcr.VcrAuditTaskEntity;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.Valid;

/**
 * <AUTHOR>
 * @Description TODO
 * @Date 2024/11/20 14:11
 * @Version V1.0
 **/
@Slf4j
@RestController
@AllArgsConstructor
@RequestMapping(value = "/baiduCloudIntelligent/vcrAuditTask", produces = MediaType.APPLICATION_JSON_VALUE)
@Api(tags = "百度云视频审核")
public class VcrAuditTaskController {
    @Autowired
    private VcrAuditTaskService vcrAuditTaskService;

    @ApiOperation(value = "更新审核主任务")
    @PutMapping("/update")
    public CommonResponse<Boolean> update(@Valid @RequestBody VcrAuditTaskEntity vcrAuditTask) {
        return CommonResponse.success(vcrAuditTaskService.updateById(vcrAuditTask));
    }
}
