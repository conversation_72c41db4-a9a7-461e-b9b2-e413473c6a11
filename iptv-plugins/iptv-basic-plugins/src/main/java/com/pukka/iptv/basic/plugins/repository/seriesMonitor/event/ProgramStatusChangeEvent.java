package com.pukka.iptv.basic.plugins.repository.seriesMonitor.event;


import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.Date;

/**
 * @projectName: iptv-cloud
 * @package: com.pukka.iptv.basic.plugins.repository.seriesMonitor.event
 * @className: ProgramStatusChangeEvent
 * @author: chiron
 * @description: 子集状态变更事件
 * @date: 2025/5/23 10:37
 * @version: 1.0
 */

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ProgramStatusChangeEvent implements Serializable {
    private static final long serialVersionUID = 1L;

    private Long programId;
    private String seriesCode;
    private String contentCode;
    private Long spId;
    private Integer newStatus;
    private Date changeTime;
}
