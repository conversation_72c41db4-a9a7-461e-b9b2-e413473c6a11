package com.pukka.iptv.basic.plugins.repository.dualScreen.job;

import com.pukka.iptv.common.api.feign.api.SPHDFeignClient;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @Description TODO
 * @Date 2024/12/6 11:16
 * @Version V1.0
 **/
@Slf4j
@Component
public class ValidMediaJob {
    @Autowired
    private SPHDFeignClient sphdFeignClient;

    @XxlJob("sphdValidMediaJobHandler")
    public void validMedia(){
        try {
            sphdFeignClient.exeValidMedia("cucc");
        } catch (Exception e) {
            log.error("cucc同步接口调用失败", e);
        }
        try {
            sphdFeignClient.exeValidMedia("ctcc");
        } catch (Exception e) {
            log.error("ctcc同步接口调用失败", e);
        }
    }
}
