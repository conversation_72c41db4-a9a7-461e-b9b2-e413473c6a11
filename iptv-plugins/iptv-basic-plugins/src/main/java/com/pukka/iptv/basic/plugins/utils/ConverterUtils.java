package com.pukka.iptv.basic.plugins.utils;

import com.pukka.iptv.common.base.enums.VcrAuditStatusEnums;
import com.pukka.iptv.common.data.model.baidu.vcr.VcrAuditSubTaskEntity;
import com.pukka.iptv.common.data.model.baidu.vcr.request.VcrAuditTaskRequest;
import lombok.extern.slf4j.Slf4j;

import java.util.Arrays;
import java.util.HashMap;
import java.util.Map;
import java.util.Optional;

/**
 * <AUTHOR>
 * @Description TODO
 * @Date 2024/11/7 10:51
 * @Version V1.0
 **/
@Slf4j
public class ConverterUtils {

    /**
     * 将VcrAuditSubTaskEntity实体转换为VcrAuditTaskRequest请求对象。
     *
     * @param subTaskEntity VcrAuditSubTaskEntity实体对象，包含子任务的相关信息。
     * @return 转换后的VcrAuditTaskRequest请求对象，包含转换后的任务请求信息。
     */
    public static VcrAuditTaskRequest convertToVcrAuditTaskRequest(VcrAuditSubTaskEntity subTaskEntity) {
        if (subTaskEntity == null) {
            throw new IllegalArgumentException("subTaskEntity 不能为空");
        }
        VcrAuditTaskRequest request = new VcrAuditTaskRequest();
        try {
            // 映射字段
            request.setSource(subTaskEntity.getSource());
            request.setPriority(subTaskEntity.getPriority());
            request.setPreset(subTaskEntity.getPreset());
            request.setDescription(subTaskEntity.getDescription());
            request.setNotification(subTaskEntity.getNotification());
            request.setNotificationType(subTaskEntity.getNotificationType());

        } catch (Exception e) {
            log.error("转换 VcrAuditSubTaskEntity 到 VcrAuditTaskRequest 失败", e);
        }
        return request;
    }

    /**
     * 构建HTTP请求体
     *
     * @param task 审核子任务实体对象
     * @return 包含请求参数的Map对象
     * <p>
     * 此方法根据传入的审核子任务实体对象构建HTTP请求体。请求体中包含以下参数：
     * - source: 审核任务的来源
     * - priority: 审核任务的优先级（可选）
     * - preset: 审核任务的预设配置（可选）
     * - description: 审核任务的描述（可选）
     * - notification: 审核任务完成后是否发送通知（可选）
     * - notificationType: 通知类型（可选）
     * <p>
     * 如果某个参数为null，则不会将其添加到请求体中。
     */
    public static Map<String, Object> convertToRequestBody(VcrAuditSubTaskEntity task) {
        Map<String, Object> requestBody = new HashMap<>();
        requestBody.put("source", task.getSource());
        addOptionalValue(task.getPriority(), "priority", requestBody);
        addOptionalValue(task.getPreset(), "preset", requestBody);
        addOptionalValue(task.getDescription(), "description", requestBody);
        addOptionalValue(task.getNotification(), "notification", requestBody);
        addOptionalValue(task.getNotificationType(), "notificationType", requestBody);
        return requestBody;
    }

    /**
     * 将可选值添加到映射中
     *
     * @param value 要添加到映射中的值，可能为null
     * @param key   映射中的键
     * @param map   存储键值对的映射
     *              <p>
     *              此方法用于将给定的值添加到指定的映射中，但仅当该值非null时才进行添加。
     *              如果给定的值为null，则不会向映射中添加任何内容。
     *              方法首先使用Optional.ofNullable将值包装为一个Optional对象，
     *              然后调用ifPresent方法检查值是否存在。
     *              如果值存在，则使用map.put方法将其添加到映射中，其中键为指定的key参数，值为给定的value参数。
     */
    private static void addOptionalValue(Object value, String key, Map<String, Object> map) {
        Optional.ofNullable(value).ifPresent(v -> map.put(key, v));
    }

    /**
     * 将审核状态转换为审核代码
     *
     * @param status 审核状态
     * @return 对应的审核代码
     * @throws IllegalArgumentException 如果提供的状态未知
     */
    public static int convertStatusToAuditCode(String status) {
        Optional<VcrAuditStatusEnums> matchingStatus = Arrays.stream(VcrAuditStatusEnums.values())
                .filter(auditStatus -> auditStatus.name().equalsIgnoreCase(status))
                .findFirst();

        return matchingStatus.map(VcrAuditStatusEnums::getValue)
                .orElseThrow(() -> new IllegalArgumentException("未知的状态: " + status));
    }
}
