package com.pukka.iptv.basic.plugins.repository.vcr.service.impl;

import com.baomidou.mybatisplus.core.toolkit.ObjectUtils;
import com.pukka.iptv.basic.plugins.config.VcrMediaAuditConfig;
import com.pukka.iptv.basic.plugins.repository.vcr.enums.VcrHttpMethodEnums;
import com.pukka.iptv.basic.plugins.repository.vcr.service.VcrAuditSubTaskService;
import com.pukka.iptv.basic.plugins.repository.vcr.service.VcrAuditTaskService;
import com.pukka.iptv.basic.plugins.repository.vcr.service.VcrService;
import com.pukka.iptv.basic.plugins.repository.vcr.webclient.WebClientApiFactory;
import com.pukka.iptv.basic.plugins.repository.vcr.webclient.WebClientRequest;
import com.pukka.iptv.basic.plugins.utils.ProcessUtils;
import com.pukka.iptv.common.data.model.baidu.vcr.VcrAuditSubTaskEntity;
import com.pukka.iptv.common.data.model.baidu.vcr.VcrAuditTransit;
import com.pukka.iptv.common.data.model.baidu.vcr.response.PresetResponse;
import com.pukka.iptv.common.data.model.baidu.vcr.response.VcrAuditTaskResponse;
import com.pukka.iptv.common.data.model.baidu.vcr.response.VcrTemplateResponse;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Collections;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @Description TODO
 * @Date 2024/10/22 14:19
 * @Version V1.0
 **/

@Slf4j
@Service
public class VcrServiceImpl implements VcrService {

    @Autowired
    private VcrMediaAuditConfig config;
    @Autowired
    private ProcessUtils processUtils;
    @Autowired
    private WebClientApiFactory webClientApiFactory;
    @Autowired
    private VcrAuditTaskService vcrAuditTaskService;
    @Autowired
    private VcrAuditSubTaskService vcrAuditSubTaskService;




    /**
     * 保存多个审核任务
     *
     * @param vcrAuditTransitList 要保存的审核任务实体列表
     * @return 如果所有审核任务保存成功，则返回true；否则返回false
     * @throws Exception 如果在保存审核任务过程中发生任何异常，将抛出此异常
     *                   <p>
     *                   此方法用于将一组审核任务实体保存到数据库或存储系统中。
     *                   它首先调用vcrAuditTaskService的saveAuditTaskBatch方法来执行实际的保存操作。
     *                   如果saveAuditTaskBatch方法返回的code为空，则认为保存失败，并记录错误日志。
     *                   如果在保存过程中发生任何异常，也会记录错误日志，并抛出异常。
     *                   如果所有审核任务成功保存，则返回true；否则返回false。
     */
    @Override
    public boolean save(List<VcrAuditTransit> vcrAuditTransitList) {
        return vcrAuditTaskService.saveAuditTaskBatch(vcrAuditTransitList);
    }

    /**
     * 取消审核任务
     *
     * @param taskId 要取消的审核任务的任务ID
     * @return 如果成功取消审核任务，则返回true；否则返回false
     * @throws Exception 如果在取消审核任务的过程中发生异常，则抛出此异常
     *                   <p>
     *                   此方法用于取消指定的审核任务。它首先根据配置和任务ID构造取消审核任务的URL，
     *                   然后通过vcrApiHandler调用对应的API接口来取消任务。
     *                   如果取消成功，则记录日志并返回true；
     *                   如果在取消过程中发生异常，则记录错误日志并返回false。
     */
    @Override
    public boolean cancelAuditTask(String taskId) {
        try {
            String cancelAuditUrl = String.format("%s/%s?cancel", config.getCancelAuditTask(), taskId);
            WebClientRequest request = WebClientRequest.builder()
                    .uri(cancelAuditUrl)
                    .method(VcrHttpMethodEnums.PUT)
                    .header("X-User-Name", config.getPlatformSymbol())
                    .build();
            webClientApiFactory.executeRequest(request, Object.class, response -> ObjectUtils.isNotEmpty(response) ? response : null);
            log.info("成功取消审核任务，任务ID: {}", taskId);
            return true;
        } catch (Exception e) {
            log.error("取消审核任务时发生错误, 任务ID: {}, 错误: {}", taskId, e.getMessage(), e);
            return false;
        }
    }

    /**
     * 根据任务ID查询审核任务状态
     *
     * @param taskId 审核任务的来源标识
     * @return 如果成功查询到审核任务，则返回true；否则返回false
     * @throws Exception 如果在查询审核任务的过程中发生异常，则抛出此异常
     *                   <p>
     *                   该方法根据提供的来源标识（source）来查询审核任务。
     *                   它首先根据配置和来源标识构造查询URL，
     *                   然后通过vcrApiHandler调用对应的API接口进行查询。
     *                   如果查询成功，将记录一条日志信息，并返回true；
     *                   如果在查询过程中发生异常，将记录一条错误信息，并返回false。
     */
    @Override
    public String getAuditTaskByTaskId(String taskId) {
        try {
            String url = String.format("%s/?taskId=%s", config.getGetAuditTaskByTaskId(), taskId);
            WebClientRequest request = WebClientRequest.builder()
                    .uri(url)
                    .method(VcrHttpMethodEnums.GET)
                    .header("X-User-Name", config.getPlatformSymbol())
                    .build();
            VcrAuditTaskResponse vcrAuditTaskResponse = webClientApiFactory.executeRequest(request, VcrAuditTaskResponse.class, response -> ObjectUtils.isNotEmpty(response) ? response : null);
            return ObjectUtils.isNotEmpty(vcrAuditTaskResponse)
                    ? vcrAuditTaskResponse.getLabel()
                    : null;
        } catch (Exception e) {
            log.error("查询所有模板时发生错误。异常类型: {}, 异常消息: {}", e.getClass().getName(), e.getMessage(), e);
            return null;
        }
    }

    /**
     * 根据内容ID和类型获取审核任务
     *
     * @param id   媒资内容ID
     * @param type 媒资内容类型
     * @return 如果成功查询到审核任务，则返回审核任务的ID；否则返回null
     * @throws Exception 如果在查询审核任务的过程中发生异常，则抛出此异常
     *                   <p>
     *                   此方法根据提供的媒资内容ID和类型来查询对应的审核任务。
     *                   首先，它调用vcrAuditSubTaskService的getAuditTaskByContentId方法来查询审核任务。
     *                   如果查询结果为空（即没有找到对应的审核任务），则返回null。
     *                   如果查询成功，将记录一条日志信息，并返回查询到的审核任务ID。
     *                   如果在查询过程中发生异常，将记录一条错误信息，并返回null。
     */
    @Override
    public VcrAuditSubTaskEntity getAuditTaskByContentId(String id, String type) {
        try {
            //根据媒资信息查询审核任务表
            return vcrAuditSubTaskService.getAuditTaskByContentId(id, type);
        } catch (Exception e) {
            log.error("查询指定媒资审核任务时发生错误, 内容ID: {}, 错误: {}", id, e.getMessage(), e);
            return null;
        }
    }

    /**
     * 审核任务结果回调方法
     *
     * @param response 审核任务结果响应对象，包含任务结果的相关信息
     * @return 如果回调处理成功，则返回true；否则返回false
     * @throws Exception 如果在回调处理过程中发生异常，则抛出此异常
     * <p>
     * 此方法作为审核任务结果回调，用于处理审核任务响应。
     * 它接受一个VcrAuditTaskResponse类型的参数，该参数包含了审核任务的结果信息。
     * 方法首先验证响应对象是否为null，若为空则记录警告日志并返回false。
     * 接着，从响应中提取任务ID，若任务ID存在，则调用processUtils的processTask方法处理任务，
     * 并根据处理结果记录相应日志。若任务ID不存在，则记录警告日志。
     * 若在回调处理过程中发生异常，将捕获并记录异常日志，并返回false。
     * 若回调处理成功完成，则返回true。
     */

    @Override
    public boolean callback(VcrAuditTaskResponse response) {
        try {
            // 先验证响应对象是否为null
            if (response == null) {
                log.warn("收到空的响应对象");
                return false;
            }
            Optional<String> taskIdOptional = Optional.ofNullable(response.getTaskId());
            if (taskIdOptional.isPresent()) {
                String taskId = taskIdOptional.get();
                boolean success = processUtils.processTask(taskId, response);
                if (success) {
                    log.info("任务处理成功，任务ID: {}", taskId);
                } else {
                    log.error("任务处理失败，任务ID: {}", taskId);
                }
            } else {
                log.warn("响应中未包含有效的任务ID");
            }

            return true;
        } catch (Exception e) {
            // 捕获并记录异常
            log.error("回调方法执行时发生异常", e);
            return false;
        }
    }





    /**
     * 获取所有模板
     *
     * @return 包含所有模板名称的列表，如果查询失败则返回空列表
     * @throws Exception 如果在查询过程中发生任何异常，将抛出此异常
     *                   <p>
     *                   此方法用于查询所有可用的模板。
     *                   它通过调用vcrApiHandler的callApi方法，并传入相应的HTTP方法（GET）、API策略类型（MEDIA_AUDIT）和获取所有模板的URL（由config.getGetAllTemplates()提供）。
     *                   如果查询成功，将记录一条日志信息，并返回一个包含所有模板名称的列表。
     *                   如果在查询过程中发生任何异常，将记录错误信息并抛出异常，同时返回一个空列表。
     *                   <p>
     *                   注意：当前实现中存在一个逻辑错误，成功查询后应返回模板列表而非null。这可能是一个未完成的代码片段或遗漏了部分逻辑。
     */
    @Override
    public List<VcrTemplateResponse> getAllTemplates() {
        try {
            WebClientRequest request = WebClientRequest.builder()
                    .uri(config.getGetAllTemplates())
                    .method(VcrHttpMethodEnums.GET)
                    .header("X-User-Name", config.getPlatformSymbol())
                    .build();

            return webClientApiFactory.executeRequest(request, PresetResponse.class, response -> response.getPresets().stream()
                    .map(preset -> new VcrTemplateResponse(preset.getName(), preset.getName()))
                    .collect(Collectors.toList()));
        } catch (Exception e) {
            log.error("查询所有模板时发生错误。异常类型: {}, 异常消息: {}", e.getClass().getName(), e.getMessage(), e);
            return Collections.emptyList();
        }
    }



}

