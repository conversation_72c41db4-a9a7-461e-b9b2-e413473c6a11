package com.pukka.iptv.basic.plugins.controller.dualScreen;

import com.pukka.iptv.basic.plugins.repository.dualScreen.service.DualScreenInteractionService;
import com.pukka.iptv.common.base.vo.CommonResponse;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR>
 * @Description TODO
 * @Date 2024/12/9 09:59
 * @Version V1.0
 **/
@RestController
@RequestMapping(value = "/dualScreenInteraction", produces = MediaType.APPLICATION_JSON_VALUE)
@Api(tags = "双屏互动controller")
public class DualScreenInteractionController {
    @Autowired
    private DualScreenInteractionService dualScreenInteractionService;

    @ApiOperation(value = "查询对应运营商在ftp上的媒资文件地址")
    @GetMapping("/getFtpMediaUrlForOperator")
    public CommonResponse<String> getFtpMediaUrlForOperator(@RequestParam("operator")  String operator) {
        if (!"ctcc".equals(operator) && !"cucc".equals(operator)) {
            return CommonResponse.fail("查询参数有误");
        }
        String ftp = dualScreenInteractionService.getFtpMediaUrlForOperator(operator);
        return CommonResponse.success(ftp);
    }
}
