package com.pukka.iptv.basic.plugins.controller.vcr;

import com.pukka.iptv.basic.plugins.repository.vcr.service.VcrAuditSubTaskService;
import com.pukka.iptv.common.base.vo.CommonResponse;
import com.pukka.iptv.common.data.model.baidu.vcr.VcrAuditSubTaskEntity;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.Valid;
import java.util.List;

/**
 * <AUTHOR>
 * @Description TODO
 * @Date 2024/11/20 10:42
 * @Version V1.0
 **/
@Slf4j
@RestController
@AllArgsConstructor
@RequestMapping(value = "/baiduCloudIntelligent/vcrAuditSubTask", produces = MediaType.APPLICATION_JSON_VALUE)
@Api(tags = "百度云视频子审核")
public class VcrAuditSubTaskController {

    @Autowired
    private VcrAuditSubTaskService vcrAuditSubTaskService;

    @ApiOperation(value = "根据审核任务code获取子审核任务信息")
    @GetMapping("/getByParentCode")
    public CommonResponse<List<VcrAuditSubTaskEntity>> getByParentCode(@Valid @RequestParam(name = "code", required = true) String code) {
        return CommonResponse.success(vcrAuditSubTaskService.getByParentCode(code));
    }
}
