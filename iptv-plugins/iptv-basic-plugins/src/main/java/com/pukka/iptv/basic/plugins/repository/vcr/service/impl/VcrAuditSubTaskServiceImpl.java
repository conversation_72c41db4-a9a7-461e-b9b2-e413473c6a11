package com.pukka.iptv.basic.plugins.repository.vcr.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.ObjectUtils;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.pukka.iptv.basic.plugins.mapper.vcr.VcrAuditSubTaskMapper;
import com.pukka.iptv.basic.plugins.repository.vcr.service.VcrAuditSubTaskService;
import com.pukka.iptv.common.base.enums.VcrAuditStatusEnums;
import com.pukka.iptv.common.data.model.baidu.vcr.VcrAuditSubTaskEntity;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @Description TODO
 * @Date 2024/11/4 14:50
 * @Version V1.0
 **/
@Slf4j
@Service
public class VcrAuditSubTaskServiceImpl extends ServiceImpl<VcrAuditSubTaskMapper, VcrAuditSubTaskEntity> implements
        VcrAuditSubTaskService {

    /**
     * 根据内容ID和类型获取审核任务
     *
     * @param id   内容ID
     * @param type 任务类型
     * @return 审核任务的ID，如果未查询到则返回null
     */
    @Override
    public VcrAuditSubTaskEntity getAuditTaskByContentId(String id, String type) {
        LambdaQueryWrapper<VcrAuditSubTaskEntity> wrapper = Wrappers.lambdaQuery(VcrAuditSubTaskEntity.class)
                .eq(VcrAuditSubTaskEntity::getContentId, id)
                .eq(VcrAuditSubTaskEntity::getContentType, type)
                .orderByDesc(VcrAuditSubTaskEntity::getCreateTime)
                .last("limit 1");
        return this.getOne(wrapper);
    }

    /**
     * 根据任务ID获取审核子任务实体
     *
     * @param taskId 审核子任务的ID
     * @return 对应的审核子任务实体，如果不存在则返回null
     */
    @Override
    public VcrAuditSubTaskEntity getAuditTaskByTaskId(String taskId) {
        LambdaQueryWrapper<VcrAuditSubTaskEntity> wrapper = Wrappers.lambdaQuery(VcrAuditSubTaskEntity.class)
                .eq(VcrAuditSubTaskEntity::getTaskId, taskId);
        return this.getOne(wrapper);
    }


    /**
     * 获取指定数量的审核任务并更新其状态
     *
     * @param count 要获取的审核任务数量
     * @return 获取到的审核任务列表
     * <p>
     * 该方法首先根据给定的数量参数查询处于"PROVISIONING"状态的审核任务，
     * 并按照ID升序排序。然后，将查询到的任务列表返回。
     * <p>
     * 如果查询到了记录，则进一步对这些记录的状态进行更新，
     * 将它们的状态从"PROVISIONING"更新为"PROCESSING"。
     * <p>
     * 方法使用事务管理，如果执行过程中发生异常，则回滚事务。
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public List<VcrAuditSubTaskEntity> getAuditTaskQuantity(int count) {
        LambdaQueryWrapper<VcrAuditSubTaskEntity> wrapper = Wrappers.lambdaQuery(VcrAuditSubTaskEntity.class)
                .eq(VcrAuditSubTaskEntity::getStatus, VcrAuditStatusEnums.WAITING.getValue())
                .orderByAsc(VcrAuditSubTaskEntity::getId)
                .last("limit " + count);
        List<VcrAuditSubTaskEntity> tasks = this.list(wrapper);
        // 如果有记录，则进行状态更新
        if (!tasks.isEmpty()) {
            List<Long> ids = tasks.stream()
                    .map(VcrAuditSubTaskEntity::getId)
                    .collect(Collectors.toList());

            LambdaUpdateWrapper<VcrAuditSubTaskEntity> updateWrapper = Wrappers.lambdaUpdate(VcrAuditSubTaskEntity.class)
                    .set(VcrAuditSubTaskEntity::getStatus, VcrAuditStatusEnums.PROCESSING.getValue())
                    .in(VcrAuditSubTaskEntity::getId, ids);

            this.update(updateWrapper);
        }
        return tasks;
    }

    /**
     * 检查指定任务ID的所有子任务是否都已完成
     *
     * @param taskId 任务ID，用于标识要检查其所有子任务是否已完成的任务
     * @return 如果指定任务ID的所有子任务都已完成，则返回true；否则返回false
     */
    @Override
    public boolean isAllSubTasksFinished(String taskId) {
        LambdaQueryWrapper<VcrAuditSubTaskEntity> wrapper = Wrappers.lambdaQuery(VcrAuditSubTaskEntity.class)
                .eq(VcrAuditSubTaskEntity::getTaskId, taskId)
                .orderByDesc(VcrAuditSubTaskEntity::getCreateTime)
                .last("limit 1");

        VcrAuditSubTaskEntity task = this.getOne(wrapper);
        if (ObjectUtils.isNotEmpty(task)) {
            LambdaQueryWrapper<VcrAuditSubTaskEntity> vcrAuditSubTaskEntityLambdaQueryWrapper = Wrappers.lambdaQuery(VcrAuditSubTaskEntity.class)
                    .eq(VcrAuditSubTaskEntity::getParentCode, task.getParentCode());
            List<VcrAuditSubTaskEntity> list = this.list(vcrAuditSubTaskEntityLambdaQueryWrapper);
            if (list.stream().allMatch(item -> org.apache.commons.lang3.ObjectUtils.isNotEmpty(item.getStatus()))
                    && list.stream().noneMatch(item -> VcrAuditStatusEnums.WAITING.getValue().equals(item.getStatus()))
                    && list.stream().noneMatch(item -> VcrAuditStatusEnums.PROCESSING.getValue().equals(item.getStatus()))) {
                log.info("当前 taskId:{},所有子任务已完成", taskId);
                return true;
            } else {
                log.info("当前 taskId:{}，还有子任务没有完成", taskId);
                return false;
            }
        }
        log.info("当前 taskId:{}，没有子任务", taskId);
        return false;
    }

    /**
     * 根据父级代码获取视频审核子任务列表
     *
     * @param code 父级代码
     * @return 包含视频审核子任务实体的列表
     */
    @Override
    public List<VcrAuditSubTaskEntity> getByParentCode(String code) {
        LambdaQueryWrapper<VcrAuditSubTaskEntity> wrapper = Wrappers.lambdaQuery(VcrAuditSubTaskEntity.class)
                .eq(VcrAuditSubTaskEntity::getParentCode, code)
                .orderByAsc(VcrAuditSubTaskEntity::getUpdateTime);
        return this.list(wrapper);
    }

    /**
     *
     * 更新任务状态。
     *
     * @param taskId 任务ID
     * @param status 新的任务状态
     * @return 更新成功返回true，否则返回false
     */
    @Override
    public boolean updateStatus(String taskId, Integer status) {
        LambdaUpdateWrapper<VcrAuditSubTaskEntity> updateWrapper = Wrappers.lambdaUpdate(VcrAuditSubTaskEntity.class)
                .set(VcrAuditSubTaskEntity::getStatus, status)
                .eq(VcrAuditSubTaskEntity::getTaskId, taskId);
            return this.update(updateWrapper);
    }

    /**
     * 更新审核状态
     *
     * @param ids    需要更新状态的审核任务ID列表
     * @param status
     * @return 如果更新成功且影响行数大于0则返回true，否则返回false
     * <p>
     * 直接使用LambdaUpdateWrapper构建更新条件，将指定ID的任务状态更新为审核通过（REVIEWAPPROVED）。
     * 如果ids列表为空，update方法将不会更新任何记录，因此无需显式判断。
     */
    @Override
    public Boolean updateAuditStatus(List<String> ids, Integer status) {
        LambdaUpdateWrapper<VcrAuditSubTaskEntity> updateWrapper = Wrappers.lambdaUpdate(VcrAuditSubTaskEntity.class)
                .set(VcrAuditSubTaskEntity::getStatus, status)
                .in(VcrAuditSubTaskEntity::getTaskId, ids);
        // 直接返回update操作的结果，如果ids为空，则不会更新任何记录，返回false
        return this.update(updateWrapper);
    }

    /**
     * 更新审核任务的备注信息
     *
     * @param taskId 审核任务的ID
     * @param remark 新的备注信息
     * @return 如果更新成功返回true，否则返回false
     *
     * 此方法用于更新指定审核任务的备注信息。它创建了一个LambdaUpdateWrapper对象，
     * 该对象设置了新的备注信息，并指定了需要更新的审核任务ID。
     * 然后，通过调用update方法执行更新操作，并返回更新是否成功的布尔值。
     */
    @Override
    public Boolean updateAuditRemark(String taskId, String remark) {
        LambdaUpdateWrapper<VcrAuditSubTaskEntity> updateWrapper = Wrappers.lambdaUpdate(VcrAuditSubTaskEntity.class)
                .set(VcrAuditSubTaskEntity::getDescription, remark)
                .eq(VcrAuditSubTaskEntity::getTaskId, taskId);
        return this.update(updateWrapper);
    }


}
