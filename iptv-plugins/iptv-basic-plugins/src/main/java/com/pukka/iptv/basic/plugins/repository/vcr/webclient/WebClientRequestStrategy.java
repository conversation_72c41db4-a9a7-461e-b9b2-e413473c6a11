package com.pukka.iptv.basic.plugins.repository.vcr.webclient;

import reactor.core.publisher.Mono;

import java.util.function.Function;

/**
 * <AUTHOR>
 * @Description TODO
 * @Date 2024/11/12 15:02
 * @Version V1.0
 **/
public interface WebClientRequestStrategy {
    /**
     * 执行HTTP请求并返回响应结果。
     *
     * @param <T>         期望的响应类型
     * @param <R>         经过映射后的响应类型
     * @param request     包含请求信息的WebClientRequest对象
     * @param responseType 响应数据的类型
     * @param responseMapper 用于将响应数据从类型T映射到类型R的函数
     * @return 返回一个Mono<R>对象，该对象包含经过映射的响应数据
     */
    <T, R> Mono<R> execute(WebClientRequest request, Class<T> responseType, Function<T, R> responseMapper);
    /**
     * 创建一个指定类型的实例。
     *
     * @param clazz 要创建的实例的类型
     * @param <T>   创建的实例的类型
     * @return 创建的实例
     * @throws RuntimeException 如果实例创建失败，将抛出此异常
     */
    default <T> T createInstance(Class<T> clazz) {
        if (clazz == null) {
            return null;
        }
        try {
            return clazz.getDeclaredConstructor().newInstance();
        } catch (Exception e) {
            throw new RuntimeException("Failed to instantiate responseType", e);
        }
    }

}
