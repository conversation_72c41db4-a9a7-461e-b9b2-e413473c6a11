package com.pukka.iptv.basic.plugins.repository.seriesMonitor.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.pukka.iptv.common.api.feign.bms.BmsContentFeignClient;
import com.pukka.iptv.common.api.feign.bms.BmsProgramFeignClient;
import com.pukka.iptv.common.base.vo.CommonResponse;
import com.pukka.iptv.common.core.util.DateUtils;
import com.pukka.iptv.common.data.model.bms.BmsContent;
import com.pukka.iptv.common.data.model.bms.BmsProgram;
import com.pukka.iptv.common.data.vo.CodeList;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Async;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.concurrent.atomic.AtomicInteger;

/**
 * @projectName: iptv-cloud
 * @package: com.pukka.iptv.basic.plugins.repository.seriesMonitor.service
 * @className: CompensationScanner
 * @author: chiron
 * @description: 补偿扫描服务，定期检查剧头状态是否正确
 * @date: 2025/5/23 11:20
 * @version: 1.0
 */
@Service
@Slf4j
public class CompensationScanner {

    @Autowired
    private BmsContentFeignClient bmsContentFeignClient;

    @Autowired
    private BmsProgramFeignClient bmsProgramFeignClient;

    // 目标发布状态列表
    private static final List<Integer> TARGET_PUBLISH_STATUS = Arrays.asList(3, 5, 6, 7, 8, 10);

    // 目标内容类型列表
    private static final List<Integer> TARGET_CONTENT_TYPES = Arrays.asList(3, 4, 5);

    // 失败状态列表
    private static final List<Integer> FAILED_STATUS_LIST = Arrays.asList(4, 7, 10);

    // 每页大小
    private static final int PAGE_SIZE = 50;

    /**
     * 每天凌晨3点执行补偿扫描
     */
    @Scheduled(cron = "0 0 3 * * ?")
    @Async("taskExecutor")
    public void scheduledCompensationScan() {
        log.info("开始执行定时补偿扫描...");
        scanAndFixSeriesStatus();
    }

    /**
     * 扫描并修复剧头状态
     */
    public void scanAndFixSeriesStatus() {
        // 记录开始时间
        long startTime = System.currentTimeMillis();
        String startTimeStr = DateUtils.getTime();

        log.info("开始补偿扫描剧头数据，开始时间: {}", startTimeStr);

        // 统计计数器
        AtomicInteger processedCount = new AtomicInteger(0);
        AtomicInteger updatedCount = new AtomicInteger(0);
        AtomicInteger failedCount = new AtomicInteger(0);

        int pageNo = 1;
        boolean hasMore = true;

        while (hasMore) {
            try {
                // 分页查询剧头
                CommonResponse<Page<BmsContent>> response = bmsContentFeignClient.listContentByStatusAndType(
                        TARGET_PUBLISH_STATUS, TARGET_CONTENT_TYPES, pageNo, PAGE_SIZE);

                if (response == null || response.getData() == null) {
                    log.error("获取剧头数据失败");
                    break;
                }

                Page<BmsContent> contentPage = response.getData();
                List<BmsContent> records = contentPage.getRecords();
                if (CollectionUtils.isEmpty(records)) {
                    hasMore = false;
                    continue;
                }

                // 增加处理计数
                processedCount.addAndGet(records.size());

                // 处理每个剧头
                for (BmsContent content : records) {
                    try {
                        // 查询剧头下的子集
                        List<String> codes = Collections.singletonList(content.getCmsContentCode());
                        CommonResponse<List<BmsProgram>> programResponse = bmsProgramFeignClient.getByProgramId(
                                CodeList.of(codes, content.getSpId()));

                        if (programResponse == null || programResponse.getData() == null) {
                            continue;
                        }

                        List<BmsProgram> programs = programResponse.getData();

                        // 检查是否有失败状态的子集
                        boolean hasFailedPrograms = programs.stream()
                                .anyMatch(p -> p.getPublishStatus() != null && FAILED_STATUS_LIST.contains(p.getPublishStatus()));

                        // 检查并更新剧头状态
                        Integer currentSubRemind = content.getSubRemind();
                        boolean needUpdate = false;
                        Integer newSubRemind = null;

                        if (hasFailedPrograms && (currentSubRemind == null || currentSubRemind != -1)) {
                            // 有失败子集但未标记为-1
                            needUpdate = true;
                            newSubRemind = -1;
                        } else if (!hasFailedPrograms && (currentSubRemind == null || currentSubRemind == -1)) {
                            // 没有失败子集但标记为-1或未标记
                            needUpdate = true;
                            newSubRemind = 0;
                        }

                        // 如果需要更新
                        if (needUpdate) {
                            BmsContent updateContent = new BmsContent();
                            updateContent.setId(content.getId());
                            updateContent.setSubRemind(newSubRemind);

                            CommonResponse<Boolean> updateResponse = bmsContentFeignClient.updateById(updateContent);
                            if (updateResponse != null && Boolean.TRUE.equals(updateResponse.getData())) {
                                updatedCount.incrementAndGet();
                                log.info("成功更新剧头状态: seriesId={}, seriesCode={}, oldStatus={}, newStatus={}",
                                        content.getId(), content.getCmsContentCode(), currentSubRemind, newSubRemind);
                            } else {
                                failedCount.incrementAndGet();
                                log.error("更新剧头状态失败: seriesId={}, seriesCode={}, response={}",
                                        content.getId(), content.getCmsContentCode(), updateResponse);
                            }
                        }
                    } catch (Exception e) {
                        failedCount.incrementAndGet();
                        log.error("处理剧头{}时发生错误", content.getCmsContentCode(), e);
                    }
                }

                // 记录当前进度
                if (pageNo % 5 == 0) {
                    long currentTime = System.currentTimeMillis();
                    long elapsedSeconds = (currentTime - startTime) / 1000;
                    log.info("补偿扫描进度: 已处理{}页，共{}个剧头，更新{}个，失败{}个，耗时{}秒",
                            pageNo, processedCount.get(), updatedCount.get(), failedCount.get(), elapsedSeconds);
                }

                pageNo++;
                hasMore = records.size() == PAGE_SIZE;

                // 避免过快处理下一页
                Thread.sleep(1000);
            } catch (Exception e) {
                log.error("补偿扫描第{}页时发生错误", pageNo, e);

                // 出错后等待一段时间再继续
                try {
                    Thread.sleep(5000);
                } catch (InterruptedException ie) {
                    Thread.currentThread().interrupt();
                }

                // 减少重试频率
                hasMore = pageNo <= 10;
            }
        }

        // 记录结束时间和总耗时
        long endTime = System.currentTimeMillis();
        String endTimeStr = DateUtils.getTime();
        long totalTimeSeconds = (endTime - startTime) / 1000;
        long totalTimeMinutes = totalTimeSeconds / 60;
        long remainingSeconds = totalTimeSeconds % 60;

        log.info("补偿扫描完成! 统计信息如下:");
        log.info("开始时间: {}", startTimeStr);
        log.info("结束时间: {}", endTimeStr);
        log.info("总耗时: {}分{}秒", totalTimeMinutes, remainingSeconds);
        log.info("处理剧头数量: {}", processedCount.get());
        log.info("更新剧头数量: {}", updatedCount.get());
        log.info("处理失败的剧头数量: {}", failedCount.get());
    }

}

