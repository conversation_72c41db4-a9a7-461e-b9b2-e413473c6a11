package com.pukka.iptv.basic.plugins.repository.vcr.service;

import com.pukka.iptv.common.data.model.baidu.vcr.VcrAuditSubTaskEntity;
import com.pukka.iptv.common.data.model.baidu.vcr.VcrAuditTransit;
import com.pukka.iptv.common.data.model.baidu.vcr.response.VcrAuditTaskResponse;
import com.pukka.iptv.common.data.model.baidu.vcr.response.VcrTemplateResponse;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @Description TODO
 * @Date 2024/10/24 15:26
 * @Version V1.0
 **/
public interface VcrService {

    boolean save(List<VcrAuditTransit> vcrAuditInterflowEntities);

    boolean cancelAuditTask(String taskId);

    String getAuditTaskByTaskId(String taskId);

    VcrAuditSubTaskEntity getAuditTaskByContentId(String id, String type);

    boolean callback(VcrAuditTaskResponse response);

    List<VcrTemplateResponse> getAllTemplates();
}
