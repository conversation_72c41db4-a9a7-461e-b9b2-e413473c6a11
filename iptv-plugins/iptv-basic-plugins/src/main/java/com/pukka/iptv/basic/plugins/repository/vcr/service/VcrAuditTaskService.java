package com.pukka.iptv.basic.plugins.repository.vcr.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.pukka.iptv.common.data.model.baidu.vcr.VcrAuditTransit;
import com.pukka.iptv.common.data.model.baidu.vcr.VcrAuditTaskEntity;

import java.util.List;

/**
 * <AUTHOR>
 * @Description TODO
 * @Date 2024/11/4 14:49
 * @Version V1.0
 **/
public interface VcrAuditTaskService extends IService<VcrAuditTaskEntity> {
    /**
     * 批量保存审核任务
     *
     * @param vcrAuditInterflowEntities 要保存的审核任务交互实体列表
     * @return 返回保存操作的结果信息（通常是操作成功的提示或错误消息）
     *
     * 此方法接受一个包含多个审核任务交互实体（VcrAuditTransit）的列表作为参数，
     * 并将这些实体批量保存到数据库中。每个实体代表一个审核任务的相关数据。
     *
     * 方法执行后，将返回一个字符串，用于表示保存操作的结果。
     * 如果保存成功，字符串可能包含操作成功的提示信息；
     * 如果保存失败，字符串应包含相应的错误消息，以便调用者了解失败的原因。
     */
    Boolean saveAuditTaskBatch(List<VcrAuditTransit> vcrAuditInterflowEntities);

    /**
     * 更新审核任务状态
     *
     * @param code   审核任务代码，用于唯一标识一个审核任务
     * @param result 审核结果，用于更新审核任务的状态
     *
     * 此方法用于根据提供的审核任务代码（code）更新对应审核任务的状态（result）。
     * 调用此方法时，需要传入审核任务的唯一代码和期望更新的状态值。
     * 方法内部会根据提供的代码找到对应的审核任务，并更新其状态为传入的结果值。
     */
    VcrAuditTaskEntity updateStatus(String code, Integer result);
}
