package com.pukka.iptv.basic.plugins.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * @projectName: iptv-cloud
 * @package: com.pukka.iptv.basic.plugins.dto
 * @className: SeriesStatusInfo
 * @author: chiron
 * @description: TODO
 * @date: 2025/5/23 10:54
 * @version: 1.0
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class SeriesStatusInfo {
    // 剧头ID
    private Long seriesId;

    // SP ID
    private Long spId;

    // 剧头编码
    private String seriesCode;

    // 是否有失败子集
    private boolean hasFailedPrograms;

    // 是否已标记
    private boolean alreadyMarked;
}
