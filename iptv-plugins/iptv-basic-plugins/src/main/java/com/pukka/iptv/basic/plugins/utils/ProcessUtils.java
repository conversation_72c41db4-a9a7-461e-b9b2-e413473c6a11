package com.pukka.iptv.basic.plugins.utils;

import com.pukka.iptv.basic.plugins.repository.vcr.enums.VcrLabelStatusEnums;
import com.pukka.iptv.basic.plugins.repository.vcr.service.VcrAuditSubTaskService;
import com.pukka.iptv.basic.plugins.repository.vcr.service.VcrAuditTaskService;
import com.pukka.iptv.common.api.feign.cms.CmsProgramFeignClient;
import com.pukka.iptv.common.base.enums.VcrAuditResultsEnums;
import com.pukka.iptv.common.base.vo.CommonResponse;
import com.pukka.iptv.common.core.util.SpringUtils;
import com.pukka.iptv.common.data.model.baidu.vcr.VcrAuditSubTaskEntity;
import com.pukka.iptv.common.data.model.baidu.vcr.VcrAuditTaskEntity;
import com.pukka.iptv.common.data.model.baidu.vcr.response.VcrAuditTaskResponse;
import com.pukka.iptv.common.data.model.cms.CmsProgram;
import com.pukka.iptv.common.rabbitmq.constans.WebsocketConstant;
import lombok.extern.slf4j.Slf4j;
import org.springframework.amqp.rabbit.core.RabbitTemplate;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * <AUTHOR>
 * @Description TODO
 * @Date 2024/11/25 14:45
 * @Version V1.0
 **/
@Slf4j
@Component
public class ProcessUtils {

    private final RabbitTemplate rabbitTemplate = SpringUtils.getBean(RabbitTemplate.class);
    private final VcrAuditTaskService vcrAuditTaskService = SpringUtils.getBean(VcrAuditTaskService.class);
    private final CmsProgramFeignClient cmsProgramFeignClient = SpringUtils.getBean(CmsProgramFeignClient.class);
    private final VcrAuditSubTaskService vcrAuditSubTaskService = SpringUtils.getBean(VcrAuditSubTaskService.class);

    /**
     * 处理任务的核心逻辑
     *
     * @param taskId   任务ID
     * @param response 审核任务的响应对象
     * @return 任务处理是否成功
     */
    public boolean processTask(String taskId, VcrAuditTaskResponse response) {
        try {
            // 获取任务的状态
            Integer status = getLabelStatus(response);

            // 查找审核子任务
            VcrAuditSubTaskEntity vcrAuditSubTask = getAuditSubTask(taskId);
            if (vcrAuditSubTask == null) {
                return false;
            }

            // 更新子任务状态
            if (!updateSubTaskStatus(taskId, status)) {
                return false;
            }
            //更新媒资状态
            CommonResponse<Boolean> booleanCommonResponse = cmsProgramFeignClient.updateCmsProgramStatus(convertVcrToCmsProgram(vcrAuditSubTask, status));
            if (!booleanCommonResponse.getData()) {
                log.error("更新媒资状态失败，子任务ID: {}", taskId);
                return false;
            }
            // 查询主任务是否完成
            if (areAllSubTasksFinished(taskId)) {
                return handleMainTaskCompletion(vcrAuditSubTask);
            } else {
                log.info("当前子任务存在其余未完成任务，子任务id: {}, 不更新主任务状态", taskId);
            }

            return true;
        } catch (Exception e) {
            log.error("处理任务时发生错误，任务ID: {}", taskId, e);
            return false;
        }
    }

    /**
     * 将VcrAuditSubTaskEntity对象转换为CmsProgram对象列表
     *
     * @param vcrAuditSubTask 需要转换的VcrAuditSubTaskEntity对象
     * @param status          审核状态
     * @return CmsProgram对象列表
     */
    private List<CmsProgram> convertVcrToCmsProgram(VcrAuditSubTaskEntity vcrAuditSubTask, Integer status) {
        return Stream.of(vcrAuditSubTask)
                .map(task -> {
                    CmsProgram cmsProgram = new CmsProgram();
                    cmsProgram.setId(Long.valueOf(task.getContentId()));
                    cmsProgram.setAuditStatus(status);
                    return cmsProgram;
                })
                .collect(Collectors.toList());
    }


    /**
     * 根据VcrAuditTaskResponse中的标签获取标签状态
     *
     * @param response VcrAuditTaskResponse对象，包含需要获取状态的标签
     * @return 返回标签对应的状态值
     * @throws IllegalArgumentException 如果标签状态无效，则抛出此异常
     */
    private Integer getLabelStatus(VcrAuditTaskResponse response) {
        return Optional.ofNullable(VcrLabelStatusEnums.getLabelStatus(response.getLabel()))
                .map(VcrLabelStatusEnums::getValue)
                .orElseThrow(() -> new IllegalArgumentException("Invalid label status"));
    }

    /**
     * 根据任务ID获取审核子任务实体
     *
     * @param taskId 任务ID
     * @return 返回对应的审核子任务实体，如果未找到则返回null
     */
    private VcrAuditSubTaskEntity getAuditSubTask(String taskId) {
        VcrAuditSubTaskEntity vcrAuditSubTask = vcrAuditSubTaskService.getAuditTaskByTaskId(taskId);
        if (vcrAuditSubTask == null) {
            log.error("未找到对应的审核子任务, 任务ID: {}", taskId);
        }
        return vcrAuditSubTask;
    }

    /**
     * 更新审核子任务的状态
     *
     * @param taskId 任务ID
     * @param status 需要更新的状态值
     * @return 更新是否成功，true表示成功，false表示失败
     */
    private boolean updateSubTaskStatus(String taskId, Integer status) {
        boolean result = vcrAuditSubTaskService.updateStatus(taskId, status);
        if (!result) {
            log.warn("更新审核子任务状态失败，任务ID: {}", taskId);
        }
        return result;
    }

    /**
     * 判断给定任务ID的所有子任务是否都已完成
     *
     * @param taskId 任务ID
     * @return 如果所有子任务都已完成，则返回true；否则返回false
     */
    private boolean areAllSubTasksFinished(String taskId) {
        return vcrAuditSubTaskService.isAllSubTasksFinished(taskId);
    }

    /**
     * 处理主任务完成状态
     *
     * @param vcrAuditSubTask 审核子任务实体
     * @return 如果处理成功返回true，否则返回false
     */
    private boolean handleMainTaskCompletion(VcrAuditSubTaskEntity vcrAuditSubTask) {
        VcrAuditTaskEntity vcrAuditTask = vcrAuditTaskService.updateStatus(vcrAuditSubTask.getParentCode(), VcrAuditResultsEnums.AUDITFINISH.getValue());
        if (vcrAuditTask != null) {
            // 若全部完成，进行消息发送
            rabbitTemplate.convertAndSend(WebsocketConstant.WEBSOCKET_TOPIC_EXCHANGE,
                    WebsocketConstant.WEBSOCKET_FEEDBACK_GENERAL_AUDIT_PUBLISH_ROUTING, vcrAuditTask);
            log.info("主任务已完成，任务code: {}，审核状态更新并通知已发送", vcrAuditTask.getCode());
            return true;
        } else {
            log.error("处理主任务完成状态，子任务ID: {}, 审核状态更新并通知发送失败", vcrAuditSubTask.getTaskId());
            return false;
        }
    }
}
