package com.pukka.iptv.basic.plugins.repository.vcr.webclient;

import com.pukka.iptv.basic.plugins.config.VcrMediaAuditConfig;
import com.pukka.iptv.basic.plugins.repository.vcr.enums.VcrHttpMethodEnums;
import com.pukka.iptv.basic.plugins.repository.vcr.webclient.handler.GetRequestHandler;
import com.pukka.iptv.basic.plugins.repository.vcr.webclient.handler.PostRequestHandler;
import com.pukka.iptv.basic.plugins.repository.vcr.webclient.handler.PutRequestHandler;
import com.pukka.iptv.common.core.util.SpringUtils;
import org.springframework.stereotype.Component;
import org.springframework.web.reactive.function.client.WebClient;
import reactor.core.publisher.Mono;

import java.util.HashMap;
import java.util.Map;
import java.util.function.Function;

/**
 * <AUTHOR>
 * @Description TODO
 * @Date 2024/11/12 14:05
 * @Version V1.0
 **/
@Component
public class WebClientApiFactory {

    private final WebClient webClient;
    private final Map<VcrHttpMethodEnums, WebClientRequestStrategy> strategies;
    private VcrMediaAuditConfig config = SpringUtils.getBean(VcrMediaAuditConfig.class);
    /**
     * WebClientApi的构造函数。
     *
     * @param webClientBuilder WebClient的构建器对象，用于配置和创建WebClient实例。
     */
    public WebClientApiFactory(WebClient.Builder webClientBuilder) {
        this.webClient = webClientBuilder.baseUrl(config.getBaiduAuditUrl()).build();
        this.strategies = new HashMap<>();
        this.strategies.put(VcrHttpMethodEnums.GET, new GetRequestHandler(webClient));
        this.strategies.put(VcrHttpMethodEnums.POST, new PostRequestHandler(webClient));
        this.strategies.put(VcrHttpMethodEnums.PUT, new PutRequestHandler(webClient));
    }

    /**
     * 执行Web客户端请求并返回映射后的响应结果。
     *
     * @param <T>         响应数据的类型
     * @param <R>         映射后的响应数据的类型
     * @param request     包含请求信息的WebClientRequest对象
     * @param responseType 响应数据期望的类型
     * @param responseMapper 用于将响应数据从类型T映射到类型R的函数
     * @return 映射后的响应结果
     * @throws RuntimeException 如果请求执行失败或响应映射失败，将抛出运行时异常
     */
    public  <T, R> R executeRequest(WebClientRequest request, Class<T> responseType, Function<T, R> responseMapper) {
        return getRequestStrategy(request.getMethod())
                .execute(request, responseType, responseMapper)
                .block();
    }
    /**
     * 根据HTTP方法获取对应的Web客户端请求策略。
     *
     * @param method HTTP方法枚举，表示要使用的HTTP请求方法。
     * @return 与给定HTTP方法对应的Web客户端请求策略。
     *         如果给定方法没有对应的策略，则返回一个默认策略，该策略在执行时会抛出UnsupportedOperationException异常。
     */
    private WebClientRequestStrategy getRequestStrategy(VcrHttpMethodEnums method) {
        return strategies.getOrDefault(method, new WebClientRequestStrategy() {
            /**
             * 执行Web客户端请求。
             *
             * @param request 包含请求信息的WebClientRequest对象。
             * @param responseType 期望的响应数据类型。
             * @param mapper 用于将响应数据从一种类型映射到另一种类型的函数。
             * @param <T> 响应数据的原始类型。
             * @param <R> 映射后的响应数据类型。
             * @return 一个Mono对象，包含映射后的响应数据。
             *         如果给定方法不受支持，则返回包含UnsupportedOperationException异常的Mono对象。
             */
            @Override
            public <T, R> Mono<R> execute(WebClientRequest request, Class<T> responseType, Function<T, R> mapper) {
                return Mono.error(new UnsupportedOperationException("Unsupported HTTP method: " + method));
            }
        });
    }

}
