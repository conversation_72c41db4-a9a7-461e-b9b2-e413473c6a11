package com.pukka.iptv.basic.plugins.rabbitmq.listener;

import com.alibaba.fastjson.JSON;
import com.pukka.iptv.basic.plugins.repository.seriesMonitor.service.ProgramStatusAnalyzer;
import com.pukka.iptv.common.data.dto.PublishParamsDto;
import com.pukka.iptv.common.rabbitmq.constans.FeedbackConstant;
import com.rabbitmq.client.Channel;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.springframework.amqp.core.ExchangeTypes;
import org.springframework.amqp.rabbit.annotation.Exchange;
import org.springframework.amqp.rabbit.annotation.Queue;
import org.springframework.amqp.rabbit.annotation.QueueBinding;
import org.springframework.amqp.rabbit.annotation.RabbitListener;
import org.springframework.amqp.support.AmqpHeaders;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.messaging.handler.annotation.Header;
import org.springframework.messaging.handler.annotation.Payload;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @Description 剧集监控消息监听器
 * @Date 2024/5/11 09:55
 * @Version V1.0
 **/
@Slf4j
@Component
public class SeriesMonitorListener {

    @Autowired
    private ProgramStatusAnalyzer programStatusAnalyzer;

    @RabbitListener(bindings = {@QueueBinding(
            value = @Queue(value = FeedbackConstant.FEEDBACK_ALL_QUEUE),
            exchange = @Exchange(value = FeedbackConstant.FEEDBACK_EXCHANGE, type = ExchangeTypes.TOPIC),
            key = FeedbackConstant.FEEDBACK_ALL_ROUTING)},
            containerFactory = "rabbitListenerContainerFactory"
    )
    public void seriesMonitorReceived(@Payload PublishParamsDto params, Channel channel,
                                      @Header(AmqpHeaders.DELIVERY_TAG) Long deliveryTag) throws Exception {
        log.info("剧集监控系统接收到发布反馈: {}", JSON.toJSONString(params));

        try {
            if (ObjectUtils.isEmpty(params)) {
                log.warn("发布反馈数据为空，消息丢弃");
                channel.basicAck(deliveryTag, false);
                return;
            }

            if (Boolean.TRUE.equals(params.getIsFinish())) {
                log.warn("发布反馈数据处理完成，消息丢弃: {}", params);
                channel.basicAck(deliveryTag, false);
                return;
            }

            // 只处理子集类型的内容 (contentType == 2)
            if (params.getContentType() != null && params.getContentType() == 2) {
                // 委托给 ProgramStatusAnalyzer 处理具体业务逻辑
                boolean result = programStatusAnalyzer.processStatusChange(params);

                if (!result) {
                    log.warn("子集状态处理失败: contentId={}, contentType={}, status={}",
                            params.getContentId(), params.getContentType(), params.getStatus());
                }
            }

            channel.basicAck(deliveryTag, false);
        } catch (Exception e) {
            log.error("处理发布反馈消息异常: {}", params, e);
            // 出错也确认消息，避免无限重试，但记录错误
            channel.basicAck(deliveryTag, false);
        }
    }
}
