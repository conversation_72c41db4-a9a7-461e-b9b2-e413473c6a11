package com.pukka.iptv.basic.plugins.repository.seriesMonitor.service;

import com.pukka.iptv.common.api.feign.bms.BmsContentFeignClient;
import com.pukka.iptv.common.api.feign.bms.BmsProgramFeignClient;
import com.pukka.iptv.common.base.enums.ResultStatusEnum;
import com.pukka.iptv.common.base.vo.CommonResponse;
import com.pukka.iptv.common.data.dto.PublishParamsDto;
import com.pukka.iptv.common.data.model.bms.BmsContent;
import com.pukka.iptv.common.data.model.bms.BmsProgram;
import com.pukka.iptv.common.data.vo.CodeList;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.concurrent.atomic.AtomicLong;

/**
 * @projectName: iptv-cloud
 * @package: com.pukka.iptv.basic.plugins.repository.seriesMonitor.service
 * @className: ProgramStatusAnalyzer
 * @author: chiron
 * @description: 节目状态分析服务，处理子集状态变更并更新剧头标记
 * @date: 2025/5/23 10:41
 * @version: 1.0
 */
@Service
@Slf4j
public class ProgramStatusAnalyzer {

    @Autowired
    private BmsProgramFeignClient bmsProgramFeignClient;

    @Autowired
    private BmsContentFeignClient bmsContentFeignClient;

    // 失败状态列表
    private static final List<Integer> FAILED_STATUS_LIST = Arrays.asList(4, 7, 10);

    // 统计计数
    private final AtomicLong processedCount = new AtomicLong(0);
    private final AtomicLong successCount = new AtomicLong(0);
    private final AtomicLong failureCount = new AtomicLong(0);

    /**
     * 处理子集状态变更
     *
     * @param params 发布参数数据
     * @return 处理结果，成功返回true，失败返回false
     */
    public boolean processStatusChange(PublishParamsDto params) {
        processedCount.incrementAndGet();
        try {
            Long programId = params.getContentId();
            Integer result = params.getResult();

            if (programId == null || result == null) {
                log.warn("子集ID或结果状态为空: {}", params);
                return false;
            }

            // 获取子集详细信息
            BmsProgram program = getProgramInfo(programId);

            if (program == null || program.getCmsSeriesCode() == null || program.getSpId() == null) {
                log.warn("获取子集信息失败或信息不完整: programId={}", programId);
                return false;
            }

            // 获取剧头信息
            BmsContent series = getContentInfo(program.getSpId(), program.getCmsSeriesCode());

            if (series == null) {
                log.warn("获取剧头信息失败: spId={}, seriesCode={}",
                        program.getSpId(), program.getCmsSeriesCode());
                return false;
            }

            // 如果子集为失败状态
            if (ResultStatusEnum.FAIL.getCode().equals(result)) {
                // 将剧头标记为-1
                return markSeriesWithFailedProgram(series);
            } else {
                // 如果子集为成功状态，检查剧头下是否还有其他失败子集
                // 传递当前处理的子集ID和最新状态，确保检查逻辑考虑当前变更
                return checkAndUpdateSeriesStatus(series, programId, result);
            }
        } catch (Exception e) {
            failureCount.incrementAndGet();
            log.error("处理子集状态变更异常", e);
            return false;
        } finally {
            // 记录处理信息，每1000条输出一次统计
            if (processedCount.get() % 1000 == 0) {
                log.info("子集状态监控统计 - 总处理: {}, 成功: {}, 失败: {}",
                        processedCount.get(), successCount.get(), failureCount.get());
            }
        }
    }

    /**
     * 获取节目信息
     */
    private BmsProgram getProgramInfo(Long programId) {
        try {
            CommonResponse<BmsProgram> response = bmsProgramFeignClient.getById(programId);
            if (response != null && response.getData() != null) {
                return response.getData();
            }
        } catch (Exception e) {
            log.error("获取节目信息失败: programId={}", programId, e);
        }
        return null;
    }

    /**
     * 获取剧头信息
     */
    private BmsContent getContentInfo(Long spId, String seriesCode) {
        try {
            CommonResponse<BmsContent> response = bmsContentFeignClient.getByCode(spId, seriesCode);
            if (response != null && response.getData() != null) {
                return response.getData();
            }
        } catch (Exception e) {
            log.error("获取剧头信息失败: spId={}, seriesCode={}", spId, seriesCode, e);
        }
        return null;
    }

    /**
     * 将剧头标记为有失败子集(-1)
     */
    private boolean markSeriesWithFailedProgram(BmsContent series) {
        try {
            // 只有当前状态不是-1时才需要更新
            if (series.getSubRemind() == null || series.getSubRemind() != -1) {
                BmsContent updateContent = new BmsContent();
                updateContent.setId(series.getId());
                updateContent.setSubRemind(-1);

                CommonResponse<Boolean> response = bmsContentFeignClient.updateById(updateContent);
                if (response != null && Boolean.TRUE.equals(response.getData())) {
                    successCount.incrementAndGet();
                    log.info("成功将剧头标记为有失败子集: seriesId={}, seriesCode={}",
                            series.getId(), series.getCmsContentCode());
                    return true;
                } else {
                    failureCount.incrementAndGet();
                    log.error("将剧头标记为有失败子集失败: seriesId={}, seriesCode={}, response={}",
                            series.getId(), series.getCmsContentCode(), response);
                    return false;
                }
            }
            return true;
        } catch (Exception e) {
            failureCount.incrementAndGet();
            log.error("将剧头标记为有失败子集时发生异常: seriesId={}, seriesCode={}",
                    series.getId(), series.getCmsContentCode(), e);
            return false;
        }
    }

    /**
     * 检查剧头下是否还有失败子集，如果没有则将状态重置为0
     *
     * @param series 剧头信息
     * @param currentProgramId 当前处理的子集ID
     * @param currentResult 当前子集的最新处理结果
     * @return 处理结果
     */
    private boolean checkAndUpdateSeriesStatus(BmsContent series, Long currentProgramId, Integer currentResult) {
        try {
            // 获取该剧头下的所有子集
            List<String> codes = Collections.singletonList(series.getCmsContentCode());
            CommonResponse<List<BmsProgram>> programResponse = bmsProgramFeignClient.getByProgramId(
                    CodeList.of(codes, series.getSpId()));

            if (programResponse == null || programResponse.getData() == null) {
                log.warn("查询子集信息失败: spId={}, seriesCode={}",
                        series.getSpId(), series.getCmsContentCode());
                return false;
            }

            List<BmsProgram> programs = programResponse.getData();

            // 检查是否有失败状态的子集，需要考虑当前正在处理的子集的最新状态
            boolean hasFailedPrograms = programs.stream()
                    .anyMatch(p -> {
                        // 如果是当前正在处理的子集，使用最新的结果状态进行判断
                        if (p.getId().equals(currentProgramId)) {
                            // 当前子集如果是成功状态，则不算失败子集
                            return ResultStatusEnum.FAIL.getCode().equals(currentResult);
                        }
                        // 其他子集使用数据库中的状态
                        return p.getPublishStatus() != null && FAILED_STATUS_LIST.contains(p.getPublishStatus());
                    });

            // 如果没有失败子集，且剧头当前状态不是0，则更新为0
            if (!hasFailedPrograms && (series.getSubRemind() == null || series.getSubRemind() != 0)) {
                BmsContent updateContent = new BmsContent();
                updateContent.setId(series.getId());
                updateContent.setSubRemind(0);

                CommonResponse<Boolean> response = bmsContentFeignClient.updateById(updateContent);
                if (response != null && Boolean.TRUE.equals(response.getData())) {
                    successCount.incrementAndGet();
                    log.info("成功将剧头标记为无失败子集: seriesId={}, seriesCode={}",
                            series.getId(), series.getCmsContentCode());
                    return true;
                } else {
                    failureCount.incrementAndGet();
                    log.error("将剧头标记为无失败子集失败: seriesId={}, seriesCode={}, response={}",
                            series.getId(), series.getCmsContentCode(), response);
                    return false;
                }
            } else if (hasFailedPrograms && (series.getSubRemind() == null || series.getSubRemind() != -1)) {
                // 如果有失败子集，但状态不是-1，则更新为-1
                return markSeriesWithFailedProgram(series);
            }
            return true;
        } catch (Exception e) {
            failureCount.incrementAndGet();
            log.error("检查剧头子集状态时发生异常: seriesId={}, seriesCode={}",
                    series.getId(), series.getCmsContentCode(), e);
            return false;
        }
    }
}
