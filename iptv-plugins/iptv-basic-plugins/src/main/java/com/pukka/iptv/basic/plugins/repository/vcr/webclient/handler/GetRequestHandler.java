package com.pukka.iptv.basic.plugins.repository.vcr.webclient.handler;

import com.pukka.iptv.basic.plugins.repository.vcr.webclient.WebClientRequest;
import com.pukka.iptv.basic.plugins.repository.vcr.webclient.WebClientRequestStrategy;
import org.springframework.web.reactive.function.client.WebClient;
import reactor.core.publisher.Mono;

import java.time.Duration;
import java.util.function.Function;

/**
 * <AUTHOR>
 * @Description TODO
 * @Date 2024/11/12 15:03
 * @Version V1.0
 **/
public class GetRequestHandler implements WebClientRequestStrategy {
    private final WebClient webClient;

    public GetRequestHandler(WebClient webClient) {
        this.webClient = webClient;
    }
    /**
     * 执行一个GET请求，并返回映射后的响应结果。
     *
     * @param <T>         响应数据的类型
     * @param <R>         映射后的响应数据的类型
     * @param request     包含请求信息的WebClientRequest对象
     * @param responseType 期望的响应数据类型
     * @param responseMapper 用于将响应数据从类型T映射到类型R的函数
     * @return 包含映射后响应数据的Mono对象
     *
     * 该方法通过WebClient执行一个GET请求。它首先设置请求的URI和请求头，
     * 然后发送请求并获取响应。响应体将被转换为Mono<T>类型，
     * 并应用一个5秒的超时限制。如果请求过程中出现错误，
     * 将尝试通过createInstance方法创建一个responseType类型的默认实例作为错误返回。
     * 最后，使用responseMapper函数将响应数据从类型T映射到类型R。
     */
    @Override
    public <T, R> Mono<R> execute(WebClientRequest request, Class<T> responseType, Function<T, R> responseMapper) {
        return webClient.get()
                .uri(request.getUri())
                .header(request.getHeaderName(), request.getHeaderValue())
                .retrieve()
                .bodyToMono(responseType)
                .timeout(Duration.ofSeconds(5))
                .onErrorReturn(createInstance(responseType))
                .map(responseMapper);
    }
}
