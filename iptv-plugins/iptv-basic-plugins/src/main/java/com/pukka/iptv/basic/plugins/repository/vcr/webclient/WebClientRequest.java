package com.pukka.iptv.basic.plugins.repository.vcr.webclient;

import com.pukka.iptv.basic.plugins.repository.vcr.enums.VcrHttpMethodEnums;
import lombok.Data;

/**
 * <AUTHOR>
 * @Description TODO
 * @Date 2024/11/12 15:01
 * @Version V1.0
 **/
@Data
public class WebClientRequest {
    private final String uri;
    private final VcrHttpMethodEnums method;
    private final Object body;
    private final String headerName;
    private final String headerValue;

    private WebClientRequest(String uri, VcrHttpMethodEnums method, Object body, String headerName, String headerValue) {
        this.uri = uri;
        this.method = method;
        this.body = body;
        this.headerName = headerName;
        this.headerValue = headerValue;
    }

    public static WebClientRequestBuilder builder() {
        return new WebClientRequestBuilder();
    }


    public static class WebClientRequestBuilder {
        private String uri;
        private VcrHttpMethodEnums method;
        private Object body;
        private String headerName;
        private String headerValue;

        public WebClientRequestBuilder uri(String uri) {
            this.uri = uri;
            return this;
        }

        public WebClientRequestBuilder method(VcrHttpMethodEnums method) {
            this.method = method;
            return this;
        }

        public WebClientRequestBuilder body(Object body) {
            this.body = body;
            return this;
        }

        public WebClientRequestBuilder header(String headerName, String headerValue) {
            this.headerName = headerName;
            this.headerValue = headerValue;
            return this;
        }

        public WebClientRequest build() {
            return new WebClientRequest(uri, method, body, headerName, headerValue);
        }
    }
}
