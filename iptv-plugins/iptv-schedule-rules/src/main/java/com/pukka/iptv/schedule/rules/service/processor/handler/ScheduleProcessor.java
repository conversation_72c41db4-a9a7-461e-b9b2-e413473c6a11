package com.pukka.iptv.schedule.rules.service.processor.handler;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.pukka.iptv.common.api.feign.bms.BmsScheduleFeignClient;
import com.pukka.iptv.common.api.feign.cms.CmsProhibitScheduleFeignClient;
import com.pukka.iptv.common.api.feign.cms.CmsScheduleManageFeignClient;
import com.pukka.iptv.common.api.feign.copyright.RuleProhibitScheduleFeignClient;
import com.pukka.iptv.common.api.feign.in.InScheduleFeignClient;
import com.pukka.iptv.common.base.constant.TextConstants;
import com.pukka.iptv.common.base.enums.SourceEnum;
import com.pukka.iptv.common.base.vo.CommonResponse;
import com.pukka.iptv.common.core.util.SpringUtils;
import com.pukka.iptv.common.data.dto.PublishParamsDto;
import com.pukka.iptv.common.data.model.bms.BmsSchedule;
import com.pukka.iptv.common.data.model.cms.CmsProhibitSchedule;
import com.pukka.iptv.common.data.model.copyright.RuleProhibitSchedule;
import com.pukka.iptv.common.data.model.in.InSchedule;
import com.pukka.iptv.common.data.vo.CodeList;
import com.pukka.iptv.common.data.vo.req.ScheduleManageReq;
import com.pukka.iptv.schedule.rules.service.processor.ContentProcessor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @Description 节目单处理
 * @Date 2024/8/29 14:12
 * @Version V1.0
 **/
@Slf4j
@Component
public class ScheduleProcessor implements ContentProcessor {

    private final InScheduleFeignClient inScheduleFeignClient = SpringUtils.getBean(InScheduleFeignClient.class);
    private final BmsScheduleFeignClient bmsScheduleFeignClient = SpringUtils.getBean(BmsScheduleFeignClient.class);
    private final CmsScheduleManageFeignClient cmsScheduleManageFeignClient = SpringUtils.getBean(CmsScheduleManageFeignClient.class);
    private final CmsProhibitScheduleFeignClient cmsProhibitScheduleFeignClient = SpringUtils.getBean(CmsProhibitScheduleFeignClient.class);
    private final RuleProhibitScheduleFeignClient ruleProhibitScheduleFeignClient = SpringUtils.getBean(RuleProhibitScheduleFeignClient.class);


    @Override
    public boolean process(PublishParamsDto params) {
        List<InSchedule> inScheduleList = beforeProcess(params);
        if (CollectionUtil.isEmpty(inScheduleList)) {
            log.info("节目单处理 ->>>>>> 前置处理,当前对象:{},查询inSchedule表返回data为空!", params);
            return false;
        }
        String channelCode = inScheduleList.stream()
                .findFirst()
                .map(InSchedule::getChannelCode)
                .orElse(null);
        if (channelCode == null) {
            log.warn("节目单处理 ->>>>>> 前置处理,当前对象:{},获取频道号为空!", params);
            return false;
        }
        List<CmsProhibitSchedule> cmsProhibitSchedules = filterProhibitSchedule(channelCode, inScheduleList);
        if (CollectionUtil.isEmpty(cmsProhibitSchedules)) {
            log.info("节目单处理 ->>>>>> 前置处理,当前对象:{},节目单过滤为空!", params);
            return true;
        }
        //获取cmsProhibitSchedules中的code 集合
        List<String> cmsScheduleCodes = cmsProhibitSchedules.stream()
                .map(CmsProhibitSchedule::getCode)
                .collect(Collectors.toList());
        if (CollectionUtil.isEmpty(cmsScheduleCodes)) {
            log.warn("节目单处理 ->>>>>> 前置处理,当前对象:{},节目单过滤为空，不进行回收!", params);
            return false;
        }
        //获取ScheduleManageReq对象
        ScheduleManageReq scheduleManageReq = convertToScheduleManageReq(cmsScheduleCodes, params.getSpId());
        //添加非空校验
        if (scheduleManageReq == null) {
            log.info("节目单处理 ->>>>>> 前置处理,当前对象:{},节目单过滤为空，不进行回收!", params);
            return false;
        }
        CommonResponse<Boolean> data = cmsScheduleManageFeignClient.unCheckScheduleManageRollback(scheduleManageReq);
        if (!Boolean.TRUE.equals(data.getData())) {
            log.info("节目单处理 ->>>>>> 前置处理,当前对象:{},节目单回收失败!", params);
            return false;
        }
        return data.getData();
    }

    /**
     * 对象转换方法
     *
     * @param codes
     * @param spId
     * @return
     */

    public ScheduleManageReq convertToScheduleManageReq(List<String> codes, Long spId) {
        if (CollectionUtil.isEmpty(codes)) {
            log.warn("传入的 cmsScheduleCodes 列表为空或所有对象无有效:{}", codes);
            return null;
        }

        ScheduleManageReq scheduleManageReq = new ScheduleManageReq();
        CodeList codeList = CodeList.of(codes, spId);

        try {
            CommonResponse<List<BmsSchedule>> bmsScheduleList = bmsScheduleFeignClient.getByCodeList(codeList);

            if (bmsScheduleList == null || CollectionUtil.isEmpty(bmsScheduleList.getData())) {
                log.warn("传入的 BmsSchedule 列表为空或所有对象无有效 code");
                return scheduleManageReq;
            }

            List<String> ids = bmsScheduleList.getData().stream()
                    .map(BmsSchedule::getId)
                    .map(String::valueOf)
                    .collect(Collectors.toList());
            scheduleManageReq.setIds(ids);
        } catch (Exception e) {
            log.error("转换 CmsProhibitSchedule 失败: ", e);
        }

        return scheduleManageReq;
    }



    /**
     * 前置处理
     *
     * @param params
     */
    private List<InSchedule> beforeProcess(PublishParamsDto params) {
        if (ObjectUtils.isEmpty(params) || ObjectUtils.isEmpty(params.getCorrelateId())) {
            log.info("节目单处理 ->>>>>> 前置处理,当前对象:{},correlateId is empty", params);
            return new ArrayList<>();
        }

        CommonResponse<List<InSchedule>> scheduleInfoByCorrelateId = inScheduleFeignClient.getScheduleInfoByCorrelateId(params.getCorrelateId());
        List<InSchedule> inScheduleList = Optional.ofNullable(scheduleInfoByCorrelateId.getData())
                .map(data -> JSONArray.parseArray(JSON.toJSONString(data)).toJavaList(InSchedule.class))
                .orElse(new ArrayList<>());

        if (CollectionUtil.isEmpty(inScheduleList)) {
            log.warn("节目单处理 -> 前置处理，当前对象: {}，查询 inSchedule 表返回 data 为空!", params);
        }
        return inScheduleList;
    }

    /**
     * 根据频道的违禁规则过滤频道的节目单
     *
     * @param channelCode
     * @param dataList
     * @return
     */
    public List<CmsProhibitSchedule> filterProhibitSchedule(String channelCode, List<InSchedule> dataList) {
        //节目数据为空直接返回，进入后续流程
        if (CollectionUtil.isEmpty(dataList)) {
            return new ArrayList<>();
        }
        try {
            CommonResponse<List<RuleProhibitSchedule>> commonResponse = ruleProhibitScheduleFeignClient.getByChannelCode(
                    channelCode);
            if (!TextConstants.OK.equals(commonResponse.getCode())) {
                log.error("节目单违禁规则获取失败 channelCode : {}, Response :{}", channelCode,
                        commonResponse);
                return new ArrayList<>();
            }
            List<RuleProhibitSchedule> ruleProhibitSchedules = commonResponse.getData();
            if (CollectionUtil.isEmpty(ruleProhibitSchedules)) {
                log.warn("节目单违禁规则校验 返回结果为空 channelCode : {}", channelCode);
                return new ArrayList<>();
            }
            // 过滤违禁节目单
            List<CmsProhibitSchedule> prohibitList = dataList.stream()
                    .map(e -> checkProhibitSchedule(e, ruleProhibitSchedules))
                    .filter(ObjectUtils::isNotEmpty)
                    .collect(Collectors.toList());

            if (CollectionUtil.isNotEmpty(prohibitList)) {
                CommonResponse<Boolean> response = cmsProhibitScheduleFeignClient.save(prohibitList);
                if (!TextConstants.OK.equals(response.getCode())) {
                    log.error("节目单违禁单保存失败，data : {}, Response : {}", JSONUtil.toJsonStr(prohibitList), commonResponse);
                }
            }
            return prohibitList;
        } catch (Exception e) {
            log.error("节目单违禁规则过滤失败，频道号: {}", channelCode, e);
        }
        return new ArrayList<>();
    }

    /**
     * 检查节目单是否违禁
     *
     * @param schedule
     * @param ruleProhibitSchedules
     * @return
     */
    private CmsProhibitSchedule checkProhibitSchedule(InSchedule schedule, List<RuleProhibitSchedule> ruleProhibitSchedules) {
        if (CollectionUtil.isEmpty(ruleProhibitSchedules)) {
            return null;
        }
        for (RuleProhibitSchedule ruleProhibitSchedule : ruleProhibitSchedules) {
            if (ruleProhibitSchedule.getMatchStatus() == 1 && schedule.getProgramName().equals(ruleProhibitSchedule.getProhibitedWords())) {
                return createCmsProhibitSchedule(schedule, ruleProhibitSchedule);
            }
            if (ruleProhibitSchedule.getMatchStatus() == 2 && schedule.getProgramName().contains(ruleProhibitSchedule.getProhibitedWords())) {
                return createCmsProhibitSchedule(schedule, ruleProhibitSchedule);
            }
        }
        return null;
    }

    /**
     * 创建违禁节目单实体
     *
     * @param schedule
     * @param ruleProhibitSchedule
     * @return
     */
    private CmsProhibitSchedule createCmsProhibitSchedule(InSchedule schedule, RuleProhibitSchedule ruleProhibitSchedule) {
        CmsProhibitSchedule cmsProhibitSchedule = BeanUtil.copyProperties(schedule, CmsProhibitSchedule.class);
        cmsProhibitSchedule.setRuleProhibitCode(ruleProhibitSchedule.getCode());
        cmsProhibitSchedule.setCpId(ruleProhibitSchedule.getCpId());
        cmsProhibitSchedule.setCpName(ruleProhibitSchedule.getCpName());
        cmsProhibitSchedule.setSource(SourceEnum.SYSWORK.getValue());
        cmsProhibitSchedule.setChannelName(ruleProhibitSchedule.getChannelName());
        cmsProhibitSchedule.setChannelId(ruleProhibitSchedule.getChannelId());
        cmsProhibitSchedule.setCreatorName(ruleProhibitSchedule.getCreatorName());
        return cmsProhibitSchedule;
    }
}
