package com.pukka.iptv.schedule.rules.service.listener;

import com.pukka.iptv.common.base.enums.ContentTypeEnum;
import com.pukka.iptv.common.data.dto.PublishParamsDto;
import com.pukka.iptv.common.rabbitmq.constans.FeedbackConstant;
import com.pukka.iptv.common.rabbitmq.constans.ScheduleRulesConstant;
import com.pukka.iptv.schedule.rules.service.processor.ContentProcessor;
import com.pukka.iptv.schedule.rules.service.processor.ContentProcessorFactory;
import com.rabbitmq.client.Channel;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.springframework.amqp.core.ExchangeTypes;
import org.springframework.amqp.rabbit.annotation.Exchange;
import org.springframework.amqp.rabbit.annotation.Queue;
import org.springframework.amqp.rabbit.annotation.QueueBinding;
import org.springframework.amqp.rabbit.annotation.RabbitListener;
import org.springframework.amqp.support.AmqpHeaders;
import org.springframework.messaging.handler.annotation.Header;
import org.springframework.messaging.handler.annotation.Payload;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @Description RabbitMQ监听
 * @Date 2024/5/11 09:55
 * @Version V1.0
 **/
@Slf4j
@Component
public class RabbitMQListener {


    @RabbitListener(bindings = {@QueueBinding(
            value = @Queue(value = ScheduleRulesConstant.SCHEDULE_RULES_AUTO_PUBLISH_QUEUE),
            exchange = @Exchange(value = FeedbackConstant.FEEDBACK_EXCHANGE, type = ExchangeTypes.TOPIC),
            key = ScheduleRulesConstant.SCHEDULE_RULES_AUTO_PUBLISH_ROUTING)},
            containerFactory = "rabbitListenerContainerFactory"
    )
    public void feedBackAutoReceived(@Payload PublishParamsDto params, Channel channel, @Header(AmqpHeaders.DELIVERY_TAG) Long deliveryTag) throws Exception {
        try {
            if (ObjectUtils.isEmpty(params)) {
                log.warn("节目单规则 -----> 分发反馈MQ自动发布数据信息为空,消息丢弃");
                return;
            }
            if (Boolean.TRUE.equals(params.getIsFinish())) {
                log.warn("节目单规则 -----> 分发反馈MQ自动发布数据汇总处理完成,消息丢弃,消息：{}", params);
                return;
            }
            log.info("节目单规则 -----> 分发反馈MQ自动发布数据处理，参数：{}", params);
            ContentProcessor processor = ContentProcessorFactory.getProcessor(ContentTypeEnum.getByValue(params.getContentType()));
            boolean result = processor.process(params);
            if (!result) {
                log.error("节目单规则 -----> 分发反馈MQ自动发布数据处理失败,消息丢弃,媒资id：{}，媒资类型:{}", params.getContentId(), params.getContentType());
            }
        } catch (Exception e) {
            log.error("节目单规则 -----> 分发反馈自动发布数据处理异常,参数:{},错误原因:", params, e);
        } finally {
            channel.basicAck(deliveryTag, false);
        }

    }
}
