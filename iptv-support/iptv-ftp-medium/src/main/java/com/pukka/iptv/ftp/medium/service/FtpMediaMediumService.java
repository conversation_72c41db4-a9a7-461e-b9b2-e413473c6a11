package com.pukka.iptv.ftp.medium.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.pukka.iptv.ftp.medium.model.FtpMediaMedium;

import java.util.List;

public interface FtpMediaMediumService extends IService<FtpMediaMedium> {

    void createBatch(List<FtpMediaMedium> ftpMediaMediaList, Integer batchSize);

    void updateDelSignBatch(Long cpId, Integer delSign, List<FtpMediaMedium> ftpMediaMediaList, Integer batchSize);
}
