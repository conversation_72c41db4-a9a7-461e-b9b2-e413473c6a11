package com.pukka.iptv.ftp.medium.easyexcel;

import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.ExcelReader;
import com.alibaba.excel.ExcelWriter;
import com.alibaba.excel.read.metadata.ReadSheet;
import com.alibaba.excel.write.metadata.WriteSheet;
import com.pukka.iptv.common.base.exception.CommonResponseException;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.io.OutputStream;
import java.net.URLEncoder;
import java.util.List;

/**
 * EasyExcel 工具类
 *
 * @author: Charlene
 * @date: 2021/2/22 0012 下午 11:28
 */
@Slf4j
public class EasyExcelUtil<T> {
    //用于统计导入数量
    public static final ThreadLocal<Integer> countMap = new ThreadLocal<>();

    /**
     * 下载模板
     * 文件下载（失败了会返回一个有部分数据的Excel）
     * 直接写，这里注意，finish的时候会自动关闭OutputStream,当然你外面再关闭流问题不大
     * 这里注意 swagger 可能会导致各种问题，请直接用浏览器或者用postman
     *
     * @param response
     * @param fileName
     * @param sheetName
     * @param initData  初始化数据
     * @throws IOException
     */
    public static <T> void exportExcel(HttpServletResponse response, String fileName, String sheetName, Class<T> clz, List<T> initData) {
        response.setContentType("application/vnd.ms-excel");
        response.setCharacterEncoding("utf-8");
        // 这里URLEncoder.encode可以防止中文乱码 当然和easyexcel没有关系
        try {
            fileName = URLEncoder.encode(fileName, "UTF-8").replaceAll("\\+", "%20");
            response.setHeader("Content-disposition", "attachment;filename*=utf-8''" + fileName + ".xlsx");
            EasyExcel.write(response.getOutputStream(), clz).sheet(sheetName).doWrite(initData);
        } catch (Exception e) {
            e.printStackTrace();
            log.error("-----import data error msg:", e.getMessage());
        }
    }

    //多sheet写入
    public static void exportExcel(HttpServletResponse response, String fileName, List<String> sheetNames, List<Class> clzs, List<List> initDatas) {
        response.setContentType("application/vnd.ms-excel");
        response.setCharacterEncoding("utf-8");
        // 这里URLEncoder.encode可以防止中文乱码 当然和easyexcel没有关系
        OutputStream outputStream = null;
        try {
            fileName = URLEncoder.encode(fileName, "UTF-8").replaceAll("\\+", "%20");
            response.setHeader("Content-disposition", "attachment;filename*=utf-8''" + fileName + ".xlsx");
            outputStream = response.getOutputStream();
        } catch (Exception e) {
            e.printStackTrace();
            log.error("-----encode fileName error msg:", e.getMessage());
        }
        ExcelWriter excelWriter = EasyExcel.write(outputStream).build();
        try {
            for (int i = 0; i < sheetNames.size(); i++) {
                WriteSheet writeSheet = EasyExcel.writerSheet(i, sheetNames.get(i)).head(clzs.get(i)).build();
                excelWriter.write(CollectionUtils.isEmpty(initDatas) ? null : initDatas.get(i), writeSheet);
            }
        } catch (Exception e) {
            e.printStackTrace();
            log.error("-----import data error msg:", e.getMessage());
        } finally {
            excelWriter.finish();
        }
    }

    /**
     * 数据导入
     *
     * @param file
     * @param listener
     * @return 成功导入数量
     */
    public static <T> Integer importExcel(MultipartFile file, ExcelListener listener, Class<T> clz) {
        Integer res = null;
        try {
            countMap.set(0);
            //这里 需要指定读用哪个class去读，然后读取第一个sheet 文件流会自动关闭,异步读取,每次读取一行,存5行后一起存入数据库
            EasyExcel.read(file.getInputStream(), clz, listener).sheet().doRead();
            res = countMap.get();
        } catch (IOException e) {
            e.printStackTrace();
            throw new CommonResponseException("导入异常,请稍后再试");
        } finally {
            countMap.remove();
        }
        return res;
    }

    /**
     * @description: 多sheet导入
     * @param:
     * @return:
     * @author: Mazy
     * @date: 2021/4/26
     */
    public static Integer importExcel(MultipartFile file, List<ExcelListener> listeners, List<Class> clzs) {
        Integer res = null;
        try {
            countMap.set(0);
            //这里 需要指定读用哪个class去读,异步读取,每次读取一行,存5行后一起存入数据库
            ExcelReader excelReader = EasyExcel.read(file.getInputStream()).build();
            int size = listeners.size();
            ReadSheet[] readSheets = new ReadSheet[size];
            for (int i = 0; i < listeners.size(); i++) {
                ReadSheet readSheet = EasyExcel.readSheet(i).head(clzs.get(i)).registerReadListener(listeners.get(i)).build();
                readSheets[i] = readSheet;
            }
            excelReader.read(readSheets);
            res = countMap.get();
        } catch (IOException e) {
            e.printStackTrace();
            throw new CommonResponseException("导入异常,请稍后再试");
        } finally {
            countMap.remove();
        }
        return res;
    }

}
 

