package com.pukka.iptv.ftp.medium.mapper;

import com.pukka.iptv.ftp.medium.model.FtpMediaMedium;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
public interface FtpMediaMediumMapper extends MyBaseMapper<FtpMediaMedium> {

    int batchUpdateDelSign(@Param("cpId") Long cpId,@Param("delSign") Integer delSign, @Param("entityList") List<FtpMediaMedium> ftpMediaMediumList);

}