package com.pukka.iptv.ftp.medium.listener.mq;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.pukka.iptv.common.base.constant.RedisKeyConstants;
import com.pukka.iptv.common.base.enums.FTPMediumEnum;
import com.pukka.iptv.common.rabbitmq.config.DelFTPMediumMQConfig;
import com.pukka.iptv.common.redis.service.RedisService;
import com.pukka.iptv.ftp.medium.mapper.FtpMediaMediumMapper;
import com.pukka.iptv.ftp.medium.model.FtpMediaMedium;
import com.pukka.iptv.ftp.medium.util.FTPAllFilesUtil;
import com.rabbitmq.client.Channel;
import lombok.extern.slf4j.Slf4j;
import org.springframework.amqp.rabbit.annotation.Exchange;
import org.springframework.amqp.rabbit.annotation.Queue;
import org.springframework.amqp.rabbit.annotation.QueueBinding;
import org.springframework.amqp.rabbit.annotation.RabbitListener;
import org.springframework.amqp.support.AmqpHeaders;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.messaging.handler.annotation.Header;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.util.Date;
import java.util.Map;


/**
 * @Author: liqq
 * @CreateDate: 2022/9/17 13:02
 */
@Component
@Slf4j
public class DelFTPMediumMQListener {

    @Autowired
    private FtpMediaMediumMapper ftpMediaMediumMapper;


    @RabbitListener(bindings = {@QueueBinding(
            value = @Queue(value = DelFTPMediumMQConfig.DEL_FTP_MEDIUM_QUEUE),
            exchange = @Exchange(value = DelFTPMediumMQConfig.DEL_FTP_MEDIUM_EXCHANGE),
            key = DelFTPMediumMQConfig.DEL_FTP_MEDIUM_ROUTING)},containerFactory="rabbitListenerContainerFactory")
    @Transactional(rollbackFor = Exception.class)
    public void recieved(FtpMediaMedium ftpMediaMedium, Channel channel, @Header(AmqpHeaders.DELIVERY_TAG) Long deliveryTag) throws Exception {
        log.info("删除介质 接收到参数：{}", ftpMediaMedium);
        Integer delStatus = FTPMediumEnum.FAIL_TO_DELETE_STATUS.getCode();
        try {
            //去除多余的/号 仅此处去除,避免影响到其他处
            if (FTPAllFilesUtil.DelFtp(ftpMediaMedium.getFilePath())){
                delStatus = FTPMediumEnum.ALREADY_DELETED_STATUS.getCode();
            }
            ftpMediaMediumMapper.update(null, Wrappers.<FtpMediaMedium>lambdaUpdate().set(FtpMediaMedium::getDelStatus, delStatus)
                    .set(FtpMediaMedium::getUpdateTime,new Date())
                    .eq(FtpMediaMedium::getId, ftpMediaMedium.getId()));

        } catch (Exception e) {
            log.error("删除介质 接收到参数：{}, 错误原因：{}", ftpMediaMedium, e.getMessage());
        } finally {
            channel.basicAck(deliveryTag, false);
        }
    }
}
