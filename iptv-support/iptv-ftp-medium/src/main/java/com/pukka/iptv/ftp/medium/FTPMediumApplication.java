package com.pukka.iptv.ftp.medium;

import com.pukka.iptv.common.swagger.annotation.EnableCustomOpenApi;
import lombok.extern.slf4j.Slf4j;
import org.mybatis.spring.annotation.MapperScan;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.CommandLineRunner;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.web.servlet.ServletComponentScan;
import org.springframework.cloud.openfeign.EnableFeignClients;
import org.springframework.scheduling.annotation.EnableScheduling;

import java.net.InetAddress;

/**
 * <AUTHOR>
 * @date: 2021/7/23
 */
@Slf4j
@ServletComponentScan
@EnableCustomOpenApi
@MapperScan("com.pukka.iptv.**.mapper")
@SpringBootApplication
@EnableFeignClients("com.pukka.iptv.common.api.feign")
@EnableScheduling
public class FTPMediumApplication implements CommandLineRunner {

    @Value("${server.port}")
    public String port;

    public static void main(String[] args) {
        SpringApplication.run(FTPMediumApplication.class, args);
    }


    @Override
    public void run(String... args) throws Exception {
        System.out.println("Swagger地址：http://" + InetAddress.getLocalHost().getHostAddress() + ":" + port + "/doc.html");
    }
}
