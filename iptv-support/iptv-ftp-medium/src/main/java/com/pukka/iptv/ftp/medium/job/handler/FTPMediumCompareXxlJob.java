package com.pukka.iptv.ftp.medium.job.handler;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.pukka.iptv.common.base.enums.FTPMediumEnum;
import com.pukka.iptv.common.rabbitmq.config.DelFTPMediumMQConfig;
import com.pukka.iptv.ftp.medium.mapper.CmsResourceDTOMapper;
import com.pukka.iptv.ftp.medium.mapper.FtpMediaMediumMapper;
import com.pukka.iptv.ftp.medium.model.CmsResourceDTO;
import com.pukka.iptv.ftp.medium.model.FtpMediaMedium;
import com.pukka.iptv.ftp.medium.service.FtpMediaMediumService;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.context.XxlJobHelper;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.amqp.rabbit.core.RabbitTemplate;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.stream.Collectors;

/**
 * @ClassName StatisticsXxlJob
 * @Description TODO
 * <AUTHOR>
 * @Date 2022/7/15 15:02
 * @Version
 */
@Component
@Slf4j
public class FTPMediumCompareXxlJob {

    @Autowired
    private CmsResourceDTOMapper cmsResourceMapper;
    @Autowired
    private FtpMediaMediumService ftpMediaMediumService;
    @Autowired
    private FtpMediaMediumMapper ftpMediaMediumMapper;

    @XxlJob("ftpMediumCompareJobHandler")
    public ReturnT<String> ftpMediumCompareJobHandler() throws Exception {
        log.info("XXL-JOB, ftpMediumCompareJobHandler.time={}", new Date());
        try {
            // 获取参数
            String params = XxlJobHelper.getJobParam();
            log.info("接收調度中心参数...[{}]", params);
            String cpCode = null;
            Integer compareSize = null;
            JSONObject jsonObject = JSON.parseObject(params);
            if (jsonObject != null) {
                cpCode = jsonObject.getString("cpCode");
                compareSize = jsonObject.getInteger("compareSize");
            }
            //获取ftp_media_medium数据
            LambdaQueryWrapper<FtpMediaMedium> queryWrapper = Wrappers.lambdaQuery();
            queryWrapper.isNull(FtpMediaMedium::getCpId);
            queryWrapper.eq(FtpMediaMedium::getDelSign, FTPMediumEnum.WAITING_FOR_COMPARISON_SIGN.getCode());
            if (jsonObject != null && StringUtils.isNotEmpty(cpCode)) {
                queryWrapper.eq(FtpMediaMedium::getCpCode, cpCode);
            }
            if (jsonObject != null && (compareSize != null && compareSize > 0)) {
                queryWrapper.last("limit " + compareSize);
            }
            List<FtpMediaMedium> list = ftpMediaMediumService.list(queryWrapper);
            Map<String, FtpMediaMedium> ftpMediaMediumMap = list.stream().collect(Collectors.toMap(FtpMediaMedium::getFilePath, o -> o, (k1, k2) -> k1));
            //获取对应的

            //获取cms_resource中的两个字段 组成映射关系 path - cpid,同时只保留.ts里的文件
            List<CmsResourceDTO> cmsResourceList = cmsResourceMapper.selectByCPCode(cpCode);
            Map<String, CmsResourceDTO> cmsResourceMap = cmsResourceList.stream()
                    .collect(Collectors.toMap(CmsResourceDTO::getFileUrl, o -> o, (k1, k2) -> k1));
            //取两个数据的交集
            Set<String> cmsResourceKeys = cmsResourceMap.keySet();
            //初始化是否删除的两个数组
            List<FtpMediaMedium> updateNonDeletableMediumList = new ArrayList<>();
            List<FtpMediaMedium> updateIsDeletableMediumList = new ArrayList<>();
            //cmsResourceKeys 里数据绑定cpid
            Set<String> ftpMediaSet = ftpMediaMediumMap.keySet();
            for (String ftpkey : ftpMediaSet) {
                FtpMediaMedium ftpMediaMedium = ftpMediaMediumMap.get(ftpkey);
                if (cmsResourceKeys.contains(ftpkey)) {
                    CmsResourceDTO cmsResourceDTO = cmsResourceMap.get(ftpkey);
                    ftpMediaMedium.setCpId(cmsResourceDTO.getCpId());
                    ftpMediaMedium.setDelSign(FTPMediumEnum.NON_DELETABLE_SIGN.getCode());
                    updateNonDeletableMediumList.add(ftpMediaMedium);
                } else {
                    ftpMediaMedium.setDelSign(FTPMediumEnum.IS_DELETABLE_SIGN.getCode());
                    updateIsDeletableMediumList.add(ftpMediaMedium);
                }
            }
            // 根据cpId进行分组   存在的改为不可删除,剩余的改为可删除
            Map<Long, List<FtpMediaMedium>> groupedMedia = updateNonDeletableMediumList.stream()
                    .collect(Collectors.groupingBy(FtpMediaMedium::getCpId));
            for (Long cpId : groupedMedia.keySet()) {
                ftpMediaMediumService.updateDelSignBatch(cpId, FTPMediumEnum.NON_DELETABLE_SIGN.getCode(), groupedMedia.get(cpId),2000);
            }
            if (!updateIsDeletableMediumList.isEmpty()){
                ftpMediaMediumService.updateDelSignBatch(null, FTPMediumEnum.IS_DELETABLE_SIGN.getCode(), updateIsDeletableMediumList,2000);
            }
            return ReturnT.SUCCESS;
        } catch (Exception e) {
            log.error("定时对比冗余[.ts]文件,e={}", e.getMessage());
            return ReturnT.FAIL;
        }

    }






    @XxlJob("ftpMediumFileUrlCompareJobHandler")
    public ReturnT<String> ftpMediumFileUrlCompareJobHandler() throws Exception {
        log.info("XXL-JOB, ftpMediumCompareJobHandler.time={}", new Date());
        try {
            // 获取参数
            String params = XxlJobHelper.getJobParam();
            log.info("接收調度中心参数...[{}]", params);
            Integer compareSize = null;
            JSONObject jsonObject = JSON.parseObject(params);
            if (jsonObject != null) {
                compareSize = jsonObject.getInteger("compareSize");
            }
            //获取ftp_media_medium数据
            LambdaQueryWrapper<FtpMediaMedium> queryWrapper = Wrappers.lambdaQuery();
            queryWrapper.isNull(FtpMediaMedium::getCpId);
            queryWrapper.eq(FtpMediaMedium::getDelSign, FTPMediumEnum.WAITING_FOR_COMPARISON_SIGN.getCode());
            if (jsonObject != null && (compareSize != null && compareSize > 0)) {
                queryWrapper.last("limit " + compareSize);
            }
            List<FtpMediaMedium> list = ftpMediaMediumService.list(queryWrapper);
            if (list.isEmpty()){
                return ReturnT.SUCCESS;
            }
            Map<String, FtpMediaMedium> ftpMediaMediumMap = list.stream().collect(Collectors.toMap(FtpMediaMedium::getFilePath, o -> o, (k1, k2) -> k1));
            //获取对应的
            Set<String> ftpMediaSet = ftpMediaMediumMap.keySet();
            //获取cms_resource中的两个字段 组成映射关系 path - cpid,同时只保留.ts里的文件
            List<CmsResourceDTO> cmsResourceList = cmsResourceMapper.selectByFileUrl(ftpMediaSet);
            Map<String, CmsResourceDTO> cmsResourceMap = cmsResourceList.stream()
                    .collect(Collectors.toMap(CmsResourceDTO::getFileUrl, o -> o, (k1, k2) -> k1));
            //取两个数据的交集
            Set<String> cmsResourceKeys = cmsResourceMap.keySet();
            //初始化是否删除的两个数组
            List<FtpMediaMedium> updateNonDeletableMediumList = new ArrayList<>();
            List<FtpMediaMedium> updateIsDeletableMediumList = new ArrayList<>();
            //cmsResourceKeys 里数据绑定cpid

            for (String ftpkey : ftpMediaSet) {
                FtpMediaMedium ftpMediaMedium = ftpMediaMediumMap.get(ftpkey);
                if (cmsResourceKeys.contains(ftpkey)) {
                    CmsResourceDTO cmsResourceDTO = cmsResourceMap.get(ftpkey);
                    ftpMediaMedium.setCpId(cmsResourceDTO.getCpId());
                    ftpMediaMedium.setDelSign(FTPMediumEnum.NON_DELETABLE_SIGN.getCode());
                    updateNonDeletableMediumList.add(ftpMediaMedium);
                } else {
                    ftpMediaMedium.setDelSign(FTPMediumEnum.IS_DELETABLE_SIGN.getCode());
                    updateIsDeletableMediumList.add(ftpMediaMedium);
                }
            }
            // 根据cpId进行分组   存在的改为不可删除,剩余的改为可删除
            Map<Long, List<FtpMediaMedium>> groupedMedia = updateNonDeletableMediumList.stream()
                    .collect(Collectors.groupingBy(FtpMediaMedium::getCpId));
            for (Long cpId : groupedMedia.keySet()) {
                ftpMediaMediumService.updateDelSignBatch(cpId, FTPMediumEnum.NON_DELETABLE_SIGN.getCode(), groupedMedia.get(cpId),2000);
            }
            if (!updateIsDeletableMediumList.isEmpty()){
                ftpMediaMediumService.updateDelSignBatch(null, FTPMediumEnum.IS_DELETABLE_SIGN.getCode(), updateIsDeletableMediumList,2000);
            }
            return ReturnT.SUCCESS;
        } catch (Exception e) {
            log.error("定时对比冗余[.ts]文件,e={}", e.getMessage());
            return ReturnT.FAIL;
        }

    }
}
