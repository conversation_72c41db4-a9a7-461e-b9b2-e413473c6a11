package com.pukka.iptv.ftp.medium.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.pukka.iptv.ftp.medium.model.CmsResourceDTO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Set;

/**
 *
 * @author: zhengcl
 * @date: 2021-9-15 15:13:01
 */

@Mapper
public interface CmsResourceDTOMapper extends BaseMapper<CmsResourceDTO>{
    List<CmsResourceDTO>  selectByCPCode(@Param("cpCode") String cpCode);

    List<CmsResourceDTO>  selectByFileUrl(@Param("fileUrls") Set<String> fileUrls);
}
