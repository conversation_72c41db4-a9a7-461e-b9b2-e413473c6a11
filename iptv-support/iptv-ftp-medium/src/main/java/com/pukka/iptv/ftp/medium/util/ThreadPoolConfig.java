package com.pukka.iptv.ftp.medium.util;

import org.springframework.beans.BeansException;
import org.springframework.beans.factory.DisposableBean;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.ApplicationContext;
import org.springframework.context.ApplicationContextAware;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;

import java.util.concurrent.Executor;
import java.util.concurrent.ThreadPoolExecutor;


/**
 * @title: 线程池配置类
 * @author: Charlene
 * @date: 2021/5/28 14:31
 */
@Configuration
public class ThreadPoolConfig implements ApplicationContextAware, DisposableBean {
    private ApplicationContext applicationContext;

    @Value("${ftp.medium.threadPool.core}")
    private int core;
    @Value("${ftp.medium.threadPool.max}")
    private int max;

    @Bean(name = "getNewOnlineContentExecutor")
    public Executor getNewOnlineContentExecutor() {
        ThreadPoolTaskExecutor threadPoolTaskExecutor = new ThreadPoolTaskExecutor();
        //核心线程数
        threadPoolTaskExecutor.setCorePoolSize(core);
        threadPoolTaskExecutor.setAllowCoreThreadTimeOut(true);
        //最大线程数
        threadPoolTaskExecutor.setMaxPoolSize(max);
        //配置线程池前缀
        threadPoolTaskExecutor.setThreadNamePrefix("get-newOnline-content-Thread-");
        //拒绝策略
        threadPoolTaskExecutor.setRejectedExecutionHandler(new ThreadPoolExecutor.AbortPolicy());
        threadPoolTaskExecutor.initialize();
        return threadPoolTaskExecutor;
    }

    @Bean(name = "getOfflineContentExecutor")
    public Executor getOfflineContentExecutor() {
        ThreadPoolTaskExecutor threadPoolTaskExecutor = new ThreadPoolTaskExecutor();
        //核心线程数
        threadPoolTaskExecutor.setCorePoolSize(core);
        threadPoolTaskExecutor.setAllowCoreThreadTimeOut(true);
        //最大线程数
        threadPoolTaskExecutor.setMaxPoolSize(max);
        //配置线程池前缀
        threadPoolTaskExecutor.setThreadNamePrefix("get-offline-content-Thread-");
        //拒绝策略
        threadPoolTaskExecutor.setRejectedExecutionHandler(new ThreadPoolExecutor.AbortPolicy());
        threadPoolTaskExecutor.initialize();
        return threadPoolTaskExecutor;
    }

    @Bean(name = "getNewOnlineContentTotalAsyncExecutor")
    public Executor getNewOnlineContentTotalAsyncExecutor() {
        ThreadPoolTaskExecutor threadPoolTaskExecutor = new ThreadPoolTaskExecutor();
        //核心线程数
        threadPoolTaskExecutor.setCorePoolSize(core);
        threadPoolTaskExecutor.setAllowCoreThreadTimeOut(true);
        //最大线程数
        threadPoolTaskExecutor.setMaxPoolSize(max);
        //配置线程池前缀
        threadPoolTaskExecutor.setThreadNamePrefix("get-newOnline-content-total-Thread-");
        //拒绝策略
        threadPoolTaskExecutor.setRejectedExecutionHandler(new ThreadPoolExecutor.AbortPolicy());
        threadPoolTaskExecutor.initialize();
        return threadPoolTaskExecutor;
    }


    @Override
    public void destroy() throws Exception {
        ThreadPoolTaskExecutor executor1 = applicationContext.getBean("getNewOnlineContentExecutor", ThreadPoolTaskExecutor.class);
        if (executor1 != null) {
            executor1.shutdown();
        }
        ThreadPoolTaskExecutor executor2 = applicationContext.getBean("getOfflineContentExecutor", ThreadPoolTaskExecutor.class);
        if (executor2 != null) {
            executor2.shutdown();
        }
        ThreadPoolTaskExecutor executor3 = applicationContext.getBean("getNewOnlineContentTotalAsyncExecutor", ThreadPoolTaskExecutor.class);
        if (executor3 != null) {
            executor3.shutdown();
        }
    }

    @Override
    public void setApplicationContext(ApplicationContext applicationContext) throws BeansException {
        this.applicationContext = applicationContext;
    }
}
