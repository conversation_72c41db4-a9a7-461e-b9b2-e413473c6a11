package com.pukka.iptv.ftp.medium.model;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

@Data
@EqualsAndHashCode
@JsonIgnoreProperties(value = { "handler" })
@TableName(value = "cms_resource",autoResultMap=true)
public class CmsResourceDTO {

    /**文件地址，ftp格式*/
    @TableField(value = "file_url")
    @ApiModelProperty(value="文件地址，ftp格式",dataType="String",name="fileUrl")
    private String fileUrl;

    @TableField(value = "cp_id")
    @ApiModelProperty(value="CP账号ID",dataType="Long",name="cpId")
    private Long cpId;


}
