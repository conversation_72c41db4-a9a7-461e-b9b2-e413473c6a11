<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.pukka.iptv.ftp.medium.mapper.FtpMediaMediumMapper">

<update id="batchUpdateDelSign">
    update ftp_media_medium
    <set>
        <if test="delSign != null">
            del_sign = #{delSign,jdbcType=INTEGER},
        </if>
        <if test="cpId != null">
            cp_id = #{cpId},
        </if>
        update_time = CURRENT_TIMESTAMP
    </set>
    <where>
        <trim prefix="AND id IN">
            <foreach collection="entityList" index="index" item="item" separator="," open="(" close=")">
                #{item.id,jdbcType=BIGINT}
            </foreach>
        </trim>
    </where>
    </update>
</mapper>