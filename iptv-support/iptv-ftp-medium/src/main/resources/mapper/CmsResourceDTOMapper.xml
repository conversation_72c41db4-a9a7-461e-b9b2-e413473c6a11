<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.pukka.iptv.ftp.medium.mapper.CmsResourceDTOMapper">
    <select id="selectByCPCode" resultType="com.pukka.iptv.ftp.medium.model.CmsResourceDTO">
        SELECT cr.*
        FROM cms_resource cr
                 LEFT JOIN sys_cp sc on sc.id = cr.cp_id
        <where>
            <if test="cpCode != null">
                AND sc.code = #{cpCode}
            </if>
        </where>
    </select>

    <select id="selectByFileUrl" resultType="com.pukka.iptv.ftp.medium.model.CmsResourceDTO">
        SELECT cr.*
        FROM cms_resource cr
        WHERE
        cr.file_url IN
        <foreach collection="fileUrls" item="url" index="index" open="(" close=")" separator=",">
            #{url}
        </foreach>
    </select>
</mapper>