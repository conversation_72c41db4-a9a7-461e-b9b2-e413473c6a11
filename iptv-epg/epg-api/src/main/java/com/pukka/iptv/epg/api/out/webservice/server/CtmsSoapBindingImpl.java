/**
 * CtmsSoapBindingImpl.java
 * <p>
 * This file was auto-generated from WSDL
 * by the Apache Axis 1.4 Apr 22, 2006 (06:55:48 PDT) WSDL2Java emitter.
 */

package com.pukka.iptv.epg.api.out.webservice.server;

import com.pukka.iptv.common.base.enums.ItemResultEnum;
import com.pukka.iptv.common.base.enums.PublishStatusEnum;
import com.pukka.iptv.common.core.util.DateUtils;
import com.pukka.iptv.common.core.util.SpringUtils;
import com.pukka.iptv.common.core.util.StringUtils;
import com.pukka.iptv.common.data.vo.epg.EpgOutOrderVo;
import com.pukka.iptv.common.rabbitmq.constans.IssueOrderConstant;
import lombok.extern.slf4j.Slf4j;
import org.springframework.amqp.rabbit.core.RabbitTemplate;

import java.net.MalformedURLException;


@Slf4j
public class CtmsSoapBindingImpl implements CSPResponse {


    private RabbitTemplate rabbitTemplate = SpringUtils.getBean("rabbitTemplate");

    @Override
    public CSPResult resultNotify(String CSPID, String LSPID, String correlateID, int cmdResult, String resultFileURL) throws java.rmi.RemoteException, MalformedURLException {
        CSPResult cspResult = new CSPResult();
        Integer resultCode = ItemResultEnum.Success.getValue();
        String description = null;
        //组装发送消息
        EpgOutOrderVo epgOutOrderVo = new EpgOutOrderVo();
        //20220728 添加下游反馈完成时间
        epgOutOrderVo.setFeedbackTime(DateUtils.getNowDate());
        epgOutOrderVo.setCspId(CSPID);
        epgOutOrderVo.setLspId(LSPID);
        epgOutOrderVo.setResultFileUrl(resultFileURL);
        epgOutOrderVo.setFileSetId(correlateID);
        try {
            // 处理失败
            if (cmdResult != ItemResultEnum.Success.getValue()) {
                epgOutOrderVo.setStatus(PublishStatusEnum.FAILPUBLISH.getCode());
                log.info("c1out-api反馈解析 ->>>>> 当前工单下游反馈失败,CSPID:{},LSPID:{},correlateID:{},cmdResult:{},resultFileURL:{}.", CSPID, LSPID, correlateID, cmdResult, resultFileURL);
                //工单结果为失败需要解析并读取错误描述
                if (StringUtils.isEmpty(resultFileURL)) {
                    description = "result error resultFileURL is null";
                    log.warn("c1out-api反馈解析 ->>>>> CSPID:{},LSPID:{},correlateID:{},cmdResult:{},resultFileURL:{}", CSPID, LSPID, correlateID, cmdResult, description);
                    epgOutOrderVo.setStatusDescription(description);
                }else{
                    description = "下游反馈失败";
                    epgOutOrderVo.setStatusDescription(description);
                }
            } else {
                epgOutOrderVo.setStatus(PublishStatusEnum.PUBLISH.getCode());
                epgOutOrderVo.setStatusDescription("下游反馈成功");
                log.info("c1out-api反馈解析 ->>>>> 当前工单下游反馈成功,CSPID:{},LSPID:{},correlateID:{},cmdResult:{},resultFileURL:{}.", CSPID, LSPID, correlateID, cmdResult, resultFileURL);
            }
            // 处理成功，发送消息
            this.rabbitTemplate.convertAndSend(IssueOrderConstant.OUT_EXCHANGE, IssueOrderConstant.OUT_BASE_REPORT_ROUTING, epgOutOrderVo);
            log.info("c1out-api反馈解析 ->>>>> 消息发送完毕...");
        } catch (Exception e) {
            log.error("c1out-api反馈解析 ->>>>> 信息获取或发送失败,CSPID:{},LSPID:{},correlateID:{},cmdResult:{},resultFileURL:{}.错误信息:{}", CSPID, LSPID, correlateID, cmdResult, resultFileURL, e);
            resultCode = ItemResultEnum.Fail.getValue();
        } finally {
            cspResult.setResult(resultCode);
            cspResult.setErrorDescription(description);
            return cspResult;
        }
    }

}
