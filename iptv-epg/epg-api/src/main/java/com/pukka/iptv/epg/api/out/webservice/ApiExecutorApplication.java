package com.pukka.iptv.epg.api.out.webservice;

import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.web.servlet.ServletComponentScan;
import org.springframework.cloud.openfeign.EnableFeignClients;

/**
 * <AUTHOR>
 * @date 2023/3/19
 */

@ServletComponentScan
@EnableFeignClients("com.pukka.iptv.common.api.feign")
@SpringBootApplication
public class ApiExecutorApplication {

	public static void main(String[] args) {
		SpringApplication.run(ApiExecutorApplication.class, args);
		System.out.println("ApiExecutorApplication 服务器启动完成");
	}

}
