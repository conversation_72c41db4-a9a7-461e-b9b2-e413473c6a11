package com.pukka.iptv.epg.api.out.webservice.controller;

import com.alibaba.fastjson.JSON;
import com.pukka.iptv.common.base.enums.SystemSourceEnums;
import com.pukka.iptv.common.base.vo.CommonResponse;
import com.pukka.iptv.common.core.util.IdUtils;
import com.pukka.iptv.common.data.model.EpgIssueOrder;
import com.pukka.iptv.common.rabbitmq.constans.IssueOrderConstant;
import com.pukka.iptv.common.rabbitmq.vo.MessageBody;
import io.swagger.annotations.Api;
import lombok.extern.slf4j.Slf4j;
import org.springframework.amqp.AmqpException;
import org.springframework.amqp.rabbit.connection.CorrelationData;
import org.springframework.amqp.rabbit.core.RabbitTemplate;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.Valid;
import java.util.Map;


/**
 * @Author: chiron
 * @Date: 2023/03/01/10:24
 * @Description: 发布接口
 */
@RestController
@RequestMapping(value = "/issue", produces = MediaType.APPLICATION_JSON_VALUE)
@Api(tags = "发布接口")
@Slf4j
public class IssueOrderController {

    @Autowired
    private RabbitTemplate rabbitTemplate;

    @PostMapping(value = "/order")
    public CommonResponse<Boolean> issueOrder(@RequestBody @Valid EpgIssueOrder issueOrder) {
        try {
            log.info("EPG发布接口 -----> 获取数据:{}",JSON.toJSONString(issueOrder));
            MessageBody message = new MessageBody();
            Map<String, Object> params = JSON.parseObject(JSON.toJSONString(issueOrder), Map.class);
            message.setType(SystemSourceEnums.EPG.getInfo());
            message.setId(IdUtils.simpleUUID());
            message.setMsg(params);
            CorrelationData correlationData = new CorrelationData(java.util.UUID.randomUUID().toString());
            rabbitTemplate.convertAndSend(IssueOrderConstant.OUT_EXCHANGE, IssueOrderConstant.OUT_BASE_ORDER_ROUTING, message, correlationData);
            log.info("EPG发布接口 -----> RabbitMQ EXCHANGE:{},ROUTING:{},信息:{} 消息发送完毕...",
                    IssueOrderConstant.OUT_EXCHANGE, IssueOrderConstant.OUT_BASE_ORDER_ROUTING, JSON.toJSONString(issueOrder));
            return CommonResponse.success(true);
        } catch (AmqpException exception) {
            log.error("EPG发布接口 -----> RabbitMQ EXCHANGE:{},ROUTING:{},信息:{} 消息发送失败!错误信息:{}",
                    IssueOrderConstant.OUT_EXCHANGE, IssueOrderConstant.OUT_BASE_ORDER_ROUTING, JSON.toJSONString(issueOrder), exception);
            return CommonResponse.success(false);
        }
    }

}
