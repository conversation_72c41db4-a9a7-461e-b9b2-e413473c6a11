server:
<<<<<<<< HEAD:iptv-downloader/downloader-server/src/main/resources/bootstrap-pre.yml
  port: 7004
========
  port: 7010
>>>>>>>> hotfix-RepairConfigFile:iptv-epg/epg-api/src/main/resources/bootstrap-pre.yml
  #servlet:
  #  context-path: /@project.artifactId@
#nacos服务地址及账号密码
nacos:
  server-addr: **************:8848
  namespace: pre
  username: nacos
  password: 6bUjznnWubxJm9GW1DcN
  group: iptv
  file-extension: yaml
  # app-name: ${spring.application.name}
hostname: ${HOSTNAME:logs}