<?xml version="1.0" encoding="UTF-8"?>
<deployment xmlns="http://xml.apache.org/axis/wsdd/" xmlns:java="http://xml.apache.org/axis/wsdd/providers/java">
    <globalConfiguration>
        <parameter name="sendMultiRefs" value="true"/>
        <parameter name="disablePrettyXML" value="true"/>
        <parameter name="adminPassword" value="admin"/>
        <parameter name="attachments.Directory" value=""/>
        <parameter name="dotNetSoapEncFix" value="true"/>
        <parameter name="enableNamespacePrefixOptimization" value="false"/>
        <parameter name="sendXMLDeclaration" value="true"/>
        <parameter name="sendXsiTypes" value="true"/>
        <parameter name="attachments.implementation" value="org.apache.axis.attachments.AttachmentsImpl"/>
        <requestFlow>
            <handler type="java:org.apache.axis.handlers.JWSHandler">
                <parameter name="scope" value="session"/>
            </handler>
            <handler type="java:org.apache.axis.handlers.JWSHandler">
                <parameter name="scope" value="request"/>
                <parameter name="extension" value=".jwr"/>
            </handler>
        </requestFlow>
    </globalConfiguration>
    <handler name="URLMapper" type="java:org.apache.axis.handlers.http.URLMapper"/>
    <handler name="LocalResponder" type="java:org.apache.axis.transport.local.LocalResponder"/>
    <handler name="Authenticate" type="java:org.apache.axis.handlers.SimpleAuthenticationHandler"/>

    <service name="ctms" provider="java:RPC" style="rpc" use="encoded">      <parameter name="wsdlTargetNamespace" value="iptv"/>
        <parameter name="wsdlServiceElement" value="CSPResponseService"/>
        <parameter name="schemaUnqualified" value="iptv"/>
        <parameter name="wsdlServicePort" value="ctms"/>
        <parameter name="className" value="com.pukka.iptv.epg.api.out.webservice.server.CtmsSoapBindingSkeleton"/>
        <parameter name="wsdlPortType" value="CSPResponse"/>
        <parameter name="typeMappingVersion" value="1.2"/>
        <parameter name="allowedMethods" value="*"/>

        <typeMapping
                xmlns:ns="iptv"
                qname="ns:CSPResult"
                type="java:com.pukka.iptv.epg.api.out.webservice.server.CSPResult"
                serializer="org.apache.axis.encoding.ser.BeanSerializerFactory"
                deserializer="org.apache.axis.encoding.ser.BeanDeserializerFactory"
                encodingStyle="http://schemas.xmlsoap.org/soap/encoding/"
        />
    </service>

    <transport name="http">
        <requestFlow>
            <handler type="URLMapper"/>
            <handler type="java:org.apache.axis.handlers.http.HTTPAuthHandler"/>
        </requestFlow>
        <parameter name="qs:list" value="org.apache.axis.transport.http.QSListHandler"/>
        <parameter name="qs:wsdl" value="org.apache.axis.transport.http.QSWSDLHandler"/>
        <parameter name="qs.list" value="org.apache.axis.transport.http.QSListHandler"/>
        <parameter name="qs.method" value="org.apache.axis.transport.http.QSMethodHandler"/>
        <parameter name="qs:method" value="org.apache.axis.transport.http.QSMethodHandler"/>
        <parameter name="qs.wsdl" value="org.apache.axis.transport.http.QSWSDLHandler"/>
    </transport>
    <transport name="local">
        <responseFlow>
            <handler type="LocalResponder"/>
        </responseFlow>
    </transport>
</deployment>