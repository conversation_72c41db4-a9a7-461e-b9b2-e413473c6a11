<?xml version="1.0" encoding="UTF-8"?>

<configuration>
    <springProperty scope="context" name="logback.level" source="logback.level" defaultValue="INFO"/>
    <springProperty scope="context" name="spring.application.name" source="spring.application.name" />
    <springProperty scope="context" name="spring.rabbitmq.addresses" source="spring.rabbitmq.addresses" />
    <springProperty scope="context" name="spring.rabbitmq.username" source="spring.rabbitmq.username" />
    <springProperty scope="context" name="spring.rabbitmq.password" source="spring.rabbitmq.password"  />
    <springProperty scope="context" name="spring.rabbitmq.virtual-host" source="spring.rabbitmq.virtual-host" />
    <springProperty scope="context" name="log.file.path" source="log.file.path" />
    <springProperty scope="context" name="log.file.name" source="log.file.name" />
    <springProperty scope="context" name="log.file.suffix" source="log.file.suffix" />
    <springProperty scope="context" name="log.file.compress.suffix" source="log.file.compress.suffix" />
    <!--<property name="log.file.path" value="/data/iptv-cloud/applogs/${spring.application.name}/${HOSTNAME}"/>-->

    <!--获取IP地址-->
    <conversionRule conversionWord="ip" converterClass="com.pukka.iptv.common.base.util.LogIpUtil"/>
    <!--<conversionRule conversionWord="host" converterClass="com.pukka.iptv.common.core.util.HostUtil"/>-->
    <!-- 彩色日志格式 -->
    <property name="CONSOLE_LOG_PATTERN"
              value="${CONSOLE_LOG_PATTERN:-%clr(%d{yyyy-MM-dd HH:mm:ss.SSS}){faint} %clr(${LOG_LEVEL_PATTERN:-%5p}) %clr(${PID:- }){magenta} %clr(---){faint} %clr([%15.15t]){faint} %clr(%-40.40logger{39}){cyan} %clr(:){faint} %m%n${LOG_EXCEPTION_CONVERSION_WORD:-%wEx}}"/>
<!--  <property name="AMQP_LOG_PATTERN"
              value="[%-5level] %d{${DATETIME}} [%thread] [%ip] %logger{36} %file:%line - %m%n"/>-->
    <!--格式化输出,%d:日期;%thread:线程名;%-5level：级别,从左显示5个字符宽度;%msg:日志消息;%n:换行符-->
    <property name="AMQP_LOG_PATTERN"
              value="[%-5level] %d{yyyy-MM-dd HH:mm:ss} [%thread] [%ip] %logger{36} %file:%line - %m%n"/>
    <!-- 彩色日志依赖的渲染类 -->
    <conversionRule conversionWord="clr" converterClass="org.springframework.boot.logging.logback.ColorConverter"/>
    <conversionRule conversionWord="wex"
                    converterClass="org.springframework.boot.logging.logback.WhitespaceThrowableProxyConverter"/>
    <conversionRule conversionWord="wEx"
                    converterClass="org.springframework.boot.logging.logback.ExtendedWhitespaceThrowableProxyConverter"/>
    <!-- Console log output -->
    <appender name="console" class="ch.qos.logback.core.ConsoleAppender">
        <encoder>
            <!--<pattern>%red(%d{yyyy-MM-dd HH:mm:ss}) %green([%thread]) [%class:%line] %highlight(%-5level) - %cyan(%msg%n)</pattern>-->
            <!--<pattern>%d{${DATETIME}} [%-5level] %logger{36} %file:%line - %msg%n</pattern>-->
            <pattern>${CONSOLE_LOG_PATTERN}</pattern>
            <charset>UTF-8</charset>
        </encoder>
        <filter class="ch.qos.logback.classic.filter.ThresholdFilter">
            <!--<level>DEBUG</level>-->
        </filter>
    </appender>
    <appender name="amqp" class="org.springframework.amqp.rabbit.logback.AmqpAppender">
        <encoder>
            <pattern>${AMQP_LOG_PATTERN}</pattern>
        </encoder>
        <addresses>${spring.rabbitmq.addresses}</addresses>
        <username>${spring.rabbitmq.username}</username>
        <password>${spring.rabbitmq.password}</password>
        <applicationId>${spring.application.name}</applicationId>
        <declareExchange>true</declareExchange>
        <exchangeType>topic</exchangeType>
        <exchangeName>log.exchange.topic</exchangeName>
        <virtualHost>${spring.rabbitmq.virtual-host}</virtualHost>
        <routingKeyPattern>*.${spring.application.name}</routingKeyPattern>
        <durable>false</durable>
        <generateId>true</generateId>
        <charset>UTF-8</charset>
        <deliveryMode>NON_PERSISTENT</deliveryMode>
        <autoDelete>false</autoDelete>
    </appender>
    <!-- Log file debug output -->
    <appender name="${log.file.name}" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <file>${log.file.path}${log.file.name}.${log.file.suffix}</file>
        <!-- <rollingPolicy class="ch.qos.logback.core.rolling.SizeAndTimeBasedRollingPolicy">
             <fileNamePattern>${log.file.path}/%d{yyyy-MM, aux}/debug.%d{yyyy-MM-dd}.%i.log.gz</fileNamePattern>
             <maxFileSize>50MB</maxFileSize>&ndash;&gt;
             <maxHistory>30</maxHistory>
         </rollingPolicy>-->
        <rollingPolicy class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">
            <fileNamePattern>${log.file.path}${log.file.name}.%d{yyyy-MM-dd}.${log.file.suffix}.${log.file.compress.suffix}</fileNamePattern>
            <maxHistory>30</maxHistory>
        </rollingPolicy>
        <encoder>
            <pattern>%date [%thread] %-5level [%logger{50}] %file:%line - %msg%n</pattern>
            <charset>UTF-8</charset>
        </encoder>
        <filter class="ch.qos.logback.classic.filter.ThresholdFilter">
           <!-- <level>DEBUG</level>-->
            <!-- 匹配时的操作：接收（记录） -->
            <!--<onMatch>ACCEPT</onMatch>-->
            <!-- 不匹配时的操作：拒绝（不记录） -->
            <!--<onMismatch>DENY</onMismatch>-->
        </filter>
    </appender>

    <!-- Log file error output -->
    <!-- <appender name="error" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <file>${log.file.path}/error.log</file>-->
    <!--<rollingPolicy class="ch.qos.logback.core.rolling.SizeAndTimeBasedRollingPolicy">
        <fileNamePattern>${log.file.path}/%d{yyyy-MM}/error.%d{yyyy-MM-dd}.%i.log.gz</fileNamePattern>
        &lt;!&ndash;<maxFileSize>50MB</maxFileSize>&ndash;&gt;
        <maxHistory>30</maxHistory>
    </rollingPolicy>-->
    <!--<rollingPolicy class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">
        <fileNamePattern>${log.file.path}/error.%d{yyyy-MM-dd}.log.gz</fileNamePattern>
        <maxHistory>30</maxHistory>
    </rollingPolicy>
    <encoder>
        <pattern>%date [%thread] %-5level [%logger{50}] %file:%line - %msg%n</pattern>
    </encoder>
    <filter class="ch.qos.logback.classic.filter.ThresholdFilter">
        <level>ERROR</level>
    </filter>
</appender> -->

    <!--nacos 心跳 INFO 屏蔽-->
    <!--<logger name="com.alibaba.nacos" level="OFF">
        <appender-ref ref="info"/>
    </logger>-->
    <!--<springProfile name="dev">
        <logger name="com.pukka.iptv.manage.mapper" level="debug"/>
    </springProfile>-->
    <!--<logger name="com.pukka.iptv.manage.mapper" level="debug" additivity="false">
        <appender-ref ref="console"/>
    </logger>-->
  <!--  <logger name="org.apache.ibatis" level="TRACE"/>
    <logger name="java.sql.Connection" level="DEBUG"/>
    <logger name="java.sql.Statement" level="DEBUG"/>
    <logger name="java.sql.PreparedStatement" level="DEBUG"/>-->
    <!--<logger name="com.pukka.iptv.manage.mapper" level="debug" additivity="false">
        &lt;!&ndash; 追加到日志文件中,文件名与上文定义的日志文件名要一致&ndash;&gt;
        <appender-ref ref="console"/>
        <appender-ref ref="info" />
    </logger>-->

    <!-- Level: FATAL 0  ERROR 3  WARN 4  INFO 6  DEBUG 7 -->
    <root level="${logback.level}">
        <appender-ref ref="amqp"/>
        <appender-ref ref="console"/>
        <appender-ref ref="${log.file.name}"/>
        <!-- <appender-ref ref="error"/>-->
    </root>
</configuration>
