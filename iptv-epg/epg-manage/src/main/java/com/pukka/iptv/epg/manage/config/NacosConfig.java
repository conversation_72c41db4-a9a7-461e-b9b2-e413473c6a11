package com.pukka.iptv.epg.manage.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.context.annotation.Configuration;

@Data
@Configuration
@RefreshScope
@ConfigurationProperties(prefix = "spring.epg.nacos.config")
public class NacosConfig
{
    /**
     * 是否刷新
     */
    private boolean isrefresh;

    /**
     * 间隔时间
     */
    private String interval;
}
