package com.pukka.iptv.epg.manage.service.push;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.pukka.iptv.common.base.enums.PublishStatusEnum;
import com.pukka.iptv.common.base.enums.StatusEnum;
import com.pukka.iptv.common.core.util.DateUtils;
import com.pukka.iptv.common.data.model.epg.EpgOutOrder;
import com.pukka.iptv.epg.manage.model.PolymericData;
import com.pukka.iptv.epg.manage.service.out.EpgOutOrderService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;

/**
 * @Author: chiron
 * @Date: 2023/04/21/11:03
 * @Description:
 */
@Component
@Slf4j
public class PushDatabaseService {

    @Autowired
    private EpgOutOrderService epgOutOrderService;

    /**
     * 入库
     *
     * @param polymericData
     * @return
     */
    public Boolean pushDatabase(PolymericData polymericData) {
        List<EpgOutOrder> epgOutOrders = new ArrayList<>();
        LambdaQueryWrapper<EpgOutOrder> lambdaQueryWrapper = new LambdaQueryWrapper<EpgOutOrder>()
                .eq(EpgOutOrder::getStatus, PublishStatusEnum.WAITPUBLISH.getCode())
                .le(EpgOutOrder::getCreateTime, DateUtils.getBeforeDateDay(30));
        switch (polymericData.getOperateObjectEnum()) {
            case EPG_NODE:
                //查询关联epg_out_order表一个月之前的数据数据
                epgOutOrders = epgOutOrderService.list(
                        lambdaQueryWrapper.in(EpgOutOrder::getNodeId, polymericData.getIdList()));
                break;
            case EPG_FILE:
                //查询关联epg_out_order表一个月之前的数据数据
                epgOutOrders = epgOutOrderService.list(
                        lambdaQueryWrapper.in(EpgOutOrder::getEpgFileId, polymericData.getIdList()));
                break;
            case EPG_TEMPLATE:
                //查询关联epg_out_order表一个月之前的数据数据
                epgOutOrders = epgOutOrderService.list(
                        lambdaQueryWrapper.in(EpgOutOrder::getTemplateId, polymericData.getIdList()));
                break;
            default:
                log.error("当前:{}.入库失败，未知业务类型", polymericData);
                return false;
        }
        if (CollectionUtils.isNotEmpty(epgOutOrders)) {
            try {
                epgOutOrders.stream().forEach(epgOutOrder -> epgOutOrder.setStatus(StatusEnum.DELETE.getCode()));
                epgOutOrderService.updateBatchById(epgOutOrders);
            } catch (Exception e) {
                log.error("删除epg_file表数据时，关联epg_out_order表数据删除失败，错误信息：{}", e.getMessage());
                return false;
            }
        }
        log.info("入库成功");
        return true;
    }
}
