package com.pukka.iptv.epg.manage.utils;

import com.pukka.iptv.common.base.constant.ObjectsTypeConstants;
import com.pukka.iptv.common.core.constant.SymbolConstant;
import com.pukka.iptv.common.core.util.SafeUtil;
import com.pukka.iptv.common.rabbitmq.constans.IssueOrderConstant;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.springframework.amqp.core.BindingBuilder;
import org.springframework.amqp.core.DirectExchange;
import org.springframework.amqp.core.Queue;
import org.springframework.amqp.rabbit.core.RabbitAdmin;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.Map;

/**
 * @Author: chiron
 * @Date: 2022/09/23/09:22
 * @Description:
 */
@Slf4j
@Component
public class InitializeQueueUtil {

    @Autowired
    private RabbitAdmin rabbitAdmin;

    /**
     * 自动创建分发优先级队列
     *
     * @param code 队列code
     */
    public void initializeBinding(String code) {
        if (ObjectUtils.isEmpty(code)) {
            log.error("节点通道 -----> 自动创建分发优先级队列失败,传入参数code为空!");
            return;
        }
        Map<String, Object> args = new HashMap<String, Object>();
        args.put(ObjectsTypeConstants.PRIORITY_QUEUE, 10);
        rabbitAdmin.declareQueue(
                new Queue(IssueOrderConstant.OUT_ORDER_SEND_QUEUE + SymbolConstant.UNDER_SCORE + SafeUtil.getString(code)
                        , true, false, false, args));
        rabbitAdmin.declareBinding(BindingBuilder.bind(new Queue(IssueOrderConstant.OUT_ORDER_SEND_QUEUE + SymbolConstant.UNDER_SCORE + SafeUtil.getString(code))
        ).to(new DirectExchange(IssueOrderConstant.OUT_EXCHANGE)).with(IssueOrderConstant.OUT_ORDER_SEND_ROUTING + SymbolConstant.UNDER_SCORE + SafeUtil.getString(code)));
    }
}
