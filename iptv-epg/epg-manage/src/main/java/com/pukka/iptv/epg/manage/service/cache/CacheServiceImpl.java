package com.pukka.iptv.epg.manage.service.cache;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.pukka.iptv.common.base.constant.RedisKeyConstants;
import com.pukka.iptv.common.base.enums.ContentStatusEnum;
import com.pukka.iptv.common.data.model.epg.*;
import com.pukka.iptv.common.data.model.sys.SysNat;
import com.pukka.iptv.common.redis.service.RedisService;
import com.pukka.iptv.epg.manage.mapper.out.EpgOutOrderMapper;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.time.Duration;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.util.List;
import java.util.Map;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @Date 2021/10/15 11:24
 * @Description
 */
@Slf4j
@Component
public class CacheServiceImpl {

    @Autowired
    private RedisService redisService;

    @Autowired
    private EpgOutOrderMapper epgOutOrderMapper;

    /**
     * 缓存EpgNode
     *
     * @param nodeList
     * @return
     */
    public boolean cacheEpgNode(List<EpgNode> nodeList) {
        try {
            Map<String, EpgNode> epgNodeMap = nodeList.stream()
                    .collect(Collectors.toMap(epgNode -> String.valueOf(epgNode.getLspId()), epgNode -> epgNode, (val1, val2) -> val2));
            redisService.setCacheMap(RedisKeyConstants.EPG_NODE_KEY, epgNodeMap);
            return true;
        } catch (Exception exception) {
            log.error("redis插入EpgNode失败:{}", exception.getMessage());
            return false;
        }
    }

    /**
     * 缓存SysRoleMenu
     *
     * @param sysRoleMenuList
     * @return
     */
    public boolean cacheSysRoleMenu(List<SysRoleMenu> sysRoleMenuList) {
        try {
            Map<String, SysRoleMenu> sysRoleMenuMap = sysRoleMenuList.stream()
                    .collect(Collectors.toMap(sysRoleMenu -> String.valueOf(sysRoleMenu.getRoleId()), sysRoleMenu -> sysRoleMenu, (val1, val2) -> val2));
            redisService.setCacheMap(RedisKeyConstants.SYS_ROLE_MENU_KEY, sysRoleMenuMap);
            return true;
        } catch (Exception exception) {
            log.error("redis插入SysRole失败:{}", exception.getMessage());
            return false;
        }
    }

    /**
     * 缓存EpgOutOrder
     *
     * @param epgTemplateList
     * @return
     */
    public boolean cacheEpgTemplate(List<EpgTemplate> epgTemplateList) {
        try {
            Map<String, EpgTemplate> epgTemplateMap = epgTemplateList.stream()
                    .collect(Collectors.toMap(epgTemplate -> String.valueOf(epgTemplate.getId()), epgTemplate -> epgTemplate, (val1, val2) -> val2));
            redisService.setCacheMap(RedisKeyConstants.EPG_TEMPLATE_KEY, epgTemplateMap);
            return true;
        } catch (Exception exception) {
            log.error("redis插入EpgTemplate失败:{}", exception.getMessage());
            return false;
        }
    }

    /**
     * 缓存AtomicInteger
     *
     * @return
     */
    public boolean cacheAtomicInteger() {
        try {
            redisService.setCacheObject(RedisKeyConstants.ATOMICINTEGER_KEY, 1);
            return true;
        } catch (Exception exception) {
            log.error("redis插入AtomicInteger失败:{}", exception.getMessage());
            return false;
        }
    }

    /**
     * 缓存事件表总行数
     *
     * @param aLong
     * @return
     */
    public boolean cacheOutOrderTotal(Long aLong) {
        try {
            if (ObjectUtils.isNotEmpty(aLong)) {
                redisService.setCacheObject(RedisKeyConstants.OUTORDERTOTAL_KEY, aLong, 4L, TimeUnit.HOURS);
            } else {
                log.warn("redis插入OutOrderTotal,获取总行数为空");
            }
            return true;
        } catch (Exception exception) {
            log.error("redis插入OutOrderTotal失败:{}", exception.getMessage());
            return false;
        }
    }

    /**
     * 获取事件表总行数
     *
     * @return
     */
    public Long getOutOrderTotal() {
        try {
            Long cacheObject = redisService.getCacheObject(RedisKeyConstants.OUTORDERTOTAL_KEY);
            if (ObjectUtils.isNotEmpty(cacheObject)) {
                return cacheObject;
            } else {
                //获取事件表总行数
                LambdaQueryWrapper<EpgOutOrder> epg = Wrappers.lambdaQuery(EpgOutOrder.class);
                Long aLong = epgOutOrderMapper.selectCount(epg);
                boolean b = cacheOutOrderTotal(aLong);
                log.warn("redis获取OutOrderTotal为:{}", aLong);
                return aLong;
            }
        } catch (Exception exception) {
            log.error("redis获取OutOrderTotal失败:{}", exception.getMessage());
            return 99999L;
        }
    }

    /**
     * 缓存EpgFile
     * @param fileList
     * @return
     */
    public boolean cacheEpgFile(List<EpgFile> fileList) {
        try {
            Map<String, EpgFile> epgFileMap = fileList.stream()
                    .collect(Collectors.toMap(epgFile -> String.valueOf(epgFile.getFileId()), epgFile -> epgFile, (val1, val2) -> val2));
            redisService.setCacheMap(RedisKeyConstants.EPG_FILE_KEY, epgFileMap);
            redisService.expire(RedisKeyConstants.EPG_FILE_KEY,timeOut(),TimeUnit.SECONDS);
            return true;
        } catch (Exception exception) {
            log.error("redis插入EpgFile失败:{}", exception.getMessage());
            return false;
        }
    }

    /**
     * 获取当前时间到凌晨的时间差 单位:秒
     * @return
     */
    private long timeOut() {
        LocalDate today = LocalDate.now();
        LocalDateTime todayMidnight = LocalDateTime.of(today, LocalTime.MIDNIGHT);
        LocalDateTime tomorrowMidnight = todayMidnight.plusDays(1);
        long seconds = TimeUnit.NANOSECONDS.toSeconds(Duration.between(LocalDateTime.now(), tomorrowMidnight).toNanos());
        return seconds;
    }

    /**
     * 缓存SysNat
     * @param sysNatList
     * @return
     */
    public boolean setSysNat(List<SysNat> sysNatList) {
        try {
            Map<String, SysNat> dictionaryBaseMap = sysNatList.stream().filter(sysNat -> sysNat.getStatus().equals(ContentStatusEnum.EFFECTIVE.getCode())).collect(Collectors.toMap(sysNat -> sysNat.getNatSource(), sysNat -> sysNat, (a, b) -> b));
            redisService.setCacheMap(RedisKeyConstants.EPG_NAT_KEY, dictionaryBaseMap);
            return true;
        } catch (Exception exception) {
            log.error("redis插入SysNat失败，", exception);
            return false;
        }
    }
}
