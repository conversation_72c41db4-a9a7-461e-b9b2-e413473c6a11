package com.pukka.iptv.epg.manage.model;

import com.pukka.iptv.common.log.enums.OperateObjectEnum;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;
import lombok.extern.slf4j.Slf4j;

import java.util.List;

/**
 * @Author: chiron
 * @Date: 2023/04/21/10:36
 * @Description:
 */
@Slf4j
@NoArgsConstructor
@Data
@Accessors(chain = true)
public class PolymericData {
    /**
     * 数据库表ID集合
     */
    public List<String> idList;
    /**
     * 操作对象枚举
     */
    public OperateObjectEnum operateObjectEnum;
}
