package com.pukka.iptv.epg.manage.mapper.sys;


import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.pukka.iptv.common.data.model.epg.SysUserTemplate;
import com.pukka.iptv.common.data.vo.epg.SysUserTemplateVo;
import com.pukka.iptv.common.data.vo.req.SysUserTemplateReq;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 *
 * @author: zhengcl
 * @date: 2021-8-17 16:48:36
 */

@Mapper
public interface SysUserTemplateMapper extends BaseMapper<SysUserTemplate> {

    Page<SysUserTemplateVo> pageUserByTemplateId(@Param("page") Page page, @Param("sysUserTemplateReq") SysUserTemplateReq sysUserTemplateReq);

    List<Long> selectByUserId(@Param("userId") Long userId);
}
