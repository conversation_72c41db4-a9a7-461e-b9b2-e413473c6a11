package com.pukka.iptv.epg.manage.service.out.impl;

import com.alibaba.nacos.common.utils.UuidUtils;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.pukka.iptv.common.base.constant.RedisKeyConstants;
import com.pukka.iptv.common.base.enums.CommonResponseEnum;
import com.pukka.iptv.common.base.enums.PublishStatusEnum;
import com.pukka.iptv.common.base.enums.StatusEnum;
import com.pukka.iptv.common.base.exception.CommonResponseException;
import com.pukka.iptv.common.base.util.UUID;
import com.pukka.iptv.common.core.util.DateUtils;
import com.pukka.iptv.common.data.dto.RetrieveFileEntity;
import com.pukka.iptv.common.data.model.epg.EpgFile;
import com.pukka.iptv.common.data.model.epg.EpgOutOrder;
import com.pukka.iptv.common.data.vo.epg.ResultUploadVo;
import com.pukka.iptv.common.data.vo.req.EpgFileReq;
import com.pukka.iptv.common.log.enums.OperateObjectEnum;
import com.pukka.iptv.common.rabbitmq.constans.IssueOrderConstant;
import com.pukka.iptv.common.redis.service.RedisService;
import com.pukka.iptv.epg.manage.mapper.out.EpgFileMapper;
import com.pukka.iptv.epg.manage.mapper.out.EpgOutOrderMapper;
import com.pukka.iptv.epg.manage.mapper.out.EpgTemplateMapper;
import com.pukka.iptv.epg.manage.model.PolymericData;
import com.pukka.iptv.epg.manage.service.out.EpgFileService;
import com.pukka.iptv.epg.manage.service.out.EpgOutOrderService;
import com.pukka.iptv.epg.manage.utils.downloadUtils.FtpAnalysisUtil;
import com.pukka.iptv.epg.manage.utils.downloadUtils.FtpClientService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.amqp.rabbit.core.RabbitTemplate;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.io.UnsupportedEncodingException;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.time.Duration;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * @Author: chiron
 * @Date: 2023/03/02/09:48
 * @Description:
 */
@Service
@Slf4j
public class EpgFileServiceImpl extends ServiceImpl<EpgFileMapper, EpgFile> implements EpgFileService {
    @Autowired
    private RabbitTemplate rabbitTemplate;
    @Autowired
    private EpgOutOrderService epgOutOrderService;
    @Autowired
    private EpgFileMapper epgFileMapper;
    @Autowired
    private FtpClientService ftpClientService;
    @Autowired
    private RedisService redisService;
    @Autowired
    private EpgOutOrderMapper epgOutOrderMapper;
    @Autowired
    private FtpAnalysisUtil ftpAnalysisUtil;
    @Value("${spring.epg.storage.out-ftp-prefix}")
    private String prefix;
    @Value("${spring.epg.storage.read-write.out-file.account}")
    private String username;
    @Value("${spring.epg.storage.read-write.out-file.pwd}")
    private String password;
    /**
     * 分页
     *
     * @param page
     * @param epgFilesReq
     * @return
     */
    @Override
    public Page<EpgFile> pageByQuery(Page page, EpgFileReq epgFilesReq) {
        //时间筛选默认为当天时间
        try {
            dateChange(epgFilesReq);
        } catch (ParseException e) {
            e.printStackTrace();
        }

        return this.page(page, Wrappers.<EpgFile>lambdaQuery().like(StringUtils.isNotBlank(epgFilesReq.getShowName()), EpgFile::getShowName, epgFilesReq.getShowName())
                .ne(EpgFile::getStatus, StatusEnum.DELETE.getCode())
                .between(StringUtils.isNotBlank(epgFilesReq.getStartTime()), EpgFile::getCreateTime, epgFilesReq.getStartTime(), epgFilesReq.getEndTime())
                .orderByDesc(EpgFile::getCreateTime));
/*        //时间筛选默认为当天时间
        try {
            dateChange(epgFilesVo);
        } catch (ParseException e) {
            e.printStackTrace();
        }

        return this.page(page, Wrappers.<EpgFiles>lambdaQuery().like(StringUtils.isNotBlank(epgFilesVo.getFileName()),EpgFiles::getShowName,epgFilesVo.getFileName())
                .between(EpgFiles::getCreateTime,epgFilesVo.getStartTime(),epgFilesVo.getEndTime())
                .orderBy("1".equals(epgFilesVo.getSortKey()),"asc".equals(epgFilesVo.getSortValue()),EpgFiles::getCreateTime));*/
    }

    /**
     * 下载文件
     *
     * @param filePath
     * @param response
     * @return
     */
    @Override
    public Boolean retrieveFile(String filePath, HttpServletResponse response) {
        //判断文件是否在ftp存在
        if (!ftpAnalysisUtil.IsExistPath(filePath)) {
            throw new CommonResponseException(CommonResponseEnum.FAIL, "文件在ftp中不存在");
        }
        try {
            //从地址中获取文件名
            String fileName = filePath.substring(filePath.lastIndexOf("/") + 1);
            RetrieveFileEntity retrieveFile = new RetrieveFileEntity().setFileName(fileName)
                    .setFileUrl(filePath).setResponse(response);
            Boolean aBoolean = ftpClientService.readFtpStream(retrieveFile);
            if (!aBoolean) {
                log.warn("下载文件 -----> 文件ftp信息:{},检索文件并下载失败!", filePath);
                return false;
            }
        } catch (Exception exception) {
            log.error("下载文件 -----> 失败,错误信息:{}", exception);
            return false;
        }
        return true;
    }

    /**
     * 更新
     *
     * @param epgFile
     * @return
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean updateWithId(EpgFile epgFile) {
        if (StringUtils.isBlank(epgFile.getFilePath())) {
            throw new CommonResponseException(CommonResponseEnum.ERROR_PARAMETER, "源文件URL地址不能为空");
        }
        if (epgFile.getFilePath().lastIndexOf(".") == -1) {
            throw new CommonResponseException(CommonResponseEnum.ERROR_PARAMETER, "源文件URL地址格式错误");
        }
        if (epgFile.getFilePath().lastIndexOf("/") == -1) {
            throw new CommonResponseException(CommonResponseEnum.ERROR_PARAMETER, "源文件URL地址格式错误");
        }
        if (!epgFile.getFilePath().startsWith("/")){
            throw new CommonResponseException(CommonResponseEnum.ERROR_PARAMETER, "源文件URL地址格式错误");
        }
        if (epgFile.getFilePath().substring(1,2).contains("/")){
            throw new CommonResponseException(CommonResponseEnum.ERROR_PARAMETER, "源文件URL地址格式错误");
        }
        String suffix = epgFile.getFilePath().substring(epgFile.getFilePath().lastIndexOf("."));
        //判断上传文件后缀是否为.tar
        if (!".tar".equals(suffix)) {
            throw new CommonResponseException(CommonResponseEnum.FAIL, "源文件URL地址格式错误");
        }
        String[] split = epgFile.getFilePath().split("/");
        boolean flag = false;
        for (String s : split) {
            if (s.startsWith(" ") || s.endsWith(" ")) {
                flag = true;
            }
        }
        if (flag) {
            throw new CommonResponseException(CommonResponseEnum.ERROR_PARAMETER, "源文件URL地址格式错误");
        }
        //从ftp地址中获取文件名
        String fileName = epgFile.getFilePath().substring(epgFile.getFilePath().lastIndexOf("/") + 1);
        epgFile.setShowName(fileName);
        //如果修改filepath去发布表修改filename
        Long selectPathCount = epgFileMapper.selectCount(Wrappers.<EpgFile>lambdaQuery().eq(EpgFile::getFilePath, epgFile.getFilePath()).ne(EpgFile::getStatus, StatusEnum.DELETE.getCode()));
        //存入数据库
        boolean b = this.updateById(epgFile);
        EpgFile epgFile1 = this.getById(epgFile.getId());
        if (!(selectPathCount!=null && selectPathCount > 0)){
            epgOutOrderService.update(Wrappers.<EpgOutOrder>lambdaUpdate()
//                    .set(EpgOutOrder::getEpgFileName,fileName)
                    .set(EpgOutOrder::getEpgFilePath,epgFile.getFilePath())
                    .eq(EpgOutOrder::getEpgFileId,epgFile1.getFileId()));
        }
        Date createTime = epgFile1.getCreateTime();
        //存进缓存
        if (b && isNow(createTime)) {
            redisService.setCacheHash(RedisKeyConstants.EPG_FILE_KEY, String.valueOf(epgFile1.getFileId()), epgFile1);
            redisService.expire(RedisKeyConstants.EPG_FILE_KEY, timeOut(), TimeUnit.SECONDS);
        }
        return b;
    }

    private boolean isNow(Date createTime) {
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
        Date date = new Date();
        String c = sdf.format(createTime);
        String d = sdf.format(date);
        return c.equals(d);
    }

    /**
     * 删除
     *
     * @param id
     * @return
     */
    @Override
    public Boolean deleteById(Long id) {
        //判断传入id是否存在
        EpgFile epgFile = epgFileMapper.selectById(id);
        if (epgFile == null) {
            throw new CommonResponseException(CommonResponseEnum.DATA_EMPTY);
        }
        //删除
        EpgFile epgFile1 = new EpgFile();
        epgFile1.setId(id);
        epgFile1.setStatus(StatusEnum.DELETE.getCode());
        return this.updateById(epgFile1);
    }

    /**
     * 批量删除
     *
     * @param ids
     * @return
     */
    @Override
    public Boolean deleteByIds(List<Long> ids) {
        //判断参数
        if (ids == null || ids.size() == 0) {
            return false;
        }
        List<EpgFile> epgFiles = epgFileMapper.selectBatchIds(ids);
        epgFiles.stream().forEach(epgFile -> epgFile.setStatus(StatusEnum.DELETE.getCode()));
        boolean b = this.updateBatchById(epgFiles);
        List<EpgFile> collect = epgFiles.stream().filter(epgFile -> isNow(epgFile.getCreateTime())).collect(Collectors.toList());
        if (b && CollectionUtils.isNotEmpty(collect)){
            redisService.delCacheMapKey(RedisKeyConstants.EPG_FILE_KEY,collect.stream().map(EpgFile::getFileId).toArray(String[]::new));
        }
        List<String> fileIds = epgFiles.stream().map(EpgFile::getFileId).collect(Collectors.toList());
        //查询关联epg_out_order表一个月内的数据数据
        List<EpgOutOrder> epgOutOrders = epgOutOrderService.list(new LambdaQueryWrapper<EpgOutOrder>()
                .eq(EpgOutOrder::getStatus, PublishStatusEnum.WAITPUBLISH.getCode())
                .in(EpgOutOrder::getEpgFileId, fileIds)
                .ge(EpgOutOrder::getCreateTime, DateUtils.getBeforeDateDay(30)));
        if (CollectionUtils.isNotEmpty(epgOutOrders)) {
            try {
                epgOutOrders.stream().forEach(epgOutOrder -> epgOutOrder.setStatus(StatusEnum.DELETE.getCode()));
                epgOutOrderService.updateBatchById(epgOutOrders);
            } catch (Exception e) {
                log.error("删除文件数据时，关联epg_out_order表数据删除失败，错误信息：{}", e.getMessage());
                throw new CommonResponseException(CommonResponseEnum.FAIL, "删除文件数据时，关联事件表数据删除失败");
            }
        }
        //推送rabbitmq队列
        try {
            PolymericData polymericData = new PolymericData();
            polymericData.setIdList(fileIds).setOperateObjectEnum(OperateObjectEnum.EPG_FILE);
            rabbitTemplate.convertAndSend(IssueOrderConstant.OUT_EXCHANGE, IssueOrderConstant.EPG_WAREHOUSING_ROUTING, polymericData);
        } catch (Exception e) {
            log.error("删除文件数据时，推送rabbitmq队列失败，文件fileIds:{},错误信息：{}", fileIds, e.getMessage());
            throw new CommonResponseException(CommonResponseEnum.FAIL, "删除文件数据时，关联事件表数据删除失败");
        }
        return b;
    }

    @Override
    public List<EpgFile> fileList(String filePath) {
        Map<String, EpgFile> cacheMap = redisService.getCacheMap(RedisKeyConstants.EPG_FILE_KEY);
        List<EpgFile> collect = cacheMap.entrySet().stream().map(s -> new EpgFile(s.getKey(), s.getValue().getFilePath(), s.getValue().getStatus())).collect(Collectors.toList());
        List<EpgFile> list = collect.stream().filter(epgFile -> epgFile.getStatus() == 1).collect(Collectors.toList());
        if (!StringUtils.isNotBlank(filePath)) {
            return list;
        }
        List<EpgFile> list2 = list.stream().filter(epgFile -> epgFile.getFilePath().contains(filePath)).collect(Collectors.toList());
        return list2;
    }

    /**
     * 上传文件
     * @param file
     * @return
     */
    @Override
    public ResultUploadVo upload(MultipartFile file) throws Exception {
        long fileSize = file.getSize();
        long limitSize = 157286400;
        if (fileSize>limitSize){
            throw new CommonResponseException(CommonResponseEnum.FAIL, "文件大小超过150MB限制");
        }
        String filename = file.getOriginalFilename();
        String suffix = filename.substring(filename.lastIndexOf("."));
        //判断上传文件后缀是否为.tar
        if (!".tar".equals(suffix)) {
            throw new CommonResponseException(CommonResponseEnum.FAIL, "只支持上传tar类型文件");
        }
        String url = String.format("ftp://%s:%s@%s/", username, password, prefix);
        url = new String(url.getBytes("UTF-8"), "ISO-8859-1");
        String s = null;
        try {
            s = FtpClientService.fileUpByFtp(file,filename,url);
        } catch (IOException e) {
            e.printStackTrace();
        }
        if (StringUtils.isEmpty(s)) {
            throw new CommonResponseException("图片上传异常");
        }
        ResultUploadVo resultUploadVo = new ResultUploadVo();
        resultUploadVo.setFileType("file");
        resultUploadVo.setStatus("enabled");
        resultUploadVo.setId(UuidUtils.generateUuid());
        resultUploadVo.setUrl("/"+s);
        resultUploadVo.setFileName(s);
        return resultUploadVo;
    }

    /**
     * 新增
     *
     * @param epgFile
     * @return
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean increase(EpgFile epgFile) {
        if (StringUtils.isBlank(epgFile.getFilePath())) {
            throw new CommonResponseException(CommonResponseEnum.ERROR_PARAMETER, "源文件URL地址不能为空");
        }
        if (epgFile.getFilePath().lastIndexOf(".") == -1) {
            throw new CommonResponseException(CommonResponseEnum.ERROR_PARAMETER, "源文件URL地址格式错误");
        }
        if (epgFile.getFilePath().lastIndexOf("/") == -1) {
            throw new CommonResponseException(CommonResponseEnum.ERROR_PARAMETER, "源文件URL地址格式错误");
        }
        if (!epgFile.getFilePath().startsWith("/")){
            throw new CommonResponseException(CommonResponseEnum.ERROR_PARAMETER, "源文件URL地址格式错误");
        }
        if (epgFile.getFilePath().substring(1,2).contains("/")){
            throw new CommonResponseException(CommonResponseEnum.ERROR_PARAMETER, "源文件URL地址格式错误");
        }
        String suffix = epgFile.getFilePath().substring(epgFile.getFilePath().lastIndexOf("."));
        //判断上传文件后缀是否为.tar
        if (!".tar".equals(suffix)) {
            throw new CommonResponseException(CommonResponseEnum.FAIL, "源文件URL地址格式错误");
        }
        String startTime = getTodayStartTime();
        String endTime = getTodayEndTime();
        List<EpgFile> fileList = epgFileMapper.selectList(Wrappers.<EpgFile>lambdaQuery()
                .eq(EpgFile::getFilePath, epgFile.getFilePath())
                .eq(EpgFile::getStatus, StatusEnum.COME)
                .between(EpgFile::getCreateTime,startTime,endTime));
        if (CollectionUtils.isNotEmpty(fileList)){
            throw new CommonResponseException(CommonResponseEnum.FAIL, "文件名重复");
        }
        String[] split = epgFile.getFilePath().split("/");
        boolean flag = false;
        for (String s : split) {
            System.out.println(s);
            if (s.startsWith(" ") || s.endsWith(" ")) {
                flag = true;
            }
        }
        if (flag) {
            throw new CommonResponseException(CommonResponseEnum.ERROR_PARAMETER, "源文件URL地址格式错误");
        }
        //从ftp地址中获取文件名
        epgFile.setShowName(epgFile.getFilePath().substring(epgFile.getFilePath().lastIndexOf("/") + 1));
        boolean b = this.save(epgFile);
        EpgFile epgFile1 = this.getById(epgFile.getId());
        if (b) {
            redisService.setCacheHash(RedisKeyConstants.EPG_FILE_KEY, String.valueOf(epgFile1.getFileId()), epgFile1);
            redisService.expire(RedisKeyConstants.EPG_FILE_KEY, timeOut(), TimeUnit.SECONDS);
            System.out.println(epgFile.getFileId());
        }
        return b;
    }

    private String getTodayEndTime() {
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd 23:59:59");
        return sdf.format(new Date());
    }

    private String getTodayStartTime() {
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd 00:00:00");
            return sdf.format(new Date());
    }

    /**
     * 前端传入时间处理
     *
     * @param epgFilesReq
     * @throws ParseException
     */
    private void dateChange(EpgFileReq epgFilesReq) throws ParseException {
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
        sdf.setLenient(false);
        SimpleDateFormat sdf1 = new SimpleDateFormat("yyyy-MM-dd 00:00:00");
        SimpleDateFormat sdf2 = new SimpleDateFormat("yyyy-MM-dd 23:59:59");
        if (StringUtils.isNotBlank(epgFilesReq.getStartTime()) && StringUtils.isNotBlank(epgFilesReq.getEndTime())) {
            Date newDate1 = sdf.parse(epgFilesReq.getStartTime());
            Date newDate2 = sdf.parse(epgFilesReq.getEndTime());
            epgFilesReq.setStartTime(sdf1.format(newDate1));
            epgFilesReq.setEndTime(sdf2.format(newDate2));
        }

/*
        SimpleDateFormat sdf = new SimpleDateFormat("yyyyMMdd");
        sdf.setLenient(false);
        SimpleDateFormat sdf1 = new SimpleDateFormat("yyyy-MM-dd 00:00:00");
        SimpleDateFormat sdf2 = new SimpleDateFormat("yyyy-MM-dd 23:59:59");
        if (StringUtils.isNotBlank(epgFilesVo.getCreateTime())){
            Date newDate = sdf.parse(epgFilesVo.getCreateTime());
            epgFilesVo.setStartTime(sdf1.format(newDate));
            epgFilesVo.setEndTime(sdf2.format(newDate));
        }else {
            epgFilesVo.setStartTime(sdf1.format(LocalDate.now()));
            epgFilesVo.setEndTime(sdf2.format(LocalDate.now()));
        }
*/

    }

    /**
     * 获取当前时间到凌晨的时间差 单位:秒
     *
     * @return
     */
    public static long timeOut() {
        LocalDate today = LocalDate.now();
        LocalDateTime todayMidnight = LocalDateTime.of(today, LocalTime.MIDNIGHT);
        LocalDateTime tomorrowMidnight = todayMidnight.plusDays(1);
        long seconds = TimeUnit.NANOSECONDS.toSeconds(Duration.between(LocalDateTime.now(), tomorrowMidnight).toNanos());
        return seconds;
    }

}
