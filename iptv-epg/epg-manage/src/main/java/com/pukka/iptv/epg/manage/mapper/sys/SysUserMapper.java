package com.pukka.iptv.epg.manage.mapper.sys;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.pukka.iptv.common.data.model.epg.SysUser;
import com.pukka.iptv.common.data.vo.req.EpgSysUserReq;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;


@Mapper
public interface SysUserMapper extends BaseMapper<SysUser> {
    List<SysUser> listUserRole(@Param("userIds") List<Long> userIds);

    Page<SysUser> pageByTemplateId(@Param("page") Page page, @Param("epgSysUserReq") EpgSysUserReq epgSysUserReq);
}
