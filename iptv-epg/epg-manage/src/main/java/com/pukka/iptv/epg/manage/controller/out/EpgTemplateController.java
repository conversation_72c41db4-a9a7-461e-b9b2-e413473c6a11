package com.pukka.iptv.epg.manage.controller.out;


import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.pukka.iptv.common.base.enums.SystemSourceEnums;
import com.pukka.iptv.common.base.vo.CommonResponse;
import com.pukka.iptv.common.data.model.epg.EpgTemplate;
import com.pukka.iptv.common.data.vo.IdList;
import com.pukka.iptv.common.data.vo.epg.EpgTemplateVo;
import com.pukka.iptv.common.data.vo.req.EpgTemplateReq;
import com.pukka.iptv.common.log.annotation.SysLog;
import com.pukka.iptv.common.log.enums.OperateObjectEnum;
import com.pukka.iptv.common.log.enums.OperateTypeEnum;
import com.pukka.iptv.epg.manage.service.out.EpgTemplateService;
import io.swagger.annotations.Api;
import lombok.AllArgsConstructor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * @Author: chiron
 * @Date: 2023/03/01/10:25
 * @Description:
 */
@RestController
@AllArgsConstructor
@RequestMapping(value = "/templateList", produces = MediaType.APPLICATION_JSON_VALUE)
@Api(tags = "模版管理")
public class EpgTemplateController {

    @Autowired
    private EpgTemplateService epgTemplateService;
    /**
     * 分页
     * @param page
     * @param epgTemplateReq
     * @return
     */
    @GetMapping("/page")
    public CommonResponse<Page> page(Page page, EpgTemplateReq epgTemplateReq){
        return CommonResponse.success(epgTemplateService.pageByQuery(page,epgTemplateReq));
    }

    /**
     * 根据id查询
     * @param id
     * @return
     */
    @GetMapping("/{id}")
    public CommonResponse<EpgTemplate> getById( @PathVariable(name = "id") Long id) {
        return CommonResponse.success(epgTemplateService.getById(id));
    }

    /**
     * 新增
     * @param epgTemplateReq
     * @return
     */
    @SysLog(objectType = OperateObjectEnum.EPG_TEMPLATE, operateType = OperateTypeEnum.SAVE, objectIds = "#epgTemplateReq.id", objectNames = "#epgTemplateReq.name", source = SystemSourceEnums.EPG)
    @PostMapping
    public CommonResponse<Boolean> save(@RequestBody EpgTemplateReq epgTemplateReq) {
        return  CommonResponse.success(epgTemplateService.increase(epgTemplateReq));
    }
    /**
     * 修改
     * @param epgTemplate
     * @return
     */
    @SysLog(objectType = OperateObjectEnum.EPG_TEMPLATE, operateType = OperateTypeEnum.UPDATE, objectIds = "#epgTemplate.id", objectNames = "#epgTemplate.name", source = SystemSourceEnums.EPG)
    @PutMapping
    public CommonResponse<Boolean> updateById( @RequestBody EpgTemplate epgTemplate) {
        return CommonResponse.success(epgTemplateService.updateWithId(epgTemplate));
    }


    /**
     * 批量删除
     * @param idList
     * @return
     */
    @SysLog(objectType = OperateObjectEnum.EPG_TEMPLATE, operateType = OperateTypeEnum.DELETE, objectIds = "#idList.ids", source = SystemSourceEnums.EPG)
    @DeleteMapping
    public CommonResponse<Boolean> deleteByIds(@RequestBody IdList idList) {
        return  CommonResponse.success(epgTemplateService.deleteByIds(idList.getIds()));
    }

    /**
     * 从缓存中查询所有模板信息
     * @param nodeCode
     * @return
     */
    @GetMapping("/redisList")
    public CommonResponse<List<EpgTemplateVo>> redisList(@RequestParam(name = "nodeCode" , required = false) String nodeCode, @RequestParam(name = "userId" , required = false) Long userId){
        return CommonResponse.success(epgTemplateService.redisList(nodeCode,userId));
    }


}
