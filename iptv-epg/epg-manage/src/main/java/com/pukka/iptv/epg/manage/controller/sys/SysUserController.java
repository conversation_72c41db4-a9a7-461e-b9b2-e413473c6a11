package com.pukka.iptv.epg.manage.controller.sys;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.pukka.iptv.common.base.enums.SystemSourceEnums;
import com.pukka.iptv.common.base.vo.CommonResponse;
import com.pukka.iptv.common.base.vo.SecurityUser;
import com.pukka.iptv.common.data.model.epg.SysUser;
import com.pukka.iptv.common.data.params.SysUserParam;
import com.pukka.iptv.common.data.params.SysUserStateParam;
import com.pukka.iptv.common.data.vo.IdList;
import com.pukka.iptv.common.data.vo.req.EpgSysUserReq;
import com.pukka.iptv.common.data.vo.req.SysUserTemplateReq;
import com.pukka.iptv.common.log.annotation.SysLog;
import com.pukka.iptv.common.log.enums.OperateObjectEnum;
import com.pukka.iptv.common.log.enums.OperateTypeEnum;
import com.pukka.iptv.epg.manage.service.sys.SysUserService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.AllArgsConstructor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.List;

/**
 *
 * @author: zhengcl
 * @date: 2021-8-18 10:37:55
 */

@RestController
@AllArgsConstructor
@RequestMapping(value = "/sysUser", produces = MediaType.APPLICATION_JSON_VALUE)
@Api(tags="sysUser管理")
public class SysUserController {

    @Autowired
    private SysUserService sysUserService;

    @ApiOperation(value = "分页")
    @GetMapping("/page" )
    public CommonResponse<Page> page(@Valid Page<SysUser> page, SysUser sysUser) {
        return  CommonResponse.success(sysUserService.pageLike(page, sysUser));
    }
    /**
     * 查询模板可以绑定的用户
     * @param page
     * @param epgSysUserReq
     * @return
     */
    @GetMapping("/pageByTemplateId")
    public CommonResponse<Page> pageByTemplateId(Page page, EpgSysUserReq epgSysUserReq ){
        return CommonResponse.success(sysUserService.pageByTemplateId(page,epgSysUserReq));
    }
    @ApiOperation(value = "详情")
    @GetMapping("/{id}")
    public CommonResponse<SysUser> getById(@Valid @PathVariable(name = "id")  Long id) {
        return CommonResponse.success(sysUserService.getById(id));
    }

    @ApiOperation(value = "获取指定模板可以添加用户权限的用户列表")
    @GetMapping
    public CommonResponse<List<SysUser>> getByTemplateId(@Valid @RequestParam(name = "templateId")  Long templateId) {
        return CommonResponse.success(sysUserService.getByTemplateId(templateId));
    }

    @SysLog(objectType = OperateObjectEnum.SYS_USER, operateType = OperateTypeEnum.SAVE, objectIds = "#sysUser.id", objectNames = "#sysUser.name",source = SystemSourceEnums.EPG)
    @ApiOperation(value = "新增")
    @PostMapping
    public CommonResponse<Boolean> save(@Valid @RequestBody SysUser sysUser) {
        return  CommonResponse.success(sysUserService.save(sysUser));
    }

    @SysLog(objectType = OperateObjectEnum.SYS_USER, operateType = OperateTypeEnum.UPDATE, objectIds = "#sysUser.id", objectNames = "#sysUser.name",source = SystemSourceEnums.EPG)
    @ApiOperation(value = "修改")
    @PutMapping
    public CommonResponse<Boolean> updateById(@Valid @RequestBody SysUser sysUser) {
        return CommonResponse.success(sysUserService.update(sysUser));
    }

    /**
     * 同步删除用户关系
     * @param idList
     * @return
     */
    @SysLog(objectType = OperateObjectEnum.SYS_USER, operateType = OperateTypeEnum.DELETE, objectIds = "#idList.ids",source = SystemSourceEnums.EPG)
    @ApiOperation(value = "批量删除")
    @DeleteMapping()
    public CommonResponse<Boolean> deleteByIds(@Valid @RequestBody IdList idList) {
        return  CommonResponse.success(sysUserService.removeUserAndMappings(idList.getIds()));
    }

    @ApiOperation(value ="根据用户名查询详情" )
    @GetMapping("/getByUsername")
    public CommonResponse<SecurityUser> getByUsername(@RequestParam(name = "username", required = true) String username) {
        return CommonResponse.success(sysUserService.getByUsername(username));
    }

    @SysLog(objectType = OperateObjectEnum.SYS_USER, operateType = OperateTypeEnum.UPDATE,objectIds = "#sysUser.id",objectNames = "sysUser.name",source = SystemSourceEnums.EPG)
    @ApiOperation(value = "修改状态")
    @PutMapping("/status")
    public CommonResponse<Boolean> updateStatusById(@Valid @RequestBody SysUser sysUser) {
        return CommonResponse.success(sysUserService.updateStatusById(sysUser));
    }

    @SysLog(objectType = OperateObjectEnum.SYS_USER, operateType = OperateTypeEnum.UPDATE_PASSWORD,objectIds = "#param.userId",source = SystemSourceEnums.EPG)
    @ApiOperation(value = "修改密码")
    @PutMapping("/password")
    public CommonResponse<Boolean> updatePassword(@Valid @RequestBody SysUserParam param) {
        return CommonResponse.success(sysUserService.updatePassword(param));
    }

    @SysLog(objectType = OperateObjectEnum.SYS_USER, operateType = OperateTypeEnum.RESET_PASSWORD,objectIds = "#ids",source = SystemSourceEnums.EPG)
    @ApiOperation(value = "重置密码")
    @PutMapping("/defaultPassword")
    public CommonResponse<String> resetPassword(@Valid @RequestBody List<Long> ids) {
        return CommonResponse.success(sysUserService.resetPassword(ids));
    }

    @SysLog(objectType = OperateObjectEnum.SYS_USER, operateType = OperateTypeEnum.RESET_STATE,objectIds = "#sysUserStateParam.ids",source = SystemSourceEnums.EPG)
    @ApiOperation(value = "重置状态")
    @PutMapping("/defaultState")
    public CommonResponse<Boolean> resetState(@Valid @RequestBody SysUserStateParam sysUserStateParam) {
        return CommonResponse.success(sysUserService.resetState(sysUserStateParam));
    }

    @SysLog(objectType = OperateObjectEnum.SYS_USER, operateType = OperateTypeEnum.LOGIN,objectIds = "#id",objectNames = "#username",source = SystemSourceEnums.EPG)
    @ApiOperation(value ="登录" )
    @GetMapping("/login")
    public CommonResponse<SecurityUser> login(@RequestParam(name = "username", required = true) String username,@RequestParam(name = "id", required = false) Long id) {
        return CommonResponse.success(null);
    }

    @SysLog(objectType = OperateObjectEnum.SYS_USER, operateType = OperateTypeEnum.LOGOUT,objectIds = "#id",objectNames = "#username",source = SystemSourceEnums.EPG)
    @ApiOperation(value ="退出" )
    @GetMapping("/logout")
    public CommonResponse<SecurityUser> logout(@RequestParam(name = "username", required = true) String username,@RequestParam(name = "id", required = false) Long id) {
        return CommonResponse.success(null);
    }

}
