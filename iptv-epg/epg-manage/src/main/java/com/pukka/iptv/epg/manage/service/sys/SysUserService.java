package com.pukka.iptv.epg.manage.service.sys;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.pukka.iptv.common.base.vo.SecurityUser;
import com.pukka.iptv.common.data.model.epg.SysUser;
import com.pukka.iptv.common.data.params.SysUserParam;
import com.pukka.iptv.common.data.params.SysUserStateParam;
import com.pukka.iptv.common.data.vo.req.EpgSysUserReq;

import java.util.List;

/**
 *
 * @author: zhengcl
 * @date: 2021-8-17 16:48:35
 */

public interface SysUserService extends IService<SysUser> {
	
	 /**
     * 根据用户名查询用户
     * @param username
     * @return
     */
    SecurityUser getByUsername(String username);

    @Override
    boolean save(SysUser sysUser);

    boolean update(SysUser sysUser);

    boolean updateStatusById(SysUser sysUser);

    Page<SysUser> pageLike(Page<SysUser> page, SysUser sysUser);

    /**
     * 修改密码
     *
     * @param param
     * @return
     */
    boolean updatePassword(SysUserParam param);

    /**
     * 重置密码
     *
     * @param ids
     * @return
     */
    String resetPassword(List<Long> ids);

    boolean resetState(SysUserStateParam sysUserStateParam);

    boolean removeUserAndMappings(List<Long> ids);

    List<SysUser> getByTemplateId(Long templateId);

    /**
     * 查询模板可以绑定的用户
     * @param page
     * @param epgSysUserReq
     * @return
     */
    Page<SysUser> pageByTemplateId(Page page, EpgSysUserReq epgSysUserReq);
}


