package com.pukka.iptv.epg.manage.controller.sys;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.pukka.iptv.common.base.enums.SystemSourceEnums;
import com.pukka.iptv.common.base.vo.CommonResponse;
import com.pukka.iptv.common.data.model.epg.SysUserTemplate;
import com.pukka.iptv.common.data.vo.IdList;
import com.pukka.iptv.common.data.vo.req.SysUserTemplateReq;
import com.pukka.iptv.common.log.annotation.SysLog;
import com.pukka.iptv.common.log.enums.OperateObjectEnum;
import com.pukka.iptv.common.log.enums.OperateTypeEnum;
import com.pukka.iptv.epg.manage.service.sys.SysUserTemplateService;
import io.swagger.annotations.Api;
import lombok.AllArgsConstructor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.stream.Collectors;


/**
 *
 * @author: zhengcl
 * @date: 2021-8-18 10:37:55
 */

@RestController
@AllArgsConstructor
@RequestMapping(value = "/sysUserTemplate", produces = MediaType.APPLICATION_JSON_VALUE)
@Api(tags="sysUserTemplate模板用户列表管理")
public class SysUserTemplateController {

    @Autowired
    private SysUserTemplateService sysUserTemplateService;

    /**
     * 根据模板id分页查询用户
     * @param page
     * @param sysUserTemplateReq
     * @return
     */
    @GetMapping("/pageUser")
    public CommonResponse<Page> pageUserById(Page page, SysUserTemplateReq sysUserTemplateReq ){
        return CommonResponse.success(sysUserTemplateService.pageUserByTemplateId(page,sysUserTemplateReq));
    }

    /**
     * 批量删除模板关联用户
     * @param idList
     * @return
     */
    @SysLog(objectType = OperateObjectEnum.SYS_USER_TEMPLATE, operateType = OperateTypeEnum.DELETE, objectIds = "#idList.ids", source = SystemSourceEnums.EPG)
    @DeleteMapping
    public CommonResponse<Boolean> deleteByIds(@RequestBody IdList idList) {
        return  CommonResponse.success(sysUserTemplateService.removeByIds(idList.getIds()));
    }

    /**
     * 新增
     * @param sysUserTemplateReq
     * @return
     */
    @SysLog(objectType = OperateObjectEnum.SYS_USER_TEMPLATE, operateType = OperateTypeEnum.SAVE, objectIds = "#sysUserTemplateReq.userIds",  source = SystemSourceEnums.EPG)
    @PostMapping
    public CommonResponse<Boolean> save(@RequestBody SysUserTemplateReq sysUserTemplateReq) {
        List<SysUserTemplate> list = sysUserTemplateReq.getUserIds().stream().map(userId -> new SysUserTemplate(userId, sysUserTemplateReq.getTemplateId())).collect(Collectors.toList());
        return  CommonResponse.success(sysUserTemplateService.saveBatch(list));
    }
}
