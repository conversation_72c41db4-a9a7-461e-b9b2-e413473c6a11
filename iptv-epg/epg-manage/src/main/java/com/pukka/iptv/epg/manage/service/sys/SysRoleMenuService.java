package com.pukka.iptv.epg.manage.service.sys;

import com.baomidou.mybatisplus.extension.service.IService;
import com.pukka.iptv.common.data.model.epg.SysRoleMenu;
import com.pukka.iptv.common.data.vo.req.RoleMenuReq;
import com.pukka.iptv.common.data.vo.resp.MenuParamResp;

import java.util.List;

/**
 * @author: zhengcl
 * @date: 2021-8-17 16:48:33
 */

public interface SysRoleMenuService extends IService<SysRoleMenu> {
    Boolean saveOrUpdateDBandRedis(RoleMenuReq roleMenuResp);

    Boolean  deleteDBandRedis(List<Long> ids);

    List<MenuParamResp>  getRoleMenuById(Long id);
}


