package com.pukka.iptv.epg.manage.service.out.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.pukka.iptv.common.base.constant.RedisKeyConstants;
import com.pukka.iptv.common.base.enums.CommonResponseEnum;
import com.pukka.iptv.common.base.enums.PublishStatusEnum;
import com.pukka.iptv.common.base.enums.StatusEnum;
import com.pukka.iptv.common.base.exception.CommonResponseException;
import com.pukka.iptv.common.core.util.DateUtils;
import com.pukka.iptv.common.core.util.SafeUtil;
import com.pukka.iptv.common.data.model.epg.EpgNode;
import com.pukka.iptv.common.data.model.epg.EpgOutOrder;
import com.pukka.iptv.common.data.model.epg.EpgTemplate;
import com.pukka.iptv.common.data.model.epg.SysUserTemplate;
import com.pukka.iptv.common.data.vo.epg.EpgTemplateVo;
import com.pukka.iptv.common.data.vo.req.EpgTemplateReq;
import com.pukka.iptv.common.log.enums.OperateObjectEnum;
import com.pukka.iptv.common.rabbitmq.constans.IssueOrderConstant;
import com.pukka.iptv.common.redis.service.RedisService;
import com.pukka.iptv.epg.manage.mapper.out.EpgOutOrderMapper;
import com.pukka.iptv.epg.manage.mapper.out.EpgTemplateMapper;
import com.pukka.iptv.epg.manage.mapper.sys.SysUserTemplateMapper;
import com.pukka.iptv.epg.manage.model.PolymericData;
import com.pukka.iptv.epg.manage.service.out.EpgNodeService;
import com.pukka.iptv.epg.manage.service.out.EpgOutOrderService;
import com.pukka.iptv.epg.manage.service.out.EpgTemplateService;
import com.pukka.iptv.epg.manage.service.sys.SysUserTemplateService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.amqp.rabbit.core.RabbitTemplate;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * @Author: chiron
 * @Date: 2023/03/02/09:48
 * @Description:
 */
@Slf4j
@Service
public class EpgTemplateServiceImpl extends ServiceImpl<EpgTemplateMapper, EpgTemplate> implements EpgTemplateService {

    @Autowired
    private RabbitTemplate rabbitTemplate;
    @Autowired
    private EpgOutOrderService epgOutOrderService;
    @Autowired
    private EpgOutOrderMapper epgOutOrderMapper;
    @Autowired
    private EpgTemplateMapper epgTemplateMapper;
    @Autowired
    private RedisService redisService;
    @Autowired
    private SysUserTemplateService sysUserTemplateService;
    @Autowired
    private SysUserTemplateMapper sysUserTemplateMapper;
    @Autowired
    private EpgNodeService epgNodeService;

    /**
     * 分页
     *
     * @param page
     * @param epgTemplateReq
     * @return
     */
    @Override
    public Page<EpgTemplate> pageByQuery(Page page, EpgTemplateReq epgTemplateReq) {
        //处理前端传过来的时间参数
        try {
            dateChange(epgTemplateReq);
        } catch (ParseException e) {
            e.printStackTrace();
        }
        Page<EpgTemplate> pageList  = epgTemplateMapper.selectPageByUserId(page,epgTemplateReq);
        return pageList;
    }

    /**
     * 新增
     *
     * @param epgTemplateReq
     * @return
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean increase(EpgTemplateReq epgTemplateReq) {
        if (StringUtils.isBlank(epgTemplateReq.getName())) {
            throw new CommonResponseException(CommonResponseEnum.ERROR_PARAMETER, "模板名称格式错误");
        }
        if (StringUtils.isBlank(epgTemplateReq.getNodeName())) {
            throw new CommonResponseException(CommonResponseEnum.ERROR_PARAMETER, "结点名称格式错误");
        }
        if (StringUtils.isBlank(epgTemplateReq.getPath())) {
            throw new CommonResponseException(CommonResponseEnum.ERROR_PARAMETER, "EPG路径格式错误");
        }
        if (epgTemplateReq.getStatus() == null) {
            throw new CommonResponseException(CommonResponseEnum.ERROR_PARAMETER, "状态格式错误");
        }
        if (StringUtils.isBlank(epgTemplateReq.getNodeCode())) {
            throw new CommonResponseException(CommonResponseEnum.ERROR_PARAMETER, "结点code格式错误");
        }

        //判断模板名称是否存在
        Long count = epgTemplateMapper.selectCount(Wrappers.<EpgTemplate>lambdaQuery().eq(EpgTemplate::getName, epgTemplateReq.getName()).ne(EpgTemplate::getStatus, StatusEnum.DELETE.getCode()).last("limit 1"));
        if (count > 0) {
            throw new CommonResponseException(CommonResponseEnum.FAIL, "模板名称已存在，请重新填写");
        }
        List<EpgNode> epgNodes = epgNodeService.redisList();
        List<EpgNode> collect = epgNodes.stream().filter(epgNode -> epgTemplateReq.getNodeCode().equals(epgNode.getCode())).collect(Collectors.toList());
        if (!(collect.size() > 0)) {
            throw new CommonResponseException(CommonResponseEnum.FAIL, "所属节点已被删除，请重新选择");
        }
        //新增模板存入数据库
        boolean b = this.save(epgTemplateReq);
        //将管理员和创建模板用户添加到该模板用户列表中
        ArrayList<SysUserTemplate> sysUserTemplates = new ArrayList<>();
        sysUserTemplates.add(new SysUserTemplate(1L,epgTemplateReq.getId()));
        if (!Long.valueOf(1L).equals(epgTemplateReq.getUserId())){
            sysUserTemplates.add(new SysUserTemplate(epgTemplateReq.getUserId(),epgTemplateReq.getId()));
        }
        sysUserTemplateService.saveBatch(sysUserTemplates);
        EpgTemplate epgTemplate1 = this.getById(epgTemplateReq.getId());
        //新增模板存入缓存
        if (b) {
            redisService.setCacheHash(RedisKeyConstants.EPG_TEMPLATE_KEY, String.valueOf(epgTemplate1.getId()), epgTemplate1);
        }

        return b;
    }

    /**
     * 修改
     *
     * @param epgTemplate
     * @return
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean updateWithId(EpgTemplate epgTemplate) {
        if (StringUtils.isBlank(epgTemplate.getName())) {
            throw new CommonResponseException(CommonResponseEnum.ERROR_PARAMETER, "模板名称格式错误");
        }
        if (StringUtils.isBlank(epgTemplate.getNodeName())) {
            throw new CommonResponseException(CommonResponseEnum.ERROR_PARAMETER, "结点名称格式错误");
        }
        if (StringUtils.isBlank(epgTemplate.getPath())) {
            throw new CommonResponseException(CommonResponseEnum.ERROR_PARAMETER, "EPG路径格式错误");
        }
        if (epgTemplate.getStatus() == null) {
            throw new CommonResponseException(CommonResponseEnum.ERROR_PARAMETER, "状态格式错误");
        }
        if (StringUtils.isBlank(epgTemplate.getNodeCode())) {
            throw new CommonResponseException(CommonResponseEnum.ERROR_PARAMETER, "结点code格式错误");
        }

        //判断模板名称是否存在
        Long count = epgTemplateMapper.selectCount(Wrappers.<EpgTemplate>lambdaQuery().eq(EpgTemplate::getName, epgTemplate.getName()).ne(EpgTemplate::getStatus, StatusEnum.DELETE.getCode()).ne(EpgTemplate::getId, epgTemplate.getId()).last("limit 1"));
        if (count != null && count > 0) {
            throw new CommonResponseException(CommonResponseEnum.FAIL, "模板名称已存在，请重新填写");
        }
        //判断模板名字是否改变
        Long repeat = epgTemplateMapper.selectCount(Wrappers.<EpgTemplate>lambdaQuery().eq(EpgTemplate::getName, epgTemplate.getName()).ne(EpgTemplate::getStatus, StatusEnum.DELETE.getCode()).last("limit 1"));
        List<EpgNode> epgNodes = epgNodeService.redisList();
        List<EpgNode> collect = epgNodes.stream().filter(epgNode -> epgTemplate.getNodeCode().equals(epgNode.getCode())).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(collect)) {
            throw new CommonResponseException(CommonResponseEnum.FAIL, "所属节点已被删除，请重新选择");
        }
        //修改模板更新数据库
        if (StatusEnum.LOSE.getCode().equals(epgTemplate.getStatus())) {
            //禁用模版关联事件
            execute(new ArrayList<String>() {
                {
                    add(SafeUtil.getString(epgTemplate.getId()));
                }
            });
        }
        boolean b = this.updateById(epgTemplate);
        EpgTemplate epgTemplate1 = this.getById(epgTemplate.getId());
        //修改模板更新缓存
        if (b && StatusEnum.COME.getCode().equals(epgTemplate1.getStatus())) {
            redisService.setCacheHash(RedisKeyConstants.EPG_TEMPLATE_KEY, String.valueOf(epgTemplate1.getId()), epgTemplate1);
        } else if (b && StatusEnum.LOSE.getCode().equals(epgTemplate1.getStatus())) {
            redisService.delCacheMapKey(RedisKeyConstants.EPG_TEMPLATE_KEY, String.valueOf(epgTemplate1.getId()));
        }

        if (!(repeat>0)){
            //修改发布表
            epgOutOrderService.update(Wrappers.<EpgOutOrder>lambdaUpdate()
            .set(EpgOutOrder::getTemplateName,epgTemplate.getName())
            .eq(EpgOutOrder::getTemplateId,epgTemplate1.getId()));
        }
        return b;
    }

    /**
     * 批量删除
     *
     * @param ids
     * @return
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean deleteByIds(List<Long> ids) {
        //判断参数
        if (ids == null || ids.size() == 0) {
            return false;
        }
        List<String> idList = ids.stream().map(id -> String.valueOf(id)).collect(Collectors.toList());
        List<EpgTemplate> epgTemplates = epgTemplateMapper.selectBatchIds(ids);
        if (CollectionUtils.isEmpty(epgTemplates)) {
            throw new CommonResponseException(CommonResponseEnum.FAIL, "模板不存在");
        }
        epgTemplates.stream().forEach(epgTemplate -> epgTemplate.setStatus(StatusEnum.DELETE.getCode()));
        boolean b = this.updateBatchById(epgTemplates);
        if (b && CollectionUtils.isNotEmpty(epgTemplates)) {
            redisService.delCacheMapKey(RedisKeyConstants.EPG_TEMPLATE_KEY, epgTemplates.stream().map(epgTemplate -> String.valueOf(epgTemplate.getId())).toArray(String[]::new));
        }
        //禁用模版关联事件
        execute(idList);
        return b;
    }

    @Override
    public List<EpgTemplateVo> redisList(String nodeCode, Long userId) {
        Map<String, EpgTemplate> cacheMap = redisService.getCacheMap(RedisKeyConstants.EPG_TEMPLATE_KEY);
        List<EpgTemplate> list = new ArrayList<>(cacheMap.values());
        List<Long> l = sysUserTemplateMapper.selectByUserId(userId);
        List<EpgTemplate> collect = list.stream().filter(epgTemplate -> l.contains(epgTemplate.getId())).collect(Collectors.toList());
        if (!StringUtils.isNotBlank(nodeCode)) {
            return getEpgTemplateVos(collect);
        }
        List<EpgTemplate> list2 = collect.stream().filter(epgTemplate -> epgTemplate.getNodeCode().equals(nodeCode)).collect(Collectors.toList());
        return getEpgTemplateVos(list2);
    }

    private List<EpgTemplateVo> getEpgTemplateVos(List<EpgTemplate> collect) {
        List<EpgTemplate> epgTemplateList = collect.stream()
                .filter(epgTemplate -> epgTemplate.getStatus().equals(StatusEnum.COME.getCode()))
                .collect(Collectors.toList());
        return epgTemplateList.stream()
                .map(epgTemplate -> {
                    EpgTemplateVo epgTemplateVo = new EpgTemplateVo();
                    BeanUtils.copyProperties(epgTemplate, epgTemplateVo);
                    epgTemplateVo.setNamePath(epgTemplate.getName() + epgTemplate.getPath());
                    return epgTemplateVo;
                }).collect(Collectors.toList());
    }


    /**
     * 前端传入时间处理
     *
     * @param epgTemplateReq
     * @throws ParseException
     */
    private void dateChange(EpgTemplateReq epgTemplateReq) throws ParseException {
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
        sdf.setLenient(false);
        SimpleDateFormat sdf1 = new SimpleDateFormat("yyyy-MM-dd 00:00:00");
        SimpleDateFormat sdf2 = new SimpleDateFormat("yyyy-MM-dd 23:59:59");
        if (StringUtils.isNotBlank(epgTemplateReq.getStartTime()) && StringUtils.isNotBlank(epgTemplateReq.getEndTime())) {
            Date newDate1 = sdf.parse(epgTemplateReq.getStartTime());
            Date newDate2 = sdf.parse(epgTemplateReq.getEndTime());
            epgTemplateReq.setStartTime(sdf1.format(newDate1));
            epgTemplateReq.setEndTime(sdf2.format(newDate2));
        }
    }

    /**
     * 查询模板列表
     *
     * @param idList
     */
    private void execute(List<String> idList) {
        //查询关联epg_out_order表一个月内的数据数据
        List<EpgOutOrder> epgOutOrders = epgOutOrderService.list(new LambdaQueryWrapper<EpgOutOrder>()
                .eq(EpgOutOrder::getStatus, PublishStatusEnum.WAITPUBLISH.getCode())
                .in(EpgOutOrder::getTemplateId, idList)
                .ge(EpgOutOrder::getCreateTime, DateUtils.getBeforeDateDay(30)));
        if (CollectionUtils.isNotEmpty(epgOutOrders)) {
            try {
                epgOutOrders.stream().forEach(epgOutOrder -> epgOutOrder.setStatus(StatusEnum.DELETE.getCode()));
                epgOutOrderService.updateBatchById(epgOutOrders);
            } catch (Exception e) {
                log.error("删除模板数据时，关联epg_out_order表数据删除失败，错误信息：{}", e.getMessage());
                throw new CommonResponseException(CommonResponseEnum.FAIL, "删除模版数据时，关联事件表数据删除失败");
            }
        }
        //推送rabbitmq队列
        try {
            PolymericData polymericData = new PolymericData();
            polymericData.setIdList(idList).setOperateObjectEnum(OperateObjectEnum.EPG_NODE);
            rabbitTemplate.convertAndSend(IssueOrderConstant.OUT_EXCHANGE, IssueOrderConstant.EPG_WAREHOUSING_ROUTING, polymericData);
        } catch (Exception e) {
            log.error("删除模板数据时，推送rabbitmq队列失败，模板Ids:{},错误信息：{}", idList, e.getMessage());
            throw new CommonResponseException(CommonResponseEnum.FAIL, "删除模版数据时，关联事件表数据删除失败");
        }
    }
}
