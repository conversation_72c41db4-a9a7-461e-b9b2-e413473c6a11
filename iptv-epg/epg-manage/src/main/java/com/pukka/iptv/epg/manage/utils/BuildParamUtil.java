package com.pukka.iptv.epg.manage.utils;

import com.baomidou.mybatisplus.core.toolkit.ObjectUtils;
import com.pukka.iptv.common.data.model.epg.SysMenu;
import com.pukka.iptv.common.data.vo.resp.MenuParamResp;
import lombok.extern.slf4j.Slf4j;

import java.util.Comparator;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * @Author: chiron
 * @Date: 2023/03/21/14:35
 * @Description:
 */
@Slf4j
public class BuildParamUtil {

    /**
     * 构建
     *
     * @param control
     * @param sysMenu
     */
    public static MenuParamResp buildParamResp(String control, SysMenu sysMenu) {
        MenuParamResp menuParamResp = new MenuParamResp();
        if (ObjectUtils.isNotEmpty(sysMenu)) {
            menuParamResp.setId(sysMenu.getId());
            menuParamResp.setControl(control);
            menuParamResp.setIcon(sysMenu.getIcon());
            menuParamResp.setName(sysMenu.getName());
            menuParamResp.setPath(sysMenu.getPath());
            menuParamResp.setSequence(sysMenu.getSequence());
            menuParamResp.setParentId(sysMenu.getParentId());
        }
        return menuParamResp;
    }

    /**
     * 构建菜单树
     *
     * @param menuList
     * @return
     */
    public static List<MenuParamResp> buildMenuTree(List<MenuParamResp> menuList) {
        //添加menuList非空判断
        if (menuList == null || menuList.size() == 0) {
            log.warn("菜单menuList为空");
            return null;
        }
        Map<Long, MenuParamResp> menuMap = menuList.stream().collect(Collectors.toMap(MenuParamResp::getId, Function.identity()));
        List<MenuParamResp> rootList = menuList.stream().filter(menu -> menu.getParentId() == 0).collect(Collectors.toList());
        menuList.stream().filter(menu -> menu.getParentId() != 0).forEach(menu -> {
            MenuParamResp parent = menuMap.get(menu.getParentId());
            if (parent != null) {
                parent.getRoutes().add(menu);
            }
        });
        //添加MenuParamResp非空判断
        if (rootList == null || rootList.size() == 0) {
            log.warn("菜单rootList为空");
            return null;
        }
        //删除rootlist中为null的数据
        rootList.removeIf(menu -> ObjectUtils.isEmpty(menu));
        rootList.sort(Comparator.comparing(MenuParamResp::getSequence));
        rootList.forEach(menu -> menu.getRoutes().sort(Comparator.comparing(MenuParamResp::getSequence)));
        return rootList;
    }


}
