package com.pukka.iptv.epg.manage.service.out;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.pukka.iptv.common.data.model.epg.EpgFile;
import com.pukka.iptv.common.data.vo.epg.ResultUploadVo;
import com.pukka.iptv.common.data.vo.req.EpgFileReq;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.io.UnsupportedEncodingException;
import java.util.List;

/**
 * @Author: chiron
 * @Date: 2023/03/02/09:47
 * @Description:
 */

public interface EpgFileService extends IService<EpgFile> {
    /**
     * 分页
     * @param page
     * @param epgFilesReq
     * @return
     */
    Page<EpgFile> pageByQuery(Page page, EpgFileReq epgFilesReq);

    /**
     * 新增
     * @param epgFile
     * @return
     */
    Boolean increase(EpgFile epgFile);

    /**
     *下载文件
     * @param filePath
     * @param response
     * @return
     */
    Boolean retrieveFile(String filePath, HttpServletResponse response);

    /**
     * 修改文件
     * @param epgFile
     * @return
     */
    Boolean updateWithId(EpgFile epgFile);

    /**
     * 删除
     * @param id
     * @return
     */
    Boolean deleteById(Long id);

    /**
     * 批量删除
     * @param ids
     * @return
     */
    Boolean deleteByIds(List<Long> ids);

    List<EpgFile> fileList(String filePath);

    /**
     * 上传文件
     * @param file
     * @return
     */
    ResultUploadVo upload(MultipartFile file) throws Exception;
}
