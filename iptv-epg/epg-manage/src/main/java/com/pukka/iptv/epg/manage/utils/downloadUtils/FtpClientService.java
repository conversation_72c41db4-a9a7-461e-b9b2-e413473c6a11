package com.pukka.iptv.epg.manage.utils.downloadUtils;

import com.pukka.iptv.common.base.constant.RedisKeyConstants;
import com.pukka.iptv.common.core.constant.SymbolConstant;
import com.pukka.iptv.common.core.util.DateUtils;
import com.pukka.iptv.common.core.util.FtpUtil;
import com.pukka.iptv.common.core.util.SafeUtil;
import com.pukka.iptv.common.core.util.SpringUtils;
import com.pukka.iptv.common.data.dto.RetrieveFileEntity;
import com.pukka.iptv.common.data.model.sys.SysNat;
import com.pukka.iptv.common.proxy.config.EpgOutProxyProperties;
import com.pukka.iptv.common.redis.service.RedisService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.io.ByteArrayInputStream;
import java.io.IOException;
import java.io.InputStream;
import java.io.OutputStream;
import java.net.URLEncoder;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

import static java.util.regex.Pattern.compile;

/**
 * @author: chiron Date: 2022/5/12 14:16 Description: 实现文件上传下载
 */
@Slf4j
@Component
public class FtpClientService {

    private EpgOutProxyProperties epgOutProxyProperties = SpringUtils.getBean(EpgOutProxyProperties.class);
    private RedisService redisService = SpringUtils.getBean(RedisService.class);

    /**
     * 通过ftpUrl上传FTP服务器文件
     *
     * @param file
     * @param filename
     * @param url
     * @return
     */
    public static String fileUpByFtp(MultipartFile file, String filename, String url) throws IOException {
        Integer ret = -1;
        //主动被动轮流尝试-B
        //尝试次数
        int tryCount = 0;
        //是否删除成功
        boolean isSuccess = false;
        boolean isPassiveMode = true;
        String path = "";
        InputStream inputStream = new ByteArrayInputStream(file.getBytes());
        while (tryCount < 4 && !isSuccess) {
            FtpUtil ftpUtil = null;
            try {
                isPassiveMode = (tryCount % 2 == 0);
                String[] values = FtpUtil.getSingleMatchValue(url);
                ftpUtil = new FtpUtil(values[2], values[3], values[0], values[1]);
                ftpUtil.login(isPassiveMode);
                path = ftpUtil.upFileByEpg(inputStream, filename, url);
                isSuccess = path.equalsIgnoreCase(url + filename);
            } catch (Exception exception) {
                log.error("--fileUpByFtp Exception.", exception);
            } finally {
                tryCount++;
                try {
                    if (null != ftpUtil) {
                        ftpUtil.logout();
                    }
                } catch (Exception e) {
                    log.error("--fileUpByFtp  exception,when ftpUtil.logout().", e);
                }
            }
        }
        //主动被动轮流尝试-E

        return filename;
    }


    /**
     * 打开单ftp连接读取下载介质
     *
     * @param retrieveFile
     * @return
     */
    public Boolean readFtpStream(RetrieveFileEntity retrieveFile) {
        // 是否删除成功
        String destination = "";
        Boolean aBoolean = false;
        String ftpPath = retrieveFile.getFileUrl();
        try {
            HttpServletResponse response = retrieveFile.getResponse();
            response.reset();
            // 设置文件头 最后一个参数是设置下载的文件名并编码为UTF-8
            response.setHeader(
                    "Content-Disposition",
                    "attachment;filename*=UTF-8''"
                            + URLEncoder.encode(getString(retrieveFile.getFileName()), "UTF-8"));
            OutputStream out = response.getOutputStream();
            FtpUtil ftpUtil = null;
            try {
                String[] values = FtpUtil.getSingleMatchValue(ftpPath);
                destination = values[4];
                //判断是否包含代理
                Boolean existProxyIp = isExistProxyIp(ftpPath);
                // 如果包含代理地址，使用代理的方式去ftp下载
                ftpUtil = new FtpUtil(ftpPath, existProxyIp, epgOutProxyProperties);
                aBoolean = ftpUtil.readFtpStream(ftpPath, out);
            } catch (Exception exception) {
                log.error("打开单ftp连接读取下载文件 -----> 数据流传输失败,Exception:{}.", exception);
                return aBoolean;
            } finally {
                try {
                    if (ftpUtil != null) {
                        ftpUtil.logout();
                    }
                } catch (Exception e) {
                    log.error("打开单ftp连接读取下载文件 -----> 关闭ftp连接失败.exception:{}", e);
                }
            }
        } catch (Exception exception) {
            log.error("打开单ftp连接读取下载文件 -----> 检索文件并下载失败, 错误信息:{}", exception);
        }
        log.info(
                "打开单ftp连接读取下载文件 -----> url:{},dirPath:{}", SafeUtil.getString(ftpPath),
                SafeUtil.getString(destination));
        return aBoolean;
    }

    /**
     * 打开单ftp连接读取下载介质
     * @param retrieveFile
     * @return
     */
    public Boolean readFtpConternt(RetrieveFileEntity retrieveFile) {
        String fileName = retrieveFile.getFileName();
        String content = retrieveFile.getFileContent();
        Boolean aBoolean = false;
        try {
            HttpServletResponse response = retrieveFile.getResponse();
            response.reset();
            // 设置文件头 最后一个参数是设置下载的文件名并编码为UTF-8
            response.setHeader(
                    "Content-Disposition",
                    "attachment;filename*=UTF-8''"
                            + URLEncoder.encode(getString(retrieveFile.getFileName()), "UTF-8"));
            OutputStream outputStream = response.getOutputStream();
            // 设置缓冲区大小
            int bufferSize = 1024;
            byte[] buffer = new byte[bufferSize];
            try {
                int length;
                int offset = 0;
                while (offset < content.length()) {
                    length = Math.min(content.length() - offset, bufferSize);
                    buffer = content.substring(offset, offset + length).getBytes();
                    outputStream.write(buffer, 0, length);
                    offset += length;
                }
                outputStream.flush();
                outputStream.close();
            } catch (Exception exception) {
                log.error("打开单ftp连接读取下载文件 -----> 数据流传输失败.", exception);
                return aBoolean;
            }
        } catch (Exception exception) {
            log.error("打开单ftp连接读取下载文件 -----> 检索文件并下载失败.", exception);
        }
        log.info(
                "打开单ftp连接读取下载文件 -----> fileName:{}", SafeUtil.getString(fileName));
        return aBoolean;
    }

    /**
     * 编辑文件名称
     *
     * @param mediaName
     * @return
     */
    private String getString(String mediaName) {
        String name = "";
        if (mediaName.contains(SymbolConstant.PERIOD)) {
            String firstCharacters = mediaName.substring(0, mediaName.lastIndexOf(SymbolConstant.PERIOD));
            String lastCharacters = mediaName.substring((firstCharacters.length()));
            name = firstCharacters + SymbolConstant.PERIOD + DateUtils.dateTimeNow() + lastCharacters;
        } else {
            name = mediaName + SymbolConstant.PERIOD + DateUtils.dateTimeNow();
        }
        return name;
    }

    /**
     * 查询前置机代理地址，是否存在
     * 若存在，则使用代理的方式下载ftp文件，否则使用原始下载方式
     *
     * @param sourceUrl
     * @return
     */
    Boolean isExistProxyIp(String sourceUrl) {
        Pattern compile = compile("\\d+\\.\\d+\\.\\d+\\.\\d+\\:\\d+");
        Matcher matcher = compile.matcher(sourceUrl);
        try {
            if (matcher.find()) {
                // 匹配到ip 去redis中查询
                String address = matcher.group();
                SysNat nat = redisService.getCacheMapValue(RedisKeyConstants.EPG_NAT_KEY, address);
                if (nat != null) {
                    return true;
                }
            }
        } catch (Exception exception) {
            log.error("查询前置机代理地址，是否存在 -----> 查询redis异常,Exception:{}", exception.getMessage());
            return false;
        }
        return false;
    }
}
