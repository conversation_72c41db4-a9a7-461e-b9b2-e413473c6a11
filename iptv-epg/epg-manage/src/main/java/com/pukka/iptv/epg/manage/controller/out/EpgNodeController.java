package com.pukka.iptv.epg.manage.controller.out;

import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.pukka.iptv.common.base.enums.StatusEnum;
import com.pukka.iptv.common.base.enums.SystemSourceEnums;
import com.pukka.iptv.common.base.vo.CommonResponse;
import com.pukka.iptv.common.data.model.epg.EpgNode;
import com.pukka.iptv.common.data.vo.req.EpgNodeReq;
import com.pukka.iptv.common.log.annotation.SysLog;
import com.pukka.iptv.common.log.enums.OperateObjectEnum;
import com.pukka.iptv.common.log.enums.OperateTypeEnum;
import com.pukka.iptv.epg.manage.service.out.EpgNodeService;
import io.swagger.annotations.Api;
import lombok.AllArgsConstructor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * @Author: chiron
 * @Date: 2023/03/01/10:24
 * @Description:
 */
@RestController
@AllArgsConstructor
@RequestMapping(value = "/nodeList", produces = MediaType.APPLICATION_JSON_VALUE)
@Api(tags = "节点管理")
public class EpgNodeController {

    @Autowired
    private EpgNodeService epgNodeService;
    /**
     * 分页
     * @param page
     * @param epgNodeReq
     * @return
     */
    @GetMapping("/page")
    public CommonResponse<Page> page(Page page, EpgNodeReq epgNodeReq){
        return CommonResponse.success(epgNodeService.page(page, Wrappers.<EpgNode>lambdaQuery().eq(StringUtils.isNotBlank(epgNodeReq.getLspId()),EpgNode::getLspId,epgNodeReq.getLspId())
                .ne(EpgNode::getStatus, StatusEnum.DELETE.getCode())
                .like(StringUtils.isNotBlank(epgNodeReq.getName()),EpgNode::getName,epgNodeReq.getName())
                .orderByDesc(EpgNode::getCreateTime)));
    }

    /**
     * 根据lspId查询
     * @param lspId
     * @return
     */
    @GetMapping
    public CommonResponse<EpgNode> getById( @RequestParam(name = "lspId") String lspId) {
        return CommonResponse.success(epgNodeService.getOne(Wrappers.<EpgNode>lambdaQuery().eq(EpgNode::getLspId,lspId)));
    }
    /**
     * 修改
     * @param epgNode
     * @return
     */
    @SysLog(objectType = OperateObjectEnum.EPG_NODE, operateType = OperateTypeEnum.UPDATE, objectIds = "#epgNode.id", objectNames = "#epgNode.name", source = SystemSourceEnums.EPG)
    @PutMapping
    public CommonResponse<Boolean> updateById( @RequestBody EpgNode epgNode) {
        return CommonResponse.success(epgNodeService.updateWithId(epgNode));
    }
    /**
     * 新增
     * @param epgNode
     * @return
     */
    @SysLog(objectType = OperateObjectEnum.EPG_NODE, operateType = OperateTypeEnum.SAVE, objectIds = "#epgNode.id", objectNames = "#epgNode.name", source = SystemSourceEnums.EPG)
    @PostMapping
    public CommonResponse<Boolean> save(@RequestBody EpgNode epgNode) {
        return  CommonResponse.success(epgNodeService.increase(epgNode));
    }


    /**
     * 批量删除
     * @param idList
     * @return
     */
    @SysLog(objectType = OperateObjectEnum.EPG_NODE, operateType = OperateTypeEnum.DELETE, objectIds = "#idList", source = SystemSourceEnums.EPG)
    @DeleteMapping
    public CommonResponse<Boolean> deleteByIds(@RequestBody List<String> idList) {
        return  CommonResponse.success(epgNodeService.deleteByIds(idList));
    }

    /**
     * 从缓存中查询所有节点信息
     * @return
     */
    @GetMapping("/redisList")
    public CommonResponse<List<EpgNode>> redisList(){
        return CommonResponse.success(epgNodeService.redisList());
    }
}
