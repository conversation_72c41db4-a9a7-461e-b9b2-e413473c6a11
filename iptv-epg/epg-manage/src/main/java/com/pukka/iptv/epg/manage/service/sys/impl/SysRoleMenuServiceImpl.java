package com.pukka.iptv.epg.manage.service.sys.impl;

import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.core.toolkit.ObjectUtils;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.pukka.iptv.common.base.constant.RedisKeyConstants;
import com.pukka.iptv.common.base.enums.CommonResponseEnum;
import com.pukka.iptv.common.base.exception.CommonResponseException;
import com.pukka.iptv.common.data.model.epg.SysMenu;
import com.pukka.iptv.common.data.model.epg.SysRoleMenu;
import com.pukka.iptv.common.data.vo.req.RoleMenuReq;
import com.pukka.iptv.common.data.vo.resp.MenuParamResp;
import com.pukka.iptv.common.redis.service.RedisService;
import com.pukka.iptv.epg.manage.mapper.sys.SysMenuMapper;
import com.pukka.iptv.epg.manage.mapper.sys.SysRoleMenuMapper;
import com.pukka.iptv.epg.manage.service.cache.CacheServiceImpl;
import com.pukka.iptv.epg.manage.service.sys.SysRoleMenuService;
import com.pukka.iptv.epg.manage.utils.BuildParamUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Map;

/**
 * @author: zhengcl
 * @date: 2021-8-17 16:48:33
 */
@Slf4j
@Service
public class SysRoleMenuServiceImpl extends ServiceImpl<SysRoleMenuMapper, SysRoleMenu> implements SysRoleMenuService {

    @Autowired
    private RedisService redisService;
    @Autowired
    private CacheServiceImpl cacheService;
    @Autowired
    private SysMenuMapper sysMenuMapper;
    @Autowired
    private SysRoleMenuMapper sysRoleMenuMapper;

    @Override
    public Boolean saveOrUpdateDBandRedis(RoleMenuReq roleMenuReq) {
        if (ObjectUtils.isEmpty(roleMenuReq.getRoleId())) {
            throw new CommonResponseException(CommonResponseEnum.ERROR_PARAMETER);
        }
        //查询角色对应菜单关系
        SysRoleMenu sysRoleMenu = sysRoleMenuMapper.selectOne(Wrappers.lambdaQuery(SysRoleMenu.class)
                .eq(SysRoleMenu::getRoleId, roleMenuReq.getRoleId()).last("limit 1"));
        //初始化入库实体
        SysRoleMenu roleMenu = new SysRoleMenu();
        //开始处理控件关系
        if (ObjectUtils.isNotEmpty(sysRoleMenu)) {
            //新增操作
            roleMenu.setId(sysRoleMenu.getId());
        }
        roleMenu.setRoleId(roleMenuReq.getRoleId()).setMenuParams(JSON.toJSONString(roleMenuReq.getHashMap()));
        boolean save = saveOrUpdate(roleMenu);
        if (save) {
            try {
                cacheService.cacheSysRoleMenu(Collections.singletonList(roleMenu));
            } catch (Exception exception) {
                log.error("新增/更新菜单角色关系失败,Exception:", exception);
                throw new CommonResponseException(CommonResponseEnum.CREATE_ERROR, "新增/更新菜单角色关系失败,redis异常:" + exception.getMessage());
            }
        }
        return true;
    }

    @Override
    public Boolean deleteDBandRedis(List<Long> ids) {
        boolean remove = this.removeByIds(ids);
        if (remove) {
            try {
                ids.forEach(id -> redisService.deleteByHashKey(RedisKeyConstants.SYS_ROLE_MENU_KEY, id.toString()));
            } catch (Exception exception) {
                log.error("删除菜单角色关系失败,Exception:", exception);
                throw new CommonResponseException("redis异常：" + exception.getMessage());
            }
        }
        return true;
    }

    /**
     * 获取菜单集
     *
     * @param id
     * @return
     */
    @Override
    public List<MenuParamResp> getRoleMenuById(Long id) {
        //校验角色ID是否启用
        if (ObjectUtils.isEmpty(id)) {
            throw new CommonResponseException(CommonResponseEnum.ERROR_PARAMETER);
        }
        //查询角色对应菜单关系
        SysRoleMenu sysRoleMenu = sysRoleMenuMapper.selectOne(Wrappers.lambdaQuery(SysRoleMenu.class)
                .eq(SysRoleMenu::getRoleId, id).last("limit 1"));
        if (ObjectUtils.isEmpty(sysRoleMenu)) {
            log.warn("查询角色:{} 对应菜单数据为空", id);
            return new ArrayList<>();
        }
        String menuParams = sysRoleMenu.getMenuParams();
        Map<Long, String> map = JSON.parseObject(menuParams, Map.class);
        List<MenuParamResp> paramResp = new ArrayList<>();
        map.entrySet().parallelStream().forEach(entry -> {
            SysMenu sysMenu = sysMenuMapper.selectById(entry.getKey());
            if (ObjectUtils.isNotEmpty(sysMenu)) {
                MenuParamResp menuParamResp = BuildParamUtil.buildParamResp(entry.getValue(), sysMenu);
                paramResp.add(menuParamResp);
            }
        });
        if (CollectionUtils.isNotEmpty(paramResp)) {
            List<MenuParamResp> resp = BuildParamUtil.buildMenuTree(paramResp);
            return resp;
        } else {
            log.warn("查询角色:{} 对应菜单数据错误", id);
            return new ArrayList<>();
        }
    }

}
