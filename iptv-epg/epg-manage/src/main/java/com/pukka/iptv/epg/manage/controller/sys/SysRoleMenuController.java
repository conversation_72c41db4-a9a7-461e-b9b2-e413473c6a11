package com.pukka.iptv.epg.manage.controller.sys;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.pukka.iptv.common.base.enums.SystemSourceEnums;
import com.pukka.iptv.common.base.vo.CommonResponse;
import com.pukka.iptv.common.data.model.epg.SysRoleMenu;
import com.pukka.iptv.common.data.vo.IdList;
import com.pukka.iptv.common.data.vo.req.RoleMenuReq;
import com.pukka.iptv.common.data.vo.resp.MenuParamResp;
import com.pukka.iptv.common.log.annotation.SysLog;
import com.pukka.iptv.common.log.enums.OperateObjectEnum;
import com.pukka.iptv.common.log.enums.OperateTypeEnum;
import com.pukka.iptv.epg.manage.service.sys.SysRoleMenuService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.AllArgsConstructor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.List;

/**
 * @author: zhengcl
 * @date: 2021-8-18 10:37:53
 */

@RestController
@AllArgsConstructor
@RequestMapping(value = "/sysRoleMenu", produces = MediaType.APPLICATION_JSON_VALUE)
@Api(tags = "sysRoleMenu管理")
public class SysRoleMenuController {

    @Autowired
    private SysRoleMenuService sysRoleMenuService;

    @ApiOperation(value = "分页", hidden = true)
    @GetMapping("/page")
    public CommonResponse<Page> page(@Valid Page page, SysRoleMenu sysRoleMenu) {
        return CommonResponse.success(sysRoleMenuService.page(page, Wrappers.lambdaQuery(sysRoleMenu).orderByDesc(SysRoleMenu::getId)));
    }

    @ApiOperation(value = "详情")
    @GetMapping("/{id}")
    public CommonResponse<List<MenuParamResp>> getRoleMenuById(@Valid @PathVariable(name = "id") Long id) {
        return CommonResponse.success(sysRoleMenuService.getRoleMenuById(id));
    }

    @SysLog(objectType = OperateObjectEnum.SYS_ROLE_MENU, operateType = OperateTypeEnum.UPDATE, objectIds = "#roleMenuResp.roleId",objectNames = "#roleMenuResp.roleName", source = SystemSourceEnums.EPG)
    @ApiOperation(value = "新增更新")
    @PostMapping
    public CommonResponse<Boolean> saveOrUpdate(@Valid @RequestBody RoleMenuReq roleMenuResp) {
        return CommonResponse.success(sysRoleMenuService.saveOrUpdateDBandRedis(roleMenuResp));
    }

    @SysLog(objectType = OperateObjectEnum.SYS_ROLE_MENU, operateType = OperateTypeEnum.DELETE, source = SystemSourceEnums.EPG)
    @ApiOperation(value = "批量删除")
    @DeleteMapping
    public CommonResponse<Boolean> deleteByIds(@Valid @RequestBody IdList idList) {
        return CommonResponse.success(sysRoleMenuService.deleteDBandRedis(idList.getIds()));
    }

}
