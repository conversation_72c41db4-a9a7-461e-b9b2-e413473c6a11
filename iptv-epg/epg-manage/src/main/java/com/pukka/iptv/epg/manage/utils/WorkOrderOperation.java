package com.pukka.iptv.epg.manage.utils;

import com.alibaba.fastjson.JSON;
import com.pukka.iptv.common.api.feign.epg.IssueOrderFeignClient;
import com.pukka.iptv.common.base.enums.PublishStatusEnum;
import com.pukka.iptv.common.base.vo.CommonResponse;
import com.pukka.iptv.common.data.model.EpgIssueOrder;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @Date 2023-03-09 12:04
 * @Description 工单操作
 */
@Slf4j
@Component
public class WorkOrderOperation {

    @Autowired
    private IssueOrderFeignClient issueOrderFeignClient;

    /**
     * EPG发布接口
     *
     * @param issueOrder
     * @param consumer
     * @return
     */
    public Boolean send(EpgIssueOrder issueOrder, WorkOrderConsumer consumer) {
        // 发布工单
        CommonResponse<Boolean> publish = sendWorkOrder(issueOrder);
        Boolean success = publish.getData();
        log.info(success ? "EPG发布接口调用成功 " : "EPG发布接口调用失败 下发参数: " + JSON.toJSONString(issueOrder));
        // 回调
        consumer.publishCallback(success, success ? PublishStatusEnum.PUBLISHING.getCode() : PublishStatusEnum.FAILPUBLISH.getCode(), publish.getMessage());
        log.info("EPG发布接口反馈信息：{}", publish.getMessage());
        return success;
    }

    public CommonResponse<Boolean> sendWorkOrder(EpgIssueOrder issueOrder) {
        return issueOrderFeignClient.issueOrder(issueOrder);
    }

    @FunctionalInterface
    public interface WorkOrderConsumer {
        // 是否调用成功、即将要修改的发布状态、即将要修改的发布描述
        void publishCallback(boolean success, Integer publishStatus, String description);
    }

}
