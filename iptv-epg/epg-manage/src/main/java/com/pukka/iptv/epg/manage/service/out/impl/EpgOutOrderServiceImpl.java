package com.pukka.iptv.epg.manage.service.out.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.pukka.iptv.common.base.constant.RedisKeyConstants;
import com.pukka.iptv.common.base.enums.PublishStatusEnum;
import com.pukka.iptv.common.base.enums.StatusEnum;
import com.pukka.iptv.common.base.exception.BizException;
import com.pukka.iptv.common.core.util.FtpUtil;
import com.pukka.iptv.common.core.util.SpringUtils;
import com.pukka.iptv.common.data.dto.DateFormatCompletionDto;
import com.pukka.iptv.common.data.dto.RetrieveFileEntity;
import com.pukka.iptv.common.data.model.EpgIssueOrder;
import com.pukka.iptv.common.data.model.epg.EpgFile;
import com.pukka.iptv.common.data.model.epg.EpgNode;
import com.pukka.iptv.common.data.model.epg.EpgOutOrder;
import com.pukka.iptv.common.data.model.epg.EpgTemplate;
import com.pukka.iptv.common.data.vo.req.EpgOrderQueryReq;
import com.pukka.iptv.common.proxy.config.EpgOutProxyProperties;
import com.pukka.iptv.common.redis.service.RedisService;
import com.pukka.iptv.epg.manage.config.NacosConfig;
import com.pukka.iptv.epg.manage.mapper.out.EpgFileMapper;
import com.pukka.iptv.epg.manage.mapper.out.EpgNodeMapper;
import com.pukka.iptv.epg.manage.mapper.out.EpgOutOrderMapper;
import com.pukka.iptv.epg.manage.service.cache.CacheServiceImpl;
import com.pukka.iptv.epg.manage.service.out.EpgOutOrderService;
import com.pukka.iptv.epg.manage.utils.TimeResolutionUtil;
import com.pukka.iptv.epg.manage.utils.WorkOrderOperation;
import com.pukka.iptv.epg.manage.utils.downloadUtils.FtpAnalysisUtil;
import com.pukka.iptv.epg.manage.utils.downloadUtils.FtpClientService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.servlet.http.HttpServletResponse;
import java.util.Date;
import java.util.List;

/**
 * @Author: chiron
 * @Date: 2023/03/01/10:11
 * @Description:
 */
@Slf4j
@Service
public class EpgOutOrderServiceImpl extends ServiceImpl<EpgOutOrderMapper, EpgOutOrder> implements EpgOutOrderService {
    private EpgOutProxyProperties epgOutProxyProperties = SpringUtils.getBean(EpgOutProxyProperties.class);
    @Autowired
    private NacosConfig nacosConfig;
    @Autowired
    private RedisService redisService;
    @Autowired
    private CacheServiceImpl cacheService;
    @Autowired
    private EpgOutOrderMapper epgOutOrderMapper;
    @Autowired
    private EpgFileMapper epgFileMapper;
    @Autowired
    private EpgNodeMapper epgNodeMapper;
    @Autowired
    private FtpAnalysisUtil ftpAnalysisUtil;
    @Autowired
    private WorkOrderOperation workOrderOperation;
    @Autowired
    private FtpClientService ftpClientService;


    /**
     * 新增
     *
     * @param epgOutOrder
     * @return
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean save(EpgOutOrder epgOutOrder) {
        if (ObjectUtils.isEmpty(epgOutOrder)) {
            throw new BizException("新增EPG模板处理失败,参数为空,请检查");
        }
        completeAttribute(epgOutOrder);
        if (ObjectUtils.isEmpty(epgOutOrder.getStatus())) {
            epgOutOrder.setStatus(PublishStatusEnum.WAITPUBLISH.getCode());
        }
        int result = epgOutOrderMapper.insert(epgOutOrder);
        return result > 0;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean removeByIds(List<Long> ids) {
        if (ObjectUtils.isEmpty(ids)) {
            throw new BizException("删除EPG模板处理失败,参数为空,请检查");
        }
        LambdaQueryWrapper<EpgOutOrder> epgOutOrderLambdaQueryWrapper = Wrappers.lambdaQuery(EpgOutOrder.class)
                .in(EpgOutOrder::getId, ids)
                .eq(EpgOutOrder::getStatus, PublishStatusEnum.PUBLISHING.getCode());
        List<EpgOutOrder> epgOutOrders = epgOutOrderMapper.selectList(epgOutOrderLambdaQueryWrapper);
        if (CollectionUtils.isNotEmpty(epgOutOrders)) {
            throw new BizException("当前数据存在处理中状态，无法删除,请检查");
        }
        int result = epgOutOrderMapper.deleteBatchIds(ids);
        return result > 0;
    }

    /**
     * 更新
     *
     * @param epgOutOrder
     * @return
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean updateById(EpgOutOrder epgOutOrder) {
        if (ObjectUtils.isEmpty(epgOutOrder.getId())) {
            throw new BizException("更新EPG模板处理失败,数据为空,请检查");
        }
        EpgOutOrder epgOutOrder1 = epgOutOrderMapper.selectById(epgOutOrder.getId());
        if (ObjectUtils.isEmpty(epgOutOrder1)) {
            throw new BizException("更新EPG模板处理失败,数据库查询为空,请检查");
        }
        if (!PublishStatusEnum.WAITPUBLISH.getCode().equals(epgOutOrder1.getStatus())) {
            throw new BizException("更新EPG模板处理失败,当前模版处理状态需为待处理状态,请检查");
        }
        completeAttribute(epgOutOrder);
        int result = epgOutOrderMapper.updateById(epgOutOrder);
        return result > 0;
    }

    /**
     * 填充名称信息
     *
     * @param epgOutOrder
     * @return
     */
    private void completeAttribute(EpgOutOrder epgOutOrder) {
        if (ObjectUtils.isNotEmpty(epgOutOrder.getEpgFileId())) {
            LambdaQueryWrapper<EpgFile> epgFilesLambdaQueryWrapper = Wrappers.lambdaQuery(EpgFile.class)
                    .eq(EpgFile::getFileId, epgOutOrder.getEpgFileId())
                    .eq(EpgFile::getStatus, StatusEnum.COME.getCode())
                    .last("limit 1");
            EpgFile epgFile = epgFileMapper.selectOne(epgFilesLambdaQueryWrapper);
            if (ObjectUtils.isNotEmpty(epgFile)) {
                epgOutOrder.setEpgFileName(epgFile.getShowName());
                epgOutOrder.setEpgFilePath(epgFile.getFilePath());
            } else {
                throw new BizException("文件不存在,请检查");
            }
        }
        if (ObjectUtils.isNotEmpty(epgOutOrder.getNodeId())) {
            LambdaQueryWrapper<EpgNode> epgNodeLambdaQueryWrapper = Wrappers.lambdaQuery(EpgNode.class)
                    .eq(EpgNode::getId, epgOutOrder.getNodeId())
                    .eq(EpgNode::getStatus, StatusEnum.COME.getCode())
                    .last("limit 1");
            EpgNode epgNode = epgNodeMapper.selectOne(epgNodeLambdaQueryWrapper);
            if (ObjectUtils.isNotEmpty(epgNode)) {
                epgOutOrder.setNodeName(epgNode.getName());
            } else {
                throw new BizException("节点不存在,请检查");
            }
        }
        if (ObjectUtils.isNotEmpty(epgOutOrder.getTemplateId())) {
            EpgTemplate epgTemplate = redisService.getCacheMapValue(RedisKeyConstants.EPG_TEMPLATE_KEY, epgOutOrder.getTemplateId());
            if (ObjectUtils.isNotEmpty(epgTemplate)) {
                epgOutOrder.setTemplateName(epgTemplate.getName());
            } else {
                throw new BizException("模板不存在,请检查");
            }
        }
    }

    /**
     * 分页
     *
     * @param page
     * @param req
     * @return
     */
    @Override
    public IPage pageList(Page<EpgOutOrder> page, EpgOrderQueryReq req) {
        page.setSize(page.getSize() == -1 ? Integer.MAX_VALUE : page.getSize());
        //时间格式化
        DateFormatCompletionDto dateFormatCompletionDto =
                new DateFormatCompletionDto()
                        .setStartTime(req.getStartTime())
                        .setEndTime(req.getEndTime());
        TimeResolutionUtil.dateTimeCompare(dateFormatCompletionDto);
        LambdaQueryWrapper<EpgOutOrder> epgOutOrderLambdaQueryWrapper = Wrappers.lambdaQuery(EpgOutOrder.class)
                .eq(ObjectUtils.isNotEmpty(req.getStatus()), EpgOutOrder::getStatus, req.getStatus())
                .eq(ObjectUtils.isNotEmpty(req.getFileSetId()), EpgOutOrder::getFileSetId, req.getFileSetId())
                .like(ObjectUtils.isNotEmpty(req.getTemplateName()), EpgOutOrder::getTemplateName, req.getTemplateName())
                .ge(ObjectUtils.isNotEmpty(dateFormatCompletionDto.getStartTime()), EpgOutOrder::getCreateTime, dateFormatCompletionDto.getStartTime())
                .le(ObjectUtils.isNotEmpty(dateFormatCompletionDto.getEndTime()), EpgOutOrder::getCreateTime, dateFormatCompletionDto.getEndTime())
                .like(ObjectUtils.isNotEmpty(req.getCreatorName()),EpgOutOrder::getCreatorName,req.getCreatorName())
                .orderByDesc(EpgOutOrder::getId);
        //全表查询时从缓存中获取总数
        if (nacosConfig.isIsrefresh() && epgOutOrderLambdaQueryWrapper.isEmptyOfWhere()) {
            page.setSearchCount(false);
            page.setTotal(cacheService.getOutOrderTotal());
        }
        return this.page(page, epgOutOrderLambdaQueryWrapper);
    }

    /**
     * 处理
     *
     * @param issueOrder
     * @return
     */
    @Override
    public Boolean publish(EpgIssueOrder issueOrder) {
        //获取事件分发信息
        List<EpgOutOrder> epgOutOrders = epgOutOrderMapper.selectBatchIds(issueOrder.getIdList());
        //校验工单状态
        epgOutOrders.stream().filter(order -> !PublishStatusEnum.WAITPUBLISH.getCode().equals(order.getStatus())).forEach(order -> {
            log.warn("调用EPG处理接口 -----> 处理失败,处理状态存在不为待处理状态,请检查");
            throw new BizException("处理失败,事件状态需要为待处理状态,请检查");
        });
        //校验必填字段
        epgOutOrders.stream().filter(order -> StringUtils.isEmpty(order.getNodeId()) || StringUtils.isEmpty(order.getTemplateId())
                        || StringUtils.isEmpty(order.getEpgFileId()) || ObjectUtils.isEmpty(order.getUnCompression()))
                .forEach(order -> {
                    log.warn("调用EPG处理接口 -----> 处理失败,节点信息/模版信息/文件信息为空");
                    throw new BizException("处理失败,该EPG模板处理 必填信息为空");
                });
        boolean result = workOrderOperation.send(issueOrder, (success, publishStatus, description) -> {
            // 更新处理状态以及处理描述
            this.update(Wrappers.lambdaUpdate(EpgOutOrder.class)
                    .set(EpgOutOrder::getPublishTime, new Date())
                    .set(EpgOutOrder::getStatus, publishStatus)
                    .in(EpgOutOrder::getId, issueOrder.getIdList()));
        });
        return result;
    }

    /**
     * EPG事件模块下载文件
     *
     * @param path
     * @param response
     * @return
     */
    @Override
    public Boolean retrieveFile(String path, HttpServletResponse response) {
        if (StringUtils.isEmpty(path)) {
            log.warn("调用EPG事件模块下载文件接口 -----> 下载路径为空");
            throw new BizException("下载路径为空");
        }
        try {
            Boolean existProxyIp = ftpAnalysisUtil.isExistProxyIp(path);
            log.info("当前下载地址:{},是否存在代理地址:{}", path, existProxyIp);
            // 如果包含代理地址，使用代理的方式去ftp下载
            FtpUtil ftpUtil = new FtpUtil(path, existProxyIp, epgOutProxyProperties);
            String ftpContent = ftpUtil.getFtpContent(path);
            if (StringUtils.isEmpty(ftpContent)) {
                log.warn("调用EPG事件模块下载文件接口 -----> 当前下载地址:{},download failed", path);
                throw new BizException("下载内容为空");
            } else {
                //从地址中获取文件名
                String fileName = path.substring(path.lastIndexOf("/") + 1);
                RetrieveFileEntity retrieveFile = new RetrieveFileEntity().setFileName(fileName)
                        .setFileContent(ftpContent).setResponse(response);
                Boolean aBoolean = ftpClientService.readFtpConternt(retrieveFile);
                if (!aBoolean) {
                    log.warn("调用EPG事件模块下载文件接口 -----> 文件ftp信息:{},检索文件并下载失败!", path);
                    return false;
                }
                return true;
            }
        } catch (Exception e) {
            log.error("调用EPG事件模块下载文件接口 -----> 文件ftp信息:{},检索文件并下载失败!", path, e);
        }
        return false;
    }

    @Override
    public Boolean copy(List<Long> idList) {
        if (CollectionUtils.isEmpty(idList)) {
            throw new BizException("新增EPG模板处理失败,参数为空,请检查");
        }
        Long id = idList.get(0);
        EpgOutOrder epgOutOrder = getById(id);
        EpgOutOrder copyEpgOutOrder = new EpgOutOrder();
        copyEpgOutOrder.setNodeId(epgOutOrder.getNodeId());
        copyEpgOutOrder.setNodeName(epgOutOrder.getNodeName());
        copyEpgOutOrder.setTemplateId(epgOutOrder.getTemplateId());
        copyEpgOutOrder.setTemplateName(epgOutOrder.getTemplateName());
        copyEpgOutOrder.setEpgFileId(epgOutOrder.getEpgFileId());
        copyEpgOutOrder.setEpgFilePath(epgOutOrder.getEpgFilePath());
        copyEpgOutOrder.setStatus(PublishStatusEnum.WAITPUBLISH.getCode());
        copyEpgOutOrder.setRemark(epgOutOrder.getRemark());
        copyEpgOutOrder.setUnCompression(epgOutOrder.getUnCompression());
        int result = epgOutOrderMapper.insert(copyEpgOutOrder);
        return result > 0;
    }
}
