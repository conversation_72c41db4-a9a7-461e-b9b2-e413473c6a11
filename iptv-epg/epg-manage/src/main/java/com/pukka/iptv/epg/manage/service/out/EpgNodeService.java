package com.pukka.iptv.epg.manage.service.out;

import com.baomidou.mybatisplus.extension.service.IService;
import com.pukka.iptv.common.data.model.epg.EpgNode;

import java.util.List;

/**
 * @Author: chiron
 * @Date: 2023/03/02/09:47
 * @Description:
 */

public interface EpgNodeService extends IService<EpgNode> {

    /**
     * 新增
     * @param epgNode
     * @return
     */
    Boolean increase(EpgNode epgNode);

    /**
     *编辑
     * @param epgNode
     * @return
     */
    Boolean updateWithId(EpgNode epgNode);

    /**
     * 删除
     * @param lspId
     * @return
     */
    Boolean deleteById(String lspId);

    /**
     * 批量删除
     * @param ids
     * @return
     */
    Boolean deleteByIds(List<String> ids);

    /**
     * 从缓存中查询所有节点信息
     * @return
     */
    List<EpgNode> redisList();
}
