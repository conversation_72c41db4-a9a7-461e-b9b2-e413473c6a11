package com.pukka.iptv.epg.manage.service.sys.impl;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.pukka.iptv.common.base.enums.CommonResponseEnum;
import com.pukka.iptv.common.base.exception.CommonResponseException;
import com.pukka.iptv.common.data.model.epg.SysUserTemplate;
import com.pukka.iptv.common.data.vo.epg.SysUserTemplateVo;
import com.pukka.iptv.common.data.vo.req.SysUserTemplateReq;
import com.pukka.iptv.epg.manage.mapper.sys.SysUserTemplateMapper;
import com.pukka.iptv.epg.manage.service.sys.SysUserTemplateService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 *
 * @author: zhengcl
 * @date: 2021-8-17 16:48:36
 */

@Service
public class SysUserTemplateServiceImpl extends ServiceImpl<SysUserTemplateMapper, SysUserTemplate> implements SysUserTemplateService {

    @Autowired
    private SysUserTemplateMapper sysUserTemplateMapper;


    /**
     * 根据模板id分页查询用户
     * @param page
     * @param sysUserTemplateReq
     * @return
     */
    @Override
    public Page<SysUserTemplateVo> pageUserByTemplateId(Page page, SysUserTemplateReq sysUserTemplateReq) {
        if(sysUserTemplateReq.getTemplateId()==null){
            throw new CommonResponseException(CommonResponseEnum.ERROR_PARAMETER);
        }
        return sysUserTemplateMapper.pageUserByTemplateId(page, sysUserTemplateReq);

    }

}


