package com.pukka.iptv.epg.manage.service.out;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.pukka.iptv.common.data.model.EpgIssueOrder;
import com.pukka.iptv.common.data.model.epg.EpgOutOrder;
import com.pukka.iptv.common.data.vo.req.EpgOrderQueryReq;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * @Author: chiron
 * @Date: 2023/03/01/10:07
 * @Description:
 */

public interface EpgOutOrderService extends IService<EpgOutOrder> {

    /**
     * 删除
     * @param ids
     * @return
     */
    boolean removeByIds(List<Long> ids);

    /**
     * 分页
     * @param req
     * @return
     */
    IPage<EpgOutOrder> pageList(Page<EpgOutOrder> page, EpgOrderQueryReq req);

    /**
     * 发布
     * @param issueOrder
     * @return
     */
    Boolean publish(EpgIssueOrder issueOrder);

    /**
     * 下载文件
     * @param path
     * @param response
     * @return
     */
    Boolean retrieveFile(String path, HttpServletResponse response);

    /**
     * 复制
     * @param idList
     * @return
     */
    Boolean copy(List<Long> idList);
}
