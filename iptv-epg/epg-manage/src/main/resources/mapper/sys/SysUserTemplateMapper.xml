<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">


<!-- 不使用namespace的话sql搜索定位会比较方便 -->
<mapper namespace="com.pukka.iptv.epg.manage.mapper.sys.SysUserTemplateMapper">


    <select id="pageUserByTemplateId" resultType="com.pukka.iptv.common.data.vo.epg.SysUserTemplateVo">
        select sut.id as id,su.id as user_id,su.name as name,su.description as description,sut.create_time as create_time
        from sys_user su,
             (select id,user_id,create_time
              from sys_user_template  where template_id = #{sysUserTemplateReq.templateId}) sut where su.id=sut.user_id and su.status = 1
            order by sut.create_time desc
    </select>
    <select id="selectByUserId" resultType="java.lang.Long">
        select template_id
        from sys_user_template where user_id = #{userId};
    </select>
</mapper>

