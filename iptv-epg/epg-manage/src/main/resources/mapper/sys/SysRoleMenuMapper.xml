<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">


<!-- 不使用namespace的话sql搜索定位会比较方便 -->
<mapper namespace="com.pukka.iptv.epg.manage.mapper.sys.SysRoleMenuMapper">

	<!-- 表名 -->
	<sql id="tableName">
        sys_role_menu
    </sql>
    
    <sql id="columns">
        `id`,`role_id`,`menu_id`,`create_time`,`update_time`
    </sql>
    
    <sql id="where">
        <where>
	        <if test="null != id">
            AND `id` = #{id}
            </if>
	        <if test="null != roleId">
            AND `role_id` = #{roleId}
            </if>
	        <if test="null != menuId">
            AND `menu_id` = #{menuId}
            </if>
	        <if test="null != createTime and '' != createTime">
            AND `create_time` = #{createTime}
            </if>
	        <if test="null != updateTime and '' != updateTime">
            AND `update_time` = #{updateTime}
            </if>
        </where>
    </sql>

</mapper>

