package com.pukka.iptv;

import com.pukka.iptv.common.core.util.FileMd5Util;
import org.junit.Test;

import java.io.File;
import java.io.FileInputStream;
import java.io.IOException;

import static org.junit.Assert.assertTrue;

/**
 * Unit test for simple App.
 */
public class AppTest {
    /**
     * Rigorous Test :-)
     */
    @Test
    public void shouldAnswerWithTrue() {
        assertTrue(true);
    }

    @Test
    public void testMd5() {
        String filePath = "/Users/<USER>/Downloads/111-2.tar";
        //读取本地文件流方法
        File file = new File(filePath);
        try (FileInputStream fis = new FileInputStream(file)) {
            // 获取文件长度
            int length = (int) file.length();
            // 创建一个byte数组来存储文件内容
            byte[] buffer = new byte[length];
            // 读取文件内容到byte数组
            int bytesRead = fis.read(buffer);
            // 确保已经读取了整个文件内容
            if (bytesRead == length) {
                // 使用byte数组进行进一步处理
                String read = new String(buffer, "utf-8");
                String computeMD5 = FileMd5Util.computeMD5(read);
                System.out.println(computeMD5);
            } else {
                System.err.println("Error: File was not completely read.");
            }
        } catch (IOException e) {
            e.printStackTrace();
        }
    }
}
