package com.pukka.iptv.epg.c1out.executor;

import org.mybatis.spring.annotation.MapperScan;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.web.servlet.ServletComponentScan;
import org.springframework.cloud.openfeign.EnableFeignClients;
import org.springframework.retry.annotation.EnableRetry;

/**
 * <AUTHOR> 2018-10-28 00:38:13
 */

@EnableRetry
@EnableFeignClients("com.pukka.iptv.common.api.feign")
@MapperScan("com.pukka.iptv.epg.c1out.executor.c1out.dao.mapper")
@ServletComponentScan
@SpringBootApplication
public class C1outExecutorApplication {

    public static void main(String[] args) {
        SpringApplication.run(C1outExecutorApplication.class, args);
        System.out.println("C1outExecutorApplication 服务启动!");
    }

}