package com.pukka.iptv.epg.c1out.executor.jobhandler.outorder;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.pukka.iptv.common.base.enums.PublishStatusEnum;
import com.pukka.iptv.common.core.util.SpringUtils;
import com.pukka.iptv.common.core.util.StringUtils;
import com.pukka.iptv.common.data.model.epg.EpgOutOrder;
import com.pukka.iptv.common.data.vo.epg.EpgOutOrderVo;
import com.pukka.iptv.epg.c1out.executor.c1out.dao.entity.ResultResponse;
import com.pukka.iptv.epg.c1out.executor.c1out.utils.StrategyHandlerUtil;
import com.pukka.iptv.epg.c1out.executor.common.retry.RetryException;
import com.pukka.iptv.epg.c1out.executor.common.util.C1outUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @description: 消费工单任务
 */
@Component
@Slf4j
public class OutOrderConsume implements IOutOrder {

    private C1outUtils c1OutUtils = SpringUtils.getBean(C1outUtils.class);
    private StrategyHandlerUtil strategyHandlerUtil = SpringUtils.getBean(StrategyHandlerUtil.class);
    private String errorDescription;

    @Override
    public String getClassPropertyName() {
        return "工单消费执行器";
    }

    @Override
    public String getErrorDescription() {
        return errorDescription;
    }


    @Override
    public boolean outHandle(String msg) {
        log.debug("执行消费工单任务操作 ->>>>>> 开始执行");
        EpgOutOrderVo epgOutOrderVo = JSONObject.parseObject(msg, EpgOutOrderVo.class);
        try {
            //下发下游csp
            ResultResponse resultResponse = c1OutUtils.startSendCsp(epgOutOrderVo.getPath(), epgOutOrderVo.getCspId(), epgOutOrderVo.getLspId(), epgOutOrderVo.getFileSetId(), epgOutOrderVo.getCmdFileUrl());
            if (!resultResponse.getResult()) {
                errorDescription = resultResponse.getDescription();
                log.info("执行消费工单任务操作 ->>>>>> 工单消费成功,发送至下游csp:{} 工单Id:{} 工单信息:{}", epgOutOrderVo.getPath(), epgOutOrderVo.getFileSetId(), JSON.toJSONString(epgOutOrderVo));
                return false;
            }
            return true;
        } catch (Exception e) {
            errorDescription = "执行消费工单任务操作 ->>>>>> 工单消费失败,工单Id:" + epgOutOrderVo.getFileSetId();
            log.error(errorDescription + ",错误信息:" + e.getMessage());
            throw new RetryException(errorDescription);
        }
    }

    @Override
    public List<EpgOutOrder> getReportEntity(String msg) {
        EpgOutOrderVo epgOutOrderVo = JSON.parseObject(msg, EpgOutOrderVo.class);
        strategyHandlerUtil.buildErrorResp(epgOutOrderVo, StringUtils.isEmpty(getErrorDescription()) ? "执行发布工单任务失败" : getErrorDescription());
        List<EpgOutOrder> outOrders = c1OutUtils.getReportEntity(new ArrayList<EpgOutOrder>() {
            {
                add(epgOutOrderVo);
            }
        }, "执行发布工单任务失败");
        return outOrders;
    }

}
