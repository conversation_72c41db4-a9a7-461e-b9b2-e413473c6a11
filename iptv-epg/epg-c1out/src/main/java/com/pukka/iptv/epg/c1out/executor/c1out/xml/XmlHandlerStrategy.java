package com.pukka.iptv.epg.c1out.executor.c1out.xml;

import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.pukka.iptv.common.base.constant.ObjectsTypeConstants;
import com.pukka.iptv.common.base.enums.EpgElementTypeEnums;
import com.pukka.iptv.epg.c1out.executor.c1out.dao.entity.OrderObjectsEntity;
import com.pukka.iptv.epg.c1out.executor.c1out.dao.entity.OrderXmlEntity;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.dom4j.Document;
import org.dom4j.DocumentHelper;
import org.dom4j.Element;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * @author: chiron
 * Date: 2023/3/21 9:32 AM
 * Description: 组装xml文件类
 */
@Slf4j
@Component(ObjectsTypeConstants.ISSUE_BEAN_PRE + ObjectsTypeConstants.XMLGENERATERULES_BEAN_PRE)
public class XmlHandlerStrategy implements IXmlHandlerStrategy {
    /**
     * 生成xml文件
     *
     * @param orderXmlEntity
     * @return
     */
    @Override
    public Element generateXMLFile(OrderXmlEntity orderXmlEntity) {
        Element root = null;
        if (ObjectUtils.isNotEmpty(orderXmlEntity) && CollectionUtils.isNotEmpty(orderXmlEntity.getSubOrderObjectsEntities())
                && orderXmlEntity.getSubOrderObjectsEntities().size() > 0) {
            root = createAdiElement();
            boolean objects = getObjects(orderXmlEntity, root);
            if (!objects) {
                return null;
            }
        }
        return root;
    }


    /**
     * 创建元素
     *
     * @return
     */
    @Override
    public Element createAdiElement() {
        Element root = DocumentHelper.createElement("ADI");
        root.addAttribute("xmlns:xsi", "http://www.w3.org/2001/XMLSchema-instance");
        return root;
    }


    /**
     * 组成objects
     *
     * @param orderXmlEntity
     * @param root
     * @return
     */
    @Override
    public boolean getObjects(OrderXmlEntity orderXmlEntity, Element root) {
        Element objects = root.addElement("Objects");
        List<OrderObjectsEntity> subOrderObjectsEntities = orderXmlEntity.getSubOrderObjectsEntities();
        //组建objects
        try {
            for (OrderObjectsEntity orderObjectsEntity : subOrderObjectsEntities) {
                Element object = objects.addElement("Object");
                if (StringUtils.isBlank(orderObjectsEntity.getElementType()) || StringUtils.isBlank(orderObjectsEntity.getId())) {
                    log.error("生成xml文件失败,执行创建objects时工单Attribute属性存在空值");
                    return false;
                }

                object.addAttribute("ElementType", orderObjectsEntity.getElementType() != null ? orderObjectsEntity.getElementType() : "");
                object.addAttribute("ID", orderObjectsEntity.getId() != null ? orderObjectsEntity.getId() : "");
                if (EpgElementTypeEnums.EPGFILE.getInfo().equals(orderObjectsEntity.getElementType())) {
                    if (ObjectUtils.isNotEmpty(orderObjectsEntity.getAction())) {
                        object.addAttribute("Action", orderObjectsEntity.getAction());
                    } else {
                        log.error("生成xml文件失败,执行创建EPGFile objects时工单Attribute属性Action为空值,工单信息:{}", orderObjectsEntity);
                        return false;
                    }
                }
                orderObjectsEntity.getPropertyDic().forEach((key, value) -> {
                    object.addElement("Property").addAttribute("Name", key).addText(value != null ? value : "");
                });
            }
        } catch (Exception exception) {
            log.error("生成xml文件:{}失败,执行创建objects时:{}", orderXmlEntity, exception);
            return false;
        }
        return true;
    }


    /**
     * 获取生成内容
     *
     * @param root
     * @return
     */
    @Override
    public String getOutXmlContent(Element root) {
        Document document = DocumentHelper.createDocument(root);
        return document.asXML();
    }
}