package com.pukka.iptv.epg.c1out.executor.c1out.strategy.handler;

import com.pukka.iptv.common.data.vo.epg.EpgOutOrderVo;
import com.pukka.iptv.epg.c1out.executor.c1out.dao.entity.OrderObjectsEntity;
import com.pukka.iptv.epg.c1out.executor.c1out.dao.entity.ResultResponse;

import java.io.Serializable;

/**
 * @Author: chiron
 * @Date: 2023/03/08/10:49
 * @Description:
 */

public interface IBuildHandler <T extends Serializable> {


    /**
     * 构建xml实体
     * @param epgOutOrderVo
     * @return
     */
    ResultResponse buildXmlOrderEntity(EpgOutOrderVo epgOutOrderVo) ;

    /**
     * 获取EPGFileSet
     * @param epgOutOrderVo
     * @return
     */
    OrderObjectsEntity achieveFileSet(EpgOutOrderVo epgOutOrderVo);

    /**
     * 获取EPGFile
     * @param epgOutOrderVo
     * @return
     */
    OrderObjectsEntity achieveFile(EpgOutOrderVo epgOutOrderVo);
}
