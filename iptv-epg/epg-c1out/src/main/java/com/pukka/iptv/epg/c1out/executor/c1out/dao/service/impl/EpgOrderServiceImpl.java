package com.pukka.iptv.epg.c1out.executor.c1out.dao.service.impl;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.pukka.iptv.common.data.model.epg.EpgOutOrder;
import com.pukka.iptv.common.data.vo.epg.EpgOutOrderVo;
import com.pukka.iptv.epg.c1out.executor.c1out.dao.mapper.EpgOrderMapper;
import com.pukka.iptv.epg.c1out.executor.c1out.dao.service.EpgOrderService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.springframework.stereotype.Service;

/**
 * @Author: chiron
 * @Date: 2023/03/01/10:11
 * @Description:
 */
@Slf4j
@Service
public class EpgOrderServiceImpl extends ServiceImpl<EpgOrderMapper, EpgOutOrder> implements EpgOrderService {


    @Override
    public Boolean updateStatus(EpgOutOrderVo epgOutOrderVo) {
        try {
            this.update(
                    Wrappers.lambdaUpdate(EpgOutOrder.class)
                            .set(ObjectUtils.isNotEmpty(epgOutOrderVo.getStatus()),EpgOutOrder::getStatus, epgOutOrderVo.getStatus())
                            .set(ObjectUtils.isNotEmpty(epgOutOrderVo.getStatusDescription()),EpgOutOrder::getStatusDescription, epgOutOrderVo.getStatusDescription())
                            .set(ObjectUtils.isNotEmpty(epgOutOrderVo.getFeedbackTime()),EpgOutOrder::getFeedbackTime, epgOutOrderVo.getFeedbackTime())
                            .set(ObjectUtils.isNotEmpty(epgOutOrderVo.getDuration()),EpgOutOrder::getDuration, epgOutOrderVo.getDuration())
                            .eq(ObjectUtils.isNotEmpty(epgOutOrderVo.getId()),EpgOutOrder::getId, epgOutOrderVo.getId()));
            return true;
        } catch (Exception exception) {
            log.error("更新状态信息失败,错误信息:{}", exception.getMessage());
        }
        return false;
    }

    @Override
    public Boolean updateFtpCmdPath(EpgOutOrderVo epgOutOrderVo) {
        try {
            this.update(
                    Wrappers.lambdaUpdate(EpgOutOrder.class)
                            .set(EpgOutOrder::getCmdFileUrl, epgOutOrderVo.getCmdFileUrl())
                            .eq(EpgOutOrder::getId, epgOutOrderVo.getId()));
            return true;
        } catch (Exception exception) {
            log.error("更新ftp信息失败,错误信息:{}", exception.getMessage());
        }
        return false;
    }
}
