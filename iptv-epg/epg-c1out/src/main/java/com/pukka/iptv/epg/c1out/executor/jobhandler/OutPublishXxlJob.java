package com.pukka.iptv.epg.c1out.executor.jobhandler;

import com.pukka.iptv.common.core.constant.SymbolConstant;
import com.pukka.iptv.common.core.util.SafeUtil;
import com.pukka.iptv.common.core.util.StringUtils;
import com.pukka.iptv.common.rabbitmq.constans.IssueOrderConstant;
import com.pukka.iptv.epg.c1out.executor.common.util.C1outUtils;
import com.pukka.iptv.epg.c1out.executor.jobhandler.outorder.IOutOrder;
import com.pukka.iptv.epg.c1out.executor.jobhandler.outorder.OutOrderConsume;
import com.xxl.job.core.context.XxlJobHelper;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.io.IOException;


/**
 * <AUTHOR>
 * @description:
 * @create 2023-03-30 14:38
 */
@Component
@Slf4j
public class OutPublishXxlJob {

    /**
     * 消费工单执行器（Bean模式）
     */
    @XxlJob("IssueOrderJobHandler")
    public void IssueOrderJobHandler() {
        log.debug("开始处理消费工单执行器!");
        String param = SafeUtil.getString(XxlJobHelper.getJobParam());
        log.debug("消费工单执行器 -----> param:{}", param);
        Integer pullCount = 0;
        String queueName = IssueOrderConstant.OUT_ORDER_SEND_QUEUE;
        try {
            if (StringUtils.isNotEmpty(param)) {
                String[] split = param.split(SymbolConstant.COMMA);
                if (split.length <= 1) {
                    pullCount = Integer.valueOf(param);
                    log.warn("消费工单执行器job -----> param:{} 消费工单执行器 队列信息参数不全!", param);
                    return;
                } else {
                    pullCount = StringUtils.isNotEmpty(split[0]) ? Integer.valueOf(split[0]) : 3;
                    if (StringUtils.isNotEmpty(split[1])) {
                        //添加节点信息
                        queueName = queueName + SymbolConstant.UNDER_SCORE + split[1];
                    }
                }
            }
        } catch (Exception exception) {
            log.error("消费工单执行器 getJobParam 有误!错误信息:{}", exception);
        }
        try {
            IOutOrder outOrder = new OutOrderConsume();
            C1outUtils c1OutUtils = new C1outUtils();
            c1OutUtils.receive(pullCount, outOrder, queueName);
        } catch (IOException e) {
            log.error("消费工单执行器job失败.错误信息:{}", e);
        }
    }

}
