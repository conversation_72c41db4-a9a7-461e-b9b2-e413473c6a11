package com.pukka.iptv.epg.c1out.executor.jobhandler.outorder;

import com.alibaba.fastjson.JSON;
import com.pukka.iptv.common.base.enums.PriorityEnums;
import com.pukka.iptv.common.base.enums.PublishStatusEnum;
import com.pukka.iptv.common.base.enums.StatusEnum;
import com.pukka.iptv.common.data.model.EpgIssueOrder;
import com.pukka.iptv.common.data.model.epg.EpgOutOrder;
import com.pukka.iptv.common.data.vo.epg.EpgOutOrderVo;
import com.pukka.iptv.common.rabbitmq.constans.IssueOrderConstant;
import com.pukka.iptv.common.rabbitmq.vo.MessageBody;
import com.pukka.iptv.epg.c1out.executor.c1out.dao.mapper.EpgOrderMapper;
import com.pukka.iptv.epg.c1out.executor.c1out.utils.StrategyHandlerUtil;
import com.pukka.iptv.epg.c1out.executor.common.util.C1outUtils;
import com.rabbitmq.client.Channel;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.amqp.rabbit.annotation.Exchange;
import org.springframework.amqp.rabbit.annotation.Queue;
import org.springframework.amqp.rabbit.annotation.QueueBinding;
import org.springframework.amqp.rabbit.annotation.RabbitListener;
import org.springframework.amqp.support.AmqpHeaders;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.messaging.handler.annotation.Header;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @description: 生成工单任务
 */
@Component
@Slf4j
public class OutOrderCreate implements IOutOrder {
    @Autowired
    private EpgOrderMapper epgOrderMapper;

    private String errorDescription;

    @Override
    public String getErrorDescription() {
        return this.errorDescription;
    }

    @Override
    public String getClassPropertyName() {
        return "工单创建执行器";
    }

    @Autowired
    private C1outUtils c1OutUtils;
    @Autowired
    private StrategyHandlerUtil strategyHandlerUtil;

    @RabbitListener(bindings = {
            @QueueBinding(value = @Queue(value = IssueOrderConstant.OUT_BASE_ORDER_QUEUE),
                    exchange = @Exchange(value = IssueOrderConstant.OUT_EXCHANGE),
                    key = IssueOrderConstant.OUT_BASE_ORDER_ROUTING)}, containerFactory = "rabbitListenerContainerFactory")
    private void recieved(MessageBody messageBody, Channel channel, @Header(AmqpHeaders.DELIVERY_TAG) Long deliveryTag) throws Exception {
        log.debug("RabbitMQ 队列:{}, 开始监听", IssueOrderConstant.OUT_BASE_ORDER_QUEUE);
        try {
            if (ObjectUtils.isEmpty(messageBody) || ObjectUtils.isEmpty(messageBody.getMsg())) {
                log.warn("执行生成工单任务操作 ->>>>>> 生成工单失败,获取信息为空!");
                return;
            }
            Map<String, Object> msg = messageBody.getMsg();
            String message = JSON.toJSONString(msg);
            log.info("RabbitMQ 队列:{}, 获取信息:{}", IssueOrderConstant.OUT_BASE_ORDER_QUEUE, message);
            c1OutUtils.retryOrderHandle(this, message);
        } catch (Exception exception) {
            log.info("RabbitMQ 队列:{},获取信息失败:{}", IssueOrderConstant.OUT_BASE_ORDER_QUEUE, exception);
        } finally {
            //执行是否成功，返回确认应答
            channel.basicAck(deliveryTag, false);
        }
    }

    @Override
    public List<EpgOutOrder> getReportEntity(String msg) {
        EpgIssueOrder issueOrder = JSON.parseObject(msg, EpgIssueOrder.class);
        //获取事件分发信息
        List<EpgOutOrder> epgOutOrders = epgOrderMapper.selectBatchIds(issueOrder.getIdList());
        List<EpgOutOrder> outOrders = c1OutUtils.getReportEntity(epgOutOrders, "执行生成工单任务失败");
        return outOrders;
    }

    @Override
    public boolean outHandle(String msg) {
        log.info("执行生成工单任务操作 ->>>>>> 开始执行");
        //获取消息参数
        EpgIssueOrder issueOrder = JSON.parseObject(msg, EpgIssueOrder.class);
        List<EpgOutOrderVo> epgOutOrderVoList;
        try {
            //获取事件分发信息
            List<EpgOutOrder> epgOutOrders = epgOrderMapper.selectBatchIds(issueOrder.getIdList());
            if (!CollectionUtils.isEmpty(epgOutOrders) && epgOutOrders.size() > 0) {
                List<EpgOutOrderVo> epgOutOrderVos = new ArrayList<>();
                epgOutOrders.forEach(epgOutOrder -> {
                    //清空事件分发表状态描述
                    epgOutOrder.setStatus(PublishStatusEnum.PUBLISHING.getCode()).setStatusDescription(StringUtils.EMPTY).setResultFileUrl(StringUtils.EMPTY);
                    int i = epgOrderMapper.updateById(epgOutOrder);
                    if (i <= 0) {
                        log.warn("执行生成工单任务操作 ->>>>>>  更新事件分发表状态描述失败,数据:{}", JSON.toJSONString(epgOutOrder));
                    }
                    //设置优先级
                    EpgOutOrderVo epgOutOrderVo = new EpgOutOrderVo().setPriority(
                            ObjectUtils.isNotEmpty(issueOrder.getPriority()) ? issueOrder.getPriority() : PriorityEnums.GENERAL.getValue());
                    BeanUtils.copyProperties(epgOutOrder, epgOutOrderVo);
                    epgOutOrderVos.add(epgOutOrderVo);
                });
                log.info("执行生成工单任务操作 ->>>>>>  获取EPG信息成功,数据:{}", JSON.toJSONString(epgOutOrderVos));
                //执行生成工单操作
                epgOutOrderVoList = strategyHandlerUtil.generateXmlUpload(epgOutOrderVos);
            } else {
                log.info("执行生成工单任务操作 ->>>>>>  获取EPG信息为空,数据:{}", msg);
                return false;
            }
        } catch (Exception exception) {
            errorDescription = "执行生成工单任务操作 ->>>>>> 数据库查询EPG信息操作失败";
            log.error("执行生成工单任务操作 ->>>>>> 数据库查询EPG信息操作失败,工单数据 {} 错误信息:{}", msg, exception);
            return false;
        }
        try {
            if (!CollectionUtils.isEmpty(epgOutOrderVoList) && epgOutOrderVoList.size() > 0) {
                log.info("执行生成工单任务 ->>>>>> 生成工单成功,数据:{}", JSON.toJSONString(epgOutOrderVoList));
                Boolean extracted = c1OutUtils.extracted(epgOutOrderVoList);
                return extracted;
            }
            log.warn("执行生成工单任务操作 ->>>>>> 数据:{}，生成工单实体为空", msg);
        } catch (Exception exception) {
            log.error("执行生成工单任务操作 ->>>>>> 工单任务发送MQ失败,工单数据 {} 错误信息:{}", msg, exception);
        }
        return false;
    }

}
