package com.pukka.iptv.epg.c1out.executor.common.util;

import com.alibaba.fastjson.JSON;
import com.pukka.iptv.common.core.constant.SymbolConstant;
import com.pukka.iptv.common.core.util.SpringUtils;
import com.pukka.iptv.common.core.util.StringUtils;
import com.pukka.iptv.common.data.model.epg.EpgOutOrder;
import com.pukka.iptv.common.data.vo.epg.EpgOutOrderVo;
import com.pukka.iptv.common.rabbitmq.constans.IssueOrderConstant;
import com.pukka.iptv.epg.c1out.executor.c1out.dao.entity.ResultResponse;
import com.pukka.iptv.epg.c1out.executor.c1out.dao.service.EpgOrderService;
import com.pukka.iptv.epg.c1out.executor.c1out.utils.StrategyHandlerUtil;
import com.pukka.iptv.epg.c1out.executor.c1out.webservice.Request.CSPRequest;
import com.pukka.iptv.epg.c1out.executor.c1out.webservice.Request.CSPRequestServiceLocator;
import com.pukka.iptv.epg.c1out.executor.c1out.webservice.Request.CSPResult;
import com.pukka.iptv.epg.c1out.executor.common.retry.RetryException;
import com.pukka.iptv.epg.c1out.executor.jobhandler.outorder.IOutOrder;
import com.rabbitmq.client.Channel;
import com.rabbitmq.client.GetResponse;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.springframework.amqp.rabbit.core.RabbitTemplate;
import org.springframework.retry.RetryCallback;
import org.springframework.retry.RetryContext;
import org.springframework.retry.support.RetryTemplate;
import org.springframework.stereotype.Component;

import java.io.IOException;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @Date 2021/10/29 8:09 下午
 * @description: 分发工具类
 * @Version 1.0
 */
@Slf4j
@Component
public class C1outUtils {

    private EpgOrderService epgOrderService = SpringUtils.getBean(EpgOrderService.class);

    private RabbitTemplate rabbitTemplate = SpringUtils.getBean("outRabbitTemplate");

    private StrategyHandlerUtil strategyHandlerUtil = SpringUtils.getBean(StrategyHandlerUtil.class);

    private RetryTemplate retryTemplate = SpringUtils.getBean("rabbitRetry");

    private Channel channel;

    /**
     * 获取Channel
     *
     * @return
     */
    private Channel getChannel(String queueName) {
        if (channel == null) {
            channel = rabbitTemplate.getConnectionFactory().createConnection().createChannel(true);
        }
        return channel;
    }

    /**
     * 清理channel
     */
    private void delChannel(String queueName) {
        if (channel == null) {
            return;
        }
        try {
            if (channel.isOpen()) {
                channel.close();
            }
        } catch (Exception exception) {
            log.error("当前RabbitMQ 队列:{}, 关闭MQ channel通道失败！错误原因:{}", queueName, exception);
        }
    }


    /**
     * 分发模块上报
     *
     * @param iOutOrder
     * @param msg
     * @return
     */
    public boolean OutPublishReport(IOutOrder iOutOrder, String msg) {
        try {
            List<EpgOutOrder> reportEntity = iOutOrder.getReportEntity(msg);
            if (reportEntity != null) {
                reportEntity.stream().forEach(vo -> {
                    epgOrderService.updateById(vo);
                    log.info("分发模块上报更新数据库完毕,message:{}..", vo);
                });
            }
        } catch (Exception exception) {
            log.error("分发模块上报更新数据库失败,msg:", msg);
            return false;
        }
        return true;
    }

    /**
     * retry
     *
     * @param iOutOrder
     * @param msg
     * @throws Throwable
     */
    public void retryOrderHandle(IOutOrder iOutOrder, String msg) {
        try {
            RetryCallback<Object, Throwable> objectThrowableRetryCallback = new RetryCallback<Object, Throwable>() {
                @Override
                public Object doWithRetry(RetryContext retryContext) throws Throwable {
                    boolean b = iOutOrder.outHandle(msg);
                    if (!b) {
                        OutPublishReport(iOutOrder, msg);
                    }
                    return null;
                }
            };
            retryTemplate.execute(objectThrowableRetryCallback);
        } catch (Throwable throwable) {
            log.error("当前发布执行器:{} 执行异常，分发模块进行上报操作！", iOutOrder.getClassPropertyName());
            OutPublishReport(iOutOrder, msg);
        }
    }

    /**
     * 从mq中主动拉取消息，autoack
     *
     * @param count
     * @throws IOException
     */
    public void receive(Integer count, IOutOrder iOutOrder, String queueName) throws IOException {
        try {
            Channel channel = getChannel(queueName);
            long messageCount = channel.messageCount(queueName);
            if (messageCount <= 0) {
                log.debug("当前RabbitMQ 队列:{}, messageCountCount为空", queueName);
                //清理channel
                delChannel(queueName);
                return;
            }
            // 查询队列未消费的消息数，可以监控消息堆积的情况
            log.info("RabbitMQ 队列:{}, messageCountCount:{}", queueName, messageCount);
            long deliveryTag = 0L;
            // 拉取条数
            int counter = 3;
            if (count != null && count > 0) {
                counter = count;
            }
            for (int i = 0; i < counter; i++) {
                try {
                    GetResponse response = channel.basicGet(queueName, true);
                    if (response != null) {
                        try {
                            String msg = new String(response.getBody(), "UTF-8");
                            retryOrderHandle(iOutOrder, msg);
                        } catch (Exception exception) {
                            log.error("RabbitMQ 队列:{}, 分发执行器:{} 异常:{}", queueName, iOutOrder.getClassPropertyName(), exception);
                        }
                    } else {
                        log.debug("当前RabbitMQ 队列:{}, messageCountCount为空", queueName);
                        break;
                    }
                    messageCount = channel.messageCount(queueName);
                    if (messageCount <= 0) {
                        log.info(queueName + " 队列为空，结束接收");
                        break;
                    }
                } catch (Exception e) {
                    log.error("RabbitMQ 队列:{}, 分发执行器:{} 异常:{}", queueName, iOutOrder.getClassPropertyName(), e);
                }
            }
        } catch (Exception exception) {
            log.error("RabbitMQ 队列:{}, 分发执行器:{} 获取信息异常:{}", queueName, iOutOrder.getClassPropertyName(), exception);
        } finally {
            //清理channel
            delChannel(queueName);
        }
    }

    /**
     * 下发工单数据至下游
     *
     * @param path
     * @param cspId
     * @param lspId
     * @param correlateId
     * @param upFilePath
     * @return
     */
    public ResultResponse startSendCsp(String path, String cspId, String lspId, String correlateId, String upFilePath) {
        ResultResponse result = new ResultResponse();
        try {
            RetryCallback<Object, Throwable> objectThrowableRetryCallback = new RetryCallback<Object, Throwable>() {
                @Override
                public Object doWithRetry(RetryContext retryContext) throws Throwable {
                    ResultResponse resultResponse = new ResultResponse();
                    try {
                        //发送webservice
                        CSPResult cspResult;
                        java.net.URL portAddress = new java.net.URL(path);
                        CSPRequest client = new CSPRequestServiceLocator().getctms(portAddress);
                        cspResult = client.execCmd(cspId, lspId,
                                correlateId, upFilePath);
                        log.info("执行发布操作 ->>>>>> 下发工单数据至下游,webservice结果:{},correlateId:{},工单地址代理后地址:{},ErrorDescription:{}", cspResult.getResult(), correlateId, upFilePath, cspResult.getErrorDescription());
                        resultResponse.setResult(true);
                        if (cspResult.getResult() == -1) {
                            String errorMsg = String.format("执行发布操作 ->>>>>> 下发工单数据至下游,webservice结果:%s,correlateId:%s,工单地址:%s,ErrorDescription:%s"
                                    , cspResult.getResult(), correlateId, upFilePath, cspResult.getErrorDescription());
                            log.warn(errorMsg);
                            resultResponse.setErrorResult(errorMsg);
                        }
                    } catch (Exception exception) {
                        log.error("执行发布操作 ->>>>>> 下发工单数据至下游,webservice结果:CTMS 失败,下发地址:{},correlateId:{},ErrorDescription:{}", path, correlateId, exception.getMessage());
                        throw new RetryException(exception.getMessage());
                    }
                    return resultResponse;
                }
            };
            Object execute = retryTemplate.execute(objectThrowableRetryCallback);
            result = (ResultResponse) execute;
        } catch (Throwable throwable) {
            String errorMsg = "执行发布操作 ->>>>>> 下发工单数据至下游,webservice重试至最大次数,结果:失败,下发地址:" + path + ",CorrelateId:" + correlateId;
            result.setErrorResult(errorMsg);
            log.error(errorMsg);
        }
        return result;
    }

    /**
     * 推送工单信息至分发mq
     *
     * @param epgOutOrderVos
     * @return
     */
    public Boolean extracted(List<EpgOutOrderVo> epgOutOrderVos) {
        try {
            epgOutOrderVos.stream().forEach(epgOutOrderVo -> {
                log.info("待发送工单信息 epgOutOrderVo:{}", JSON.toJSONString(epgOutOrderVo));
                Integer finalProiortyValue = epgOutOrderVo.getPriority();
                String routingKey = IssueOrderConstant.OUT_ORDER_SEND_ROUTING + SymbolConstant.UNDER_SCORE + epgOutOrderVo.getLspId();
                try {
                    rabbitTemplate.convertAndSend(IssueOrderConstant.OUT_EXCHANGE,
                            routingKey, epgOutOrderVo, message -> {
                                message.getMessageProperties().setPriority(ObjectUtils.isNotEmpty(finalProiortyValue) ? finalProiortyValue : 0);
                                return message;
                            });
                    log.info("消息:{} 发送至 EXCHANGE:{},Routing-key:{} Priority:{} 完毕.",
                            epgOutOrderVo,
                            IssueOrderConstant.OUT_EXCHANGE,
                            routingKey, finalProiortyValue);
                } catch (Exception exception) {
                    log.error("消息:{} 发送至 EXCHANGE:{},Routing-key:{} Priority:{} 失败.错误信息:{}",
                            epgOutOrderVo,
                            IssueOrderConstant.OUT_EXCHANGE,
                            routingKey, finalProiortyValue, exception);
                    strategyHandlerUtil.buildErrorResp(epgOutOrderVo, "工单执行分发MQ失败");
                    epgOrderService.updateStatus(epgOutOrderVo);
                }
            });
        } catch (Exception exception) {
            log.error("子工单消息发送至 MQ 失败.错误信息:{}", exception);
            return false;
        }
        return true;
    }

    /**
     * 计算发布时间
     *
     * @param startDate
     * @param endDate
     * @return
     */
    public int calLastedTime(Date startDate, Date endDate) {
        int result = -1;
        try {
            long endDateTime = endDate.getTime();
            long startDateTime = startDate.getTime();
            result = (int) ((endDateTime - startDateTime) / 1000);
        } catch (Exception exception) {
            log.error("计算发布时间 -----> 失败,错误信息:{}", exception);
        }
        return result;
    }


    /**
     * 构建失败应答实体
     *
     * @param epgOutOrders
     * @param errorInfo
     * @return
     */
    public List<EpgOutOrder> getReportEntity(List<EpgOutOrder> epgOutOrders, String errorInfo) {
        List<EpgOutOrder> outOrders = new ArrayList<>();
        epgOutOrders.forEach(epgOutOrder -> {
            try {
                strategyHandlerUtil.buildErrorResp(epgOutOrder, StringUtils.isNotEmpty(epgOutOrder.getStatusDescription()) ? epgOutOrder.getStatusDescription() : errorInfo);
                if (ObjectUtils.isNotEmpty(epgOutOrder) && ObjectUtils.isNotEmpty(epgOutOrder.getId())) {
                    outOrders.add(epgOutOrder);
                }
            } catch (Exception exception) {
                log.error("执行生成工单任务操作 ->>>>>>  构建应答实体失败,数据:{}，错误信息:{} ", JSON.toJSONString(epgOutOrder), exception.getMessage());
            }
        });
        return outOrders;
    }

}
