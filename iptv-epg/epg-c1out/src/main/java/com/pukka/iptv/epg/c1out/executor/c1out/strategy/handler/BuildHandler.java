package com.pukka.iptv.epg.c1out.executor.c1out.strategy.handler;

import com.alibaba.fastjson.JSON;
import com.pukka.iptv.common.base.enums.ActionEnums;
import com.pukka.iptv.common.base.enums.EpgElementTypeEnums;
import com.pukka.iptv.common.core.util.SafeUtil;
import com.pukka.iptv.common.data.vo.epg.EpgOutOrderVo;
import com.pukka.iptv.epg.c1out.executor.c1out.dao.entity.OrderObjectsEntity;
import com.pukka.iptv.epg.c1out.executor.c1out.dao.entity.OrderXmlEntity;
import com.pukka.iptv.epg.c1out.executor.c1out.dao.entity.ResultResponse;
import com.pukka.iptv.epg.c1out.executor.c1out.utils.StrategyHandlerUtil;
import com.pukka.iptv.epg.c1out.executor.common.content.FtpHandleConstant;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * @Author: chiron
 * @Date: 2023/03/07/16:44
 * @Description:
 */
@Slf4j
@Component
public class BuildHandler implements IBuildHandler {

    @Autowired
    private StrategyHandlerUtil strategyHandlerUtil;

    /**
     * 构造xml工单实体
     *
     * @param epgOutOrderVo
     * @return
     */
    @Override
    public ResultResponse buildXmlOrderEntity(EpgOutOrderVo epgOutOrderVo) {
        ResultResponse resultResponse = new ResultResponse();
        try {
            OrderXmlEntity data = resultResponse.getData();
            List<OrderObjectsEntity> orderObjectsEntities = data.getSubOrderObjectsEntities() != null
                    ? data.getSubOrderObjectsEntities() : new ArrayList<>();
            //构建ftp文件目录
            StringBuffer xmlDestination = strategyHandlerUtil.getFileName(FtpHandleConstant.DIR_XML);
            data.setXmlDestination(xmlDestination.toString());
            OrderObjectsEntity orderObjectsEntity = achieveFileSet(epgOutOrderVo);
            if (ObjectUtils.isNotEmpty(orderObjectsEntity)) {
                orderObjectsEntities.add(orderObjectsEntity);
                OrderObjectsEntity orderObjectsEntity1 = achieveFile(epgOutOrderVo);
                if (ObjectUtils.isEmpty(orderObjectsEntity1)) {
                    resultResponse.setErrorResult(String.format("构造xml工单实体 ->>>>>> 信息:%s 生成 EPGFile 失败！", JSON.toJSONString(epgOutOrderVo)));
                    log.warn("构造xml工单实体 ->>>>>> 信息:{}，生成 EPGFile 失败", JSON.toJSONString(epgOutOrderVo));
                    return resultResponse;
                }
                orderObjectsEntities.add(orderObjectsEntity1);
            } else {
                resultResponse.setErrorResult(String.format("构造xml工单实体 ->>>>>> 信息:%s 生成 EPGFileSet 失败！", JSON.toJSONString(epgOutOrderVo)));
                log.warn("构造xml工单实体 ->>>>>> 信息:{}，生成 EPGFileSet 失败", JSON.toJSONString(epgOutOrderVo));
                return resultResponse;
            }
            data.setSubOrderObjectsEntities(orderObjectsEntities);
        } catch (Exception exception) {
            resultResponse.setErrorResult(String.format("构造xml工单实体 ->>>>>> 信息:%s 执行发布工单操作失败！", JSON.toJSONString(epgOutOrderVo)));
            log.warn("构造xml工单实体 ->>>>>> 信息:{}，执行发布工单操作失败,错误描述:{}！", JSON.toJSONString(epgOutOrderVo), exception);

        } finally {
            return resultResponse;
        }
    }

    /**
     * 获取EPGFileSet
     *
     * @param epgOutOrderVo
     * @return
     */
    @Override
    public OrderObjectsEntity achieveFileSet(EpgOutOrderVo epgOutOrderVo) {
        OrderObjectsEntity orderObjectsEntity = new OrderObjectsEntity();
        orderObjectsEntity.setElementType(EpgElementTypeEnums.EPGFILESET.getInfo());
        orderObjectsEntity.setId(epgOutOrderVo.getFileSetId());
        Map<String, String> propertyDic = new HashMap<>();
        propertyDic.put("EPGGroup", SafeUtil.getString(epgOutOrderVo.getEpgGroupPath()));
        propertyDic.put("NeedUnTar", SafeUtil.getString(epgOutOrderVo.getUnCompression()));
        orderObjectsEntity.setPropertyDic(propertyDic);
        return orderObjectsEntity;
    }

    /**
     * 获取EPGFile
     *
     * @param epgOutOrderVo
     * @return
     */
    @Override
    public OrderObjectsEntity achieveFile(EpgOutOrderVo epgOutOrderVo) {
        OrderObjectsEntity orderObjectsEntity = new OrderObjectsEntity();
        orderObjectsEntity.setAction(ActionEnums.EPG_REGIST_UPDATE.getInfo());
        orderObjectsEntity.setElementType(EpgElementTypeEnums.EPGFILE.getInfo());
        orderObjectsEntity.setId(epgOutOrderVo.getFileSetId());
        Map<String, String> propertyDic = new HashMap<>();
        propertyDic.put("SourceUrl", SafeUtil.getString(epgOutOrderVo.getSourceUrl()));
        propertyDic.put("DestPath", SafeUtil.getString(epgOutOrderVo.getDestPath()));
        propertyDic.put("MD5", SafeUtil.getString(epgOutOrderVo.getMd5()));
        orderObjectsEntity.setPropertyDic(propertyDic);
        return orderObjectsEntity;
    }

}
