package com.pukka.iptv.epg.c1out.executor.c1out.dao.entity;

import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @Date 2021/10/9 10:55 上午
 * @description: 调用返回应答
 * @Version 1.0
 */
@Getter
@Setter
public class ResultResponse implements Serializable {

    private static final long serialVersionUID = 4915340620655958873L;
    /**
     * 返回结果
     */
    private Boolean result;
    /**
     * 错误描述信息
     */
    private String description;
    /**
     * 返回数据体
     */
    private OrderXmlEntity data;

    private String cspId;

    private String lspId;

    /**
     * 错误描述
     *
     * @param errorDescription
     */
    public void setErrorResult(String errorDescription) {
        this.setResult(false);
        this.setDescription(errorDescription);
    }

    public ResultResponse() {
        this.result = true;
        this.data = new OrderXmlEntity();
    }
}
