package com.pukka.iptv.epg.c1out.executor.c1out.dao.entity;

import lombok.Data;

import java.io.Serializable;
import java.util.HashMap;
import java.util.Map;

/**
 * @Author: chiron
 * @Date: 2023/03/07/16:26
 * @Description:
 */
@Data
public class OrderObjectsEntity implements Serializable {
    private static final long serialVersionUID = -3800290258643448968L;

    /**
     * EPGFile
     */
    private String elementType;
    /**
     * EPGFile的唯一标识
     */
    private String id;
    /**
     * 操作类型
     * 更新，REGIST-UPDATE
     * 删除，DELETE
     */
    private String action;

    /**
     * 属性集
     */
    private Map<String,String> propertyDic;

    public OrderObjectsEntity() {
        propertyDic=new HashMap<>();
    }
}
