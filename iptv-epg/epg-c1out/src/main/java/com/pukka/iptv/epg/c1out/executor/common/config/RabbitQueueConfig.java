package com.pukka.iptv.epg.c1out.executor.common.config;

import com.pukka.iptv.common.base.constant.ObjectsTypeConstants;
import com.pukka.iptv.common.base.constant.RedisKeyConstants;
import com.pukka.iptv.common.core.constant.SymbolConstant;
import com.pukka.iptv.common.core.util.SafeUtil;
import com.pukka.iptv.common.data.model.epg.EpgNode;
import com.pukka.iptv.common.rabbitmq.constans.IssueOrderConstant;
import com.pukka.iptv.common.redis.service.RedisService;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.amqp.core.BindingBuilder;
import org.springframework.amqp.core.DirectExchange;
import org.springframework.amqp.core.Queue;
import org.springframework.amqp.rabbit.core.RabbitAdmin;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 工单消费队列
 * 根据spchannelid绑定queue、routingkey
 */
@Configuration
public class RabbitQueueConfig {

    @Autowired
    private RedisService redisService;
    @Autowired
    private RabbitAdmin rabbitAdmin;

    /**
     * 交换机
     *
     * @return
     */
    @Bean
    public DirectExchange outPublishlExchange() {
        return new DirectExchange(IssueOrderConstant.OUT_EXCHANGE);
    }

    /**
     * Binding,将该routing key的消息通过交换机转发到该队列
     * 根据分发通道创建队列
     */
    @Bean
    public void initializeBinding() {
        Map<String, EpgNode> cacheList = redisService.getCacheMap(RedisKeyConstants.EPG_NODE_KEY);
        Map<String, Object> args = new HashMap<String, Object>();
        args.put(ObjectsTypeConstants.PRIORITY_QUEUE, 10);
        if (ObjectUtils.isNotEmpty(cacheList)) {
            List<EpgNode> epgNodeList = cacheList.values().stream().collect(Collectors.toList());
            List<String> stringList = epgNodeList.stream().map(EpgNode::getLspId).filter(lsp -> StringUtils.isNotEmpty(lsp)).distinct().collect(Collectors.toList());
            stringList.forEach((item) -> {
                rabbitAdmin.declareQueue(
                        new Queue(IssueOrderConstant.OUT_ORDER_SEND_QUEUE + SymbolConstant.UNDER_SCORE + SafeUtil.getString(item)
                                , true, false, false, args));
                rabbitAdmin.declareBinding(BindingBuilder.bind(new Queue(IssueOrderConstant.OUT_ORDER_SEND_QUEUE + SymbolConstant.UNDER_SCORE + SafeUtil.getString(item) )
                ).to(outPublishlExchange()).with(IssueOrderConstant.OUT_ORDER_SEND_ROUTING + SymbolConstant.UNDER_SCORE + SafeUtil.getString(item) ));
            });
        }
    }

}
