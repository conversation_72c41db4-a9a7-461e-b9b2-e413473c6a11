package com.pukka.iptv.epg.c1out.executor.c1out.dao.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.pukka.iptv.common.data.model.epg.EpgOutOrder;
import com.pukka.iptv.common.data.vo.epg.EpgOutOrderVo;

/**
 * @Author: chiron
 * @Date: 2023/03/01/10:07
 * @Description:
 */

public interface EpgOrderService extends IService<EpgOutOrder> {
    Boolean updateStatus(EpgOutOrderVo epgOutOrderVo);

    Boolean updateFtpCmdPath(EpgOutOrderVo epgOutOrderVo);
}
