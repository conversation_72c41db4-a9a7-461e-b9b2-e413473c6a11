package com.pukka.iptv.epg.c1out.executor.common.retry;

import com.pukka.iptv.epg.c1out.executor.common.config.XxlJobConfig;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.retry.RetryCallback;
import org.springframework.retry.RetryContext;
import org.springframework.retry.RetryListener;
import org.springframework.retry.backoff.FixedBackOffPolicy;
import org.springframework.retry.policy.SimpleRetryPolicy;
import org.springframework.retry.policy.TimeoutRetryPolicy;
import org.springframework.retry.support.RetryTemplate;

import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 * @Date 2021/10/29 9:31 上午
 * @description: Retry模板管理
 * @Version 1.0
 */
@Configuration
public class RetryConfig {
    @Autowired
    private XxlJobConfig xxlJobConfig;

    @Bean(name = "rabbitRetry")
    public RetryTemplate retryTemplate() {
        RetryTemplate retryTemplate = new RetryTemplate();
        retryTemplate.setListeners(getRetryListenter());
        Map<Class<? extends Throwable>, Boolean> retryableExceptions = new HashMap<>();
        retryableExceptions.put(RetryException.class, true);
        SimpleRetryPolicy retryPolicy = new SimpleRetryPolicy(xxlJobConfig.maxAttempts, retryableExceptions);
        retryTemplate.setRetryPolicy(retryPolicy);
        FixedBackOffPolicy backOffPolicy = new FixedBackOffPolicy();
        backOffPolicy.setBackOffPeriod(xxlJobConfig.timeout);
        retryTemplate.setBackOffPolicy(backOffPolicy);
        return retryTemplate;
    }

    @Bean(name = "timeoutRetry")
    public RetryTemplate timeoutRetry() {
        RetryTemplate retryTemplate = new RetryTemplate();
        TimeoutRetryPolicy policy = new TimeoutRetryPolicy();
        policy.setTimeout(xxlJobConfig.timeout);
        retryTemplate.setRetryPolicy(policy);
        return retryTemplate;
    }

    /**
     * retry事件监听
     * @return
     */
    public static RetryListener[] getRetryListenter() {
        RetryListener[] retryListeners = {
                new RetryListener() {
                    @Override
                    public <T, E extends Throwable> boolean open(RetryContext retryContext, RetryCallback<T, E> retryCallback) {
                        //方法准备执行
                        System.out.println("retry方法开始执行");
                        return true;
                    }

                    @Override
                    public <T, E extends Throwable> void close(RetryContext retryContext, RetryCallback<T, E> retryCallback, Throwable throwable) {
                        //方法重试完毕处理
                        System.out.println("retry方法执行结束");
                    }

                    @Override
                    public <T, E extends Throwable> void onError(RetryContext retryContext, RetryCallback<T, E> retryCallback, Throwable throwable) {
                        //方法执行异常处理
                        System.out.println("retry方法执行,正在进行重试");
                    }
                }
        };
        return retryListeners;
    }
}
