package com.pukka.iptv.epg.c1out.executor.c1out.xml;

import com.pukka.iptv.common.core.util.SpringUtils;
import com.pukka.iptv.epg.c1out.executor.c1out.dao.entity.OrderXmlEntity;
import lombok.extern.slf4j.Slf4j;
import org.dom4j.Element;
import org.springframework.stereotype.Component;

/**
 * @author: chiron
 * Date: 2022/3/22 4:53 PM
 * Description: xml规则集合策略
 */
@Slf4j
@Component
public class XmlRulesLoadStrategy {

    private IXmlHandlerStrategyManager iXmlHandlerStrategyManager = SpringUtils.getBean(IXmlHandlerStrategyManager.class);


    /**
     * 获取策略后缀名
     *
     * @return
     */
    public IXmlHandlerStrategy getStrategy() {
        return iXmlHandlerStrategyManager.createHandlerStrategy("");
    }

    /**
     * 生成xml文件
     *
     * @param orderXmlEntity
     * @return
     */
    public Element getXml(OrderXmlEntity orderXmlEntity) {
        IXmlHandlerStrategy handlerStrategy = getStrategy();
        //当主工单生成xml文件时
        Element element = handlerStrategy.generateXMLFile(orderXmlEntity);
        return element;
    }

    /**
     * 解析为xml字符串
     *
     * @param element
     * @return
     */
    public String getOutXmlContent(Element element) {
        IXmlHandlerStrategy handlerStrategy = getStrategy();
        String outXmlContent = handlerStrategy.getOutXmlContent(element);
        return outXmlContent;
    }
}