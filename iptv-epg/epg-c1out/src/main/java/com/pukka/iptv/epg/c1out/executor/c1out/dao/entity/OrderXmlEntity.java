package com.pukka.iptv.epg.c1out.executor.c1out.dao.entity;

import lombok.Data;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * @Author: chiron
 * @Date: 2023/03/07/16:26
 * @Description:
 */
@Data
public class OrderXmlEntity implements Serializable {
    private static final long serialVersionUID = -7961082138594727369L;
    /**
     * xml文件名信息
     */
    private String xmlDestination;
    /**
     * EPGFileSet
     */
    private String ElementType;
    /**
     * EPGFileSet的唯一标识
     */
    private String id;
    /**
     * object
     */
    private List<OrderObjectsEntity> subOrderObjectsEntities;
    /**
     * 补充参数
     */
    private Map<String, String> param;

    public OrderXmlEntity() {
        this.subOrderObjectsEntities = new ArrayList<>();
        param = new HashMap<>();
    }
}
