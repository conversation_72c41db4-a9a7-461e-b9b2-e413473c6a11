package com.pukka.iptv.epg.c1out.executor.common.config;


import com.pukka.iptv.epg.c1out.executor.c1out.xml.IXmlHandlerStrategy;
import com.pukka.iptv.epg.c1out.executor.c1out.xml.XmlHandlerStrategy;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Primary;

/**
 * @author: chiron
 * Date: 2022/3/28 9:52 AM
 * Description:
 */
@Configuration
public class Config {

    @Bean
    @Primary
    public IXmlHandlerStrategy getIXmlHandlerStrategy(){
        return new XmlHandlerStrategy();
    }
}