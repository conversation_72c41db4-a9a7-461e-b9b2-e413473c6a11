package com.pukka.iptv.epg.c1out.executor.c1out.utils;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.pukka.iptv.common.base.enums.CommonResponseEnum;
import com.pukka.iptv.common.base.enums.PublishStatusEnum;
import com.pukka.iptv.common.base.exception.CommonResponseException;
import com.pukka.iptv.common.core.constant.SymbolConstant;
import com.pukka.iptv.common.core.util.*;
import com.pukka.iptv.common.data.model.epg.EpgFile;
import com.pukka.iptv.common.data.model.epg.EpgNode;
import com.pukka.iptv.common.data.model.epg.EpgOutOrder;
import com.pukka.iptv.common.data.model.epg.EpgTemplate;
import com.pukka.iptv.common.data.vo.epg.EpgOutOrderVo;
import com.pukka.iptv.epg.c1out.executor.c1out.dao.entity.OrderXmlEntity;
import com.pukka.iptv.epg.c1out.executor.c1out.dao.entity.ResultResponse;
import com.pukka.iptv.epg.c1out.executor.c1out.dao.mapper.EpgFileMapper;
import com.pukka.iptv.epg.c1out.executor.c1out.dao.mapper.EpgNodeMapper;
import com.pukka.iptv.epg.c1out.executor.c1out.dao.mapper.EpgTemplateMapper;
import com.pukka.iptv.epg.c1out.executor.c1out.dao.service.EpgOrderService;
import com.pukka.iptv.epg.c1out.executor.c1out.strategy.handler.IPublishStrategyManage;
import com.pukka.iptv.epg.c1out.executor.c1out.xml.XmlRulesLoadStrategy;
import com.pukka.iptv.epg.c1out.executor.common.config.CommonConfig;
import com.pukka.iptv.epg.c1out.executor.common.retry.RetryException;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.dom4j.Element;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.retry.RetryCallback;
import org.springframework.retry.RetryContext;
import org.springframework.retry.support.RetryTemplate;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;


/**
 * <AUTHOR>
 * @description: 地址工具类
 * @create 2023-03-13 11:53
 */
@Slf4j
@Component
public class StrategyHandlerUtil {

    @Autowired
    private EpgFileMapper epgFileMapper;
    @Autowired
    private EpgNodeMapper epgNodeMapper;
    @Autowired
    private EpgTemplateMapper epgTemplateMapper;
    @Autowired
    private EpgOrderService epgOrderService;
    @Autowired
    private CommonConfig commonConfig;
    @Autowired
    private XmlRulesLoadStrategy xmlRulesLoadStrategy;
    @Autowired
    private IPublishStrategyManage iPublishStrategyManage;

    private RetryTemplate retryTemplate = SpringUtils.getBean("rabbitRetry");


    /**
     * 拼装文件名
     *
     * @param dirPrefix
     * @return
     */
    public StringBuffer getFileName(String dirPrefix) {
        StringBuffer fileName = new StringBuffer(dirPrefix);
        //添加时间文件夹
        String day = DateUtils.dateTimeNow(DateUtils.YYYYMMDD);
        fileName.append(day);
        fileName.append("/");
        return fileName;
    }

    /**
     * 构建xml工单
     *
     * @param epgOutOrderVos
     * @return
     */
    public List<EpgOutOrderVo> generateXmlUpload(List<EpgOutOrderVo> epgOutOrderVos) {
        List<EpgOutOrderVo> epgOutOrderVoList = new ArrayList<>();
        for (EpgOutOrderVo epgOutOrderVo : epgOutOrderVos) {
            try {
                Boolean aBoolean = buildPublishMate(epgOutOrderVo);
                if (aBoolean) {
                    //获取内网xml存储ftp路径
                    String uploadFtp = getUploadFtp(commonConfig.innerFtpPrefix, commonConfig.writeXmlAccount, commonConfig.writeXmlPwd);
                    if (StringUtils.isEmpty(uploadFtp)) {
                        log.warn("执行生成xml并上传ftp操作 ->>>>>> 工单唯一ID:{} 工单内容:{}，获取xml文件ftp配置信息失败，配置信息为空！"
                                , epgOutOrderVo.getFileSetId(), epgOutOrderVo);
                        buildErrorResp(epgOutOrderVo, "获取xml文件ftp配置信息为空");
                        epgOrderService.updateStatus(epgOutOrderVo);
                        continue;
                    }
                    //生成工单实体
                    ResultResponse resultResponse = iPublishStrategyManage.startPublish(epgOutOrderVo);
                    if (resultResponse.getResult()) {
                        OrderXmlEntity data = resultResponse.getData();
                        //生成工单xml
                        Element xml = xmlRulesLoadStrategy.getXml(data);
                        if (ObjectUtils.isEmpty(xml)) {
                            log.warn("执行生成xml并上传ftp操作 ->>>>>> 工单唯一ID:{} 工单内容:{}，生成工单xml操作失败！",
                                    epgOutOrderVo.getFileSetId(), epgOutOrderVo);
                            buildErrorResp(epgOutOrderVo, "生成工单xml操作失败");
                            epgOrderService.updateStatus(epgOutOrderVo);
                            continue;
                        }
                        String outXmlContent = xmlRulesLoadStrategy.getOutXmlContent(xml);
                        log.info("执行生成xml并上传ftp操作 ->>>>>> 工单唯一ID:{} 工单内容:{},组装上传ftp地址，uploadFtp:{}",
                                epgOutOrderVo.getFileSetId(), epgOutOrderVo, uploadFtp);
                        String xmlDestination = data.getXmlDestination() + SafeUtil.getString(epgOutOrderVo.getFileSetId());
                        if (StringUtils.isNotEmpty(xmlDestination) && xmlDestination.indexOf(SymbolConstant.LEFT_DIVIDE) == 0) {
                            xmlDestination = xmlDestination.substring(1);
                        }
                        String upFile = SafeUtil.upByFtp(outXmlContent, xmlDestination + ".xml", uploadFtp);
                        if (StringUtils.isEmpty(upFile)) {
                            log.warn("执行生成xml并上传ftp操作 ->>>>>> 工单唯一ID:{} 工单内容:{},上传ftp失败，uploadFtp结果地址为空!",
                                    epgOutOrderVo.getFileSetId(), epgOutOrderVo);
                            buildErrorResp(epgOutOrderVo, "上传Ftp结果地址为空");
                            epgOrderService.updateStatus(epgOutOrderVo);
                            continue;
                        }
                        log.info("执行生成xml并上传ftp操作 ->>>>>> 工单唯一ID:{} 工单内容:{} 上传ftp完毕!uploadFtp结果地址:{}",
                                epgOutOrderVo.getFileSetId(), epgOutOrderVo, upFile);
                        //获取外网xml存储ftp路径
                        String outUploadFtp = getUploadFtp(commonConfig.outFtpPrefix, commonConfig.readXmlAccount, commonConfig.readXmlPwd);
                        //组装替换ftpUrl
                        String replaceFtp = outUploadFtp + xmlDestination + ".xml";
                        log.info("执行生成xml并上传ftp操作 ->>>>>> 工单唯一ID:{} 工单内容:{} 组装替换ftpurl完毕!replaceFtp结果地址:{}",
                                epgOutOrderVo.getFileSetId(), epgOutOrderVo, replaceFtp);
                        epgOutOrderVo.setCmdFileUrl(replaceFtp);
                        //ftp地址回填数据库表cmd_file_url字段
                        Boolean b = epgOrderService.updateFtpCmdPath(epgOutOrderVo);
                        if (!b) {
                            log.warn("执行生成xml并上传ftp操作 ->>>>>> 工单唯一ID:{} 工单内容:{},ftp地址回填数据库失败!",
                                    epgOutOrderVo.getFileSetId(), epgOutOrderVo);
                            buildErrorResp(epgOutOrderVo, "tp地址回填数据库失败");
                            epgOrderService.updateStatus(epgOutOrderVo);
                            continue;
                        }
                        epgOutOrderVoList.add(epgOutOrderVo);
                    } else {
                        //生成工单实体失败，直接修改工单状态
                        log.warn("执行生成xml并上传ftp操作 ->>>>>> 工单唯一ID:{} 工单内容:{},构建工单xml实体失败，发布结束!",
                                epgOutOrderVo.getFileSetId(), epgOutOrderVo);
                        buildErrorResp(epgOutOrderVo, "构建工单xml实体失败");
                        epgOrderService.updateStatus(epgOutOrderVo);
                    }

                } else {
                    log.warn("执行生成xml并上传ftp操作 ->>>>>> 工单唯一ID:{} 工单内容:{},构建发布信息失败，发布结束!",
                            epgOutOrderVo.getFileSetId(), epgOutOrderVo);
                    buildErrorResp(epgOutOrderVo, "构建发布信息失败");
                    epgOrderService.updateStatus(epgOutOrderVo);
                }
            } catch (Exception exception) {
                log.error("执行生成xml并上传ftp操作 ->>>>>> 工单唯一ID:{} 工单内容:{},执行错误，错误信息:{}",
                        epgOutOrderVo.getFileSetId(), epgOutOrderVo, exception);
            }
        }
        return epgOutOrderVoList;
    }

    /**
     * 获取文件md5值
     *
     * @param epgFile
     * @param epgOutOrderVo
     * @return
     */
    private Boolean getFileMd5(EpgFile epgFile, EpgOutOrderVo epgOutOrderVo) {
        Boolean result = true;
        try {
            RetryCallback<Object, Throwable> objectThrowableRetryCallback = new RetryCallback<Object, Throwable>() {
                @Override
                public Object doWithRetry(RetryContext retryContext) throws Throwable {
                    Boolean result = true;
                    try {
                        //获取file存储ftp路径
                        String uploadFtp = getUploadFtp(commonConfig.innerFtpPrefix, commonConfig.readFileAccount, commonConfig.readFilePwd);
                        //拉取ftp文件计算md5值
                        String filePath = uploadFtp + epgFile.getFilePath().replace(SymbolConstant.LEFT_DIVIDE,"");
                        log.debug("file存储ftp路径 :{}", filePath);
                        //替换为外网地址
                        String sourceUrl = getUploadFtp(commonConfig.outFtpPrefix, commonConfig.readFileAccount, commonConfig.readFilePwd)
                                + epgFile.getFilePath().replace(SymbolConstant.LEFT_DIVIDE,"");
                        epgOutOrderVo.setSourceUrl(sourceUrl);
                        //******** 删除destPath字段
                        //epgOutOrderVo.setDestPath(epgFile.getFilePath());
                        //校验ftp文件是否存在
                        filePath = new String(filePath.getBytes("UTF-8"), "ISO-8859-1");
                        if (!IsExistPath(filePath)) {
                            result = false;
                            epgFile.setMd5("");
                            epgFileMapper.updateById(epgFile);
                            log.warn("执行生成xml并上传ftp操作 ->>>>>> 获取文件md5值,文件code:{} 文件路径:{}，文件md5:{} 文件在ftp中不存在！",
                                    epgFile.getCode(), epgFile.getFilePath(), epgFile.getMd5());
                            return result;
                        }
                        String computeMD5 = SafeUtil.readFtpFileMd5(filePath);
                        log.info("执行生成xml并上传ftp操作 ->>>>>> 获取文件md5值,文件code:{} 文件路径:{}，文件md5:{}",
                                epgFile.getCode(), epgFile.getFilePath(), computeMD5);
                        epgFile.setMd5(computeMD5);
                        int i = epgFileMapper.updateById(epgFile);
                        epgOutOrderVo.setMd5(computeMD5);
                        if (i > 0) {
                            log.info("执行生成xml并上传ftp操作 ->>>>>> 获取文件md5值,文件code:{} 文件路径:{}，文件md5:{} 文件md5更新完毕！",
                                    epgFile.getCode(), epgFile.getFilePath(), epgFile.getMd5());
                        } else {
                            result = false;
                            log.info("执行生成xml并上传ftp操作 ->>>>>> 获取文件md5值,文件code:{} 文件路径:{} 文件md5更新失败！",
                                    epgFile.getCode(), epgFile.getFilePath());
                            return result;
                        }
                    } catch (Exception exception) {
                        log.error("执行生成xml并上传ftp操作 ->>>>>> 获取文件md5值错误,文件code:{} 文件路径:{} 错误信息:{}",
                                epgFile.getCode(), epgFile.getFilePath(), exception.getMessage());
                        throw new RetryException(exception.getMessage());
                    }
                    return result;
                }
            };
            Object execute = retryTemplate.execute(objectThrowableRetryCallback);
            result = (Boolean) execute;
        } catch (Throwable throwable) {
            log.error("执行生成xml并上传ftp操作 ->>>>>> 获取文件md5值失败,重试至最大次数,文件code:{} 文件路径:{}", epgFile.getCode(), epgFile.getFilePath());
        }
        return result;
    }


    /**
     * 构建失败应答实体
     *
     * @param epgOutOrder
     * @return
     */
    public void buildErrorResp(EpgOutOrder epgOutOrder, String statusDescription) {
        epgOutOrder.setStatus(PublishStatusEnum.FAILPUBLISH.getCode());
        epgOutOrder.setStatusDescription(statusDescription);
        epgOutOrder.setDuration(-1);
        epgOutOrder.setFeedbackTime(epgOutOrder.getCreateTime());
    }

    /**
     * 构建发布信息
     *
     * @param epgOutOrderVo
     * @return
     */
    private Boolean buildPublishMate(EpgOutOrderVo epgOutOrderVo) {
        LambdaQueryWrapper<EpgFile> wrapper = Wrappers.lambdaQuery(EpgFile.class)
                .eq(EpgFile::getFileId, epgOutOrderVo.getEpgFileId()).last("limit 1");
        EpgFile epgFile = epgFileMapper.selectOne(wrapper);
        Boolean aBoolean = getFileMd5(epgFile, epgOutOrderVo);
        if (aBoolean) {
            EpgNode epgNode = epgNodeMapper.selectById(epgOutOrderVo.getNodeId());
            if (ObjectUtils.isNotEmpty(epgNode) && ObjectUtils.isNotEmpty(epgNode.getLspId())
                    && ObjectUtils.isNotEmpty(epgNode.getCspId()) && ObjectUtils.isNotEmpty(epgNode.getPath())) {
                epgOutOrderVo.setLspId(epgNode.getLspId());
                epgOutOrderVo.setCspId(epgNode.getCspId());
                epgOutOrderVo.setPath(epgNode.getPath());
            } else {
                log.warn("构建发布信息操作 ->>>>>> 获取文件EPGGroup路径,事件FileSetId:{} 文件FileId:{} 节点信息不完整！",
                        epgOutOrderVo.getFileSetId(), epgOutOrderVo.getEpgFileId());
                return false;
            }
            EpgTemplate epgTemplate = epgTemplateMapper.selectById(epgOutOrderVo.getTemplateId());
            if (ObjectUtils.isNotEmpty(epgTemplate)) {
                if (ObjectUtils.isNotEmpty(epgTemplate.getPath())) {
                    epgOutOrderVo.setEpgGroupPath(epgTemplate.getPath());
                } else {
                    log.warn("构建发布信息操作 ->>>>>> 获取文件EPGGroup路径,事件FileSetId:{} 文件FileId:{} 路径为空！",
                            epgOutOrderVo.getFileSetId(), epgOutOrderVo.getEpgFileId());
                }
            }
            return true;
        }
        log.warn("构建发布信息操作 ->>>>>> 获取文件md5值,事件FileSetId:{} 文件FileId:{} 文件md5值为空！",
                epgOutOrderVo.getFileSetId(), epgOutOrderVo.getEpgFileId());
        return false;
    }


    /**
     * 组装ftp上传地址
     *
     * @param url
     * @param account
     * @param password
     * @return
     */
    public String getUploadFtp(String url, String account, String password) {
        StringBuffer result = new StringBuffer();
        try {
            result.append("ftp://");
            result.append(account);
            result.append(":");
            result.append(password + "@" + url + "/");
        } catch (Exception exception) {
            log.error("组装ftp上传地址 ->>>>>> getUploadFtp执行失败！errorMessage:{}", exception);
            throw new CommonResponseException("组装ftp上传地址失败!");
        }
        return result.toString();
    }

    /**
     * 检验地址是否存在
     *
     * @param url
     * @return
     */
    public boolean IsExistPath(String url) {
        try {
            String[] values = FtpUtil.getSingleMatchValue(url);
            FtpUtil ftpUtil = new FtpUtil(values[2], values[3], values[0], values[1]);
            boolean result = ftpUtil.checkFileExists(url);
            return result;
        } catch (Exception e) {
            log.error("error ftp", e.getMessage());
            return false;
        }
    }
}
