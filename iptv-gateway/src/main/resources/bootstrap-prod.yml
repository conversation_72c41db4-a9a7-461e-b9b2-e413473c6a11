server:
  port: 7000
  #servlet:
  #  context-path: /@project.artifactId@
#nacos服务地址及账号密码
nacos:
  server-addr: **************:18848
  namespace: iptv
  username: bokong
  password: kQ_XFgjnILTjcPiHv9wF
  group: iptv
  file-extension: yaml
sentinel:
  dashboard: **************:8080
  nacos:
    group-id: iptv
    data-type: json
    rule-type: flow
  # app-name: ${spring.application.name}
hostname: ${HOSTNAME:logs}

