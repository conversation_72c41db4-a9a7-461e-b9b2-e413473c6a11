${AnsiColor.BRIGHT_GREEN}
Application Version: ${project.version}
Spring Boot Version: ${spring-boot.version}
Spring Application Name: ${spring.application.name}
                                  ,----,
                                ,/   .`|
  ,----..      ,---,          ,`   .'  :   ,---,.           .---.   ,---,
 /   /   \    '  .' \       ;    ;     / ,'  .' |          /. ./|  '  .' \            ,---,
|   :     :  /  ;    '.   .'___,/    ,',---.'   |      .--'.  ' ; /  ;    '.         /_ ./|
.   |  ;. / :  :       \  |    :     | |   |   .'     /__./ \ : |:  :       \  ,---, |  ' :
.   ; /--`  :  |   /\   \ ;    |.';  ; :   :  |-, .--'.  '   \' .:  |   /\   \/___/ \.  : |
;   | ;  __ |  :  ' ;.   :`----'  |  | :   |  ;/|/___/ \ |    ' '|  :  ' ;.   :.  \  \ ,' '
|   : |.' .'|  |  ;/  \   \   '   :  ; |   :   .';   \  \;      :|  |  ;/  \   \\  ;  `  ,'
.   | '_.' :'  :  | \  \ ,'   |   |  ' |   |  |-, \   ;  `      |'  :  | \  \ ,' \  \    '
'   ; : \  ||  |  '  '--'     '   :  | '   :  ;/|  .   \    .\  ;|  |  '  '--'    '  \   |
'   | '/  .'|  :  :           ;   |.'  |   |    \   \   \   ' \ ||  :  :           \  ;  ;
|   :    /  |  | ,'           '---'    |   :   .'    :   '  |--" |  | ,'            :  \  \
 \   \ .'   `--''                      |   | ,'       \   \ ;    `--''               \  ' ;
  `---`                                `----'          '---"                          `--`
