server:
  port: 7000
  #servlet:
  #  context-path: /@project.artifactId@
#nacos服务地址及账号密码
nacos:
  server-addr: **************:8848
  namespace: bokong_test
  username: nacos
  password: LCW6KLEAHa_vectvznfv
  group: iptv
  file-extension: yaml
sentinel:
  dashboard: **************:8718
  nacos:
    group-id: iptv
    data-type: json
    rule-type: flow
  # app-name: ${spring.application.name}
hostname: ${HOSTNAME:logs}