//package com.pukka.iptv.gateway.config;
//
//import com.fasterxml.jackson.annotation.JsonAutoDetect;
//import com.fasterxml.jackson.annotation.JsonTypeInfo;
//import com.fasterxml.jackson.annotation.PropertyAccessor;
//import com.fasterxml.jackson.databind.ObjectMapper;
//import com.fasterxml.jackson.databind.jsontype.impl.LaissezFaireSubTypeValidator;
//import org.springframework.context.annotation.Bean;
//import org.springframework.context.annotation.Configuration;
//import org.springframework.data.redis.connection.ReactiveRedisConnectionFactory;
//import org.springframework.data.redis.core.ReactiveRedisTemplate;
//import org.springframework.data.redis.serializer.RedisSerializationContext;
//import org.springframework.data.redis.serializer.StringRedisSerializer;
//
///**
// * @Author: zhengcl
// * @Date: 2021/7/29 22:37
// */
//
//@EnableCaching
//@Configuration
//public class ReactiveRedisConfig {
//    @Bean
//    public ReactiveRedisTemplate<String, String> reactiveRedisTemplate(ReactiveRedisConnectionFactory reactiveRedisConnectionFactory) {
//        StringRedisSerializer stringRedisSerializer = new StringRedisSerializer();
//        //GenericFastJsonRedisSerializer fastJsonRedisSerializer = new GenericFastJsonRedisSerializer();
//        ObjectGenericFastJsonRedisSerializer genericFastJsonSerializer = new ObjectGenericFastJsonRedisSerializer(Object.class);
//        ObjectMapper mapper = new ObjectMapper();
//        mapper.setVisibility(PropertyAccessor.ALL, JsonAutoDetect.Visibility.ANY);
//        mapper.activateDefaultTyping(LaissezFaireSubTypeValidator.instance, ObjectMapper.DefaultTyping.NON_FINAL, JsonTypeInfo.As.PROPERTY);
//        genericFastJsonSerializer.setObjectMapper(mapper);
//
//        RedisSerializationContext.SerializationPair<String> keySerializationPair =
//                RedisSerializationContext.SerializationPair.fromSerializer(stringRedisSerializer);
//        RedisSerializationContext.SerializationPair<Object> valueSerializationPair =
//                RedisSerializationContext.SerializationPair.fromSerializer(genericFastJsonSerializer);
//        RedisSerializationContext.SerializationPair<Object> hashValueSerializationPair =
//                RedisSerializationContext.SerializationPair.fromSerializer(genericFastJsonSerializer);
//
//        RedisSerializationContext context = new RedisSerializationContext<String, Object>() {
//            @Override
//            public SerializationPair getKeySerializationPair() {
//                return keySerializationPair;
//            }
//
//            @Override
//            public SerializationPair getValueSerializationPair() {
//                return valueSerializationPair;
//            }
//
//            @Override
//            public SerializationPair getHashKeySerializationPair() {
//                return keySerializationPair;
//            }
//
//            @Override
//            public SerializationPair getHashValueSerializationPair() {
//                return hashValueSerializationPair;
//            }
//
//            @Override
//            public SerializationPair<String> getStringSerializationPair() {
//                return keySerializationPair;
//            }
//        };
//        return new ReactiveRedisTemplate<>(reactiveRedisConnectionFactory, context);
//    }
//
//}
