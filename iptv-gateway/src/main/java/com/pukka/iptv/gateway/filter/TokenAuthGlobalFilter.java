package com.pukka.iptv.gateway.filter;

import cn.hutool.core.util.CharsetUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.pukka.iptv.common.base.config.AuthoritiesConfig;
import com.pukka.iptv.common.base.enums.CommonResponseEnum;
import com.pukka.iptv.common.base.util.ServletUtil;
import com.pukka.iptv.common.core.constant.CacheConstants;
import com.pukka.iptv.common.core.constant.SymbolConstant;
import com.pukka.iptv.common.core.exception.AuthException;
import com.pukka.iptv.common.base.util.JwtTokenUtil;
import com.pukka.iptv.common.base.vo.SecurityUser;
import com.pukka.iptv.gateway.config.WhiteListConfig;
import com.pukka.iptv.gateway.util.GatewayUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.codec.Charsets;
import org.apache.commons.collections4.CollectionUtils;
import org.reactivestreams.Publisher;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cloud.gateway.filter.GatewayFilterChain;
import org.springframework.cloud.gateway.filter.GlobalFilter;
import org.springframework.core.Ordered;
import org.springframework.core.io.buffer.DataBuffer;
import org.springframework.core.io.buffer.DataBufferFactory;
import org.springframework.http.server.reactive.ServerHttpRequest;
import org.springframework.http.server.reactive.ServerHttpResponse;
import org.springframework.http.server.reactive.ServerHttpResponseDecorator;
import org.springframework.security.core.GrantedAuthority;
import org.springframework.stereotype.Component;
import org.springframework.util.AntPathMatcher;
import org.springframework.util.MultiValueMap;
import org.springframework.util.StringUtils;
import org.springframework.web.server.ServerWebExchange;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;

import java.io.UnsupportedEncodingException;
import java.net.URI;
import java.net.URLDecoder;
import java.nio.charset.Charset;
import java.util.List;


/**
 * @Author: zhengcl
 * @Date: 2021/7/24 20:03
 */

@Slf4j
@Component
public class TokenAuthGlobalFilter implements GlobalFilter, Ordered {

    @Autowired
    private WhiteListConfig whiteListConfig;
//    @Autowired
//    private RedisTemplate redisTemplate;
    private AntPathMatcher antPathMatcher = new AntPathMatcher();
    public TokenAuthGlobalFilter(WhiteListConfig whiteListConfig) {
        this.whiteListConfig = whiteListConfig;
    }

    @Override
    public Mono<Void> filter(ServerWebExchange exchange, GatewayFilterChain chain) {
        ServerHttpRequest request = exchange.getRequest();
        // 获取当前网关访问的URL
        String url = request.getURI().getPath();
        // 跳过不需要验证的路径
        if(matchUrl(whiteListConfig.getUrls(),url)){
            return chain.filter(exchange);
        }
        //从cookie获取token
       /* MultiValueMap<String, HttpCookie> cookies = exchange.getRequest().getCookies();
        HttpCookie cookie = cookies.getFirst("Authorization");
        String token = cookie.getValue();*/

        //从header获取token
        String token = JwtTokenUtil.getTokenByServerHttpRequest(exchange.getRequest());
        if (!StringUtils.hasLength(token)) {
            return GatewayUtils.getMono(exchange.getResponse(), CommonResponseEnum.EMPTY_TOKEN);
        }
        Boolean isExpired = JwtTokenUtil.isTokenExpired(token);
        if(isExpired){
            return GatewayUtils.getMono(exchange.getResponse(), CommonResponseEnum.TOKEN_EXPIRED);
        }
        // 并获取当前登录用户信息
        SecurityUser securityUser = null;
        try {
            // 判断用户token，获取用户信息
            securityUser = JwtTokenUtil.getSecurityUser(token);
            // 刷新令牌
            //String newToken = JwtTokenUtil.refreshUserToken(sysLoginUser,30000);
        } catch (AuthException e) {
            e.printStackTrace();
            return GatewayUtils.getMono(exchange.getResponse(), CommonResponseEnum.TOKEN_ERROR);
        }

        // 判断用户是否存在
        if(securityUser==null){
            return GatewayUtils.getMono(exchange.getResponse(), CommonResponseEnum.NON_EXISTENT_USERS);
        }

        // 如果是超级管理员，直接放过资源权限校验
        if(JwtTokenUtil.isSuperAdmin(securityUser)) {
            return chain.filter(exchange);
            //return chain.filter(getMutableExchange(exchange,sysLoginUser));
        }

        // 首先校验当前用户有没有 当前请求requestUri的权限
        boolean hasUriPermission = hasPermission(securityUser,url);
        if (!hasUriPermission) {
            // 当前用户具没有有资源访问权限
            return GatewayUtils.getMono(exchange.getResponse(),CommonResponseEnum.UNAUTHORIZED);
        }
       /* SysLoginUser finalSysLoginUser = sysLoginUser;
        return chain.filter(getMutableExchange(exchange,sysLoginUser)).then(
                Mono.fromRunnable(() -> {
                    MultiValueMap<String, String> queryParams = request.getQueryParams();
                    String requestUrl = UriComponentsBuilder.fromPath(path).queryParams(queryParams).build().toUriString();
                    ServerHttpResponse response = exchange.getResponse();
                    HttpHeaders headers = response.getHeaders();
                    headers.add(CacheConstants.HEADER, CacheConstants.TOKEN_PREFIX + finalSysLoginUser.getToken());
                })
        );*/
        return chain.filter(exchange);
        //return chain.filter(getMutableExchange(exchange,sysLoginUser));
    }

    /**
    * 匹配白名单
    */
    private boolean matchUrl(List<String> patterns,String url){
        if(StringUtils.hasLength(url) && !CollectionUtils.isEmpty(patterns)){
            for (String pattern : patterns) {
               if(antPathMatcher.match(pattern,url)){
                   return true;
               }
            }
        }
        return false;
    }

    private ServerWebExchange getMutableExchange(ServerWebExchange exchange, SecurityUser securityUser){
        // 设置用户信息到到请求头
        ServerHttpRequest mutableReq = exchange.getRequest().mutate()
                .header(CacheConstants.DETAILS_USER_ID, securityUser.getId().toString())
                .header(CacheConstants.DETAILS_USERNAME, ServletUtil.urlEncode(securityUser.getUsername()))
                .header(CacheConstants.HEADER, CacheConstants.TOKEN_PREFIX + securityUser.getToken()).build();
        //设置用户信息到响应头
        ServerHttpResponse response = exchange.getResponse();
        response.getHeaders().add(CacheConstants.HEADER, CacheConstants.TOKEN_PREFIX + securityUser.getToken());
        DataBufferFactory bufferFactory = response.bufferFactory();
        ServerHttpResponseDecorator mutableRes = new ServerHttpResponseDecorator(response) {
            @Override
            public Mono<Void> writeWith(Publisher<? extends DataBuffer> body) {
                if (body instanceof Flux) {
                    Flux<? extends DataBuffer> fluxBody = (Flux<? extends DataBuffer>) body;
                    return super.writeWith(fluxBody.map(dataBuffer -> {
                        byte[] content = new byte[dataBuffer.readableByteCount()];
                        dataBuffer.read(content);
                        return bufferFactory.wrap(content);
                    }));
                }
                return super.writeWith(body);
            }
        };
        ServerWebExchange mutableExchange = exchange.mutate().request(mutableReq).response(mutableRes).build();
        return mutableExchange;
    }

    /**
     * 网关抛异常
     *
     * @param exchange
     */
   /* @NotNull
    private Mono<Void> getVoidMono(ServerWebExchange exchange,String msg) {
        ServerHttpResponse response = exchange.getResponse();
        response.getHeaders().setContentType(MediaType.APPLICATION_JSON);
        response.setStatusCode(HttpStatus.OK);
        log.error("[鉴权异常处理]请求路径:{}", exchange.getRequest().getPath());
        return response.writeWith(Mono.fromSupplier(() -> {
            DataBufferFactory bufferFactory = response.bufferFactory();
            return bufferFactory.wrap(JSON.toJSONBytes(CommonResponse.general(CommonResponseEnum.UNAUTHORIZED,msg)));
        }));
    }*/


    /**
     * 判断当前登录用户是否是否包含资源权限
     * 1.获取系统所有权限
     * 2.判断当前uri是否在所有权限验证清单，如果不存在则无需进行用户验证
     * 3.如果uri存在于权限验证清单，则进行用户权限匹配，如果无法匹配则返回错误
     */
    public boolean hasPermission(SecurityUser securityUser,String requestUri) {
     /*   String removePrefix = StrUtil.removePrefix(requestUri, SymbolConstant.LEFT_DIVIDE);
        //Collection<? extends GrantedAuthority> authorities = securityUser.getAuthorities();
        //从redis获取权限
        Object object = redisTemplate.opsForHash().get(JwtTokenUtil.getUserRedisTokenKey(securityUser.getId()), "authorities");
        List<GrantedAuthority> authorities = JSONArray.parseArray(JSON.toJSONString(object),GrantedAuthority.class);
        String requestPermission = removePrefix.replaceAll(SymbolConstant.LEFT_DIVIDE, SymbolConstant.COLON);
        if(authorities.contains(requestPermission) && authorities.equals(AuthoritiesConfig.DEFAULT_AUTHORITIES)){
            return true;
        }
        return false;*/
        return true;
    }

    @Override
    public int getOrder() {
        return -200;
    }
}
