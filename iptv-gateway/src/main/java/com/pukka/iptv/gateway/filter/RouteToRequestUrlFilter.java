//package com.pukka.iptv.gateway.filter;
//import static org.springframework.cloud.gateway.support.ServerWebExchangeUtils.GATEWAY_ROUTE_ATTR;
//import static org.springframework.cloud.gateway.support.ServerWebExchangeUtils.GATEWAY_SCHEME_PREFIX_ATTR;
//import static org.springframework.cloud.gateway.support.ServerWebExchangeUtils.containsEncodedParts;
//
//import java.io.UnsupportedEncodingException;
//import java.net.InetSocketAddress;
//import java.net.URI;
//import java.net.URLEncoder;
//import java.util.ArrayList;
//import java.util.HashMap;
//import java.util.List;
//import java.util.regex.Pattern;
//
//import org.slf4j.Logger;
//import org.slf4j.LoggerFactory;
//import org.springframework.cloud.gateway.filter.GatewayFilterChain;
//import org.springframework.cloud.gateway.filter.GlobalFilter;
//import org.springframework.cloud.gateway.route.Route;
//import org.springframework.core.Ordered;
//import org.springframework.http.HttpMethod;
//import org.springframework.http.server.reactive.ServerHttpRequest;
//import org.springframework.stereotype.Component;
//import org.springframework.util.CollectionUtils;
//import org.springframework.web.server.ServerWebExchange;
//import org.springframework.web.util.UriComponents;
//import org.springframework.web.util.UriComponentsBuilder;
//
//import com.alibaba.fastjson.JSON;
//
//import reactor.core.publisher.Mono;
//
///**
// * @Author: zhengcl
// * @Date: 2021/12/20 18:47
// */
//
//@Component
//public class RouteToRequestUrlFilter implements GlobalFilter, Ordered {
//
//    private static Logger logger = LoggerFactory.getLogger(RouteToRequestUrlFilter.class);
//
//    public static final int ROUTE_TO_URL_FILTER_ORDER = -300;
//    private static final String SCHEME_REGEX = "[a-zA-Z]([a-zA-Z]|\\d|\\+|\\.|-)*:.*";
//    static final Pattern schemePattern = Pattern.compile(SCHEME_REGEX);
//    @Override
//    public int getOrder() {
//        return ROUTE_TO_URL_FILTER_ORDER;
//    }
//
//    @Override
//    public Mono<Void> filter(ServerWebExchange exchange, GatewayFilterChain chain) {
//        ServerHttpRequest request = exchange.getRequest();
//        String path = request.getPath().pathWithinApplication().value();
//        InetSocketAddress remoteAddress = request.getRemoteAddress();
//        HttpMethod method = request.getMethod();
//        Route route = exchange.getAttribute(GATEWAY_ROUTE_ATTR);
//        if (route == null) {
//            return chain.filter(exchange);
//        }
//        URI uri = request.getURI();
//        boolean encoded = containsEncodedParts(uri);
//        URI routeUri = route.getUri();
//        logger.info("请求uri:{}",JSON.toJSONString(uri));
//        if (hasAnotherScheme(routeUri)) {
//            // this is a special url, save scheme to special attribute
//            // replace routeUri with schemeSpecificPart
//            exchange.getAttributes().put(GATEWAY_SCHEME_PREFIX_ATTR, routeUri.getScheme());
//            routeUri = URI.create(routeUri.getSchemeSpecificPart());
//        }
//        logger.info("请求接口:{},客户端远程IP地址:{},请求方法:{},目标URI:{}",
//                path, JSON.toJSONString(remoteAddress), JSON.toJSONString(method), JSON.toJSONString(routeUri));
////        if(path.startsWith("转发的接口地址")) {//不是所有的都走编码所以加了判断
//
//            UriComponents uriComponents = UriComponentsBuilder.fromUri(exchange.getRequest().getURI()).build();
//            HashMap<String, List<String>> newQueryParamsMap = new HashMap<>();
//            uriComponents.getQueryParams().entrySet().forEach(mapEntry ->{
//                String newKey = null;
//                try {
//                    newKey = mapEntry.getKey().contains("%")? mapEntry.getKey(): URLEncoder.encode(mapEntry.getKey(), "UTF-8");
//                } catch (UnsupportedEncodingException e) {
//                    e.printStackTrace();
//                }
//                List<String> newValue = new ArrayList<>();
//                mapEntry.getValue().forEach(listEntry ->{
//
//                    if (listEntry.contains("%")) {
//                        newValue.add(listEntry);
//                    } else {
//                        try {
//                            newValue.add(URLEncoder.encode(listEntry, "UTF-8"));
//                        } catch (UnsupportedEncodingException e) {
//                            e.printStackTrace();
//                        }
//                    }
//                });
//                newQueryParamsMap.put(newKey,newValue);
//            });
//            URI mergedUrl = UriComponentsBuilder.fromUri(uri)
//                    .replaceQueryParams(CollectionUtils.toMultiValueMap(newQueryParamsMap))
//                    .scheme(routeUri.getScheme())
//                    .host(routeUri.getHost())
//                    .port(routeUri.getPort())
//                    .build(encoded)
//                    .toUri();
//            ServerHttpRequest newRequest = request.mutate().uri(mergedUrl).build();
//            exchange = exchange.mutate().request(newRequest).build();
////        }
//        return chain.filter(exchange);
//    }
//
//    /* for testing */
//    static boolean hasAnotherScheme(URI uri) {
//        return schemePattern.matcher(uri.getSchemeSpecificPart()).matches() && uri.getHost() == null
//                && uri.getRawPath() == null;
//    }
//
//}