//package com.pukka.iptv.gateway.config;
//
//import io.netty.channel.ChannelHandlerContext;
//import io.netty.channel.ChannelInboundHandlerAdapter;
//import io.netty.handler.codec.http.HttpRequest;
//import lombok.extern.slf4j.Slf4j;
//import org.springframework.boot.autoconfigure.web.embedded.NettyWebServerFactoryCustomizer;
//import org.springframework.boot.autoconfigure.web.embedded.TomcatWebServerFactoryCustomizer;
//import org.springframework.boot.autoconfigure.web.reactive.TomcatReactiveWebServerFactoryCustomizer;
//import org.springframework.boot.autoconfigure.web.servlet.TomcatServletWebServerFactoryCustomizer;
//import org.springframework.boot.web.embedded.netty.NettyReactiveWebServerFactory;
//import org.springframework.boot.web.embedded.netty.NettyServerCustomizer;
//import org.springframework.boot.web.embedded.tomcat.TomcatConnectorCustomizer;
//import org.springframework.boot.web.embedded.tomcat.TomcatServletWebServerFactory;
//import org.springframework.boot.web.server.WebServerFactoryCustomizer;
//import org.springframework.stereotype.Component;
//import reactor.netty.ConnectionObserver;
//import reactor.netty.NettyPipeline;
//
//import java.io.UnsupportedEncodingException;
//import java.net.URLDecoder;
//import java.net.URLEncoder;
//import java.util.ArrayList;
//import java.util.List;
//
///**
// * @Author: zhengcl
// * @Date: 2021/12/22 18:32
// * 兼容Tomcat不支持特殊字符，兼容高版本tomcat
// */
//
//@Component
//@Slf4j
//class MyNettyWebServerCustomizer  implements WebServerFactoryCustomizer<NettyReactiveWebServerFactory> {
//
//    /**
//     * 需要encode的特殊字符
//     */
//    private final List<Character> charList = new ArrayList<Character>() {
//        {
//            this.add('|');
//            this.add('{');
//            this.add('}');
//            this.add('[');
//            this.add(']');
//        }
//    };
//
//    @Override
//    public void customize(NettyReactiveWebServerFactory factory) {
//        factory.addServerCustomizers((NettyServerCustomizer) httpServer ->
//                httpServer.observe((conn, state) -> {
//                    if (state == ConnectionObserver.State.CONNECTED) {
//                        conn.channel().pipeline().addAfter(NettyPipeline.HttpCodec, "",  new QueryHandler());
//                    }
//                }));
//    }
//
//
//    class QueryHandler extends ChannelInboundHandlerAdapter {
//
//        public QueryHandler() {
//        }
//
//        @Override
//        public void channelRead(ChannelHandlerContext ctx, Object msg) throws UnsupportedEncodingException {
//            if (msg instanceof HttpRequest) {
//                HttpRequest request = (HttpRequest) msg;
//                String url = request.uri();
//                // fix url
//                log.info("url: {}", url);
//               /* String[] split = url.split("\\?");
//                StringBuilder fixUrl = new StringBuilder(split[0]);
//                if (split.length > 1) {
//                    fixUrl.append("?");
//                    char[] chars = split[1].toCharArray();
//                    for (char aChar : chars) {
//                        if (charList.contains(aChar)) {
//                            fixUrl.append(URLEncoder.encode(String.valueOf(aChar), "UTF-8"));
//                        }else {
//                            fixUrl.append(aChar);
//                        }
//                    }
//                }*/
//                String fixUrl = URLDecoder.decode(url,"UTF-8");
//                log.info("fixUrl: {}", fixUrl);
//                request.setUri(fixUrl);
//            }
//            ctx.fireChannelRead(msg);
//        }
//        @Override
//        public boolean isSharable() {
//            return true;
//        }
//    }
//}