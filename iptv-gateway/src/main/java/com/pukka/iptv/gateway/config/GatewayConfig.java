package com.pukka.iptv.gateway.config;

import com.pukka.iptv.gateway.handler.GatewayExceptionHandler;
import com.pukka.iptv.gateway.handler.SentinelFallbackHandler;
import org.springframework.boot.autoconfigure.web.ErrorProperties;
import org.springframework.boot.autoconfigure.web.WebProperties;
import org.springframework.boot.autoconfigure.web.reactive.error.DefaultErrorWebExceptionHandler;
import org.springframework.boot.web.reactive.error.ErrorAttributes;
import org.springframework.cloud.gateway.filter.factory.RetryGatewayFilterFactory;
import org.springframework.context.ApplicationContext;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.core.Ordered;
import org.springframework.core.annotation.Order;
import org.springframework.http.HttpMethod;

import java.util.stream.Collectors;

import static org.springframework.http.HttpStatus.INTERNAL_SERVER_ERROR;
import static org.springframework.http.HttpStatus.Series.SERVER_ERROR;

/**
 * 网关限流配置
 *
 * <AUTHOR>
 * @date 2021/7/23
 */

@Configuration
public class GatewayConfig {
    @Bean
    @Order(Ordered.HIGHEST_PRECEDENCE)
    public SentinelFallbackHandler sentinelGatewayExceptionHandler() {
        return new SentinelFallbackHandler();
    }

    /**
     * 将bean注入到容器中的方式：
     * 1、xml配置文件bean标签
     * 2、组件注解
     * 3、组件类中通过@Bean标注的方法返回值
     */
//    @Bean
////    @Order(-1) //设置优先级
//    public DefaultErrorWebExceptionHandler defaultErrorWebExceptionHandler(
//            ErrorAttributes errorAttributes,
//            WebProperties.Resources resources,
//            ErrorProperties errorProperties,
//            ApplicationContext applicationContext
//    ) {
//
//        GatewayExceptionHandler gatewayExceptionHandler = new GatewayExceptionHandler(
//                errorAttributes,
//                resources,
//                errorProperties,
//                applicationContext);
//
//        gatewayExceptionHandler.setViewResolvers(viewResolvers.orderedStream().collect(Collectors.toList()));
//        gatewayExceptionHandler.setMessageWriters(serverCodecConfigurer.getWriters());
//        gatewayExceptionHandler.setMessageReaders(serverCodecConfigurer.getReaders());
//
//        return gatewayExceptionHandler;
//    }
}
