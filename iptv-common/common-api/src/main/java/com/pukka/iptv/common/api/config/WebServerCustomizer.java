package com.pukka.iptv.common.api.config;

import org.apache.coyote.http11.Http11NioProtocol;
import org.springframework.boot.web.embedded.tomcat.TomcatConnectorCustomizer;
import org.springframework.boot.web.embedded.tomcat.TomcatServletWebServerFactory;
import org.springframework.boot.web.server.WebServerFactoryCustomizer;
import org.springframework.stereotype.Component;

/**
 * @Author: zhengcl
 * @Date: 2021/12/22 18:32
 * 兼容Tomcat不支持特殊字符，兼容高版本tomcat
 */

@Component
class WebServerCustomizer implements WebServerFactoryCustomizer<TomcatServletWebServerFactory> {

    //参考
    /*@Configuration
    public class TomcatConfig {
        @Bean
        public ServletWebServerFactory webServerFactory() {
            TomcatServletWebServerFactory fa = new TomcatServletWebServerFactory();
            fa.addConnectorCustomizers(connector -> {
                connector.setProperty("relaxedQueryChars", "(),/:;<=>?@[]{}");
                connector.setProperty("rejectIllegalHeader", "false");
            });
            return fa;
        }
    }*/

    @Override
    public void customize(TomcatServletWebServerFactory factory) {
//        factory.addBuilderCustomizers(builder-> builder.setServerOption(UndertowOptions.ALLOW_UNESCAPED_CHARACTERS_IN_URL, Boolean.TRUE));
        factory.addConnectorCustomizers((TomcatConnectorCustomizer) connector -> {
//            connector.setProperty("relaxedQueryChars", "[]{}");
            connector.setProperty("relaxedPathChars", "\"<>[\\]^`{|}(),/:;<=>?@[]{}");
            connector.setProperty("relaxedQueryChars", "\"<>[\\]^`{|}(),/:;<=>?@[]{}");
            connector.setProperty("rejectIllegalHeader", "false");
            connector.setMaxParameterCount(Integer.MAX_VALUE);
            Http11NioProtocol protocol = (Http11NioProtocol) connector.getProtocolHandler();
            protocol.setDisableUploadTimeout(false);
            protocol.setMaxHeaderCount(20 * 1024);
            protocol.setMaxHttpHeaderSize(40 * 1024);
            //protocol.setAcceptCount(200);
            //protocol.setMaxConnections(200);
//            protocol.setConnectionTimeout(20000);
//            protocol.setMaxSavePostSize(4194304);
        });
    }

    //上传限制
    /*@Bean
    public MultipartConfigElement multipartConfigElement() {
        MultipartConfigFactory factory = new MultipartConfigFactory();
        //  单个数据大小
        factory.setMaxFileSize("1024MB"); // KB,MB
        /// 总上传数据大小
        factory.setMaxRequestSize("1024MB");
        return factory.createMultipartConfig();
    }*/
}
