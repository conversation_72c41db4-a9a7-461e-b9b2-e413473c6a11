package com.pukka.iptv.common.api.config;

import com.alibaba.fastjson.serializer.SerializerFeature;
import com.alibaba.fastjson.support.config.FastJsonConfig;
import com.alibaba.fastjson.support.spring.FastJsonHttpMessageConverter;
import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.SerializationFeature;
import com.fasterxml.jackson.databind.module.SimpleModule;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import com.fasterxml.jackson.datatype.jdk8.Jdk8Module;
import com.fasterxml.jackson.datatype.jsr310.JavaTimeModule;
import com.jeesuite.springweb.ext.feign.CustomErrorDecoder;
import com.jeesuite.springweb.utils.WebUtils;
import com.pukka.iptv.common.api.handler.FeignRequestInterceptor;
import com.pukka.iptv.common.core.util.MediaTypeUtil;
import feign.*;
import feign.codec.Decoder;
import feign.codec.Encoder;
import feign.form.spring.SpringFormEncoder;
import feign.jackson.JacksonDecoder;
import feign.optionals.OptionalDecoder;
import org.springframework.beans.BeansException;
import org.springframework.beans.factory.ObjectFactory;
import org.springframework.beans.factory.ObjectProvider;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.autoconfigure.condition.ConditionalOnClass;
import org.springframework.boot.autoconfigure.condition.ConditionalOnMissingBean;
import org.springframework.boot.autoconfigure.condition.ConditionalOnMissingClass;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.boot.autoconfigure.http.HttpMessageConverters;
import org.springframework.boot.autoconfigure.web.servlet.WebMvcRegistrations;
import org.springframework.cloud.openfeign.FeignClientsConfiguration;
import org.springframework.cloud.openfeign.support.DefaultGzipDecoder;
import org.springframework.cloud.openfeign.support.ResponseEntityDecoder;
import org.springframework.cloud.openfeign.support.SpringDecoder;
import org.springframework.cloud.openfeign.support.SpringEncoder;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Primary;
import org.springframework.core.annotation.AnnotationUtils;
import org.springframework.http.MediaType;
import org.springframework.http.codec.json.Jackson2JsonDecoder;
import org.springframework.http.converter.HttpMessageConverter;
import org.springframework.http.converter.json.MappingJackson2HttpMessageConverter;
import org.springframework.stereotype.Controller;
import org.springframework.web.servlet.config.annotation.EnableWebMvc;
import org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerMapping;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.TimeZone;
import java.util.stream.Collectors;

/**
 * Feign配置
 * <AUTHOR>
 * @date 2021-07-14
 */

@Configuration
@ConditionalOnClass({Feign.class})
public class FeignConfig extends FeignClientsConfiguration {

//    @Autowired
//    private ObjectFactory<HttpMessageConverters> messageConverters;

    @Bean
    Logger.Level feignLoggerLevel() {
        return Logger.Level.FULL;
    }

    /*@Bean
    public Retryer feignRetryer(){
        // period=100 发起当前请求的时间间隔,单位毫秒
        // maxPeriod=1000 发起当前请求的最大时间间隔,单位毫秒
        // maxAttempts=2 重试次数是1，因为包括第一次，所以我们如果想要重试2次，就需要设置为3
        Retryer retryer = new Retryer.Default(100, 1000, 3);
        return retryer;
    }*/

    /*@Bean
    public Contract jaxrsContract() {
        return new JAXRSContract();
    }*/

    /*private HttpMessageConverter createFastJsonConverter() {

        //创建fastJson消息转换器
        FastJsonHttpMessageConverter fastConverter = new FastJsonHttpMessageConverter();

        //升级最新版本需加=============================================================
        fastConverter.setSupportedMediaTypes(MediaTypeUtil.getSupportMediaTypes());

        //创建配置类
        FastJsonConfig fastJsonConfig = new FastJsonConfig();
        //修改配置返回内容的过滤
        //WriteNullListAsEmpty  ：List字段如果为null,输出为[],而非null
        //WriteNullStringAsEmpty ： 字符类型字段如果为null,输出为"",而非null
        //DisableCircularReferenceDetect ：消除对同一对象循环引用的问题，默认为false（如果不配置有可能会进入死循环）
        //WriteNullBooleanAsFalse：Boolean字段如果为null,输出为false,而非null
        //WriteMapNullValue：是否输出值为null的字段,默认为false
        fastJsonConfig.setSerializerFeatures(
                SerializerFeature.DisableCircularReferenceDetect,
                SerializerFeature.WriteMapNullValue,
                SerializerFeature.PrettyFormat,
                SerializerFeature.WriteNullListAsEmpty,
                SerializerFeature.WriteMapNullValue,
                SerializerFeature.WriteEnumUsingToString,
                SerializerFeature.WriteNullStringAsEmpty
        );
        fastJsonConfig.setDateFormat("yyyy-MM-dd HH:mm:ss");
        fastConverter.setFastJsonConfig(fastJsonConfig);

        return fastConverter;
    }*/

    /*@Bean
    @Override
    public Retryer feignRetryer() {
        return new Retryer.Default();
    }

    @Bean
    public Encoder feignFormEncoder() {
        return new SpringFormEncoder();
    }*/
    /* @Bean
    @Override
    public Decoder feignDecoder() {

    }*/

   /* @Bean
    public Encoder feignEncoder(){
        return new SpringEncoder(messageConverters)
    }*/

    /*@Bean
    @Override
    public Decoder feignDecoder() {
//        return new GenericsFeignResultDecoder();
//        return new JacksonDecoder();
        return new ResponseEntityDecoder(new SpringDecoder(messageConverters));
    }*/

    @Bean
    public Contract feignContract() {
        return new feign.Contract.Default();
    }

    @Bean
    @Override
    public Decoder feignDecoder() {
        MappingJackson2HttpMessageConverter jacksonConverter = new MappingJackson2HttpMessageConverter(customObjectMapper());
        jacksonConverter.setSupportedMediaTypes(MediaTypeUtil.getSupportMediaTypes());
        ObjectFactory<HttpMessageConverters> objectFactory = () -> new HttpMessageConverters(jacksonConverter);
        return new ResponseEntityDecoder(new SpringDecoder(objectFactory));
    }

    @Bean
    public Encoder feignEncoder(){
        MappingJackson2HttpMessageConverter jacksonConverter = new MappingJackson2HttpMessageConverter(customObjectMapper());
        jacksonConverter.setSupportedMediaTypes(MediaTypeUtil.getSupportMediaTypes());
        ObjectFactory<HttpMessageConverters> objectFactory = () -> new HttpMessageConverters(jacksonConverter);
        return new SpringEncoder(objectFactory);
    }

    public ObjectMapper customObjectMapper(){
        ObjectMapper objectMapper = new ObjectMapper();
        objectMapper.configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);
        objectMapper.configure(SerializationFeature.FAIL_ON_EMPTY_BEANS, false);
        // objectMapper.configure(DeserializationFeature.ACCEPT_EMPTY_STRING_AS_NULL_OBJECT, true);
        // objectMapper.disable(SerializationFeature.FAIL_ON_EMPTY_BEANS);
        // SimpleModule simpleModule = new SimpleModule();
        // simpleModule.addSerializer(Long.class, ToStringSerializer.instance);
        // 去掉long类型的字段即分页属性size/total。只有Long类型才会生效，即主键id
        // simpleModule.addSerializer(Long.TYPE, ToStringSerializer.instance);
        // objectMapper.registerModule(simpleModule);
        objectMapper.registerModule(new JavaTimeModule());
        objectMapper.setTimeZone(TimeZone.getDefault());
        return objectMapper;
    }

    @Bean
    @Primary
    @ConditionalOnProperty("feign.compression.response.useGzipDecoder")
    public Decoder responseGzipDecoder(ObjectFactory<HttpMessageConverters> messageConverters) {
        return new OptionalDecoder(new ResponseEntityDecoder(
                new DefaultGzipDecoder(new SpringDecoder(messageConverters))));
    }

    @Bean
    public WebMvcRegistrations feignWebRegistrations() {
        RequestMappingHandlerMapping handlerMapping = this.requestMappingHandlerMapping();
        return new WebMvcRegistrations() {
            @Override
            public RequestMappingHandlerMapping getRequestMappingHandlerMapping() {
                return handlerMapping;
            }
        };
    }


    /**
     * 使SpringMVC只扫描带有@Controller的@RequestMapping，
     * 而忽略掉带有@RequestMapping的FeignClient的接口，从而避免启动报Ambiguous mapping错误
     */
    private RequestMappingHandlerMapping requestMappingHandlerMapping() {
        return new RequestMappingHandlerMapping() {
            @Override
            protected boolean isHandler(Class<?> beanType) {
                return super.isHandler(beanType) && (AnnotationUtils.findAnnotation(beanType, Controller.class) != null);
            }
        };
    }

    @Bean
    public RequestInterceptor requestInterceptor()
    {
        return new FeignRequestInterceptor();
    }

    /**
     * @return
     * 使用了RequestInterceptor拦截器, 可以定制请求头, 如果不想定制, 可以改为
     * return Feign.builder().errorDecoder(new CustomErrorDecoder());
     */
    @Bean
    public Feign.Builder feignBuilder() {
        return Feign.builder().requestInterceptor(new RequestInterceptor() {
            @Override
            public void apply(RequestTemplate requestTemplate) {
                Map<String, String> customHeaders = WebUtils.getCustomHeaders();
                customHeaders.forEach((k, v) -> {
                    requestTemplate.header(k, v);
                });
            }
        }).errorDecoder(new FeignErrorDecoder());
    }
}