package com.pukka.iptv.common.api.feign.sys;

/*
import com.pukka.iptv.common.base.vo.CommonResponse;
import com.pukka.iptv.common.core.constant.ServerConstants;
import com.pukka.iptv.common.data.model.sys.SysOutPassage;
import com.pukka.iptv.common.data.vo.sys.SysOutpassageStorageRelaVO;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestParam;

import javax.validation.Valid;

@FeignClient(name = ServerConstants.MANAGE_SERVER,path = "/storageInformation")
public interface SysOutpassageStorageRelaFeignClient {
    *//**
     * 获取账户信息
     * @param outPassageId
     * @return
     *//*
    @GetMapping("/getByOutPassageIdAndStorageId")
    public CommonResponse<SysOutpassageStorageRelaVO> getByOutPassageIdAndStorageId(
            @Valid @RequestParam(name = "outPassageId", required = true)  Long outPassageId,
            @RequestParam(name = "storageId", required = true)  Long storageId);

}*/
