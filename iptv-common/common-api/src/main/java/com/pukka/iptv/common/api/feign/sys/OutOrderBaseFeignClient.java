package com.pukka.iptv.common.api.feign.sys;

import com.pukka.iptv.common.base.vo.CommonResponse;
import com.pukka.iptv.common.core.constant.ServerConstants;
import com.pukka.iptv.common.data.model.OutOrderBase;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestParam;

import javax.validation.Valid;
import java.util.List;

/**
 * @description  主工单
 */
@FeignClient(name = ServerConstants.MANAGE_SERVER,path = "/outOrderBase")
public interface OutOrderBaseFeignClient {

    /**
     * 获取主工单信息
     * @param id
     * @return
     */
    @GetMapping("/getById")
    CommonResponse<OutOrderBase> getById(@Valid @RequestParam(name = "id", required = true) Long id);

    /**
     * 查询物理频道id信息
     * @param baseOrderId
     * @return
     */
    @PostMapping("/getPhysicalChannelIds")
    CommonResponse<List<Long>> getPhysicalChannelIds(@Valid @RequestParam(name = "baseOrderId", required = true) String baseOrderId);

    /**
     * 查询节目单id信息
     * @param baseOrderId
     * @return
     */
    @PostMapping("/getScheduleIds")
    CommonResponse<List<Long>> getScheduleIds(@Valid @RequestParam(name = "baseOrderId", required = true) String baseOrderId);

    /**
     * 查询子集id信息
     * @param baseOrderId
     * @return
     */
    @PostMapping("/getSubsetIds")
    CommonResponse<List<Long>> getSubsetIds(@Valid @RequestParam(name = "baseOrderId", required = true) String baseOrderId);

    /**
     * 查询主工单信息
     * @param ids
     * @return
     */
    @GetMapping("/getByIds")
    CommonResponse<List<OutOrderBase>> getByIds(@Valid @RequestParam(name = "ids", required = true) String ids,@Valid @RequestParam(name = "contentType", required = true) String contentType, @Valid @RequestParam(name = "action", required = true) Integer action);
}
