package com.pukka.iptv.common.api.config;

import com.pukka.iptv.common.core.config.HttpPoolProperties;
import com.pukka.iptv.common.proxy.config.SystemInProxyProperties;
import lombok.extern.slf4j.Slf4j;
import org.apache.http.HttpHost;
import org.apache.http.auth.AuthScope;
import org.apache.http.auth.UsernamePasswordCredentials;
import org.apache.http.client.CredentialsProvider;
import org.apache.http.client.HttpClient;
import org.apache.http.client.config.RequestConfig;
import org.apache.http.config.Registry;
import org.apache.http.config.RegistryBuilder;
import org.apache.http.conn.socket.ConnectionSocketFactory;
import org.apache.http.conn.socket.PlainConnectionSocketFactory;
import org.apache.http.conn.ssl.NoopHostnameVerifier;
import org.apache.http.conn.ssl.SSLConnectionSocketFactory;
import org.apache.http.conn.ssl.TrustSelfSignedStrategy;
import org.apache.http.impl.client.BasicCredentialsProvider;
import org.apache.http.impl.client.HttpClientBuilder;
import org.apache.http.impl.client.HttpClients;
import org.apache.http.impl.client.LaxRedirectStrategy;
import org.apache.http.impl.conn.PoolingHttpClientConnectionManager;
import org.apache.http.ssl.SSLContextBuilder;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.web.servlet.MultipartConfigFactory;
import org.springframework.cloud.client.loadbalancer.LoadBalanced;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.http.client.ClientHttpRequestFactory;
import org.springframework.http.client.HttpComponentsClientHttpRequestFactory;
import org.springframework.http.client.SimpleClientHttpRequestFactory;
import org.springframework.http.client.support.BasicAuthenticationInterceptor;
import org.springframework.http.converter.HttpMessageConverter;
import org.springframework.http.converter.StringHttpMessageConverter;
import org.springframework.util.unit.DataSize;
import org.springframework.web.client.RestTemplate;

import javax.servlet.MultipartConfigElement;
import java.net.InetSocketAddress;
import java.net.Proxy;
import java.nio.charset.StandardCharsets;
import java.security.KeyManagementException;
import java.security.KeyStoreException;
import java.security.NoSuchAlgorithmException;
import java.time.Duration;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2021/8/16 RestTemplate
 */

@Slf4j
@Configuration
public class RestTemplateConfig {

    @Value("${spring.servlet.multipart.max-file-size:50}")
    private Integer maxFileSize;
    @Value("${spring.servlet.multipart.max-request-size:100}")
    private Integer maxRequestSize;
    @Autowired
    private HttpPoolProperties httpPoolProperties;
    @Autowired
    private SystemInProxyProperties systemInProxyProperties;

    @Bean("restTemplate")
    @LoadBalanced
    public RestTemplate restTemplate() {
        //支持数据压缩的resttemplate
        HttpComponentsClientHttpRequestFactory factory = new HttpComponentsClientHttpRequestFactory(
                HttpClientBuilder.create().build());
        RestTemplate restTemplate = new RestTemplate(factory);
        return restTemplate;
    }

    @Bean("httpRestTemplate")
    public RestTemplate httpRestTemplate() throws NoSuchAlgorithmException, KeyStoreException, KeyManagementException {
        //SimpleClientHttpRequestFactory requestFactory = new SimpleClientHttpRequestFactory();
        //具备连接池配置
        RestTemplate restTemplate = new RestTemplate(httpRequestFactory());
        List<HttpMessageConverter<?>> converters = restTemplate.getMessageConverters();
        converters.remove(1); // 移除原来的转换器
        // 设置字符编码为utf-8
        HttpMessageConverter<?> converter = new StringHttpMessageConverter(StandardCharsets.UTF_8);
        converters.add(1, converter); // 添加新的转换器(注:convert顺序错误会导致失败)
        restTemplate.setMessageConverters(converters);

        return restTemplate;
    }

    @Bean("httpProxyRestTemplate")
    public RestTemplate httpProxyRestTemplate() {
        RestTemplate restTemplate = new RestTemplate();
        SimpleClientHttpRequestFactory factory = new SimpleClientHttpRequestFactory();
        factory.setReadTimeout(5 * 000);//ms
        factory.setConnectTimeout(10 * 0000);//ms
        if (systemInProxyProperties.isEnable()) {
            factory.setProxy(new Proxy(Proxy.Type.HTTP, new InetSocketAddress(systemInProxyProperties.getHost(), systemInProxyProperties.getPort())));
            restTemplate.getInterceptors().add(new BasicAuthenticationInterceptor(systemInProxyProperties.getUsername(), systemInProxyProperties.getPassword()));
        }
        restTemplate.setRequestFactory(factory);
        List<HttpMessageConverter<?>> converters = restTemplate.getMessageConverters();
        converters.remove(1); // 移除原来的转换器
        // 设置字符编码为utf-8
        HttpMessageConverter<?> converter = new StringHttpMessageConverter(StandardCharsets.UTF_8);
        // 添加新的转换器(注:convert顺序错误会导致失败)
        converters.add(1, converter);
        restTemplate.setMessageConverters(converters);
        return restTemplate;
    }

	/*@Bean("httpProxyRestTemplate")
	public RestTemplate httpProxyRestTemplate() {
		SimpleClientHttpRequestFactory httpRequestFactory = new SimpleClientHttpRequestFactory();
		httpRequestFactory.setReadTimeout(10*1000);
		httpRequestFactory.setConnectTimeout(10*1000);
		if(systemInProxyProperties.isEnable()){
			SocketAddress address = new InetSocketAddress(systemInProxyProperties.getHost(), systemInProxyProperties.getPort());
			Proxy proxy = new Proxy(Proxy.Type.HTTP, address);
			httpRequestFactory.setProxy(proxy);
		}
		RestTemplate restTemplate = new RestTemplate(httpRequestFactory);
		if(systemInProxyProperties.isEnable()){
//			RestTemplate template = new RestTemplateBuilder()
//					.basicAuthentication("username", "password")
//					.build();
			List<ClientHttpRequestInterceptor> interceptors = restTemplate.getInterceptors();
			interceptors.add(new BasicAuthenticationInterceptor(systemInProxyProperties.getUsername(),systemInProxyProperties.getPassword()));
		}
		return restTemplate;
	}*/

	/*@Bean("httpProxyRestTemplate")
	public RestTemplate httpProxyRestTemplate() throws NoSuchAlgorithmException, KeyStoreException, KeyManagementException {
		//SimpleClientHttpRequestFactory requestFactory = new SimpleClientHttpRequestFactory();
		//具备连接池配置
		RestTemplate restTemplate = new RestTemplate(httpProxyRequestFactory());
		List<HttpMessageConverter<?>> converterList = restTemplate.getMessageConverters();
		converterList.remove(1); // 移除原来的转换器
		// 设置字符编码为utf-8
		HttpMessageConverter<?> converter = new StringHttpMessageConverter(StandardCharsets.UTF_8);
		converterList.add(1, converter); // 添加新的转换器(注:convert顺序错误会导致失败)
		restTemplate.setMessageConverters(converterList);

		return restTemplate;
	}*/

    public ClientHttpRequestFactory httpRequestFactory() throws NoSuchAlgorithmException, KeyStoreException, KeyManagementException {
        HttpComponentsClientHttpRequestFactory requestFactory = new HttpComponentsClientHttpRequestFactory();
        //默认值为true，当post或者put大文件的时候会造成内存溢出情况，设置为false将数据直接流入底层HttpURLConnection
        requestFactory.setBufferRequestBody(false);
        requestFactory.setHttpClient(httpClient());
        requestFactory.setConnectTimeout((int) Duration.ofSeconds(5).toMillis());
        return requestFactory;
    }

    public ClientHttpRequestFactory httpProxyRequestFactory() throws NoSuchAlgorithmException, KeyStoreException, KeyManagementException {
        HttpComponentsClientHttpRequestFactory requestFactory = new HttpComponentsClientHttpRequestFactory();
        //默认值为true，当post或者put大文件的时候会造成内存溢出情况，设置为false将数据直接流入底层HttpURLConnection
        requestFactory.setBufferRequestBody(false);
        requestFactory.setHttpClient(httpProxyClient());
        requestFactory.setConnectTimeout((int) Duration.ofSeconds(5).toMillis());
        return requestFactory;
    }

    public HttpClient httpClient() throws NoSuchAlgorithmException, KeyStoreException, KeyManagementException {
        Registry<ConnectionSocketFactory> registry = RegistryBuilder.<ConnectionSocketFactory>create()
                .register("http", PlainConnectionSocketFactory.getSocketFactory())
                .register("https", SSLConnectionSocketFactory.getSocketFactory())
                .build();

        PoolingHttpClientConnectionManager connectionManager = new PoolingHttpClientConnectionManager(registry);
        connectionManager.setMaxTotal(httpPoolProperties.getMaxTotal());
        connectionManager.setDefaultMaxPerRoute(httpPoolProperties.getDefaultMaxPerRoute());
        connectionManager.setValidateAfterInactivity(httpPoolProperties.getValidateAfterInactivity());
        RequestConfig requestConfig = RequestConfig.custom()
                .setSocketTimeout(httpPoolProperties.getSocketTimeout()) //服务器返回数据(response)的时间，超过抛出read timeout
                .setConnectTimeout(httpPoolProperties.getConnectTimeout()) //连接上服务器(握手成功)的时间，超出抛出connect timeout
                .setConnectionRequestTimeout(httpPoolProperties.getConnectionRequestTimeout())//从连接池中获取连接的超时时间，超时间未拿到可用连接，会抛出org.apache.http.conn.ConnectionPoolTimeoutException: Timeout waiting for connection from pool
                .build();

        SSLContextBuilder builder = new SSLContextBuilder();
        builder.loadTrustMaterial(null, new TrustSelfSignedStrategy());
        SSLConnectionSocketFactory sslConnectionSocketFactory = new SSLConnectionSocketFactory(builder.build(), NoopHostnameVerifier.INSTANCE);

        return HttpClients.custom()
                .setSSLHostnameVerifier(new NoopHostnameVerifier())
                .setSSLSocketFactory(sslConnectionSocketFactory)
//				.setDefaultCredentialsProvider(credsProvider)
                .setDefaultRequestConfig(requestConfig)
                .setRedirectStrategy(new LaxRedirectStrategy())
                .setConnectionManager(connectionManager)
                .build();
    }

    public HttpClient httpProxyClient() throws NoSuchAlgorithmException, KeyStoreException, KeyManagementException {
        Registry<ConnectionSocketFactory> registry = RegistryBuilder.<ConnectionSocketFactory>create()
                .register("http", PlainConnectionSocketFactory.getSocketFactory())
                .register("https", SSLConnectionSocketFactory.getSocketFactory())
                .build();

        PoolingHttpClientConnectionManager connectionManager = new PoolingHttpClientConnectionManager(registry);
        connectionManager.setMaxTotal(httpPoolProperties.getMaxTotal());
        connectionManager.setDefaultMaxPerRoute(httpPoolProperties.getDefaultMaxPerRoute());
        connectionManager.setValidateAfterInactivity(httpPoolProperties.getValidateAfterInactivity());
        RequestConfig requestConfig = RequestConfig.custom()
                .setSocketTimeout(httpPoolProperties.getSocketTimeout()) //服务器返回数据(response)的时间，超过抛出read timeout
                .setConnectTimeout(httpPoolProperties.getConnectTimeout()) //连接上服务器(握手成功)的时间，超出抛出connect timeout
                .setConnectionRequestTimeout(httpPoolProperties.getConnectionRequestTimeout())//从连接池中获取连接的超时时间，超时间未拿到可用连接，会抛出org.apache.http.conn.ConnectionPoolTimeoutException: Timeout waiting for connection from pool
                .build();

        SSLContextBuilder builder = new SSLContextBuilder();
        builder.loadTrustMaterial(null, new TrustSelfSignedStrategy());
        SSLConnectionSocketFactory sslConnectionSocketFactory = new SSLConnectionSocketFactory(builder.build(), NoopHostnameVerifier.INSTANCE);
        //代理认证
        CredentialsProvider credentialsProvider = new BasicCredentialsProvider();
        credentialsProvider.setCredentials(new AuthScope(systemInProxyProperties.getHost(), systemInProxyProperties.getPort()), new UsernamePasswordCredentials(systemInProxyProperties.getUsername(), systemInProxyProperties.getPassword()));
        HttpHost proxy = new HttpHost(systemInProxyProperties.getHost(), systemInProxyProperties.getPort());

        return HttpClients.custom()
                .setSSLHostnameVerifier(new NoopHostnameVerifier())
                .setSSLSocketFactory(sslConnectionSocketFactory)
                .setProxy(proxy)
                .setDefaultCredentialsProvider(credentialsProvider)
                .setDefaultRequestConfig(requestConfig)
                .setRedirectStrategy(new LaxRedirectStrategy())
                .setConnectionManager(connectionManager)
                .build();
    }

    @Bean
    public MultipartConfigElement multipartConfigElement() {
        MultipartConfigFactory factory = new MultipartConfigFactory();
        //  单个数据大小
        factory.setMaxFileSize(DataSize.ofMegabytes(maxFileSize));
        /// 总上传数据大小
        factory.setMaxRequestSize(DataSize.ofMegabytes(maxRequestSize));
        return factory.createMultipartConfig();
    }


    @Bean
    public ClientHttpRequestFactory simpleClientHttpRequestFactory() {
        SimpleClientHttpRequestFactory factory = new SimpleClientHttpRequestFactory();
        //请求工厂类是否应用缓冲请求正文内部，默认值为true，
        // 当post或者put大文件的时候会造成内存溢出情况，设置为false将数据直接流入底层HttpURLConnection。
        factory.setBufferRequestBody(false);
        factory.setConnectTimeout(150000);
        factory.setReadTimeout(150000);
        return factory;
    }

}
