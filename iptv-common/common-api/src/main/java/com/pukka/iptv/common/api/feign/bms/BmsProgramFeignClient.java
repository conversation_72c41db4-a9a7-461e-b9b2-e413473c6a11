package com.pukka.iptv.common.api.feign.bms;

import com.pukka.iptv.common.base.vo.CommonResponse;
import com.pukka.iptv.common.core.constant.ServerConstants;
import com.pukka.iptv.common.data.model.bms.BmsContent;
import com.pukka.iptv.common.data.model.bms.BmsProgram;
import com.pukka.iptv.common.data.vo.CodeList;
import com.pukka.iptv.common.data.vo.IdList;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.List;

/**
 * <AUTHOR>
 * @description: 内容bms对象获取接口
 * @create 2021-09-01 17:11
 */
@FeignClient(name = ServerConstants.MANAGE_SERVER, path = "/bmsProgram")
public interface BmsProgramFeignClient {

    @GetMapping("/getById")
    CommonResponse<BmsProgram> getById(@Valid @RequestParam(name = "id", required = true) Long id);

    // 定时发布
    @PostMapping("/programSchedulePublish")
    CommonResponse<Boolean> programSchedulePublish();

    @PutMapping("/cpFeedbackFail")
    CommonResponse<Boolean> cpFeedbackFail(@RequestBody IdList idList);

    @PutMapping("/cpFeedbackSuccess")
    CommonResponse<Boolean> cpFeedbackSuccess(@RequestBody IdList idList);

    @GetMapping("/getSeriesBySubProgramIds")
    CommonResponse<BmsContent> getSeriesBySubProgramIds(@RequestParam(name = "programId") Long programId);

    @PostMapping("/getByProgramId")
    CommonResponse<List<BmsProgram>> getByProgramId(@RequestBody CodeList codeList);
}
