package com.pukka.iptv.common.api.config;

import com.pukka.iptv.common.core.annotation.LoginParam;
import com.pukka.iptv.common.base.enums.CommonResponseEnum;
import com.pukka.iptv.common.base.util.JwtTokenUtil;
import com.pukka.iptv.common.base.vo.CommonResponse;
import com.pukka.iptv.common.base.vo.SecurityUser;
import com.pukka.iptv.common.core.util.ResponseUtil;
import com.pukka.iptv.common.core.util.StringUtils;
import org.springframework.core.MethodParameter;
import org.springframework.web.bind.support.WebDataBinderFactory;
import org.springframework.web.context.request.NativeWebRequest;
import org.springframework.web.method.support.HandlerMethodArgumentResolver;
import org.springframework.web.method.support.ModelAndViewContainer;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;

/**
 * @Author: zhengcl
 * @Date: 2021/10/18 13:01
 */

public class SecurityParameterResolver implements HandlerMethodArgumentResolver {

    @Override
    public boolean supportsParameter(MethodParameter methodParameter) {
        return methodParameter.getParameterType() == SecurityUser.class && methodParameter.hasParameterAnnotation(LoginParam.class);
    }

    @Override
    public Object resolveArgument(MethodParameter methodParameter, ModelAndViewContainer modelAndViewContainer, NativeWebRequest nativeWebRequest, WebDataBinderFactory webDataBinderFactory) throws Exception {
        HttpServletRequest request = nativeWebRequest.getNativeRequest(HttpServletRequest.class);
        if(request==null){
            return null;
        }
        String token = JwtTokenUtil.getTokenByHttpServletRequest(request);
        HttpServletResponse response = nativeWebRequest.getNativeResponse(HttpServletResponse.class);
        if(response==null){
            return null;
        }
        if (!StringUtils.hasLength(token)) {
            loginFailureResponse(response);
            return null;
        }
        SecurityUser securityUser = JwtTokenUtil.getSecurityUser(token);
        if (securityUser == null) {
            loginFailureResponse(response);
            return null;
        }
        return securityUser;
    }

    private void loginFailureResponse(HttpServletResponse response) throws IOException {
        ResponseUtil.out(response, CommonResponse.general(CommonResponseEnum.USER_NOT_LOGIN));
    }
}
