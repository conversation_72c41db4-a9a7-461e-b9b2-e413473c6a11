package com.pukka.iptv.common.api.feign.sys;

import com.pukka.iptv.common.core.constant.ServerConstants;
import com.pukka.iptv.common.data.model.sys.SysTenant;
import com.pukka.iptv.common.base.vo.CommonResponse;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.List;

/**
 * @Author: zhengcl
 * @Date: 2021/8/12 21:35
 */

@FeignClient(name = ServerConstants.MANAGE_SERVER,path = "/sysTenant")
public interface SysTenantFeignClient {

    @GetMapping("/listByUserId")
    CommonResponse<List<SysTenant>> listByUserId(@RequestParam(name = "userId", required = true) Long userId);

}
