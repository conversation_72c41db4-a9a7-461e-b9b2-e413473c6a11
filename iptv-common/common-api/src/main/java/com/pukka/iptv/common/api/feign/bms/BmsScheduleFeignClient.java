package com.pukka.iptv.common.api.feign.bms;

import com.pukka.iptv.common.base.vo.CommonResponse;
import com.pukka.iptv.common.core.constant.ServerConstants;
import com.pukka.iptv.common.data.model.bms.BmsSchedule;
import com.pukka.iptv.common.data.model.cms.CmsSchedule;
import com.pukka.iptv.common.data.vo.CodeList;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

import javax.validation.Valid;
import java.util.List;

/**
 * <AUTHOR>
 * @description: 节目单bms对象获取接口
 * @create 2021-09-01 17:11
 */
@FeignClient(name = ServerConstants.MANAGE_SERVER, path = "/bmsSchedule")
public interface BmsScheduleFeignClient
{

    @GetMapping("/getById")
    CommonResponse<BmsSchedule> getById(@Valid @RequestParam(name = "id", required = true) Long id);

    @GetMapping("/getTiming")
    CommonResponse<List<BmsSchedule>> getTiming();

    @PostMapping("/getByCodeList")
    CommonResponse<List<BmsSchedule>> getByCodeList(@RequestBody CodeList codeList);
}
