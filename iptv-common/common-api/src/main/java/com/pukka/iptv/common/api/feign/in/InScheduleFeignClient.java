package com.pukka.iptv.common.api.feign.in;

import com.pukka.iptv.common.base.vo.CommonResponse;
import com.pukka.iptv.common.core.constant.ServerConstants;
import com.pukka.iptv.common.data.model.in.InSchedule;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestParam;

import javax.validation.Valid;
import java.util.List;

/**
 * <AUTHOR>
 * @Description TODO
 * @Date 2024/8/30 10:11
 * @Version V1.0
 **/
@FeignClient(name = ServerConstants.MANAGE_SERVER, path = "/inSchedule")
public interface InScheduleFeignClient {

    @GetMapping("/getScheduleInfoByCorrelateId")
    CommonResponse<List<InSchedule>> getScheduleInfoByCorrelateId(@Valid @RequestParam(name = "correlateId", required = true) String correlateId);
}
