package com.pukka.iptv.common.api.feign.bms;

import com.pukka.iptv.common.base.vo.CommonResponse;
import com.pukka.iptv.common.core.constant.ServerConstants;
import com.pukka.iptv.common.data.model.bms.BmsPackageContent;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestParam;

import javax.validation.Valid;
import java.util.List;

/**
 * 产品包内容
 * <AUTHOR>
 * @Verison 1.0
 */
@FeignClient(name = ServerConstants.MANAGE_SERVER,path = "/bmsPackageContent")
public interface BmsPackageContentFeignClient {
    @GetMapping("/getByPackageContentId")
    public CommonResponse<List<BmsPackageContent>> getByPackageContentId(@Valid @RequestParam(name = "id", required = true)  Long id);

    // 定时发布
    @PostMapping("/schedulePublish")
    CommonResponse<Boolean> packageContentSchedulePublish();
}
