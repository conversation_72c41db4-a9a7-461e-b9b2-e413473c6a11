package com.pukka.iptv.common.api.feign.copyright;

import com.pukka.iptv.common.base.vo.CommonResponse;
import com.pukka.iptv.common.core.constant.ServerConstants;
import com.pukka.iptv.common.data.model.cms.CmsProgram;
import com.pukka.iptv.common.data.model.cms.CmsSeries;
import com.pukka.iptv.common.data.vo.cms.CmsProgramVO;
import com.pukka.iptv.common.data.vo.cms.CmsSeriesVO;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

/**
 * @Author: chiron
 * @Date: 2022/08/08/15:55
 * @Description:
 */
@FeignClient(name = ServerConstants.MANAGE_SERVER, path = "/ruleProhibitController")
public interface RuleProhibitFeignClient {

    @PostMapping("/ruleProhibitProgram")
    CommonResponse<CmsProgram> ruleProhibitProgram(@RequestBody CmsProgramVO cmsProgram);

    @PostMapping("/ruleProhibitSeries")
    CommonResponse<CmsSeries> ruleProhibitSeries(@RequestBody CmsSeriesVO cmsSeries);
}
