package com.pukka.iptv.common.api.handler;

/**
 * @title:
 * @author: zhengcl
 * @date: 2021/7/23 14:22
 */

/*@Component
@Slf4j
public class FeignFallbackFactory<T> implements FallbackFactory<T> {
    private static final ThrowableAnalyzer THROWABLE_ANALYZER = new ThrowableAnalyzer();
    @Override
    public T create(Throwable cause) {
        return createFallbackService(cause);
    }

    private T createFallbackService(Throwable cause) {
        Throwable[] causeChain = THROWABLE_ANALYZER.determineCauseChain(cause);
        RetryableException ase1 = (RetryableException) THROWABLE_ANALYZER.getFirstThrowableOfType(RetryableException.class, causeChain);
        log.error("服务出错了", cause);
        if (ase1 != null) {
            return toResponse("服务[{}]接口调用超时！", ase1.request(),cause);
        }
        FeignException ase2 = (FeignException) THROWABLE_ANALYZER.getFirstThrowableOfType(FeignException.class, causeChain);
        if (ase2 != null) {
            return toResponse("服务[{}]接口调用出错了！", ase2.request(),cause);
        }
        // 创建一个JDK代理类
        return ProxyUtil.newProxyInstance((proxy, method, args) -> CommonResponse.commonfail(HttpStatus.INTERNAL_SERVER_ERROR,cause.getMessage(),cause), cause.getClass());
    }

    private T toResponse(String respMsg, Request request, Throwable cause) {
        Target<?> target = request.requestTemplate().feignTarget();
        Class<?> targetClazz = target.type();
        CommonResponse.commonfail(HttpStatus.INTERNAL_SERVER_ERROR,getFormatResponse(respMsg, target.name()),respMsg);
        String serviceName = target.name();
        return ProxyUtil.newProxyInstance((proxy, method, args) -> CommonResponse.commonfail(HttpStatus.INTERNAL_SERVER_ERROR,getFormatResponse(respMsg, serviceName),cause), targetClazz);
    }
    private String getFormatResponse(String respMsg, String serviceName){
        JSONObject o = new JSONObject();
        o.put("response",respMsg);
        o.put("service",serviceName);
        return o.toString();
    }


}*/
