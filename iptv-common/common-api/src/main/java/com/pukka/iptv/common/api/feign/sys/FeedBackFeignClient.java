package com.pukka.iptv.common.api.feign.sys;

import com.pukka.iptv.common.base.vo.CommonResponse;
import com.pukka.iptv.common.core.constant.ServerConstants;
import com.pukka.iptv.common.data.dto.PublishParamsDto;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

import javax.validation.Valid;

/**
 * 下发反馈
 */
@FeignClient(name = ServerConstants.MANAGE_SERVER,path = "/feedback")
public interface FeedBackFeignClient {
    /**
     * 下发反馈
     * @param params
     * @return
     */
    @PostMapping("/report")
    public CommonResponse<Boolean> feedback(@Valid @RequestBody PublishParamsDto params);

    /**
     * 自动发布 c2out 内容反馈接口
     * @param correlateid
     * @param spids
     * @param result
     * @return
     */
    @PostMapping("/autoPublishReport")
    CommonResponse<Boolean> autoPublishFeedback(@Valid @RequestParam(name = "correlateid", required = true) String correlateid,
                                                @Valid @RequestParam(name = "spids", required = true) String spids,
                                                @Valid @RequestParam(name = "result", required = true) Integer result,
                                                @Valid @RequestParam(name = "isFinish", required = true) boolean isFinish);
}
