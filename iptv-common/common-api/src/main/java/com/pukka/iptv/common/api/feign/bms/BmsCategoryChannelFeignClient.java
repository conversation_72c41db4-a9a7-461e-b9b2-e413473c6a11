package com.pukka.iptv.common.api.feign.bms;

import com.pukka.iptv.common.core.constant.ServerConstants;
import com.pukka.iptv.common.data.model.bms.BmsCategoryChannel;
import com.pukka.iptv.common.base.vo.CommonResponse;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestParam;

import javax.validation.Valid;
import java.util.List;

/**
 * <AUTHOR>
 * @Date 2021/9/7 10:48 上午
 * @description: 栏目频道
 * @Version 1.0
 */
@FeignClient(name = ServerConstants.MANAGE_SERVER,path = "/bmsCategoryChannel")
public interface BmsCategoryChannelFeignClient {
    @GetMapping("/getByCategoryChanelId" )
    CommonResponse<List<BmsCategoryChannel>> getByCategoryChanelId(@Valid @RequestParam(name = "id", required = true)  Long id);
}
