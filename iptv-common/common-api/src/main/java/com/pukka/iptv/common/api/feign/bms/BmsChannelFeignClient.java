package com.pukka.iptv.common.api.feign.bms;

import com.pukka.iptv.common.base.vo.CommonResponse;
import com.pukka.iptv.common.core.constant.ServerConstants;
import com.pukka.iptv.common.data.model.bms.BmsChannel;
import com.pukka.iptv.common.data.model.bms.BmsContent;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestParam;

import javax.validation.Valid;

/**
 * <AUTHOR>
 * @description: 频道bms对象获取接口
 * @create 2021-09-01 17:11
 */
@FeignClient(name = ServerConstants.MANAGE_SERVER,path = "/bmsChannel")
public interface BmsChannelFeignClient {

    @GetMapping("/getById")
    CommonResponse<BmsChannel> getById(@Valid @RequestParam(name = "id", required = true) Long id);

}
