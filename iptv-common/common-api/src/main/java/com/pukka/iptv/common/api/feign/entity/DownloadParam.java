package com.pukka.iptv.common.api.feign.entity;

import java.util.Date;
import lombok.Data;
import com.fasterxml.jackson.annotation.JsonFormat;

/**
 * <AUTHOR>
 * @Date 2021/8/31 16:36
 * @Description
 */
@Data
public class DownloadParam {
    private String sourceFtpUrl;
    private Integer fileType;
    private String priority;
    private String targetFtpUrl;
    private String notifyUrl;
    private String fileCode;
}
