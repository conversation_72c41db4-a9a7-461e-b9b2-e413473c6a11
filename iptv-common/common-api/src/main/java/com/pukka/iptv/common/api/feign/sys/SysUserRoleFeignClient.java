package com.pukka.iptv.common.api.feign.sys;

import com.pukka.iptv.common.base.vo.CommonResponse;
import com.pukka.iptv.common.core.constant.ServerConstants;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestParam;

import javax.validation.Valid;

/**
 * @Author: zhengcl
 * @Date: 2021/8/11 18:35
 */

@FeignClient(name = ServerConstants.MANAGE_SERVER,path = "/sysUserRole")
public interface SysUserRoleFeignClient {
    @GetMapping("/isAdmin")
    CommonResponse<Boolean> isAdmin(@Valid @RequestParam(name = "userId", required = true) Long userId);

    }
