package com.pukka.iptv.common.api.feign.epg;

import com.pukka.iptv.common.base.vo.CommonResponse;
import com.pukka.iptv.common.core.constant.ServerConstants;
import com.pukka.iptv.common.data.model.EpgIssueOrder;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import javax.validation.Valid;

/**
 * 发布
 * <AUTHOR>
 */
@FeignClient(name = ServerConstants.EPG_API_SERVER, path = "/issue")
public interface IssueOrderFeignClient {
    @PostMapping(value = "/order")
    CommonResponse<Boolean> issueOrder(@RequestBody @Valid EpgIssueOrder issueOrder);
}
