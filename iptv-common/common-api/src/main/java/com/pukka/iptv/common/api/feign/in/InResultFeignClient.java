package com.pukka.iptv.common.api.feign.in;


import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.pukka.iptv.common.base.vo.CommonResponse;
import com.pukka.iptv.common.core.constant.ServerConstants;
import com.pukka.iptv.common.data.model.in.InResult;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;


@FeignClient(name = ServerConstants.JOB_C2IN_EXECUTOR,path = "/inResult")
public interface InResultFeignClient {
    //自动发布，反馈工单状态
    @PostMapping("/updateState")
    public CommonResponse<Boolean> updateByState(@RequestBody InResult inResult);

}
