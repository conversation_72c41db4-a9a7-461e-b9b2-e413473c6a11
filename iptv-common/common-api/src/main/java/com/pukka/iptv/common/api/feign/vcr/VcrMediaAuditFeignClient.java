package com.pukka.iptv.common.api.feign.vcr;

import com.pukka.iptv.common.base.vo.CommonResponse;
import com.pukka.iptv.common.core.constant.ServerConstants;
import com.pukka.iptv.common.data.model.baidu.vcr.VcrAuditSubTaskEntity;
import com.pukka.iptv.common.data.model.baidu.vcr.VcrAuditTransit;
import com.pukka.iptv.common.data.model.baidu.vcr.response.VcrTemplateResponse;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @Description TODO
 * @Date 2024/11/25 09:53
 * @Version V1.0
 **/
@FeignClient(name = ServerConstants.IPTV_BASIC_PLUGINS,path = "/baiduCloudIntelligent/vcrMediaAudit")
public interface VcrMediaAuditFeignClient {

    /**
     * 保存视频审核记录
     *
     * @param vcrAuditTransitEntityList 视频审核记录实体列表
     * @return 保存结果，包含操作是否成功的信息
     */
    @PostMapping("/save")
    CommonResponse<Boolean> save(@RequestBody @Valid List<VcrAuditTransit> vcrAuditTransitEntityList);

    /**
     * 取消审核任务
     *
     * @param taskId 审核任务ID
     * @return 包含操作结果的通用响应对象
     */
    @PutMapping("/cancelAuditTask")
    CommonResponse<Boolean> cancelAuditTask(@Valid @RequestParam(name = "taskId") String taskId);

    /**
     * 根据内容ID获取审核任务
     *
     * @param contentId 内容ID
     * @param contentType 内容类型
     * @return 包含审核任务信息的通用响应对象
     */
    @GetMapping("/getAuditTaskByContentId")
    CommonResponse<VcrAuditSubTaskEntity> getAuditTaskByContentId(@Valid @RequestParam(name = "contentId") String contentId, @Valid @RequestParam(name = "contentType") String contentType) ;

    /**
     * 获取所有模板
     *
     * @return 包含所有模板名称的通用响应对象
     */
    @GetMapping("/getAllTemplates")
    CommonResponse<List<VcrTemplateResponse>> getAllTemplates();


    /**
     * 更新审核状态
     *
     * @param taskIds 审核任务ids
     * @return CommonResponse<Boolean> 包含操作结果的通用响应对象
     */
    @PutMapping("/updateAuditStatus")
    CommonResponse<Boolean> updateAuditStatus(@Valid @RequestParam(name = "taskIds") String taskIds);

    /**
     * 更新审核任务备注信息
     *
     *
     * @param taskId 需要更新备注的审核任务ID，通过@RequestParam注解从请求参数中获取，参数名为"taskId"。
     * @param remark 新的备注信息，通过@RequestParam注解从请求参数中获取，参数名为"remark"。
     * @return 返回一个CommonResponse<Boolean>对象，包含更新操作的结果。如果更新成功，CommonResponse的data字段为true；如果更新失败，为false。
     */
    @PutMapping("/updateAuditRemark")
    CommonResponse<Boolean> updateAuditRemark(@Valid @RequestParam(name = "taskId") String taskId, @Valid @RequestParam(name = "remark") String remark);
}
