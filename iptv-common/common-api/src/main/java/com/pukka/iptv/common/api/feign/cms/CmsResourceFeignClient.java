package com.pukka.iptv.common.api.feign.cms;

import com.pukka.iptv.common.base.vo.CommonResponse;
import com.pukka.iptv.common.core.constant.ServerConstants;
import com.pukka.iptv.common.data.dto.CmsResourceDto;
import com.pukka.iptv.common.data.model.cms.CmsResource;
import com.pukka.iptv.common.data.vo.req.BlobUploadReq;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.List;

/**
 * 下载上报相关接口
 *
 * <AUTHOR>
 * @create 2021-09-14 10:02
 */
@FeignClient(name = ServerConstants.MANAGE_SERVER, path = "/cmsResource")
public interface CmsResourceFeignClient {

    @PostMapping("/blobUpload")
    CommonResponse blobUpload(@RequestBody CmsResource resource);

    @PostMapping("/clientBlobUpload")
    CommonResponse clientBlobUpload(@RequestBody BlobUploadReq blobUploadReq);

    @PostMapping("/deleteByIdsOnlyTS")
    CommonResponse<Boolean> deleteByIdsOnlyTS(@RequestBody CmsResourceDto cmsResourceDto);

    @GetMapping("/deleteByIdsOnlyTSBySelect")
    CommonResponse<Boolean> deleteByIdsOnlyTSBySelect();
}
