package com.pukka.iptv.common.api.feign.sys;

import com.pukka.iptv.common.core.constant.ServerConstants;
import com.pukka.iptv.common.base.vo.CommonResponse;
import com.pukka.iptv.common.base.vo.SecurityUser;
import io.swagger.annotations.ApiOperation;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestParam;

import javax.validation.Valid;

/**
 * @Author: zhengcl
 * @Date: 2021/8/11 18:35
 */

//@RequestMapping(value = "/sysMenu", produces = MediaType.APPLICATION_JSON_VALUE)
@FeignClient(name = ServerConstants.MANAGE_SERVER,path = "/sysUser")
public interface SysUserFeignClient {

    @GetMapping("/getByUsername")
    CommonResponse<SecurityUser> getByUsername(@RequestParam(name = "username", required = true) String username);

    @GetMapping("/login")
    CommonResponse<SecurityUser> login(@RequestParam(name = "username", required = true) String username,@RequestParam(name = "id", required = false) Long id);

    @GetMapping("/logout")
    CommonResponse<SecurityUser> logout(@RequestParam(name = "username", required = true) String username,@RequestParam(name = "id", required = false) Long id);

    @GetMapping("/isExistByUserId")
    CommonResponse<Boolean>  isExistByUserId(@RequestParam(name = "userid", required = true)Long userId);

    @ApiOperation(value = "是否是超级管理员")
    @GetMapping("/isSuperAdmin")
    CommonResponse<Boolean> isSuperAdmin(@Valid @RequestParam(name = "userId", required = true) Long userId);
    }
