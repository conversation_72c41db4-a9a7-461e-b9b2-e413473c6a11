package com.pukka.iptv.common.api.feign.cms;

import com.pukka.iptv.common.base.vo.CommonResponse;
import com.pukka.iptv.common.core.constant.ServerConstants;
import com.pukka.iptv.common.data.dto.CmsChannelDto;
import com.pukka.iptv.common.data.model.cms.CmsChannel;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;

/**
 * <AUTHOR>
 * @description:
 * @create 2021-09-01 17:24
 */
@FeignClient(name = ServerConstants.MANAGE_SERVER, path = "/cmsChannel")
public interface CmsChannelFeignClient {


    @GetMapping("/getById")
    CommonResponse<CmsChannel> getById(@Valid @RequestParam(name = "id", required = true) Long id);

    @PostMapping(value = "/save", consumes = MediaType.APPLICATION_JSON_VALUE)
    CommonResponse save(@RequestBody CmsChannelDto cmsChannelDto);

    @DeleteMapping("/delete")
    CommonResponse delete(@Valid @RequestBody CmsChannelDto cmsChannelDto);

    @PutMapping(value = "/update", consumes = MediaType.APPLICATION_JSON_VALUE)
    CommonResponse updateById(@RequestBody CmsChannelDto cmsChannelDto);
}
