package com.pukka.iptv.common.api.feign.sys;

import com.pukka.iptv.common.base.vo.CommonResponse;
import com.pukka.iptv.common.core.constant.ServerConstants;
import com.pukka.iptv.downloader.model.FileTask;
import com.pukka.iptv.downloader.model.resp.DownloadNotifyResp;
import com.pukka.iptv.downloader.model.resp.R;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

import javax.validation.Valid;
import java.io.IOException;

/**
 * <AUTHOR>
 * @Date 2021/8/31 16:31
 * @Description
 */
@FeignClient(name = ServerConstants.DOWNLOAD_SERVER, path = "/api")
public interface DownloadFeignClient {
    @PostMapping("/addTask")
    CommonResponse<DownloadNotifyResp> save(@RequestBody FileTask fileTask);

    @PostMapping("/file/delete")
    CommonResponse<String> fileDelete(@RequestParam("url") String url, @RequestParam(value = "storeId", required = false) Long storeId);

    @PostMapping("/testCallback")
    R<?> testCallback(@Valid @RequestBody R<DownloadNotifyResp> mediaInfo);
}
