package com.pukka.iptv.common.api.feign.copyright;

import com.pukka.iptv.common.base.vo.CommonResponse;
import com.pukka.iptv.common.core.constant.ServerConstants;
import com.pukka.iptv.common.data.model.copyright.RuleProhibitSchedule;
import javax.validation.Valid;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import java.util.List;
import org.springframework.web.bind.annotation.RequestParam;

/**
 * @Author: chiron
 * @Date: 2023/11/22/15:55
 * @Description:
 */
@FeignClient(name = ServerConstants.MANAGE_SERVER, path = "/ruleProhibitSchedule")
public interface RuleProhibitScheduleFeignClient {

    @GetMapping("/getByChannelCode")
    CommonResponse<List<RuleProhibitSchedule>> getByChannelCode(@Valid @RequestParam(name = "channelCode", required = true)String channelCode);
}
