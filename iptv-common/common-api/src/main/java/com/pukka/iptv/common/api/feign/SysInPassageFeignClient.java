package com.pukka.iptv.common.api.feign;

import com.pukka.iptv.common.api.fallback.SysMenuFeignFallbackFactory;
import com.pukka.iptv.common.base.vo.CommonResponse;
import com.pukka.iptv.common.core.constant.ServerConstants;
import com.pukka.iptv.common.data.model.sys.SysInPassage;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestParam;

import javax.validation.Valid;

/**
 * @ClassName SysInPassageFeignClient
 * @Description TODO
 * <AUTHOR>
 * @Date 2021/9/17 15:05
 * @Version
 */
@FeignClient(name = ServerConstants.MANAGE_SERVER,path = "/sysInPassage")
public interface SysInPassageFeignClient {

    @GetMapping("/getByName")
    CommonResponse<SysInPassage> getByName(@Valid @RequestParam(name = "name", required = true)  String name);
}
