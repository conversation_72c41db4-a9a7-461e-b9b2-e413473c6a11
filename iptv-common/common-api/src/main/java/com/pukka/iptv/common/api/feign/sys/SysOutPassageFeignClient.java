package com.pukka.iptv.common.api.feign.sys;

import com.pukka.iptv.common.base.vo.CommonResponse;
import com.pukka.iptv.common.core.constant.ServerConstants;
import com.pukka.iptv.common.data.model.sys.SysOutPassage;
import com.pukka.iptv.common.data.model.sys.SysSp;
import io.swagger.annotations.ApiOperation;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

import javax.validation.Valid;

/**
 * 分发通道
 */
@FeignClient(name = ServerConstants.MANAGE_SERVER,path = "/sysOutPassage")
public interface SysOutPassageFeignClient {
    /**
     * 获取通道信息
     * @param id
     * @return
     */
    @GetMapping("/getById")
    public CommonResponse<SysOutPassage> getById(@Valid @RequestParam(name = "id", required = true)  Long id);
    /**
     * 获取通道信息
     * @param lspId
     * @return
     */
    @GetMapping("/getByLspId")
    public CommonResponse<SysOutPassage> getByLspId(@Valid @RequestParam(name = "lspId", required = true)  String lspId);
}
