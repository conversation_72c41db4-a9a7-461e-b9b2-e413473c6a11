package com.pukka.iptv.common.api.feign.vcr;

import com.pukka.iptv.common.base.vo.CommonResponse;
import com.pukka.iptv.common.core.constant.ServerConstants;
import com.pukka.iptv.common.data.model.baidu.vcr.VcrAuditTaskEntity;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;

import javax.validation.Valid;

/**
 * <AUTHOR>
 * @Description TODO
 * @Date 2024/11/20 14:08
 * @Version V1.0
 **/
@FeignClient(name = ServerConstants.IPTV_BASIC_PLUGINS,path = "/baiduCloudIntelligent/vcrAuditTask")
public interface VcrAuditTaskFeignClient {
    /**
     * 更新视频审核任务
     *
     * @PutMapping("/update")：定义一个处理HTTP PUT请求的映射，用于更新资源
     * @param vcrAuditTask：视频审核任务实体，通过请求体接收，并使用@Valid注解进行校验
     * @return CommonResponse<Boolean>：返回一个包含更新结果的CommonResponse对象
     *         - 如果更新成功，则CommonResponse中的data字段为true
     *         - 如果更新失败，则可能返回相应的错误信息
     */
    @PutMapping("/update")
    CommonResponse<Boolean> update(@Valid @RequestBody VcrAuditTaskEntity vcrAuditTask);
}
