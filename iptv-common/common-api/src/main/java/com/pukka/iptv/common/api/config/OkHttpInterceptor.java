package com.pukka.iptv.common.api.config;

import lombok.extern.slf4j.Slf4j;
import okhttp3.Interceptor;
import okhttp3.Request;
import okhttp3.Response;
import okhttp3.ResponseBody;
import org.springframework.util.StringUtils;

import java.io.IOException;

/**
 * @Author: zhengcl
 * @Date: 2021/11/25 15:14
 */

@Slf4j
public class OkHttpInterceptor implements Interceptor {
    @Override
    public Response intercept(Chain chain) throws IOException {
        Request originRequest = chain.request();
        Response response = null;
        if (StringUtils.hasLength(originRequest.header("Accept-Encoding"))) {
            Request request = originRequest.newBuilder().removeHeader("Accept-Encoding").build();

            long doTime = System.nanoTime();
            response = chain.proceed(request);
            long currentTime = System.nanoTime();
            if(response != null) {
                ResponseBody responseBody = response.peekBody(1024 * 1024);
                log.info(String.format("接收响应: [%s] %n返回json:【%s】 %.1fms%n%s",
                        response.request().url(),
                        responseBody.string(),
                        (currentTime - doTime) / 1e6d,
                        response.headers()));
            }else {
                String encodedPath = originRequest.url().encodedPath();
                log.info(String.format("接收响应: [%s] %n %.1fms%n",
                        encodedPath,
                        (currentTime - doTime) / 1e6d));
            }
        }
        return response;
    }
}