package com.pukka.iptv.common.api.feign.in;

import com.pukka.iptv.common.base.vo.CommonResponse;
import com.pukka.iptv.common.core.constant.ServerConstants;
import com.pukka.iptv.common.data.model.order.SubOrderXmlEntity;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestParam;

/**
 * @author: chiron
 * Date: 2022/1/18 5:05 PM
 * Description:
 */
@FeignClient(name = ServerConstants.JOB_C2IN_EXECUTOR,path = "/auto")
public interface AutoOutInOrderFeignClient {
    @GetMapping("/getOrderXmlByCorrelateId")
    CommonResponse<String> getOrderXmlEntity(@RequestParam(value = "correlateId") String correlateId) ;

    @GetMapping("/getOrderXmlByCorrelateIdSpId")
    CommonResponse<String> getOrderXmlEntity(@RequestParam(value = "correlateId") String correlateId, @RequestParam(value = "spId") String spId);
}
