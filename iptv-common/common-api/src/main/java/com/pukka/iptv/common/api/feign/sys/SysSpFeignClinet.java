package com.pukka.iptv.common.api.feign.sys;

import com.pukka.iptv.common.base.vo.CommonResponse;
import com.pukka.iptv.common.core.constant.ServerConstants;
import com.pukka.iptv.common.data.model.sys.SysSp;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestParam;

import javax.validation.Valid;

/**
 * sysSp
 */
@FeignClient(name = ServerConstants.MANAGE_SERVER,path = "/sysSp")
public interface SysSpFeignClinet {
    /**
     * 获取sp信息
     * @param id
     * @return
     */
    @GetMapping("/getById")
    public CommonResponse<SysSp> getById(@Valid @RequestParam(name = "id", required = true)  Long id);
}
