package com.pukka.iptv.common.api.feign.cms;

import com.pukka.iptv.common.core.constant.ServerConstants;
import com.pukka.iptv.common.data.model.cms.CmsPicture;
import com.pukka.iptv.common.base.vo.CommonResponse;
import io.swagger.annotations.ApiOperation;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.List;

/**
 * <AUTHOR>
 * @Date 2021/9/2 10:07 上午
 * @description: 图片
 * @Version 1.0
 */
@FeignClient(name = ServerConstants.MANAGE_SERVER, path = "/cmsPicture")
public interface CmsPictureFeignClient {
    @GetMapping("/getById")
    CommonResponse<CmsPicture> getById(@Valid @RequestParam(name = "id", required = true)  Long id);


    @GetMapping("/getByContentIdContentType")
    CommonResponse<List<CmsPicture>> getByContentIdContentType(@Valid @RequestParam(name = "contentId", required = true)  Long contentId, @Valid @RequestParam(name = "contentType", required = true)  Integer contentType);

    @PostMapping(value = "/save", consumes = MediaType.APPLICATION_JSON_VALUE)
    public CommonResponse<Boolean> save(@Valid @RequestBody CmsPicture cmsPicture);

    @PutMapping(value = "/update" ,consumes = MediaType.APPLICATION_JSON_VALUE)
    public CommonResponse<Boolean> updateById(@Valid @RequestBody CmsPicture cmsPicture);

    @DeleteMapping("/deleteById")
    public CommonResponse<Boolean> delPicture(@RequestBody CmsPicture cmsPicture);

    @GetMapping("/getByCode")
    CommonResponse<CmsPicture> getByCode(@Valid @RequestParam(name = "code", required = true) String code);

}
