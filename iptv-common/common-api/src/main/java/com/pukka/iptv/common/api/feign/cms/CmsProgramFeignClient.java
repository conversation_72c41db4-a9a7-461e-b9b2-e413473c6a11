package com.pukka.iptv.common.api.feign.cms;

import com.pukka.iptv.common.base.vo.CommonResponse;
import com.pukka.iptv.common.core.constant.ServerConstants;
import com.pukka.iptv.common.data.dto.CmsProgramDto;
import com.pukka.iptv.common.data.model.cms.CmsMovie;
import com.pukka.iptv.common.data.model.cms.CmsProgram;
import com.pukka.iptv.common.data.model.statistics.StatisticsIn;
import com.pukka.iptv.common.data.model.statistics.StatisticsInAgain;
import com.pukka.iptv.common.data.model.statistics.StatisticsInCheck;
import com.pukka.iptv.common.data.model.statistics.StatisticsInFinal;
import com.pukka.iptv.common.data.vo.req.StatisticsInVo;
import feign.Request;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.List;

/**
 * <AUTHOR>
 * @description:
 * @create 2021-09-01 17:24
 */
@FeignClient(name = ServerConstants.MANAGE_SERVER, path = "/cmsProgram")
public interface CmsProgramFeignClient {


    @GetMapping("/getById")
    CommonResponse<CmsProgram> getById(@Valid @RequestParam(name = "id", required = true) Long id);

    @PostMapping(value = "/subset/save",consumes = MediaType.APPLICATION_JSON_VALUE)
    CommonResponse subsetSave(@RequestBody CmsProgramDto cmsProgramDto);

    @DeleteMapping("/subset/deleteByIds")
    CommonResponse subsetDeleteByIds(@RequestBody CmsProgramDto cmsProgramDto);

    @PutMapping(value = "/subset/updateWithoutMov")
    CommonResponse subsetUpdate(@RequestBody CmsProgramDto cmsProgram);

    @PostMapping(value = "/simpleset/save" )
    CommonResponse simpleSetSave(@RequestBody CmsProgramDto cmsProgramDto);

    @PutMapping(value = "/simpleset/updateWithoutMov")
    CommonResponse simpleSetUpdate(@RequestBody CmsProgramDto cmsProgram);

    @GetMapping("/relateRelease")
    public CommonResponse<Boolean> relateRelease(@RequestParam("relateCount") Integer relateCount);

    @GetMapping("/relatePreview")
    public CommonResponse<Boolean> relatePreview(@RequestParam("relateCount") Integer relateCount);

    @PostMapping("/programStatistic")
    public CommonResponse<List<StatisticsIn>> programStatistic(@RequestBody StatisticsInVo statisticsInVo,  Request.Options options);

    @PostMapping("/subStatistic")
    public CommonResponse<List<StatisticsIn>> subStatistic(@RequestBody StatisticsInVo statisticsInVo,  Request.Options options);

    @PostMapping("/subAndProgramStatistic")
    public CommonResponse<List<StatisticsIn>> subAndProgramStatistic(@RequestBody  StatisticsInVo statisticsInVo, Request.Options options);

    @PostMapping("/selfProgramStatistic")
    public CommonResponse<List<StatisticsInCheck>> selfProgramStatistic(@RequestBody StatisticsInVo statisticsInVo, Request.Options options);

    @PostMapping("/selfSubStatistic")
    public CommonResponse<List<StatisticsInCheck>> selfSubStatistic(@RequestBody StatisticsInVo statisticsInVo, Request.Options options);

    @PostMapping("/finalProgramStatistic")
    public CommonResponse<List<StatisticsInFinal>> finalProgramStatistic(@RequestBody StatisticsInVo statisticsInVo, Request.Options options);

    @PostMapping("/finalSubStatistic")
    public CommonResponse<List<StatisticsInFinal>> finalSubStatistic(@RequestBody StatisticsInVo statisticsInVo, Request.Options options);

    @PostMapping("/againProgramStatistic")
    public CommonResponse<List<StatisticsInAgain>> againProgramStatistic(@RequestBody StatisticsInVo statisticsInVo, Request.Options options);

    @PostMapping("/againSubStatistic")
    public CommonResponse<List<StatisticsInAgain>> againSubStatistic(@RequestBody StatisticsInVo statisticsInVo, Request.Options options);

    @PutMapping("/updateCmsProgramStatus")
    public CommonResponse<Boolean> updateCmsProgramStatus(@RequestBody List<CmsProgram> cmsProgramList);
}
