package com.pukka.iptv.common.api.feign.bms;

import com.pukka.iptv.common.base.vo.CommonResponse;
import com.pukka.iptv.common.core.constant.ServerConstants;
import com.pukka.iptv.common.data.model.bms.BmsPhysicalChannel;
import com.pukka.iptv.common.data.vo.bms.BmsPhysicalChannelQueryVO;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

import javax.validation.Valid;
import java.util.List;

/**
 * <AUTHOR>
 * @description: 物理频道bms对象获取接口
 * @create 2021-09-01 17:11
 */
@FeignClient(name = ServerConstants.MANAGE_SERVER,path = "/bmsPhysicalChannel")
public interface BmsPhysicalChannelFeignClient {

    @GetMapping("/getById")
    CommonResponse<BmsPhysicalChannel> getById(@Valid @RequestParam(name = "id", required = true) Long id);

    @PostMapping("/getPhysicalChannels")
    CommonResponse<List<BmsPhysicalChannel>> findPhysicalBychannelIds(@RequestBody BmsPhysicalChannelQueryVO physicalChannelQueryVO);
}
