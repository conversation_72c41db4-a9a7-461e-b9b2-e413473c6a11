package com.pukka.iptv.common.core.exception.enums;

import com.pukka.iptv.common.core.exception.enums.abs.AbstractBaseExceptionEnum;
import com.pukka.iptv.common.core.factory.ExpEnumCodeFactory;

/**
 * 认证相关的异常的枚举
 * <p>
 * 认证和鉴权的区别：
 * <p>
 * 认证可以证明你能登录系统，认证的过程是校验token的过程
 * 鉴权可以证明你有系统的哪些权限，鉴权的过程是校验角色是否包含某些接口的权限
 *
 * <AUTHOR>
 * @date 2021/7/18 22:22
 */
public enum AuthorizationExceptionEnum implements AbstractBaseExceptionEnum {

    /**
     * 没有访问权限
     */
    NO_VISIT_AUTHORIZE(201, "没有访问权限");

    private final Integer code;

    private final String message;

    AuthorizationExceptionEnum(Integer code, String message) {
        this.code = code;
        this.message = message;
    }

    @Override
    public Integer getCode() {
        return ExpEnumCodeFactory.getExpEnumCode(this.getClass(), code);
    }

    @Override
    public String getMessage() {
        return message;
    }

}
