package com.pukka.iptv.common.core.util;

import com.pukka.iptv.common.proxy.config.EpgOutProxyProperties;
import com.pukka.iptv.common.proxy.config.SystemOutProxyProperties;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.net.ftp.*;
import org.springframework.util.StringUtils;

import java.io.*;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.StringTokenizer;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * FtpUtil工具类
 *
 * <AUTHOR>
 * @date 2021/1/12 0012 下午 21:13
 */
@Slf4j
public class FtpUtil {


    private FTPClient ftpClient;
    private String ipAddress;
    private int ipPort;
    private String userName;
    private String passWord;
    private static final String ENCODING = "utf-8";
    /**
     * 连接超时，可理解为调用connect（）方法的超时。默认为10秒
     */
    private static final int CONNECT_TIMEOUT = 10 * 1000;
    /**
     * 数据超时，是一个读超时。即从Ftp读数据的超时限制。默认4个小时
     */
    private static final int DATA_TIMEOUT = 4 * 60 * 60 * 1000;

    /**
     * 读取数据时阻塞链路的超时时间。默认10秒
     */
    private static final int SO_TIMEOUT = 10 * 1000;

    /**
     * 通过Ip地址、端口、用户名和密码初始化Ftp工具
     *
     * @param ip       String 机器IP
     * @param port     String 机器FTP端口号
     * @param username String FTP用户名
     * @param password String FTP密码
     */
    public FtpUtil(String ip, String port, String username, String password) {
        init(ip, port, username, password);
    }

    /**
     * 通过Ip地址、用户名和密码初始化Ftp工具
     *
     * @param ip       String 机器IP，默认端口为21
     * @param username String FTP用户名
     * @param password String FTP密码
     */
    public FtpUtil(String ip, String username, String password) {
        init(ip, "21", username, password);
    }

    /**
     * 通过ftpUrl初始化Ftp工具
     *
     * @param ftpUrl ***************************************
     */
    /**
     * "ftp://xstore:iptv!#$xs@**************:6069/xstore/Out/Distribute/PhysicalChannel/655043218924220416";
     * values[0]    xstore
     * values[1]    iptv!#$xs
     * values[2]    **************
     * values[3]    6069
     * values[4]    xstore/Out/Distribute/PhysicalChannel/655043218924220416
     */
    public FtpUtil(String ftpUrl, boolean proxy, SystemOutProxyProperties systemOutProxyProperties) {
//        Map<String, String> map = UrlUtil.parseFtp(ftpUrl);
        log.info("FtpUtil===>>>使用代理？{}", proxy);
        if (proxy) {
            init(systemOutProxyProperties);
        } else {
            String[] arr = UrlUtil.getSingleMatchValue(ftpUrl);
            init(arr[2], arr[3], arr[0], arr[1]);
        }
    }

    /**
     * 通过ftpUrl初始化Ftp工具
     * @param ftpUrl
     * @param proxy
     * @param epgOutProxyProperties
     */
    public FtpUtil(String ftpUrl, boolean proxy, EpgOutProxyProperties epgOutProxyProperties) {
        log.info("epg FtpUtil===>>>使用代理？{}", proxy);
        if (proxy) {
            init(epgOutProxyProperties);
        } else {
            String[] arr = UrlUtil.getSingleMatchValue(ftpUrl);
            init(arr[2], arr[3], arr[0], arr[1]);
        }
    }

    /**
     * 获取ftp内容
     *
     * @param ftpurl
     * @return
     */
    public String getFtpContent(String ftpurl) {
        String str = null;
        try {
            Map<String, String> map = UrlUtil.parseFtp(ftpurl);
            ipAddress = map.get("ipAddress");
            log.info("getFtpContent======>>>>map.get(ipPort)================{}", map.get("ipPort"));
            ipPort = SafeUtil.getInt(map.get("ipPort"), 21);
            userName = map.get("userName");
            passWord = map.get("passWord");
            log.info("ftp ipAdress=" + ipAddress + ",ipPort=" + ipPort + ",userName=" + userName + ",passWord=" + passWord);
            login();
            String path = map.get("path");
            path = path.replace("//", "/"); //防止双杠导致目录失败
            //log.info("ftp path="+path);
            String changePath = path.substring(0, path.lastIndexOf("/") + 1);
            log.info("ftp changePath=" + changePath);
            //先试全部目录
            boolean re = ftpClient.changeWorkingDirectory(changePath);
            log.info("ftp re " + re);
            //如果整体不能取，分开取
            if (!re) {
                String[] tempChangePath = changePath.split("/");
                for (int i = 1; i < tempChangePath.length; i++) {
                    re = ftpClient.changeWorkingDirectory(tempChangePath[i]);
                    if (!re) {
                        log.error("changeWorkingDirectory is fail!");
                        return str;
                    }
                }
            }
            String fileStr = path.replace(changePath, "");
            log.info("ftp fileStr=" + fileStr);
            InputStream in = ftpClient.retrieveFileStream(fileStr);
            BufferedReader br = new BufferedReader(new InputStreamReader(in, "utf-8"));
            String data = null;
            StringBuffer resultBuffer = new StringBuffer();
            while ((data = br.readLine()) != null) {
                resultBuffer.append(data + "\n");
            }
            str = resultBuffer.toString();
            log.info("ftp content:" + str);
            logout();
        } catch (Exception e) {
            log.error("ftp error:" + e.getMessage(), e);
        }finally {
            try {
                logout();
            } catch (Exception e) {
                log.error("ftp error:", e);
            }
        }
        return str;
    }

    /**
     * Ftp工具初始化方法
     *
     * @param ip       Ip地址
     * @param port     端口
     * @param username 名称
     * @param password 密码
     */
    private void init(String ip, String port, String username, String password) {
        ipAddress = ip;
        if (StringUtils.hasLength(port)) {
            ipPort = Integer.parseInt(port);
        } else {
            ipPort = 21;
        }
        ftpClient = new FTPClient();
        userName = username;
        passWord = password;
    }

    /**
     * Ftp工具初始化方法，使用代理
     *
     */
    private void init(SystemOutProxyProperties systemOutProxyProperties) {
        String host = systemOutProxyProperties.getHost();
        Integer port = systemOutProxyProperties.getPort();
        String username = systemOutProxyProperties.getUsername();
        String password = systemOutProxyProperties.getPassword();

        log.info("使用代理===================>>>");
        log.info("systemProxyProperties.getHost()：{}",host);
        log.info("systemProxyProperties.getPort()：{}",port);
        log.info("systemProxyProperties.getUsername()：{}",username);
        log.info("systemProxyProperties.getPassword()：{}",password);


        ftpClient = new FTPHTTPClient(host, port, username, password);
    }

    /**
     * Ftp工具初始化方法，使用代理
     * @param epgOutProxyProperties
     */
    private void init(EpgOutProxyProperties epgOutProxyProperties) {
        String host = epgOutProxyProperties.getHost();
        Integer port = epgOutProxyProperties.getPort();
        String username = epgOutProxyProperties.getUsername();
        String password = epgOutProxyProperties.getPassword();

        log.info("使用代理===================>>>");
        log.info("epgOutProxyProperties.getHost()：{}",host);
        log.info("epgOutProxyProperties.getPort()：{}",port);
        log.info("epgOutProxyProperties.getUsername()：{}",username);
        log.info("epgOutProxyProperties.getPassword()：{}",password);


        ftpClient = new FTPHTTPClient(host, port, username, password);
    }

    /**
     * 登录FTP服务器
     *
     * @throws Exception 通用异常
     */
    public void login() throws Exception {
        log.info("login()");
        //登陆初始化
        this.loginInit();
        ftpClient.enterLocalPassiveMode();

        int reply = ftpClient.getReplyCode();
        if (!FTPReply.isPositiveCompletion(reply)) {
            logout();
            log.error("login().服务器拒绝连接，返回码：" + reply);
        }
    }

    /**
     * 登陆初始化
     *
     * @throws IOException 普通异常
     */
    private void loginInit() throws IOException {
        //链接超时时间=10s
        ftpClient.setConnectTimeout(CONNECT_TIMEOUT);
        //登陆FTP服务器
        if(!ftpClient.isConnected()){
            // 连接
            ftpClient.connect(ipAddress, ipPort);
        }
        //数据读取超时时间=4个小时
        ftpClient.setDataTimeout(DATA_TIMEOUT);
        //等待Socket链接响应时间=10秒
        ftpClient.setSoTimeout(SO_TIMEOUT);

        ftpClient.login(userName, passWord);

        //设置编码=utf-8
        ftpClient.setControlEncoding(ENCODING);
        //设置文件类型（二进制）
        ftpClient.setFileType(FTP.BINARY_FILE_TYPE);
        ftpClient.setBufferSize(1024);
    }

    /**
     * 登录FTP服务器
     *
     * @param isPassiveMode 模式
     * @throws IOException IO异常
     */
    public void login(boolean isPassiveMode) throws IOException {
        log.info("login(isPassiveMode),isPassiveMode=" + isPassiveMode);
        //登陆初始化
        this.loginInit();

        if (!isPassiveMode) {
            ftpClient.enterLocalActiveMode();//主动模式
            log.info("======enterLocalActiveMode   succcess=========");
        } else {
            ftpClient.enterLocalPassiveMode();//被动模式
            log.info("======enterLocalPassiveMode   succcess=========");
        }

        int reply = ftpClient.getReplyCode();
        if (!FTPReply.isPositiveCompletion(reply)) {
            logout();
            log.error("login().服务器拒绝连接，返回码：" + reply);
        }
    }

    /**
     * 退出FTP服务器
     *
     * @throws IOException IO异常
     */
    public void logout() throws IOException {
        log.info("logout");
        if (ftpClient != null && ftpClient.isConnected()) {
            ftpClient.logout();
            ftpClient.disconnect();
        }
    }

    /**
     * 构建目录
     * <p>
     * 描述：在FTP服务器上建立指定的目录,当目录已经存在的情下不会影响目录下的文件,这样用以判断FTP
     * 上传文件时保证目录的存在目录格式必须以"/"根目录开头
     *
     * @param pathList String
     */
    public void buildList(String pathList) {
        try {
            log.info("---->buildList.pathList=" + pathList);
            login();
            StringTokenizer s = new StringTokenizer(pathList, "/"); // sign
            String pathName = "";
            String tempdir = "";

            while (s.hasMoreElements()) {
                tempdir = utf8toiso8859(String.valueOf(s.nextElement()));
                pathName = utf8toiso8859((pathName + "/" + tempdir));
                log.info("----buildList.pathList=" + pathList + ",changeWorkingDirectory=" + tempdir);
                boolean flag = ftpClient.changeWorkingDirectory(tempdir);
                if (!flag) {
                    log.info("----buildList.pathList=" + pathList + ",makeDirectory=" + tempdir);
                    boolean b = ftpClient.makeDirectory(tempdir);
                    log.info("----buildList.pathList=" + pathList + ",makeDirectory=" + tempdir + ",mkdir ret=" + b);
                    ftpClient.changeWorkingDirectory(tempdir);
                }
            }
        } catch (Exception e) {
            log.error("<----buildList.exception={}", e);
        } finally {
            log.info("<----buildList.pathList=" + pathList);
        }
    }

    public void doBuildList(String pathList) throws IOException {
        // sign
        StringTokenizer s = new StringTokenizer(pathList, "/");
        String pathName = "";
        String tempdir = "";
        while (s.hasMoreElements()) {
            tempdir = utf8toiso8859((String) s.nextElement());
            pathName = utf8toiso8859((pathName + "/" + tempdir));
            boolean flag = ftpClient.changeWorkingDirectory(tempdir);
            if (!flag) {
                ftpClient.makeDirectory(tempdir);
                ftpClient.changeWorkingDirectory(tempdir);
            }
        }
    }


    /**
     * 上传文件到FTP服务器,destination路径以FTP服务器的"/"开始，带文件名、 上传文件只能使用二进制模式，当文件存在时再次上传则会覆盖
     *
     * @param source      String
     * @param destination String
     * @throws Exception
     */
    public String upFile(String source, String destination, String rootUrl) {
        String url = "";
        FileInputStream ftpIn = null;
        try {
            login();
            String name = destination;
            if (destination.contains("/")) {
                String temp = destination.substring(0, destination.lastIndexOf("/"));
                boolean changeDir = ftpClient.changeWorkingDirectory(temp);
                if (!changeDir) {
                    doBuildList(temp);
                }
                name = destination.substring(destination.lastIndexOf("/") + 1);
            }
            ftpIn = new FileInputStream(new File(source));
            boolean flag = ftpClient.storeFile(name, ftpIn);
            log.info("store file success:" + flag + ",ftpclient reply string:" + ftpClient.getReplyString());

            if (flag) {
                url = rootUrl + destination;
            }
        } catch (Exception e) {
            e.printStackTrace();
        } finally {
            try {
                ftpIn.close();
                logout();
            } catch (Exception e) {
                e.printStackTrace();
            }
        }
        return url;
    }

    public String upFile(InputStream input, String destination, String rootUrl) {
        String url = "";
        try {
            login();
            String name = destination;
            if (destination.contains("/")) {
                String temp = destination.substring(0, destination.lastIndexOf("/"));
                String[] split = temp.split("/");
                for (String s : split) {
                    boolean changeDir = ftpClient.changeWorkingDirectory(s);
                    if (!changeDir) {
                        doBuildList(s);
                    }
                }
				/*String temp = destination.substring(0, destination.lastIndexOf("/"));
				boolean changeDir = ftpClient.changeWorkingDirectory(temp);
				if(!changeDir){
					doBuildList(temp);
				}*/
                name = destination.substring(destination.lastIndexOf("/") + 1);
            }
            boolean flag = ftpClient.storeFile(name, input);
            if (flag) {
                url = rootUrl + destination;
            }
        } catch (Exception e) {
            e.printStackTrace();
            log.error(e.getMessage(),e.fillInStackTrace());
        } finally {
            try {
                logout();
            } catch (Exception e) {
                e.printStackTrace();
            }
        }
        return url;
    }

    /**
     * JSP中的流上传到FTP服务器, 上传文件只能使用二进制模式，当文件存在时再次上传则会覆盖 字节数组做为文件的输入流,此方法适用于JSP中通过
     * request输入流来直接上传文件在RequestUpload类中调用了此方法， destination路径以FTP服务器的"/"开始，带文件名
     *
     * @param sourceData  byte[]
     * @param destination String
     * @throws Exception
     */
    public String upFile(byte[] sourceData, String destination, String rootUrl) {
        String url = "";
        InputStream ftpIn = null;
        try {
            login();
            String name = destination;
            if (destination.contains("/")) {
                String temp = destination.substring(0, destination.lastIndexOf("/"));
                boolean changeDir = ftpClient.changeWorkingDirectory(temp);
                if (!changeDir) {
                    doBuildList(temp);
                }
                name = destination.substring(destination.lastIndexOf("/") + 1);
            }
            ftpIn = new ByteArrayInputStream(sourceData);
            boolean flag = ftpClient.storeFile(name, ftpIn);
            log.info("store file success:" + flag + ",ftpclient reply string:" + ftpClient.getReplyString());

            if (flag) {
                url = rootUrl + destination;
            }
        } catch (Exception e) {
            e.printStackTrace();
        } finally {
            try {
                ftpIn.close();
                logout();
            } catch (Exception e) {
                e.printStackTrace();
            }
        }
        return url;
    }

    public String upFile(File file, String destination, String rootUrl) {
        String url = "";
        FileInputStream ftpIn = null;
        try {
            login();
            String name = destination;
            if (destination.contains("/")) {
                String temp = destination.substring(0, destination.lastIndexOf("/"));
                boolean changeDir = ftpClient.changeWorkingDirectory(temp);
                if (!changeDir) {
                    doBuildList(temp);
                }
                name = destination.substring(destination.lastIndexOf("/") + 1);
            }
            ftpIn = new FileInputStream(file);
            boolean flag = ftpClient.storeFile(name, ftpIn);
            log.info("store file success:" + flag + ",ftpclient reply string:" + ftpClient.getReplyString());

            if (flag) {
                url = rootUrl + destination;
            }
        } catch (Exception e) {
            e.printStackTrace();
        } finally {
            try {
                ftpIn.close();
                logout();
            } catch (Exception e) {
                e.printStackTrace();
            }
        }
        return url;
    }

    /**
     * 从FTP文件服务器上下载文件SourceFileName，到本地destinationFileName 所有的文件名中都要求包括完整的路径名在内
     *
     * @param SourceFileName      String
     * @param destinationFileName String
     * @throws Exception
     */
    public void downFile(String SourceFileName, String destinationFileName) {
        FileOutputStream fos = null;
        try {
            login();
            fos = new FileOutputStream(destinationFileName);
            String sourceTemp = utf8toiso8859(SourceFileName);
            boolean flag = ftpClient.retrieveFile(sourceTemp, fos);
            log.info("down file success:" + flag + ",ftpclient reply string:" + ftpClient.getReplyString());
            ;
        } catch (Exception e) {
            e.printStackTrace();
        } finally {
            try {
                fos.close();
                logout();
            } catch (Exception e) {
                e.printStackTrace();
            }
        }
    }


    /**
     * 从FTP文件服务器上下载文件，输出到字节数组中
     *
     * @param SourceFileName String
     * @return byte[]
     * @throws Exception
     */
    public byte[] downFile(String SourceFileName) throws Exception {
        SourceFileName = checkName(SourceFileName);
        ByteArrayOutputStream byteOut = null;
        String params = "";
        byte[] ret = null;
        boolean b = false;
        try {
            params = "downFile.SourceFileName=" + SafeUtil.getString(SourceFileName);
            log.info("---->" + params);
            byteOut = new ByteArrayOutputStream();
            b = ftpClient.retrieveFile(SourceFileName, byteOut);
            params += ",retrieveFile=" + b;
            byteOut.close();
            ret = byteOut.toByteArray();

        } catch (IOException e) {
            log.error("", e);

        } finally {
            if (ret != null) {
                params += ",byte[].length=" + ret.length;
            }
            log.info("<-----" + params);
        }

        return ret;
    }

    /**
     * ftp流传输浏览器
     * @param remotePath
     * @param out
     * @return
     */
    public Boolean streamTrans(String remotePath, OutputStream out) {
        remotePath = checkName(remotePath);
        InputStream inputStream = null;
        BufferedInputStream bufferedInputStream = null;
        try {

            byte[] buf = new byte[1024];
            int len = 0;
            // 验证FTP服务器是否登录成功
            int replyCode = ftpClient.getReplyCode();
            if (!FTPReply.isPositiveCompletion(replyCode)) {
                log.warn("ftpServer refused connection, replyCode:{}", replyCode);
                return false;
            }
            inputStream = ftpClient.retrieveFileStream(remotePath);
            bufferedInputStream = new BufferedInputStream(inputStream);
            while (bufferedInputStream != null && (len = bufferedInputStream.read(buf)) > 0) {
                out.write(buf, 0, len);
            }
            out.flush();
            out.close();
            return true;
        } catch (Exception exception) {
            log.error("streamTrans -----> retrieve file failure!,错误信息:{}", exception);
            return false;
        } finally {
            if (bufferedInputStream != null) {
                try {
                    bufferedInputStream.close();
                } catch (IOException e) {
                    log.error("streamTrans -----> 关闭buffer流失败,错误信息:{}", e);
                }
            }
            if (inputStream != null) {
                try {
                    inputStream.close();
                } catch (IOException e) {
                    log.error("streamTrans -----> 关闭inputStream流失败,错误信息:{}", e);
                }
            }
        }
    }

    public void upFile(byte[] sourceData, String destination) throws Exception {
        int len = 0;
        if (sourceData != null) {
            len = sourceData.length;
        }
        log.info("---->upFile.sourceData.length=" + len
                + ",destination=" + SafeUtil.getString(destination));
        destination = checkName(destination);
        if (destination.split("/").length > 2) {
            if (!isExistFilePath(destination.substring(0, destination.lastIndexOf('/')))) {
                buildList(destination.substring(0, destination.lastIndexOf('/')));
            }
        }

        InputStream ftpIn = new ByteArrayInputStream(sourceData);
        log.info("--upFile.storeFile...");
        boolean b = ftpClient.storeFile(destination, ftpIn);
        log.info("<----upFile.sourceData.length=" + len
                + ",destination=" + SafeUtil.getString(destination)
                + ",storeFile=" + b);
    }

    public boolean uploadFile(byte[] sourceData, String destination) throws Exception {
        boolean ret = false;
        int len = 0;
        if (sourceData != null) {
            len = sourceData.length;
        }
        log.info("---->upFile.sourceData.length=" + len
                + ",destination=" + SafeUtil.getString(destination));
        destination = checkName(destination);
        if (destination.split("/").length > 2) {
            if (!isExistFilePath(destination.substring(0, destination.lastIndexOf('/')))) {
                buildList(destination.substring(0, destination.lastIndexOf('/')));
            }
        }

        InputStream ftpIn = new ByteArrayInputStream(sourceData);
        log.info("--upFile.storeFile...");
        ret = ftpClient.storeFile(destination, ftpIn);
        log.info("<----upFile.sourceData.length=" + len
                + ",destination=" + SafeUtil.getString(destination)
                + ",storeFile=" + ret);
        return ret;
    }

    /**
     * 取得指定目录下的所有文件名，不包括目录名称
     * 分析nameList得到的输入流中的数，得到指定目录下的所有文件名
     *
     * @param fullPath String
     * @return ArrayList
     * @throws Exception
     */
    public boolean isExistFilePath(String fullPath) throws Exception {
        String parentPath = fullPath.substring(0, fullPath.lastIndexOf('/'));
        String pathName = fullPath.substring(fullPath.lastIndexOf('/') + 1);
        parentPath = checkName(parentPath);

        ArrayList namesList = getDirs(parentPath);
        return namesList.contains(pathName);
    }

    /**
     * 取得指定目录下的所有文件名，不包括目录名称
     * 分析nameList得到的输入流中的数，得到指定目录下的所有文件名
     *
     * @param fullPath String
     * @return ArrayList
     * @throws Exception
     */
    public ArrayList getDirs(String fullPath) throws Exception {
        fullPath = checkName(fullPath);
        ArrayList namesList = new ArrayList();
        FTPFile[] names = ftpClient.listDirectories(fullPath);
        log.info("---------------------------------------names.length={}", names.length);
        if (names != null) {
            for (int i = 0; i < names.length; i++) {
                namesList.add(names[i].getName());
            }
        }
        return namesList;
    }

    public List<String> listFile(String path) throws Exception {
        path = checkName(path);
        List<String> namesList = new ArrayList<>();
        ftpClient.changeWorkingDirectory(path);
        FTPFile[] names = ftpClient.listFiles();
        log.info("---------------------------------------names.length={}", names.length);
        for (FTPFile name : names) {
            namesList.add(name.getName());
        }
        return namesList;
    }

    public static String checkName(String name) {
        if (!name.startsWith("/")) {
            name = "/" + name;
        }
        return name;
    }

    public ArrayList<String> arFiles = new ArrayList<>();

    public void List(String pathName, String ext) throws IOException {
        if (pathName.startsWith("/") && pathName.endsWith("/")) {
            String directory = pathName;
            //更换目录到当前目录
            if (this.ftpClient.changeWorkingDirectory(directory)) {
                FTPFile[] files = this.ftpClient.listFiles();
                for (FTPFile file : files) {
                    if (file.getName().equalsIgnoreCase(".") || file.getName().equalsIgnoreCase("..")) {
                        continue;
                    }
                    if (file.isFile()) {
                        if (file.getName().endsWith(ext)) {
                            arFiles.add(directory + file.getName());
                        }
                    } else if (file.isDirectory()) {
                        List(directory + file.getName() + "/", ext);
                    }
                }
            } else {
                log.error("--List.fail to changeWorkingDirectory.directory=" + SafeUtil.getString(pathName));
            }
        } else {
            log.error("--List.invalid path.pathName=" + SafeUtil.getString(pathName));
        }
    }

    /**
     * 转码[utf-8 -> ISO-8859-1] 不同的平台需要不同的转码     FTP协议传输的是iso-8859-1 根据FTP服务器配置的编码转成对应的iso-8859-1
     *
     * @param obj
     * @return ""
     */
    public String utf8toiso8859(Object obj) {
        try {
            if (obj != null) {
                return new String(obj.toString().getBytes(ENCODING), "iso-8859-1");
            }
        } catch (Exception e) {
            return "";
        }
        return "";
    }

    /**
     * 删除一个文件。ftp url: ftp://user:pwd@ip:port/a/b/c/1.txt
     * ftpClient外部登录，外部登出
     *
     * @param filename 1.txt
     * @param relPath  /a/b/c
     * @return true or false
     */
    public boolean deleteFile(String filename, String relPath) {
        boolean flag = true;
        try {
            flag = ftpClient.changeWorkingDirectory(relPath);
            if (flag) {
                flag = ftpClient.deleteFile(utf8toiso8859(filename));
            }
            if (log.isInfoEnabled()) {
                log.info("delete file " + filename + " success:" + flag + ",ftpclient reply string:" + ftpClient.getReplyString());
            }
        } catch (Exception e) {
            log.error("delete file exception", e);
        }
        return flag;
    }

    /**
     * 删除一个文件
     */
    public boolean doDeleteFile(String filename) {
        boolean flag = true;
        try {
            flag = ftpClient.deleteFile(utf8toiso8859(filename));
            log.info("delete file " + filename + " success:" + flag + ",ftpclient reply string:" + ftpClient.getReplyString());
        } catch (Exception e) {
            e.printStackTrace();
        }
        return flag;
    }

    /**
     * 删除目录
     */
    public Boolean deleteDirectory(String pathname) {
        boolean flag = true;
        try {
            login();
            if (ftpClient.changeWorkingDirectory(utf8toiso8859(pathname))) {
                FTPFile[] files = ftpClient.listFiles();
                for (FTPFile file : files) {
                    //System.out.println(file.getName());
                    //windows 系统做ftp服务器 会出现获取文件列表二外获取到.和 .. 两个目录
                    if (file.isDirectory() && !".".equals(file.getName()) && !"..".equals(file.getName())) {
                        doDeleteDirectory(pathname + file.getName() + "/");
                        //doDeleteDirectory(file.getName());
                        //System.out.println("del dir "+file.getName());
                    } else {
                        doDeleteFile(pathname + file.getName());
                        //System.out.println("del "+file.getName());
                    }
                }
            }
            //System.out.println("del dir "+pathname);

            flag = ftpClient.removeDirectory(utf8toiso8859(pathname));
            log.info("delete file " + pathname + " success:" + flag + ",ftpclient reply string:" + ftpClient.getReplyString());
        } catch (Exception e) {
            e.printStackTrace();
        } finally {
            try {
                logout();
            } catch (Exception e) {
                e.printStackTrace();
            }
        }
        return flag;
    }

    /**
     * 删除目录
     */
    public Boolean doDeleteDirectory(String pathname) {
        boolean flag = true;
        try {
            if (ftpClient.changeWorkingDirectory(utf8toiso8859(pathname))) {
                FTPFile[] files = ftpClient.listFiles();
                for (FTPFile file : files) {
                    //System.out.println(file.getName());
                    if (file.isDirectory() && !".".equals(file.getName()) && !"..".equals(file.getName())) {
                        doDeleteDirectory(pathname + file.getName() + "/");
                        //doDeleteDirectory(file.getName());
                        //System.out.println("del dir "+file.getName());
                    } else {
                        doDeleteFile(pathname + file.getName());
                        //System.out.println("del "+file.getName());
                    }
                }
            }
            flag = ftpClient.removeDirectory(utf8toiso8859(pathname));
            log.info("delete file " + pathname + " success:" + flag + ",ftpclient reply string:" + ftpClient.getReplyString());
        } catch (Exception e) {
            e.printStackTrace();
        }
        return flag;
    }

    private static final String FTP_HEADER = "ftp://";

    public static String getPath(String ftpUrl) {
        int index = ftpUrl.indexOf('/', FTP_HEADER.length() + 1);
        if (index >= 0) {
            return ftpUrl.substring(index);
        }
        return "/";
    }

    /**
     * get path size
     *
     * @param ftpUrl ftp url
     * @return the size of file or directory
     */
    public long getSize(String ftpUrl) {
        try {
            login();
            String path = getPath(ftpUrl);
            log.info("path  size=============" + utf8toiso8859(path));
            FTPFile[] files = ftpClient.listFiles(utf8toiso8859(path));
            if (files == null || files.length == 0) {

                log.info("path  size2=============" + path);
                files = ftpClient.listFiles(path);
                if (files == null || files.length == 0) {
                    return -1;
                }
            }

            long ret = 0;
            for (FTPFile f : files) {
                ret += f.getSize();
            }
            return ret;
        } catch (Exception e) {
            log.error("get size", e);
        } finally {
            try {
                logout();
            } catch (Exception e) {
                log.error("log out", e);
            }
        }
        return -1;
    }


    public int fileexist(String ftpUrl) {
        try {
            login();
            String path = getPath(ftpUrl);
            log.info("path  size=============" + utf8toiso8859(path));
            FTPFile[] files = ftpClient.listFiles(utf8toiso8859(path));
            if (files == null || files.length == 0) {
                return 2;
            }

            return 1;
        } catch (Exception e) {
            log.error("get size", e);
        } finally {
            try {
                logout();
            } catch (Exception e) {
                log.error("log out", e);
            }
        }
        return 2;
    }

    /*********************************************************/
    public boolean upFile(String localFilePath, String remoteFilePath) {
        if (!remoteFilePath.startsWith("/")) {
            return false;
        }
        FileInputStream ftpIn = null;
        if (remoteFilePath.indexOf('/', 1) > 0) {
            makeSureDirExist(remoteFilePath.substring(0, remoteFilePath.lastIndexOf("/")));
        }

        try {
            login();
            ftpIn = new FileInputStream(new File(localFilePath));
            boolean flag = ftpClient.storeFile(remoteFilePath, ftpIn);
            log.info("upload file success:" + flag + ",ftpclient reply string:"
                    + ftpClient.getReplyString());
            return flag;
        } catch (Exception e) {
            log.error("upload failed", e);
        } finally {
            try {
                if (ftpIn != null) {
                    ftpIn.close();
                }
            } catch (Exception e) {
                log.error("close file error", e);
            }

            try {
                logout();
            } catch (Exception e) {
                log.error("log out error", e);
            }
        }
        return false;
    }

    private void makeSureDirExist(String dir) {
        mkdirs(dir);
    }


    public void mkdirs(String remotePath) {
        if (remotePath.startsWith("/")) {
            remotePath = remotePath.substring(1);
        }
        try {
            login();
            int index = 0, nindex;
            while ((nindex = remotePath.indexOf('/', index)) > 0) {
                ftpClient.makeDirectory(remotePath.substring(0, nindex));
                index = nindex + 1;
            }
            ftpClient.makeDirectory(remotePath);
        } catch (Exception e) {
            log.error("mkdir error:" + remotePath, e);
        } finally {
            try {
                logout();
            } catch (Exception e) {
                log.error("log out", e);
            }
        }
    }

    /**
     * 从FTP文件服务器上下载文件SourceFileName，到本地destinationFileName 所有的文件名中都要求包括完整的路径名在内
     *
     * @param sourceFileName      String
     * @param destinationFileName String
     * @throws Exception
     */
    public boolean downFile2(String sourceFileName, String destinationFileName) {
        FileOutputStream fos = null;
        try {
            login();
            fos = new FileOutputStream(destinationFileName);
            boolean flag = ftpClient.retrieveFile(sourceFileName, fos);
            log.info("down file success:" + flag + ",ftpclient reply string:" + ftpClient
                    .getReplyString());
            return flag;
        } catch (Exception e) {
            log.error("", e);
        } finally {
            try {
                if (fos != null) {
                    fos.close();
                }
            } catch (Exception e) {
                log.error("close file error", e);
            }
            try {
                logout();
            } catch (Exception e) {
                log.error("logout error", e);
            }
        }
        return false;
    }

    public long getFileSize(String fullFilePath) {
        try {
            FTPListParseEngine engine = ftpClient.initiateListParsing(fullFilePath);
            while (engine.hasNext()) {
                FTPFile[] files = engine.getNext(5);
                for (int i = 0; i < files.length; i++) {
                    long size = files[i].getSize();
                    return size;
                }
            }
        } catch (IOException e) {
            log.error("getFileSize exp.", e);
            // TODO Auto-generated catch block
            e.printStackTrace();

        }
        return 0L;
    }


    /**
     * @param ftpRelPath  ftp相对路径
     * @param filePath    本地完整路径
     * @param offset      偏移量下载，若小于等于0，那么完整下载；否则断点下载;
     * @param ftpfilezise 服务器上文件大小，若小于等于0，那么不做文件大小校验；
     * @return 0 success,others fail.
     */
    public int swpk_downFileStream(String ftpRelPath, String filePath, long offset, long ftpfilezise) {
        int tmpret = -1;//0代表成功，其他失败

        File tmpfile = null;

        Long tmplocalfilesize = 0L;
        Long tmpftpfilesize = 0L;

        InputStream tmpstream = null;
        Integer tmpbuffersize = 1024;
        byte[] buffer = null;
        RandomAccessFile tmprandfile = null;

        long tmpadjustoffset = offset;//调整后的偏移量

        try {
            log.info("-->swpk_downFile.ftpRelPath=" + SafeUtil.getString(ftpRelPath) + ",filePath=" + SafeUtil.getString(filePath) + ",offset=" + offset + ",ftpfilezise=" + ftpfilezise);

            ftpRelPath = checkName(ftpRelPath);
            tmpftpfilesize = ftpfilezise;

            tmpfile = new File(filePath);
            if (tmpfile.exists()) {//文件存在
                if (tmpftpfilesize > 0) {
                    tmplocalfilesize = tmpfile.length();
                    if (tmplocalfilesize > tmpftpfilesize) {//若本地文件大于FTP上的文件大小，那么重新下载
                        log.info("--swpk_downFile.localfilesize > ftpfilesize,reset adjustoffset=0.");
                        tmpadjustoffset = 0;
                        if (!tmpfile.delete()) {
                            log.error("--swpk_downFile.fail to del file.filePath=" + SafeUtil.getString(filePath));
                        }
                    } else if (offset > tmpftpfilesize) {//若偏离量大于FTP上文件大小，那么重新下载
                        log.info("--swpk_downFile.offset > ftpfilesize,reset adjustoffset=0.");
                        tmpadjustoffset = 0;
                        if (!tmpfile.delete()) {
                            log.error("--swpk_downFile.fail to del file..filePath=" + SafeUtil.getString(filePath));
                        }
                    } else {
                        tmpadjustoffset = tmplocalfilesize;
                        log.info("--swpk_downFile.reset adjustoffset=" + tmpadjustoffset);
                    }
                }
            } else {
                File tmpParentFile = tmpfile.getParentFile();
                if (!tmpParentFile.exists()) {
                    tmpParentFile.mkdirs();
                }

                tmpadjustoffset = 0;
                log.info("--swpk_downFile.Not found file,reset adjustoffset = 0.filePath=" + SafeUtil.getString(filePath));
            }

            if (tmpadjustoffset > 0) {
                ftpClient.setRestartOffset(tmpadjustoffset);
                log.info("--swpk_downFile.continue downloading,adjustoffset=" + tmpadjustoffset);
            }

            ftpClient.setBufferSize(tmpbuffersize);

            tmpstream = ftpClient.retrieveFileStream(ftpRelPath);

            tmprandfile = new RandomAccessFile(tmpfile, "rw");
            if (tmpadjustoffset > 0) {
                tmprandfile.seek(tmpadjustoffset);
            }

            buffer = new byte[tmpbuffersize];
            int bytecount = 0;
            while ((bytecount = tmpstream.read(buffer)) != -1) {
                tmprandfile.write(buffer, 0, bytecount);
            }

            if (tmpstream != null) {
                tmpstream.close();
            }

            if (!ftpClient.completePendingCommand()) {
                ftpClient.logout();
                ftpClient.disconnect();
            }

            //关闭文件
            try {
                if (tmprandfile != null) {
                    tmprandfile.close();
                    Thread.sleep(500);
                }
            } catch (Exception e2) {
                log.error("--swpk_downFile Exception.tmprandfile.close()", e2);
            }


            long tmplocalfilelength = tmpfile.length();
            if (tmpftpfilesize > 0) {
                //下载文件大小与ftp上校验
                if ((tmplocalfilelength == tmpftpfilesize) && tmplocalfilelength > 0) {
                    tmpret = 0;//文件大小一样，并且大于0，则成功
                } else {
                    tmpret = -1003;
                    log.error("--swpk_downFile.-1003,localfile.length()<>ftpfilezise.localfile.length=" + tmplocalfilelength + ",ftpfilesize=" + tmpftpfilesize);
                    return tmpret;
                }
            } else {
                if (tmplocalfilelength > 0) {
                    tmpret = 0;//文件大小 大于0，则成功
                } else {
                    tmpret = -1004;
                    log.error("--swpk_downFile.-1004,localfile.length()< 0 ");
                    return tmpret;
                }
            }
        } catch (IOException e) {
            tmpret = -9999;
            log.error("--swpk_downFile.-9999,exception.", e);
        } finally {
            try {
                if (tmprandfile != null) {
                    tmprandfile.close();
                }
            } catch (Exception e2) {
                log.error("--swpk_downFile.Exception tmprandfile.close().", e2);
            }

            try {
                if (tmpstream != null) {
                    tmpstream.close();
                }
            } catch (Exception e3) {
                log.error("--swpk_downFile.Exception tmpstream.close().", e3);
            }

            log.info("<--swpk_downFile.ftpRelPath=" + SafeUtil.getString(ftpRelPath) + ",filePath=" + SafeUtil.getString(filePath) + ",offset=" + offset + ",adjustoffset=" + tmpadjustoffset + ",ret=" + tmpret);
        }

        return tmpret;
    }


    public static String getUrl(String userName, String passWord,
                                String host, int port, String path) {
        return FTP_HEADER + userName + ":" + passWord + "@" + host + ":" + port + path;
    }


    public static String getUrl2(String userName, String passWord,
                                 String host, String path) {
        return FTP_HEADER + userName + ":" + passWord + "@" + host + path;
    }

    /**
     * @param str ***************************/
     * @return String[0]=user;
     * String[1]=pwd;
     * String[2]=127.0.0.1
     * ;String[3]=21;
     * String[4]=a/b/c/1.txt;
     * String[5]=/a/b/c;
     * String[6]=1.txt;
     */
    public static String[] getSingleMatchValue(String str) {
        String[] values = null;
        if (str == null) {
            return values;
        }

        String[] strs = str.split("://");
        if (strs == null || strs.length == 0) {
            return values;
        }
        if (strs == null || strs.length < 2 || !strs[0].equalsIgnoreCase("ftp")) {
            return values;
        }
        //values=new String[5];
        values = new String[7];//增加相对路径和文件名  /a/b/c  和 1.txt
        String address = strs[1].replaceAll("//", "/");
        values[0] = address.substring(0, address.indexOf(':'));
        address = address.substring(address.indexOf(':') + 1, address.length());
        String result = "";
        Pattern p = Pattern.compile("@[0-9.:]{4,25}");
        Matcher m = p.matcher(address);
        if (m.find() && m.toMatchResult().group() != null) {
            result = m.toMatchResult().group().trim();
        } else {
            result = "";
        }
        values[1] = address.split(result)[0];
        values[4] = address.split(result)[1];
        values[4] = values[4].substring(values[4].indexOf('/') + 1);
        values[2] = result.substring(result.indexOf('@') + 1);
        if (values[2].contains(":")) {
            String[] temp = values[2].split(":");
            values[2] = temp[0];
            values[3] = temp[1];
        } else {
            values[3] = "21";
        }

        String tmpRelFilePath = values[4]; //  a/b/c/1.txt or 1.txt
        if (StringUtils.hasLength(tmpRelFilePath)) {
            int pos1 = tmpRelFilePath.lastIndexOf("/");
            if (pos1 > -1) {
                String s1 = "/" + tmpRelFilePath.substring(0, pos1);// /a/b/c
                String s2 = tmpRelFilePath.substring(pos1 + 1);// 1.txt
                values[5] = s1;
                values[6] = s2;
            } else {
                values[5] = "/";
                values[6] = tmpRelFilePath;
            }
        }


        return values;
    }

    public static void main(String[] args) {
        //System.out.println(getPath("******************************************/Movie/movie002.avi"));

        System.out.println(getSingleMatchValue("******************************************/bbb/Movie/movie002.avi"));
        System.out.println("");
    }

    public Boolean readFtpStream(String ftpPath, OutputStream out) throws Exception {
        String[] values = getSingleMatchValue(ftpPath);
        String destination = values[4];
        ipAddress = values[2];
        if (StringUtils.hasLength(values[3])) {
            ipPort = Integer.parseInt(values[3]);
        } else {
            ipPort = 21;
        }
        userName = values[0];
        passWord = values[1];
        login();

        return streamTrans(destination, out);
    }
    public FtpUtil(String ftpurl)
            throws Exception {
        Map<String, String> map =UrlUtil.parseFtp(ftpurl);
        init(map.get("ipAddress"),map.get("ipPort"),map.get("userName"),map.get("passWord"));
    }

    public boolean checkFileExists(String fileUrl) {
        boolean flag = true;
        try {
            Map<String, String> map = UrlUtil.parseFtp(fileUrl);
            ipAddress = map.get("ipAddress");
            log.info("getFtpContent======>>>>map.get(ipPort)================{}", map.get("ipPort"));
            ipPort = SafeUtil.getInt(map.get("ipPort"), 21);
            userName = map.get("userName");
            passWord = map.get("passWord");
            log.info("ftp ipAdress=" + ipAddress + ",ipPort=" + ipPort + ",userName=" + userName + ",passWord=" + passWord);
            login();
            // 进入当前用户根目录，方便listNames方法从用户根路径查找文件
            ftpClient.changeWorkingDirectory("/");
            String[] value = getSingleMatchValue(fileUrl);
            String name = checkName(value[4]);
            // 当前文件存在时，会返回文件路径
            String[] listNames = ftpClient.listNames(name);
            flag = listNames.length > 0;
            log.info(flag ? "文件存在" : "文件不存在");
        } catch (Exception e) {
            log.error("ftp error",e);
        }finally{
            try {
                logout();
            } catch (Exception e) {
                log.error("ftp error",e);
            }
        }
        return flag;
    }
    public String upFileByEpg(InputStream input, String destination, String rootUrl) {
        String url = "";
        try {
            login();
            String name = destination;
            if (destination.contains("/")) {
                String temp = destination.substring(0, destination.lastIndexOf("/"));
                String[] split = temp.split("/");
                for (String s : split) {
                    boolean changeDir = ftpClient.changeWorkingDirectory(s);
                    if (!changeDir) {
                        doBuildList(s);
                    }
                }
				/*String temp = destination.substring(0, destination.lastIndexOf("/"));
				boolean changeDir = ftpClient.changeWorkingDirectory(temp);
				if(!changeDir){
					doBuildList(temp);
				}*/
                name = destination.substring(destination.lastIndexOf("/") + 1);
            }
            boolean flag = ftpClient.storeFile(new String(name.getBytes("UTF-8"), "ISO-8859-1"), input);
            if (flag) {
                url = rootUrl + destination;
            }
        } catch (Exception e) {
            e.printStackTrace();
            log.error(e.getMessage(),e.fillInStackTrace());
        } finally {
            try {
                logout();
            } catch (Exception e) {
                e.printStackTrace();
            }
        }
        return url;
    }
}
