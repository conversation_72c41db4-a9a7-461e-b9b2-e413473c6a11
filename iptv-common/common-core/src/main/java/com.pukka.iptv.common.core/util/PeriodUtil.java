package com.pukka.iptv.common.core.util;

import lombok.extern.slf4j.Slf4j;
import org.springframework.util.StringUtils;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.ZoneOffset;
import java.time.format.DateTimeFormatter;
import java.time.temporal.TemporalAdjusters;

/**
 * @title:
 * @author: zhengcl
 * @date: 2021/5/28 16:46
 */
@Slf4j
public abstract class PeriodUtil {

    //获取账单期
    public static String getPeriod(String param) {
        //账单期,默认生成前一个月的
        int billIncomePeriodBefore = 1;
        int res = 0;
        if (StringUtils.hasLength(param)) {
            try {
                res = Integer.parseInt(param);
            } catch (NumberFormatException e) {

                log.error("------param input error,msg:{}-------", e.getCause());
            }
        }
        if (res > 0) {
            billIncomePeriodBefore = res;
        }
        LocalDate now = LocalDate.now();
        LocalDate localDate = now.plusMonths(-billIncomePeriodBefore);
        String format = localDate.format(DateTimeFormatter.ofPattern(DateUtils.YYYYMM));
        return format;
    }

    public static Long getPeriodStartTimestamp(String period) {
        period=period+"-01 02:00:00";
        DateTimeFormatter timeFormatter = DateTimeFormatter.ofPattern(DateUtils.YYYYMMDDHHMMSS);
        LocalDateTime parse = LocalDateTime.parse(period, timeFormatter);
        LocalDateTime start = parse.with(TemporalAdjusters.firstDayOfMonth())
                .withHour(0)
                .withMinute(0)
                .withSecond(0)
                .withNano(0);
        long l = start.toInstant(ZoneOffset.of("+8")).toEpochMilli();
        return l;
    }

    public static Long getPeriodEndTimestamp(String period) {
        period=period+"-01 02:00:00";
        DateTimeFormatter timeFormatter = DateTimeFormatter.ofPattern(DateUtils.YYYYMMDDHHMMSS);
        LocalDateTime parse = LocalDateTime.parse(period, timeFormatter);
        LocalDateTime end = parse.with(TemporalAdjusters.lastDayOfMonth())
                .withHour(23)
                .withMinute(59)
                .withSecond(59);
        long l = end.toInstant(ZoneOffset.of("+8")).toEpochMilli();
        return l;
    }


    public static String getNewPeriod(String period,int offset) {
        period=period+"-01";
        DateTimeFormatter timeFormatter = DateTimeFormatter.ofPattern(DateUtils.YYYYMMDD);
        LocalDate parse = LocalDate.parse(period, timeFormatter);
        LocalDate start = parse.plusMonths(offset);
        DateTimeFormatter dateTimeFormatter = DateTimeFormatter.ofPattern(DateUtils.YYYYMM);
        String format = start.format(dateTimeFormatter);
        return format;
    }


}
