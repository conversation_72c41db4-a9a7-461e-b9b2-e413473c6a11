package com.pukka.iptv.common.core.util.easyexcel;

import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.ExcelReader;
import com.alibaba.excel.ExcelWriter;
import com.alibaba.excel.read.metadata.ReadSheet;
import com.alibaba.excel.write.metadata.WriteSheet;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.io.OutputStream;
import java.net.URLEncoder;
import java.util.List;

/**
 * EasyExcel 工具类
 *
 * @author: zhengcl
 * @date: 2021/2/22 0012 下午 11:28
 */
@Slf4j
public class EasyExcelUtil<T> {
    //用于统计导入数量

    /**
     * 下载模板
     * 文件下载（失败了会返回一个有部分数据的Excel）
     * 直接写，这里注意，finish的时候会自动关闭OutputStream,当然你外面再关闭流问题不大
     * 这里注意 swagger 可能会导致各种问题，请直接用浏览器或者用postman
     *
     * @param response
     * @param fileName
     * @param sheetName
     * @param initData  初始化数据
     * @throws IOException
     */
    public static <T> void exportExcelSingleSheet(HttpServletResponse response, String fileName, String sheetName, Class<T> clz, List<?> initData) {
        response.setContentType("application/vnd.ms-excel");
        response.setCharacterEncoding("utf-8");
        // 这里URLEncoder.encode可以防止中文乱码 当然和easyexcel没有关系
        try {
            fileName = URLEncoder.encode(fileName, "UTF-8").replaceAll("\\+", "%20");
            response.setHeader("Content-disposition", "attachment;filename*=utf-8''" + fileName + ".xlsx");
            EasyExcel.write(response.getOutputStream(), clz).sheet(sheetName).doWrite(initData);
        } catch (Exception e) {

            log.error("-----export data error msg:", e.getMessage());
        }
    }

    public static final ThreadLocal<Integer> countMap = new ThreadLocal<>();

    //多sheet写入
    public static <T> void exportExcelMultiSheet(HttpServletResponse response, String fileName, List<String> sheetNames, List<Class> clzs, List<List<T>> initDatas) {
        response.setContentType("application/vnd.ms-excel");
        response.setCharacterEncoding("utf-8");
        // 这里URLEncoder.encode可以防止中文乱码 当然和easyexcel没有关系
        OutputStream outputStream = null;
        try {
            fileName = URLEncoder.encode(fileName, "UTF-8").replaceAll("\\+", "%20");
            response.setHeader("Content-disposition", "attachment;filename*=utf-8''" + fileName + ".xlsx");
            outputStream = response.getOutputStream();
        } catch (Exception e) {

            log.error("-----encode fileName error msg:", e.getMessage());
        }
        ExcelWriter excelWriter = EasyExcel.write(outputStream).build();
        try {
            for (int i = 0; i < sheetNames.size(); i++) {
                WriteSheet writeSheet = EasyExcel.writerSheet(i, sheetNames.get(i)).head(clzs.get(i)).build();
                excelWriter.write(CollectionUtils.isEmpty(initDatas) ? null : initDatas.get(i), writeSheet);
            }
        } catch (Exception e) {

            log.error("-----import data error msg:", e.getMessage());
        } finally {
            excelWriter.finish();
        }
    }


    /**
     * 获取到ExcelWriter
     *
     * @param response
     * @param fileName
     * @param <T>
     * @return
     */
    public static <T> ExcelWriter getExcelWriter(HttpServletResponse response, String fileName) {
        response.setContentType("application/vnd.ms-excel");
        response.setCharacterEncoding("utf-8");
        // 这里URLEncoder.encode可以防止中文乱码 当然和easyexcel没有关系
        OutputStream outputStream = null;
        try {
            fileName = URLEncoder.encode(fileName, "UTF-8").replaceAll("\\+", "%20");
            response.setHeader("Content-disposition", "attachment;filename*=utf-8''" + fileName + ".xlsx");
            outputStream = response.getOutputStream();
        } catch (Exception e) {

            log.error("-----encode fileName error msg:", e.getMessage());
        }
        ExcelWriter excelWriter = EasyExcel.write(outputStream).build();

        return excelWriter;
    }


    /**
     * 分批量导出,注意此方法流未关闭,和getExcelWriter 一起用于分批量分页导出
     *
     * @param excelWriter
     * @param sheetNames
     * @param clzs
     * @param initDatas
     * @param <T>
     */
    public static <T> void exportexcelmultisheetByBatch(ExcelWriter excelWriter, List<String> sheetNames, List<Class> clzs, List<List<T>> initDatas) {
        for (int i = 0; i < sheetNames.size(); i++) {
            WriteSheet writeSheet = EasyExcel.writerSheet(i, sheetNames.get(i)).head(clzs.get(i)).build();
            excelWriter.write(CollectionUtils.isEmpty(initDatas) ? null : initDatas.get(i), writeSheet);
        }

    }

    /**
     * 分批量导出,注意此方法流未关闭,和getExcelWriter 一起用于分批量分页导出
     *
     * @param excelWriter
     * @param
     * @param clzs
     * @param initDatas
     * @param <T>
     */
    public static <T> void exportSingleSheet(ExcelWriter excelWriter, String sheetName, Class clzs, List<T> initDatas) {
        WriteSheet writeSheet = EasyExcel.writerSheet(0, sheetName).head(clzs).build();
        excelWriter.write(CollectionUtils.isEmpty(initDatas) ? null : initDatas, writeSheet);

    }

    /**
     * 数据导入
     *
     * @param file
     * @param listener
     * @return 成功导入数量
     */
    public static <T> Integer importExcel(MultipartFile file, ExcelListener listener, Class<T> clz) {
        Integer res = null;
        try {
            countMap.set(0);
            //这里 需要指定读用哪个class去读，然后读取第一个sheet 文件流会自动关闭,异步读取,每次读取一行,存5行后一起存入数据库
            EasyExcel.read(file.getInputStream(), clz, listener).sheet().doRead();
            res = countMap.get();
        } catch (IOException e) {
            throw new RuntimeException("导入异常,请稍后再试");
        } finally {
            countMap.remove();
        }
        return res;
    }

    public static Integer importExcel(MultipartFile file, List<ExcelListener> listeners, List<Class> clzs) {
        Integer res = null;
        try {
            countMap.set(0);
            //这里 需要指定读用哪个class去读,异步读取,每次读取一行,存5行后一起存入数据库
            ExcelReader excelReader = EasyExcel.read(file.getInputStream()).build();
            int size = listeners.size();
            ReadSheet[] readSheets = new ReadSheet[size];
            for (int i = 0; i < listeners.size(); i++) {
                ReadSheet readSheet = EasyExcel.readSheet(i).head(clzs.get(i)).registerReadListener(listeners.get(i)).build();
                readSheets[i] = readSheet;
            }
            excelReader.read(readSheets);
            res = countMap.get();
        } catch (IOException e) {
            throw new RuntimeException("导入异常,请稍后再试");
        } finally {
            countMap.remove();
        }
        return res;
    }

    /**
     * 读取csv，保存数据库
     *
     * @param path
     * @return
     */
    public static <T> Integer readAndsaveExcelFile(String path, ExcelListener listener, Class<T> clz) {

        Integer res = null;
        try {
            countMap.set(0);
            //这里 需要指定读用哪个class去读，然后读取第一个sheet 文件流会自动关闭,异步读取,每次读取一行,存5行后一起存入数据库
            EasyExcel.read(path, clz, listener).sheet().doRead();
            res = countMap.get();
        } catch (Exception e) {
            throw new RuntimeException("解析异常,请稍后再试");
        } finally {
            countMap.remove();
        }
        return res;
    }


}
 

