package com.pukka.iptv.common.core.util;

import lombok.extern.slf4j.Slf4j;

import java.io.BufferedReader;
import java.io.File;
import java.io.FileInputStream;
import java.io.InputStreamReader;
import java.util.ArrayList;
import java.util.List;

/**
 *  文件工具类
 * <AUTHOR>
 * @date  2021/1/12 0012 下午 21:13
 */
@Slf4j
public class FileUtil {
    /**
     * 读取文件
     * @param filePath 文件路径
     * @param encoding 编码，默认 Utf-8
     * @return 每行数据放入列表
     */
    public static List<String> readFile(String filePath, String encoding){

        List<String> ret = new ArrayList<>();

        if(encoding == null || "".equalsIgnoreCase(encoding)){
            encoding = "UTF-8";
        }
        BufferedReader br = null;
        try{
            File file = new File(filePath);
            if(file.isFile() && file.exists()) {
                //构造一个BufferedReader类来读取文件
                br = new BufferedReader(new InputStreamReader(new FileInputStream(file), encoding));

                String line = null;
                //使用readLine方法，一次读一行
                while ((line = br.readLine()) != null) {
                    ret.add(line);
                }
            }else{
                log.error("--readFile.file not exist.");
            }
        }catch(Exception e){
            log.error("--readFile Exception.",e);
        }finally {
            if(br!=null){
                try{
                    br.close();
                }catch (Exception e2){
                    log.error("--readFile Exception.BufferedReader.close();",e2);
                }
            }
        }
        return ret;
    }
}
