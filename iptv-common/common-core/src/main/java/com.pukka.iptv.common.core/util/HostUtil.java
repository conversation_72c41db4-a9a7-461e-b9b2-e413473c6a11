package com.pukka.iptv.common.core.util;

import ch.qos.logback.classic.pattern.ClassicConverter;
import ch.qos.logback.classic.spi.ILoggingEvent;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;

import java.net.Inet4Address;
import java.net.InetAddress;
import java.net.NetworkInterface;
import java.util.Enumeration;

/**
 * <AUTHOR>
 * @date 2019/5/14 11:37
 * @description
 */
@Data
@Slf4j
public class HostUtil extends ClassicConverter {
    private static String hostName;

    static {
        try {
            Enumeration<NetworkInterface> allNetInterfaces = NetworkInterface.getNetworkInterfaces();
            while (allNetInterfaces.hasMoreElements()) {
                NetworkInterface netInterface = (NetworkInterface) allNetInterfaces.nextElement();
                Enumeration<InetAddress> addresses = netInterface.getInetAddresses();
                while (addresses.hasMoreElements()) {
                    hostName = InetAddress.getLocalHost().getHostName();
                }
            }
        } catch (Exception e) {

        }
    }


    @Override
    public String convert(ILoggingEvent event) {
        return hostName;
    }
}