package com.pukka.iptv.common.core.util;

import com.google.common.net.UrlEscapers;
import lombok.extern.slf4j.Slf4j;
import org.springframework.util.StringUtils;

import java.net.MalformedURLException;
import java.net.URL;
import java.util.HashMap;
import java.util.Map;
import java.util.regex.Matcher;
import java.util.regex.Pattern;


/**
 *  Url转换解析类
 * <AUTHOR>
 * @date  2021/1/12 0012 下午 21:13
 */
@Slf4j
public class UrlUtil {


	public static Map<String, String> parseFtp(String url) throws MalformedURLException {
		Map<String, String> temp = new HashMap<>(16);
		String temp2  = UrlEscapers.urlFragmentEscaper().escape(url);
		URL urlTemp = new URL(temp2);
		String ipAddress = urlTemp.getHost();
		StringBuilder ipPort= new StringBuilder();
		if (urlTemp.getPort()!=-1) {
			ipPort.append(urlTemp.getPort());
		}
		if(!StringUtils.hasLength(ipPort.toString())){
			ipPort.append(21);
		}
		String path = urlTemp.getPath();
		String userInfo = urlTemp.getUserInfo();
		StringBuilder userName = new StringBuilder();
		StringBuilder userPwd = new StringBuilder();
		if(StringUtils.hasLength(userInfo)){
			String[] array = userInfo.split(":");
			if(array.length>0){
				userName.append(array[0]);
			}
			
			if(array.length>=1){
				userPwd.append(array[1]);
			}
		}
		temp.put("userName", userName.toString());
		temp.put("passWord", percentDecode(userPwd.toString()));
		temp.put("ipAddress", ipAddress);
		temp.put("ipPort", ipPort.toString());
		temp.put("path", path);
		
		return temp;
	}

	public static String percentDecode(String encodeMe) {
		if (encodeMe == null) {
			return "";
		}
		String decoded = encodeMe.replace("%21", "!");
		decoded = decoded.replace("%20", " ");
		decoded = decoded.replace("%23", "#");
		decoded = decoded.replace("%24", "$");
		decoded = decoded.replace("%26", "&");
		decoded = decoded.replace("%27", "'");
		decoded = decoded.replace("%28", "(");
		decoded = decoded.replace("%29", ")");
		decoded = decoded.replace("%2A", "*");
		decoded = decoded.replace("%2B", "+");
		decoded = decoded.replace("%2C", ",");
		decoded = decoded.replace("%2F", "/");
		decoded = decoded.replace("%3A", ":");
		decoded = decoded.replace("%3B", ";");
		decoded = decoded.replace("%3D", "=");
		decoded = decoded.replace("%3F", "?");
		decoded = decoded.replace("%40", "@");
		decoded = decoded.replace("%5B", "[");
		decoded = decoded.replace("%5D", "]");
		decoded = decoded.replace("%25", "%");
		return decoded;
	}

	/**
	 * 通过ftpUrl获取带有相对路径的文件
	 * @param url ftp://ip:port/aaa/bbb/ccc.jpg
	 * @return /aaa/bbb/ccc.jpg
	 */
	public static String getRelPathWithName(String url){
		StringBuilder relPathFile = new StringBuilder();
		try{
			String urlTemp = replaceSpecialString(url);
			URL tempUrl = new URL(urlTemp);
			relPathFile.append(tempUrl.getPath());
		}catch (Exception e){
			log.error("--getRelPathWithName.",e);
		}
		return relPathFile.toString();
	}

	/**
	 * 通过ftpUrl获取文件名
	 * @param url ftp://ip:port/aaa/bbb/ccc.jpg
	 * @return ccc.jpg
	 */
	public static String getFileName(String url){
		StringBuilder fileName = new StringBuilder();
		try{
			String path = getRelPathWithName(url);
			fileName.append(path.substring(path.lastIndexOf("/")+1));
		}catch (Exception e){
			log.error("--getFileName.",e);
		}
		return fileName.toString();
	}

	/**
	 * 通过ftpUrl获取相对路径目录
	 * @param url ftp://ip:port/aaa/bbb/ccc.jpg
	 * @return /aaa/bbb/ 相对路径
	 */
	public static String getRelPath(String url){
		StringBuilder relPath = new StringBuilder();
		try{
			String path = getRelPathWithName(url);
			relPath.append(path.substring(0,path.lastIndexOf("/")+1));
		}catch (Exception e){
			log.error("--getRelPath.",e);
		}
		return  relPath.toString();
	}

	/**
	 *FtpUrl特殊标识符转换
	 *
	 *标识符   +       URL 中+号表示空格                %2B
	 *标识符  空格      URL中的空格可以用+号或者编码       %20
	 *标识符  /        分隔目录和子目录                  %2F
	 *标识符  ?        分隔实际的URL和参数               %3F
	 *标识符  %        指定特殊字符                     %25
	 *标识符  #        表示书签                        %23
	 *标识符	 &        URL 中指定的参数间的分隔符        %26
	 *标识符	 =        URL 中指定参数的值              %3D
	 * @param url 地址
	 * @return 结果
	 */
	public static String replaceSpecialString(String url){
		if(null != url){
			return url.replace("#","%23");
		}else{
			return "";
		}
	}

    /**
     * "ftp://xstore:iptv!#$xs@**************:6069/xstore/Out/Distribute/PhysicalChannel/655043218924220416";
     * @param str
     * @return values[0]    xstore
     * @return values[1]    iptv!#$xs
     * @return values[2]    **************
     * @return values[3]    6069
     * @return values[4]    xstore/Out/Distribute/PhysicalChannel/655043218924220416
     */
    public static String[] getSingleMatchValue(String str )
    {
        String[] values=null;
        if (str==null) {
            return values;
        }

        String[] strs=str.split("://");
        if (strs==null||strs.length==0) {
            return values;
        }
        if (strs==null||strs.length<2||!strs[0].equalsIgnoreCase("ftp")) {
            return values;
        }
        values=new String[5];
        String address=strs[1].replaceAll("//", "/");
        values[0]=address.substring(0, address.indexOf(':'));
        address=address.substring(address.indexOf(':')+1, address.length());
        String result="";
        Pattern p=Pattern.compile("@[0-9.:]{4,25}");
        Matcher m=p.matcher(address);
        if (m.find()&& m.toMatchResult().group()!=null) {
            result=m.toMatchResult().group().trim();
        }else{
            result="";
        }
        values[1]=address.split(result)[0] ;
        values[4]=address.split(result)[1];
        values[4]=values[4].substring( values[4].indexOf('/')+1);
        values[2]=result.substring( result.indexOf('@')+1);
        if (values[2].contains(":")) {
            String[] temp=values[2].split(":");
            values[2]=temp[0];
            values[3]=temp[1];
        }else{
            values[3]="21";
        }

        return values;
    }

	/**
	 * 主要方法
	 * @param args 参数
	 */
	public static void main(String[] args) throws MalformedURLException {
//		String url=  "ftp://pake:Psj#dj80GT@**************:21/20200402/iptv_vod_20200402000500017.txt";
//		String url=  "ftp://xstore:iptv!#$xs@**************:6069/xstore/Out/Distribute/PhysicalChannel/655043218924220416";
		String url=  "*********************************************/syncdir/remote/CCTV/response/CCTV_SOAP_RESULT_CCTV_SOAP_MSG_202111162016463440614.xml";

//		System.out.println(replaceSpecialString(url));
//
		Map<String, String> map = parseFtp(url);
		map.forEach((s, s2) -> {
			System.out.println(map.get(s));
		});

//		System.out.println(getFileName(url));
//		System.out.println(getRelPath(url));
//		System.out.println(getRelPathWithName(url));
//        String[] singleMatchValue = getSingleMatchValue(url);
//        for (String s : singleMatchValue) {
//            System.out.println(s);
//        }
	}

}
