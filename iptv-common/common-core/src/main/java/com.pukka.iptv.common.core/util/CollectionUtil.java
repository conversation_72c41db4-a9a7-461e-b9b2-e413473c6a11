package com.pukka.iptv.common.core.util;

import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2021/5/24
 */
public class CollectionUtil {

    /**
     * 比较两个集合是否相等
     *
     * @param a
     * @param b
     * @return
     */
    public static boolean isEqualCollectionFast(final Collection a, final Collection b) {
        if (a.size() != b.size()) {
            return false;
        }
        Map mapa = getMap(a);
        Map mapb = getMap(b);

        if (mapa.size() != mapb.size()) {
            return false;
        }

        Iterator it = mapa.keySet().iterator();

        while (it.hasNext()) {
            Object obj = it.next();
            if (getFreq(obj, mapa) != getFreq(obj, mapb)) {
                return false;
            }
        }
        return true;
    }


    private static Integer INTEGER_ONE = new Integer(1);


    /**
     * 集合转map，值就是key出现的次数
     *
     * @param coll
     * @return
     */
    public static Map getMap(final Collection coll) {
        Map count = new HashMap();
        Iterator it = coll.iterator();
        while (it.hasNext()) {
            Object obj = it.next();
            Integer c = (Integer) (count.get(obj));
            if (null == c) {
                count.put(obj, INTEGER_ONE);
            } else {
                count.put(obj, new Integer(c.intValue() + 1));
            }
        }
        return count;
    }

    /**
     * 获取map的值（频率）
     *
     * @param obj
     * @param freqMap
     * @return
     */
    private static final int getFreq(final Object obj, final Map freqMap) {
        Integer count = (Integer) freqMap.get(obj);
        if (null == count) {
            return 0;
        }
        return count.intValue();
    }

    public static Set<String> stringToStringSet(String str, String separator){
        return Arrays.stream(str.split(separator)).
                map(s -> s.trim()).collect(Collectors.toSet());
    }

    public static Set<Long> stringToLongSet(String str,String separator){
        return Arrays.stream(str.split(separator)).
                map(s -> Long.parseLong(s.trim())).collect(Collectors.toSet());
    }

    public static List<String> stringToStringList(String str,String separator){
        return Arrays.stream(str.split(separator)).
                map(s -> s.trim()).collect(Collectors.toList());
    }

    public static List<Long> stringToLongList(String str,String separator){
        return Arrays.stream(str.split(separator)).
                map(s -> Long.parseLong(s.trim())).collect(Collectors.toList());
    }

}
