package com.pukka.iptv.common.core.util;

import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;

import java.lang.management.ManagementFactory;
import java.math.BigDecimal;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.time.*;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.Date;
import java.util.List;

import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;

/**
 * 时间工具类
 *
 * <AUTHOR>
 */
public class DateUtils extends DateUtil {
    public static String YYYY = "yyyy";

    public static String YYYY_MM = "yyyy-MM";

    public static String YYYYMM = "yyyyMM";

    public static String YYYY_MM_DD = "yyyy-MM-dd";

    public static String YYYYMMDD = "yyyyMMdd";

    public static String YYYYMMDDHHMMSS = "yyyyMMddHHmmss";

    public static String YYYYMMDDHHMMSSS = "yyyyMMddHHmmssSSS";

    public static String YYYY_MM_DD_HH_MM_SS = "yyyy-MM-dd HH:mm:ss";

    public static String HHMMSS = "HHmmss";

    public static String HH_MM_SS = "HH:mm:ss";

    private static String[] parsePatterns = {
            "yyyy-MM-dd", "yyyy-MM-dd HH:mm:ss", "yyyy-MM-dd HH:mm", "yyyy-MM", "yyyy/MM/dd",
            "yyyy/MM/dd HH:mm:ss", "yyyy/MM/dd HH:mm", "yyyy/MM", "yyyyMM", "yyyy.MM.dd",
            "yyyyMMdd", "yyyy.MM.dd HH:mm:ss", "yyyy.MM.dd HH:mm", "yyyy.MM"};

    public static String STARTTIME_SUFFIX = "00:00:00";

    public static String ENDTIME_SUFFIX = "23:59:59";

    public static BigDecimal SECONEND_BIGDECIMAL = new BigDecimal(1000 * 3600 * 24);

    /**
     * 获取当前Date型日期
     *
     * @return Date() 当前日期
     */
    ;public static Date getNowDate() {
        return new Date();
    }

    /**
     * 获取当前日期, 默认格式为yyyy-MM-dd
     *
     * @return String
     */
    public static String getDate() {
        return dateTimeNow(YYYY_MM_DD);
    }

    public static final String getTime() {
        return dateTimeNow(YYYY_MM_DD_HH_MM_SS);
    }

    public static final String dateTimeNow() {
        return dateTimeNow(YYYYMMDDHHMMSS);
    }

    public static final String dateTimeNow(final String format) {
        return parseDateToStr(format, new Date());
    }

    public static final String dateTime(final Date date) {
        return parseDateToStr(YYYY_MM_DD, date);
    }

    public static final String parseDateToStr(final String format, final Date date) {
        return new SimpleDateFormat(format).format(date);
    }

    public static final Date dateTime(final String format, final String ts) {
        try {
            return new SimpleDateFormat(format).parse(ts);
        } catch (ParseException e) {
            throw new RuntimeException(e);
        }
    }

    public static final Date dateTime(final String ts) {
        try {
            return new SimpleDateFormat(YYYY_MM_DD_HH_MM_SS).parse(ts);
        } catch (ParseException e) {
            throw new RuntimeException(e);
        }
    }

    public static final Date dateTime2(final String ts) {
        try {
            return new SimpleDateFormat(YYYY_MM_DD).parse(ts);
        } catch (ParseException e) {
            throw new RuntimeException(e);
        }
    }

    public static final String parseDateToStr(final Date date) {
        return new SimpleDateFormat(YYYY_MM_DD).format(date);
    }

    /**
     * 日期路径 即年/月/日 如2018/08/08
     */
    public static final String datePath() {
        Date now = new Date();
        return DateUtils.format(now, "yyyy/MM/dd");
    }

    /**
     * 日期路径 即年/月/日 如20180808
     */
    public static final String dateTime() {
        Date now = new Date();
        return DateUtil.format(now, "yyyyMMdd");
    }

    /**
     * 日期型字符串转化为日期 格式
     */
    public static Date parseDate(Object str) {
        if (str == null) {
            return null;
        }
        try {
            return DateUtil.parse(str.toString(), parsePatterns);
        } catch (Exception e) {
            return null;
        }
    }

    /**
     * 获取服务器启动时间
     */
    public static Date getServerStartDate() {
        long time = ManagementFactory.getRuntimeMXBean().getStartTime();
        return new Date(time);
    }

    /**
     * 计算两个时间差
     */
    public static String getDatePoor(Date endDate, Date nowDate) {
        long nd = 1000 * 24 * 60 * 60;
        long nh = 1000 * 60 * 60;
        long nm = 1000 * 60;
        // long ns = 1000;
        // 获得两个时间的毫秒时间差异
        long diff = endDate.getTime() - nowDate.getTime();
        // 计算差多少天
        long day = diff / nd;
        // 计算差多少小时
        long hour = diff % nd / nh;
        // 计算差多少分钟
        long min = diff % nd % nh / nm;
        // 计算差多少秒//输出结果
        // long sec = diff % nd % nh % nm / ns;
        return day + "天" + hour + "小时" + min + "分钟";
    }

    /**
     * 检查传入的字符串是否能格式化为date
     *
     * @param dateStrList dateStrList
     * @return false 不能
     */
    public static boolean checkDataPattern(String pattern, String... dateStrList) {
        try {
            for (String dateStr : dateStrList) {
                if (StringUtils.isNotEmpty(dateStr)) {
                    parse(dateStr, pattern);
                }
            }
            return true;
        } catch (Exception e) {
            return false;
        }
    }

    /**
     * 获取当前day天前时间，并转成 yyyy-MM-dd HH:mm:ss格式
     */
    public static Date getBeforeDateDay(int day) {
        Calendar calendar = Calendar.getInstance();
        calendar.add(Calendar.DATE, -day);
        return calendar.getTime();
    }

    /**
     * 获取当前day天前到今天全部时间，并转成 yyyy-MM-dd HH:mm:ss格式
     */
    public static List<String> getBeforeDateDayList(int day) {
        ArrayList<String> list = new ArrayList<>();
        for (int i = 0; i < day; i++) {
            Calendar calendar = Calendar.getInstance();
            calendar.add(Calendar.DATE, -i);
            String formatDate = getFormatDate(calendar.getTime(), YYYY_MM_DD) + " 00:00:00";
            list.add(formatDate);
        }
        return list;
    }

    /**
     * 日期转字符串
     *
     * @param date
     * @param fmt  日期格式
     * @return
     */
    public static String getFormatDate(Date date, String fmt) {
        try {
            SimpleDateFormat sdf = new SimpleDateFormat(fmt);
            String str = sdf.format(date);
            return str;
        } catch (Exception e) {
            return null;
        }
    }

    public static long getDateDiff(String dateStr) {
        long day = 0;
        if (ObjectUtils.isEmpty(dateStr)) {
            return -1;
        }
        try {
            Date date = new SimpleDateFormat(DateUtils.YYYYMMDDHHMMSS).parse(dateStr);
            SimpleDateFormat dateFormat = new SimpleDateFormat(YYYYMMDDHHMMSS);
            long startDateTime = dateFormat.parse(dateFormat.format(DateTime.now())).getTime();
            long endDateTime = dateFormat.parse(dateFormat.format(date)).getTime();

            BigDecimal dayBig = new BigDecimal((endDateTime) - (startDateTime));
            day = dayBig.divide(SECONEND_BIGDECIMAL,0,BigDecimal.ROUND_UP).longValue();
        } catch (Exception exception) {
           return -1l;
        }
        return day;
    }

    public static void main(String[] args) {
        List<String> beforeDateDayList = getBeforeDateDayList(30);
        System.out.println(getBeforeDateDay(1));

        long date = getDateDiff("20320328195120");
        System.out.println(date);


    }
}
