package com.pukka.iptv.common.core.util;


import lombok.extern.slf4j.Slf4j;

import javax.servlet.http.Cookie;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

/**
 * <AUTHOR>
 * @date 2021/7/28
 */
@Slf4j
public class CookieUtil {
    public static final String TOKEN_NAME = "userToken";
    //单位秒
    public static final int TOKEN_EXPIRESECONDS = 1800;

    //往浏览器写入cookie信息
    public static void addCookie(HttpServletRequest request, HttpServletResponse response, String cookieName, String cookieValue, int expireSeconds) {
        Cookie cookie = new Cookie(cookieName, cookieValue);
        cookie.setDomain(request.getServerName());//作用域，用户请求哪个域名的时候回带上该cookie信息,域名应该是nginx域名或者zuul域名
        log.info("---domain:{}---", request.getServerName());
        cookie.setPath("/");
        cookie.setMaxAge(expireSeconds);
        response.addCookie(cookie);
    }

    //从请求中获取指定Cookie的信息.
    public static String getCookie(HttpServletRequest request, String cookieName) {
        Cookie[] cookies = request.getCookies();
        if (cookies == null || cookies.length == 0) {
            //请求中没有cookie的信息,不需要做任何的逻辑
            return null;
        }
        for (Cookie cookie : cookies) {
            if (cookie.getName().equals(cookieName)) {
                return cookie.getValue();
            }
        }
        return null;
    }
}