package com.pukka.iptv.common.core.util;

import java.lang.reflect.Method;

/**
 * @Author: zhengcl
 * @Date: 2021/8/30 16:35
 */
public class EnumUtil {

    public static Object getEnumValue(Class clazz, Object code){
        Object[] enumConstants = clazz.getEnumConstants();
        try {
            for(Object object : enumConstants){
                //获取对象的公开方法，参数标识和方法名称
                Method codeMethod = clazz.getMethod("getCode");
                Method nameMethod = clazz.getMethod("getMessage");
                if(code.equals(codeMethod.invoke(object))){
                    return nameMethod.invoke(object);
                }
            }
        }catch (Exception e){
            e.printStackTrace();
        }
        return null;
    }
}
