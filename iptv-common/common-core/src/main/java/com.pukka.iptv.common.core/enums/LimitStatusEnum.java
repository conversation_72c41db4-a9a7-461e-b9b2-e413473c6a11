package com.pukka.iptv.common.core.enums;

/**
 * 是否限速 0不限速 ， 1限速
 */
public enum LimitStatusEnum {

    NOT_LIMIT(0, "不限速"),
    LIMIT(1, "限速");
    private Integer code;
    private String msg;


    LimitStatusEnum(Integer code, String msg) {
        this.code = code;
        this.msg = msg;
    }

    public Integer getCode() {
        return code;
    }

    public void setCode(Integer code) {
        this.code = code;
    }

    public String getMsg() {
        return msg;
    }

    public void setMsg(String msg) {
        this.msg = msg;
    }
}
