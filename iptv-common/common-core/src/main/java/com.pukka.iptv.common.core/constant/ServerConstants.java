package com.pukka.iptv.common.core.constant;

/**
 * <AUTHOR>
 * @date 2021年06月22日16:41:01 服务名称
 */
public class ServerConstants {

    /**
     * 认证中心
     */
    public static final String AUTH_SERVER = "iptv-auth";

    /**
     * 后台服务
     */
    public static final String MANAGE_SERVER = "iptv-manage";

    /**
     * 网关服务
     */
    public static final String GATEWAY_SERVER = "iptv-gateway";

    /**
     * 注入API
     */
    public static final String IN_API = "c2in-api";

    /**
     * 分发API
     */
    public static final String OUT_API = "c2out-api";

    /**
     * 下载服务
     */
    public static final String DOWNLOAD_SERVER = "downloader-server";

    /**
     * 监控服务
     */
    public static final String MONITOR_SERVER = "system-monitor";

    /**
     * 调度中心服务
     */
    public static final String JOB_ADMIN = "system-xxl-job-admin";

    /**
     * 执行器服务
     */
    public static final String JOB_EXECUTOR = "system-xxl-job";

    /**
     * 执行器服务
     */
    public static final String JOB_C2IN_EXECUTOR = "system-xxl-job-c2in";

    /**
     * 分发服务
     */
    public static final String JOB_C2OUT_EXECUTOR = "system-xxl-job-c2out";

    /**
     * smp服务
     */
    public static final String SMP_SERVER = "iptv-smp";

    /**
     * 节目单违禁规则
     */
    public static final String IPTV_SCHEDULE_RULES = "iptv-schedule-rules";

    /**
     * 基础插件
     */
    public static final String IPTV_BASIC_PLUGINS = "iptv-basic-plugins";

    /**
     * EPG上下游接口服务
     */
    public static final String EPG_API_SERVER = "epg-api";

    /**
     * EPG后台服务
     */
    public static final String EPG_MANAGE_SERVER = "epg-manage";

    /**
     * EPG后台服务
     */
    public static final String EPG_C1OUT_SERVER = "epg-c1out";
}
