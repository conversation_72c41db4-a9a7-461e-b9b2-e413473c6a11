package com.pukka.iptv.common.proxy.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

/**
 * @Author: zhengcl
 * @Date: 2021/11/4 16:09
 */

@Component
@ConfigurationProperties(prefix = "system.out.proxy")
@Data
public class SystemOutProxyProperties {

    private boolean enable;
    private String host;
    private Integer port;
    private String username;
    private String password;
    private String nonHosts;


}