package com.pukka.iptv.common.proxy.config;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import java.io.IOException;
import java.net.*;

/**
 * @Author: zhengcl
 * @Date: 2021/11/4 16:07
 */

@Configuration
public class SystemProxyConfig {

    @Autowired
    private SystemInProxyProperties systemInProxyProperties;

//    @Bean
    public void systemProxy() throws IOException {

        if(systemInProxyProperties.isEnable()) {
            Authenticator.setDefault(new DefaultAuthenticator(systemInProxyProperties.getUsername(), systemInProxyProperties.getPassword()));
            SocketAddress addr = new InetSocketAddress("************", 3128);
            Proxy proxy = new Proxy(Proxy.Type.SOCKS, addr);
            Socket socket = new Socket(proxy);
            InetSocketAddress dest = new InetSocketAddress("*************", 20021);
            Authenticator.setDefault(new DefaultAuthenticator(systemInProxyProperties.getUsername(), systemInProxyProperties.getPassword()));
            socket.connect(dest);
//            socket.close();

//            SocketAddress addr = new InetSocketAddress("************", 80);
//            Proxy proxy = new Proxy(Proxy.Type.HTTP, addr);
//            URL url = new URL("http://www.google.com.tw");
//            URLConnection conn = url.openConnection(proxy);
//            conn.connect();
//
//            System.setProperty("http.proxyHost", systemProxyProperties.getHost());
//            System.setProperty("http.proxyPort", systemProxyProperties.getPort() + "");
//            System.setProperty("http.proxyUser", systemProxyProperties.getUsername());
//            System.setProperty("http.proxyPassword", systemProxyProperties.getPassword());
//            System.setProperty("http.nonProxyHosts", systemProxyProperties.getNonHosts());
//            System.setProperty("https.proxyHost", systemProxyProperties.getHost());
//            System.setProperty("https.proxyPort", systemProxyProperties.getPort() + "");
//            System.setProperty("https.proxyUser", systemProxyProperties.getUsername());
//            System.setProperty("https.proxyPassword", systemProxyProperties.getPassword());
//            System.setProperty("https.nonProxyHosts", systemProxyProperties.getNonHosts());
//            System.setProperty("ftp.proxyHost", systemProxyProperties.getHost());
//            System.setProperty("ftp.proxyPort", systemProxyProperties.getPort() + "");
//            System.setProperty("ftp.proxyUser", systemProxyProperties.getUsername());
//            System.setProperty("ftp.proxyPassword", systemProxyProperties.getPassword());
//            System.setProperty("ftp.nonProxyHosts", systemProxyProperties.getNonHosts());
//            System.setProperty("socks.proxyHost", systemProxyProperties.getHost());
//            System.setProperty("socks.proxyPort", systemProxyProperties.getPort() + "");
//            System.setProperty("socks.proxyUser", systemProxyProperties.getUsername());
//            System.setProperty("socks.proxyPassword", systemProxyProperties.getPassword());
//            System.setProperty("socks.nonProxyHosts", systemProxyProperties.getNonHosts());
//            Authenticator.setDefault(new DefaultAuthenticator(systemProxyProperties.getUsername(), systemProxyProperties.getPassword()));
        }
//        URL url = new URL("http://*************:8090/webDemo/index.jsp");
        //创建代理服务器
//        InetSocketAddress addr = new InetSocketAddress(systemProxyProperties.getHost(), systemProxyProperties.getPort());
//        Proxy proxy = new Proxy(Proxy.Type.HTTP, addr); //SOCKS代理
//        Proxy proxy = new Proxy(Proxy.Type.HTTP, addr); //HTTP代理
//        String headerKey = "Proxy-Authorization";
//        String headerValue = "Basic " + Base64.getEncoder().encode((systemProxyProperties.getSquidProxyUsername()+":"+systemProxyProperties.getSquidProxyPassword()).getBytes());
//        URLConnection uc = url.openConnection(proxy);
//        uc.setRequestProperty(headerKey, headerValue);
//        uc.connect()
//        Authenticator.setDefault(new DefaultAuthenticator(systemProxyProperties.getUsername(),systemProxyProperties.getPassword()));
//        return proxy;

    }

}
