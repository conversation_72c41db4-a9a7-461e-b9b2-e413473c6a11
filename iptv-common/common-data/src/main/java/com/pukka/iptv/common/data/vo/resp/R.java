package com.pukka.iptv.common.data.vo.resp;


import com.baomidou.mybatisplus.core.metadata.IPage;
import com.pukka.iptv.common.base.vo.CommonResponse;


/**
 * @author: wz
 * @date: 2021/8/31 11:43
 * @description:
 */
public class R<T> extends CommonResponse<T> {


    public R(Integer code, String message, T data) {
        super(code, message, data);
    }

    public static <T> CommonResponse<IPage<T>> page(IPage<T> p) {
        return R.success(new RPage<T>()
                .setCurrent(p.getCurrent())
                .setRecords(p.getRecords())
                .setSize(p.getSize())
                .setTotal(p.getTotal()));
    }
}
