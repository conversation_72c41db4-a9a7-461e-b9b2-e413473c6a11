package com.pukka.iptv.common.data.vo.resp;

import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

/**
 * @Author: wz
 * @Date: 2021/11/8 21:03
 * @Description:
 */
@Setter
@Getter
@Accessors(chain = true)
public class Rt<T> {
    private T data;
    private boolean ok;
    private String msg;

    public static <T> Rt<T> ok(T t) {
        Rt<T> r = new Rt<>();
        r.setData(t).setOk(true);
        return r;
    }

    public static <T> Rt<T> notOK(T t) {
        Rt<T> r = new Rt<>();
        r.setData(t).setOk(false);
        return r;
    }

    public static <T> Rt<T> notOK(T t, String msg) {
        Rt<T> r = new Rt<>();
        r.setData(t).setOk(false).setMsg(msg);
        return r;
    }
}
