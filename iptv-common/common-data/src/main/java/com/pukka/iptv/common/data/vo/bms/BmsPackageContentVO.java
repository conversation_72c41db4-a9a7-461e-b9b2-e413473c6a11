package com.pukka.iptv.common.data.vo.bms;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.NoArgsConstructor;
import com.pukka.iptv.common.data.model.bms.BmsPackageContent;
import io.swagger.annotations.ApiModel;
import lombok.Data;
import lombok.Getter;
import lombok.Setter;

/**
 * 包内容
 * 
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2021-08-27 14:54:32
 */
@Getter
@Setter
@JsonIgnoreProperties(value = { "handler" })
@NoArgsConstructor
@ApiModel("BmsPackageContentVo")
public class BmsPackageContentVO extends BmsPackageContent implements java.io.Serializable{

}
