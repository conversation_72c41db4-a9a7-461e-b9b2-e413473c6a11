package com.pukka.iptv.common.data.model.order;

import com.fasterxml.jackson.annotation.JsonSubTypes;
import com.fasterxml.jackson.annotation.JsonTypeInfo;
import com.pukka.iptv.common.base.constant.ObjectsTypeConstants;
import com.pukka.iptv.common.base.enums.ActionEnums;
import com.pukka.iptv.common.base.enums.DefinitionEnums;
import com.pukka.iptv.common.data.model.in.InChannel;
import com.pukka.iptv.common.data.model.in.InPhysicalChannel;
import com.pukka.iptv.common.data.model.in.InSchedule;
import com.pukka.iptv.common.data.model.in.InSeries;
import com.pukka.iptv.common.data.util.SafeUtil;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.io.Serializable;
import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 * @Date 2021/8/31 3:54 下午
 * @description: 组装object信息
 * @Version 1.0
 */
@Getter
@Setter
@ToString
@JsonTypeInfo(use = JsonTypeInfo.Id.NAME,include = JsonTypeInfo.As.PROPERTY,property = "elementType",visible = true)
@JsonSubTypes({@JsonSubTypes.Type(value=CategoryObjectEntity.class,name = "Category")
        ,@JsonSubTypes.Type(value=MovieObjectEntity.class,name = "Movie")
        ,@JsonSubTypes.Type(value=PackageObjectEntity.class,name = "Package")
        ,@JsonSubTypes.Type(value=PictureObjectEntity.class,name = "Picture")
        ,@JsonSubTypes.Type(value=ProgramObjectEntity.class,name = "Program")})
public class SubOrderObjectsEntity implements Serializable {
    private static final long serialVersionUID = -7387214962532296452L;
    /**
     * 序号
     */
    private int serialNumber;
    /**
     * id
     */
    private String id;
    /**
     * 类型
     */
    private String elementType;
    /**
     * 代码
     */
    private String code;
    /**
     * 执行操作
     */
    private String action;
    /**
     * 发布状态
     */
    private String publishStatus;
    /**
     * 属性集
     */
    private Map<String,String> propertyDic;

    public SubOrderObjectsEntity(InChannel inChannel) {
        elementType = ObjectsTypeConstants.CHANNEL;
        id = inChannel.getCode();
        code = inChannel.getCode();
        action = ActionEnums.getInfoByCode(inChannel.getAction());
        propertyDic = new HashMap<>();
        propertyDic.put("ChannelNumber", inChannel.getChannelNumber());
        propertyDic.put("Name", inChannel.getName());
        propertyDic.put("CallSign", SafeUtil.getString(inChannel.getCallSign()));
        propertyDic.put("TimeShift", SafeUtil.getString(inChannel.getTimeShift()));
        propertyDic.put("StorageDuration", SafeUtil.getString(inChannel.getStorageDuration()));
        propertyDic.put("TimeShiftDuration", SafeUtil.getString(inChannel.getTimeShiftDuration()));
        propertyDic.put("Description", SafeUtil.getString(inChannel.getDescription()));
        propertyDic.put("Country", SafeUtil.getString(inChannel.getCountry()));
        propertyDic.put("State", SafeUtil.getString(inChannel.getState()));
        propertyDic.put("City", SafeUtil.getString(inChannel.getCity()));
        propertyDic.put("ZipCode", SafeUtil.getString(inChannel.getZipCode()));
        propertyDic.put("Type", SafeUtil.getString(inChannel.getType()));
        propertyDic.put("SubType", SafeUtil.getString(inChannel.getSubType()));
        propertyDic.put("Language", SafeUtil.getString(inChannel.getLanguage()));
        propertyDic.put("Status", SafeUtil.getString(inChannel.getStatus()));
        propertyDic.put("StartTime", SafeUtil.getString(inChannel.getStartTime()));
        propertyDic.put("EndTime", SafeUtil.getString(inChannel.getEndTime()));
        propertyDic.put("Macrovision", SafeUtil.getString(inChannel.getMacrovision()));
        propertyDic.put("Bilingual", SafeUtil.getString(inChannel.getBilingual()));
        propertyDic.put("VSPCode", SafeUtil.getString(inChannel.getVspCode()));
    }

    public SubOrderObjectsEntity(InSeries inSeries) {
        elementType = ObjectsTypeConstants.SERIES;
        id = inSeries.getCode();
        code = inSeries.getCode();
        action = ActionEnums.getInfoByCode(inSeries.getAction());
        propertyDic = new HashMap<>();
        propertyDic.put("Name", inSeries.getName());
        propertyDic.put("OrderNumber", inSeries.getOrderNumber());
        propertyDic.put("OriginalName", inSeries.getOriginalName());
        propertyDic.put("SortName", inSeries.getSortName());
        propertyDic.put("SearchName", inSeries.getSearchName());
        propertyDic.put("OrgAirDate", inSeries.getOrgAirDate());
        propertyDic.put("ReleaseYear", "");
        propertyDic.put("LicensingWindowStart", inSeries.getLicensingWindowStart());
        propertyDic.put("LicensingWindowEnd", inSeries.getLicensingWindowEnd());
        propertyDic.put("DisplayAsNew", SafeUtil.getString(inSeries.getDisplayAsNew()));
        propertyDic.put("DisplayAsLastChance", SafeUtil.getString(inSeries.getDisplayAsLastChance()));
        propertyDic.put("Macrovision", SafeUtil.getString(inSeries.getMacrovision()));
        propertyDic.put("Price", SafeUtil.getPrice(inSeries.getPrice()));
        propertyDic.put("VolumnCount", SafeUtil.getString(inSeries.getVolumnCount()));
        propertyDic.put("Status", SafeUtil.getString(inSeries.getStatus()));
        propertyDic.put("Description", inSeries.getDescription());
        propertyDic.put("ActorDisplay", inSeries.getKpeople());
        propertyDic.put("Kpeople", inSeries.getKpeople());
        propertyDic.put("Director", inSeries.getDirector());
        propertyDic.put("ScriptWriter", inSeries.getScriptWriter());
        propertyDic.put("Compere", inSeries.getCompere());
        propertyDic.put("Guest", inSeries.getGuest());
        propertyDic.put("Reporter", inSeries.getReporter());
        propertyDic.put("OPIncharge", inSeries.getOpIncharge());
        propertyDic.put("SeriesType", SafeUtil.getString(inSeries.getSeriesType()));
        propertyDic.put("CopyRight", inSeries.getCopyRight());
        propertyDic.put("ContentProvider", inSeries.getContentProvider());
        propertyDic.put("VSPCode", inSeries.getVspCode());
        propertyDic.put("Rating", inSeries.getRating());
        propertyDic.put("NewPrice", inSeries.getNewPrice());
        propertyDic.put("PgmCategory", inSeries.getPgmCategory());
        propertyDic.put("PgmSndClass", inSeries.getPgmSndClass());
        propertyDic.put("DefinitionFlag", SafeUtil.getString(DefinitionEnums.getDefinitionflag(inSeries.getDefinitionFlag())));
        propertyDic.put("Language", inSeries.getLanguage());
        //propertyDic.put("DefinitionFlag", SafeUtil.getString(inSeries.getDefinitionFlag()));
        propertyDic.put("Type", SafeUtil.getString(inSeries.getType()));
        propertyDic.put("Tags", SafeUtil.getString(inSeries.getTags()));

    }

    public SubOrderObjectsEntity(InPhysicalChannel inPhysicalChannel) {
        elementType = ObjectsTypeConstants.PHYSICAL_CHANNEL;
        id = inPhysicalChannel.getCode();
        code = inPhysicalChannel.getCode();
        action = ActionEnums.getInfoByCode(inPhysicalChannel.getAction());
        propertyDic = new HashMap<>();
        propertyDic.put("ChannelCode", inPhysicalChannel.getChannelCode());
        propertyDic.put("ChannelID", SafeUtil.getString(inPhysicalChannel.getChannelId()));
        propertyDic.put("BitRateType", SafeUtil.getString(inPhysicalChannel.getBitRateType()));
        propertyDic.put("MultiCastIP", inPhysicalChannel.getMultiCastIp());
        propertyDic.put("MultiCastPort", inPhysicalChannel.getMultiCastPort());
        propertyDic.put("MediaSpec", inPhysicalChannel.getMediaSpec());
    }

    public SubOrderObjectsEntity(InSchedule inSchedule) {
        elementType = ObjectsTypeConstants.SCHEDULE;
        id = inSchedule.getCode();
        code = inSchedule.getCode();
        action = ActionEnums.getInfoByCode(inSchedule.getAction());
        propertyDic = new HashMap<>();
        propertyDic.put("ChannelCode", inSchedule.getChannelCode());
        propertyDic.put("ChannelID", SafeUtil.getString(inSchedule.getChannelCode()));
        propertyDic.put("ProgramName", inSchedule.getProgramName());
        propertyDic.put("StartDate", inSchedule.getStartDate());
        propertyDic.put("StartTime", inSchedule.getStartTime());
        propertyDic.put("Duration", inSchedule.getDuration());
        //propertyDic.put("StorageDuration", SafeUtil.getString(inSchedule.getStorageDuration()));
        propertyDic.put("Status", SafeUtil.getString(inSchedule.getStatus()));
        //propertyDic.put("Description", inSchedule.getDescription());
        //propertyDic.put("Genre", inSchedule.getGenre());
    }

    public SubOrderObjectsEntity() {}
}
