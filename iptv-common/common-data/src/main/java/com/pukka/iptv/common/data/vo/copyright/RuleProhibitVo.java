package com.pukka.iptv.common.data.vo.copyright;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.pukka.iptv.common.base.enums.ProhibitPointEnum;
import com.pukka.iptv.common.data.model.copyright.RuleProhibit;
import io.swagger.annotations.ApiModel;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

/**
 * @Author: chiron
 * @Date: 2022/08/04/10:25
 * @Description:
 */
@Data
@EqualsAndHashCode
@NoArgsConstructor
@Accessors(chain = true)
@ApiModel("RuleProhibitVo")
@JsonIgnoreProperties(ignoreUnknown = true)
public class RuleProhibitVo extends RuleProhibit implements java.io.Serializable {
    /**
     * 违禁片名称
     */
    private String contentName;
    /**
     * 违禁媒资code
     */
    private String contentCode;
    /**
     * 违禁媒资类型
     */
    private String type;
    /**
     * cpId
     */
    private Long cpId;
    /**
     * 触发点类型
     */
    private ProhibitPointEnum prohibitPointEnum;
}
