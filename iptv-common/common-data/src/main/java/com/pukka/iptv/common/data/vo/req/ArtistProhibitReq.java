package com.pukka.iptv.common.data.vo.req;

import com.pukka.iptv.common.data.model.copyright.ArtistProhibit;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 *
 * @author: zhengcl
 * @date: 2022-7-18 10:33:47
 */

@Data
@AllArgsConstructor
@Accessors(chain = true)
public class ArtistProhibitReq extends ArtistProhibit implements java.io.Serializable{

}
