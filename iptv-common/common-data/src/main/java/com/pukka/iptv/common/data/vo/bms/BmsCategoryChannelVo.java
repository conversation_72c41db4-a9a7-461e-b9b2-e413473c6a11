package com.pukka.iptv.common.data.vo.bms;

import lombok.Getter;
import lombok.Setter;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.Pattern;
import java.io.Serializable;
import java.util.List;

import static com.pukka.iptv.common.data.util.RegexUtil.ALL_CHAR_REGEX;

/**
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2021/9/7
 */
@Getter
@Setter
public class BmsCategoryChannelVo  implements Serializable
{

    /**
     * 频道与栏目关系ids
     */
    @Pattern(regexp = ALL_CHAR_REGEX + "{0,255}")
    @NotBlank
    private String ids;
    /**
     * 是否定时发布 1定时 2不定时
     */
    private Integer doSchedule;
    /**
     * 定时发布时间
     */
    private String scheduleTime;

    /**
     * 栏目ids
     */
    @NotEmpty
    private List<Long> categoryIds;

    /**
     * 频道ids
     */
    @NotEmpty
    private List<Long> channelIds;

    private int priority;
}
