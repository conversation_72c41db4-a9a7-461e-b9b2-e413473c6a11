package com.pukka.iptv.common.data.dto;

import lombok.Data;

import javax.validation.constraints.NotNull;

/**
 * @Author: liaowj
 * @Description: 内容发布反馈Dto，用于接收内容发布上报的请求参数，以一个主工单作为一次反馈
 * @CreateDate: 2021/9/22 17:06
 * @Version: 1.0
*/
@Data
public class PublishParamsDto {

    /**
     * 必填,类型：1：单集   2：子集   3：电视剧   4：系列片   5：片花6：直播   7：物理频道   8：栏目   9：产品包   10：节目单  （11：图片12：视频介质   ）
     * 18：栏目节目   19：栏目剧集   20：栏目频道   21：产品包频道   22：产品包剧集  24：频道+物理频道
     */
    @NotNull
    private Integer contentType;

    /**
     * spid
     */
    private Long spId;
    /**
     * spname
     */
    private String spName;

    /**
     * bms主键id,必填
     */
    @NotNull
    private Long contentId;

    /**
     * 显示名称
     */
    private String showName;

    /**
     * 动作必填 1：REGIST，2：UPDATE，3：DELETE
     */
    @NotNull
    private Integer action;

    /**
     * 图片id，以逗号隔开
     */
    private String pictureIds;

    /**
     * 工单类型 1.普通发布2.自动发布
     */
    private Integer orderType;

    /**
     * 下游处理结果, 必填 0：成功
     *              -1：失败
     */
    @NotNull
    private Integer result;

    /**
     * 结果描述
     */
    private String errorDescription;

    /** 节目的状态，用于区分是否是物理删除 */
    private Integer status;

    /**
     * 媒姿对应主工单id
     */
    private String baseOrderId;

    /**
     * 注入工单correlateId
     */
    private String correlateId;

    /**
     * 注入工单spIds
     */
    private String spIds;


    /**
     * 是否sp集中上报
     */
    private Boolean isFinish;
}
