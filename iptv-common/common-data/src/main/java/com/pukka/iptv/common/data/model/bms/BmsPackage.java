package com.pukka.iptv.common.data.model.bms;

import com.baomidou.mybatisplus.annotation.*;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.Date;

/**
 * 产品包
 *
 * <AUTHOR>
 * @date 2021-08-26 16:38:54
 */
@Data
@Accessors(chain = true)
@JsonIgnoreProperties(value = {"handler"}, ignoreUnknown = true)
@TableName(value = "bms_package", autoResultMap = true)
public class BmsPackage implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @TableId(type = IdType.AUTO)
    @ApiModelProperty(value = "主键", dataType = "Long", name = "id")
    private Long id;

    /**
     * 全局唯一标识
     */
    @TableField(value = "code")
    @ApiModelProperty(value = "全局唯一标识", dataType = "String", name = "code")
    private String code;

    /**
     * 产品包名称
     */
    @TableField(value = "name")
    @ApiModelProperty(value = "产品包名称", dataType = "String", name = "name")
    private String name;

    /**
     * Package 类型 0: VOD 1: PVOD 2: Channel 包 3: TVOD 4: SVOD 99: Mix(待定义
     */
    @TableField(value = "type")
    @ApiModelProperty(value = "Package 类型 0: VOD 1: PVOD 2: Channel 包 3: TVOD 4: SVOD 99: Mix(待定义", dataType = "Integer", name = "type")
    private Integer type;

    /**
     * 分类：0：普通包 1：统一包
     */
    @TableField(value = "classifiation")
    @ApiModelProperty(value = "分类：0：普通包 1：统一包", dataType = "Integer", name = "classifiation")
    private Integer classifiation;

    /**
     * 索引名称供界面排序
     */
    @TableField(value = "sort_name")
    @ApiModelProperty(value = "索引名称供界面排序", dataType = "String", name = "sortName")
    private String sortName;

    /**
     * 搜索名称供界面搜索
     */
    @TableField(value = "search_name")
    @ApiModelProperty(value = "搜索名称供界面搜索", dataType = "String", name = "searchName")
    private String searchName;

    /**
     * 租用有效期（小时）
     */
    @TableField(value = "rental_period")
    @ApiModelProperty(value = "租用有效期（小时）", dataType = "Long", name = "rentalPeriod")
    private Long rentalPeriod;

    /**
     * 订购编号
     */
    @TableField(value = "order_number")
    @ApiModelProperty(value = "订购编号", dataType = "String", name = "orderNumber")
    private String orderNumber;

    /**
     * 有效定购开始时间 (YYYYMMDDHH24MiSS)
     */
    @TableField(value = "licensing_window_start")
    @ApiModelProperty(value = "有效定购开始时间 (YYYYMMDD)", dataType = "String", name = "licensingWindowStart")
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private String licensingWindowStart;

    /**
     * 有效定购结束时间 (YYYYMMDDHH24MiSS)
     */
    @TableField(value = "licensing_window_end")
    @ApiModelProperty(value = "有效定购结束时间 (YYYYMMDD)", dataType = "String", name = "licensingWindowEnd")
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private String licensingWindowEnd;

    /**
     * 定价
     */
    @TableField(value = "price")
    @ApiModelProperty(value = "定价", dataType = "java.math.BigDecimal", name = "price")
    private java.math.BigDecimal price;

    /**
     * 状态标志 0:失效 1:生效 ，默认有效
     */
    @TableField(value = "status")
    @ApiModelProperty(value = "状态标志 0:失效 1:生效 ，默认有效", dataType = "Integer", name = "status")
    private Integer status;

    /**
     * 描述信息
     */
    @TableField(value = "description")
    @ApiModelProperty(value = "描述信息", dataType = "String", name = "description")
    private String description;

    /**
     * 创建时间
     */
    @TableField(value = "create_time", fill = FieldFill.INSERT)
    @ApiModelProperty(value = "创建时间", dataType = "String", name = "createTime")
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date createTime;

    /**
     * 更新时间
     */
    @TableField(value = "update_time", fill = FieldFill.UPDATE)
    @ApiModelProperty(value = "更新时间", dataType = "String", name = "updateTime")
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date updateTime;

    /**
     * spId
     */
    @TableField(value = "sp_id")
    @ApiModelProperty(value = "spId", dataType = "Long", name = "spId")
    private Long spId;

    /**
     * SP名称
     */
    @TableField(value = "sp_name")
    @ApiModelProperty(value = "SP名称", dataType = "String", name = "spName")
    private String spName;

    /**
     * 所属渠道id
     */
    @TableField(value = "bms_sp_channel_id")
    @ApiModelProperty(value = "渠道id，来自sys_dictionary_itemsl表", dataType = "Long", name = "bmsSpChannelId")
    private Long bmsSpChannelId;

    /**
     * 所属渠道
     */
    @TableField(value = "bms_sp_channel_name")
    @ApiModelProperty(value = "所属渠道", dataType = "String", name = "bmsSpChannelName")
    private String bmsSpChannelName;

    /**
     * 发布通道ID，多个ID以英文逗号隔开
     */
    @TableField(value = "out_passage_ids")
    @ApiModelProperty(value = "发布通道ID，多个ID以英文逗号隔开", dataType = "String", name = "outPassageIds")
    private String outPassageIds;

    /**
     * 分发通道名称以英文逗号 隔开
     */
    @TableField(value = "out_passage_names")
    @ApiModelProperty(value = "分发通道名称以英文逗号 隔开", dataType = "String", name = "outPassageNames")
    private String outPassageNames;

    /**
     * 发布状态  1：待发布 2：发布中 3：发布成功 4：发布失败 5：待更新 6：更新中 7：更新失败 8：回收中 9：回收成功 10：回收失败
     */
    @TableField(value = "publish_status")
    @ApiModelProperty(value = "发布状态  1：待发布 2：发布中 3：发布成功 4：发布失败 5：待更新 6：更新中 7：更新失败 8：回收中 9：回收成功 10：回收失败", dataType = "Integer", name = "publishStatus")
    private Integer publishStatus;

    /**
     * publishTime
     */
    @TableField(value = "publish_time")
    @ApiModelProperty(value = "publishTime", dataType = "String", name = "publishTime")
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date publishTime;

    /**
     * 发布描述
     */
    @TableField(value = "publish_description")
    @ApiModelProperty(value = "发布描述", dataType = "String", name = "publishDescription")
    private String publishDescription;

    /**
     * 创建人
     */
    @TableField(value = "creator_name", fill = FieldFill.INSERT)
    @ApiModelProperty(value = "创建人", dataType = "String", name = "creatorName")
    private String creatorName;

    /**
     * creatorId
     */
    @TableField(value = "creator_id", fill = FieldFill.INSERT)
    @ApiModelProperty(value = "creatorId", dataType = "Long", name = "creatorId")
    private Long creatorId;

    /**
     * 来源1：专线注入 2：人工创建
     */
    @TableField(value = "source")
    @ApiModelProperty(value = "来源1：专线注入 2：人工创建", dataType = "Integer", name = "source")
    private Integer source;

    /**
     * 资费类型      0：免费  1：单包月  2：续包月 3：单包季 4：续包季 5：单包半年 6：单包年 7：单点收费
     */
    @TableField(value = "price_type")
    @ApiModelProperty(value = "资费类型      0：免费  1：单包月  2：续包月 3：单包季 4：续包季 5：单包半年 6：单包年 7：单点收费", dataType = "Integer", name = "priceType")
    private Integer priceType;

    /**
     * 锁定状态  1：未锁定 2：锁定
     */
    @TableField(value = "lock_status")
    @ApiModelProperty(value = "锁定状态  1：未锁定 2：锁定", dataType = "Integer", name = "lockStatus")
    private Integer lockStatus;

    /**
     * cpId
     */
    @TableField(value = "cp_id")
    @ApiModelProperty(value = "cpId", dataType = "Long", name = "cpId")
    private Long cpId;
    /**
     * CP名称
     */
    @TableField(value = "cp_name")
    @ApiModelProperty(value = "CP名称", dataType = "String", name = "cpName")
    private String cpName;

    /**
     * 扩展code，是运营商使用的code
     */
    @TableField(value = "extra_code")
    @ApiModelProperty(value = "扩展code，cp提供的code与运营商使用的code不一致，当发给运营商时，使用extraCode，发给UT使用code", dataType = "String", name = "extraCode")
    private String extraCode;
    /**
     * 产品包排序
     */
    @TableField(value = "sequence")
    @ApiModelProperty(value = "产品包排序", dataType = "Long", name = "sequence")
    private Integer sequence;
}
