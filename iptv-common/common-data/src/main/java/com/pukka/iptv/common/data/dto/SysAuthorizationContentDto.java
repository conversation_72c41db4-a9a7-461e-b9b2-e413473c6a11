package com.pukka.iptv.common.data.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.util.Date;
import lombok.Data;
import com.fasterxml.jackson.annotation.JsonFormat;

import java.io.Serializable;

/**
 * TODO 授权媒资实体
 *
 * <AUTHOR>
 * @date 2021/9/1 16:06
 */
@Data
@ApiModel("授权媒资实体")
public class SysAuthorizationContentDto implements Serializable {

    @ApiModelProperty(value = "媒资id",example = "12321324324123321",name="id")
    private Long id;

    @ApiModelProperty(value = "媒资名称",example = "小猪佩奇",name = "name")
    private String name;

    @ApiModelProperty(value="op终审状态,0未审核 1审核未通过 2审核通过",example = "1",name = "opCheckStatus")
    private Integer opCheckStatus;

    @ApiModelProperty(value="所属CPID",dataType="Long",name="cpId")
    private Long cpId;

    @ApiModelProperty(value="所属CP名称",dataType="Long",name="cpId")
    private String cpName;

    @ApiModelProperty(value="媒资类型,0-单集 1-剧集",example = "1",name = "seriesFlag")
    private Integer seriesFlag;

    @ApiModelProperty(value = "媒资分类",example = "电影",name = "pgmCategory")
    private String pgmCategory;

    @ApiModelProperty(value = "媒资类型",example = "电影",name = "pgmCategory")
    private String pgmSndClass;

    @ApiModelProperty(value = "媒资别名",example = "电影",name = "originalName")
    private String originalName;


    @ApiModelProperty(value = "code",example = "电影",name = "code")
    private String code;
    @ApiModelProperty(value = "清晰度",example = "1",name = "definitionFlag")
    private Integer definitionFlag;
    @ApiModelProperty(value = "用户账号",example = "1",name = "userName")
    private String userName;
}
