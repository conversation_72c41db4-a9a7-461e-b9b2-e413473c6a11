package com.pukka.iptv.common.data.dto;


import cn.hutool.core.util.ObjectUtil;
import com.pukka.iptv.common.base.enums.RequestResourceEnum;
import com.pukka.iptv.common.base.enums.SourceEnum;
import com.pukka.iptv.common.data.model.cms.CmsProgram;
import com.pukka.iptv.common.data.model.in.InProgram;
import lombok.Data;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.util.Assert;

import java.io.Serializable;
import java.util.List;

@Data
//@JsonIgnoreProperties(ignoreUnknown = true)
public class CmsProgramDto extends CmsProgram implements Serializable {
    private List<CmsPictureDto> picArray;
    //正片id
    private String movIds;
    //预览片id
    private String previewIds;

    //2级分类idList
    private List<String> pgmSndClassIdList;

    private String ids;

    private List<String> ctcc;
    private List<String> cmcc;
    private List<String> cucc;

    private String timeStart;
    private String timeEnd;

    private String cspId;


    // 这个字段的含义是调用这个接口的场景   1 表示是后台定时任务调用  2 表示是前端页面调用
    private Integer requestResource;

    public void validRequestResource() {
        Assert.notNull(requestResource, "请选择人工创建或专线创建");
    }

    // 单集新增参数校验
    public void validSimpleSave() {
        if (requestResource.equals(RequestResourceEnum.SYSTEM.getCode())) {
            validSimple();
            setName(getName().trim());
        }
    }


    // 单集修改参数校验
    public void validSimpleUpdate() {
        if (requestResource.equals(RequestResourceEnum.SYSTEM.getCode())) {
            Assert.notNull(getId(), "节目ID不能为空");
            validSimple();
            setName(getName().trim());
        }
    }

    //子集新增参数校验
    public void validSubsetSave() {
        if (requestResource.equals(RequestResourceEnum.SYSTEM.getCode())) {
            validSubset();
            setName(getName().trim());
        }
    }

    //子集编辑参数校验
    public void validSubsetUpdate() {
        if (requestResource.equals(RequestResourceEnum.SYSTEM.getCode())) {
            Assert.notNull(getId(), "节目ID不能为空");
            validSubset();
            setName(getName().trim());
        }
    }

    public CmsProgramDto(CmsProgram cmsProgram){
        BeanUtils.copyProperties(cmsProgram, this, "id", "createTime", "updateTime");
    }

    public CmsProgramDto(InProgram inProgram) {
        BeanUtils.copyProperties(inProgram, this, "id", "createTime", "updateTime");
        this.setSource(SourceEnum.SYSWORK.getValue());
    }

    public CmsProgramDto() {
    }

    //单集基础参数校验
    private void validSimple() {
        Assert.hasText(getName(), "媒资名称不能为空");
        if (ObjectUtil.isNotEmpty(getName())) {
            if (getName().length() > 128) {
                throw new IllegalArgumentException("媒资名称不能大于128位");
            }
        }
        Assert.notNull(getCpId(), "所属CP不能为空");
        Assert.hasText(getContentProvider(), "内容提供商不能为空");
        Assert.notNull(getPgmCategoryId(), "一级分类不能为空");
        Assert.notNull(getPgmSndClassIdList(), "二级分类不能为空");
        Assert.hasText(getDescription(), "描述不能为空");

        if (ObjectUtil.isNotEmpty(getDescription())) {
            if (getDescription().length() > 4000) {
                throw new IllegalArgumentException("描述不能大于4000位");
            }
        }

        Assert.hasText(getLicensingWindowStart(), "授权开始时间不能为空");
        Assert.hasText(getLicensingWindowEnd(), "授权结束时间不能为空");
//        getRating()
        if (ObjectUtil.isNotEmpty(getRating())) {
            if (getRating().length() > 3) {
                throw new IllegalArgumentException("评分长度不能大于3位");
            }
        }
    }

    //子集基础参数校验
    private void validSubset() {
        Assert.hasText(getName(), "节目名称不能为空");
        if (ObjectUtil.isNotEmpty(getName())) {
            if (getName().length() > 128) {
                throw new IllegalArgumentException("媒资名称不能大于128位");
            }
        }

        if (ObjectUtil.isEmpty(getEpisodeIndex())) {
            throw new IllegalArgumentException("集数不能为空");
        }
        if (ObjectUtil.isNotEmpty(getEpisodeIndex())) {
            if (getEpisodeIndex().equals(0)) {
                throw new IllegalArgumentException("集数不能0");
            }
        }
        Assert.notNull(getCpId(), "所属CP不能为空");
        Assert.hasText(getDescription(), "节目描述不能为空");
        if (ObjectUtil.isNotEmpty(getDescription())) {
            if (getDescription().length() > 4000) {
                throw new IllegalArgumentException("描述不能大于4000位");
            }
        }
    }

    /**
     * 判断参数可以为空 但是不能为空字符串
     *
     * @param param   校验参数
     * @param message 返回信息
     */
    public static void check(String param, String message) {
        String result = StringUtils.deleteWhitespace(param);
        if ("".equals(result)) {
            throw new IllegalArgumentException(message + "不能为空格");
        }
    }
}
