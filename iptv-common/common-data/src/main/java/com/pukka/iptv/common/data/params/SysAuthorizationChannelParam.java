package com.pukka.iptv.common.data.params;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.NoArgsConstructor;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import java.util.Date;
import lombok.Data;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * TODO
 *
 * <AUTHOR>
 * @date 2021/9/3 17:04
 */
@Data
@AllArgsConstructor
@JsonIgnoreProperties(value = { "handler" })
@NoArgsConstructor
@ApiModel("引入授权频道参数")
public class SysAuthorizationChannelParam implements Serializable {
    @ApiModelProperty(value = "频道id集合,用(,)分隔",name = "ids")
    private String ids;
    @ApiModelProperty(value = "要授权的cpId",name = "cpId")
    private Long cpId;
    @ApiModelProperty(value = "要授权的spId",name = "spId")
    private Long spId;
    @ApiModelProperty(value = "要授权的sp名称",name = "spName")
    private String spName;
    @ApiModelProperty(value = "发布通道ID，多个ID以英文逗号隔开", dataType = "String", name = "outPassageIds")
    private String outPassageIds;
    @ApiModelProperty(value = "分发通道名称以英文逗号 隔开", dataType = "String", name = "outPassageNames")
    private String outPassageNames;
}
