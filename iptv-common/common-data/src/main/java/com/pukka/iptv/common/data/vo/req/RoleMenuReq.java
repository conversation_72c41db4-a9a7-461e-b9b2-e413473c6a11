package com.pukka.iptv.common.data.vo.req;

import lombok.Data;

import javax.validation.constraints.NotNull;
import java.util.HashMap;

/**
 * 角色菜单权限
 * <AUTHOR>
 */
@Data
public class RoleMenuReq {

    @NotNull(message = "角色id不能为空")
    private Long roleId;

    /**
     * 角色名称
     */
    private String roleName;

    /**
     * key:menu_id
     * value:control对象
     */
    private HashMap<String,String> hashMap = new HashMap<>();
}
