package com.pukka.iptv.common.data.params;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * @ClassName SysAuthorizationCell
 * @Description TODO
 * <AUTHOR>
 * @Date 2022/4/27 10:23
 * @Version
 */
@Data
public class SysAuthorizationCell implements Serializable {
    /**
     * id
     */
    private Long id;
    /**
     * 1 已授权  0 未授权
     */
    @ApiModelProperty(value = "1 已授权  0 未授权",name = "authorize")
    private Integer authorize;

    @ApiModelProperty(value = "0-单集,1-剧集",name = "seriesFlag")
    private Integer seriesFlag;
}
