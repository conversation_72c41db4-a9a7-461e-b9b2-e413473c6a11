package com.pukka.iptv.common.data.vo.req;

import com.baomidou.mybatisplus.annotation.TableField;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.pukka.iptv.common.data.dto.OutSpDto;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.Max;
import javax.validation.constraints.Min;
import javax.validation.constraints.Pattern;
import java.io.Serializable;
import java.util.List;

import static com.pukka.iptv.common.data.util.RegexUtil.ALL_CHAR_REGEX;

/**
 * @ClassName BmsContentQueryReqBySP
 * @Description TODO
 * <AUTHOR>
 * @Date 2022/4/14 9:24
 * @Version
 */
@Data
@JsonIgnoreProperties(ignoreUnknown = true)
public class BmsContentQueryReqBySP implements Serializable {

    private Long id;

    @Pattern(regexp = ALL_CHAR_REGEX + "{0,5000}")
    @ApiModelProperty(value = "name", dataType = "String", name = "媒资名称")
    private String name;

    @ApiModelProperty(value="媒资类型,0-单集 1-剧集",example = "1",name = "seriesFlag")
    private Integer seriesFlag;

    /**
     * 媒资分类，节目形态，如：新闻，电影
     */
    @TableField(value = "pgm_category")
    @ApiModelProperty(value = "媒资分类，节目形态，如：新闻，电影", dataType = "String", name = "pgmCategory")
    private String pgmCategory;

    @Min(0)
    @Max(4)
    @ApiModelProperty(value = "op审核 1：op未审核 2：审核中 3：审核通过 4：审核未通过", dataType = "Integer", name = "opCheckStatus")
    private Integer opCheckStatus;

    /**
     * SP名称
     */
    @TableField(value = "sp_name")
    @ApiModelProperty(value = "SP名称", dataType = "String", name = "spName")
    private String spName;

    /**
     * CP名称
     */
    @TableField(value = "cp_name")
    @ApiModelProperty(value = "CP名称", dataType = "String", name = "cpName")
    private String cpName;


    /**
     * 剩余天数
     */
    @TableField(value = "display_as_last_chance")
    @ApiModelProperty(value = "剩余天数 ", dataType = "Long", name = "displayAsLastChance")
    private Long displayAsLastChance;

    /**
     * 有效开始时间（YYYYMMDDHH24MiSS）
     */
    @TableField(value = "licensing_window_start")
    @ApiModelProperty(value = "有效开始时间（YYYYMMDDHH24MiSS）", dataType = "String", name = "licensingWindowStart")
    private String licensingWindowStart;
    /**
     * 有效结束时间（YYYYMMDDHH24MiSS）
     */
    @TableField(value = "licensing_window_end")
    @ApiModelProperty(value = "有效结束时间（YYYYMMDDHH24MiSS）", dataType = "String", name = "licensingWindowEnd")
    private String licensingWindowEnd;


    private List<OutSpDto> spNameList;

    /**
     * 1 已授权  0 未授权
     */
    private Integer authorize;

    /**
     * 清晰度
     */
    private Integer definitionFlag;
    /**
     * 原名
     */
    private String originalName;
}
