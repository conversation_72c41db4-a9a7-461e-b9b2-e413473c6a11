package com.pukka.iptv.common.data.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
@ApiModel("内容源片关系实体")
public class ContentResourceDto {

    @ApiModelProperty(value="所属CP",dataType="Long",name="cpId")
    private Long cpId;

    @ApiModelProperty(value="节目ID，有可能来自于program，series，channel等表，需指定类型才可用",dataType="Long",name="contentId")
    private Long contentId;

    @ApiModelProperty(value="resource源片Id",dataType="Long",name="resourceId")
    private Long resourceId;

    @ApiModelProperty(value="内容类型",dataType="Integer",name="contentType")
    private Integer contentType;

    @ApiModelProperty(value="",dataType="String",name="contentId")
    private String contentCode;

    @ApiModelProperty(value="",dataType="String",name="resourceCode")
    private String resourceCode;

    @ApiModelProperty(value="视频类型，1：正片，2：预览片",dataType="Integer",name="resourceType")
    private Integer resourceType;

    @ApiModelProperty(value = "关联状态", dataType = "Integer", name = "releaseStatus")
    private Integer releaseStatus;

    @ApiModelProperty(value = "节目名称", dataType = "String", name = "contentName")
    private String contentName;
}
