package com.pukka.iptv.common.data.util;

import java.util.Optional;

/**
 * @PACKAGE_NAME: Utility.Tooles.Tuples
 * @NAME: Tuple4 四元数组
 * @USER: wangbo
 * @DATE: 2021/9/06
 **/

public class Tuple2<A, B> extends Tuple {
    private A a;
    private B b;

    public Tuple2(A a, B b) {
        this.a = a;
        this.b = b;
    }

    @Override
    public Optional<A> getA() {
        return Optional.ofNullable(a);
    }

    @Override
    public Optional<B> getB() {
        return Optional.ofNullable(b);
    }

    @Override
    public <C> Optional<C> getC() {
        return Optional.empty();
    }
    @Override
    public <D> Optional<D> getD() {
        return Optional.empty();
    }

    @Override
    public String toString() {
        return "Tuple2{" + "a=" + a + ", b=" + b + '}';
    }
}
