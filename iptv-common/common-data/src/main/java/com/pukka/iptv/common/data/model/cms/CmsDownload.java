package com.pukka.iptv.common.data.model.cms;

import com.baomidou.mybatisplus.annotation.*;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.NoArgsConstructor;
import lombok.EqualsAndHashCode;
import lombok.Data;

import java.util.Date;

/**
 *
 * @author: liuli
 * @date: 2021年10月22日 下午3:56:03
 */

@Data
@EqualsAndHashCode
@JsonIgnoreProperties(value = { "handler" })
@NoArgsConstructor
@TableName(value = "cms_download",autoResultMap=true)
public class CmsDownload extends Model<CmsDownload> implements java.io.Serializable{

    private static final long serialVersionUID = 1L;
	/**id*/
	@TableField(value = "id")
    @TableId(type = IdType.AUTO)
    @ApiModelProperty(value="id",dataType="Long",name="id")
    private Long id;
	/**优先级*/
	@TableField(value = "priority")
    @ApiModelProperty(value="优先级",dataType="Integer",name="priority")
    private Integer priority;
	/**文件名*/
	@TableField(value = "name")
    @ApiModelProperty(value="文件名",dataType="String",name="name")
    private String name;
	/**下载内容唯一标识，当type=1时，为resource_code，当type=2时为cms_picture code*/
	@TableField(value = "cms_content_code")
    @ApiModelProperty(value="下载内容唯一标识，当type=1时，为resource_code，当type=2时为cms_picture code",dataType="String",name="cmsContentCode")
    private String cmsContentCode;
	/**1.视频  2.图片*/
	@TableField(value = "type")
    @ApiModelProperty(value="1.视频  2.图片",dataType="Integer",name="type")
    private Integer type;
	/**文件大小单位兆M*/
	@TableField(value = "file_size")
    @ApiModelProperty(value="文件大小单位兆M",dataType="Integer",name="fileSize")
    private Integer fileSize;
	/**所属节目名称*/
	@TableField(value = "content_name")
    @ApiModelProperty(value="所属节目名称",dataType="String",name="contentName")
    private String contentName;
	/**状态 1.待下载 2.下载中 3.下载成功 4.下载失败*/
	@TableField(value = "status")
    @ApiModelProperty(value="状态 1.待下载 2.下载中 3.下载成功 4.下载失败 5.添加下载任务失败",dataType="Integer",name="status")
    private Integer status;
	/**描述*/
	@TableField(value = "description")
    @ApiModelProperty(value="描述",dataType="String",name="description")
    private String description;
	/**入库地址*/
	@TableField(value = "target_address")
    @ApiModelProperty(value="入库地址",dataType="String",name="targetAddress")
    private String targetAddress;
	/**源地址*/
	@TableField(value = "source_address")
    @ApiModelProperty(value="源地址",dataType="String",name="sourceAddress")
    private String sourceAddress;
	/**createTime*/
    @TableField(value = "create_time", fill = FieldFill.INSERT)
    @ApiModelProperty(value = "创建时间", dataType = "Date", name = "createTime")
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date createTime;
    /**
     * 更新时间
     */
    @TableField(value = "update_time", fill = FieldFill.UPDATE)
    @ApiModelProperty(value = "更新时间", dataType = "Date", name = "updateTime")
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date updateTime;
	/**注入通道名称*/
	@TableField(value = "in_passage_name")
    @ApiModelProperty(value="注入通道名称",dataType="String",name="inPassageName")
    private String inPassageName;
	/**注入通道id*/
	@TableField(value = "in_passage_id")
    @ApiModelProperty(value="注入通道id",dataType="Long",name="inPassageId")
    private Long inPassageId;
	/**creatorId*/
	@TableField(value = "creator_id")
    @ApiModelProperty(value="creatorId",dataType="Long",name="creatorId")
    private Long creatorId;
	/**创建人*/
	@TableField(value = "creator_name")
    @ApiModelProperty(value="创建人",dataType="String",name="creatorName")
    private String creatorName;
	/**请求参数*/
	@TableField(value = "call_back_url")
    @ApiModelProperty(value="回调地址",dataType="String",name="param")
    private String callBackUrl;
	/**存储id sys_storage*/
	@TableField(value = "storage_id")
    @ApiModelProperty(value="存储id sys_storage",dataType="Long",name="storageId")
    private Long storageId;

    /**是否使用代理 0:不使用代理 1:使用代理*/
    @TableField(value = "proxy_flag")
    @ApiModelProperty(value="是否使用代理 0:不使用代理 1:使用代理",dataType="int",name="proxyFlag")
	private Integer proxyFlag;

    @TableField(value = "cp_id")
    @ApiModelProperty(value="cpid",dataType="Long",name="cpId")
    private Long cpId;

    /**cpName*/
    @TableField(value = "cp_name")
    @ApiModelProperty(value="cpName",dataType="String",name="cpName")
    private String cpName;
}
