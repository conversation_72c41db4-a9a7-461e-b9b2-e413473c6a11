package com.pukka.iptv.common.data.vo.req;

import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;
import org.springframework.util.Assert;

import javax.validation.constraints.Max;
import javax.validation.constraints.Min;
import javax.validation.constraints.NotEmpty;
import java.util.List;

/**
 * <AUTHOR>
 * @create 2021-09-03 15:57
 */
@Getter
@Setter
@Accessors(chain = true)
public class BmsCategoryOperationReq {
    @ApiModelProperty(value = "id集合")
    @NotEmpty
    private List<Long> idList;

    @ApiModelProperty(value = "锁定状态 1：未锁定 2：锁定")
    @Min(1)
    @Max(2)
    private Integer lockStatus;

    @ApiModelProperty(value = "状态 0：失效 1：生效")
    @Min(0)
    @Max(1)
    private Integer status;

    @ApiModelProperty(value = "发布状态 1:待发布 2:发布中 3:发布成功 4:发布失败 5:待更新 6:更新中 7:更新失败 8:回收中 9:回收成功 10:回收失败")
    @Min(1)
    @Max(10)
    private Integer publishStatus;

    @ApiModelProperty(value = "true: 修改并发布  false: 只修改不发布")
    private Boolean needPublish;

    // 批量锁定/解锁 校验
    public void validBatchLock() { Assert.notNull(lockStatus, "锁定状态不能为null"); }

    // 生效/失效 校验
    public void validStatus() {
        Assert.notNull(needPublish, "needPublish不能为null");
        Assert.notNull(status, "状态不能为null");
    }

    // 修改发布状态 校验
    public void validPublish() {
        Assert.notNull(publishStatus, "发布状态不能为null");
    }

}

