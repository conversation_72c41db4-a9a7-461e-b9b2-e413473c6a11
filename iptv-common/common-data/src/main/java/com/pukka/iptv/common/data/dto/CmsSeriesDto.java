package com.pukka.iptv.common.data.dto;

import cn.hutool.core.util.ObjectUtil;
import com.pukka.iptv.common.base.enums.RequestResourceEnum;
import com.pukka.iptv.common.base.enums.SourceEnum;
import com.pukka.iptv.common.data.model.cms.CmsSeries;
import com.pukka.iptv.common.data.model.in.InSeries;
import lombok.Data;
import org.springframework.beans.BeanUtils;
import org.springframework.util.Assert;

import java.io.Serializable;
import java.util.List;

@Data
public class CmsSeriesDto extends CmsSeries implements Serializable {

    private List<CmsPictureDto> picArray;
    //正片id
    private String movIds;
    //预览片id
    private String previewIds;

    private String ids;

    //2级分类idList
    private List<String> pgmSndClassIdList;

    private List<String> ctcc;
    private List<String> cmcc;
    private List<String> cucc;

    private String timeStart;
    private String timeEnd;

    private String cspId;

    //之前的剧集类型
    private Integer upType;

    // 这个字段的含义是调用这个接口的场景  1 表示是后台定时任务调用  2 表示是前端页面调用
    private Integer requestResource;

    public void validRequestResource() {
        Assert.notNull(requestResource, "请选择人工创建或专线创建");
    }

    // 剧集新增参数校验
    public void validSeriesSave() {
        if (requestResource.equals(RequestResourceEnum.SYSTEM.getCode())) {
            validSeries();
            setName(getName().trim());
        }
    }


    // 剧集编辑参数校验
    public void validSeriesUpdate() {
        if (requestResource.equals(RequestResourceEnum.SYSTEM.getCode())) {
            Assert.notNull(getId(), "节目ID不能为空");
            validSeries();
            setName(getName().trim());
        }
    }

    public CmsSeriesDto(CmsSeries cmsSeries) {
        BeanUtils.copyProperties(cmsSeries, this, "id", "createTime", "updateTime");
    }

    public CmsSeriesDto(InSeries inSeries) {
        BeanUtils.copyProperties(inSeries, this, "id", "createTime", "updateTime");
        this.setSource(SourceEnum.SYSWORK.getValue());
    }

    public CmsSeriesDto() {
    }

    //剧集基础参数校验
    private void validSeries() {
        Assert.hasText(getName(), "媒资名称不能为空");
        Assert.notNull(getCpId(), "所属CP不能为空");
        Assert.hasText(getContentProvider(), "内容提供商不能为空");
        Assert.notNull(getPgmCategoryId(), "一级分类不能为空");
        Assert.notNull(getPgmSndClassIdList(), "二级分类不能为空");
        Assert.hasText(getDescription(), "描述不能为空");
        Assert.hasText(getLicensingWindowStart(), "授权开始时间不能为空");
        Assert.hasText(getLicensingWindowEnd(), "授权结束时间不能为空");
        //        getRating()
        if (ObjectUtil.isNotEmpty(getName())) {
            if (getName().length() > 128) {
                throw new IllegalArgumentException("媒资名称不能大于128位");
            }
        }
        if (ObjectUtil.isNotEmpty(getRating())) {
            if (getRating().length() > 3) {
                throw new IllegalArgumentException("评分长度不能大于三位");
            }
        }
    }
}
