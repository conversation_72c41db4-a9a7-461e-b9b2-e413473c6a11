package com.pukka.iptv.common.data.vo.req;

import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.util.List;
import java.util.Map;

/**
 * @Author: wangbo
 * @Date: 2022/6/2 15:29
 */
@Getter
@Setter
@Accessors(chain = true)
public class UpdateContentDetailReq {
    /**
     * 媒资唯一标识code
     */
    @NotEmpty
    List<String> codes;
    /**
     * 需要修改的字段
     */
    @NotEmpty
    Map<String, Object> parameterMap;
    /**
     * 1 单集  2 剧集
     */
    @NotNull
    Integer type;
}
