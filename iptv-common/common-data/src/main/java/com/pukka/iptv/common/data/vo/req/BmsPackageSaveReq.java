package com.pukka.iptv.common.data.vo.req;

import com.pukka.iptv.common.base.enums.PackagePriceTypeEnum;
import com.pukka.iptv.common.data.model.bms.BmsPackage;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;
import org.springframework.util.Assert;

import javax.validation.constraints.Max;
import javax.validation.constraints.Min;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @create 2021-09-09 16:18
 */
@Getter
@Setter
@Accessors(chain = true)
public class BmsPackageSaveReq extends BmsPackage{
    @ApiModelProperty(value = "产品包名称")
    @NotBlank
    private String name;

    @ApiModelProperty(value = "spId")
    @NotNull
    private Long spId;

    @ApiModelProperty(value = "产品包类型 0:VOD  1:PVOD   2:Channel 4:SVOD  99:Mix(待定义")
    @Min(0)
    @Max(99)
    @NotNull
    private Integer type;

    @ApiModelProperty(value = "资费类型 0：免费 1：单月包 2：续月包3：单季包4：续季包5：半年单包6：年单包7：单点收费")
    @Min(0)
    @Max(7)
    @NotNull
    private Integer priceType;

    @ApiModelProperty(value = "定价")
    private BigDecimal price;

    @ApiModelProperty(value = "描述信息")
    private String description;

    @ApiModelProperty(value = "生效时间")
    @NotBlank
    private String licensingWindowStart;

    @ApiModelProperty(value = "失效时间")
    @NotBlank
    private String licensingWindowEnd;

    @ApiModelProperty(value = "状态标志  0:失效 1:生效(默认有效)")
    @NotNull
    private Integer status;

    @ApiModelProperty(value = "来源1：专线注入 2：人工创建")
    @NotNull
    @Min(1)
    @Max(2)
    private Integer source;

    // 校验价格
    public void validPrice() {
        // Valid做了priceType非空校验 非免费包，价格不能小于0
        if(PackagePriceTypeEnum.FREE.getCode() != priceType){
            Assert.state( price != null && price.signum() > 0, "定价不能小于0");
        } else {
            Assert.isNull(price, PackagePriceTypeEnum.FREE.getValue()+" 不可设置价格");
        }
    }

    // 校验时间
    public void validTime() {
        // Valid已做非空校验
        // 2021-11-17 15:03:15  长度为14
        Assert.state(licensingWindowStart.length() + licensingWindowEnd.length() == 28, "时间格式错误");
    }

}
