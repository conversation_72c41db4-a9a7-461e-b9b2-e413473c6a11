package com.pukka.iptv.common.data.params;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.NoArgsConstructor;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * TODO 操作员修改参数
 *
 * <AUTHOR>
 * @date 2021/12/1 15:12
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@JsonIgnoreProperties(value = { "handler" })
@ApiModel("查询参数")
public class SysUserParam implements Serializable {

    @ApiModelProperty(value="用户id",dataType="Long",name="userId")
    private Long userId;

    @ApiModelProperty(value = "原始密码",dataType="String",example = "111",name = "oldPwd")
    private String oldPwd;

    @ApiModelProperty(value = "新密码",dataType="String",example = "111",name = "oldPwd")
    private String pwd;

}
