package com.pukka.iptv.common.data.model.order;

import com.pukka.iptv.common.base.constant.ObjectsTypeConstants;
import com.pukka.iptv.common.base.constant.TextConstants;
import com.pukka.iptv.common.base.enums.ActionEnums;
import com.pukka.iptv.common.data.model.in.InMovie;
import com.pukka.iptv.common.data.util.SafeUtil;
import lombok.Getter;
import lombok.Setter;

import java.util.HashMap;

/**
 * <AUTHOR>
 * @Date 2021/9/3 11:40
 * @description: 视频介质信息
 * @Version 1.0
 */
@Getter
@Setter
public class MovieObjectEntity extends SubOrderObjectsEntity{

    public MovieObjectEntity(InMovie inMovie){
        setPropertyDic(new HashMap<>());
        this.setAction(ActionEnums.getInfoByCode(inMovie.getAction()));
        this.setCode(inMovie.getCode());
        this.setId(inMovie.getCode());
        this.setElementType(ObjectsTypeConstants.MOVIE);
        getPropertyDic().put("Type", SafeUtil.getString(inMovie.getType()));
        getPropertyDic().put("FileURL", inMovie.getFileUrl());
        getPropertyDic().put("SourceDRMType", SafeUtil.getString(inMovie.getSourceDrmType()));
        getPropertyDic().put("DestDRMType", SafeUtil.getString(inMovie.getDestDrmType()));
        getPropertyDic().put("AudioType", SafeUtil.getString(inMovie.getAudioType()));
        getPropertyDic().put("ScreenFormat", SafeUtil.getString(inMovie.getScreenFormat()));
        getPropertyDic().put("ClosedCaptioning", SafeUtil.getString(inMovie.getClosedCaption()));
        getPropertyDic().put("MediaSpec", inMovie.getMediaSpec());
        getPropertyDic().put("BitRateType", SafeUtil.getString(inMovie.getBirRateType()));
        getPropertyDic().put("MovieHeadDuration", SafeUtil.getString(inMovie.getMovieHeadDuration()));
        getPropertyDic().put("MovieTailDuration", SafeUtil.getString(inMovie.getMovieTailDuration()));
        if (inMovie.getFileUrl().contains(TextConstants.M3U8)) {
            getPropertyDic().put("BizDomain", "2");
        } else {
            getPropertyDic().put("BizDomain", "0");
        }

    }

    public MovieObjectEntity(){

    }

}
