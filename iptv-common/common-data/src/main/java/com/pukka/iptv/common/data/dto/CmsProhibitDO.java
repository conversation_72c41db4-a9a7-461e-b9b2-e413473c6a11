package com.pukka.iptv.common.data.dto;

import com.pukka.iptv.common.data.model.copyright.CmsProhibit;
import lombok.Data;

import java.io.Serializable;

/**
 * @ClassName CmsProgramDO
 * @Description TODO
 * <AUTHOR>
 * @Date 2022/3/23 11:02
 * @Version
 */
@Data
public class CmsProhibitDO extends CmsProhibit implements Serializable {

    /**
     * 当包含换行符时，是多名称查询
     */
    private String[] nameList;

    private Integer current;

    private Integer size;

    /**
     * 模糊精确查询标志
     */
    private Integer likeOrinFlag;
}
