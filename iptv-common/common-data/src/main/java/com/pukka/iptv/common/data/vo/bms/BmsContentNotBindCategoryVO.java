package com.pukka.iptv.common.data.vo.bms;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.NoArgsConstructor;
import com.pukka.iptv.common.data.model.bms.BmsContent;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.util.Date;

/**
 * 内容表，不包含子集
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2021-08-27 14:54:25
 */
@Getter
@Setter
@JsonIgnoreProperties(value = {"handler"})
@ApiModel("BmsContentVo")
public class BmsContentNotBindCategoryVO implements Serializable {

    /**
     * 主键
     */
    @TableField(value = "id")
    @TableId(type = IdType.AUTO)
    @ApiModelProperty(value = "主键", dataType = "Long", name = "id")
    private Long id;

    /**
     * 节目名称
     */
    @TableField(value = "name")
    @ApiModelProperty(value = "节目名称", dataType = "String", name = "name")
    private String name;

    /**
     * cpId
     */
    @TableField(value = "cp_id")
    @ApiModelProperty(value = "cpId", dataType = "Long", name = "cpId")
    private Long cpId;
    /**
     * CP名称
     */
    @TableField(value = "cp_name")
    @ApiModelProperty(value = "CP名称", dataType = "String", name = "cpName")
    private String cpName;

    /**
     * 1：电影 3：电视剧 4：系列片 5：片花 此处没有子集，子集由program表单独保存
     */
    @TableField(value = "content_type")
    @ApiModelProperty(value = "1：电影 3：电视剧 4：系列片 5：片花 此处没有子集，子集由program表单独保存", dataType = "Integer", name = "contentType")
    private Integer contentType;
    /**
     * op审核 1：op未审核 2：审核中 3：审核通过 4：审核未通过
     */
    @TableField(value = "op_check_status")
    @ApiModelProperty(value = "op审核 1：op未审核 2：审核中 3：审核未通过 4：审核通过", dataType = "Integer", name = "opCheckStatus")
    private Integer opCheckStatus;

    /**
     * 发布状态  1：待发布 2：发布中 3：发布成功 4：发布失败 5：待更新 6：更新中 7：更新失败 8：回收中 9：回收成功 10：回收失败
     */
    @TableField(value = "publish_status")
    @ApiModelProperty(value = "发布状态  1：待发布 2：发布中 3：发布成功 4：发布失败 5：待更新 6：更新中 7：更新失败 8：回收中 9：回收成功 10：回收失败", dataType = "Integer", name = "publishStatus")
    private Integer publishStatus;

    /**
     * 媒资分类，节目形态，如：新闻，电影
     */
    @TableField(value = "pgm_category")
    @ApiModelProperty(value = "媒资分类，节目形态，如：新闻，电影", dataType = "String", name = "pgmCategory")
    private String pgmCategory;
    /**
     * 二级标签，如：动作，科幻
     */
    @TableField(value = "pgm_snd_class")
    @ApiModelProperty(value = "二级标签，如：动作，科幻", dataType = "String", name = "pgmSndClass")
    private String pgmSndClass;
    /**
     * 创建时间
     */
    @TableField(value = "create_time")
    @ApiModelProperty(value = "创建时间", dataType = "String", name = "createTime")
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date createTime;
    /**
     * 更新时间
     */
    @TableField(value = "update_time")
    @ApiModelProperty(value = "更新时间", dataType = "String", name = "updateTime")
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date updateTime;

    @TableField(value = "original_name")
    @ApiModelProperty(value = "原名", dataType = "String", name = "originalName")
    private String originalName;
}
