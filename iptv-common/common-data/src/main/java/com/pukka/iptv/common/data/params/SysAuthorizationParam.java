package com.pukka.iptv.common.data.params;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.NoArgsConstructor;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;

import java.util.Arrays;
import lombok.Data;
import lombok.ToString;
import org.apache.commons.lang3.StringUtils;

import javax.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * TODO 查询合同列表参数
 *
 * <AUTHOR>
 * @date 2021/8/30 15:36
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@JsonIgnoreProperties(value = { "handler" })
@ApiModel("查询参数")
@ToString
public class SysAuthorizationParam implements Serializable {

    @ApiModelProperty(value = "媒资名称",example = "小猪佩奇",name = "name")
    private String name;

    @ApiModelProperty(value = "别名",example = "小猪佩奇",name = "originalName")
    private String originalName;

    @ApiModelProperty(value = "code",example = "1",name = "code")
    private String code;

    @ApiModelProperty(value = "清晰度",example = "1",name = "definitionFlag")
    private Integer definitionFlag;

    @ApiModelProperty(value = "用户账号",example = "admin",name = "userName")
    private String userName;

    @ApiModelProperty(value = "频道号",example = "111",name = "channelNumber")
    private String channelNumber;

    @ApiModelProperty(value="op终审状态,0未审核 1审核未通过 2审核通过",example = "1",name = "opCheckStatus")
    private Integer opCheckStatus;

    @ApiModelProperty(value="发布状态,0未审核 1审核未通过 2审核通过",example = "1",name = "opCheckStatus")
    private Integer publishStatus;

    @ApiModelProperty(value="当前选择的CP",dataType="Long",name="cpId")
    private Long cpId;

    @ApiModelProperty(value="当前选择的SP",dataType="Long",name="cpId")
    private Long spId;

    @ApiModelProperty(value="媒资类型,0-单集 1-剧集",example = "1",name = "seriesFlag")
    @NotNull
    private Integer seriesFlag;

    @ApiModelProperty(value = "媒资分类id",example = "111111",name = "pgmCategoryId")
    private Long pgmCategoryId;

    @ApiModelProperty(value = "发布通道ID", dataType = "String", name = "outPassageId")
    private String outPassageId;

    // 当包含换行符时，是多名称查询
    private String[] nameList;

    public void setName(String name) {
        if (StringUtils.isNotBlank(name) && name.contains("\n")) {
            nameList = name.trim().split("\n");
            return;
        }
        this.name = name;
    }


    public static void main(String[] args) {
        String name = "\n测试 \n ceshi";

        String[] split = name.split("\n");

        System.out.println(Arrays.toString(split));
    }
}
