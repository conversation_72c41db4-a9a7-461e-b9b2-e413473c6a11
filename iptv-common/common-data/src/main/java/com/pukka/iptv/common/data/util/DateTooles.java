package com.pukka.iptv.common.data.util;

import java.text.DateFormat;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.time.LocalDateTime;
import java.time.ZoneOffset;
import java.util.Calendar;
import java.util.Date;

/**
 * @ClassName DateTooles
 * @Description
 * <AUTHOR>
 * @Date 2020-11-4 - 上午 9:20
 */
public class DateTooles
{
    public final static String YYYY_MM_DD_HH_mm_ss ="yyyy-MM-dd HH:mm:ss";
    public final static String YYYYMMDDHHMMSS="yyyyMMddHHmmss";
    public enum DateType {
        /*
        天
        * */
        Day,
        /*
       小时
       * */
        Hour,
        /*
       分钟
       * */
        Min,
        /*
       秒
       * */
        Sec
    }

    /*
     * 根据枚举类型返回时间差
     * */
    public static long getDiffTime(Date startDate, Date endDate, DateType type) {
        long nd = 1000 * 24 * 60 * 60;
        long nh = 1000 * 60 * 60;
        long nm = 1000 * 60;
        long ns = 1000;
        // long ns = 1000;
        // 获得两个时间的毫秒时间差异
        long diff = endDate.getTime() - startDate.getTime();
        // 计算差多少天
        long day = diff / nd;
        // 计算差多少小时
        long hour = diff / nh;
        // 计算差多少分钟
        long min = diff / nm;
        //计算差多少秒
        long sec = diff / ns;
        switch (type) {
            case Day:
                return day;
            case Hour:
                return hour;
            case Min:
                return min;
            case Sec:
                return sec;
            default:
                return day;
        }
    }

    /**
     *
     * 根据枚举类型返回时间差,返回小数位
     * */
    public static Double getDiffTimeToDouble(Date startDate, Date endDate, DateType type) {
        long nd = 1000 * 24 * 60 * 60;
        long nh = 1000 * 60 * 60;
        long nm = 1000 * 60;
        long ns = 1000;
        // long ns = 1000;
        // 获得两个时间的毫秒时间差异
        double diff = endDate.getTime() - startDate.getTime();
        // 计算差多少天
        double day = diff / nd * 1.0;
        // 计算差多少小时
        double hour = diff / nh* 1.0;
        // 计算差多少分钟
        double min = diff / nm* 1.0;
        //计算差多少秒
        double sec = diff / ns* 1.0;
        switch (type) {
            case Day:
                return day;
            case Hour:
                return hour;
            case Min:
                return min;
            case Sec:
                return sec;
            default:
                return day;
        }
    }
    public static Date formatParseDate(String strDate,String pattern){
        if(strDate==null || pattern==null || "".equals(strDate.trim()) ||"".equals(pattern.trim()) ){
            throw new IllegalArgumentException("字符串或日期格式为空");
        }
        Date date = null;
        try{
            SimpleDateFormat parser = new SimpleDateFormat(pattern);
            date=parser.parse(strDate);
        }catch (ParseException e) {
            System.out.println("格式化失败" + e.getMessage());
        }
        return date;
    }
    public static String format(Date date, String pattern) {
        if(date==null){
            return "";
        }
        if (date == null || pattern == null || "".equals(pattern.trim())){
            throw new IllegalArgumentException("日期或字符串格式为空");
        }
        String str = null;
        try {
            SimpleDateFormat parser = new SimpleDateFormat(pattern);
            str = parser.format(date);
        } catch (Exception e) {
            System.out.println("不能转换" + e.getMessage());
        }
        return str;
    }

    public static void main(String[] args) {
        format(YYYY_MM_DD_HH_mm_ss,"20210506112211","");
    }
    public static String format(String str, String oldP, String newP) {
        if (str == null || "".equals(str.trim()) || oldP == null
                || "".equals(oldP.trim()) || newP == null
                || "".equals(newP.trim())){
            throw new IllegalArgumentException("不能为空");
        }
        return format(parseDate(str,oldP),newP);
    }
    /**
     * 设置时间格式返回时间
     */
    public static Date ConvertFormatToTime(String format, Date date) throws ParseException
    {
        SimpleDateFormat df = new SimpleDateFormat(format);
        return df.parse(df.format(date));
    }

    /**
     * 设置时间格式返回时间
     */
    public static Date ConvertFormatToTime(String format, String date) throws ParseException {
        SimpleDateFormat df = new SimpleDateFormat(format);
        return df.parse(date);
    }

    /**
     * 设置时间格式返回字符串
     */
    public static String ConvertFormatToString(String format, Date date) throws ParseException {
        SimpleDateFormat df = new SimpleDateFormat(format);
        return df.format(date);
    }

    /**
     * 获取指定时间对应的毫秒数
     *
     * @param time "HH:mm:ss"
     * @return
     */
    public static long getTimeMillis(String time) {

        try {
            DateFormat dateFormat = new SimpleDateFormat("yy-MM-dd HH:mm:ss");

            DateFormat dayFormat = new SimpleDateFormat("yy-MM-dd");

            Date curDate = dateFormat.parse(dayFormat.format(new Date()) + " " + time);

            return curDate.getTime();
        } catch (ParseException e) {

            e.printStackTrace();

        }

        return System.currentTimeMillis();

    }

    /**
     * 获取当前时间
     */
    public static String getTimeString(String format) {
        SimpleDateFormat df = new SimpleDateFormat(format);
        Calendar calendar = Calendar.getInstance();
        return df.format(calendar.getTime());
    }

    /**
     * 获取日期年份
     *
     * @param date 日期
     * @return
     */
    public static int getYear(Date date) {
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(date);
        return calendar.get(Calendar.YEAR);
    }

    /**
     * 功能描述：返回月
     *
     * @param date Date 日期
     * @return 返回月份
     */
    public static int getMonth(Date date) {
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(date);
        return calendar.get(Calendar.MONTH) + 1;
    }

    /**
     * 功能描述：返回日期
     *
     * @param date Date 日期
     * @return 返回日份
     */
    public static int getDay(Date date) {
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(date);
        return calendar.get(Calendar.DAY_OF_MONTH);
    }

    /**
     * 功能描述：返回小时
     *
     * @param date 日期
     * @return 返回小时
     */
    public static int getHour(Date date) {
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(date);
        return calendar.get(Calendar.HOUR_OF_DAY);
    }

    /**
     * 功能描述：返回分
     *
     * @param date 日期
     * @return 返回分钟
     */
    public static int getMinute(Date date) {
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(date);
        return calendar.get(Calendar.MINUTE);
    }

    /**
     * 返回秒钟
     *
     * @param date Date 日期
     * @return 返回秒钟
     */
    public static int getSecond(Date date) {
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(date);
        return calendar.get(Calendar.SECOND);
    }

    /**
     * 功能描述：返回毫
     *
     * @param date 日期
     * @return 返回毫
     */
    public static long getMillis(Date date) {
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(date);
        return calendar.getTimeInMillis();
    }

    /**
     * 解析字符串日期,不报错  异常返回null
     *
     * @param d
     * @return
     * <AUTHOR>
     * @created 2018-11-27 下午4:30:35
     */
//    public static Date parseDate(String d) {
//        try {
//            return new SimpleDateFormat("yyyy/mm/dd hh24:mi:ss").parse(d);
//        } catch (ParseException e) {
//            return null;
//        }
//    }

    /**
     * 时间增加天数
     *
     * @param dt
     * @param day
     * @return 返回时间字符串格式
     */
    public static String addDay(Date dt, int day, String format) {
        SimpleDateFormat df = new SimpleDateFormat(format);
        Calendar c = Calendar.getInstance();
        c.setTime(dt);
        c.add(Calendar.DAY_OF_MONTH, day);
        return df.format(c.getTime());
    }

    /**
     * 时间增加天数
     *
     * @param dt
     * @param day
     * @return
     */
    public static Date addDay(Date dt, int day) {
        Calendar c = Calendar.getInstance();
        c.setTime(dt);
        c.add(Calendar.DAY_OF_MONTH, day);
        return c.getTime();
    }

    /**
     * 时间增加小时
     *
     * @param dt
     * @param hours
     * @return
     */
    public static Date addHours(Date dt, int hours) {
        Calendar c = Calendar.getInstance();
        c.setTime(dt);
        c.add(Calendar.HOUR, hours);
        return c.getTime();
    }

    /**
     * 时间增加秒
     *
     * @param dt
     * @param s
     * @return
     */
    public static Date addSecond(Date dt, int s) {
        Calendar c = Calendar.getInstance();
        c.setTime(dt);
        c.add(Calendar.SECOND, s);
        return c.getTime();
    }

    /**
     * 解析字符串日期,不报错  异常返回null
     *
     * @param d
     * @param format
     * @return
     * <AUTHOR>
     * @created 2018-11-27 下午4:30:35
     */
    public static Date parseDate(String d, String format) {
        try {

            return new SimpleDateFormat(format).parse(d);
        } catch (ParseException e) {
            return null;
        }
    }
    /**
     * 解析字符串日期,不报错  异常返回null
     *
     * @param d
     * @param format
     * @return
     * <AUTHOR>
     * @created 2018-11-27 下午4:30:35
     */
    public static String parseStr(Date d, String format) {
        try {
            return new SimpleDateFormat(format).format(d);
        } catch (Exception e) {
            return null;
        }
    }


    /**
     * 解析对象到日期
     *
     * @param o
     * @return
     * <AUTHOR>
     * @created 2018-11-27 下午4:08:10
     */
    public static Date parseDate(Object o) {
        if (o == null) {
            return null;
        }
        if (o instanceof Date) {
            return (Date) o;
        }
        if (o instanceof java.sql.Date) {
            return (Date) o;
        }
        if (o instanceof String) {
            // yyyy-MM-dd HH:mm:ss  /
            String d = (String) o;
            StringBuilder format = new StringBuilder("yyyy");
            if (d.charAt(4) == '-') {
                format.append("-MM-dd");
            } else if (d.charAt(4) == '/') {
                format.append("/MM/dd");
            } else if (d.charAt(4) == '_') {
                format.append("_MM_dd");
            } else {
                format.append("MMdd");
            }
            if (d.length() < format.length()) {
                return null;
            } else if (d.length() == format.length()) {
                return parseDate(d, format.toString());
            }
            if (d.charAt(format.length()) == ' ') {
                format.append(' ');
            }
            if (d.charAt(format.length() + 2) == ':') {
                format.append("HH:mm:ss");
            } else if (d.charAt(format.length() + 2) == '/') {
                format.append("HH/mm/ss");
            } else {
                format.append("HHmmss");
            }
            if (d.length() < format.length()) {
                return null;
            }
            if (d.length() == format.length()) {
                return parseDate(d, format.toString());
            }
            if (d.charAt(format.length()) == '.' && d.length() == (format.length() + 4)) {
                format.append(".SSS");
            } else if (d.length() == (format.length() + 3)) {
                format.append("SSS");
            } else {
                d = d.substring(0, format.length());
            }
            return parseDate(d, format.toString());
        }
        if (o instanceof Long) {
            long l = (long) o;
            if (l < 10000000000l) {
                return new Date(l * 1000);
            }
            return new Date(l);
        }
        if (o instanceof Integer) {
            long l = (int) o * 1000;
            return new Date(l);
        }
        return null;
    }

    /**
     * LocalDateTime 转Date
     *
     * @param dt
     * @return
     */
    public static Date LocalDateTimeToDate(LocalDateTime dt) {
        return Date.from(dt.toInstant(ZoneOffset.of("+8")));
    }

    /**
     * Date 转 LocalDateTime
     *
     * @param date
     * @return
     */
    public static LocalDateTime DateToLocalDateTime(Date date) {
        return date.toInstant().atOffset(ZoneOffset.of("+8")).toLocalDateTime();
    }
}
