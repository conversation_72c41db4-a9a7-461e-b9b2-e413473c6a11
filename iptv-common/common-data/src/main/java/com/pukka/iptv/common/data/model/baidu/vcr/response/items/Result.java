package com.pukka.iptv.common.data.model.baidu.vcr.response.items;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.util.List;

/**
 * <AUTHOR>
 * @Description TODO
 * @Date 2024/10/25 10:44
 * @Version V1.0
 **/
@Data
@EqualsAndHashCode
@JsonIgnoreProperties(value = { "handler" })
@NoArgsConstructor
@Accessors(chain = true)
public class Result implements  java.io.Serializable {
    private static final long serialVersionUID = 3902016613156515950L;
    /**
     * 结果类型
     */
    private String type;

    /**
     * 项目列表，包含具体的识别项
     */
    private List<Item> items;
}
