package com.pukka.iptv.common.data.vo.bms;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.NoArgsConstructor;
import com.pukka.iptv.common.data.model.bms.BmsCategoryContent;
import io.swagger.annotations.ApiModel;
import lombok.Getter;
import lombok.Setter;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Pattern;

import static com.pukka.iptv.common.data.util.RegexUtil.ALL_CHAR_REGEX;

/**
 * 栏目
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2021-08-27 14:54:26
 */
@Getter
@Setter
@JsonIgnoreProperties(value = {"handler"})
@ApiModel("BmsCategoryContentVo")
public class BmsCategoryContentVO extends BmsCategoryContent implements java.io.Serializable
{


    /**
     * 频道ids
     */
    @Pattern(regexp = ALL_CHAR_REGEX + "{0,255}")
    @NotBlank
    private String ids;
    /**
     * 栏目ids
     */
    @Pattern(regexp = ALL_CHAR_REGEX + "{0,255}")
    @NotBlank
    private String categoryIds;
}
