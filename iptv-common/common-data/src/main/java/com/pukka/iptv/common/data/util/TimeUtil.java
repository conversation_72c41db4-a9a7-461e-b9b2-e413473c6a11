package com.pukka.iptv.common.data.util;


import cn.hutool.core.date.LocalDateTimeUtil;
import org.springframework.util.StringUtils;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.Calendar;
import java.util.Date;
import java.util.function.Function;

public class TimeUtil extends LocalDateTimeUtil {

    public enum UNIT {
        MILLISECOND, SECOND, MINUTE, HOUR, DAY, WEEK, MONTH, YEAR,
    }

    public final static String STAND_DATETIME_FORMAT = "yyyy-MM-dd HH:mm:ss";
    public final static String UNDERLINE_DATETIME_FORMAT = "yyyy_MM_dd_HH_mm_ss";
    public final static String UNDERLINE_TIME_FORMAT = "HH_mm_ss";
    public final static String STANDER_TIME_FORMAT = "HH:mm:ss";
    public final static String UNDERLINE_DATE_FORMAT = "yyyy_MM_dd";
    private static String[] parsePatterns = {
            "yyyy-MM-dd'T'HH:mm:ss:SSSZZ",
            "yyyy-MM-dd'T'HH:mm:ssZZ",
            "yyyy-MM-dd'T'HH:mm:ss",
            "yyyy-MM-dd", "yyyy-MM-dd HH:mm:ss", "yyyy-MM-dd HH:mm", "yyyy-MM",
            "yyyy/MM/dd", "yyyy/MM/dd HH:mm:ss", "yyyy/MM/dd HH:mm", "yyyy/MM",
            "yyyy.MM.dd", "yyyy.MM.dd HH:mm:ss", "yyyy.MM.dd HH:mm", "yyyy.MM"};

    private final static long timeThreshold = 60000;//60000;//1分钟

    public static int convertTimeUnit(UNIT unit) {
        switch (unit) {
            case MILLISECOND:
                return Calendar.MILLISECOND;
            case SECOND:
                return Calendar.SECOND;
            case MINUTE:
                return Calendar.MINUTE;
            case HOUR:
                return Calendar.HOUR;
            case DAY:
                return Calendar.DAY_OF_YEAR;
            case WEEK:
                return Calendar.WEEK_OF_YEAR;
            case MONTH:
                return Calendar.MONTH;
            case YEAR:
                return Calendar.YEAR;
        }
        return 0;
    }

    public static Calendar addTime(Calendar calendar, int time, UNIT unit) {
        calendar = calendar == null ? Calendar.getInstance() : calendar;
        calendar.add(convertTimeUnit(unit), time);
        return calendar;
    }

    /**
     * @return void
     * @description: 从 start 遍历到 end  如果回调函数返回false 则停止循环 并返回当前循环的时间
     * <AUTHOR>
     * @date 2021/6/28 13:23
     */
    public static Date foreach(Date start, Date end, UNIT unit, int per, Function<Date, Boolean> func) {
        Calendar s = Calendar.getInstance();
        s.setTime(start);
        Calendar e = Calendar.getInstance();
        e.setTime(end);
        while (!s.equals(e)) {
            Boolean continues = func.apply(s.getTime());
            if (!continues) {
                return s.getTime();
            }
            s.add(convertTimeUnit(unit), per);
        }
        func.apply(s.getTime());
        return null;
    }


    /**
     * 比较时间戳
     *
     * @param timeStamp
     * @param date
     * @return 小于 等于true 大于false
     */
    public static boolean compareTimeWithThreshold(long timeStamp, Date date) {
        if (timeStamp + timeThreshold >= date.getTime() && timeStamp - timeThreshold <= date.getTime()) {
            return true;
        }
        return false;
    }

    public static Date getDate(String time, String format) throws ParseException {
        if (StringUtils.isEmpty(time)) {
            return null;
        }

        return new SimpleDateFormat(format).parse(time);
    }

    public static Date getDate(String time) throws ParseException {
        return StringUtils.isEmpty(time) ? null : new SimpleDateFormat(STAND_DATETIME_FORMAT).parse(time);
    }


    public static Boolean same(Date time1, Date time2, UNIT unit) {
        Calendar c1 = Calendar.getInstance();
        Calendar c2 = Calendar.getInstance();
        c2.setTime(time2);
        c1.setTime(time1);
        int u = convertTimeUnit(unit);
        return c1.get(u) == c2.get(u);
    }

    public static Boolean same(Long time1, Long time2, String format) {
        return formatDate(time1, format).equals(formatDate(time2, format));
    }

    public static Boolean same(Date time1, Long time2, String format) {
        return formatDate(time1, format).equals(formatDate(time2, format));
    }

    public static Boolean same(Date time1, Date time2, String format) {
        return formatDate(time1, format).equals(formatDate(time2, format));
    }

    public static int compareTime(Date time1, Date time2) {
        return time1.compareTo(time2);
    }

    public static boolean isBigger(Date time1, Date time2) {
        return time1.compareTo(time2) > 0;
    }

    public static boolean before(Date time1, Date time2) {
        return time1.compareTo(time2) < 0;
    }

    public static String formatDate(Date date) {
        return date == null ? null : new SimpleDateFormat(STAND_DATETIME_FORMAT).format(date);
    }

    public static String formatDate(Date date, String format) {
        if (date == null) {
            return null;
        }
        return new SimpleDateFormat(format).format(date);
    }

    public static String formatDate2Str(Date date) {
        if (date == null) {
            return null;
        }
        return new SimpleDateFormat(STAND_DATETIME_FORMAT).format(date);
    }

    /**
     * 日期型字符串转化为日期 格式
     */
    public static Date parseDate(Object str) {
        if (str == null) {
            return null;
        }
        try {
            return parseDate(str);
        } catch (Exception e) {
            e.printStackTrace();
            return null;
        }
    }

    public static Date formatDateToDate(Date date, String format) {
        if (date == null) {
            return null;
        }
        SimpleDateFormat format1 = new SimpleDateFormat(format);
        String s = format1.format(date);
        try {
            return format1.parse(s);
        } catch (ParseException e) {
            e.printStackTrace();
        }
        return null;
    }

    public static String formatDate(Long timestamp) {
        return new SimpleDateFormat("yyyy_MM_dd_HH_mm_ss").format(new Date(timestamp));
    }

    public static String formatDate(Long timestamp, String format) {
        return new SimpleDateFormat(format).format(new Date(timestamp));
    }

    /**
     * 时间转换为秒
     */
    public static Integer transTimeToSecond(Date date) {
        return date.getHours() * 60 * 60 + date.getMinutes() * 60 + date.getSeconds();
    }


    /**
     * @Description: 获取定时任务表达式
     * @author: wz
     * @date: 2019/7/3 15:59
     */
    public static String getCron(Date date) {
        String dateFormat = "ss mm HH dd MM ? yyyy";
        return new SimpleDateFormat(dateFormat).format(date);
    }

    public static Long diff(Date now, Date last, UNIT unit) {
        long temp = now.getTime() - last.getTime();
        temp = temp < 0 ? -temp : temp;
        if (unit.equals(UNIT.SECOND)) {
            return temp / 1000;
        }
        if (unit.equals(UNIT.MINUTE)) {
            return temp / 1000 / 60;
        }
        if (unit.equals(UNIT.HOUR)) {
            return temp / 1000 / 60 / 60;
        }
        if (unit.equals(UNIT.DAY)) {
            return temp / 1000 / 60 / 60 / 24;
        }
        return 0L;
    }

    public static Long diff(Date start) {
        return new Date().getTime() - start.getTime();
    }

    public static UNIT parseUnit(String str) {
        if (!StringUtils.isEmpty(str)) {
            UNIT[] values = UNIT.values();
            for (UNIT value : values) {
                if (value.name().equalsIgnoreCase(str)) {
                    return value;
                }
            }
        }
        return null;
    }
}
