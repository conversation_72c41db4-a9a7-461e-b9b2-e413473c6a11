package com.pukka.iptv.common.data.vo.cms;

import com.pukka.iptv.common.data.model.cms.CmsDownload;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.pukka.iptv.common.data.model.*;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;
import lombok.Data;

/**
 *
 * @author: liuli
 * @date: 2021年10月22日 下午3:56:03
 */

@Data
@AllArgsConstructor
@JsonIgnoreProperties(value = { "handler" })
@ApiModel("CmsDownloadVo")
public class CmsDownloadVo extends CmsDownload implements java.io.Serializable{

}
