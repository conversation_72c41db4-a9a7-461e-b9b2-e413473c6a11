package com.pukka.iptv.common.data.vo.req;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.pukka.iptv.common.data.model.statistics.StatisticsInInit;
import io.swagger.annotations.ApiModel;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.AllArgsConstructor;
import lombok.Data;

/**
 *
 * @author: tan
 * @date: 2022-7-21 9:54:09
 */

@Data
@JsonIgnoreProperties(value = { "handler" })
@ApiModel("StatisticsInInitVo")
public class StatisticsInInitVo extends Page<StatisticsInInit> implements java.io.Serializable{

    /**
     * 开始时间
     */
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private String startTime;
    /**
     * 结束时间
     */
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private String endTime;
}
