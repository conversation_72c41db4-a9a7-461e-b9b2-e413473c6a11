package com.pukka.iptv.common.data.util;


import java.util.regex.Pattern;


/**
 * 配合@RegexFilter注解使用的正则过滤工具
 *
 * @author: wz
 * @Date: 2018/12/24 11:53
 * @Description:
 */
public class RegexUtil {


    public final static String ALL_CHAR = "\\s\\S";
    public final static String ALL_CHAR_REGEX = "[\\s\\S]";
    public final static String ZH_CN = "\\u4e00-\\u9fa5";
    public final static String NUMBER = "[\\d]";
    public final static String LANGUAGE = "\\w\\u4e00-\\u9fa5";
    public final static String PHONE = "^\\d{11}$";
    public final static String PHONE_CODE = "^\\d{4,6}$";
    public final static String BLANK = "[\\s\\p{Zs}]*";//中文英文空格
    public final static String badWord = "^((?!badWord).)*$";//排除badWord


    public static boolean matches(String regex, String target) {
        return Pattern.matches(regex, target);
    }

    public static String getAllCharRegex(int length) {
        return "^" + ALL_CHAR_REGEX + "{0," + length + "}$";
    }

    public static String getAllCharRegex(int min, int max) {
        return "^" + ALL_CHAR_REGEX + "{" + min + "," + max + "}$";
    }

    public static String getRegex(String regex, int min, int max) {
        return "^" + regex + "{" + min + "," + max + "}$";
    }

}
