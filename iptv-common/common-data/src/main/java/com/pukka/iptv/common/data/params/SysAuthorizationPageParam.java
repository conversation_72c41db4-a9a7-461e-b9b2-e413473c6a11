package com.pukka.iptv.common.data.params;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.NoArgsConstructor;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import java.util.Date;
import lombok.Data;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.NoArgsConstructor;

import javax.validation.constraints.Max;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Pattern;
import javax.validation.constraints.Size;

/**
 * TODO 查询合同列表参数
 *
 * <AUTHOR>
 * @date 2021/8/30 15:36
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@JsonIgnoreProperties(value = { "handler" })
@ApiModel("查询参数")
public class SysAuthorizationPageParam {

    /**
     * 合同号
     */
    @ApiModelProperty(value = "合同号", example = "CP1授权SP1",name = "name")
    private String name;

    /**
     * 是否自动授权 1：是 2：否
     */
    @ApiModelProperty(value = "是否自动授权 1：是 2：否", example = "1",name = "autoAuthorize")
    private Integer autoAuthorize;

}
