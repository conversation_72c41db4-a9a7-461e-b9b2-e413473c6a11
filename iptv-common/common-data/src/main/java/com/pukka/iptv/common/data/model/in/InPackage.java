package com.pukka.iptv.common.data.model.in;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.NoArgsConstructor;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Date;

/**
 *
 * @author: tan
 * @date: 2021-8-24 10:47:07
 */

@Data
@EqualsAndHashCode
@JsonIgnoreProperties(value = { "handler" })
@NoArgsConstructor
@TableName(value = "in_package",autoResultMap=true)
public class InPackage extends Model<InPackage> implements java.io.Serializable{

    private static final long serialVersionUID = 1L;
	/**主键*/
	@TableField(value = "id")
    @TableId(type = IdType.AUTO)
    @ApiModelProperty(value="主键",dataType="Long",name="id")
    private Long id;
	/**全局唯一标识*/
	@TableField(value = "code")
    @ApiModelProperty(value="全局唯一标识",dataType="String",name="code")
    private String code;
	/**产品包名称*/
	@TableField(value = "name")
    @ApiModelProperty(value="产品包名称",dataType="String",name="name")
    private String name;
	/**Package 类型 0: VOD 包 2: Channel 包 4: SVOD 99: Mix(待定义*/
	@TableField(value = "type")
    @ApiModelProperty(value="Package 类型 0: VOD 包 2: Channel 包 4: SVOD 99: Mix(待定义",dataType="Integer",name="type")
    private Integer type;
	/**索引名称供界面排序*/
	@TableField(value = "sort_name")
    @ApiModelProperty(value="索引名称供界面排序",dataType="String",name="sortName")
    private String sortName;
	/**应答文件包含，0: 成功 其他: 错误代码*/
	@TableField(value = "result")
    @ApiModelProperty(value="应答文件包含，0: 成功 其他: 错误代码",dataType="Integer",name="result")
    private Integer result;
	/**应答文件包含，错误描述*/
	@TableField(value = "error_description")
    @ApiModelProperty(value="应答文件包含，错误描述",dataType="String",name="errorDescription")
    private String errorDescription;
	/**搜索名称供界面搜索*/
	@TableField(value = "search_name")
    @ApiModelProperty(value="搜索名称供界面搜索",dataType="String",name="searchName")
    private String searchName;
	/**租用有效期（小时）*/
	@TableField(value = "rental_period")
    @ApiModelProperty(value="租用有效期（小时）",dataType="Integer",name="rentalPeriod")
    private Integer rentalPeriod;
	/**订购编号*/
	@TableField(value = "order_number")
    @ApiModelProperty(value="订购编号",dataType="String",name="orderNumber")
    private String orderNumber;
	/**有效定购开始时间 (YYYYMMDDHH24MiSS)*/
	@TableField(value = "licensing_window_start")
    @ApiModelProperty(value="有效定购开始时间 (YYYYMMDDHH24MiSS)",dataType="String",name="licensingWindowStart")
    private String licensingWindowStart;
	/**有效定购结束时间 (YYYYMMDDHH24MiSS)*/
	@TableField(value = "licensing_window_end")
    @ApiModelProperty(value="有效定购结束时间 (YYYYMMDDHH24MiSS)",dataType="String",name="licensingWindowEnd")
    private String licensingWindowEnd;
	/**定价*/
	@TableField(value = "price")
    @ApiModelProperty(value="定价",dataType="java.math.BigDecimal",name="price")
    private java.math.BigDecimal price;
	/**状态标志 0:失效 1:生效 ，默认有效*/
	@TableField(value = "status")
    @ApiModelProperty(value="状态标志 0:失效 1:生效 ，默认有效",dataType="Integer",name="status")
    private Integer status;
	/**描述信息*/
	@TableField(value = "description")
    @ApiModelProperty(value="描述信息",dataType="String",name="description")
    private String description;
	/**创建时间*/
	@TableField(value = "create_time")
    @ApiModelProperty(value="创建时间",dataType="String",name="createTime")
    @JsonFormat(shape = JsonFormat.Shape.STRING,pattern = "yyyy-MM-dd HH:mm:ss",timezone="GMT+8")
    private Date createTime;
	/**更新时间*/
	@TableField(value = "update_time")
    @ApiModelProperty(value="更新时间",dataType="String",name="updateTime")
    @JsonFormat(shape = JsonFormat.Shape.STRING,pattern = "yyyy-MM-dd HH:mm:ss",timezone="GMT+8")
    private Date updateTime;
	/**动作 1：REGIST，2：UPDATE，3：DELETE*/
	@TableField(value = "action")
    @ApiModelProperty(value="动作 1：REGIST，2：UPDATE，3：DELETE",dataType="Integer",name="action")
    private Integer action;
	/**correlateId*/
	@TableField(value = "correlate_id")
    @ApiModelProperty(value="correlateId",dataType="String",name="correlateId")
    private String correlateId;
    /**extraCode*/
    @TableField(value = "extra_code")
    @ApiModelProperty(value="上游爱上cp传入code2字段，兼容底量数据使用",dataType="String",name="extraCode")
    private String extraCode;
}
