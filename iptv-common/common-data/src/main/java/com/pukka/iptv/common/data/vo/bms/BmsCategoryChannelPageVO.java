package com.pukka.iptv.common.data.vo.bms;

import com.baomidou.mybatisplus.annotation.TableField;
import com.pukka.iptv.common.data.model.bms.BmsCategoryChannel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;

@Getter
@Setter
public class BmsCategoryChannelPageVO extends BmsCategoryChannel implements Serializable
{
    /**
     * 频道发布状态
     */
    private Integer channelPublishStatus;

    /**
     * 频道发布通道
     */
    private String channelOutPassageNames;

    /**
     * 建议频道号
     */
    @TableField(value = "channel_number")
    @ApiModelProperty(value = "建议频道号", dataType = "String", name = "channelNumber")
    private String channelNumber;
}
