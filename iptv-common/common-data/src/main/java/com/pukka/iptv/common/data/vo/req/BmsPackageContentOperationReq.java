package com.pukka.iptv.common.data.vo.req;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import org.springframework.util.Assert;

import javax.validation.constraints.Max;
import javax.validation.constraints.Min;
import javax.validation.constraints.NotEmpty;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @create 2021-09-16 09:56
 *  产品包内容 解除绑定/绑定/绑定并发布/绑定并定时发布/发布/定时发布
 *  本类为 产品包内容操作 的通用请求参数类， 参数校验方法根据以上业务不同进行 组合校验
 */
@Getter
@Setter
public class BmsPackageContentOperationReq {
    @ApiModelProperty("产品包ID")
    @NotEmpty
    private List<Long> packageIdList;

    @ApiModelProperty("产品包关系主键id")
    @NotEmpty
    private List<Long> relationIdList;

    @ApiModelProperty("操作类型：0:绑定  1:绑定并发布 2:绑定并定时发布 3:发布 4:定时发布")
    @Max(4)
    @Min(0)
    private Integer strategy;

    @ApiModelProperty("定时发布时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone="GMT+8")
    private Date timedPublish;

    public void validDate() {
        Assert.notNull(timedPublish, "定时时间不能为null");
    }

    public void validStrategy() {
        Assert.notNull(strategy, "操作类型不能为null");
        if (strategy == 2 || strategy == 3)
            Assert.notNull(timedPublish, "定时时间不能为null");
    }

    public Date getTimed() {
        return strategy == 2 || strategy == 3 ? timedPublish : null;
    }
}
