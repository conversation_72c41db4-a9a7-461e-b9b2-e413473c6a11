package com.pukka.iptv.common.data.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.util.Date;
import lombok.Data;
import com.fasterxml.jackson.annotation.JsonFormat;

import java.io.Serializable;

/**
 * TODO 未授权频道实体
 *
 * <AUTHOR>
 * @date 2021/9/3 15:36
 */
@Data
@ApiModel("未授权频道实体")
public class SysAuthorizationChannelDto implements Serializable {

    @ApiModelProperty(value="频道id",dataType="Long",name="id")
    private Long id;
    @ApiModelProperty(value="SPID",dataType="Long",name="spId")
    private Long spId;
    @ApiModelProperty(value="频道名称",dataType="String",name="name")
    private String name;
    @ApiModelProperty(value="全局唯一标识",dataType="String",name="code")
    private String code;
    @ApiModelProperty(value="建议频道号",dataType="String",name="channelNumber")
    private String channelNumber;
    @ApiModelProperty(value="CP名称",dataType="String",name="cpName")
    private String cpName;
    @ApiModelProperty(value="授权通道",dataType="String",name="outPassageNames")
    private String outPassageNames;
    @ApiModelProperty(value="授权通道ids",dataType="String",name="outPassageIds")
    private String outPassageIds;
    @ApiModelProperty(value="频道类型 1:直播频道",dataType="Integer",name="type")
    private Integer type;
    @ApiModelProperty(value="发布状态 \n" +
            "1：待发布\n" +
            "2：发布中\n" +
            "3：发布成功\n" +
            "4：发布失败\n" +
            "5：待更新\n" +
            "6：更新中\n" +
            "7：更新失败\n" +
            "8：回收中\n" +
            "9：回收成功\n" +
            "10：回收失败",dataType="Integer",name="publishStatus")
    private Integer publishStatus;

}
