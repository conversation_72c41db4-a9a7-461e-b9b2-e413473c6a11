package com.pukka.iptv.common.data.model.bms;

import com.baomidou.mybatisplus.annotation.*;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Date;

/**
 * @author: lqh
 * @date: 2021-8-27 15:59:13
 */

@Data
@EqualsAndHashCode
@JsonIgnoreProperties(value = {"handler"})
@TableName(value = "bms_program", autoResultMap = true)
public class BmsProgram extends Model<BmsProgram> implements java.io.Serializable {

    private static final long serialVersionUID = 1L;
    /**
     * 主键
     */
    @TableId(type = IdType.AUTO)
    @TableField(value = "id")
    @ApiModelProperty(value = "主键", dataType = "Long", name = "id")
    private Long id;
    /**
     * 全局唯一标识
     */
    @TableField(value = "code", fill = FieldFill.INSERT)
    @ApiModelProperty(value = "全局唯一标识", dataType = "String", name = "code")
    private String code;
    /**
     * CP的program id
     */
    @TableField(value = "cms_content_id")
    @ApiModelProperty(value = "CP的program id", dataType = "Long", name = "cmsContentId")
    private Long cmsContentId;
    /**
     * CP的program code
     */
    @TableField(value = "cms_content_code")
    @ApiModelProperty(value = "CP的program code", dataType = "String", name = "cmsContentCode")
    private String cmsContentCode;
    /**
     * 节目名称
     */
    @TableField(value = "name")
    @ApiModelProperty(value = "节目名称", dataType = "String", name = "name")
    private String name;
    /**
     * 有效开始时间（YYYYMMDDHH24MiSS）
     */
    @TableField(value = "licensing_window_start")
    @ApiModelProperty(value = "有效开始时间（YYYYMMDDHH24MiSS）", dataType = "String", name = "licensingWindowStart")
    private String licensingWindowStart;
    /**
     * 有效结束时间（YYYYMMDDHH24MiSS）
     */
    @TableField(value = "licensing_window_end")
    @ApiModelProperty(value = "有效结束时间（YYYYMMDDHH24MiSS）", dataType = "String", name = "licensingWindowEnd")
    private String licensingWindowEnd;
    /**
     * 状态标志  0:失效 1:生效
     */
    @TableField(value = "status")
    @ApiModelProperty(value = "状态标志  0:失效 1:生效", dataType = "Integer", name = "status")
    private Integer status;
    /**
     * 剧头id
     */
    @TableField(value = "cms_series_id")
    @ApiModelProperty(value = "剧头id", dataType = "Long", name = "cmsSeriesId")
    private Long cmsSeriesId;
    /**
     * 剧头code
     */
    @TableField(value = "cms_series_code")
    @ApiModelProperty(value = "剧头code", dataType = "String", name = "cmsSeriesCode")
    private String cmsSeriesCode;
    /**
     * 集数，连续剧第几集
     */
    @TableField(value = "episode_index")
    @ApiModelProperty(value = "集数，连续剧第几集", dataType = "Long", name = "episodeIndex")
    private Integer episodeIndex;
    /**
     * 节目时长（分钟）
     */
    @TableField(value = "duration")
    @ApiModelProperty(value = "节目时长（分钟）", dataType = "Long", name = "duration")
    private Long duration;
    /**
     * cpId
     */
    @TableField(value = "cp_id")
    @ApiModelProperty(value = "cpId", dataType = "Long", name = "cpId")
    private Long cpId;
    /**
     * CP名称
     */
    @TableField(value = "cp_name")
    @ApiModelProperty(value = "CP名称", dataType = "String", name = "cpName")
    private String cpName;
    /**
     * spId
     */
    @TableField(value = "sp_id")
    @ApiModelProperty(value = "spId", dataType = "Long", name = "spId")
    private Long spId;
    /**
     * SP名称
     */
    @TableField(value = "sp_name")
    @ApiModelProperty(value = "SP名称", dataType = "String", name = "spName")
    private String spName;
    /**
     * 创建时间
     */
    @TableField(value = "create_time", fill = FieldFill.INSERT)
    @ApiModelProperty(value = "创建时间", dataType = "String", name = "createTime")
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date createTime;
    /**
     * 更新时间
     */
    @TableField(value = "update_time", fill = FieldFill.UPDATE)
    @ApiModelProperty(value = "更新时间", dataType = "String", name = "updateTime")
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date updateTime;
    /**
     * 正片关联状态 1：待关联 2：已关联
     */
    @TableField(value = "release_status")
    @ApiModelProperty(value = "正片关联状态 1：待关联 2：已关联", dataType = "Integer", name = "releaseStatus")
    private Integer releaseStatus;
    /**
     * 发布状态  1：待发布 2：发布中 3：发布成功 4：发布失败 5：待更新 6：更新中 7：更新失败 8：回收中 9：回收成功 10：回收失败
     */
    @TableField(value = "publish_status")
    @ApiModelProperty(value = "发布状态  1：待发布 2：发布中 3：发布成功 4：发布失败 5：待更新 6：更新中 7：更新失败 8：回收中 9：回收成功 10：回收失败", dataType = "Integer", name = "publishStatus")
    private Integer publishStatus;
    /**
     * 发布描述
     */
    @TableField(value = "publish_description")
    @ApiModelProperty(value = "发布描述", dataType = "String", name = "publishDescription")
    private String publishDescription;
    /**
     * 发布时间
     */
    @TableField(value = "publish_time")
    @ApiModelProperty(value = "发布时间", dataType = "String", name = "publishTime")
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date publishTime;
    /**
     * 定时发布，时间存在则代表已经设定为定时发布
     */
    @TableField(value = "timed_publish")
    @ApiModelProperty(value = "定时发布，时间存在则代表已经设定为定时发布", dataType = "String", name = "timedPublish")
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
//    private Date timedPublish;
    private Date timedPublish;


    /**
     * 定时发布，时间存在则代表已经设定为定时发布
     */
    @TableField(value = "timed_publish_status")
    @ApiModelProperty(value = "定时发布状态", dataType = "String", name = "timedPublishStatus")
    private Integer timedPublishStatus;
    /**
     * 定时发布，时间存在则代表已经设定为定时发布
     */
    @TableField(value = "timed_publish_description")
    @ApiModelProperty(value = "定时发布描述", dataType = "String", name = "timedPublishDescription")
    private String timedPublishDescription;


    /**
     * CP内容编码
     */
    /**
     * 审核状态 1：未审核 2：审核中 3通过 4：未通过
     */
    @TableField(value = "cp_check_status")
    @ApiModelProperty(value = "审核状态 1：未审核 2：审核中 3通过 4：未通过 ", dataType = "Integer", name = "cpCheckStatus")
    private Integer cpCheckStatus;
    /**
     * 自审描述
     */
    @TableField(value = "cp_check_desc")
    @ApiModelProperty(value = "自审描述", dataType = "String", name = "cpCheckDesc")
    private String cpCheckDesc;
    /**
     * 审核时间
     */
    @TableField(value = "cp_check_time")
    @ApiModelProperty(value = "审核时间", dataType = "String", name = "cpCheckTime")
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date cpCheckTime;
    /**
     * 审核人
     */
    @TableField(value = "cp_checker")
    @ApiModelProperty(value = "审核人", dataType = "String", name = "cpChecker")
    private String cpChecker;
    /**
     * op审核 1:op未审核  2: 审核中 4：op审核未通过  3: 审核通过
     */
    @TableField(value = "op_check_status")
    @ApiModelProperty(value = "op审核 1:op未审核  2: 审核中 4：op审核未通过  3: 审核通过", dataType = "Integer", name = "opCheckStatus")
    private Integer opCheckStatus;
    /**
     * op审核描述
     */
    @TableField(value = "op_check_desc")
    @ApiModelProperty(value = "op审核描述", dataType = "String", name = "opCheckDesc")
    private String opCheckDesc;
    /**
     * 终审人员
     */
    @TableField(value = "op_checker")
    @ApiModelProperty(value = "终审人员", dataType = "String", name = "opChecker")
    private String opChecker;
    /**
     * 终审时间
     */
    @TableField(value = "op_check_time")
    @ApiModelProperty(value = "终审时间", dataType = "String", name = "opCheckTime")
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date opCheckTime;
    /**
     * 内容提供商标识
     */
    @TableField(value = "content_provider")
    @ApiModelProperty(value = "内容提供商标识", dataType = "String", name = "contentProvider")
    private String contentProvider;

    /**
     * 发布通道ID，多个ID以英文逗号隔开
     */
    @TableField(value = "out_passage_ids")
    @ApiModelProperty(value = "发布通道ID，多个ID以英文逗号隔开", dataType = "String", name = "outPassageIds")
    private String outPassageIds;
    /**
     * 分发通道名称以英文逗号 隔开
     */
    @TableField(value = "out_passage_names")
    @ApiModelProperty(value = "分发通道名称以英文逗号 隔开", dataType = "String", name = "outPassageNames")
    private String outPassageNames;

    /**
     * CP反馈标识，当内容状态变更，变为待发布，发布成功，回收成功时，需要向内容所属CP反馈发布到运营商的状态，结合status.
     * 1：需要反馈，2：反馈结束。
     */
    @TableField(value = "cp_feedback_flag")
    @ApiModelProperty(value = "CP反馈标识，当内容状态变更，变为待发布，发布成功，回收成功，时，需要向内容所属CP反馈发布到运营商的状态", dataType = "Integer", name = "cpFeedbackFlag")
    private Integer cpFeedbackFlag;

    /**
     * 所属渠道
     */
    @TableField(value = "bms_sp_channel_name")
    @ApiModelProperty(value = "所属渠道", dataType = "String", name = "bmsSpChannelName")
    private String bmsSpChannelName;
    /**
     * 所属渠道id
     */
    @TableField(value = "bms_sp_channel_id")
    @ApiModelProperty(value = "所属渠道id", dataType = "Long", name = "bmsSpChannelId")
    private Long bmsSpChannelId;

    /**
     * 原名
     */
    @TableField(value = "original_name")
    @ApiModelProperty(value = "原名", dataType = "String", name = "originalName")
    private String originalName;

    /**
     * 预留字段
     */
    @TableField(value = "extendInfoList")
    @ApiModelProperty(value = "预留字段", dataType = "String", name = "extendInfoList")
    private String extendInfoList;

    /**
     * 节目试看(秒)
     */
    @TableField(value = "preDuration")
    @ApiModelProperty(value = "节目试看(秒)", dataType = "Long", name = "preDuration")
    private Long preDuration;
}
