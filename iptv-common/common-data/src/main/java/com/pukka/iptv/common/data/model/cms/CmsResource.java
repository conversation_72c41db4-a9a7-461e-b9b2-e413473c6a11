package com.pukka.iptv.common.data.model.cms;

import com.baomidou.mybatisplus.annotation.*;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.NoArgsConstructor;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springframework.util.Assert;

import java.util.Date;

/**
 *
 * @author: zhengcl
 * @date: 2021-9-15 15:13:01
 */

@Data
@EqualsAndHashCode
@JsonIgnoreProperties(value = { "handler" })
@NoArgsConstructor
@TableName(value = "cms_resource",autoResultMap=true)
public class CmsResource extends Model<CmsResource> implements java.io.Serializable{

    private static final long serialVersionUID = 1L;
	/**主键*/
    @TableId(type = IdType.AUTO)
	@TableField(value = "id")
    @ApiModelProperty(value="主键",dataType="Long",name="id")
    private Long id;
	/**全局唯一标识*/
	@TableField(value = "code")
    @ApiModelProperty(value="全局唯一标识",dataType="String",name="code")
    private String code;
	/**文件名*/
	@TableField(value = "name")
    @ApiModelProperty(value="文件名",dataType="String",name="name")
    private String name;
	/**文件地址，ftp格式*/
	@TableField(value = "file_url")
    @ApiModelProperty(value="文件地址，ftp格式",dataType="String",name="fileUrl")
    private String fileUrl;
	/**CP账号ID*/
	@TableField(value = "cp_id")
    @ApiModelProperty(value="CP账号ID",dataType="Long",name="cpId")
    private Long cpId;
	/**CP名称*/
	@TableField(value = "cp_name")
    @ApiModelProperty(value="CP名称",dataType="String",name="cpName")
    private String cpName;
	/**当 content_type = 1,2时为cms_program表ID 当content_type = 3,4 时为cms_series表ID*/
	@TableField(value = "content_id")
    @ApiModelProperty(value="当 content_type = 1,2时为cms_program表ID 当content_type = 3,4 时为cms_series表ID",dataType="Long",name="contentId")
    private Long contentId;
	/**节目关联状态 1：待关联 2：已关联*/
	@TableField(value = "content_status")
    @ApiModelProperty(value="节目关联状态 1：待关联 2：已关联",dataType="Integer",name="contentStatus")
    private Integer contentStatus;
	/**当 content_type = 1,2时为cms_program表code 当content_type = 3,4 时为cms_series表code*/
	@TableField(value = "content_code")
    @ApiModelProperty(value="当 content_type = 1,2时为cms_program表code 当content_type = 3,4 时为cms_series表code",dataType="String",name="contentCode")
    private String contentCode;
	/**内容类型\ 1：电影  2：子集 3：电视剧 4：系列片 5：片花*/
	@TableField(value = "content_type")
    @ApiModelProperty(value="内容类型 1：电影  2：子集 3：电视剧 4：系列片 5：片花",dataType="Integer",name="contentType")
    private Integer contentType;
	/**媒体类型 1:正片 2:预览片 */
	@TableField(value = "type")
    @ApiModelProperty(value="媒体类型 1:正片 2:预览片 ",dataType="Integer",name="type")
    private Integer type;
	/**媒体码率描述符 0：其它 1：400K 2：700K 3：1.3M 4：2M 5：2.5M 6：8M 7：10M 8：12M 9：16M 16:8M(杜比5.1) 51：1.3M（标清VBR） 52：2M（标清VBR） 53：4M（高清VBR） 54： 6M（高清VBR） 55： 2.3M（标清VBR） 56：25M（4K H264 VBR） 57：50M（4K H264 VBR） 58：15M（4K H265 VBR） 59：30M（4K H265 VBR）*/
	@TableField(value = "bit_rate_type")
    @ApiModelProperty(value="媒体码率描述符 0：其它 1：400K 2：700K 3：1.3M 4：2M 5：2.5M 6：8M 7：10M 8：12M 9：16M 16:8M(杜比5.1) 51：1.3M（标清VBR） 52：2M（标清VBR） 53：4M（高清VBR） 54： 6M（高清VBR） 55： 2.3M（标清VBR） 56：25M（4K H264 VBR） 57：50M（4K H264 VBR） 58：15M（4K H265 VBR） 59：30M（4K H265 VBR）",dataType="Integer",name="bitRateType")
    private Integer bitRateType;
	/**点播的片头时长，单位秒*/
	@TableField(value = "movie_head_duration")
    @ApiModelProperty(value="点播的片头时长，单位秒",dataType="Long",name="movieHeadDuration")
    private Long movieHeadDuration;
	/**点播的片尾时长，单位秒*/
	@TableField(value = "movie_tail_duration")
    @ApiModelProperty(value="点播的片尾时长，单位秒",dataType="Long",name="movieTailDuration")
    private Long movieTailDuration;
	/**状态*/
	@TableField(value = "status")
    @ApiModelProperty(value="状态",dataType="Integer",name="status")
    private Integer status;
	/**时长单位秒*/
	@TableField(value = "duration")
    @ApiModelProperty(value="时长单位秒",dataType="Long",name="duration")
    private Long duration;
	/**文件大小*/
	@TableField(value = "file_size")
    @ApiModelProperty(value="文件大小",dataType="Long",name="fileSize")
    private Long fileSize;
	/**所属存储*/
	@TableField(value = "storage_name")
    @ApiModelProperty(value="所属存储",dataType="String",name="storageName")
    private String storageName;
	/**存储ID*/
	@TableField(value = "storage_id")
    @ApiModelProperty(value="存储ID",dataType="Long",name="storageId")
    private Long storageId;
	/**来源1：注入， 2上传*/
	@TableField(value = "source")
    @ApiModelProperty(value="来源1：注入， 2上传",dataType="Integer",name="source")
    private Integer source;
	/**MD5码*/
	@TableField(value = "md5")
    @ApiModelProperty(value="MD5码",dataType="String",name="md5")
    private String md5;
	/**创建时间*/
    @TableField(value = "create_time", fill = FieldFill.INSERT)
    @ApiModelProperty(value="创建时间",dataType="String",name="createTime")
    @JsonFormat(shape = JsonFormat.Shape.STRING,pattern = "yyyy-MM-dd HH:mm:ss",timezone="GMT+8")
    private Date createTime;
	/**更新时间*/
	@TableField(value = "update_time", fill = FieldFill.UPDATE)
    @ApiModelProperty(value="更新时间",dataType="String",name="updateTime")
    @JsonFormat(shape = JsonFormat.Shape.STRING,pattern = "yyyy-MM-dd HH:mm:ss",timezone="GMT+8")
    private Date updateTime;
	/**创建人，只人工上传时可用*/
	@TableField(value = "creator_name", fill = FieldFill.INSERT)
    @ApiModelProperty(value="创建人，只人工上传时可用",dataType="String",name="creatorName")
    private String creatorName;
	/**创建人ID，来自于user表*/
	@TableField(value = "creator_id", fill = FieldFill.INSERT)
    @ApiModelProperty(value="创建人ID，来自于user表",dataType="Long",name="creatorId")
    private Long creatorId;
	/**1） 输出封装格式（Envelope）： a) TS b) F4V c) 3GP d) MP4*/
	@TableField(value = "envelope")
    @ApiModelProperty(value="1） 输出封装格式（Envelope）： a) TS b) F4V c) 3GP d) MP4",dataType="String",name="envelope")
    private String envelope;
	/**2） 码率控制（Bitrate Type）： a) VBR b) CBR*/
//	@TableField(value = "bitrate_type")
//    @ApiModelProperty(value="2） 码率控制（Bitrate Type）： a) VBR b) CBR",dataType="String",name="bitrateType")
//    private String bitrateType;
	/**a) 编码格式（Video Codec）： i. 针对 TS，可选 H264，HEVC ii. 针对 3GP，可选 MPEG4，H264 iii. 针对 F4V，只选 H264 iv. 针对 MP4，只选 H264，HEVC*/
	@TableField(value = "video_codec")
    @ApiModelProperty(value="a) 编码格式（Video Codec）： i. 针对 TS，可选 H264，HEVC ii. 针对 3GP，可选 MPEG4，H264 iii. 针对 F4V，只选 H264 iv. 针对 MP4，只选 H264，HEVC",dataType="String",name="videoCodec")
    private String videoCodec;
	/**码率（Video Bitrate）：1 ~ 100000 （kbits/s）*/
	@TableField(value = "video_bitrate")
    @ApiModelProperty(value="码率（Video Bitrate）：1 ~ 100000 （kbits/s）",dataType="String",name="videoBitrate")
    private Integer videoBitrate;
	/**c) 分辨率（Resolution）： i. 1080i ii. 720P iii. 576i iv. 3/4D1 v. 2/3D1 vi. CIF（只针对 3GP） vii. QCIF （只针对 3GP） viii. 3840i(4K) ix. 4096i(4K) */
	@TableField(value = "resolution")
    @ApiModelProperty(value="c) 分辨率（Resolution）： i. 1080i ii. 720P iii. 576i iv. 3/4D1 v. 2/3D1 vi. CIF（只针对 3GP） vii. QCIF （只针对 3GP） viii. 3840i(4K) ix. 4096i(4K) ",dataType="String",name="resolution")
    private String resolution;
	/**4. 帧率（Frame rate）*/
	@TableField(value = "frame_rate")
    @ApiModelProperty(value="4. 帧率（Frame rate）",dataType="Float",name="frameRate")
    private Float frameRate;
	/**a) 编码格式（Audio Codec）： i. 针对 TS，可选 MP2，AAC ii. 针对 F4V，可选 AAC Page 20 of 23 iii. 针对 3GP，可选 AAC，AMR iv. 针对 MP4，可选 AAC v. EAC3( 4K)*/
	@TableField(value = "audio_codec")
    @ApiModelProperty(value="a) 编码格式（Audio Codec）： i. 针对 TS，可选 MP2，AAC ii. 针对 F4V，可选 AAC Page 20 of 23 iii. 针对 3GP，可选 AAC，AMR iv. 针对 MP4，可选 AAC v. EAC3( 4K)",dataType="String",name="audioCodec")
    private String audioCodec;
	/**b) 码率(Audio bitrate)：1 ~ 256 （kbits/s）*/
	@TableField(value = "audio_bitrate")
    @ApiModelProperty(value="b) 码率(Audio bitrate)：1 ~ 256 （kbits/s）",dataType="String",name="audioBitrate")
    private String audioBitrate;
	/**媒体格式描述符,Envelope-BitrateType-VideoCodec-VideoBitrate-Resolution-FrameRate-AudioCodec-AudioBitrate*/
	@TableField(value = "media_spec")
    @ApiModelProperty(value="媒体格式描述符, Envelope-BitrateType-VideoCodec-VideoBitrate-Resolution-FrameRate-AudioCodec-AudioBitrate",dataType="String",name="mediaSpec")
    private String mediaSpec;
	/**0: No DRM 1: BES DRM*/
	@TableField(value = "source_drm_type")
    @ApiModelProperty(value="0: No DRM 1: BES DRM",dataType="Integer",name="sourceDrmType")
    private Integer sourceDrmType;
	/**0: No DRM 1: BES DRM*/
	@TableField(value = "dest_drm_type")
    @ApiModelProperty(value="0: No DRM 1: BES DRM",dataType="Integer",name="destDrmType")
    private Integer destDrmType;
	/**0: 其他 1: Monaural 单声道 2: Stereo 多声道 3: Two-nation monaural 双单 声道 4: Two-nation stereo 双多声 道 5: AC3(5:1 channel) AC3 声道*/
	@TableField(value = "audio_type")
    @ApiModelProperty(value="0: 其他 1: Monaural 单声道 2: Stereo 多声道 3: Two-nation monaural 双单 声道 4: Two-nation stereo 双多声 道 5: AC3(5:1 channel) AC3 声道",dataType="Integer",name="audioType")
    private Integer audioType;
	/**0: 4x3 1: 16x9(Wide)*/
	@TableField(value = "screen_format")
    @ApiModelProperty(value="0: 4x3 1: 16x9(Wide)",dataType="Integer",name="screenFormat")
    private Integer screenFormat;
	/**字幕标志 0:无字幕 1:有字幕*/
	@TableField(value = "closed_captioning")
    @ApiModelProperty(value="字幕标志 0:无字幕 1:有字幕",dataType="Integer",name="closedCaptioning")
    private Integer closedCaptioning;
    /** 本地读取到的视频媒体信息格式为 bitRate|fileSize|duration(既有可能是秒，也有可能是时间戳 10:29:30)|width|height|frameRate|ratio(比例3：2，16：9等) */
    @TableField(value = "mediainfo")
    @ApiModelProperty(value="本地读取到的视频媒体信息格式为 bitRate|fileSize|duration(时间戳 10:29:30)|width|height|frameRate|ratio(比例3：2，16：9等)",dataType="String",name="mediainfo")
    private String mediainfo;

    @TableField(value = "width")
    @ApiModelProperty(value="width",dataType="Integer",name="width")
    private Integer width;
    @TableField(value = "height")
    @ApiModelProperty(value="height",dataType="Integer",name="height")
    private Integer height;
    /** 视频ID */
    @TableField(value = "vid")
    @ApiModelProperty(value="vid",dataType="String",name="vid")
    private String vid;
    /** 文件ID*/
    @TableField(value = "fileid")
    @ApiModelProperty(value="fileid",dataType="String",name="fileid")
    private String fileid;

    @TableField(value = "content_name")
    @ApiModelProperty(value="节目名称",dataType="String",name="contentName")
    private String contentName;

    public void validResourceUpdate(){
        Assert.notNull(id, "视频ID不能为空");
        Assert.notNull(contentType, "内容类型不能为空");
        Assert.notNull(contentId, "媒资ID不能为空");
        Assert.notNull(type, "媒体类型不能为空");
    }


    public void validBlobUpload(){
        Assert.notNull(type, "视频类型不能为空");
        Assert.notNull(cpId, "CPID不能为空");
        Assert.notNull(cpName, "CP名称不能为空");
        Assert.notNull(fileUrl, "文件地址不能为空");
        Assert.notNull(code, "唯一编码不能为空");
    }

    public void validReleatedResource(){
        Assert.notNull(contentType, "内容类型不能为空");
        Assert.notNull(cpId, "CPID不能为空");
        Assert.notNull(contentId, "内容ID不能为空");
        Assert.notNull(contentCode, "内容编码不能为空");
    }
}
