package com.pukka.iptv.common.data.vo.bms;

import com.baomidou.mybatisplus.annotation.TableField;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

/**
 * <AUTHOR>
 * @create 2021-09-15 14:43
 */
@Getter
@Setter
public class BmsPackageNotBindContentVO {

    @ApiModelProperty(value="媒资id")
    private Long id;

    @ApiModelProperty(value="媒资名称")
    private String name;

    @TableField(value = "content_type")
    @ApiModelProperty(value="媒资类型 1：电影 3：电视剧 4：系列片  5：片花")
    private String contentType;

    @TableField(value = "pgm_category")
    @ApiModelProperty(value="媒资分类：电影 电视剧")
    private String pgmCategory;

    @TableField("op_check_status")
    @ApiModelProperty("终审状态 1：op未审核 2：审核中 3：审核通过 4：审核未通过")
    private Integer opCheckStatus;

    @TableField("publish_status")
    @ApiModelProperty("媒资发布状态")
    private Integer publishStatus;

    @TableField(value = "cp_name")
    @ApiModelProperty(value="CP名称",dataType="String")
    private String cpName;

}
