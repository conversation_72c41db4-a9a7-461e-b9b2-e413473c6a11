package com.pukka.iptv.common.data.model.epg;

import com.baomidou.mybatisplus.annotation.*;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.Date;

/**
 * 分发模版
 * 
 * <AUTHOR>
 * @email 
 * @date 2023-03-01 11:11:32
 */
@Data
@EqualsAndHashCode
@JsonIgnoreProperties(value = { "handler" })
@NoArgsConstructor
@TableName(value = "epg_template",autoResultMap=true)
public class EpgTemplate implements Serializable {

	private static final long serialVersionUID = -8652485349869843810L;
	/**
	 * 
	 */
	@TableId(type = IdType.AUTO)
	@TableField(value = "id")
	@ApiModelProperty(value="ObjectID",dataType="Long",name="id")
	private Long id;
	/**
	 * 分发模版
	 */
	@TableField(value = "name")
	@ApiModelProperty(value="分发模版",dataType="String",name="name")
	private String name;
	/**
	 * 唯一编码
	 */
	@TableField(value = "code", fill = FieldFill.INSERT)
	@ApiModelProperty(value="唯一编码",dataType="String",name="code")
	private String code;
	/**
	 * 创建人来自admin表
	 */
	@TableField(value = "creator_name", fill = FieldFill.INSERT)
	@ApiModelProperty(value="创建人",dataType="String",name="creatorName")
	private String creatorName;
	/**
	 * 来自admin表
	 */
	@TableField(value = "creator_id", fill = FieldFill.INSERT)
	@ApiModelProperty(value="创建人",dataType="Long",name="creatorId")
	private Long creatorId;
	/**
	 * epg路径
	 */
	@TableField(value = "path")
	@ApiModelProperty(value="epg路径",dataType="String",name="path")
	private String path;
	/**
	 * lsp名称
	 */
	@TableField(value = "node_name")
	@ApiModelProperty(value="lsp名称",dataType="String",name="nodeName")
	private String nodeName;
	/**
	 * epg唯一编码
	 */
	@TableField(value = "node_code")
	@ApiModelProperty(value="epg唯一编码",dataType="String",name="nodeCode")
	private String nodeCode;
	/**
	 * 启用状态 0可用-1:停用255：已删除
	 */
	@TableField(value = "status")
	@ApiModelProperty(value="启用状态",dataType="Integer",name="status")
	private Integer status;
	/**
	 * 描述
	 */
	@TableField(value = "description",updateStrategy = FieldStrategy.IGNORED)
	@ApiModelProperty(value="描述",dataType="String",name="description")
	private String description;
	/**
	 * 创建时间
	 */
	@TableField(value = "create_time", fill = FieldFill.INSERT)
	@ApiModelProperty(value="创建时间",dataType="String",name="createTime")
	@JsonFormat(shape = JsonFormat.Shape.STRING,pattern = "yyyy-MM-dd HH:mm:ss",timezone="GMT+8")
	private Date createTime;
	/**
	 * 更新时间
	 */
	@TableField(value = "update_time", fill = FieldFill.INSERT_UPDATE)
	@ApiModelProperty(value="工单下游反馈完成时间",dataType="String",name="updateTime")
	@JsonFormat(shape = JsonFormat.Shape.STRING,pattern = "yyyy-MM-dd HH:mm:ss",timezone="GMT+8")
	private Date updateTime;

}
