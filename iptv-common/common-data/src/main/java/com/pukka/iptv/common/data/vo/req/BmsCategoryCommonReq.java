package com.pukka.iptv.common.data.vo.req;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.NoArgsConstructor;
import com.pukka.iptv.common.data.model.bms.BmsCategory;
import com.pukka.iptv.common.data.vo.bms.BmsPictureVO;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;
import org.springframework.util.Assert;
import org.springframework.util.CollectionUtils;

import javax.validation.constraints.Max;
import javax.validation.constraints.Min;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * <AUTHOR>
 * @create 2021-09-13 10:49
 */
@Getter
@Setter
@JsonIgnoreProperties(value = { "handler" })
@NoArgsConstructor
@Accessors(chain = true)
public class BmsCategoryCommonReq extends BmsCategory{

    @ApiModelProperty(name = "图片信息数组")
    private List<BmsPictureVO> picArray;

    @ApiModelProperty(name = "id")
    private Long id;

    @ApiModelProperty(name = "栏目名")
    @NotBlank
    private String name;

    @ApiModelProperty(name = "栏目父ID，顶级为0")
    private Long parentId;

    @ApiModelProperty(name = "栏目父ID，顶级为0")
    private String parentCode;

    @ApiModelProperty(name = "栏目所属spId")
    @NotNull
    private Long spId;

    @ApiModelProperty(name = "栏目类型 1:普通栏目 2:公共栏目")
    @NotNull
    private Integer type;

    @ApiModelProperty(name = "栏目排序号")
    @Min(value = 0, message= "排序号不合法")
    @Max(value = Integer.MAX_VALUE, message= "排序号不合法")
    private Integer sequence;

    @ApiModelProperty(name = "栏目状态：0:失效 1:生效")
    @NotNull
    private Integer status;

    @ApiModelProperty(name = "栏目描述")
    private String description;

    @ApiModelProperty(value = "来源1：专线注入 2：人工创建")
    @NotNull
    @Min(1)
    @Max(2)
    private Integer source;

    private String cspId;

    // 新增校验
    public void validSave() {
        this.setId(null);
        if (parentId != null && parentId > 0) {
            Assert.notNull(parentCode, "parentCode cannot be null");
        }else {
            parentId = 0L;
            parentCode = "0";
        }
        //如果没有用到 图片上传 则不用校验图片信息的参数
        validPic();
    }

    public void validUpdate() {
        Assert.notNull(id, "id不能为null");
        validPic();
    }

    public void validPic() {
        if (!CollectionUtils.isEmpty(picArray)) {
            picArray.forEach(BmsPictureVO::valid);
        }
    }
}
