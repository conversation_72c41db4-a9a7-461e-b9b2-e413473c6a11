package com.pukka.iptv.common.data.model;

import com.baomidou.mybatisplus.annotation.*;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import java.util.Date;

/**
 *
 * @author: tan
 * @date: 2021-9-1 16:08:21
 */

@Data
@EqualsAndHashCode
@JsonIgnoreProperties(value = { "handler" })
@NoArgsConstructor
@TableName(value = "summary",autoResultMap=true)
public class Summary extends Model<Summary> implements java.io.Serializable{

    private static final long serialVersionUID = 691063300522169693L;

    /**id*/
    @TableId(type = IdType.AUTO)
	@TableField(value = "id")
    @ApiModelProperty(value="id",dataType="String",name="id")
    private Long id;
	/**分发工单总数*/
	@TableField(value = "out_order_count")
    @ApiModelProperty(value="分发工单总数",dataType="String",name="outOrderCount")
    private long outOrderCount;
	/**注入工单总数*/
	@TableField(value = "in_order_count")
    @ApiModelProperty(value="注入工单总数",dataType="String",name="inOrderCount")
    private long inOrderCount;
	/**下载任务总数*/
	@TableField(value = "cms_download_count")
    @ApiModelProperty(value="下载任务总数",dataType="String",name="cmsDownloadCount")
    private long cmsDownloadCount;
	/**分发工单最新计数*/
	@TableField(value = "last_out_order_id")
    @ApiModelProperty(value="分发工单最新计数",dataType="String",name="lastOutOrderId")
    private long lastOutOrderId;
	/**注入工单最新计数*/
	@TableField(value = "last_in_order_id")
    @ApiModelProperty(value="注入工单最新计数",dataType="String",name="lastInOrderId")
    private long lastInOrderId;
	/**下载任务最新计数*/
	@TableField(value = "last_cms_download_id")
    @ApiModelProperty(value="下载任务最新计数",dataType="String",name="lastCmsDownloadId")
    private long lastCmsDownloadId;
	/**创建时间*/
    @TableField(value = "create_time", fill = FieldFill.INSERT)
    @ApiModelProperty(value="创建时间",dataType="String",name="createTime")
    @JsonFormat(shape = JsonFormat.Shape.STRING,pattern = "yyyy-MM-dd HH:mm:ss",timezone="GMT+8")
    private Date createTime;
	/**更新时间*/
    @TableField(value = "update_time", fill = FieldFill.UPDATE)
    @ApiModelProperty(value="更新时间",dataType="String",name="updateTime")
    @JsonFormat(shape = JsonFormat.Shape.STRING,pattern = "yyyy-MM-dd HH:mm:ss",timezone="GMT+8")
    private Date updateTime;
}
