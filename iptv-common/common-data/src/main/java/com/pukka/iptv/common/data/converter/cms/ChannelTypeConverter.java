package com.pukka.iptv.common.data.converter.cms;

import com.alibaba.excel.converters.Converter;
import com.alibaba.excel.enums.CellDataTypeEnum;
import com.alibaba.excel.metadata.GlobalConfiguration;
import com.alibaba.excel.metadata.data.WriteCellData;
import com.alibaba.excel.metadata.property.ExcelContentProperty;
import org.apache.commons.lang3.StringUtils;

import java.util.HashMap;
import java.util.Map;

/**
 * @Author: wangbo
 * @Date: 2022/4/19 10:19
 */
public class ChannelTypeConverter implements Converter<Integer> {

    private static final Map<Integer, String> STRING_MAP = new HashMap<>();
    static {
        STRING_MAP.put(1, "直播频道");
    }

    @Override
    public Class supportJavaTypeKey() {
        return Integer.class;
    }

    @Override
    public CellDataTypeEnum supportExcelTypeKey() {
        return CellDataTypeEnum.STRING;
    }

    /**
     * 这里是写的时候会调用
     */
    @Override
    public WriteCellData<?> convertToExcelData(Integer value, ExcelContentProperty contentProperty, GlobalConfiguration globalConfiguration) throws Exception {
        return new WriteCellData(getType(value));
    }

    private String getType(Integer value) {
        String result = STRING_MAP.get(value);
        result = StringUtils.isEmpty(result) ? "其他频道" : result;
        return result;
    }
}
