package com.pukka.iptv.common.data.vo.epg;

import com.baomidou.mybatisplus.annotation.*;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.pukka.iptv.common.data.model.epg.EpgTemplate;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.Date;


@Data
@EqualsAndHashCode
@NoArgsConstructor
@Accessors(chain = true)
@JsonIgnoreProperties(value = { "handler" })
@ApiModel("EpgOutOrderVo")
public class EpgTemplateVo extends EpgTemplate implements Serializable {


	private String namePath;
}
