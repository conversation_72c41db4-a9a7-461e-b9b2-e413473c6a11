package com.pukka.iptv.common.data.vo.virtual;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.pukka.iptv.common.data.model.virtual.VirtualSeries;
import com.pukka.iptv.common.data.model.virtual.VirtualSubset;
import io.swagger.annotations.ApiModel;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import java.util.List;

@Data
@EqualsAndHashCode
@NoArgsConstructor
@ApiModel("VirtualSeriesVo")
@JsonIgnoreProperties(ignoreUnknown = true)
public class VirtualSeriesVo extends VirtualSeries implements java.io.Serializable{
    /** 子集code集合 */
    private List<VirtualSubset> programList;
}
