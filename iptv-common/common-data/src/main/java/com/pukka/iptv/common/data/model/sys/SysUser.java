package com.pukka.iptv.common.data.model.sys;

import com.baomidou.mybatisplus.annotation.*;
import com.pukka.iptv.common.base.enums.SysUserTypeEnum;
import io.swagger.annotations.ApiModelProperty;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.NoArgsConstructor;
import lombok.EqualsAndHashCode;
import java.util.Date;
import lombok.Data;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.experimental.Accessors;
import org.springframework.util.Assert;

/**
 *
 * @author: zhengcl
 * @date: 2021-8-17 18:55:48
 */

@Data
@EqualsAndHashCode
@JsonIgnoreProperties(value = { "handler" })
@NoArgsConstructor
@TableName(value = "sys_user",autoResultMap=true)
@Accessors(chain = true)
public class SysUser extends Model<SysUser> implements java.io.Serializable{

    private static final long serialVersionUID = 1L;
	/**ID*/
    @TableId(type = IdType.AUTO)
	@TableField(value = "id")
    @ApiModelProperty(value="ID",dataType="Long",name="id")
    private Long id;
	/**名称*/
	@TableField(value = "name")
    @ApiModelProperty(value="名称",dataType="String",name="name")
    private String name;
	/**账号*/
	@TableField(value = "username")
    @ApiModelProperty(value="账号",dataType="String",name="username")
    private String username;
	/**密码*/
	@TableField(value = "password")
    @ApiModelProperty(value="密码",dataType="String",name="password")
    private String password;
	/**性别 1：男 2：女*/
	@TableField(value = "gender")
    @ApiModelProperty(value="性别 1：男 2：女",dataType="Integer",name="gender")
    private Integer gender;
	/**头像*/
	@TableField(value = "avatar")
    @ApiModelProperty(value="头像",dataType="String",name="avatar")
    private String avatar;
	/**生日*/
	@TableField(value = "birthday")
    @ApiModelProperty(value="生日",dataType="String",name="birthday")
    private String birthday;
	/**手机*/
	@TableField(value = "phone")
    @ApiModelProperty(value="手机",dataType="String",name="phone")
    private String phone;
	/**邮箱*/
	@TableField(value = "email")
    @ApiModelProperty(value="邮箱",dataType="String",name="email")
    private String email;
	/**用户类型 1：租户类 2：管理类*/
	@TableField(value = "type")
    @ApiModelProperty(value="用户类型 1：租户类 2：管理类",dataType="Integer",name="type")
    private Integer type;
	/**状态 1：有效 2：无效*/
	@TableField(value = "status")
    @ApiModelProperty(value="状态 1：有效 2：无效",dataType="Integer",name="status")
    private Integer status;
	/**作者ID*/
	@TableField(value = "creator_id", fill = FieldFill.INSERT)
    @ApiModelProperty(value="作者ID",dataType="Long",name="creatorId")
    private Long creatorId;
	/**作者*/
	@TableField(value = "creator_name", fill = FieldFill.INSERT)
    @ApiModelProperty(value="作者",dataType="String",name="creatorName")
    private String creatorName;
	/**创建时间*/
	@TableField(value = "create_time", fill = FieldFill.INSERT)
    @ApiModelProperty(value="创建时间",dataType="String",name="createTime")
    @JsonFormat(shape = JsonFormat.Shape.STRING,pattern = "yyyy-MM-dd HH:mm:ss",timezone="GMT+8")
    private Date createTime;
	/**更新时间*/
	@TableField(value = "update_time")
    @ApiModelProperty(value="更新时间",dataType="String",name="updateTime")
    @JsonFormat(shape = JsonFormat.Shape.STRING,pattern = "yyyy-MM-dd HH:mm:ss",timezone="GMT+8")
    private Date updateTime;
	/**备注*/
	@TableField(value = "description")
    @ApiModelProperty(value="描述",dataType="String",name="description")
    private String description;
    /**角色ID*/
    @TableField(exist = false)
    @ApiModelProperty(value="角色ID集合",dataType="String",name="description")
    private String roleIds;
    /**角色名称*/
    @TableField(exist = false)
    @ApiModelProperty(value="角色ID集合",dataType="String",name="description")
    private String roleNames;
    /**租户ID*/
    @TableField(exist = false)
    @ApiModelProperty(value="角色ID集合",dataType="String",name="description")
    private String tenantIds;
    /**租户名称*/
    @TableField(exist = false)
    @ApiModelProperty(value="角色ID集合",dataType="String",name="description")
    private String tenantNames;

    public void valid() {
        Assert.notNull(name, "名称不能为空");
        Assert.isTrue(name.length() <= 32,"名称不能超过32位");
        Assert.notNull(username, "账号不能为空");
        Assert.isTrue(username.length() <= 32,"账号不能超过32位");
        Assert.notNull(password, "密码不能为空");
        Assert.isTrue(password.length() <= 64,"密码不能超过64位");
        Assert.notNull(type, "类型不能为空");
        if (SysUserTypeEnum.ADMIN.getCode().equals(type) || SysUserTypeEnum.TENANT.getCode().equals(type)) {
            Assert.notNull(roleIds, "角色不能为空");
        }
        if (SysUserTypeEnum.TENANT.getCode().equals(type)) {
            Assert.notNull(tenantIds, "租户不能为空");
        }
    }


    public void validUpdate() {
        Assert.notNull(name, "名称不能为空");
        Assert.isTrue(name.length() <= 32,"名称不能超过32位");
        Assert.notNull(username, "账号不能为空");
        Assert.isTrue(username.length() <= 32,"账号不能超过32位");
        Assert.notNull(type, "类型不能为空");
        if (SysUserTypeEnum.ADMIN.getCode().equals(type) || SysUserTypeEnum.TENANT.getCode().equals(type)) {
            Assert.notNull(roleIds, "角色不能为空");
        }
        if (SysUserTypeEnum.TENANT.getCode().equals(type)) {
            Assert.notNull(tenantIds, "租户不能为空");
        }
    }

}
