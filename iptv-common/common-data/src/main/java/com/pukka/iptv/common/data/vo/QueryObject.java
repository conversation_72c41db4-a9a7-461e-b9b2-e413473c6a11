package com.pukka.iptv.common.data.vo;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.NoArgsConstructor;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.util.Date;
import lombok.Data;
import com.fasterxml.jackson.annotation.JsonFormat;

import java.io.Serializable;

/**
 * @Author:zhengcl
 * @Date: 2021/7/12
 */
@Data
@JsonIgnoreProperties(value = {"handler"})
@ApiModel("QueryObject")
public class QueryObject implements Serializable {
    private static final long serialVersionUID = -3568278363389040262L;
    @ApiModelProperty(value = "当前页,默认1", dataType = "Integer", name = "pageNumber")
    protected Integer pageNumber = 1;
    @ApiModelProperty(value = "每页显示条数,默认12", dataType = "Integer", name = "pageSize")
    protected Integer pageSize = 12;

    public Integer getOffset() {
        return (pageNumber - 1) * pageSize;
    }
}
