package com.pukka.iptv.common.data.converter.cms;

import com.alibaba.excel.converters.Converter;
import com.alibaba.excel.enums.CellDataTypeEnum;
import com.alibaba.excel.metadata.GlobalConfiguration;
import com.alibaba.excel.metadata.data.WriteCellData;
import com.alibaba.excel.metadata.property.ExcelContentProperty;
import org.apache.commons.lang3.StringUtils;

import java.util.HashMap;
import java.util.Map;

/**
 * @Author: liqq
 * @Date: 2021/9/26
 * @Description: *
 */

public class CmsProhibitConverter implements Converter<Integer> {
    private static final Map<Integer, String> contentMap = new HashMap<>();

    static {
        contentMap.put(1, "单集");
        contentMap.put(3, "剧集");
    }

    @Override
    public Class supportJavaTypeKey() {
        return Integer.class;
    }

    @Override
    public CellDataTypeEnum supportExcelTypeKey() {
        return CellDataTypeEnum.STRING;
    }


    /**
     * 这里是写的时候会调用
     */
    @Override
    public WriteCellData<?> convertToExcelData(Integer value, ExcelContentProperty contentProperty, GlobalConfiguration globalConfiguration) throws Exception {
        return new WriteCellData(getContentType(value));
    }

    private String getContentType(Integer value) {
        String result = contentMap.get(value);
        result = StringUtils.isEmpty(result) ? "" : result;
        return result;
    }
}
