package com.pukka.iptv.common.data.model.epg;

import com.baomidou.mybatisplus.annotation.*;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.Date;

/**
 * 用户模版关系
 * 
 * <AUTHOR>
 * @email 
 * @date 2023-03-20 09:43:53
 */
@Data
@EqualsAndHashCode
@JsonIgnoreProperties(value = { "handler" })
@NoArgsConstructor
@TableName(value = "sys_user_template",autoResultMap=true)
@Accessors(chain = true)
public class SysUserTemplate extends Model<SysUserTemplate> implements Serializable {

	private static final long serialVersionUID = 940643362554272156L;
	/**
	 * ID
	 */
	@TableId(type = IdType.AUTO)
	@TableField(value = "id")
	@ApiModelProperty(value="ID",dataType="Long",name="id")
	private Long id;
	/**
	 * 用户ID
	 */
	@TableField(value = "user_id")
	@ApiModelProperty(value="用户ID",dataType="Long",name="userId")
	private Long userId;
	/**
	 * 模版ID
	 */
	@TableField(value = "template_id")
	@ApiModelProperty(value="模版ID",dataType="Long",name="templateId")
	private Long templateId;
	/**
	 * 创建时间
	 */
	@TableField(value = "create_time")
	@ApiModelProperty(value="创建时间",dataType="String",name="createTime")
	@JsonFormat(shape = JsonFormat.Shape.STRING,pattern = "yyyy-MM-dd HH:mm:ss",timezone="GMT+8")
	private Date createTime;
	/**
	 * 更新时间
	 */
	@TableField(value = "update_time")
	@ApiModelProperty(value="工单下游反馈完成时间",dataType="String",name="updateTime")
	@JsonFormat(shape = JsonFormat.Shape.STRING,pattern = "yyyy-MM-dd HH:mm:ss",timezone="GMT+8")
	private Date updateTime;

	public SysUserTemplate(Long userId,Long templateId){
		this.userId = userId;
		this.templateId = templateId;
	}
}
