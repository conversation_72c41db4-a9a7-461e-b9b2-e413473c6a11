package com.pukka.iptv.common.data.dto;

import com.pukka.iptv.common.base.enums.RequestResourceEnum;
import com.pukka.iptv.common.base.enums.SourceEnum;
import com.pukka.iptv.common.data.model.cms.CmsPhysicalChannel;
import com.pukka.iptv.common.data.model.in.InPhysicalChannel;
import lombok.Data;
import org.springframework.beans.BeanUtils;
import org.springframework.util.Assert;

import java.io.Serializable;

@Data
public class CmsPhysicalChannelDto extends CmsPhysicalChannel implements Serializable {
    // 专线创建1  人工创建2
    private Integer requestResource;

    private String ids;

    private String cspId;

    public void validRequestResource() {
        Assert.notNull(requestResource, "请选择人工创建或专线创建");
    }

    //物理频道新增参数校验
    public void validChannelSave() {
        if (requestResource.equals(RequestResourceEnum.SYSTEM.getCode())) {
            validChannel();
        }
    }


    //物理频道集编辑参数校验
    public void validChannelUpdate() {
        if (requestResource.equals(RequestResourceEnum.SYSTEM.getCode())) {
            Assert.notNull(getId(), "物理频道ID不能为空");
            validChannel();
        }
    }

    public CmsPhysicalChannelDto(InPhysicalChannel inPhysicalChannel) {
        BeanUtils.copyProperties(inPhysicalChannel, this, "id", "createTime", "updateTime", "channelId", "channelName", "channelCode");
        this.setSource(SourceEnum.SYSWORK.getValue());
    }

    public CmsPhysicalChannelDto() {

    }

    //物理频道基础参数校验
    private void validChannel() {
        Assert.hasText(getMultiCastIp(), "组播IP不能为空");
        Assert.hasText(getMultiCastPort(), "组播端口不能为空");
        if (getMultiCastPort().length() > 10) {
            throw new IllegalArgumentException("组播端口不能大于十位");

        }
        Assert.hasText(getChannelName(), "所属频道不能为空");
    }
}
