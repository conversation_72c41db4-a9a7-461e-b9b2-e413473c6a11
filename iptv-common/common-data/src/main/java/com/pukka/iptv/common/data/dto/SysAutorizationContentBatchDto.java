package com.pukka.iptv.common.data.dto;

import com.pukka.iptv.common.data.model.bms.BmsContent;
import com.pukka.iptv.common.data.model.bms.BmsPicture;
import com.pukka.iptv.common.data.model.bms.BmsProgram;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * @ClassName SysAutorizationContentBatchDto
 * @Description TODO
 * <AUTHOR>
 * @Date 2022/4/27 15:21
 * @Version
 */
@Data
public class SysAutorizationContentBatchDto implements Serializable {

    private String message;

    private List<BmsPictureDto> bmsPictureListBatch;

    private List<BmsProgram> bmsProgramBatch;

    private List<BmsContent> bmsContentList;

    private List<BmsPictureDto> bmsPictureListForProgramBatch;


}
