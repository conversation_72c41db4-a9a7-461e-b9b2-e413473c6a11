package com.pukka.iptv.common.data.vo.copyright;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.pukka.iptv.common.data.model.copyright.RuleProhibit;
import io.swagger.annotations.ApiModel;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * @Author: chiron
 * @Date: 2022/08/04/10:25
 * @Description:
 */
@Data
@EqualsAndHashCode
@NoArgsConstructor
@ApiModel("RuleProhibitListVo")
@JsonIgnoreProperties(ignoreUnknown = true)
public class RuleProhibitListVo implements java.io.Serializable{

    /**
     * 集合
     */
    private List<RuleProhibit> list;
}
