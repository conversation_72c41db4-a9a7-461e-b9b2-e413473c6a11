package com.pukka.iptv.common.data.model.bms;

import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import com.alibaba.excel.converters.date.DateStringConverter;
import com.baomidou.mybatisplus.annotation.*;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.pukka.iptv.common.data.converter.cms.SettlementConverter;
import com.pukka.iptv.common.data.converter.bms.*;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Date;

/**
 * @author: luo
 * @date: 2021-9-9 11:27:16
 */

@Data
@EqualsAndHashCode
@JsonIgnoreProperties(value = {"handler"}, ignoreUnknown = true)
@TableName(value = "bms_content", autoResultMap = true)
public class BmsContent extends Model<BmsContent> implements java.io.Serializable, UpdatePublishStatusInterface {

    private static final long serialVersionUID = 1L;
    /**
     * 主键
     */
    @ExcelIgnore
    @TableField(value = "id")
    @TableId(type = IdType.AUTO)
    @ApiModelProperty(value = "主键", dataType = "Long", name = "id")
    private Long id;
    /**
     * 全局唯一标识
     */
    @ExcelIgnore
    @ColumnWidth(40)
    @TableField(value = "code", fill = FieldFill.INSERT)
    @ApiModelProperty(value = "全局唯一标识", dataType = "String", name = "code")
    private String code;
    /**
     * 节目名称
     */
    @TableField(value = "name")
    @ColumnWidth(30)
    @ApiModelProperty(value = "节目名称", dataType = "String", name = "name")
    @ExcelProperty(value = "节目名称",index = 0)
    private String name;
    /**
     * 节目订购编号
     */
    @ExcelIgnore
    @TableField(value = "order_number")
    @ApiModelProperty(value = "节目订购编号", dataType = "String", name = "orderNumber")
    private String orderNumber;
    /**
     * 原名
     */
    @ColumnWidth(30)
    @ExcelProperty(value = "别名",index = 3)
    @TableField(value = "original_name")
    @ApiModelProperty(value = "原名", dataType = "String", name = "originalName")
    private String originalName;
    /**
     * 有效开始时间（YYYYMMDDHH24MiSS）
     */
    @ExcelProperty(value = "授权开始时间",index = 11,converter = LicensingConverter.class)
    @TableField(value = "licensing_window_start")
    @ApiModelProperty(value = "有效开始时间（YYYYMMDDHH24MiSS）", dataType = "String", name = "licensingWindowStart")
    private String licensingWindowStart;
    /**
     * 有效结束时间（YYYYMMDDHH24MiSS）
     */
    @ExcelProperty(value = "授权结束时间",index = 12,converter = LicensingConverter.class)
    @TableField(value = "licensing_window_end")
    @ApiModelProperty(value = "有效结束时间（YYYYMMDDHH24MiSS）", dataType = "String", name = "licensingWindowEnd")
    private String licensingWindowEnd;
    /**
     * 新到天数
     */
    @ExcelIgnore
    @TableField(value = "display_as_new")
    @ApiModelProperty(value = "新到天数", dataType = "Long", name = "displayAsNew")
    private Long displayAsNew;
    /**
     * 剩余天数
     */
    @ExcelIgnore
    @TableField(value = "display_as_last_chance")
    @ApiModelProperty(value = "剩余天数 ", dataType = "Long", name = "displayAsLastChance")
    private Long displayAsLastChance;
    /**
     * 媒资分类ID，来自pgm_category表
     */
    @ExcelIgnore
    @TableField(value = "pgm_category_id")
    @ApiModelProperty(value = "媒资分类ID，来自pgm_category表", dataType = "Long", name = "pgmCategoryId")
    private Long pgmCategoryId;
    /**
     * 媒资分类，节目形态，如：新闻，电影
     */
    @ExcelIgnore
    @TableField(value = "pgm_category")
    @ApiModelProperty(value = "媒资分类，节目形态，如：新闻，电影", dataType = "String", name = "pgmCategory")
    private String pgmCategory;
    /**
     * 媒资类型ID，来自pgm_snd_class表
     */
    @ExcelIgnore
    @TableField(value = "pgm_snd_class_id")
    @ApiModelProperty(value = "媒资类型ID，来自pgm_snd_class表", dataType = "Long", name = "pgmSndClassId")
    private String pgmSndClassId;
    /**
     * 二级标签，如：动作，科幻
     */
    @ExcelIgnore
    @TableField(value = "pgm_snd_class")
    @ApiModelProperty(value = "二级标签，如：动作，科幻", dataType = "String", name = "pgmSndClass")
    private String pgmSndClass;
    /**
     * 状态标志 1:生效 0:失效
     */
    @ExcelProperty(value = "状态标志",index = 4,converter = StatusConverter.class)
    @TableField(value = "status")
    @ApiModelProperty(value = "状态标志 1:生效 0:失效", dataType = "Integer", name = "status")
    private Integer status;
    /**
     * 内容提供商标识
     */
    @ExcelProperty(value = "内容提供商标识",index = 2)
    @TableField(value = "content_provider")
    @ApiModelProperty(value = "内容提供商标识", dataType = "String", name = "contentProvider")
    private String contentProvider;
    /**
     * cpId
     */
    @ExcelIgnore
    @TableField(value = "cp_id")
    @ApiModelProperty(value = "cpId", dataType = "Long", name = "cpId")
    private Long cpId;
    /**
     * CP名称
     */
    @ExcelProperty(value = "CP名称",index = 1)
    @TableField(value = "cp_name")
    @ApiModelProperty(value = "CP名称", dataType = "String", name = "cpName")
    private String cpName;
    /**
     * 创建时间
     */
    @ExcelIgnore
    @TableField(value = "create_time", fill = FieldFill.INSERT)
    @ApiModelProperty(value = "创建时间", dataType = "String", name = "createTime")
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date createTime;
    /**
     * 更新时间
     */
    @ExcelProperty(value = "更新时间",index = 8)
    @TableField(value = "update_time", fill = FieldFill.UPDATE)
    @ApiModelProperty(value = "更新时间", dataType = "String", name = "updateTime")
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date updateTime;
    /**
     * spId
     */
    @ExcelIgnore
    @TableField(value = "sp_id")
    @ApiModelProperty(value = "spId", dataType = "Long", name = "spId")
    private Long spId;
    /**
     * SP名称
     */
    @ExcelIgnore
    @TableField(value = "sp_name")
    @ApiModelProperty(value = "SP名称", dataType = "String", name = "spName")
    private String spName;
    /**
     * 1：电影 3：电视剧 4：系列片 5：片花 此处没有子集，子集由program表单独保存
     */
    @ExcelProperty(value = "媒资类型",index = 9,converter = ContentTypeConverter.class)
    @TableField(value = "content_type")
    @ApiModelProperty(value = "1：电影 3：电视剧 4：系列片 5：片花 此处没有子集，子集由program表单独保存", dataType = "Integer", name = "contentType")
    private Integer contentType;
    /**
     * 审核状态 1：未审核 2：审核中 3：审核通过 4：未通过 2：通过
     */
    @ExcelIgnore
    @TableField(value = "cp_check_status")
    @ApiModelProperty(value = "审核状态 1：未审核 2：审核中 3：审核未通过 4：通过 ", dataType = "Integer", name = "cpCheckStatus")
    private Integer cpCheckStatus;
    /**
     * 自审描述
     */
    @ExcelIgnore
    @TableField(value = "cp_check_desc")
    @ApiModelProperty(value = "自审描述", dataType = "String", name = "cpCheckDesc")
    private String cpCheckDesc;
    /**
     * 审核时间
     */
    @ExcelIgnore
    @TableField(value = "cp_check_time")
    @ApiModelProperty(value = "审核时间", dataType = "String", name = "cpCheckTime")
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date cpCheckTime;
    /**
     * 审核人
     */
    @ExcelIgnore
    @TableField(value = "cp_checker")
    @ApiModelProperty(value = "审核人", dataType = "String", name = "cpChecker")
    private String cpChecker;
    /**
     * op审核 1：op未审核 2：审核中 3：审核通过 4：审核未通过
     */
    @ExcelIgnore
    @TableField(value = "op_check_status")
    @ApiModelProperty(value = "op审核 1：op未审核 2：审核中 3：审核未通过 4：审核通过", dataType = "Integer", name = "opCheckStatus")
    private Integer opCheckStatus;
    /**
     * op审核描述
     */
    @ExcelIgnore
    @TableField(value = "op_check_desc")
    @ApiModelProperty(value = "op审核描述", dataType = "String", name = "opCheckDesc")
    private String opCheckDesc;
    /**
     * 终审人员
     */
    @ExcelIgnore
    @TableField(value = "op_checker")
    @ApiModelProperty(value = "终审人员", dataType = "String", name = "opChecker")
    private String opChecker;
    /**
     * 终审时间
     */
    @TableField(value = "op_check_time")
    @ExcelIgnore
    @ApiModelProperty(value = "终审时间", dataType = "String", name = "opCheckTime")
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date opCheckTime;
    /**
     * 来源1：专线注入 2：人工创建
     */
    @ExcelIgnore
    @TableField(value = "source")
    @ApiModelProperty(value = "来源1：专线注入 2：人工创建", dataType = "Integer", name = "source")
    private Integer source;
    /**
     * 内容编辑锁状态1:未锁定 2:已锁定
     */
    @ExcelIgnore
    @TableField(value = "lock_status")
    @ApiModelProperty(value = "内容编辑锁状态1:未锁定 2:已锁定", dataType = "Integer", name = "lockStatus")
    private Integer lockStatus;
    /**
     * 发布通道ID，多个ID以英文逗号隔开
     */
    @ExcelIgnore
    @TableField(value = "out_passage_ids")
    @ApiModelProperty(value = "发布通道ID，多个ID以英文逗号隔开", dataType = "String", name = "outPassageIds")
    private String outPassageIds;
    /**
     * 分发通道名称以英文逗号 隔开
     */
    @ExcelIgnore
    @TableField(value = "out_passage_names")
    @ApiModelProperty(value = "分发通道名称以英文逗号 隔开", dataType = "String", name = "outPassageNames")
    private String outPassageNames;
    /**
     * 发布状态  1：待发布 2：发布中 3：发布成功 4：发布失败 5：待更新 6：更新中 7：更新失败 8：回收中 9：回收成功 10：回收失败
     */
    @ExcelProperty(value = "发布状态",index = 5,converter = PublicStatusConverter.class)
    @TableField(value = "publish_status")
    @ApiModelProperty(value = "发布状态  1：待发布 2：发布中 3：发布成功 4：发布失败 5：待更新 6：更新中 7：更新失败 8：回收中 9：回收成功 10：回收失败", dataType = "Integer", name = "publishStatus")
    private Integer publishStatus;
    /**
     * 发布时间
     */
    @ExcelProperty(value = "发布时间",index = 6,converter = DateStringConverter.class)
    @TableField(value = "publish_time")
    @ApiModelProperty(value = "发布时间", dataType = "Date", name = "publishTime")
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date publishTime;
    /**
     * 发布描述
     */
    @ExcelIgnore
    @ColumnWidth(30)
    @TableField(value = "publish_description")
    @ApiModelProperty(value = "publishDescription", dataType = "String", name = "publishDescription")
    private String publishDescription;
    /**
     * 定时发布，时间存在则代表已经设定为定时发布
     */
    @ExcelIgnore
    @TableField(value = "timed_publish")
    @ApiModelProperty(value = "定时发布，时间存在则代表已经设定为定时发布", dataType = "String", name = "timedPublish")
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date timedPublish;
    /**
     * 定时发布状态，1-定时发布，0-取消定时发布
     */
    @ExcelIgnore
    @TableField(value = "timed_publish_status")
    @ApiModelProperty(value = "定时发布状态", dataType = "String", name = "timedPublishStatus")
    private Integer timedPublishStatus;
    /**
     * 定时发布，时间存在则代表已经设定为定时发布
     */
    @ExcelIgnore
    @TableField(value = "timed_publish_description")
    @ApiModelProperty(value = "定时发布描述", dataType = "String", name = "timedPublishDescription")
    private String timedPublishDescription;
    /**
     * CP内容编码
     */
    @ExcelProperty(value = "唯一标识",index = 7)
    @TableField(value = "cms_content_code")
    @ApiModelProperty(value = "CP内容编码", dataType = "String", name = "cmsContentCode")
    private String cmsContentCode;
    /**
     * 内容ID
     */
    @ExcelIgnore
    @TableField(value = "cms_content_id")
    @ApiModelProperty(value = "内容ID", dataType = "Long", name = "cmsContentId")
    private Long cmsContentId;
    /**
     * 缺集信息，信息存在说明缺集
     */
    @ExcelIgnore
    @TableField(value = "missed_info")
    @ApiModelProperty(value = "缺集信息，信息存在说明缺集", dataType = "String", name = "missedInfo")
    private String missedInfo;
    /**
     * 所属渠道
     */
    @ExcelIgnore
    @TableField(value = "bms_sp_channel_name")
    @ApiModelProperty(value = "所属渠道", dataType = "String", name = "bmsSpChannelName")
    private String bmsSpChannelName;
    /**
     * 所属渠道id
     */
    @ExcelIgnore

    @TableField(value = "bms_sp_channel_id")
    @ApiModelProperty(value = "所属渠道id", dataType = "Long", name = "bmsSpChannelId")
    private Long bmsSpChannelId;
    /**
     * 产品包ID集合，以英文逗号分割
     */
    @ExcelIgnore
    @TableField(value = "package_ids")
    @ApiModelProperty(value = "产品包ID集合，以英文逗号分割", dataType = "String", name = "packageIds")
    private String packageIds;
    /**
     * 产品包name集合，以英文逗号分割
     */
    @ExcelIgnore
    @TableField(value = "package_names")
    @ApiModelProperty(value = "产品包name集合，以英文逗号分割", dataType = "String", name = "packageNames")
    private String packageNames;
    /**
     * 栏目ID集合，以英文逗号分割
     */
    @ExcelIgnore
    @TableField(value = "category_ids")
    @ApiModelProperty(value = "栏目ID集合，以英文逗号分割", dataType = "String", name = "categoryIds")
    private String categoryIds;
    /**
     * 栏目Name集合，以英文逗号分割
     */
    @TableField(value = "category_names")
    @ExcelIgnore
    @ApiModelProperty(value = "栏目Name集合，以英文逗号分割", dataType = "String", name = "categoryNames")
    private String categoryNames;


    /**
     * 正片关联状态 1：待关联 2：已关联
     */
    @ExcelIgnore
    @TableField(value = "release_status")
    @ApiModelProperty(value = "正片关联状态 1：待关联 2：已关联", dataType = "Integer", name = "releaseStatus")
    private Integer releaseStatus;
    /**
     * 预览片关联状态 1：待关联 2：已关联
     */

    @ExcelIgnore
    @TableField(value = "preview_status")
    @ApiModelProperty(value = "预览片关联状态 1：待关联 2：已关联", dataType = "Integer", name = "previewStatus")
    private Integer previewStatus;

    /**
     * CP反馈标识，当内容状态变更，变为待发布，发布成功，回收成功时，需要结合status向内容所属CP反馈发布到运营商的状态.
     * 1：需要反馈，2：反馈结束。
     */
    @ExcelIgnore
    @TableField(value = "cp_feedback_flag")
    @ApiModelProperty(value = "CP反馈标识，当内容状态变更，变为待发布，发布成功，回收成功，时，需要向内容所属CP反馈发布到运营商的状态", dataType = "Integer", name = "cpFeedbackFlag")
    private Integer cpFeedbackFlag;

    /**
     * 节目清晰度标识
     */
    @ExcelProperty(value = "清晰度",index = 10, converter = SettlementConverter.class)
    @TableField(value = "definition_flag",updateStrategy=FieldStrategy.IGNORED)
    @ApiModelProperty(value = "节目清晰度标识 0：标清 1：高清  2：超清 3: 4K  4: 杜比（4K+杜比）\n", dataType = "Integer", name = "definitionFlag")
    private Integer definitionFlag;
    /**
     * 节目时长（分钟）
     */
    @ExcelProperty(value = "时长",index = 13)
    @TableField(value = "duration")
    @ApiModelProperty(value = "节目时长（分钟）", dataType = "Long", name = "duration")
    private Integer duration;
    /**
     * 首次下发时间
     */
    @ExcelIgnore
    @TableField(value = "first_publish_time")
    @ApiModelProperty(value = "首次下发时间", dataType = "String", name = "firstPublishTime")
    private String firstPublishTime;

    /**
     * 预留字段
     */
    @TableField(value = "extendInfoList")
    @ApiModelProperty(value = "预留字段", dataType = "String", name = "extendInfoList")
    private String extendInfoList;

    /**
     * 节目试看(秒)
     */
    @TableField(value = "preDuration")
    @ApiModelProperty(value = "节目试看(秒)", dataType = "Long", name = "preDuration")
    private Long preDuration;

    /**
     * 子集发布失败提示 0：不提醒 -1：提醒
     */
    @ExcelIgnore
    @TableField(value = "sub_remind")
    @ApiModelProperty(value = "子集发布失败提示", dataType = "Long", name = "subRemind")
    private Integer subRemind;
}
