package com.pukka.iptv.common.data.dto;

import com.pukka.iptv.common.data.model.cms.CmsPlayerPicture;
import com.pukka.iptv.common.data.model.cms.CmsPlayerSlice;
import lombok.Data;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import javax.validation.constraints.NotBlank;
import java.util.List;

/**
 * 播放器上报参数实体
 */
@Getter
@ToString
public class PlayerReportDto {

    /** 以下参数名 播放器已经固定，不可更改 */


    @NotBlank
    @Setter
    private String moviecode;

    /** 视频url */
    @NotBlank
    @Setter
    private String file;

    /** 切片信息 */
    List<Slice> slices;

    /** 图片信息 */
    List<Pic> pic;

    /** 参数可用， slices和pic任意不为空 则为 true 可用 */
    private boolean available;


    public void setSlices(List<Slice> slices) {
        if (null != slices && !slices.isEmpty()) {
            available = true;
        }
        this.slices = slices;
    }

    public void setPic(List<Pic> pic) {
        if (null != pic && !pic.isEmpty()) {
            available = true;
        }
        this.pic = pic;
    }




    @Data
    public static class Slice {
        String begin;
        String end;
    }


    @Data
    public static class Pic {
        String code;
        String url;
    }
}
