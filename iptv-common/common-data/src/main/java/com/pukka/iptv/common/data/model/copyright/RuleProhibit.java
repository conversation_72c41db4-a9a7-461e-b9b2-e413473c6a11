package com.pukka.iptv.common.data.model.copyright;

import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelProperty;
import com.baomidou.mybatisplus.annotation.*;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.pukka.iptv.common.data.converter.rule.RuleProhibitConverter;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.Date;

/**
 * @Author: chiron
 * @Date: 2022/07/25/09:46
 * @Description:违禁规则
 */
@Data
@NoArgsConstructor
@EqualsAndHashCode
@Accessors(chain = true)
@JsonIgnoreProperties(value = {"handler"})
@TableName(value = "rule_prohibit", autoResultMap = true)
public class RuleProhibit extends Model<RuleProhibit> implements Serializable {
    private static final long serialVersionUID = -2716379088706533074L;

    /**
     * 主键
     */
    @ExcelIgnore
    @TableId(type = IdType.AUTO)
    @TableField(value = "id")
    @ApiModelProperty(value = "ObjectID", dataType = "Long", name = "id")
    private Long id;
    /**
     * 全局唯一标识
     */
    @ExcelIgnore
    @TableField(value = "code")
    @ApiModelProperty(value = "违禁规则code", dataType = "String", name = "code")
    private String code;
    /**
     * 违禁规则名称
     */
    @ExcelProperty("违禁规则名称")
    @TableField(value = "show_name",updateStrategy=FieldStrategy.NOT_EMPTY)
    @ApiModelProperty(value = "违禁规则名称", dataType = "String", name = "showName")
    private String showName;
    /**
     * 违禁规则媒资分类ID，来自pgm_category表
     */
    @ExcelIgnore
    @TableField(value = "pgm_category_id",updateStrategy=FieldStrategy.IGNORED)
    @ApiModelProperty(value = "违禁规则媒资分类ID，来自pgm_category表", dataType = "Long", name = "pgmCategoryId")
    private Long pgmCategoryId;
    /**
     * 节目形态，如：新闻，电影
     */
    @ExcelProperty("媒资分类")
    @TableField(value = "pgm_category",updateStrategy=FieldStrategy.IGNORED)
    @ApiModelProperty(value = "节目形态，如：新闻，电影", dataType = "String", name = "pgmCategory")
    private String pgmCategory;
    /**
     * 违禁规则节目类型
     */
    @TableField(exist = false)
    @ExcelIgnore
    @ApiModelProperty(value = "违禁规则节目类型，如：单集，剧集", dataType = "String", name = "contentType")
    private String contentTypes;
    /**
     * 违禁规则节目类型
     */
    @ExcelProperty(value = "节目类型",converter = RuleProhibitConverter.class)
    @TableField(value = "content_type",updateStrategy=FieldStrategy.IGNORED)
    private Integer contentType;
    /**
     * 主要人物
     */
    @ExcelProperty("主演")
    @TableField(value = "kpeople",updateStrategy=FieldStrategy.IGNORED)
    @ApiModelProperty(value = "主要人物", dataType = "String", name = "kpeople")
    private String kpeople;
    /**
     * 导演
     */
    @ExcelProperty("导演")
    @TableField(value = "director",updateStrategy=FieldStrategy.IGNORED)
    @ApiModelProperty(value = "导演", dataType = "String", name = "director")
    private String director;
    /**
     * 产地id，来自于sys_dictionary_item表
     */
    @ExcelIgnore
    @TableField(value = "original_country_id",updateStrategy=FieldStrategy.IGNORED)
    @ApiModelProperty(value = "产地id，来自于sys_dictionary_item表", dataType = "Long", name = "originalCountryId")
    private Long originalCountryId;
    /**
     * 产地名称，来自于sys_dictionary_item表
     */
    @ExcelProperty("产地")
    @TableField(value = "original_country",updateStrategy=FieldStrategy.IGNORED)
    @ApiModelProperty(value = "产地名称，来自于sys_dictionary_item表", dataType = "String", name = "originalCountry")
    private String originalCountry;
    /**
     * 上映年份
     */
    @ExcelProperty("年份")
    @TableField(value = "release_year",updateStrategy=FieldStrategy.IGNORED)
    @ApiModelProperty(value = "上映年份", dataType = "String", name = "releaseYear")
    private String releaseYear;
    /**
     * 创建人
     */
    @ExcelProperty("添加人员")
    @TableField(value = "creator_name")
    @ApiModelProperty(value = "创建人", dataType = "String", name = "creatorName")
    private String creatorName;
    /**
     * 创建时间
     */
    @ExcelProperty("添加时间")
    @TableField(value = "create_time")
    @ApiModelProperty(value = "创建时间", dataType = "String", name = "createTime")
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date createTime;
    /**
     * 更新时间
     */
    @ExcelIgnore
    @TableField(value = "update_time",fill = FieldFill.INSERT_UPDATE)
    @ApiModelProperty(value = "更新时间", dataType = "String", name = "updateTime")
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date updateTime;
}
