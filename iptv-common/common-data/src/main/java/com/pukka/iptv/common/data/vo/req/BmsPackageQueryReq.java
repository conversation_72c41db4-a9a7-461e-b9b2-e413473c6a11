package com.pukka.iptv.common.data.vo.req;

import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

/**
 * <AUTHOR>
 * @create 2021-09-09 17:39
 */
@Getter
@Setter
public class BmsPackageQueryReq{
    @ApiModelProperty(value = "spIds")
    private Long spId;

    @ApiModelProperty(value = "产品包名称")
    private String name;

    @ApiModelProperty(value = "渠道名称")
    private Integer bmsSpChannelId;

    @ApiModelProperty(value = "资费类型  0:免费 1:单包月 2:续包月 3:单包季 4:续包季 5:单包半年 6:单包年 7:单点收费")
    private Integer priceType;

    @ApiModelProperty(value = "发布状态  1:待发布 2:发布中 3:发布成功 4:发布失败 5:待更新 6:更新中 7:更新失败 8:回收中 9:回收成功 10:回收失败")
    private Integer publishStatus;
}
