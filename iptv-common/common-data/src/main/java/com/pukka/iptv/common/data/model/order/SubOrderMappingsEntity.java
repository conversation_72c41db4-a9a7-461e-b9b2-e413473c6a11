package com.pukka.iptv.common.data.model.order;

import com.pukka.iptv.common.base.enums.ActionEnums;
import com.pukka.iptv.common.data.model.in.InMapping;
import com.pukka.iptv.common.data.util.SafeUtil;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.io.Serializable;
import java.util.HashMap;
import java.util.Map;
import java.util.UUID;

/**
 * <AUTHOR>
 * @Date 2021/8/31 3:55 下午
 * @description: 组装mapping信息
 * @Version 1.0
 */
@Getter
@Setter
@ToString
public class SubOrderMappingsEntity implements Serializable {
    private static final long serialVersionUID = 870285093748852927L;
    /**
     * 唯一id
     */
    private String id;
    /**
     * 序号
     */
    private int serialNumber;
    /**
     * 父类型
     */
    private String parentType;
    /**
     * 父id
     */
    private String parentId;
    /**
     * 父代码
     */
    private String parentCode;
    /**
     * 类型
     */
    private String elementType;
    /**
     * id
     */
    private String elementId;
    /**
     * 代码
     */
    private String elementCode;
    /**
     * 发布状态
     */
    private String publishStatus;
    /**
     * 操作类型
     */
    private String action;
    /**
     * 属性集
     */
    private Map<String,String> propertyDic;

    public SubOrderMappingsEntity(InMapping inMapping) {
        propertyDic = new HashMap<>();
        parentType = SafeUtil.getString(inMapping.getParentType());
        parentCode = SafeUtil.getString(inMapping.getParentCode());
        parentId = SafeUtil.getString(inMapping.getParentId());
        elementType = SafeUtil.getString(inMapping.getElementType());
        elementCode = SafeUtil.getString(inMapping.getElementCode());
        elementId = SafeUtil.getString(inMapping.getElementId());
        action = ActionEnums.getInfoByCode(inMapping.getAction());
        id = UUID.randomUUID().toString();
        getPropertyDic().put("Sequence",SafeUtil.getString(inMapping.getSequence()));
        getPropertyDic().put("Type", SafeUtil.getString(inMapping.getType()));
    }

    public SubOrderMappingsEntity() {}

    public SubOrderMappingsEntity(InMapping inMapping, String spId) {
        propertyDic = new HashMap<>();
        parentType = SafeUtil.getString(inMapping.getParentType());
        parentCode = SafeUtil.getString(inMapping.getParentCode());
        parentId = SafeUtil.getString(inMapping.getParentId());
        elementType = SafeUtil.getString(inMapping.getElementType());
        elementCode = SafeUtil.getString(inMapping.getElementCode());
        elementId = SafeUtil.getString(inMapping.getElementId());
        action = ActionEnums.getInfoByCode(inMapping.getAction());
        id = UUID.randomUUID().toString();
        getPropertyDic().put("Sequence",SafeUtil.getString(inMapping.getSequence()));
        getPropertyDic().put("Type", SafeUtil.getString(inMapping.getType()));
    }
}
