package com.pukka.iptv.common.data.model.sys;

import com.alibaba.excel.annotation.ExcelProperty;
import com.baomidou.mybatisplus.annotation.*;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.NoArgsConstructor;
import lombok.EqualsAndHashCode;
import java.util.Date;
import java.util.List;

import lombok.Data;

/**
 *
 * @author: luo
 * @date: 2021-9-9 8:36:53
 */

@Data
@EqualsAndHashCode
@JsonIgnoreProperties(value = { "handler" })
@NoArgsConstructor
@TableName(value = "sys_cp",autoResultMap=true)
public class SysCp extends Model<SysCp> implements java.io.Serializable{

    private static final long serialVersionUID = 1L;
	/**id*/
    @TableId(type = IdType.AUTO)
	@TableField(value = "id")
    @ApiModelProperty(value="id",dataType="Long",name="id")
    private Long id;
	/**CP名称*/
	@TableField(value = "name")
    @ApiModelProperty(value="CP名称",dataType="String",name="name")
    private String name;
	/**code*/
	@TableField(value = "code")
    @ApiModelProperty(value="code",dataType="String",name="code")
    private String code;
	/**createTime*/
	@TableField(value = "create_time")
    @ApiModelProperty(value="创建时间",dataType="String",name="createTime")
    @JsonFormat(shape = JsonFormat.Shape.STRING,pattern = "yyyy-MM-dd HH:mm:ss",timezone="GMT+8")
    private Date createTime;
	/**updateTime*/
	@TableField(value = "update_time")
    @ApiModelProperty(value="更新时间",dataType="String",name="updateTime")
    @JsonFormat(shape = JsonFormat.Shape.STRING,pattern = "yyyy-MM-dd HH:mm:ss",timezone="GMT+8")
    private Date updateTime;
	/**状态   1可用 2:停用 255：已删除*/
	@TableField(value = "status")
    @ApiModelProperty(value="状态   1可用 2:停用 255：已删除",dataType="Integer",name="status")
    private Integer status;
	/**存储管理配置表主键id*/
	@TableField(value = "storage_id")
    @ApiModelProperty(value="存储管理配置表主键id",dataType="Long",name="storageId")
    private Long storageId;
	/**存储管理配置表名称*/
	@TableField(value = "storage_name")
    @ApiModelProperty(value="存储管理配置表名称",dataType="String",name="storageName")
    private String storageName;
    /**总空间大小*/
    @TableField(value = "total_space")
    @ApiModelProperty(value="CP总空间大小",dataType="Long",name="totalSpace")
    private Long totalSpace;
    /**已使用比例*/
    @TableField(value = "space_threshold")
    @ApiModelProperty(value="CP磁盘阈值",dataType="java.lang.Double",name="spaceThreshold")
    private java.lang.Double spaceThreshold;
	/**是否使用转码服务1：使用 2：不使用*/
	@TableField(value = "use_transcoding")
    @ApiModelProperty(value="是否使用转码服务1：使用 2：不使用",dataType="Integer",name="useTranscoding")
    private Integer useTranscoding;
	/**是否跳过审核  1：不跳过   2：跳过自审  3：跳过终审  4：全部跳过*/
	@TableField(value = "skip_check")
    @ApiModelProperty(value="是否跳过审核  1：不跳过   2：跳过自审  3：跳过终审  4：全部跳过",dataType="Integer",name="skipCheck")
    private Integer skipCheck;
	/**是否使用媒资库功能 ##IsSearch ##SelectList{1:是,0:否}*/
	@TableField(value = "is_mediare_pository")
    @ApiModelProperty(value="是否使用媒资库功能 ##IsSearch ##SelectList{1:是,0:否}",dataType="Integer",name="mediarePository")
    private Integer mediarePository;
	/**创建人来自sys_user表*/
	@TableField(value = "creator_name", fill = FieldFill.INSERT)
    @ApiModelProperty(value="创建人来自sys_user表",dataType="String",name="creatorName")
    private String creatorName;
	/**来自sys_user表*/
	@TableField(value = "creator_id", fill = FieldFill.INSERT)
    @ApiModelProperty(value="来自sys_user表",dataType="Long",name="creatorId")
    private Long creatorId;
	/**运营类型 1：媒资内容 2：直播频道*/
	@TableField(value = "business_type")
    @ApiModelProperty(value="运营类型 1：媒资内容 2：直播频道",dataType="Integer",name="business_type")
    private Integer businessType;
	/**内容提供商，不存储*/
	@TableField(exist = false)
    @ApiModelProperty(value="内容提供商",dataType="String",name="contentProviders")
    private List<SysCpContentProvider> contentProviders;
    /**BizDomain 0：IPTV  2: WEBTV  3:  混合IPTV和WEBTV*/
    @TableField(value = "biz_domain")
    @ApiModelProperty(value="BizDomain 0：IPTV  2: WEBTV  3:  混合IPTV和WEBTV",dataType="Integer",name="biz_domain")
    private Integer bizDomain;
    /**是否跳过违禁片检测**/
    @TableField(value = "skip_prohibit")
    @ApiModelProperty(value="是否跳过违禁片检测,0:不跳过(自动加入违禁片库)1:不跳过(手动加入违禁片库)2:跳过",dataType="Integer",name="skipProhibit")
    private Integer skipProhibit;
    /**违禁检测出发点**/
    @TableField(value = "point_prohibit")
    @ApiModelProperty(value="违禁检测出发点,0:入库成功1:自审通过",dataType="Integer",name="pointProhibit")
    private Integer pointProhibit;
    /**通过老版工具上传路径已使用空间**/
    @TableField(value = "used_space")
    @ApiModelProperty(value="通过老版工具上传路径已使用空间",dataType="Long",name="usedSpace")
    private Long usedSpace;
}
