package com.pukka.iptv.common.data.vo.bms;

import lombok.Data;
import lombok.experimental.Accessors;

@Data
@Accessors(chain = true)
public class CpFeedbackContentVO {

    private Long id;
    /** 内容提供商节目 code  */
    private String contentCode;
    /**  */
    private String contentName;

    private String cpId;

    /** 内容状态 */
    private Integer status;

    private String spId;

    private Integer publishStatus;

    private Integer contentType;
}
