package com.pukka.iptv.common.data.vo.cms;

import com.pukka.iptv.common.base.enums.ProhibitPointEnum;
import com.pukka.iptv.common.data.model.cms.CmsSeries;
import lombok.Data;
import org.springframework.beans.BeanUtils;

import java.io.Serializable;

/**
 * @Author: wangbo
 * @Date: 2022/4/19 17:12
 */
@Data
public class CmsSeriesVO extends CmsSeries implements Serializable {
    /**
     * 授权 (0：未授权 1：已授权)
     */
    Integer grant;
    private String startTime;

    private String endTime;

    private ProhibitPointEnum prohibitPointEnum;

    public CmsSeriesVO(){

    }

    public CmsSeriesVO(CmsSeries cmsSeries,ProhibitPointEnum prohibitPointEnum){
        BeanUtils.copyProperties(cmsSeries,this);
        this.prohibitPointEnum = prohibitPointEnum;
    }
}
