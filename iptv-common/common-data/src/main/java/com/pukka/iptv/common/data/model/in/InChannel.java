package com.pukka.iptv.common.data.model.in;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.NoArgsConstructor;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Date;

/**
 *
 * @author: tan
 * @date: 2021-8-26 18:19:53
 */

@Data
@EqualsAndHashCode
@JsonIgnoreProperties(value = { "handler" })
@NoArgsConstructor
@TableName(value = "in_channel",autoResultMap=true)
public class InChannel extends Model<InChannel> implements java.io.Serializable{

    private static final long serialVersionUID = 1L;
	/**主键*/
	@TableField(value = "id")
    @TableId(type = IdType.AUTO)
    @ApiModelProperty(value="主键",dataType="Long",name="id")
    private Long id;
	/**全局唯一标识*/
	@TableField(value = "code")
    @ApiModelProperty(value="全局唯一标识",dataType="String",name="code")
    private String code;
	/**建议频道号*/
	@TableField(value = "channel_number")
    @ApiModelProperty(value="建议频道号",dataType="String",name="channelNumber")
    private String channelNumber;
	/**频道名称*/
	@TableField(value = "name")
    @ApiModelProperty(value="频道名称",dataType="String",name="name")
    private String name;
	/**台标名称*/
	@TableField(value = "call_sign")
    @ApiModelProperty(value="台标名称",dataType="String",name="callSign")
    private String callSign;
	/**时移标志 0:不生效 1:生效*/
	@TableField(value = "time_shift")
    @ApiModelProperty(value="时移标志 0:不生效 1:生效",dataType="Integer",name="timeShift")
    private Integer timeShift;
	/**该频道的录制节目 保存时长，可用于 时移和回看，单位为小时。*/
	@TableField(value = "storage_duration")
    @ApiModelProperty(value="该频道的录制节目 保存时长，可用于 时移和回看，单位为小时。",dataType="Integer",name="storageDuration")
    private Integer storageDuration;
	/**默认时移时长, 单位分钟 ,仅仅对 Timeshift 有效*/
	@TableField(value = "time_shift_duration")
    @ApiModelProperty(value="默认时移时长, 单位分钟 ,仅仅对 Timeshift 有效",dataType="Integer",name="timeShiftDuration")
    private Integer timeShiftDuration;
	/**描述信息*/
	@TableField(value = "description")
    @ApiModelProperty(value="描述信息",dataType="String",name="description")
    private String description;
	/**国家*/
	@TableField(value = "country")
    @ApiModelProperty(value="国家",dataType="String",name="country")
    private String country;
	/**州/省 */
	@TableField(value = "state")
    @ApiModelProperty(value="州/省 ",dataType="String",name="state")
    private String state;
	/**城市*/
	@TableField(value = "city")
    @ApiModelProperty(value="城市",dataType="String",name="city")
    private String city;
	/**邮编 */
	@TableField(value = "zip_code")
    @ApiModelProperty(value="邮编 ",dataType="String",name="zipCode")
    private String zipCode;
	/**频道类型 1:直播频道*/
	@TableField(value = "type")
    @ApiModelProperty(value="频道类型 1:直播频道",dataType="Integer",name="type")
    private Integer type;
	/**当 Type 为 1(直播频道) 1: 信号源来自 live 2: 信号源来自 virtual*/
	@TableField(value = "sub_type")
    @ApiModelProperty(value="当 Type 为 1(直播频道) 1: 信号源来自 live 2: 信号源来自 virtual",dataType="Integer",name="subType")
    private Integer subType;
	/**语言*/
	@TableField(value = "language")
    @ApiModelProperty(value="语言",dataType="String",name="language")
    private String language;
	/**状态标志 0:失效 1:生效*/
	@TableField(value = "status")
    @ApiModelProperty(value="状态标志 0:失效 1:生效",dataType="Integer",name="status")
    private Integer status;
	/**播放开始时间(HH24MI)*/
	@TableField(value = "start_time")
    @ApiModelProperty(value="播放开始时间(HH24MI)",dataType="String",name="startTime")
    private String startTime;
	/**播放结束时间(HH24MI)*/
	@TableField(value = "end_time")
    @ApiModelProperty(value="播放结束时间(HH24MI)",dataType="String",name="endTime")
    private String endTime;
	/**拷贝保护标志 0:无拷贝保护 1:有拷贝保护，默认0*/
	@TableField(value = "macrovision")
    @ApiModelProperty(value="拷贝保护标志 0:无拷贝保护 1:有拷贝保护，默认0",dataType="Integer",name="macrovision")
    private Integer macrovision;
	/**双语标志(0否，1是)*/
	@TableField(value = "bilingual")
    @ApiModelProperty(value="双语标志(0否，1是)",dataType="Integer",name="bilingual")
    private Integer bilingual;
	/**内容服务平台标识 */
	@TableField(value = "vsp_code")
    @ApiModelProperty(value="内容服务平台标识 ",dataType="String",name="vspCode")
    private String vspCode;
	/**创建时间*/
	@TableField(value = "create_time")
    @ApiModelProperty(value="创建时间",dataType="String",name="createTime")
    @JsonFormat(shape = JsonFormat.Shape.STRING,pattern = "yyyy-MM-dd HH:mm:ss",timezone="GMT+8")
    private Date createTime;
	/**更新时间*/
	@TableField(value = "update_time")
    @ApiModelProperty(value="更新时间",dataType="String",name="updateTime")
    @JsonFormat(shape = JsonFormat.Shape.STRING,pattern = "yyyy-MM-dd HH:mm:ss",timezone="GMT+8")
    private Date updateTime;
	/**应答文件包含，0: 成功 其他: 错误代码*/
	@TableField(value = "result")
    @ApiModelProperty(value="应答文件包含，0: 成功 其他: 错误代码",dataType="Integer",name="result")
    private Integer result;
	/**应答文件包含，错误描述*/
	@TableField(value = "error_description")
    @ApiModelProperty(value="应答文件包含，错误描述",dataType="String",name="errorDescription")
    private String errorDescription;
	/**动作 1：REGIST，2：UPDATE，3：DELETE*/
	@TableField(value = "action")
    @ApiModelProperty(value="动作 1：REGIST，2：UPDATE，3：DELETE",dataType="Integer",name="action")
    private Integer action;
	/**correlateId*/
	@TableField(value = "correlate_id")
    @ApiModelProperty(value="correlateId",dataType="String",name="correlateId")
    private String correlateId;
}
