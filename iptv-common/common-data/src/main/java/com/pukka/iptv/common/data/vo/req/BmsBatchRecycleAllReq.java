package com.pukka.iptv.common.data.vo.req;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotNull;
import java.util.List;

@Getter
@Setter
@JsonIgnoreProperties(value = { "handler" })
@NoArgsConstructor
@Accessors(chain = true)
public class BmsBatchRecycleAllReq
{
    @NotNull
    List<Long> ids;
    @NotNull
    Integer type;
}
