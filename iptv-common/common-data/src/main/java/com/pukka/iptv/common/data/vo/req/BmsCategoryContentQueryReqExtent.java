package com.pukka.iptv.common.data.vo.req;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.pukka.iptv.common.data.model.bms.BmsCategoryContent;
import com.pukka.iptv.common.data.vo.bms.CheckNameApi;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import javax.validation.constraints.Max;
import javax.validation.constraints.Min;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Pattern;

import static com.pukka.iptv.common.data.util.RegexUtil.ALL_CHAR_REGEX;


/**
 * @author: wz
 * @date: 2021/8/31 11:43
 * @description: 栏目内容关系查询vo
 */
@Getter
@Setter
@JsonIgnoreProperties(ignoreUnknown = true)
public class BmsCategoryContentQueryReqExtent implements CheckNameApi
{

    @NotNull
    @Min(0)
    @Max(Long.MAX_VALUE)
    @ApiModelProperty(value = "spId", dataType = "Long", name = "spId")
    private Long spId;

    @Min(0)
    @Max(Long.MAX_VALUE)
    @ApiModelProperty(value = "cpId", dataType = "Long", name = "cpId")
    private Long cpId;

    @Min(0)
    @Max(Long.MAX_VALUE)
    @ApiModelProperty(value = "categoryId", dataType = "Long", name = "栏目id")
    private Long categoryId;

    @Min(0)
    @Max(Long.MAX_VALUE)
    @ApiModelProperty(value = "categoryId", dataType = "Long", name = "栏目id")
    private Long contentId;
    @Min(0)
    @Max(50)
    @ApiModelProperty(value = "1：电影 3：电视剧 4：系列片 5：片花 此处没有子集，子集由program表单独保存", dataType = "Integer", name = "contentType")
    private Integer contentType;

    @Pattern(regexp = ALL_CHAR_REGEX + "{0,5000}")
    @ApiModelProperty(value = "name", dataType = "String", name = "媒资名称")
    private String name;

    @Min(0)
    @Max(Long.MAX_VALUE)
    @ApiModelProperty(value = "媒资分类ID，来自pgm_category表", dataType = "Long", name = "pgmCategoryId")
    private Long pgmCategoryId;


    @Min(0)
    @Max(20)
    @ApiModelProperty(value = "发布状态  1：待发布 2：发布中 3：发布成功 4：发布失败 5：待更新 6：更新中 7：更新失败 8：回收中 9：回收成功 10：回收失败", dataType = "Integer", name = "publishStatus")
    private Integer publishStatus;

    @Min(0)
    @Max(4)
    @ApiModelProperty(value = "op审核 1：op未审核 2：审核中 3：审核通过 4：审核未通过", dataType = "Integer", name = "opCheckStatus")
    private Integer opCheckStatus;

    @Min(0)
    @Max(4)
    @ApiModelProperty(value = "状态标志 1:生效 0:失效", dataType = "Integer", name = "status")
    private Integer status;

    @Min(0)
    @Max(4)
    @ApiModelProperty(value = "内容编辑锁状态1:未锁定 2:已锁定", dataType = "Integer", name = "lockStatus")
    private Integer lockStatus;

    @Max(4)
    @Min(0)
    @ApiModelProperty(value = "是否缺集 1:缺集 2:不缺", dataType = "Integer", name = "lockStatus")
    private Integer missingEpisodes;

    @ApiModelProperty(value = "原名", dataType = "String", name = "originalName")
    private String originalName;

    private String[] names;
    @Pattern(regexp = ALL_CHAR_REGEX + "{0,5000}")
    @ApiModelProperty(value = "contentName", dataType = "String", name = "媒资名称")
    private String contentName;

    @Pattern(regexp = ALL_CHAR_REGEX + "{0,5000}")
    @ApiModelProperty(value = "categoryName", dataType = "String", name = "栏目名称")
    private String categoryName;

    private String[] categoryNames;

    @Override
    public String getPackageName() {
        return null;
    }

    @Override
    public void setPackageName(String packageName) {

    }

    @Override
    public void setPackageNames(String[] names) {

    }
}
