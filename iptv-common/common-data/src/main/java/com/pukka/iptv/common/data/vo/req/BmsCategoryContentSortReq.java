package com.pukka.iptv.common.data.vo.req;

import com.pukka.iptv.common.data.model.bms.BmsCategoryContent;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

import javax.validation.constraints.Max;
import javax.validation.constraints.Min;
import javax.validation.constraints.NotNull;

/**
 * @Author: wz
 * @Date: 2021/10/7 17:52
 * @Description:
 */
@Getter
@Setter
@Accessors(chain = true)
public class BmsCategoryContentSortReq extends BmsCategoryContent {
    @NotNull
    @Min(0)
    @Max(Long.MAX_VALUE)
    private Long id;

    @NotNull
    @Min(0)
    @Max(Integer.MAX_VALUE)
    private Integer sequence;
}
