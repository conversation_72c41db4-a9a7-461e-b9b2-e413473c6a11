package com.pukka.iptv.common.data.converter.cms;

import com.alibaba.excel.converters.Converter;
import com.alibaba.excel.enums.CellDataTypeEnum;
import com.alibaba.excel.metadata.GlobalConfiguration;
import com.alibaba.excel.metadata.data.WriteCellData;
import com.alibaba.excel.metadata.property.ExcelContentProperty;
import org.apache.commons.lang3.StringUtils;

import java.util.HashMap;
import java.util.Map;

/**
 * @Author: wangbo
 * @Date: 2022/4/19 16:58
 */
public class SeriesTypeConverter implements Converter<Integer> {
    private static final Map<Integer, String> contentMap = new HashMap<>();

    static {
        contentMap.put(0, "连续剧");
        contentMap.put(1, "系列片");
    }

    @Override
    public Class supportJavaTypeKey() {
        return Integer.class;
    }

    @Override
    public CellDataTypeEnum supportExcelTypeKey() {
        return CellDataTypeEnum.STRING;
    }


    /**
     * 这里是写的时候会调用
     */
    @Override
    public WriteCellData<?> convertToExcelData(Integer value, ExcelContentProperty contentProperty, GlobalConfiguration globalConfiguration) throws Exception {
        return new WriteCellData(getContentType(value));
    }

    private String getContentType(Integer value) {
        String result = contentMap.get(value);
        result = StringUtils.isEmpty(result) ? "" : result;
        return result;
    }
}
