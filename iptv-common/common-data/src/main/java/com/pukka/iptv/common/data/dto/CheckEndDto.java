package com.pukka.iptv.common.data.dto;

import lombok.Data;

import javax.validation.constraints.Max;
import javax.validation.constraints.Min;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
@Data
public class CheckEndDto implements Serializable {
    /**
     * 多个id以逗号分隔
     */
    @NotEmpty
    private  String ids;
    /**
     * 审核媒资类型：1单集2子集3电视剧
     */
    @Min(1)
    @Max(3)
    @NotNull
    private  Integer type;
    /**
     * 审核状态 1：待审核 2：审核中 3：未通过 4：通过
     */
    @Min(1)
    @Max(4)
    @NotNull
    private Integer opCheckStatus;
    /**
     * 审核描述
     */
    private String  opCheckDesc;

    /**
     * 1:自审；2:终审；3:重审
     */
    private Integer checkType;
}
