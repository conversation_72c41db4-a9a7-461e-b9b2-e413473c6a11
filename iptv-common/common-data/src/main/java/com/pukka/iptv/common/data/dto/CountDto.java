package com.pukka.iptv.common.data.dto;

import java.util.Date;
import lombok.Data;
import com.fasterxml.jackson.annotation.JsonFormat;

import java.io.Serializable;

@Data
public class CountDto implements Serializable {
    //单集集自审个数
    private Long selfExaminationSingle;
    //子集自审个数
    private Long selfExaminationSon;
    //剧集自审个数
    private  Long selfExaminationDrama;

    //单集终审个数
    private Long finalJudgmentSingle;
    //子集终审个数
    private Long finalJudgmentSon;
    //剧集终审个数
    private  Long finalJudgmentDrama;

    //单集重审个数
    private Long finalJudgmentSingleRetrial;
    //子集重审个数
    private Long finalJudgmentSonRetrial;
    //剧集重审个数
    private  Long finalJudgmentDramaRetrial;
}
