package com.pukka.iptv.common.data.model;

import com.baomidou.mybatisplus.annotation.*;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.NoArgsConstructor;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Date;

/**
 *
 * @author: tan
 * @date: 2021-9-1 16:08:21
 */

@Data
@EqualsAndHashCode
@JsonIgnoreProperties(value = { "handler" })
@NoArgsConstructor
@TableName(value = "out_result",autoResultMap=true)
public class OutResult extends Model<OutResult> implements java.io.Serializable{

    private static final long serialVersionUID = 1L;
	/**id*/
    @TableId(type = IdType.AUTO)
	@TableField(value = "id")
    @ApiModelProperty(value="id",dataType="String",name="id")
    private Long id;
	/**关联ID，主任务ID*/
	@TableField(value = "correlate_id")
    @ApiModelProperty(value="关联ID，主任务ID",dataType="String",name="correlateId")
    private String correlateId;
	/**resultFileUrl*/
	@TableField(value = "result_file_url")
    @ApiModelProperty(value="resultFileUrl",dataType="String",name="resultFileUrl")
    private String resultFileUrl;
	/**接口结果{0:成功,-1:失败}*/
	@TableField(value = "result")
    @ApiModelProperty(value="接口结果{0:成功,-1:失败}",dataType="Integer",name="result")
    private Integer result;
	/**错误描述*/
	@TableField(value = "error_description")
    @ApiModelProperty(value="错误描述",dataType="String",name="errorDescription")
    private String errorDescription;
	/**状态 1：待处理 2：处理中 3：处理成功 4：处理失败*/
	@TableField(value = "status")
    @ApiModelProperty(value="状态 1：待处理 2：处理中 3：处理成功 4：处理失败",dataType="Integer",name="status")
    private Integer status;
	/**状态描述*/
	@TableField(value = "status_description")
    @ApiModelProperty(value="状态描述",dataType="String",name="statusDescription")
    private String statusDescription;
	/**创建时间*/
    @TableField(value = "create_time", fill = FieldFill.INSERT)
    @ApiModelProperty(value="创建时间",dataType="String",name="createTime")
    @JsonFormat(shape = JsonFormat.Shape.STRING,pattern = "yyyy-MM-dd HH:mm:ss",timezone="GMT+8")
    private Date createTime;
	/**更新时间*/
    @TableField(value = "update_time", fill = FieldFill.UPDATE)
    @ApiModelProperty(value="更新时间",dataType="String",name="updateTime")
    @JsonFormat(shape = JsonFormat.Shape.STRING,pattern = "yyyy-MM-dd HH:mm:ss",timezone="GMT+8")
    private Date updateTime;
}
