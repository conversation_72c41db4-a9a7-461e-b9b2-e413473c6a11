package com.pukka.iptv.common.data.vo.req;

import com.pukka.iptv.common.data.model.epg.EpgTemplate;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class EpgTemplateReq extends EpgTemplate implements Serializable {
    private Long userId;
    //0不开启时间排序，1开启时间排序
    private String sortKey;
    //desc降序，asc升序
    private String sortValue;
    private String startTime;
    private String endTime;
}
