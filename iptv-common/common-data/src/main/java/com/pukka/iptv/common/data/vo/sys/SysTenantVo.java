package com.pukka.iptv.common.data.vo.sys;

import com.pukka.iptv.common.data.model.sys.SysTenant;
import io.swagger.annotations.ApiModel;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;
import java.util.Date;
import lombok.Data;
import com.fasterxml.jackson.annotation.JsonFormat;

/**
 *
 * @author: zhengcl
 * @date: 2021-8-18 10:37:54
 */

@Data
@AllArgsConstructor
@JsonIgnoreProperties(value = { "handler" })
@ApiModel("SysTenantVo")
public class SysTenantVo extends SysTenant implements java.io.Serializable{

}
