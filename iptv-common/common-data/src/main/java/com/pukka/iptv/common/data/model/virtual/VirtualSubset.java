package com.pukka.iptv.common.data.model.virtual;

import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

@Data
@NoArgsConstructor
@Accessors(chain = true)
public class VirtualSubset {
    /**
     * 索引
     */
    private Long index;
    /**
     * 名称
     */
    private String name;
    /**
     * 剧集标识
     */
    private String code;
    /**
     * 时长
     */
    private String duration;

    /**
     * 剧集code
     */
    private String seriesCode;
}
