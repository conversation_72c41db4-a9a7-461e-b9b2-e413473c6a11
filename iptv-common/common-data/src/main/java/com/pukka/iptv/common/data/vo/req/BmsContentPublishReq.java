package com.pukka.iptv.common.data.vo.req;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import javax.validation.constraints.Max;
import javax.validation.constraints.Min;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.util.List;


/**
 * @author: wz
 * @date: 2021/8/31 11:43
 * @description: 内容修改vo
 */
@Getter
@Setter
@JsonIgnoreProperties(ignoreUnknown = true)
public class BmsContentPublishReq {
    @NotNull
    @Size(max = 255)
    private List<Long> ids;
    @Min(0)
    @Max(4)
    @ApiModelProperty(value = "是否定时发布 1定时2不定时（立即发布）", dataType = "Integer", name = "doSchedule")
    @NotNull
    private Integer doSchedule;
    @ApiModelProperty(value = "定时发布的时间", dataType = "String", name = "scheduleTime")
    @Size(max = 20)
    private String scheduleTime;

    
    /**
     * 设置优先级
     * 1 普通
     * 2 优先
     * 3 紧急
     */
    private int priority;

    /**
     * 是否单发剧头
     */
    @NotNull
    private Boolean alonePublish;

    /**
     * 是否发布关系
     */
    @NotNull
    private Boolean publishRelation;


}
