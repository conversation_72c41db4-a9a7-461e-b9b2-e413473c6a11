package com.pukka.iptv.common.data.vo.req;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.NoArgsConstructor;
import lombok.Getter;
import lombok.Setter;

import javax.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2021/9/23
 */
@Getter
@Setter
@JsonIgnoreProperties(ignoreUnknown = true)
public class BmsChannelPhysicsPageReq extends Page implements Serializable
{
    /**
     *物理频道名称
     */
    private String name;
    /**
     *物理频道发布状态
     */
    private Integer publishStatus;

    @NotNull
    private Long spId;
    /**
     *状态标志
     * 0:失效 1:生效
     */
    private Integer status;
    /**
     * 物理频道所属频道id
     */
    @NotNull
    private Long channelId;
}
