package com.pukka.iptv.common.data.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.util.Date;
import lombok.Data;
import com.fasterxml.jackson.annotation.JsonFormat;

import java.io.Serializable;

/**
 * TODO 授权通道dto
 *
 * <AUTHOR>
 * @date 2021/9/2 22:23
 */
@Data
@ApiModel("授权通道")
public class OutPassageDto implements Serializable {
    @ApiModelProperty("授权通道id")
    private Long id;
    @ApiModelProperty("授权通道名称")
    private String name;
    @ApiModelProperty("是否选中,true|false")
    private Boolean isSelect;
}
