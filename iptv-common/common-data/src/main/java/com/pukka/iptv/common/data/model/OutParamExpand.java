package com.pukka.iptv.common.data.model;

import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;
import lombok.extern.slf4j.Slf4j;

import java.util.Map;

/**
 * @author: chiron
 * Date: 2022/3/15 10:40 AM
 * Description: 分发接口拓展实体
 */
@Slf4j
@NoArgsConstructor
@Data
@Accessors(chain = true)
public class OutParamExpand {

    /**
     * 频道携带物理频道 Map<String, String> key:bms主键id value:action，ActionEnums枚举类 code字段(REGIST,UPDATE)
     * 图片和发布动作 Map<String, String> key:bms图片变主键id，发布媒资时携带图片的id value:图片action，ActionEnums枚举类 code字段(REGIST,UPDATE)
     * 频道和多节目单组合 Map<String, String> key:bms节目单主键id value:节目单action，ActionEnums枚举类 code字段(REGIST,UPDATE)
     * 剧集和子集组合 Map<String, String> key:bms子集表主键ids value:子集action，ActionEnums枚举类 info字段(REGIST,UPDATE)
     */
    private Map<String, String> spareMap;

    /**
     * 优先级设置 PriorityEnums的value
     */
    private Integer priority;

}