package com.pukka.iptv.common.data.model.sys;

import com.baomidou.mybatisplus.annotation.*;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springframework.util.Assert;

import java.util.Date;

/**
 * @author: tan
 * @date: 2021-9-2 23:02:27
 */

@Data
@EqualsAndHashCode
@JsonIgnoreProperties(value = {"handler"})
@TableName(value = "sys_outpassage_storage_rela", autoResultMap = true)
public class SysOutpassageStorageRela extends Model<SysOutpassageStorageRela> implements java.io.Serializable {

    private static final long serialVersionUID = 1L;
    /**
     * id
     */
    @TableId(type = IdType.AUTO)
    @TableField(value = "id")
    @ApiModelProperty(value = "id", dataType = "Long", name = "id")
    private Long id;
    /**
     * sys_out_passage表id
     */
    @TableField(value = "out_passage_id")
    @ApiModelProperty(value = "sys_out_passage表id", dataType = "Long", name = "outPassageId")
    private Long outPassageId;
    /**
     * sys_storage_directory表id
     */
    @TableField(value = "storage_directory_id")
    @ApiModelProperty(value = "sys_storage_directory表id", dataType = "Long", name = "storageDirectoryId")
    private Long storageDirectoryId;
    /**
     * 限制速度
     */
    @TableField(value = "limit_speed")
    @ApiModelProperty(value = "限制速度", dataType = "Long", name = "limitSpeed")
    private Long limitSpeed;
    /**
     * 目录类型1：源片存储目录2：成片存储目录3：图片存储目录4：文档存储目录5：工单存储目录
     */
    @TableField(value = "type")
    @ApiModelProperty(value = "目录类型1：源片存储目录2：成片存储目录3：图片存储目录4：文档存储目录5：工单存储目录", dataType = "Integer", name = "type")
    private Integer type;
    /**
     * 状态：1正常 -1:删除
     */
    @TableField(value = "status")
    @ApiModelProperty(value = "状态：1正常 -1:删除", dataType = "Integer", name = "status")
    private Integer status;
    /**
     * 创建时间
     */
    @TableField(value = "create_time", fill = FieldFill.INSERT)
    @ApiModelProperty(value = "创建时间", dataType = "String", name = "createTime")
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date createTime;
    /**
     * 更新时间
     */
    @TableField(value = "update_time",fill = FieldFill.UPDATE)
    @ApiModelProperty(value = "更新时间", dataType = "String", name = "updateTime")
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date updateTime;

}
