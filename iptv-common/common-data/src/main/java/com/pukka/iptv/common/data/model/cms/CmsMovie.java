package com.pukka.iptv.common.data.model.cms;

import com.baomidou.mybatisplus.annotation.*;
import com.pukka.iptv.common.data.group.FrameGroup;
import com.pukka.iptv.common.data.group.GetMovieGroup;
import io.swagger.annotations.ApiModelProperty;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.NoArgsConstructor;
import lombok.EqualsAndHashCode;
import java.util.Date;
import lombok.Data;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.springframework.util.Assert;

import javax.validation.constraints.NotNull;

/**
 *
 * @author: lqh
 * @date: 2021-8-27 16:23:33
 */

@Data
@EqualsAndHashCode
@JsonIgnoreProperties(value = { "handler" })
@NoArgsConstructor
@TableName(value = "cms_movie",autoResultMap=true)
public class CmsMovie extends Model<CmsMovie> implements java.io.Serializable{

    private static final long serialVersionUID = 1L;
	/**主键*/
    @TableId(type= IdType.AUTO)
	@TableField(value = "id")
    @ApiModelProperty(value="主键",dataType="Long",name="id")
    private Long id;
	/**全局唯一标识*/
    @TableField(value = "code", fill = FieldFill.INSERT)
    @ApiModelProperty(value="全局唯一标识",dataType="String",name="code")
    private String code;
	/**视频名字*/
	@TableField(value = "name")
    @ApiModelProperty(value="视频名字",dataType="String",name="name")
    private String name;
	/**媒体类型 1:正片 2:预览片 */
	@TableField(value = "type")
    @NotNull(groups = FrameGroup.class)
    @ApiModelProperty(value="媒体类型 1:正片 2:预览片 ",dataType="Integer",name="type")
    private Integer type;
	/**resourceId*/
	@TableField(value = "resource_id")
    @ApiModelProperty(value="resourceId",dataType="Long",name="resourceId")
    private Long resourceId;
	/**resourceCode*/
	@TableField(value = "resource_code")
    @ApiModelProperty(value="resourceCode",dataType="Long",name="resourceCode")
    private String resourceCode;
	/**媒体文件 URL ftp://username:password@ip:port/ ... 标准 FTP 协议*/
	@TableField(value = "file_url")
    @ApiModelProperty(value="媒体文件 URL ftp://username:password@ip:port/ ... 标准 FTP 协议",dataType="String",name="fileUrl")
    private String fileUrl;
	/**播放地址*/
	@TableField(value = "play_url")
    @ApiModelProperty(value="播放地址",dataType="String",name="playUrl")
    private String playUrl;
	/**0: No DRM 1: BES DRM*/
	@TableField(value = "source_drm_type")
    @ApiModelProperty(value="0: No DRM 1: BES DRM",dataType="Integer",name="sourceDrmType")
    private Integer sourceDrmType;
	/**0: No DRM 1: BES DRM*/
	@TableField(value = "dest_drm_type")
    @ApiModelProperty(value="0: No DRM 1: BES DRM",dataType="Integer",name="destDrmType")
    private Integer destDrmType;
	/**0: 其他 1: Monaural 单声道 2: Stereo 多声道 3: Two-nation monaural 双单 声道 4: Two-nation stereo 双多声 道 5: AC3(5:1 channel) AC3 声道*/
	@TableField(value = "audio_type")
    @ApiModelProperty(value="0: 其他 1: Monaural 单声道 2: Stereo 多声道 3: Two-nation monaural 双单 声道 4: Two-nation stereo 双多声 道 5: AC3(5:1 channel) AC3 声道",dataType="Integer",name="audioType")
    private Integer audioType;
	/**视频比例 0: 4x3 1: 16x9(Wide)*/
	@TableField(value = "screen_format")
    @ApiModelProperty(value="视频比例 0: 4x3 1: 16x9(Wide)",dataType="Integer",name="screenFormat")
    private Integer screenFormat;
	/**字幕标志 0:无字幕 1:有字幕*/
	@TableField(value = "closed_captioning")
    @ApiModelProperty(value="字幕标志 0:无字幕 1:有字幕",dataType="Integer",name="closedCaptioning")
    private Integer closedCaptioning;
	/**媒体格式描述符*/
	@TableField(value = "media_spec")
    @ApiModelProperty(value="媒体格式描述符",dataType="String",name="mediaSpec")
    private String mediaSpec;
	/**媒体码率描述符 0：其它 1：400K 2：700K 3：1.3M 4：2M 5：2.5M 6：8M 7：10M 8：12M 9：16M 16:8M(杜比5.1) 51：1.3M（标清VBR） 52：2M（标清VBR） 53：4M（高清VBR） 54： 6M（高清VBR） 55： 2.3M（标清VBR） 56：25M（4K H264 VBR） 57：50M（4K H264 VBR） 58：15M（4K H265 VBR） 59：30M（4K H265 VBR）*/
	@TableField(value = "bit_rate_type")
    @ApiModelProperty(value="媒体码率描述符 0：其它 1：400K 2：700K 3：1.3M 4：2M 5：2.5M 6：8M 7：10M 8：12M 9：16M 16:8M(杜比5.1) 51：1.3M（标清VBR） 52：2M（标清VBR） 53：4M（高清VBR） 54： 6M（高清VBR） 55： 2.3M（标清VBR） 56：25M（4K H264 VBR） 57：50M（4K H264 VBR） 58：15M（4K H265 VBR） 59：30M（4K H265 VBR）",dataType="Integer",name="bitRateType")
    private Integer bitRateType;
	/**点播的片头时长，单位秒*/
	@TableField(value = "movie_head_duration")
    @ApiModelProperty(value="点播的片头时长，单位秒",dataType="Long",name="movieHeadDuration")
    private Long movieHeadDuration;
	/**点播的片尾时长，单位秒*/
	@TableField(value = "movie_tail_duration")
    @ApiModelProperty(value="点播的片尾时长，单位秒",dataType="Long",name="movieTailDuration")
    private Long movieTailDuration;
	/**cpId*/
	@TableField(value = "cp_id")
    @ApiModelProperty(value="cpId",dataType="Long",name="cpId")
    private Long cpId;
	/**CP名称*/
	@TableField(value = "cp_name")
    @ApiModelProperty(value="CP名称",dataType="String",name="cpName")
    private String cpName;
	/**创建时间*/
    @TableField(value = "create_time", fill = FieldFill.INSERT)
    @ApiModelProperty(value="创建时间",dataType="String",name="createTime")
    @JsonFormat(shape = JsonFormat.Shape.STRING,pattern = "yyyy-MM-dd HH:mm:ss",timezone="GMT+8")
    private Date createTime;
	/**更新时间*/
	@TableField(value = "update_time", fill = FieldFill.INSERT)
    @ApiModelProperty(value="更新时间",dataType="String",name="updateTime")
    @JsonFormat(shape = JsonFormat.Shape.STRING,pattern = "yyyy-MM-dd HH:mm:ss",timezone="GMT+8")
    private Date updateTime;
	/**状态 1：有效 0：失效 -1：删除*/
	@TableField(value = "status")
    @ApiModelProperty(value="状态 1：有效 0：失效 255：删除",dataType="Integer",name="status")
    private Integer status;
	/**时长单位秒*/
	@TableField(value = "duration")
    @ApiModelProperty(value="时长单位秒",dataType="Long",name="duration")
    private Long duration;
	/**创建人，只人工上传时可用*/
    @TableField(value = "creator_name", fill = FieldFill.INSERT)
    @ApiModelProperty(value="创建人，只人工上传时可用",dataType="String",name="creatorName")
    private String creatorName;
	/**创建人ID，来自于user表*/
    @TableField(value = "creator_id", fill = FieldFill.INSERT)
    @ApiModelProperty(value="创建人ID，来自于user表",dataType="Long",name="creatorId")
    private Long creatorId;
	/**1） 输出封装格式（Envelope）： a) TS b) F4V c) 3GP d) MP4*/
	@TableField(value = "envelope")
    @ApiModelProperty(value="1） 输出封装格式（Envelope）： a) TS b) F4V c) 3GP d) MP4",dataType="String",name="envelope")
    private String envelope;
//	/**2） 码率控制（Bitrate Type）： a) VBR b) CBR*/
//	@TableField(value = "bitrate_type")
//    @ApiModelProperty(value="2） 码率控制（Bitrate Type）： a) VBR b) CBR",dataType="String",name="bitrateType")
//    private String bitrate;
	/**a) 编码格式（Video Codec）： i. 针对 TS，可选 H264，HEVC ii. 针对 3GP，可选 MPEG4，H264 iii. 针对 F4V，只选 H264 iv. 针对 MP4，只选 H264，HEVC*/
	@TableField(value = "video_codec")
    @ApiModelProperty(value="a) 编码格式（Video Codec）： i. 针对 TS，可选 H264，HEVC ii. 针对 3GP，可选 MPEG4，H264 iii. 针对 F4V，只选 H264 iv. 针对 MP4，只选 H264，HEVC",dataType="String",name="videoCodec")
    private String videoCodec;
	/**码率（Video Bitrate）：1 ~ 100000 （kbits/s）*/
	@TableField(value = "video_bitrate")
    @ApiModelProperty(value="码率（Video Bitrate）：1 ~ 100000 （kbits/s）",dataType="String",name="videoBitrate")
    private String videoBitrate;
	/**c) 分辨率（Resolution）： i. 1080i ii. 720P iii. 576i iv. 3/4D1 v. 2/3D1 vi. CIF（只针对 3GP） vii. QCIF （只针对 3GP） viii. 3840i(4K) ix. 4096i(4K) */
	@TableField(value = "resolution")
    @ApiModelProperty(value="c) 分辨率（Resolution）： i. 1080i ii. 720P iii. 576i iv. 3/4D1 v. 2/3D1 vi. CIF（只针对 3GP） vii. QCIF （只针对 3GP） viii. 3840i(4K) ix. 4096i(4K) ",dataType="String",name="resolution")
    private String resolution;
	/**4. 帧率（Frame rate）*/
	@TableField(value = "frame_rate")
    @ApiModelProperty(value="4. 帧率（Frame rate）",dataType="Float",name="frameRate")
    private Float frameRate;
	/**a) 编码格式（Audio Codec）： i. 针对 TS，可选 MP2，AAC ii. 针对 F4V，可选 AAC Page 20 of 23 iii. 针对 3GP，可选 AAC，AMR iv. 针对 MP4，可选 AAC v. EAC3( 4K)*/
	@TableField(value = "audio_codec")
    @ApiModelProperty(value="a) 编码格式（Audio Codec）： i. 针对 TS，可选 MP2，AAC ii. 针对 F4V，可选 AAC Page 20 of 23 iii. 针对 3GP，可选 AAC，AMR iv. 针对 MP4，可选 AAC v. EAC3( 4K)",dataType="String",name="audioCodec")
    private String audioCodec;
	/**b) 音频码率(Audio bitrate)：1 ~ 256 （kbits/s）*/
	@TableField(value = "audio_bitrate")
    @ApiModelProperty(value="b) 码率(Audio bitrate)：1 ~ 256 （kbits/s）",dataType="String",name="audioBitrate")
    private String audioBitrate;
    /**内容ID*/
    @TableField(value = "content_id")
    @NotNull(groups = FrameGroup.class)
    @NotNull(groups = GetMovieGroup.class)
    @ApiModelProperty(value="内容ID",dataType="Long",name="contentId")
    private Long contentId;
	/**内容类型\ 1：电影  2：子集 3：电视剧 4：系列片 5：片花*/
	@TableField(value = "content_type")
    @NotNull(groups = FrameGroup.class)
    @NotNull(groups = GetMovieGroup.class)
    @ApiModelProperty(value="内容类型 1：电影  2：子集 3：电视剧 4：系列片 5：片花",dataType="Integer",name="contentType")
    private Integer contentType;
	/**节目关联状态 1：待关联 2：已关联*/
	@TableField(value = "content_status")
    @ApiModelProperty(value="节目关联状态 1：待关联 2：已关联",dataType="Integer",name="contentStatus")
    private Integer contentStatus;
    /**当 content_type = 1,2时为cms_program表code 当content_type = 3,4 时为cms_series表code*/
    @TableField(value = "content_code")
    @ApiModelProperty(value="当 content_type = 1,2时为cms_program表code 当content_type = 3,4 时为cms_series表code",dataType="String",name="contentCode")
    private String contentCode;
    /**文件大小*/
    @TableField(value = "file_size")
    @ApiModelProperty(value="文件大小",dataType="Integer",name="fileSize")
    private Long fileSize;
    /**所属存储*/
    @TableField(value = "storage_name")
    @ApiModelProperty(value="所属存储",dataType="String",name="storageName")
    private String storageName;
    /**存储ID*/
    @TableField(value = "storage_id")
    @ApiModelProperty(value="存储ID",dataType="Long",name="storageId")
    private Long storageId;

    /** 本地读取到的视频媒体信息格式为 bitRate|fileSize|duration(既有可能是秒，也有可能是时间戳 10:29:30)|width|height|frameRate|ratio(比例3：2，16：9等) */
    @TableField(value = "mediainfo")
    @ApiModelProperty(value="本地读取到的视频媒体信息格式为 bitRate|fileSize|duration(时间戳 10:29:30)|width|height|frameRate|ratio(比例3：2，16：9等)",dataType="String",name="mediainfo")
    private String mediainfo;

    @TableField(value = "width")
    @ApiModelProperty(value="width",dataType="Integer",name="width")
    private Integer width;
    @TableField(value = "height")
    @ApiModelProperty(value="height",dataType="Integer",name="height")
    private Integer height;
    /** 视频ID */
    @TableField(value = "vid")
    @ApiModelProperty(value="vid",dataType="String",name="vid")
    private String vid;
    /** 文件ID*/
    @TableField(value = "fileid")
    @ApiModelProperty(value="fileid",dataType="String",name="fileid")
    private String fileid;

    public void valid() {
        Assert.notNull(contentId, "内容ID不能为null");
        Assert.notNull(contentType, "内容类型不能为null");
        Assert.notNull(type, "视频类型不能为null");
    }
}
