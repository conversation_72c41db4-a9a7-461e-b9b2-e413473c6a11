package com.pukka.iptv.common.data.model;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotEmpty;
import java.util.List;

/**
 * @Author: chiron
 * @Date: 2023/03/06/11:28
 * @Description:
 */
@Data
@EqualsAndHashCode
@JsonIgnoreProperties(value = { "handler" })
@NoArgsConstructor
@Accessors(chain = true)
public class EpgIssueOrder implements java.io.Serializable{

    private static final long serialVersionUID = 3324888012527495537L;

    @NotEmpty(message = "内容主键为必须参数")
    List<Long> idList;

    /**
     * 优先级
     */
    Integer priority;
}
