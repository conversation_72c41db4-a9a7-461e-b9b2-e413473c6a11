package com.pukka.iptv.common.data.model.copyright;

import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import com.baomidou.mybatisplus.annotation.*;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.pukka.iptv.common.data.converter.cms.ArtistProhibitConverter;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.Date;

/**
 * 违禁艺人表
 *
 * <AUTHOR>
 * @email
 * @date 2022-07-12 18:01:29
 */
@Data
@NoArgsConstructor
@EqualsAndHashCode
@Accessors(chain = true)
@JsonIgnoreProperties(value = {"handler"})
@TableName(value = "artist_prohibit", autoResultMap = true)
public class ArtistProhibit extends Model<ArtistProhibit> implements Serializable {

    private static final long serialVersionUID = 3249622966192090168L;
    /**
     * 主键
     */
    @ExcelIgnore
    @TableId(type = IdType.AUTO)
    @TableField(value = "id")
    @ApiModelProperty(value = "ObjectID", dataType = "Long", name = "id")
    private Long id;
    /**
     * 全局唯一标识
     */
    @ExcelIgnore
    @TableField(value = "code")
    @ApiModelProperty(value = "全局唯一标识", dataType = "String", name = "code")
    private String code;
    /**
     * 艺人名称
     */
    @ExcelProperty("艺人姓名")
    @TableField(value = "artist_name")
    @ApiModelProperty(value = "艺人名称", dataType = "String", name = "artistName")
    private String artistName;
    /**
     * 艺人性别
     */
    @ExcelProperty(value = "艺人性别",converter = ArtistProhibitConverter.class)
    @TableField(value = "artist_sex",updateStrategy=FieldStrategy.IGNORED)
    @ApiModelProperty(value = "艺人性别:1=男，2=女", dataType = "String", name = "artistSex")
    private Integer artistSex;
    @ExcelIgnore
    @TableField(exist = false)
    private String sex;
    /**
     * 出生日期（YYYYMMDDHH24MiSS）
     */
    @ExcelProperty("出生年月")
    @TableField(value = "birthday",updateStrategy=FieldStrategy.IGNORED)
    @ApiModelProperty(value = "出生日期", dataType = "String", name = "birthday")
    private String birthday;
    /**
     * 简介
     */
    @ExcelProperty("简介")
    @TableField(value = "description",updateStrategy=FieldStrategy.IGNORED)
    @ApiModelProperty(value = "简介", dataType = "String", name = "description")
    private String description;
    /**
     * 封杀原因
     */
    @ExcelProperty("封杀原因")
    @TableField(value = "prohibit_cause",updateStrategy=FieldStrategy.IGNORED)
    @ApiModelProperty(value = "封杀原因", dataType = "String", name = "prohibitCause")
    private String prohibitCause;
    /**
     * 创建人
     */
    @ExcelProperty("添加人员")
    @TableField(value = "creator_name")
    @ApiModelProperty(value = "创建人", dataType = "String", name = "creatorName")
    private String creatorName;
    /**
     * 创建时间
     */
    @ExcelProperty("添加时间")
    @ColumnWidth(20)
    @TableField(value = "create_time")
    @ApiModelProperty(value = "创建时间", dataType = "String", name = "createTime")
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date createTime;
    /**
     * 更新时间
     */
    @ExcelIgnore
    @TableField(value = "update_time",fill = FieldFill.INSERT_UPDATE)
    @ApiModelProperty(value = "更新时间", dataType = "String", name = "updateTime")
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date updateTime;

}
