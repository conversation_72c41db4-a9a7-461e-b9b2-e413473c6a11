package com.pukka.iptv.common.data.vo.smp;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

@Data
@ApiModel("SmpHistoricalDataVo")
public class SmpHistoricalDataVo implements Serializable {
    private Long current;
    private Long size;
    @ApiModelProperty(value="媒资名称",dataType="String[]",name="name")
    private String name;
    /**
     * 当包含换行符时，是多名称查询
     */
    private String[] nameList;
    @ApiModelProperty(value="cpId",dataType="Integer",name="cpId")
    private Integer cpId;
    @ApiModelProperty(value="媒资类型，单集0/子集1/剧集2",dataType="String",name="type")
    private String type;
    @ApiModelProperty(value="开始时间",dataType="String",name="startDate")
    private String startDate;
    @ApiModelProperty(value="结束时间",dataType="String",name="endDate")
    private String endDate;
    /**
     * 模糊查询
     */
    private String nameLike;

    /**
     * 模糊精确查询标志
     */
    private Integer likeOrinFlag;
    /**
     * 排序开关
     */
    @ApiModelProperty(value="排序字段，0/开启排序 1/关闭排序",dataType="String",name="sortKey")
    private String sortKey;
    /**
     * 排序规则：desc，asc
     */
    @ApiModelProperty(value="排序规则，desc/降序 asc/升序",dataType="String",name="sortValue")
    private String sortValue;
}
