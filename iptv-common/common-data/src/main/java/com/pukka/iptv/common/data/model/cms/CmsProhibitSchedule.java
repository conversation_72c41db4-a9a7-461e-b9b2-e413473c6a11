package com.pukka.iptv.common.data.model.cms;

import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelProperty;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.pukka.iptv.common.data.converter.bms.DateConverter;
import com.pukka.iptv.common.data.converter.bms.LicensingConverter;
import com.pukka.iptv.common.data.converter.bms.SourceConverter;
import com.pukka.iptv.common.data.converter.bms.TimeConverter;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 违禁节目单
 * @TableName cms_prohibit_schedule
 */
@TableName(value ="cms_prohibit_schedule")
@Data
public class CmsProhibitSchedule implements Serializable {
    /**
     * 主键
     */
    @TableId
    @ExcelIgnore
    private Long id;

    /**
     * 唯一标识
     */
    @ExcelProperty(value = "唯一标识",index = 1)
    private String code;

    /**
     * 节目单违禁规则表唯一标识
     */
    @ExcelIgnore
    @TableField(value = "rule_prohibit_code")
    private String ruleProhibitCode;

    /**
     * 频道Code
     */
    @ExcelIgnore
    @TableField(value = "channel_code")
    private String channelCode;

    /**
     * 频道名称
     */
    @ExcelProperty(value = "所属频道",index = 2)
    @TableField(value = "channel_name")
    private String channelName;

    /**
     * 逻辑频道 ID
     */
    @ExcelIgnore
    @TableField(value = "channel_id")
    private Long channelId;

    /**
     * 节目名称
     */
    @ExcelProperty(value = "节目名称",index =0)
    @TableField(value = "program_name")
    private String programName;

    /**
     * 节目开播日期
     */
    @ExcelProperty(value = "开播日期",index = 3,converter = DateConverter.class)
    @TableField(value = "start_date")
    private String startDate;

    /**
     * 节目开播时间
     */
    @ExcelProperty(value = "开播时间",index = 4,converter = TimeConverter.class)
    @TableField(value = "start_time")
    private String startTime;

    /**
     * 节目结束时间
     */
    @ExcelProperty(value = "结束时间",index = 5,converter = TimeConverter.class)
    @TableField(value = "end_time")
    private String endTime;

    /**
     * 节目时长(HH24MISS)
     */
    @ExcelProperty(value = "节目时长",index = 6,converter = TimeConverter.class)
    private String duration;

    /**
     * TVOD 保存时长(小时)缺省为空
     */
    @ExcelProperty(value = "保存时长(小时)",index = 7)
    @TableField(value = "storage_duration")
    private Integer storageDuration;

    /**
     * 状态标志1:生效 2:失效
     */
    @ExcelIgnore
    private Integer status;

    /**
     * 描述信息
     */
    @ExcelProperty(value = "描述",index = 8)
    private String description;

    /**
     * 来源 1：专线注入 2：人工创建
     */
    @ExcelProperty(value = "来源",index = 9,converter = SourceConverter.class)
    private Integer source;

    /**
     * 节目的分类标签，如“体育”，多个标签用空格或“;”区分
     */
    @ExcelIgnore
    private String genre;

    /**
     *
     */
    @TableField(value = "cp_id")
    @ExcelIgnore
    private Long cpId;

    /**
     * CP名称
     */
    @ExcelProperty(value = "CP名称",index = 10)
    @TableField(value = "cp_name")
    private String cpName;

    /**
     * 创建时间
     */
    @ExcelProperty(value = "创建时间",index = 12)
    @JsonFormat(shape = JsonFormat.Shape.STRING,pattern = "yyyy-MM-dd HH:mm:ss",timezone="GMT+8")
    @TableField(value = "create_time")
    private Date createTime;

    /**
     * 更新时间
     */
    @ExcelProperty(value = "更新时间",index = 13)
    @JsonFormat(shape = JsonFormat.Shape.STRING,pattern = "yyyy-MM-dd HH:mm:ss",timezone="GMT+8")
    @TableField(value = "update_time")
    private Date updateTime;

    /**
     * 创建人，只人工上传时可用
     */
    @ExcelProperty(value = "创建人",index = 11)
    @TableField(value = "creator_name")
    private String creatorName;

    /**
     * 创建人ID来自于sys_user
     */
    @ExcelIgnore
    @TableField(value = "creator_id")
    private Long creatorId;
}