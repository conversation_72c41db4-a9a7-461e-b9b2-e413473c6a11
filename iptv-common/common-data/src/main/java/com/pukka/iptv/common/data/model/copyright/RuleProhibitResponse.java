package com.pukka.iptv.common.data.model.copyright;

import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

/**
 * @Author: chiron
 * @Date: 2022/08/04/10:10
 * @Description: 违禁规则返回信息
 */
@Data
@NoArgsConstructor
@Accessors(chain = true)
public class RuleProhibitResponse {
    /**
     * 违禁检测结果
     * true:是违禁片；false：非违禁片
     */
    private Boolean result;
    /**
     * 0:"否",1:"是",2:"待检测",3:"跳过检测"
     */
    private Integer isProhibit;
    /**
     * 违禁片是否需入库
     */
    private Boolean isEntry;
    /**
     * 违禁占比
     */
    private Integer ratio;
    /**
     * 违禁规则匹配名称
     */
    private String ruleName;
    /**
     * 违禁规则code
     */
    private String ruleCode;
    /**
     * 违禁媒资code
     */
    private String contentCode;
    /**
     * 违禁媒资类型
     */
    private String contentType;
}
