package com.pukka.iptv.common.data.model;

import com.baomidou.mybatisplus.annotation.*;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import java.util.Date;

/**
 * <AUTHOR>
 * @Date 2021/12/24 9:38 上午
 * @description:
 * @Version 1.0
 */
@Data
@EqualsAndHashCode
@JsonIgnoreProperties(value = { "handler" })
@NoArgsConstructor
@TableName(value = "out_transmission_result",autoResultMap=true)
public class OutTransmissionResult extends Model<OutTransmissionResult> implements java.io.Serializable{
    private static final long serialVersionUID = 29078821745076834L;
    /**id*/
    @TableId(type = IdType.AUTO)
    @TableField(value = "id")
    @ApiModelProperty(value="id",dataType="String",name="id")
    private Long id;
    /**关联ID，主任务ID*/
    @TableField(value = "correlate_id")
    @ApiModelProperty(value="关联ID，主任务ID",dataType="String",name="correlateId")
    private String correlateId;
    /**cspId*/
    @TableField(value = "csp_id")
    @ApiModelProperty(value="cspId",dataType="String",name="cspId")
    private String cspId;
    /**lspId*/
    @TableField(value = "lsp_id")
    @ApiModelProperty(value="lspId",dataType="String",name="lspId")
    private String lspId;
    /**cmdFileUrl*/
    @TableField(value = "cmd_file_url")
    @ApiModelProperty(value="cmdFileUrl",dataType="String",name="cmdFileUrl")
    private String cmdFileUrl;
    /**
     * 下游回调反馈地址
     */
    @TableField(value = "result_file_url")
    @ApiModelProperty(value="反馈地址",dataType="String",name="resultFileUrl")
    private String resultFileUrl;
    /**path*/
    @TableField(value = "path")
    @ApiModelProperty(value="下游地址",dataType="String",name="path")
    private String path;
    /**outPassageId*/
    @TableField(value = "outPassage_id")
    @ApiModelProperty(value="分发通道id",dataType="String",name="outPassageId")
    private String outPassageId;
    /**outPassageName*/
    @TableField(value = "outPassage_name")
    @ApiModelProperty(value="分发通道名",dataType="String",name="outPassageName")
    private String outPassageName;
    /**下游结果{0:成功,-1:失败}*/
    @TableField(value = "result")
    @ApiModelProperty(value="接口结果{0:成功,-1:失败}",dataType="Integer",name="result")
    private Integer result;
    /**错误描述*/
    @TableField(value = "error_description")
    @ApiModelProperty(value="错误描述",dataType="String",name="errorDescription")
    private String errorDescription;
    /**状态 0:成功,-1:失败*/
    @TableField(value = "status")
    @ApiModelProperty(value="调用下游下发接口结果{0:成功,-1:失败}",dataType="Integer",name="status")
    private Integer status;
    /**状态描述*/
    @TableField(value = "status_description")
    @ApiModelProperty(value="状态描述",dataType="String",name="statusDescription")
    private String statusDescription;
    /**创建时间*/
    @TableField(value = "create_time", fill = FieldFill.INSERT)
    @ApiModelProperty(value="创建时间",dataType="String",name="createTime")
    @JsonFormat(shape = JsonFormat.Shape.STRING,pattern = "yyyy-MM-dd HH:mm:ss",timezone="GMT+8")
    private Date createTime;
    /**更新时间*/
    @TableField(value = "update_time", fill = FieldFill.UPDATE)
    @ApiModelProperty(value="更新时间",dataType="String",name="updateTime")
    @JsonFormat(shape = JsonFormat.Shape.STRING,pattern = "yyyy-MM-dd HH:mm:ss",timezone="GMT+8")
    private Date updateTime;
}
