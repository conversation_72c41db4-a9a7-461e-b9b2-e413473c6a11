package com.pukka.iptv.common.data.model.sys;

import lombok.Data;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Configuration;
import org.springframework.stereotype.Component;

/*********
 * 旧组件迁移，文件名不可改动，切勿更改为驼峰
 */
@Data
public class BlobFile {

	private String account;

	private String password;
	
	private String cpname;
	
	private String blobsourceurl;
	
	private String cpid;

	private String cpcode;


	private String blobuploadurl;

	private String resourceinfourl;

	private String DiskSpaceUrl;
	
}
