package com.pukka.iptv.common.data.vo.bms;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.pukka.iptv.common.data.model.bms.BmsProgram;
import io.swagger.annotations.ApiModel;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * @Author: chiron
 * @Date: 2023/02/24/00:11
 * @Description:
 */
@Data
@JsonIgnoreProperties(value = { "handler" },ignoreUnknown = true)
@ApiModel("BmsProgramVO")
@Accessors(chain = true)
public class BmsProgramVO extends BmsProgram implements java.io.Serializable{
    /**
     * bms表所属剧头id
     */
    private Long pid;
}
