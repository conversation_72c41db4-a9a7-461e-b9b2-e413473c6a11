package com.pukka.iptv.common.data.model.in;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.NoArgsConstructor;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Date;

/**
 *
 * @author: tan
 * @date: 2021-8-24 10:21:29
 */

@Data
@EqualsAndHashCode
@JsonIgnoreProperties(value = { "handler" })
@NoArgsConstructor
@TableName(value = "in_movie",autoResultMap=true)
public class InMovie extends Model<InMovie> implements java.io.Serializable{

    private static final long serialVersionUID = 1L;
	/**主键*/
	@TableField(value = "id")
    @TableId(type = IdType.AUTO)
    @ApiModelProperty(value="主键",dataType="Long",name="id")
    private Long id;
	/**全局唯一标识*/
	@TableField(value = "code")
    @ApiModelProperty(value="全局唯一标识",dataType="String",name="code")
    private String code;
	/**视频名字*/
	@TableField(value = "name")
    @ApiModelProperty(value="视频名字",dataType="String",name="name")
    private String name;
	/**媒体类型 1:正片 2:预览片 */
	@TableField(value = "type")
    @ApiModelProperty(value="媒体类型 1:正片 2:预览片 ",dataType="Integer",name="type")
    private Integer type;
	/**媒体文件 URL ftp://username:password@ip:port/ ... 标准 FTP 协议*/
	@TableField(value = "file_url")
    @ApiModelProperty(value="媒体文件 URL ftp://username:password@ip:port/ ... 标准 FTP 协议",dataType="String",name="fileUrl")
    private String fileUrl;
	/**播放地址*/
	@TableField(value = "play_url")
    @ApiModelProperty(value="播放地址",dataType="String",name="playUrl")
    private String playUrl;
	/**0: No DRM 1: BES DRM*/
	@TableField(value = "source_drm_type")
    @ApiModelProperty(value="0: No DRM 1: BES DRM",dataType="Integer",name="sourceDrmType")
    private Integer sourceDrmType;
	/**0: No DRM 1: BES DRM*/
	@TableField(value = "dest_drm_type")
    @ApiModelProperty(value="0: No DRM 1: BES DRM",dataType="Integer",name="destDrmType")
    private Integer destDrmType;
	/**0: 其他 1: Monaural 单声道 2: Stereo 多声道 3: Two-nation monaural 双单 声道 4: Two-nation stereo 双多声 道 5: AC3(5:1 channel) AC3 声道*/
	@TableField(value = "audio_type")
    @ApiModelProperty(value="0: 其他 1: Monaural 单声道 2: Stereo 多声道 3: Two-nation monaural 双单 声道 4: Two-nation stereo 双多声 道 5: AC3(5:1 channel) AC3 声道",dataType="Integer",name="audioType")
    private Integer audioType;
	/**0: 4x3 1: 16x9(Wide)*/
	@TableField(value = "screen_format")
    @ApiModelProperty(value="0: 4x3 1: 16x9(Wide)",dataType="Integer",name="screenFormat")
    private Integer screenFormat;
	/**字幕标志 0:无字幕 1:有字幕*/
	@TableField(value = "closed_caption")
    @ApiModelProperty(value="字幕标志 0:无字幕 1:有字幕",dataType="Integer",name="closedCaption")
    private Integer closedCaption;
	/**媒体格式描述符*/
	@TableField(value = "media_spec")
    @ApiModelProperty(value="媒体格式描述符",dataType="String",name="mediaSpec")
    private String mediaSpec;
	/**媒体码率描述符 0：其它 1：400K 2：700K 3：1.3M 4：2M 5：2.5M 6：8M 7：10M 8：12M 9：16M 16:8M(杜比5.1) 51：1.3M（标清VBR） 52：2M（标清VBR） 53：4M（高清VBR） 54： 6M（高清VBR） 55： 2.3M（标清VBR） 56：25M（4K H264 VBR） 57：50M（4K H264 VBR） 58：15M（4K H265 VBR） 59：30M（4K H265 VBR）*/
	@TableField(value = "bir_rate_type")
    @ApiModelProperty(value="媒体码率描述符 0：其它 1：400K 2：700K 3：1.3M 4：2M 5：2.5M 6：8M 7：10M 8：12M 9：16M 16:8M(杜比5.1) 51：1.3M（标清VBR） 52：2M（标清VBR） 53：4M（高清VBR） 54： 6M（高清VBR） 55： 2.3M（标清VBR） 56：25M（4K H264 VBR） 57：50M（4K H264 VBR） 58：15M（4K H265 VBR） 59：30M（4K H265 VBR）",dataType="Integer",name="birRateType")
    private Integer birRateType;
	/**点播的片头时长，单位秒*/
	@TableField(value = "movie_head_duration")
    @ApiModelProperty(value="点播的片头时长，单位秒",dataType="Long",name="movieHeadDuration")
    private Long movieHeadDuration;
	/**点播的片尾时长，单位秒*/
	@TableField(value = "movie_tail_duration")
    @ApiModelProperty(value="点播的片尾时长，单位秒",dataType="Long",name="movieTailDuration")
    private Long movieTailDuration;
	/**应答文件包含，0: 成功 其他: 错误代码*/
	@TableField(value = "result")
    @ApiModelProperty(value="应答文件包含，0: 成功 其他: 错误代码",dataType="Integer",name="result")
    private Integer result;
	/**应答文件包含，错误描述*/
	@TableField(value = "error_description")
    @ApiModelProperty(value="应答文件包含，错误描述",dataType="String",name="errorDescription")
    private String errorDescription;
	/**创建时间*/
	@TableField(value = "create_time")
    @ApiModelProperty(value="创建时间",dataType="String",name="createTime")
    @JsonFormat(shape = JsonFormat.Shape.STRING,pattern = "yyyy-MM-dd HH:mm:ss",timezone="GMT+8")
    private Date createTime;
	/**更新时间*/
	@TableField(value = "update_time")
    @ApiModelProperty(value="更新时间",dataType="String",name="updateTime")
    @JsonFormat(shape = JsonFormat.Shape.STRING,pattern = "yyyy-MM-dd HH:mm:ss",timezone="GMT+8")
    private Date updateTime;
	/**状态 1可用 2失效*/
	@TableField(value = "status")
    @ApiModelProperty(value="状态 1可用 2失效",dataType="Integer",name="status")
    private Integer status;
	/**时长单位秒*/
	@TableField(value = "duration")
    @ApiModelProperty(value="时长单位秒",dataType="Long",name="duration")
    private Long duration;
	/**动作 1：REGIST，2：UPDATE，3：DELETE*/
	@TableField(value = "action")
    @ApiModelProperty(value="动作 1：REGIST，2：UPDATE，3：DELETE",dataType="Integer",name="action")
    private Integer action;
	/**correlateId*/
	@TableField(value = "correlate_id")
    @ApiModelProperty(value="correlateId",dataType="String",name="correlateId")
    private String correlateId;
    /**视频id，规范字段，仅注入使用*/
    @TableField(value = "vid")
    @ApiModelProperty(value="视频id，规范字段，仅注入使用",dataType="String",name="vid")
    private String vid;
    /**文件id，央视规范字段，仅仅注入使用*/
    @TableField(value = "fileid")
    @ApiModelProperty(value="文件id，央视规范字段，仅仅注入使用",dataType="String",name="fileid")
    private String fileid;

}
