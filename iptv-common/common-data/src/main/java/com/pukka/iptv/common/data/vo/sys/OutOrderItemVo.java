package com.pukka.iptv.common.data.vo.sys;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.*;
import com.pukka.iptv.common.data.model.OutOrderItem;
import io.swagger.annotations.ApiModel;
import lombok.NoArgsConstructor;

import java.util.Map;

/**
 *
 * @author: tan
 * @date: 2021-9-1 16:08:21
 */

@Data
@EqualsAndHashCode
@NoArgsConstructor
@JsonIgnoreProperties(value = { "handler" })
@ApiModel("OutOrderItemVo")
public class OutOrderItemVo extends OutOrderItem implements java.io.Serializable{

    /**
     * 发布内容带图片下发
     * 1：是
     * 2：否
     */
    private Integer publishWithPicture;
    /**
     * 发布内容带图片下发组装结果
     */
    private Boolean publishWithPictureFlag;

    /**
     * 当包含换行符时，是多名称查询
     */
    private String[] nameList;

    /**
     * 模糊查询
     */
    private String nameLike;

    /**
     * 模糊精确查询标志
     */
    private Integer likeOrinFlag;

    /**
     * 媒资表cp_id
     */
    private Long cpId;

    /**
     * 媒资表sp_id
     */
    private Long spId;
    /**
     * 发布内容带视频下发
     * 1：是
     * 2：否
     */
    private Integer publishWithMovie;

    /**
     * 发布内容带视频下发组装结果
     */
    private Boolean publishWithMovieFlag;

    /**
     * bms图片变主键ids，发布媒资时携带图片的id集合
     */
    private Map<String, String> picIds;

    /**
     * 下发地址
     */
    private String path;

    /**
     * 下游回调反馈地址
     */
    private String resultFileUrl;

    /**
     * ftp文件内容
     */
    private String outXmlContent;

    /**
     * 图片参数
     */
    private String parameters;

    /**
     * 兼容UT extraCode
     */
    private String extraCode;

}
