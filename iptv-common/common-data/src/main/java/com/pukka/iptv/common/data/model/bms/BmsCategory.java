package com.pukka.iptv.common.data.model.bms;

import com.alibaba.fastjson.annotation.JSONField;
import com.baomidou.mybatisplus.annotation.*;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.Date;

/**
 * 栏目
 *
 * <AUTHOR>
 * @date 2021-08-26 16:38:55
 */
@Data
@Accessors(chain = true)
@JsonIgnoreProperties(value = {"handler"}, ignoreUnknown = true)
@TableName(value = "bms_category", autoResultMap = true)
public class BmsCategory implements Serializable {

    private static final long serialVersionUID = 1L;
    /**
     * 主键
     */
    @TableId(type = IdType.AUTO)
    @ApiModelProperty(value = "主键", dataType = "Long", name = "id")
    private Long id;

    /**
     * 全局唯一标识
     */
    @TableField(value = "code")
    @ApiModelProperty(value = "全局唯一标识", dataType = "String", name = "code")
    private String code;

    /**
     * 分类名称
     */
    @TableField(value = "name")
    @ApiModelProperty(value = "分类名称", dataType = "String", name = "name")
    private String name;

    /**
     * 父节点code
     */
    @TableField(value = "parent_code")
    @ApiModelProperty(value = "父节点code", dataType = "String", name = "parentCode")
    private String parentCode;

    /**
     * 父节点ID
     */
    @TableField(value = "parent_id")
    @ApiModelProperty(value = "父节点ID", dataType = "Long", name = "parentId")
    private Long parentId;

    /**
     * 显示顺序号
     */
    @TableField(value = "sequence")
    @ApiModelProperty(value = "显示顺序号", dataType = "Long", name = "sequence")
    private Integer sequence;

    /**
     * 状态标志 0:失效 1:生效
     */
    @TableField(value = "status")
    @ApiModelProperty(value = "状态标志 0:失效 1:生效", dataType = "Integer", name = "status")
    private Integer status;

    /**
     * 描述信息
     */
    @TableField(value = "description")
    @ApiModelProperty(value = "描述信息", dataType = "String", name = "description")
    private String description;

    /**
     * 创建时间
     */
    @TableField(value = "create_time", fill = FieldFill.INSERT)
    @ApiModelProperty(value = "创建时间", dataType = "String", name = "createTime")
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
	@JSONField
    private Date createTime;

    /**
     * 更新时间
     */
    @TableField(value = "update_time", fill = FieldFill.UPDATE)
    @ApiModelProperty(value = "更新时间", dataType = "String", name = "updateTime")
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date updateTime;

    /**
     * spId
     */
    @TableField(value = "sp_id")
    @ApiModelProperty(value = "spId", dataType = "Long", name = "spId")
    private Long spId;

    /**
     * SP名称
     */
    @TableField(value = "sp_name")
    @ApiModelProperty(value = "SP名称", dataType = "String", name = "spName")
    private String spName;
    /**
     * 所属渠道id
     */
    @TableField(value = "bms_sp_channel_id")
    @ApiModelProperty(value = "渠道id，来自sys_dictionary_itemsl表", dataType = "Long", name = "bmsSpChannelId")
    private Long bmsSpChannelId;
    /**
     * 所属渠道
     */
    @TableField(value = "bms_sp_channel_name")
    @ApiModelProperty(value = "所属渠道", dataType = "String", name = "bmsSpChannelName")
    private String bmsSpChannelName;
    /**
     * 发布通道ID，多个ID以英文逗号隔开
     */
    @TableField(value = "out_passage_ids")
    @ApiModelProperty(value = "发布通道ID，多个ID以英文逗号隔开", dataType = "String", name = "outPassageIds")
    private String outPassageIds;

    /**
     * 分发通道名称以英文逗号 隔开
     */
    @TableField(value = "out_passage_names")
    @ApiModelProperty(value = "分发通道名称以英文逗号 隔开", dataType = "String", name = "outPassageNames")
    private String outPassageNames;

    /**
     * 发布状态  1：待发布 2：发布中 3：发布成功 4：发布失败 5：待更新 6：更新中 7：更新失败 8：回收中 9：回收成功 10：回收失败
     */
    @TableField(value = "publish_status")
    @ApiModelProperty(value = "发布状态  1：待发布 2：发布中 3：发布成功 4：发布失败 5：待更新 6：更新中 7：更新失败 8：回收中 9：回收成功 10：回收失败", dataType = "Integer", name = "publishStatus")
    private Integer publishStatus;

    /**
     * 发布时间
     */
    @TableField(value = "publish_time")
    @ApiModelProperty(value = "发布时间", dataType = "String", name = "publishTime")
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date publishTime;

    /**
     * 发布描述
     */
    @TableField(value = "publish_description")
    @ApiModelProperty(value = "发布描述", dataType = "String", name = "publishDescription")
    private String publishDescription;

    /**
     * 创建人
     */
    @TableField(value = "creator_name", fill = FieldFill.INSERT)
    @ApiModelProperty(value = "创建人", dataType = "String", name = "creatorName")
    private String creatorName;

    /**
     * creatorId
     */
    @TableField(value = "creator_id", fill = FieldFill.INSERT)
    @ApiModelProperty(value = "creatorId", dataType = "Long", name = "creatorId")
    private Long creatorId;

    /**
     * 来源  1：专线注入 2：人工绑定
     */
    @TableField(value = "source")
    @ApiModelProperty(value = "来源  1：专线注入 2：人工绑定", dataType = "Integer", name = "source")
    private Integer source;

    /**
     * 栏目类型 1:普通栏目 2：公共栏目
     */
    @TableField(value = "type")
    @ApiModelProperty(value = "栏目类型 1:普通栏目 2：公共栏目", dataType = "Integer", name = "type")
    private Integer type;

    /**
     * 锁定状态   1：未锁定 2：锁定
     */
    @TableField(value = "lock_status")
    @ApiModelProperty(value = "锁定状态   1：未锁定 2：锁定", dataType = "Integer", name = "lockStatus")
    private Integer lockStatus;

    /**
     * cpId
     */
    @TableField(value = "cp_id")
    @ApiModelProperty(value = "cpId", dataType = "Long", name = "cpId")
    private Long cpId;

    /**
     * CP名称
     */
    @TableField(value = "cp_name")
    @ApiModelProperty(value = "CP名称", dataType = "String", name = "cpName")
    private String cpName;

    /**
     * 是否是叶子节点
     */
    @TableField(value = "leaf_node")
    @ApiModelProperty(value = "0:非叶子节点  1:叶子节点", dataType = "String", name = "cpName")
    private Integer leafNode;

    /**
     * 扩展code，是运营商使用的code
     */
    @TableField(value = "extra_code")
    @ApiModelProperty(value = "扩展code，cp提供的code与运营商使用的code不一致，当发给运营商时，使用extraCode，发给UT使用code", dataType = "String", name = "extraCode")
    private String extraCode;
}
