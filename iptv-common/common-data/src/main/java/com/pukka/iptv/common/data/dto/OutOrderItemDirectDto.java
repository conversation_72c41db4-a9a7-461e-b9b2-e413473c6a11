package com.pukka.iptv.common.data.dto;

import com.pukka.iptv.common.data.vo.sys.OutOrderItemVo;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @Description
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class OutOrderItemDirectDto extends OutOrderItemVo implements Serializable {
    /**
     * 下游回调反馈地址
     */

    private String webserviceUrl;

}
