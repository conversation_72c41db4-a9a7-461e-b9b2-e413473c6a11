package com.pukka.iptv.common.data.model.in;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.NoArgsConstructor;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Date;

/**
 *
 * @author: tan
 * @date: 2021-9-7 14:47:42
 */

@Data
@EqualsAndHashCode
@JsonIgnoreProperties(value = { "handler" })
@NoArgsConstructor
@TableName(value = "in_order_mappings",autoResultMap=true)
public class InOrderMappings extends Model<InOrderMappings> implements java.io.Serializable{

    private static final long serialVersionUID = 1L;
	/**注入工单关系*/
	@TableField(value = "object_id")
    @ApiModelProperty(value="注入工单关系",dataType="String",name="objectId")
    private String objectId;
	/**inorder表主键id*/
	@TableField(value = "in_order_id")
    @ApiModelProperty(value="inorder表主键id",dataType="String",name="inOrderId")
    private String inOrderId;
	/**注入工单*/
	@TableField(value = "correlate_id")
    @ApiModelProperty(value="注入工单",dataType="String",name="correlateId")
    private String correlateId;
	/**关联表名*/
	@TableField(value = "table_name")
    @ApiModelProperty(value="关联表名",dataType="String",name="tableName")
    private String tableName;
	/**行id*/
	@TableField(value = "table_row_id")
    @ApiModelProperty(value="行id",dataType="Long",name="tableRowId")
    private Long tableRowId;
	/**类型（series, program, movie, channel）*/
	@TableField(value = "type")
    @ApiModelProperty(value="类型（series, program, movie, channel）",dataType="String",name="type")
    private String type;
	/**显示名称*/
	@TableField(value = "show_name")
    @ApiModelProperty(value="显示名称",dataType="String",name="showName")
    private String showName;
	/**操作类型（REGIST, UPDATE, DELETE）*/
	@TableField(value = "action")
    @ApiModelProperty(value="操作类型（REGIST, UPDATE, DELETE）",dataType="String",name="action")
    private String action;
	/**原对象id*/
	@TableField(value = "old_object_id")
    @ApiModelProperty(value="原对象id",dataType="String",name="oldObjectId")
    private String oldObjectId;
	/**原对象code*/
	@TableField(value = "old_object_code")
    @ApiModelProperty(value="原对象code",dataType="String",name="oldObjectCode")
    private String oldObjectCode;
	/**状态*/
	@TableField(value = "status")
    @ApiModelProperty(value="状态",dataType="Integer",name="status")
    private Integer status;
	/**错误描述*/
	@TableField(value = "error_desc")
    @ApiModelProperty(value="错误描述",dataType="String",name="errorDesc")
    private String errorDesc;
	/**cspid*/
	@TableField(value = "csp_id")
    @ApiModelProperty(value="cspid",dataType="String",name="cspId")
    private String cspId;
	/**创建时间*/
	@TableField(value = "create_time")
    @ApiModelProperty(value="创建时间",dataType="String",name="createTime")
    @JsonFormat(shape = JsonFormat.Shape.STRING,pattern = "yyyy-MM-dd HH:mm:ss",timezone="GMT+8")
    private Date createTime;
	/**修改时间*/
	@TableField(value = "update_time")
    @ApiModelProperty(value="修改时间",dataType="String",name="updateTime")
    @JsonFormat(shape = JsonFormat.Shape.STRING,pattern = "yyyy-MM-dd HH:mm:ss",timezone="GMT+8")
    private Date updateTime;
	/**是否可出库*/
	@TableField(value = "is_can_out")
    @ApiModelProperty(value="是否可出库",dataType="Integer",name="canOut")
    private Integer canOut;
	/**通知时间*/
	@TableField(value = "notic_time")
    @ApiModelProperty(value="通知时间",dataType="String",name="noticTime")
    private String noticTime;
}
