package com.pukka.iptv.common.data.model.in;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.NoArgsConstructor;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springframework.util.Assert;

import java.util.Date;

/**
 *
 * @author: tan
 * @date: 2021-9-6 15:41:53
 */

@Data
@EqualsAndHashCode
@JsonIgnoreProperties(value = { "handler" })
@NoArgsConstructor
@TableName(value = "in_order",autoResultMap=true)
public class InOrder extends Model<InOrder> implements java.io.Serializable{

    private static final long serialVersionUID = 1L;
	/**ID*/
	@TableField(value = "id")
    @ApiModelProperty(value="ID",dataType="String",name="id")
    private String id;
	/**关联ID*/
	@TableField(value = "correlate_id")
    @ApiModelProperty(value="关联ID",dataType="String",name="correlateId")
    private String correlateId;
	/**动作 1：REGIST，2：UPDATE，3：DELETE*/
	@TableField(value = "action")
    @ApiModelProperty(value="动作 1：REGIST，2：UPDATE，3：DELETE",dataType="Integer",name="action")
    private Integer action;
	/**名称*/
	@TableField(value = "show_name")
    @ApiModelProperty(value="名称",dataType="String",name="showName")
    private String showName;
	/**注入通道ID*/
	@TableField(value = "in_passage_id")
    @ApiModelProperty(value="注入通道ID",dataType="Long",name="inPassageId")
    private Long inPassageId;
	/**注入通道名称*/
	@TableField(value = "in_passage_name")
    @ApiModelProperty(value="注入通道名称",dataType="String",name="inPassageName")
    private String inPassageName;
	/**优先级*/
	@TableField(value = "priority")
    @ApiModelProperty(value="优先级",dataType="Integer",name="priority")
    private Integer priority;
	/**cspId*/
	@TableField(value = "csp_id")
    @ApiModelProperty(value="cspId",dataType="String",name="cspId")
    private String cspId;
	/**lspId*/
	@TableField(value = "lsp_id")
    @ApiModelProperty(value="lspId",dataType="String",name="lspId")
    private String lspId;
	/**状态 1：待解析 2：解析中 3：解析成功 4：解析失败 5：入库中 6：入库成功 7：入库失败 8：反馈中 9：反馈成功 10：反馈失败*/
	@TableField(value = "status")
    @ApiModelProperty(value="状态 1：待解析 2：解析中 3：解析成功 4：解析失败 5：入库中 6：入库成功 7：入库失败 8：反馈中 9：反馈成功 10：反馈失败",dataType="Integer",name="status")
    private Integer status;
	/**描述*/
	@TableField(value = "description")
    @ApiModelProperty(value="描述",dataType="String",name="description")
    private String description;
	/**createTime*/
	@TableField(value = "create_time")
    @ApiModelProperty(value="createTime",dataType="String",name="createTime")
    @JsonFormat(shape = JsonFormat.Shape.STRING,pattern = "yyyy-MM-dd HH:mm:ss",timezone="GMT+8")
    private Date createTime;
	/**updateTime*/
	@TableField(value = "update_time")
    @ApiModelProperty(value="updateTime",dataType="String",name="updateTime")
    @JsonFormat(shape = JsonFormat.Shape.STRING,pattern = "yyyy-MM-dd HH:mm:ss",timezone="GMT+8")
    private Date updateTime;
    /**重发时间*/
    @TableField(value = "resend_time")
    @ApiModelProperty(value="resendTime",dataType="String",name="resendTime")
    @JsonFormat(shape = JsonFormat.Shape.STRING,pattern = "yyyy-MM-dd HH:mm:ss",timezone="GMT+8")
    private Date resendTime;
	/**文件路径*/
	@TableField(value = "cmd_file_url")
    @ApiModelProperty(value="文件路径",dataType="String",name="cmdFileUrl")
    private String cmdFileUrl;
	/**重试次数*/
	@TableField(value = "retry_count")
    @ApiModelProperty(value="重试次数",dataType="Integer",name="retryCount")
    private Integer retryCount;
	/**处理结果0: 成功 其他: 错误代码*/
	@TableField(value = "result")
    @ApiModelProperty(value="处理结果0: 成功 其他: 错误代码",dataType="Integer",name="result")
    private Integer result;
	/**结果描述*/
	@TableField(value = "error_description")
    @ApiModelProperty(value="结果描述",dataType="String",name="errorDescription")
    private String errorDescription;

}
