package com.pukka.iptv.common.data.model;

import com.alibaba.excel.annotation.ExcelIgnore;
import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.core.toolkit.ObjectUtils;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.pukka.iptv.common.base.enums.PriorityEnums;
import io.swagger.annotations.ApiModelProperty;
import java.util.Date;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode
@JsonIgnoreProperties(value = {"handler"})
@TableName(value = "out_scheduled_tasks", autoResultMap = true)
public class OutScheduledTask implements java.io.Serializable {

    private static final long serialVersionUID = 3703761215868681700L;

    /**
     * ObjectID
     */
    @TableId(type = IdType.AUTO)
    @TableField(value = "id")
    @ApiModelProperty(value = "ObjectID", dataType = "Long", name = "id")
    private Long id;
    /**
     * 唯一标识,同一批次定时发布任务的唯一标识相同
     */
    @TableField(value = "code")
    @ApiModelProperty(value = "唯一标识", dataType = "String", name = "code")
    private String code;
    /**
     * bms的内容ID
     */
    @TableField(value = "content_id")
    @ApiModelProperty(value = "bms的内容ID", dataType = "String", name = "contentId")
    private Long contentId;
    /**
     * 内容类型 1：电影 2：子集 3：电视剧 4：系列片 5：片花 6：直播 7：物理频道 8：栏目 9：产品包 10：节目单 11：图片 12：视频介质 13：节目图片 14：剧集图片
     * 15：产品包图片 16：栏目图片 17：频道图片 18：栏目节目 19：栏目剧集 20：栏目频道 21：产品包节目 22：产品包剧集
     */
    @TableField(value = "content_type")
    @ApiModelProperty(value = "内容类型 1：电影 2：子集 3：电视剧 4：系列片 5：片花 6：直播 7：物理频道 8：栏目 9：产品包 10：节目单 11：图片 12：视频介质 13：节目图片 14：剧集图片 15：产品包图片 16：栏目图片 17：频道图片 18：栏目节目 19：栏目剧集 20：栏目频道 21：产品包节目 22：产品包剧集", dataType = "Integer", name = "contentType")
    private Integer contentType;
    /**
     * 优先级 0-普通 5-优先 10-紧急
     */
    private Integer priority;
    /**
     * 定时发布，时间存在则代表已经设定为定时发布
     */
    @ExcelIgnore
    @TableField(value = "timed_publish")
    @ApiModelProperty(value = "定时发布，时间存在则代表已经设定为定时发布", dataType = "String", name = "timedPublish")
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date timedPublish;
    /**
     * 定时发布状态，0-待发布，1-已发布
     */
    @ExcelIgnore
    @TableField(value = "timed_publish_status")
    @ApiModelProperty(value = "定时发布状态", dataType = "String", name = "timedPublishStatus")
    private Integer timedPublishStatus;
    /**
     * 定时发布，时间存在则代表已经设定为定时发布
     */
    @ExcelIgnore
    @TableField(value = "timed_publish_description")
    @ApiModelProperty(value = "定时发布描述", dataType = "String", name = "timedPublishDescription")
    private String timedPublishDescription;
    /**
     * 操作员id
     */
    @TableField(value = "creator_id", fill = FieldFill.INSERT)
    @ApiModelProperty(value = "操作员id", dataType = "Long", name = "creatorId")
    private Long creatorId;
    /**
     * 操作员name
     */
    @TableField(value = "creator_name", fill = FieldFill.INSERT)
    @ApiModelProperty(value = "操作员name", dataType = "String", name = "creatorName")
    private String creatorName;
    /**
     * 创建时间
     */
    @TableField(value = "create_time", fill = FieldFill.INSERT)
    @ApiModelProperty(value = "创建时间", dataType = "DateString", name = "createTime")
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date createTime;
    /**
     * 更新时间
     */
    @TableField(value = "update_time", fill = FieldFill.UPDATE)
    @ApiModelProperty(value = "更新时间", dataType = "String", name = "updateTime")
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date updateTime;

    @TableField(value = "cancel_time")
    @ApiModelProperty(value = "取消时间", dataType = "String", name = "cancelTime")
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date cancelTime;

    public OutScheduledTask() {
        if (ObjectUtils.isEmpty(this.priority)) {
            this.priority = PriorityEnums.GENERAL.getValue();
        }
    }
}
