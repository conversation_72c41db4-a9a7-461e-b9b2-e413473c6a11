package com.pukka.iptv.common.data.params;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.util.Date;
import lombok.Data;
import com.fasterxml.jackson.annotation.JsonFormat;

import java.io.Serializable;
import java.util.List;

/**
 * TODO 修改授权通道参数
 *
 * <AUTHOR>
 * @date 2021/9/2 23:39
 */
@Data
@ApiModel("授权通道参数")
public class OutPassageParam implements Serializable{
    @ApiModelProperty("内容id,多个以逗号分隔")
    private List<Long> ids;
    @ApiModelProperty("授权通道id,多个以逗号分隔")
    private String outPassageIds;
    @ApiModelProperty("授权通道名称,多个以逗号分隔")
    private String outPassageNames;
    @ApiModelProperty("类型:1媒资  2频道")
    private Integer type;
}
