package com.pukka.iptv.common.data.vo.req;

import com.pukka.iptv.common.base.enums.PackagePriceTypeEnum;
import com.pukka.iptv.common.data.model.bms.BmsPackage;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;
import org.springframework.util.Assert;
import org.springframework.util.StringUtils;

import javax.validation.constraints.Max;
import javax.validation.constraints.Min;
import javax.validation.constraints.NotNull;
import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @create 2021-09-10 11:53
 */
@Getter
@Setter
@Accessors(chain = true)
public class BmsPackageUpdateReq extends BmsPackage{
    @ApiModelProperty(value = "产品包id")
    @NotNull
    private Long id;

    @ApiModelProperty(value = "产品包名称")
    private String name;

    @ApiModelProperty(value = "spId")
    @NotNull
    private Long spId;

    @ApiModelProperty(value = "产品包类型 0: VOD包 2:Channel包 4:SVOD 99:Mix(待定义")
    @Min(0)
    @Max(99)
    @NotNull
    private Integer type;

    @ApiModelProperty(value = "资费类型 0：免费 1：单包月 2：续包月3：单包季4：续包季5：单包半年6：单包年7：单点收费")
    @Min(0)
    @Max(7)
    @NotNull
    private Integer priceType;

    @ApiModelProperty(value = "定价")
    private BigDecimal price;

    @ApiModelProperty(value = "描述信息")
    private String description;

    @ApiModelProperty(value = "生效时间")
    private String licensingWindowStart;

    @ApiModelProperty(value = "失效时间")
    private String licensingWindowEnd;

    @ApiModelProperty(value = "状态标志  0:失效 1:生效(默认有效)")
    private Integer status;

    // 校验价格
    public void validPrice() {
        // Valid做了priceType非空校验 非免费包，价格不能小于0
        if(PackagePriceTypeEnum.FREE.getCode() != priceType){
            Assert.state( price != null && price.signum() > 0, "定价不能小于0");
        } else {
            Assert.isNull(price, PackagePriceTypeEnum.FREE.getValue()+" 不可设置价格");
        }
    }

    // 校验时间
    public void validTime() {
        if (StringUtils.hasText(licensingWindowStart) && StringUtils.hasText(licensingWindowEnd))
        // 2021-11-17 15:03:15  长度为14
        Assert.state(licensingWindowStart.length() + licensingWindowEnd.length() == 28, "时间格式错误");
    }

}
