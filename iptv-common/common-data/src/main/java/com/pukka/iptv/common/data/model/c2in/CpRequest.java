package com.pukka.iptv.common.data.model.c2in;

import com.alibaba.excel.annotation.format.DateTimeFormat;
import java.util.Date;
import lombok.Data;
import com.fasterxml.jackson.annotation.JsonFormat;

import java.io.Serializable;

/**
 * @ClassName CpRequest
 * @Description TODO
 * <AUTHOR>
 * @Date 2021/9/6 14:23
 * @Version
 */
@Data
public class CpRequest implements Serializable {

    private String cmdFileUrl;

    private String lspId;

    private String cspId;

    private String correlateId;
}
