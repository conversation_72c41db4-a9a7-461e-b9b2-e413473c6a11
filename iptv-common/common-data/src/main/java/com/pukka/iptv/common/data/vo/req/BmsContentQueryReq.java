package com.pukka.iptv.common.data.vo.req;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.pukka.iptv.common.data.model.bms.BmsContent;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import org.springframework.util.Assert;

import javax.validation.constraints.Max;
import javax.validation.constraints.Min;
import javax.validation.constraints.Pattern;
import java.util.List;

import static com.pukka.iptv.common.data.util.RegexUtil.ALL_CHAR_REGEX;


/**
 * @author: wz
 * @date: 2021/8/31 11:43
 * @description: 内容查询vo
 */
@Getter
@Setter
@JsonIgnoreProperties(ignoreUnknown = true)
public class BmsContentQueryReq extends Page<BmsContent> {
    @Min(0)
    @ApiModelProperty(value = "spId", dataType = "Long", name = "spId")
    private Long spId;
    @ApiModelProperty(value = "cpId", dataType = "Long", name = "cpId")
    private Long cpId;
    @Pattern(regexp = ALL_CHAR_REGEX + "{0,5000}")
    @ApiModelProperty(value = "name", dataType = "String", name = "媒资名称")
    private String name;
    @Min(0)
    @ApiModelProperty(value = "媒资分类ID，来自pgm_category表", dataType = "Long", name = "pgmCategoryId")
    private Long pgmCategoryId;
    @Min(0)
    @Max(50)
    @ApiModelProperty(value = "1：电影 3：电视剧 4：系列片 5：片花 此处没有子集，子集由program表单独保存", dataType = "Integer", name = "contentType")
    private Integer contentType;
    @ApiModelProperty(value = "发布状态  1：待发布 2：发布中 3：发布成功 4：发布失败 5：待更新 6：更新中 7：更新失败 8：回收中 9：回收成功 10：回收失败", dataType = "Integer", name = "publishStatus")
    @Min(0)
    @Max(20)
    private Integer publishStatus;
    @Min(0)
    @Max(4)
    @ApiModelProperty(value = "op审核 1：op未审核 2：审核中 3：审核通过 4：审核未通过", dataType = "Integer", name = "opCheckStatus")
    private Integer opCheckStatus;
    @Min(0)
    @Max(4)
    @ApiModelProperty(value = "状态标志 1:生效 0:失效", dataType = "Integer", name = "status")
    private Integer status;
    @Min(0)
    @Max(4)
    @ApiModelProperty(value = "内容编辑锁状态1:未锁定 2:已锁定", dataType = "Integer", name = "lockStatus")
    private Integer lockStatus;
    @Max(4)
    @Min(0)
    @ApiModelProperty(value = "是否缺集 1:缺集 2:不缺", dataType = "Integer", name = "missingEpisodes")
    private Integer missingEpisodes;
    @ApiModelProperty(value = "发布通道id", dataType = "String", name = "outPassageIds")
    private String outPassageId;
    @Min(0)
    @Max(Long.MAX_VALUE)
    private Long categoryId;

    private String originalName;

    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private String startUpdateTime;

    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private String endUpdateTime;

    /**
     * 节目清晰度标识
     */
    @ApiModelProperty(value = "节目清晰度标识 0：标清 1：高清  2：超清 3: 4K  4: 杜比（4K+杜比）\n", dataType = "Integer", name = "definitionFlag")
    private Integer definitionFlag;

    private String cmsContentCode;

    /**
     * 1 孤立内容   0 所有   查询孤立内容
     */
    private int lonelyContent;

    /**
     * 1 已授权  0 未授权
     */
    private Integer authorize;

    @ApiModelProperty(value="媒资类型,0-单集 1-剧集",example = "1",name = "seriesFlag")
    private Integer seriesFlag;

    // 当包含换行符时，是多名称查询
    private String[] nameList;
    /**
     * sp名称
     */
    private String spName;



    private List<Long> spIdList;


    public void validSpId() {
        Assert.notNull(spId, "spID不能为null");
    }
}
