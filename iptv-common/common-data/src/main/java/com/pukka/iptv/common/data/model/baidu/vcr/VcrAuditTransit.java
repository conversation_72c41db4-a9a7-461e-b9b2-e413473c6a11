package com.pukka.iptv.common.data.model.baidu.vcr;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.pukka.iptv.common.data.model.baidu.vcr.request.VcrAuditTaskRequest;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

/**
 * <AUTHOR>
 * @Description 视频审核通讯实体
 * @Date 2024/10/22 14:14
 * @Version V1.0
 **/
@Data
@EqualsAndHashCode
@JsonIgnoreProperties(value = {"handler"})
@NoArgsConstructor
@Accessors(chain = true)
public class VcrAuditTransit extends VcrAuditTaskRequest implements java.io.Serializable {

    private static final long serialVersionUID = -7530287795320502952L;

    /**
     * 媒资Id
     */
    private Long contentId;

    /**
     * 内容类型
     */
    private Integer contentType;

    /**
     * 内容名称
     */
    private String showName;

    /**
     * 操作员id
     */
    private Long creatorId;

    /**
     * 操作员name
     */
    private String creatorName;

    /**
     * 审核状态
     */
    private Integer status;
}
