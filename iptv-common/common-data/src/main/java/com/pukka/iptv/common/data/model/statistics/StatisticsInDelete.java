package com.pukka.iptv.common.data.model.statistics;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModelProperty;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.EqualsAndHashCode;
import lombok.Data;

/**
 *
 * @author: tan
 * @date: 2022-7-21 9:54:47
 */

@Data
@EqualsAndHashCode
@JsonIgnoreProperties(value = { "handler" })
@TableName(value = "statistics_in_delete",autoResultMap=true)
public class StatisticsInDelete extends Model<StatisticsInDelete> implements java.io.Serializable{

    private static final long serialVersionUID = 1L;
	/**id*/
	@TableField(value = "id")
    @TableId(type = IdType.AUTO)
    @ApiModelProperty(value="id",dataType="Long",name="id")
    private Long id;
	/**统计类型*/
	@TableField(value = "type")
    @ApiModelProperty(value="统计类型",dataType="Integer",name="type")
    private Integer type;
	/**媒资类型*/
	@TableField(value = "content_type")
    @ApiModelProperty(value="媒资类型",dataType="Integer",name="contentType")
    private Integer contentType;
	/**媒资code*/
	@TableField(value = "code")
    @ApiModelProperty(value="媒资code",dataType="String",name="code")
    private String code;
	/**媒资名称*/
	@TableField(value = "name")
    @ApiModelProperty(value="媒资名称",dataType="String",name="name")
    private String name;
	/**cmsContentId*/
	@TableField(value = "cms_content_id")
    @ApiModelProperty(value="cmsContentId",dataType="Long",name="cmsContentId")
    private Long cmsContentId;
	/**cmsContentCode*/
	@TableField(value = "cms_content_code")
    @ApiModelProperty(value="cmsContentCode",dataType="String",name="cmsContentCode")
    private String cmsContentCode;
	/**删除的时间*/
	@TableField(value = "statictic_date")
    @ApiModelProperty(value="删除的时间",dataType="String",name="staticticDate")
    private String staticticDate;
	/**状态（0 未处理， 1已处理）*/
	@TableField(value = "status")
    @ApiModelProperty(value="状态（0 未处理， 1已处理）",dataType="Integer",name="status")
    private Integer status;
	/**createTime*/
	@TableField(value = "create_time")
    @ApiModelProperty(value="createTime",dataType="String",name="createTime")
    private String createTime;
	/**updateTime*/
	@TableField(value = "update_time")
    @ApiModelProperty(value="updateTime",dataType="String",name="updateTime")
    private String updateTime;
}
