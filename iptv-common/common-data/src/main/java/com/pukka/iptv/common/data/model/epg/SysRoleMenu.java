package com.pukka.iptv.common.data.model.epg;

import com.baomidou.mybatisplus.annotation.*;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.Date;

/**
 * 角色菜单关系
 * 
 * <AUTHOR>
 * @email 
 * @date 2023-03-20 09:43:53
 */
@Data
@EqualsAndHashCode
@JsonIgnoreProperties(value = { "handler" })
@NoArgsConstructor
@TableName(value = "sys_role_menu",autoResultMap=true)
@Accessors(chain = true)
public class SysRoleMenu extends Model<SysRoleMenu> implements Serializable {

	private static final long serialVersionUID = -990858349042416270L;
	/**ID*/
	@TableId(type = IdType.AUTO)
	@TableField(value = "id")
	@ApiModelProperty(value="ID",dataType="Long",name="id")
	private Long id;
	/**
	 * 角色ID
	 */
	@TableField(value = "role_id")
	@ApiModelProperty(value="角色ID",dataType="Long",name="roleId")
	private Long roleId;
	/**
	 * 菜单参数集
	 * key:菜单id value:control对象
	 */
	@TableField(value = "menu_params")
	@ApiModelProperty(value="菜单参数集",dataType="String",name="menuParams")
	private String menuParams;
	/**创建时间*/
	@TableField(value = "create_time", fill = FieldFill.INSERT)
	@ApiModelProperty(value="创建时间",dataType="String",name="createTime")
	@JsonFormat(shape = JsonFormat.Shape.STRING,pattern = "yyyy-MM-dd HH:mm:ss",timezone="GMT+8")
	private Date createTime;
	/**更新时间*/
	@TableField(value = "update_time")
	@ApiModelProperty(value="更新时间",dataType="String",name="updateTime")
	@JsonFormat(shape = JsonFormat.Shape.STRING,pattern = "yyyy-MM-dd HH:mm:ss",timezone="GMT+8")
	private Date updateTime;

}
