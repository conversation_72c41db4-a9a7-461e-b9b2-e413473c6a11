package com.pukka.iptv.common.data.vo.bms;

import com.alibaba.excel.annotation.ExcelProperty;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

import java.util.Date;

/**
 * @Author: wangbo
 * @Date: 2022/4/8 11:44
 */
@Getter
@Setter
@EqualsAndHashCode
@Accessors(chain = true)
public class BmsPictureExcelVO {
    @ExcelProperty("code")
    private String code;
    @ExcelProperty("创建日期")
    private Date creatTime;
    @ExcelProperty("文件路径")
    private String fileUrl;
    @ExcelProperty("所属sp")
    private String spName;
}
