package com.pukka.iptv.common.data.model.sys;

import com.baomidou.mybatisplus.annotation.*;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.NoArgsConstructor;
import lombok.EqualsAndHashCode;
import lombok.Data;

import java.util.Date;

/**
 *
 * @author: zhengcl
 * @date: 2021-9-8 18:11:54
 */

@Data
@EqualsAndHashCode
@JsonIgnoreProperties(value = { "handler" })
@NoArgsConstructor
@TableName(value = "sys_storage",autoResultMap=true)
public class SysStorage extends Model<SysStorage> implements java.io.Serializable{

    private static final long serialVersionUID = 1L;
	/**ID*/
    @TableId(type = IdType.AUTO)
	@TableField(value = "id")
    @ApiModelProperty(value="ID",dataType="Long",name="id")
    private Long id;
	/**名称##ISSEARCH*/
	@TableField(value = "name")
    @ApiModelProperty(value="名称##ISSEARCH",dataType="String",name="name")
    private String name;
	/**存储类型 1：OSS 2：OSSFTP 3：FTP 4：ISSEARCH*/
	@TableField(value = "storage_type")
    @ApiModelProperty(value="存储类型 1：OSS 2：OSSFTP 3：FTP 4：ISSEARCH",dataType="Integer",name="storageType")
    private Integer storageType;
	/**公网访问地址*/
	@TableField(value = "outer_url")
    @ApiModelProperty(value="公网访问地址,IP + Port 如 *************:21",dataType="String",name="outerUrl")
    private String outerUrl;
	/**内网访问地址*/
	@TableField(value = "inner_url")
    @ApiModelProperty(value="内网访问地址 IP + Port 如 *************:21",dataType="String",name="innerUrl")
    private String innerUrl;
	/**图片展示地址*/
	@TableField(value = "picture_url")
    @ApiModelProperty(value="图片展示地址",dataType="String",name="pictureUrl")
    private String pictureUrl;
    /**状态1：正常 0：停用 255：删除失效 */
	@TableField(value = "status")
    @ApiModelProperty(value="状态1：正常 0：停用 255##失效##ISSEARCH",dataType="Integer",name="status")
    private Integer status;
    /**存储磁盘总空间（单位Kb）*/
    @TableField(value = "total_space")
    @ApiModelProperty(value="存储磁盘总空间（单位Kb）",dataType="Long",name="totalSpace")
    private Long totalSpace;
	/**源片存储目录*/
	@TableField(value = "original_directory")
    @ApiModelProperty(value="源片存储目录",dataType="String",name="originalDirectory")
    private String originalDirectory;
	/**成片存储目录*/
	@TableField(value = "movie_directory")
    @ApiModelProperty(value="成片存储目录",dataType="String",name="movieDirectory")
    private String movieDirectory;
	/**图片存储目录*/
	@TableField(value = "picture_directory")
    @ApiModelProperty(value="图片存储目录",dataType="String",name="pictureDirectory")
    private String pictureDirectory;
	/**文档存储目录*/
	@TableField(value = "document_directory")
    @ApiModelProperty(value="文档存储目录",dataType="String",name="documentDirectory")
    private String documentDirectory;
	/**工单存储目录*/
	@TableField(value = "cmd_directory")
    @ApiModelProperty(value="工单存储目录",dataType="String",name="cmdDirectory")
    private String cmdDirectory;
	/**注入图片存储地址前缀*/
	@TableField(value = "picture_http_prefix")
    @ApiModelProperty(value="注入图片存储地址前缀",dataType="String",name="inPicturePrefix")
    private String pictureHttpPrefix;
	/**注入源片存储地址前缀*/
	@TableField(value = "movie_http_prefix")
    @ApiModelProperty(value="注入源片存储地址前缀",dataType="String",name="inMoviePrefix")
    private String movieHttpPrefix;
	/**注入工单存储地址前缀*/
	@TableField(value = "in_xml_ftp_prefix")
    @ApiModelProperty(value="注入工单存储地址前缀",dataType="String",name="inXmlPrefix")
    private String inXmlFtpPrefix;
	/**分发图片存储地址前缀*/
	@TableField(value = "out_picture_ftp_prefix")
    @ApiModelProperty(value="分发图片存储地址前缀",dataType="String",name="outPicturePrefix")
    private String outPictureFtpPrefix;
	/**分发源片存储地址前缀*/
	@TableField(value = "out_movie_ftp_prefix")
    @ApiModelProperty(value="分发源片存储地址前缀",dataType="String",name="outMoviePrefix")
    private String outMovieFtpPrefix;
	/**分发工单存储地址前缀*/
	@TableField(value = "out_xml_ftp_prefix")
    @ApiModelProperty(value="分发工单存储地址前缀",dataType="String",name="outXmlPrefix")
    private String outXmlFtpPrefix;
    /**创建人来自admin表*/
    @TableField(value = "creator_name", fill = FieldFill.INSERT)
    @ApiModelProperty(value="创建人来自admin表",dataType="String",name="creatorName")
    private String creatorName;
    /**备注*/
    @TableField(value = "description")
    @ApiModelProperty(value="备注",dataType="String",name="description")
    private String description;
    /**来自admin表*/
    @TableField(value = "creator_id", fill = FieldFill.INSERT)
    @ApiModelProperty(value="来自admin表",dataType="Long",name="creatorId")
    private Long creatorId;
    /**创建时间*/
    @TableField(value = "create_time", fill = FieldFill.INSERT)
    @ApiModelProperty(value="创建时间",dataType="String",name="createTime")
    @JsonFormat(shape = JsonFormat.Shape.STRING,pattern = "yyyy-MM-dd HH:mm:ss",timezone="GMT+8")
    private Date createTime;
    /**更新时间*/
    @TableField(value = "update_time")
    @ApiModelProperty(value="更新时间",dataType="String",name="updateTime")
    @JsonFormat(shape = JsonFormat.Shape.STRING,pattern = "yyyy-MM-dd HH:mm:ss",timezone="GMT+8")
    private Date updateTime;

    @TableField(value = "out_movie_http_prefix")
    @ApiModelProperty(value="分发视频http地址前缀",dataType="String",name="description")
    private String outMovieHttpPrefix;

    @TableField(value = "out_picture_http_prefix")
    @ApiModelProperty(value="分发图片http地址前缀",dataType="String",name="description")
    private String outPictureHttpPrefix;
}
