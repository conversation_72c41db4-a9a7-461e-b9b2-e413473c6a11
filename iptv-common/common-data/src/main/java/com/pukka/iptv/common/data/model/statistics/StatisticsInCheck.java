package com.pukka.iptv.common.data.model.statistics;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModelProperty;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.EqualsAndHashCode;
import lombok.Data;

/**
 *
 * @author: tan
 * @date: 2022-7-21 9:15:48
 */

@Data
@EqualsAndHashCode
@JsonIgnoreProperties(value = { "handler" })
@TableName(value = "statistics_in_check",autoResultMap=true)
public class StatisticsInCheck extends Model<StatisticsInCheck> implements java.io.Serializable{

    private static final long serialVersionUID = 1L;
	/**id*/
	@TableField(value = "id")
    @TableId(type = IdType.AUTO)
    @ApiModelProperty(value="id",dataType="Long",name="id")
    private Long id;
	/**统计类型*/
	@TableField(value = "type")
    @ApiModelProperty(value="统计类型",dataType="Integer",name="type")
    private Integer type;
	/**媒资类型*/
	@TableField(value = "content_type")
    @ApiModelProperty(value="媒资类型",dataType="Integer",name="contentType")
    private Integer contentType;
	/**数量*/
	@TableField(value = "count")
    @ApiModelProperty(value="数量",dataType="Integer",name="count")
    private Integer count;
	/**duration*/
	@TableField(value = "duration")
    @ApiModelProperty(value="duration",dataType="Long",name="duration")
    private Long duration;
	/**cpId*/
	@TableField(value = "cp_id")
    @ApiModelProperty(value="cpId",dataType="Long",name="cpId")
    private Long cpId;
	/**cpName*/
	@TableField(value = "cp_name")
    @ApiModelProperty(value="cpName",dataType="String",name="cpName")
    private String cpName;
	/**统计日期*/
	@TableField(value = "statistic_date")
    @ApiModelProperty(value="统计日期",dataType="String",name="statisticDate")
    private String statisticDate;
	/**创建时间*/
	@TableField(value = "create_time")
    @ApiModelProperty(value="创建时间",dataType="String",name="createTime")
    private String createTime;
	/** 修改时间*/
	@TableField(value = "update_time")
    @ApiModelProperty(value=" 修改时间",dataType="String",name="updateTime")
    private String updateTime;

    /**1.自审；2.终审*/
    @TableField(value = "statistic_type")
    @ApiModelProperty(value="1.自审；2.终审",dataType="Integer",name="statisticType")
    private Integer statisticType;

    /**0:通过； 1：不通过*/
    @TableField(value = "pass_type")
    @ApiModelProperty(value="0:通过； 1：不通过",dataType="Integer",name="passType")
    private Integer passType;
}
