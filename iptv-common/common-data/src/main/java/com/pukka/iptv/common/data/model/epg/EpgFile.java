package com.pukka.iptv.common.data.model.epg;

import com.baomidou.mybatisplus.annotation.*;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.Date;

/**
 * epg_files
 * 
 * <AUTHOR>
 * @email 
 * @date 2023-03-01 11:11:32
 */
@Data
@EqualsAndHashCode
@Accessors(chain = true)
@JsonIgnoreProperties(value = { "handler" })
@NoArgsConstructor
@TableName(value = "epg_file",autoResultMap=true)
public class EpgFile implements Serializable {

	private static final long serialVersionUID = -437077989923332840L;

	/**
	 *
	 */
	@TableId(type = IdType.AUTO)
	@TableField(value = "id")
	@ApiModelProperty(value="ObjectID",dataType="Long",name="id")
	private Long id;
	/**
	 * 文件唯一编码
	 */
	@TableField(value = "code", fill = FieldFill.INSERT)
	@ApiModelProperty(value="文件唯一编码",dataType="String",name="code")
	private String code;
	/**
	 * 唯一ID,时间戳yyyymmddHHmmss+自增序号格式
	 */
	@TableField(value = "file_id", fill = FieldFill.INSERT)
	@ApiModelProperty(value="工单唯一ID",dataType="String",name="fileId")
	private String fileId;
	/**
	 * 文件名称,不可重复
	 */
	@TableField(value = "show_name")
	@ApiModelProperty(value="文件名称",dataType="String",name="showName")
	private String showName;
	/**
	 * 文件描述
	 */
	@TableField(value = "description",updateStrategy = FieldStrategy.IGNORED)
	@ApiModelProperty(value="文件描述",dataType="String",name="description")
	private String description;
	/**
	 * 创建人ID，来自于user表
	 */
	@TableField(value = "creator_id", fill = FieldFill.INSERT)
	@ApiModelProperty(value=" 创建人ID",dataType="Long",name="creatorId")
	private Long creatorId;
	/**
	 * 创建人
	 */
	@TableField(value = "creator_name", fill = FieldFill.INSERT)
	@ApiModelProperty(value="创建人",dataType="String",name="creatorName")
	private String creatorName;
	/**
	 * 文件ftp地址
	 */
	@TableField(value = "file_path")
	@ApiModelProperty(value="文件ftp地址",dataType="String",name="filePath")
	private String filePath;
	/**
	 * 该文件集是否为系统层文件
	 * 0：NOT System File
	 * 1：System File
	 */
	@TableField(value = "system_file")
	@ApiModelProperty(value="文件ftp地址",dataType="String",name="systemFile")
	private Integer systemFile;
	/**
	 * md5值
	 */
	@TableField(value = "md5")
	@ApiModelProperty(value="文件ftp地址",dataType="String",name="md5")
	private String md5;
	/**
	 * 状态,-1:失效,0:生效，默认生效
	 */
	@TableField(value = "status")
	@ApiModelProperty(value="状态",dataType="Integer",name="status")
	private Integer status;
	/**
	 * 所属存储
	 */
	@TableField(value = "storage_name")
	@ApiModelProperty(value="所属存储",dataType="String",name="storageName")
	private String storageName;
	/**
	 * 存储ID
	 */
	@TableField(value = "storage_id")
	@ApiModelProperty(value="存储ID",dataType="Long",name="storageId")
	private Long storageId;
	/**
	 * 上传时间
	 */
	@TableField(value = "create_time", fill = FieldFill.INSERT)
	@ApiModelProperty(value="上传时间",dataType="String",name="createTime")
	@JsonFormat(shape = JsonFormat.Shape.STRING,pattern = "yyyy-MM-dd HH:mm:ss",timezone="GMT+8")
	private Date createTime;
	/**
	 * 更新时间
	 */
	@TableField(value = "update_time", fill = FieldFill.INSERT_UPDATE)
	@ApiModelProperty(value="更新时间",dataType="String",name="updateTime")
	@JsonFormat(shape = JsonFormat.Shape.STRING,pattern = "yyyy-MM-dd HH:mm:ss",timezone="GMT+8")
	private Date updateTime;

	public EpgFile(String id,String path,Integer status ){
		this.fileId = id;
		this.filePath = path;
		this.status = status;
	}
}
