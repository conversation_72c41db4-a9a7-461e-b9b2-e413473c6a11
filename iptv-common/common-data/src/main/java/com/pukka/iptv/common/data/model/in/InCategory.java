package com.pukka.iptv.common.data.model.in;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.NoArgsConstructor;
import lombok.EqualsAndHashCode;
import lombok.Data;

import java.util.Date;

/**
 *
 * @author: tan
 * @date: 2021-11-1 9:10:32
 */

@Data
@EqualsAndHashCode
@JsonIgnoreProperties(value = { "handler" })
@NoArgsConstructor
@TableName(value = "in_category",autoResultMap=true)
public class InCategory extends Model<InCategory> implements java.io.Serializable{

    private static final long serialVersionUID = 1L;
	/**主键*/
	@TableField(value = "id")
    @TableId(type = IdType.AUTO)
    @ApiModelProperty(value="主键",dataType="Long",name="id")
    private Long id;
	/**唯一id*/
	@TableField(value = "category_id")
    @ApiModelProperty(value="唯一id",dataType="String",name="categoryId")
    private String categoryId;
	/**动作 1：REGIST，2：UPDATE，3：DELETE*/
	@TableField(value = "action")
    @ApiModelProperty(value="动作 1：REGIST，2：UPDATE，3：DELETE",dataType="Integer",name="action")
    private Integer action;
	/**全局唯一标识*/
	@TableField(value = "code")
    @ApiModelProperty(value="全局唯一标识",dataType="String",name="code")
    private String code;
	/**分类名称*/
	@TableField(value = "name")
    @ApiModelProperty(value="分类名称",dataType="String",name="name")
    private String name;
	/**父节点code*/
	@TableField(value = "parent_code")
    @ApiModelProperty(value="父节点code",dataType="String",name="parentCode")
    private String parentCode;
	/**父节点ID*/
	@TableField(value = "parent_id")
    @ApiModelProperty(value="父节点ID",dataType="Long",name="parentId")
    private Long parentId;
	/**显示顺序号*/
	@TableField(value = "sequence")
    @ApiModelProperty(value="显示顺序号",dataType="Integer",name="sequence")
    private Integer sequence;
	/**状态标志 2:失效 1:生效*/
	@TableField(value = "status")
    @ApiModelProperty(value="状态标志 2:失效 1:生效",dataType="Integer",name="status")
    private Integer status;
	/**描述信息*/
	@TableField(value = "description")
    @ApiModelProperty(value="描述信息",dataType="String",name="description")
    private String description;
	/**创建时间*/
	@TableField(value = "create_time")
    @ApiModelProperty(value="创建时间",dataType="String",name="createTime")
    @JsonFormat(shape = JsonFormat.Shape.STRING,pattern = "yyyy-MM-dd HH:mm:ss",timezone="GMT+8")
    private Date createTime;
	/**更新时间*/
	@TableField(value = "update_time")
    @ApiModelProperty(value="更新时间",dataType="String",name="updateTime")
    @JsonFormat(shape = JsonFormat.Shape.STRING,pattern = "yyyy-MM-dd HH:mm:ss",timezone="GMT+8")
    private Date updateTime;
	/**应答文件包含，0: 成功 其他: 错误代码*/
	@TableField(value = "result")
    @ApiModelProperty(value="应答文件包含，0: 成功 其他: 错误代码",dataType="Integer",name="result")
    private Integer result;
	/**应答文件包含，错误描述*/
	@TableField(value = "error_description")
    @ApiModelProperty(value="应答文件包含，错误描述",dataType="String",name="errorDescription")
    private String errorDescription;
	/**主公单ID*/
	@TableField(value = "correlate_id")
    @ApiModelProperty(value="主公单ID",dataType="String",name="correlateId")
    private String correlateId;
    /**extraCode*/
    @TableField(value = "extra_code")
    @ApiModelProperty(value="上游爱上cp传入code2字段，兼容底量数据使用",dataType="String",name="extraCode")
	private String extraCode;
}
