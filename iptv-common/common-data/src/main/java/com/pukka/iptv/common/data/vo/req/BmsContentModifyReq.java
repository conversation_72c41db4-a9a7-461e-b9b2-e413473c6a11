package com.pukka.iptv.common.data.vo.req;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.NoArgsConstructor;
import com.pukka.iptv.common.data.model.bms.BmsContent;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

import javax.validation.constraints.*;
import java.util.List;


/**
 * @author: wz
 * @date: 2021/8/31 11:43
 * @description: 内容修改vo
 */
@Getter
@Setter
@JsonIgnoreProperties(ignoreUnknown = true)
public class BmsContentModifyReq extends Page<BmsContent> {

    @NotEmpty
    @Size(max = 255)
    private List<Long> ids;
    @ApiModelProperty(value = "发布状态  1：待发布 2：发布中 3：发布成功 4：发布失败 5：待更新 6：更新中 7：更新失败 8：回收中 9：回收成功 10：回收失败", dataType = "Integer", name = "publishStatus")
    @Min(0)
    @Max(20)
    private Integer publishStatus;
    @Min(0)
    @Max(4)
    @ApiModelProperty(value = "状态标志 1:生效 0:失效", dataType = "Integer", name = "status")
    private Integer status;
    @Min(0)
    @Max(4)
    @ApiModelProperty(value = "内容编辑锁状态1:未锁定 2:已锁定", dataType = "Integer", name = "lockStatus")
    private Integer lockStatus;
}
