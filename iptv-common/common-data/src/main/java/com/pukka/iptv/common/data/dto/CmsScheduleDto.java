package com.pukka.iptv.common.data.dto;

import com.pukka.iptv.common.base.enums.RequestResourceEnum;
import com.pukka.iptv.common.base.enums.SourceEnum;
import com.pukka.iptv.common.data.model.cms.CmsSchedule;
import com.pukka.iptv.common.data.model.in.InSchedule;
import lombok.Data;
import org.springframework.beans.BeanUtils;
import org.springframework.util.Assert;

import java.io.Serializable;

@Data
public class CmsScheduleDto extends CmsSchedule implements Serializable {

    private String ids;
    /**
     * 专线创建1  人工创建2
     */
    private Integer requestResource;

    private Long spId;

    private String cspId;

    private String startDataTime;

    private String endDataTime;

    public void validRequestResource() {
        Assert.notNull(requestResource, "请选择人工创建或专线创建");
    }

    //节目单新增参数校验
    public void validScheduleSave() {
        if (requestResource.equals(RequestResourceEnum.SYSTEM.getCode())) {
            validSchedule();
            setProgramName(getProgramName().trim());
        }
    }

    //节目单修改参数校验
    public void validScheduleUpdate() {
        if (requestResource.equals(RequestResourceEnum.SYSTEM.getCode())) {
            Assert.notNull(getId(), "节目单ID不能为空");
            validSchedule();
            setProgramName(getProgramName().trim());
        }
    }

    public CmsScheduleDto(InSchedule inSchedule) {
        BeanUtils.copyProperties(inSchedule, this, "id", "createTime", "updateTime", "channelId", "channelName", "channelCode");
        this.setSource(SourceEnum.SYSWORK.getValue());
    }

    public CmsScheduleDto() {
    }

    private void validSchedule() {
        Assert.hasText(getChannelName(), "所属频道不能为空");
        Assert.notNull(getChannelId(), "频道ID不能为空");
        Assert.hasText(getProgramName(), "节目名称不能为空");
//        Assert.hasText(getStartDate(), "开播日期不能为空");
        Assert.hasText(getStartTime(), "开播时间不能为空");
        Assert.hasText(getDuration(), "节目时长不能为空");
    }

}
