package com.pukka.iptv.common.data.model.virtual;

import com.baomidou.mybatisplus.annotation.*;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.util.Date;

@Data
@JsonIgnoreProperties(value = {"handler"})
@TableName(value = "virtual_series", autoResultMap = true)
@Accessors(chain = true)
public class VirtualSeries extends Model<VirtualSeries> implements java.io.Serializable {
    private static final long serialVersionUID = 1L;
    /** ObjectID */
    @TableId(type = IdType.AUTO)
    @TableField(value = "id")
    @ApiModelProperty(value = "ObjectID", dataType = "Long", name = "id")
    private Long id;
    /** 全局编码 */
    @TableField(value = "code")
    @ApiModelProperty(value = "全局编码", dataType = "String", name = "code")
    private String code;
    /**内容类型 1：电影 2：子集 3：电视剧 */
    @TableField(value = "content_type")
    @ApiModelProperty(value="内容类型 1：电影 2：子集 3：电视剧 ",dataType="Integer",name="contentType")
    private Integer contentType;
    /** 类型 */
    @TableField(value = "type")
    @ApiModelProperty(value = "类型", dataType = "String", name = "type")
    private String type;
    /** 名称 */
    @TableField(value = "show_name")
    @ApiModelProperty(value = "名称", dataType = "String", name = "name")
    private String name;
    /** 来源 */
    @TableField(value = "vsp_name")
    @ApiModelProperty(value = "来源", dataType = "String", name = "vspname")
    private String vspname;
    /** 集数 */
    @TableField(value = "episode_num")
    @ApiModelProperty(value = "总集数", dataType = "String", name = "episodenum")
    private String episodenum;
    /** 属性 */
    @TableField(value = "price")
    @ApiModelProperty(value = "属性", dataType = "String", name = "price")
    private String price;
    /** 创建时间 */
    @TableField(value = "create_time", fill = FieldFill.INSERT)
    @ApiModelProperty(value = "创建时间", dataType = "DateString", name = "createTime")
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date createTime;
}
