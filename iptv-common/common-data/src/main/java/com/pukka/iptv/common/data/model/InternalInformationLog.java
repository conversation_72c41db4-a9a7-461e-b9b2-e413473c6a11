package com.pukka.iptv.common.data.model;

import com.baomidou.mybatisplus.annotation.*;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import io.jsonwebtoken.lang.Assert;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;
import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 * @Description 站内信
 * @Date 2024/5/11 10:27
 * @Version V1.0
 **/
@NoArgsConstructor
@Data
@EqualsAndHashCode
@Accessors(chain = true)
@TableName(value = "internal_information_log",autoResultMap=true)
public class InternalInformationLog extends Model<InternalInformationLog> implements java.io.Serializable{

    private static final long serialVersionUID = 5685798862494910993L;
    /**
     * 主键
     */
    @TableId(type = IdType.AUTO)
    @TableField(value = "id")
    @ApiModelProperty(value="ObjectID",dataType="Long",name="id")
    private Long id;

    /**
     * 全局唯一标识
     */
    @TableField(value = "code", fill = FieldFill.INSERT)
    @ApiModelProperty(value = "全局唯一标识", dataType = "String", name = "code")
    private String code;

    /**
     * 信息类型
     */
    @TableField(value = "type")
    @ApiModelProperty(value = "信息类型", dataType = "Integer", name = "type")
    private Integer type;

    /**
     * 标题
     */
    @TableField(value = "title")
    @ApiModelProperty(value = "标题", dataType = "String", name = "title")
    private String title;
    /**
     * 信息内容
     */
    @TableField(value = "content")
    @ApiModelProperty(value = "信息内容", dataType = "String", name = "content")
    private String content;

    /**
     * 消息等级
     */
    @TableField(value = "level")
    @ApiModelProperty(value = "消息等级", dataType = "Integer", name = "level")
    private Integer level;
    /**
     * 信息状态
     */
    @TableField(value = "status")
    @ApiModelProperty(value = "信息状态", dataType = "Integer", name = "status")
    private Integer status;
    /**
     * 信息状态描述
     */
    @TableField(value = "status_description")
    @ApiModelProperty(value = "信息状态描述", dataType = "String", name = "statusDescription")
    private String statusDescription;
    /**
     * 确认状态,1:未读，2:已读
     */
    @TableField(value = "result")
    @ApiModelProperty(value = "确认状态,1:未读，2:已读", dataType = "Integer", name = "result")
    private Integer result;
    /**
     * 目标用户 id
     */
    @TableField(value = "target_user_id")
    @ApiModelProperty(value = "目标用户 id", dataType = "Integer", name = "targetUserId")
    private Long targetUserId;

    /**
     * 目标用户名称
     */
    @TableField(value = "target_user_name")
    @ApiModelProperty(value = "目标用户名称", dataType = "String", name = "targetUserName")
    private String targetUserName;

    /**
     * 参数集合
     */
    @TableField(value = "param")
    @ApiModelProperty(value = "参数集合", dataType = "String", name = "param")
    private String param;

    /**
     * 信息备注
     */
    @TableField(value = "remark")
    @ApiModelProperty(value = "信息备注", dataType = "String", name = "remark")
    private String remark;

    /**
     * 信息创建时间
     */
    @TableField(value = "create_time")
    @ApiModelProperty(value = "信息创建时间", dataType = "String", name = "createTime")
    private String createTime;

    /**
     * 信息更新时间
     */
    @TableField(value = "update_time")
    @ApiModelProperty(value = "信息更新时间", dataType = "String", name = "updateTime")
    private String updateTime;

    public void valid() {
        Assert.notNull(targetUserId, "用户ID不能为null");
    }

}
