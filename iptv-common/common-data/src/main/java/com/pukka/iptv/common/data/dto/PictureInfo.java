package com.pukka.iptv.common.data.dto;

import lombok.Data;

import javax.validation.constraints.NotBlank;

/**
 * <AUTHOR>
 * @Date 2021/9/24 10:14
 * @Description
 */
@Data
public class PictureInfo {
    @NotBlank(message = "文件名")
    private String fileName;
    @NotBlank(message = "文件大小")
    private Long fileSize;
    @NotBlank(message = "图片分辨率")
    private String resolution;
    @NotBlank(message = "图片格式")
    private String format;
    private String md5;
}
