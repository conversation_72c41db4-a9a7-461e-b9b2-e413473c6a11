package com.pukka.iptv.common.data.model.copyright;

import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelProperty;
import com.baomidou.mybatisplus.annotation.*;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.pukka.iptv.common.data.converter.cms.CmsProhibitConverter;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import javax.validation.constraints.Max;
import java.io.Serializable;
import java.util.Date;

/**
 * cms违禁片表
 * 
 * <AUTHOR>
 * @email 
 * @date 2022-07-12 18:01:29
 */
@Data
@NoArgsConstructor
@EqualsAndHashCode
@Accessors(chain = true)
@JsonIgnoreProperties(value = { "handler"})
@TableName(value ="cms_prohibit",autoResultMap=true)
public class CmsProhibit extends Model<CmsProhibit> implements Serializable {

	private static final long serialVersionUID = -8231208885711367811L;
	/**
	 * 主键
	 */
	@ExcelIgnore
	@TableId(type = IdType.AUTO)
	@TableField(value = "id")
	@ApiModelProperty(value="ObjectID",dataType="Long",name="id")
	private Long id;
	/**
	 * 全局唯一标识
	 */
	@Max(value = 32, message = "Code不能超过32位")
	@ExcelIgnore
	@TableField(value = "code", fill = FieldFill.INSERT)
	@ApiModelProperty(value = "全局唯一标识", dataType = "String", name = "code")
	private String code;
	/**
	 * 违禁片规则对应标识
	 */
	@ExcelIgnore
	@TableField(value = "rule_code")
	@ApiModelProperty(value="违禁片规则对应标识",dataType="String",name="ruleCode")
	private String ruleCode;
	/**
	 * 0: 单集 1: 剧集
	 */
	@ExcelProperty(value = "节目类型",converter = CmsProhibitConverter.class)
	@TableField(value = "content_type")
	@ApiModelProperty(value = "1：电影\\r\\n2：子集\\r\\n3：电视剧\\r\\n4：系列片\\r\\n5：片花\\r\\n6：直播\\r\\n7：物理频道\\r\\n8：栏目\\r\\n9：产品包\\r\\n10：节目单\\r\\n11：图片\\r\\n12：视频介质\\r\\n13：节目图片\\r\\n14：剧集图片\\r\\n15：产品包图片\\r\\n16：栏目图片\\r\\n17：频道图片\\r\\n18：栏目节目\\r\\n19：栏目剧集\\r\\n20：栏目频道\\r\\n21：产品包节目\\r\\n22：产品包剧集\\r\\n", dataType = "Integer", name = "contentType")
	private Integer contentType;
	/**
	 * 节目名称
	 */
	@ExcelProperty("媒资名称")
	@TableField(value = "name")
	@ApiModelProperty(value = "节目名", dataType = "String", name = "name")
	private String name;
	/**
	 * 原名
	 */
	@Max(value = 128, message = "原名不能超过128位")
	@ExcelIgnore
	@TableField(value = "original_name")
	@ApiModelProperty(value = "原名", dataType = "String", name = "originalName")
	private String originalName;
	/**
	 * 演员列表
	 */
	@ExcelIgnore
	@TableField(value = "actor_display")
	@ApiModelProperty(value = "演员列表", dataType = "String", name = "actorDisplay")
	private String actorDisplay;
	/**
	 * 产地id，来自于sys_dictionary_item表
	 */
	@ExcelIgnore
	@TableField(value = "original_country_id")
	@ApiModelProperty(value = "产地id，来自于sys_dictionary_item表", dataType = "Long", name = "originalCountryId")
	private Long originalCountryId;
	/**
	 * 产地名称，来自于sys_dictionary_item表
	 */
	@ExcelProperty("产地")
	@TableField(value = "original_country")
	@ApiModelProperty(value = "产地名称，来自于sys_dictionary_item表", dataType = "String", name = "originalCountry")
	private String originalCountry;

	/**
	 * 上映年份
	 */
	@ExcelProperty("年份")
	@TableField(value = "release_year")
	@ApiModelProperty(value = "上映年份", dataType = "String", name = "releaseYear")
	private String releaseYear;
	/**
	 * 媒资分类ID，来自sys_dictionary_item表
	 */
	@ExcelIgnore
	@TableField(value = "pgm_category_id")
	@ApiModelProperty(value = "媒资分类ID，来自sys_dictionary_item表", dataType = "Long", name = "pgmCategoryId")
	private Long pgmCategoryId;
	/**
	 * 节目形态，如：新闻，电影
	 */
	@ExcelProperty("媒资分类")
	@TableField(value = "pgm_category")
	@ApiModelProperty(value = "节目形态，如：新闻，电影", dataType = "String", name = "pgmCategory")
	private String pgmCategory;
	/**
	 * 媒资类型ID，来自sys_dictionary_item表
	 */
	@ExcelIgnore
	@TableField(value = "pgm_snd_class_id")
	@ApiModelProperty(value = "媒资类型ID，来自sys_dictionary_item表", dataType = "Long", name = "pgmSndClassId")
	private String pgmSndClassId;
	/**
	 * 二级标签，如：动作，科幻
	 */
	@ExcelIgnore
	@TableField(value = "pgm_snd_class")
	@ApiModelProperty(value = "二级标签，如：动作，科幻", dataType = "String", name = "pgmSndClass")
	private String pgmSndClass;
	/**
	 * 状态标志 0:失效 1:生效
	 */
	@ExcelIgnore
	@TableField(value = "status")
	@ApiModelProperty(value = "状态标志 0:失效 1:生效", dataType = "Integer", name = "status")
	private Integer status;
	/**
	 * 节目类型1: 视频类节目2: 图文类节目
	 */
	@ExcelIgnore
	@TableField(value = "source_type")
	@ApiModelProperty(value = "节目类型1: 视频类节目 2: 图文类节目", dataType = "Integer", name = "sourceType")
	private Integer sourceType;
	/**
	 * 主要人物
	 */
	@ExcelProperty("演员")
	@TableField(value = "kpeople")
	@ApiModelProperty(value = "主要人物", dataType = "String", name = "kpeople")
	private String kpeople;
	/**
	 * 导演
	 */
	@ExcelProperty("导演")
	@TableField(value = "director")
	@ApiModelProperty(value = "导演", dataType = "String", name = "director")
	private String director;
	/**
	 * cpId
	 */
	@ExcelIgnore
	@TableField(value = "cp_id")
	@ApiModelProperty(value = "cpId", dataType = "Long", name = "cpId")
	private Long cpId;
	/**
	 * CP名称
	 */
	@ExcelProperty("所属CP")
	@TableField(value = "cp_name")
	@ApiModelProperty(value = "CP名称", dataType = "String", name = "cpName")
	private String cpName;
	/**
	 * 创建时间
	 */
	@ExcelProperty("添加时间")
	@TableField(value = "create_time", fill = FieldFill.INSERT)
	@ApiModelProperty(value = "创建时间", dataType = "String", name = "createTime")
	@JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
	private Date createTime;
	/**
	 * 更新时间
	 */
	@ExcelIgnore
	@TableField(value = "update_time", fill = FieldFill.INSERT_UPDATE)
	@ApiModelProperty(value = "更新时间", dataType = "String", name = "updateTime")
	@JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
	private Date updateTime;
	/**
	 * 审核状态 1：待审核 2：审核中 3：未通过 4：通过
	 */
	@ExcelIgnore
	@TableField(value = "cp_check_status")
	@ApiModelProperty(value = "审核状态 1：待审核 2：审核中 3：未通过 4：通过", dataType = "Integer", name = "cpCheckStatus")
	private Integer cpCheckStatus;
	/**
	 * 自审描述
	 */
	@ExcelIgnore
	@TableField(value = "cp_check_desc")
	@ApiModelProperty(value = "自审描述", dataType = "String", name = "cpCheckDesc")
	private String cpCheckDesc;
	/**
	 * 审核时间
	 */
	@ExcelIgnore
	@TableField(value = "cp_check_time")
	@ApiModelProperty(value = "审核时间", dataType = "String", name = "cpCheckTime")
	@JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
	private Date cpCheckTime;
	/**
	 * 审核人
	 */
	@ExcelIgnore
	@TableField(value = "cp_checker")
	@ApiModelProperty(value = "审核人", dataType = "String", name = "cpChecker")
	private String cpChecker;
	/**
	 * op审核  1：待审核  2：审核中  3：未通过  4：通过
	 */
	@ExcelIgnore
	@TableField(value = "op_check_status")
	@ApiModelProperty(value = "op审核  1：待审核  2：审核中  3：未通过  4：通过", dataType = "Integer", name = "opCheckStatus")
	private Integer opCheckStatus;
	/**
	 * op审核描述
	 */
	@ExcelIgnore
	@TableField(value = "op_check_desc")
	@ApiModelProperty(value = "op审核描述", dataType = "String", name = "opCheckDesc")
	private String opCheckDesc;
	/**
	 * 终审人员
	 */
	@ExcelIgnore
	@TableField(value = "op_checker")
	@ApiModelProperty(value = "终审人员", dataType = "String", name = "opChecker")
	private String opChecker;
	/**
	 * 终审时间
	 */
	@ExcelIgnore
	@TableField(value = "op_check_time")
	@ApiModelProperty(value = "终审时间", dataType = "String", name = "opCheckTime")
	@JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
	private Date opCheckTime;
	/**
	 * 来源 1：专线注入 2：人工创建
	 */
	@ExcelIgnore
	@TableField(value = "source")
	@ApiModelProperty(value = "来源 1：专线注入 2：人工创建", dataType = "Integer", name = "source")
	private Integer source;
	/**
	 * 创建人，只人工上传时可用
	 */
	@ExcelProperty("添加人员")
	@TableField(value = "creator_name", fill = FieldFill.INSERT)
	@ApiModelProperty(value = "创建人，只人工上传时可用", dataType = "String", name = "creatorName")
	private String creatorName;
	/**
	 * creatorId
	 */
	@ExcelIgnore
	@TableField(value = "creator_id", fill = FieldFill.INSERT)
	@ApiModelProperty(value = "creatorId", dataType = "Long", name = "creatorId")
	private Long creatorId;
	/**
	 * contentId
	 */
	@ExcelIgnore
	@TableField(value = "content_id", fill = FieldFill.INSERT)
	@ApiModelProperty(value = "contentId", dataType = "Long", name = "contentId")
	private Long contentId;
	/**
	 * series_type
	 */
	@ExcelIgnore
	@TableField(value = "series_type", fill = FieldFill.INSERT)
	@ApiModelProperty(value = "seriesType", dataType = "Integer", name = "seriesType")
	private Integer seriesType;
}
