package com.pukka.iptv.common.data.model.order;

import com.pukka.iptv.common.base.constant.ObjectsTypeConstants;
import com.pukka.iptv.common.base.enums.ActionEnums;
import com.pukka.iptv.common.data.model.in.InCategory;
import com.pukka.iptv.common.data.util.SafeUtil;
import lombok.Getter;
import lombok.Setter;

import java.util.HashMap;

/**
 * <AUTHOR>
 * @Date 2021/9/2 9:04 上午
 * @description: 栏目信息表
 * @Version 1.0
 */
@Getter
@Setter
public class CategoryObjectEntity extends SubOrderObjectsEntity {
    /**
     * 父code
     */
    private String parentCode;

    public CategoryObjectEntity(InCategory inCategory) {
        setPropertyDic(new HashMap<>());
        this.parentCode = inCategory.getParentCode();
        this.setAction(ActionEnums.getInfoByCode(inCategory.getAction()));
        this.setCode(inCategory.getCode());
        this.setId(String.valueOf(inCategory.getCode()));
        this.setElementType(ObjectsTypeConstants.CATEGORY);
        getPropertyDic().put("ParentID", SafeUtil.getString(inCategory.getParentCode()));
        getPropertyDic().put("Name",SafeUtil.getString(inCategory.getName()));
        getPropertyDic().put("Sequence",SafeUtil.getString(inCategory.getSequence()));
        getPropertyDic().put("Status",SafeUtil.getString(inCategory.getStatus()));
        getPropertyDic().put("Description",SafeUtil.getString(inCategory.getDescription()));
    }

    public CategoryObjectEntity() {
    }
}
