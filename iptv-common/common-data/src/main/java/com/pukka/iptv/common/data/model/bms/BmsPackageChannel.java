package com.pukka.iptv.common.data.model.bms;

import com.baomidou.mybatisplus.annotation.*;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.NoArgsConstructor;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.util.Date;

/**
 * 包内容
 * 
 * <AUTHOR>
 * @date 2021-09-10 17:08:46
 */
@Data
@EqualsAndHashCode
@JsonIgnoreProperties(value = { "handler" })
@NoArgsConstructor
@TableName(value = "bms_package_channel",autoResultMap=true)
public class BmsPackageChannel extends Model<BmsPackageChannel> implements Serializable{
	private static final long serialVersionUID = 1L;
	/**id*/
	@TableField(value = "id")
	@TableId(type = IdType.AUTO)
	@ApiModelProperty(value="id",dataType="Long",name="id")
	private Long id;
	/**content表ID*/
	@TableField(value = "bms_channel_id")
	@ApiModelProperty(value="content表ID",dataType="Long",name="bmsChannelId")
	private Long bmsChannelId;
	/**content表code*/
	@TableField(value = "cms_channel_code")
	@ApiModelProperty(value="content表code",dataType="String",name="cmsChannelCode")
	private String cmsChannelCode;
	/**媒资名称*/
	@TableField(value = "channel_name")
	@ApiModelProperty(value="媒资名称",dataType="String",name="channelName")
	private String channelName;
	/**所属产品包*/
	@TableField(value = "package_name")
	@ApiModelProperty(value="所属产品包",dataType="String",name="packageName")
	private String packageName;
	/**packageId*/
	@TableField(value = "package_id")
	@ApiModelProperty(value="packageId",dataType="Long",name="packageId")
	private Long packageId;
	/**全局唯一标识*/
	@TableField(value = "package_code")
	@ApiModelProperty(value="全局唯一标识",dataType="String",name="packageCode")
	private String packageCode;
	/**spId*/
	@TableField(value = "sp_id")
	@ApiModelProperty(value="spId",dataType="Long",name="spId")
	private Long spId;
	/**SP名称*/
	@TableField(value = "sp_name")
	@ApiModelProperty(value="SP名称",dataType="String",name="spName")
	private String spName;
	/**状态 1可用  0：停用*/
	@TableField(value = "status")
	@ApiModelProperty(value="状态 1可用  0：停用",dataType="Integer",name="status")
	private Integer status;
	/**创建时间*/
	@TableField(value = "create_time", fill = FieldFill.INSERT)
	@JsonFormat(shape = JsonFormat.Shape.STRING,pattern = "yyyy-MM-dd HH:mm:ss",timezone="GMT+8")
	@ApiModelProperty(value="创建时间",dataType="String",name="createTime")
	private Date createTime;
	/**更新时间*/
	@TableField(value = "update_time", fill = FieldFill.UPDATE)
	@JsonFormat(shape = JsonFormat.Shape.STRING,pattern = "yyyy-MM-dd HH:mm:ss",timezone="GMT+8")
	@ApiModelProperty(value="更新时间",dataType="String",name="updateTime")
	private Date updateTime;
	/**发布状态  1：待发布 2：发布中 3：发布成功 4：发布失败 5：待更新 6：更新中 7：更新失败 8：回收中 9：回收成功 10：回收失败*/
	@TableField(value = "publish_status")
	@ApiModelProperty(value="发布状态  1：待发布 2：发布中 3：发布成功 4：发布失败 5：待更新 6：更新中 7：更新失败 8：回收中 9：回收成功 10：回收失败",dataType="Integer",name="publishStatus")
	private Integer publishStatus;
	/**关系发布通道名称，用逗号分隔*/
	@TableField(value = "out_passage_names")
	@ApiModelProperty(value="关系发布通道名称，用逗号分隔",dataType="String",name="outPassageNames")
	private String outPassageNames;
	/**发布通道ids集合，用逗号分隔*/
	@TableField(value = "out_passage_ids")
	@ApiModelProperty(value="发布通道ids集合，用逗号分隔",dataType="String",name="outPassageIds")
	private String outPassageIds;
	/**来源 1：专线注入 2：人工绑定*/
	@TableField(value = "source")
	@ApiModelProperty(value="来源 1：专线注入 2：人工绑定",dataType="Integer",name="source")
	private Integer source;
	/**关系发布时间*/
	@TableField(value = "publish_time")
	@ApiModelProperty(value="关系发布时间",dataType="String",name="publishTime")
	@JsonFormat(shape = JsonFormat.Shape.STRING,pattern = "yyyy-MM-dd HH:mm:ss",timezone="GMT+8")
	private Date publishTime;
	/**关系发布描述*/
	@TableField(value = "publish_description")
	@ApiModelProperty(value="关系发布描述",dataType="String",name="publishDescription")
	private String publishDescription;
	/**绑定人员*/
	@TableField(value = "creator_name", fill = FieldFill.INSERT)
	@ApiModelProperty(value="绑定人员",dataType="String",name="creatorName")
	private String creatorName;
	/**creatorId*/
	@TableField(value = "creator_id", fill = FieldFill.INSERT)
	@ApiModelProperty(value="creatorId",dataType="Long",name="creatorId")
	private Long creatorId;
	/**媒资类型*/
	@TableField(value = "content_type")
	@ApiModelProperty(value="媒资类型",dataType="Integer",name="contentType")
	private Integer contentType;

}
