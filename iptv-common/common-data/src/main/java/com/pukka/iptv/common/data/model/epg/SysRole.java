package com.pukka.iptv.common.data.model.epg;

import com.baomidou.mybatisplus.annotation.*;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;
import org.springframework.util.Assert;

import java.io.Serializable;
import java.util.Date;

/**
 * 角色表
 * 
 * <AUTHOR>
 * @email 
 * @date 2023-03-20 09:43:51
 */
@Data
@EqualsAndHashCode
@JsonIgnoreProperties(value = { "handler" })
@NoArgsConstructor
@TableName(value = "sys_role",autoResultMap=true)
@Accessors(chain = true)
public class SysRole extends Model<SysRole> implements Serializable {

	private static final long serialVersionUID = 7719762195852734494L;
	/**
	 * ID
	 */
	@TableId(type = IdType.AUTO)
	@TableField(value = "id")
	@ApiModelProperty(value="ID",dataType="Long",name="id")
	private Long id;
	/**名称*/
	@TableField(value = "name")
	@ApiModelProperty(value="名称",dataType="String",name="name")
	private String name;
	/**类型 1：系统 2：租户*/
	@TableField(value = "type")
	@ApiModelProperty(value="类型 1：系统 2：租户",dataType="Integer",name="type")
	private Integer type;
	/**状态 1：有效 2：无效*/
	@TableField(value = "status")
	@ApiModelProperty(value="状态 1：有效 2：无效",dataType="Integer",name="status")
	private Integer status;
	/**作者ID*/
	@TableField(value = "creator_id", fill = FieldFill.INSERT)
	@ApiModelProperty(value="作者ID",dataType="Long",name="creatorId")
	private Long creatorId;
	/**作者*/
	@TableField(value = "creator_name", fill = FieldFill.INSERT)
	@ApiModelProperty(value="作者",dataType="String",name="creatorName")
	private String creatorName;
	/**创建时间*/
	@TableField(value = "create_time", fill = FieldFill.INSERT)
	@ApiModelProperty(value="创建时间",dataType="String",name="createTime")
	@JsonFormat(shape = JsonFormat.Shape.STRING,pattern = "yyyy-MM-dd HH:mm:ss",timezone="GMT+8")
	private Date createTime;
	/**备注*/
	@TableField(value = "description")
	@ApiModelProperty(value="描述",dataType="String",name="description")
	private String description;
	/**更新时间*/
	@TableField(value = "update_time")
	@ApiModelProperty(value="更新时间",dataType="String",name="updateTime")
	@JsonFormat(shape = JsonFormat.Shape.STRING,pattern = "yyyy-MM-dd HH:mm:ss",timezone="GMT+8")
	private Date updateTime;

	public void valid(){
		Assert.notNull(name, "名称不能为空");
	}


	public void validStatus() {
		Assert.notNull(id, "ID不能为空");
		Assert.notNull(status, "状态不能为空");
	}

	public void validCpoy() {
		Assert.notNull(id, "ID不能为空");
		Assert.notNull(name, "名称不能为空");
	}
}
