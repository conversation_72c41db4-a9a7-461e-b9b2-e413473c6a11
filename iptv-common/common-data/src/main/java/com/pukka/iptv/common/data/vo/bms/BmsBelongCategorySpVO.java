package com.pukka.iptv.common.data.vo.bms;

import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 * @Date 2021-11-15 11:52
 * @Description sp列表
 */
@Getter
@Setter
@Accessors(chain = true)
public class BmsBelongCategorySpVO implements Serializable {
    /**
     * spId
     */
    private Long id;
    /**
     * sp名称
     */
    private String name;
    // 所属栏目树
    private List<BmsBelongCategoryVO> children;

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (!(o instanceof BmsBelongCategorySpVO)) return false;
        BmsBelongCategorySpVO that = (BmsBelongCategorySpVO) o;
        return Objects.equals(id, that.id) && Objects.equals(children, that.children);
    }

    @Override
    public int hashCode() {
        return Objects.hash(id, name, children);
    }
}
