package com.pukka.iptv.common.data.params;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;

/**
 * TODO 操作员修改参数
 *
 * <AUTHOR> @date 2021/12/1 15:12
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@JsonIgnoreProperties(value = { "handler" })
@ApiModel("用户状态参数")
public class SysUserStateParam implements Serializable {

    @ApiModelProperty(value="用户id",dataType="List",name="ids")
    private List<Long> ids;

    @ApiModelProperty(value = "状态",dataType="Integer",example = "0",name = "state")
    private Integer state;


}
