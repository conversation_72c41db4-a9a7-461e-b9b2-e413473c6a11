package com.pukka.iptv.common.data.model.sys;

import com.baomidou.mybatisplus.annotation.*;
import io.swagger.annotations.ApiModelProperty;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.NoArgsConstructor;
import lombok.EqualsAndHashCode;
import java.util.Date;
import lombok.Data;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.experimental.Accessors;
import org.springframework.util.Assert;

/**
 *
 * @author: zhengcl
 * @date: 2021-8-17 18:55:46
 */

@Data
@EqualsAndHashCode
@JsonIgnoreProperties(value = { "handler" })
@NoArgsConstructor
@TableName(value = "sys_role",autoResultMap=true)
@Accessors(chain = true)
public class SysRole extends Model<SysRole> implements java.io.Serializable{

    private static final long serialVersionUID = 1L;
	/**ID*/
    @TableId(type = IdType.AUTO)
	@TableField(value = "id")
    @ApiModelProperty(value="ID",dataType="Long",name="id")
    private Long id;
	/**名称*/
	@TableField(value = "name")
    @ApiModelProperty(value="名称",dataType="String",name="name")
    private String name;
	/**类型 1：系统 2：租户*/
	@TableField(value = "type")
    @ApiModelProperty(value="类型 1：系统 2：租户",dataType="Integer",name="type")
    private Integer type;
	/**状态 1：有效 2：无效*/
	@TableField(value = "status")
    @ApiModelProperty(value="状态 1：有效 2：无效",dataType="Integer",name="status")
    private Integer status;
	/**作者ID*/
	@TableField(value = "creator_id", fill = FieldFill.INSERT)
    @ApiModelProperty(value="作者ID",dataType="Long",name="creatorId")
    private Long creatorId;
	/**作者*/
	@TableField(value = "creator_name", fill = FieldFill.INSERT)
    @ApiModelProperty(value="作者",dataType="String",name="creatorName")
    private String creatorName;
	/**创建时间*/
	@TableField(value = "create_time", fill = FieldFill.INSERT)
    @ApiModelProperty(value="创建时间",dataType="String",name="createTime")
    @JsonFormat(shape = JsonFormat.Shape.STRING,pattern = "yyyy-MM-dd HH:mm:ss",timezone="GMT+8")
    private Date createTime;
	/**备注*/
	@TableField(value = "description")
    @ApiModelProperty(value="描述",dataType="String",name="description")
    private String description;

    public void valid(){
        Assert.notNull(name, "名称不能为空");
    }


    public void validStatus() {
        Assert.notNull(id, "ID不能为空");
        Assert.notNull(status, "状态不能为空");
    }

    public void validCpoy() {
        Assert.notNull(id, "ID不能为空");
        Assert.notNull(name, "名称不能为空");
    }
}
