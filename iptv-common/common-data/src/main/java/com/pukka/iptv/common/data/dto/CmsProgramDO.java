package com.pukka.iptv.common.data.dto;

import com.pukka.iptv.common.data.model.cms.CmsProgram;
import lombok.Data;

import java.io.Serializable;

/**
 * @ClassName CmsProgramDO
 * @Description TODO
 * <AUTHOR>
 * @Date 2022/3/23 11:02
 * @Version
 */
@Data
public class CmsProgramDO extends CmsProgram implements Serializable {

    /**
     *  授权 (0：未授权 1：已授权)
     */
    private Integer grant;
    /**
     * 当包含换行符时，是多名称查询
     */
    private String[] nameList;

    private String startTime;

    private String endTime;
    /**
     * 模糊查询
     */
    private String nameLike;

    /**
     * 模糊精确查询标志
     */
    private Integer likeOrinFlag;

    private Integer current;

    private Integer size;
    /**
     * 1:内容管理页面
     * 2:审核页面
     */
    private Integer pageType;

}
