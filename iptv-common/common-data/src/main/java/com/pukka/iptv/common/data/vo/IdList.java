package com.pukka.iptv.common.data.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2021/8/8
 */
@Data
@EqualsAndHashCode
@ApiModel("编号列表")
public class IdList implements java.io.Serializable{
    private static final long serialVersionUID = -216438158117476909L;

    /**
     * 主键ID列表(也可能是关系表的主键ID)
     */
    @ApiModelProperty(value="编号列表",dataType="List",name="ids",example="",required = true)
    private List<Long> ids = new ArrayList<>();

    private String code;
}
