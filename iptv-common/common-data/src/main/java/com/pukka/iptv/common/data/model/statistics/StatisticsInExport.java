package com.pukka.iptv.common.data.model.statistics;

import com.alibaba.excel.annotation.ExcelProperty;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.apache.poi.hpsf.Decimal;

import java.math.BigDecimal;

/**
 *
 * @author: tan
 * @date: 2022-7-14 11:02:04
 */

@Data
@EqualsAndHashCode
@JsonIgnoreProperties(value = { "handler" })
@TableName(value = "statistics_in",autoResultMap=true)
public class StatisticsInExport extends Model<StatisticsInExport> implements java.io.Serializable{

    private static final long serialVersionUID = 1L;

    @ExcelProperty(value = "排名", order = 1)
    private Integer rankId;

	/**数量*/
	@TableField(value = "count")
    @ApiModelProperty(value="数量",dataType="Integer",name="count")
    @ExcelProperty(value = "注入量", order = 3)
    private Integer count;
	/**duration*/
	@TableField(value = "duration")
    @ApiModelProperty(value="duration",dataType="Long",name="duration")
//    @ExcelProperty(value = "时长(H)",order = 4)
    private Long duration;
	/**cpId*/

	/**cpName*/
	@TableField(value = "cp_name")
    @ApiModelProperty(value="cpName",dataType="String",name="cpName")
    @ExcelProperty(value ="CP名称", order = 2)
    private String cpName;

    @TableField(exist = false)
    @ExcelProperty(value = "时长(H)",order = 4)
	private String durationD;

}
