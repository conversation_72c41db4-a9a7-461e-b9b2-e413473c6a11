package com.pukka.iptv.common.data.model.bms;

import com.baomidou.mybatisplus.annotation.*;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.NoArgsConstructor;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.util.Date;

/**
 * 物理频道
 *
 * <AUTHOR>
 * @email
 * @date 2021-08-27 14:45:33
 */
@Data
@EqualsAndHashCode
@JsonIgnoreProperties(value = {"handler"})
@TableName(value = "bms_physical_channel", autoResultMap = true)
public class BmsPhysicalChannel extends Model<BmsPhysicalChannel> implements Serializable
{
    private static final long serialVersionUID = 1L;
    /**
     * id
     */
    @TableId(type = IdType.AUTO)
    @TableField(value = "id")
    @ApiModelProperty(value = "id", dataType = "Long", name = "id")
    private Long id;
    /**
     * 全局唯一标识
     */
    @TableField(value = "code", fill = FieldFill.INSERT)
    @ApiModelProperty(value = "全局唯一标识", dataType = "String", name = "code")
    private String code;
    /**
     * 频道 Code
     */
    @TableField(value = "cms_channel_code")
    @ApiModelProperty(value = "频道 Code", dataType = "String", name = "cmsChannelCode")
    private String cmsChannelCode;
    /**
     * 所属频道
     */
    @TableField(value = "channel_name")
    @ApiModelProperty(value = "所属频道", dataType = "String", name = "channelName")
    private String channelName;
    /**
     * 频道 ID
     */
    @TableField(value = "cms_channel_id")
    @ApiModelProperty(value = "频道 ID", dataType = "Long", name = "cmsChannelId")
    private Long cmsChannelId;
    /**
     * 组播 IP
     */
    @TableField(value = "multi_cast_ip")
    @ApiModelProperty(value = "组播 IP", dataType = "String", name = "multiCastIp")
    private String multiCastIp;
    /**
     * 组播端口
     */
    @TableField(value = "multi_cast_port")
    @ApiModelProperty(value = "组播端口", dataType = "String", name = "multiCastPort")
    private String multiCastPort;
    /**
     * cpId
     */
    @TableField(value = "cp_id")
    @ApiModelProperty(value = "cpId", dataType = "Long", name = "cpId")
    private Long cpId;
    /**
     * CP名称
     */
    @TableField(value = "cp_name")
    @ApiModelProperty(value = "CP名称", dataType = "String", name = "cpName")
    private String cpName;
    /**
     * 创建时间
     */
    @TableField(value = "create_time", fill = FieldFill.INSERT)
    @ApiModelProperty(value = "创建时间", dataType = "String", name = "createTime")
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date createTime;
    /**
     * 更新时间
     */
    @TableField(value = "update_time", fill = FieldFill.UPDATE)
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @ApiModelProperty(value = "更新时间", dataType = "String", name = "updateTime")
    private Date updateTime;
    /**
     * spId
     */
    @TableField(value = "sp_id")
    @ApiModelProperty(value = "spId", dataType = "Long", name = "spId")
    private Long spId;
    /**
     * SP名称
     */
    @TableField(value = "sp_name")
    @ApiModelProperty(value = "SP名称", dataType = "String", name = "spName")
    private String spName;
    /**
     * 发布通道ID，多个ID以英文逗号隔开
     */
    @TableField(value = "out_passage_ids")
    @ApiModelProperty(value = "发布通道ID，多个ID以英文逗号隔开", dataType = "String", name = "outPassageIds")
    private String outPassageIds;
    /**
     * 分发通道名称以英文逗号 隔开
     */
    @TableField(value = "out_passage_names")
    @ApiModelProperty(value = "分发通道名称以英文逗号 隔开", dataType = "String", name = "outPassageNames")
    private String outPassageNames;
    /**
     * 发布状态  1：待发布 2：发布中 3：发布成功 4：发布失败 5：待更新 6：更新中 7：更新失败 8：回收中 9：回收成功 10：回收失败
     */
    @TableField(value = "publish_status")
    @ApiModelProperty(value = "发布状态  1：待发布 2：发布中 3：发布成功 4：发布失败 5：待更新 6：更新中 7：更新失败 8：回收中 9：回收成功 10：回收失败", dataType = "Integer", name = "publishStatus")
    private Integer publishStatus;
    /**
     * 发布时间
     */
    @TableField(value = "publish_time")
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @ApiModelProperty(value = "发布时间", dataType = "String", name = "publishTime")
    private Date publishTime;
    /**
     * 发布描述
     */
    @TableField(value = "publish_description")
    @ApiModelProperty(value = "发布描述", dataType = "String", name = "publishDescription")
    private String publishDescription;
    /**
     * 来源
     */
    @TableField(value = "source")
    @ApiModelProperty(value = "来源", dataType = "Integer", name = "source")
    private Integer source;
    /**
     * 媒资类型
     */
    @TableField(value = "content_type")
    @ApiModelProperty(value = "媒资类型", dataType = "Integer", name = "contentType")
    private Integer contentType;
    /**
     * 频道 Code
     */
    @TableField(value = "cms_physical_channel_code")
    @ApiModelProperty(value = "频道 Code", dataType = "String", name = "cmsPhysicalChannelCode")
    private String cmsPhysicalChannelCode;
    /**
     * 频道 ID
     */
    @TableField(value = "cms_physical_channel_id")
    @ApiModelProperty(value = "频道 ID", dataType = "Long", name = "cmsPhysicalChannelId")
    private Long cmsPhysicalChannelId;

    /**
     * 媒体码率描述符
     */
    @TableField(value = "bit_rate_type")
    @ApiModelProperty(value = "媒体码率描述符", dataType = "Long", name = "bitRateType")
    private Integer bitRateType;
    /**
     * 媒体码率描述符
     */
    @TableField(value = "media_spec")
    @ApiModelProperty(value = "媒体格式描述符", dataType = "String", name = "mediaSpec")
    private String mediaSpec;
}
