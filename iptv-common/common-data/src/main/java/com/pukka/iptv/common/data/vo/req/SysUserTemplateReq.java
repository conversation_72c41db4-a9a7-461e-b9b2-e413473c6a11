package com.pukka.iptv.common.data.vo.req;

import com.pukka.iptv.common.data.model.epg.SysUserTemplate;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class SysUserTemplateReq extends SysUserTemplate implements Serializable {
    private List<Long> userIds = new ArrayList<>();
}
