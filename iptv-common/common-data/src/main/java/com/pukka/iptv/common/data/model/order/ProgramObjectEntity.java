package com.pukka.iptv.common.data.model.order;

import com.pukka.iptv.common.base.constant.ObjectsTypeConstants;
import com.pukka.iptv.common.base.enums.ActionEnums;
import com.pukka.iptv.common.base.enums.DefinitionEnums;
import com.pukka.iptv.common.data.model.in.InProgram;
import com.pukka.iptv.common.data.util.SafeUtil;
import lombok.Getter;
import lombok.Setter;

import java.util.HashMap;

/**
 * <AUTHOR>
 * @description: 单集信息表
 * @create 2021-09-02 11:08
 */
@Getter
@Setter
public class ProgramObjectEntity extends SubOrderObjectsEntity {

    public ProgramObjectEntity(InProgram inProgram) {
        setPropertyDic(new HashMap<>());
        this.setAction(ActionEnums.getInfoByCode(inProgram.getAction()));
        this.setCode(inProgram.getCode());
        this.setElementType(ObjectsTypeConstants.PROGRAM);
        this.setId(inProgram.getCode());
        getPropertyDic().put("Name", inProgram.getName());
        getPropertyDic().put("OrderNumber", inProgram.getOrderNumber());
        getPropertyDic().put("OriginalName", inProgram.getOriginalName());
        getPropertyDic().put("SortName", inProgram.getSortName());
        getPropertyDic().put("SearchName", inProgram.getSearchName());
        getPropertyDic().put("ActorDisplay", inProgram.getKpeople());
        getPropertyDic().put("WriterDisplay", inProgram.getWriterDisplay());
        getPropertyDic().put("OriginalCountry", inProgram.getOriginalCountry());
        getPropertyDic().put("Language", inProgram.getLanguage());
        getPropertyDic().put("ReleaseYear", inProgram.getReleaseYear());
        getPropertyDic().put("OrgAirDate", inProgram.getOrgAirDate());
        getPropertyDic().put("LicensingWindowStart", inProgram.getLicensingWindowStart());
        getPropertyDic().put("LicensingWindowEnd", inProgram.getLicensingWindowEnd());
        getPropertyDic().put("DisplayAsNew", SafeUtil.getString(inProgram.getDisplayAsNew()));
        getPropertyDic().put("DisplayAsLastChance", SafeUtil.getString(inProgram.getDisplayAsLastChance()));
        getPropertyDic().put("Macrovision", SafeUtil.getString(inProgram.getMacrovision()));
        getPropertyDic().put("Description", inProgram.getDescription());
        getPropertyDic().put("PgmCategory", inProgram.getPgmCategory());
        getPropertyDic().put("PgmSndClass", inProgram.getPgmSndClass());
        getPropertyDic().put("PriceTaxIn", SafeUtil.getPrice(inProgram.getPriceTaxIn()));
        getPropertyDic().put("Status", SafeUtil.getString(inProgram.getStatus()));
        getPropertyDic().put("SourceType", SafeUtil.getString(inProgram.getSourceType()));
        getPropertyDic().put("SeriesFlag", SafeUtil.getString(inProgram.getSeriesFlag()));
        getPropertyDic().put("Kpeople", inProgram.getKpeople());
        getPropertyDic().put("Director", inProgram.getDirector());
        getPropertyDic().put("ScriptWriter", inProgram.getScriptWriter());
        getPropertyDic().put("Compere", inProgram.getCompere());
        getPropertyDic().put("Guest", inProgram.getGuest());
        getPropertyDic().put("Reporter", inProgram.getReporter());
        getPropertyDic().put("OPIncharge", inProgram.getOpIncharge());
        getPropertyDic().put("VSPCode", inProgram.getVspCode());
        getPropertyDic().put("CopyRight", inProgram.getCopyRight());
        getPropertyDic().put("ContentProvider", inProgram.getContentProvider());
        getPropertyDic().put("Duration", SafeUtil.getString(inProgram.getDuration()));
        getPropertyDic().put("Rating", inProgram.getRating());
        getPropertyDic().put("NewPrice", inProgram.getNewPrice());
        getPropertyDic().put("DefinitionFlag", SafeUtil.getString(DefinitionEnums.getDefinitionflag(inProgram.getDefinitionFlag())));
        //getPropertyDic().put("DefinitionFlag", SafeUtil.getString(inProgram.getDefinitionFlag()));
        getPropertyDic().put("Type", SafeUtil.getString(inProgram.getType()));
        getPropertyDic().put("Tags", SafeUtil.getString(inProgram.getTags()));
    }

    public ProgramObjectEntity() {
    }
}
