package com.pukka.iptv.common.data.dto;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.util.List;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class AutoPublishFeedackDto {

    private String correlateId;

    private String spIds;

    /** 遵从央视规范枚举定义，进行扩展
     * 0：成功（运营商处理结果）
     * -1：失败（运营商处理结果）
     * 1：自动发布调用分发组件成功
     * 2：自动发布调用分发组件失败
     */
    private Integer result;
    private boolean isFinish;
}
