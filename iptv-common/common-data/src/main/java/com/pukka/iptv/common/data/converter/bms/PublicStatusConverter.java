package com.pukka.iptv.common.data.converter.bms;

import com.alibaba.excel.converters.Converter;
import com.alibaba.excel.enums.CellDataTypeEnum;
import com.alibaba.excel.metadata.GlobalConfiguration;
import com.alibaba.excel.metadata.data.WriteCellData;
import com.alibaba.excel.metadata.property.ExcelContentProperty;
import org.apache.commons.lang3.StringUtils;

import java.util.HashMap;
import java.util.Map;

/**
 * @Author: wangbo
 * @Date: 2022/4/19 9:57
 */
public class PublicStatusConverter implements Converter<Integer> {

    private static final Map<Integer, String> publishMap = new HashMap<>();

    static {
        publishMap.put(1, "待发布");
        publishMap.put(2, "发布中");
        publishMap.put(3, "发布成功");
        publishMap.put(4, "发布失败");
        publishMap.put(5, "待更新");
        publishMap.put(6, "更新中");
        publishMap.put(7, "更新失败");
        publishMap.put(8, "回收中");
        publishMap.put(9, "回收成功");
        publishMap.put(10, "回收失败");

    }

    @Override
    public Class supportJavaTypeKey() {
        return Integer.class;
    }

    @Override
    public CellDataTypeEnum supportExcelTypeKey() {
        return CellDataTypeEnum.STRING;
    }


    /**
     * 这里是写的时候会调用
     */
    @Override
    public WriteCellData<?> convertToExcelData(Integer value, ExcelContentProperty contentProperty, GlobalConfiguration globalConfiguration) throws Exception {
        return new WriteCellData(getPublishStatus(value));
    }

    private String getPublishStatus(Integer value) {
        String result = publishMap.get(value);
        result = StringUtils.isEmpty(result) ? "" : result;
        return result;
    }
}
