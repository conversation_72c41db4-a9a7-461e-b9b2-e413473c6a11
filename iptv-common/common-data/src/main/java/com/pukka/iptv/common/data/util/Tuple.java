package com.pukka.iptv.common.data.util;

import java.util.Optional;
/**
 * @PACKAGE_NAME: Utility.Tooles.Tuples
 * @NAME: Tuple4 四元数组
 * @USER: wangbo
 * @DATE: 2021/9/06
 **/
public abstract class Tuple
{
    //<A>：声明此方法为泛型方法，该方法持有一个类型A
    // 这里使用Optional是为了提醒使用返回值的方法做非Null检查
    public abstract <A> Optional<A> getA();

    public abstract <B> Optional<B> getB();

    public abstract <C> Optional<C> getC();

    public abstract <D> Optional<D> getD();

    public static <A, B>  Tuple of(A a, B b) {
        return new  Tuple2(a, b);
    }

    public static <A, B, C>  Tuple3 of(A a, B b, C c) {
        return new  Tuple3(a, b, c);
    }

    public static <A, B, C, D>  Tuple of(A a, B b, C c, D d) {
        return new  Tuple4(a, b, c, d);
    }


}
