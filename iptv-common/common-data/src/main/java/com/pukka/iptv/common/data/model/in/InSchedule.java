package com.pukka.iptv.common.data.model.in;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.NoArgsConstructor;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Date;

/**
 *
 * @author: liuli
 * @date: 2021年8月31日 下午2:39:47
 */

@Data
@EqualsAndHashCode
@JsonIgnoreProperties(value = { "handler" })
@NoArgsConstructor
@TableName(value = "in_schedule",autoResultMap=true)
public class InSchedule extends Model<InSchedule> implements java.io.Serializable{

    private static final long serialVersionUID = 1L;
	/**主键*/
	@TableField(value = "id")
    @TableId(type = IdType.AUTO)
    @ApiModelProperty(value="主键",dataType="Long",name="id")
    private Long id;
	/**全局唯一标识*/
	@TableField(value = "code")
    @ApiModelProperty(value="全局唯一标识",dataType="String",name="code")
    private String code;
	/**逻辑频道 Code*/
	@TableField(value = "channel_code")
    @ApiModelProperty(value="逻辑频道 Code",dataType="String",name="channelCode")
    private String channelCode;
	/**频道名称*/
	@TableField(value = "channel_name")
    @ApiModelProperty(value="频道名称",dataType="String",name="channelName")
    private String channelName;
	/**逻辑频道 ID*/
	@TableField(value = "channel_id")
    @ApiModelProperty(value="逻辑频道 ID",dataType="Long",name="channelId")
    private Long channelId;
	/**节目名称*/
	@TableField(value = "program_name")
    @ApiModelProperty(value="节目名称",dataType="String",name="programName")
    private String programName;
	/**节目开播日期*/
	@TableField(value = "start_date")
    @ApiModelProperty(value="节目开播日期",dataType="String",name="startDate")
    private String startDate;
	/**节目开播时间*/
	@TableField(value = "start_time")
    @ApiModelProperty(value="节目开播时间",dataType="String",name="startTime")
    private String startTime;
	/**节目时长(HH24MISS) */
	@TableField(value = "duration")
    @ApiModelProperty(value="节目时长(HH24MISS) ",dataType="String",name="duration")
    private String duration;
	/**TVOD 保存时长(小时) 缺省为空*/
	@TableField(value = "storage_duration")
    @ApiModelProperty(value="TVOD 保存时长(小时) 缺省为空",dataType="Integer",name="storageDuration")
    private Integer storageDuration;
	/**状态标志 1:生效 2:失效 */
	@TableField(value = "status")
    @ApiModelProperty(value="状态标志 1:生效 2:失效 ",dataType="Integer",name="status")
    private Integer status;
	/**描述信息*/
	@TableField(value = "description")
    @ApiModelProperty(value="描述信息",dataType="String",name="description")
    private String description;
	/**来源 1：专线注入 2：人工创建*/
	@TableField(value = "source")
    @ApiModelProperty(value="来源 1：专线注入 2：人工创建",dataType="Integer",name="source")
    private Integer source;
	/**节目的分类标签，如“体育”， 多个标签用空格或“;”区分*/
	@TableField(value = "genre")
    @ApiModelProperty(value="节目的分类标签，如“体育”， 多个标签用空格或“;”区分",dataType="String",name="genre")
    private String genre;
	/**cpId*/
	@TableField(value = "cp_id")
    @ApiModelProperty(value="cpId",dataType="Long",name="cpId")
    private Long cpId;
	/**CP名称*/
	@TableField(value = "cp_name")
    @ApiModelProperty(value="CP名称",dataType="String",name="cpName")
    private String cpName;
	/**创建时间*/
	@TableField(value = "create_time")
    @ApiModelProperty(value="创建时间",dataType="String",name="createTime")
    @JsonFormat(shape = JsonFormat.Shape.STRING,pattern = "yyyy-MM-dd HH:mm:ss",timezone="GMT+8")
    private Date createTime;
	/**更新时间*/
	@TableField(value = "update_time")
    @ApiModelProperty(value="更新时间",dataType="String",name="updateTime")
    @JsonFormat(shape = JsonFormat.Shape.STRING,pattern = "yyyy-MM-dd HH:mm:ss",timezone="GMT+8")
    private Date updateTime;
	/**创建人，只人工上传时可用*/
	@TableField(value = "creator_name")
    @ApiModelProperty(value="创建人，只人工上传时可用",dataType="String",name="creatorName")
    private String creatorName;
	/**创建人ID来自于sys_user*/
	@TableField(value = "creator_id")
    @ApiModelProperty(value="创建人ID来自于sys_user",dataType="Long",name="creatorId")
    private Long creatorId;
	/**动作 1：REGIST，2：UPDATE，3：DELETE*/
	@TableField(value = "action")
    @ApiModelProperty(value="动作 1：REGIST，2：UPDATE，3：DELETE",dataType="Integer",name="action")
    private Integer action;
	/**应答文件包含，0: 成功 其他: 错误代码*/
	@TableField(value = "result")
    @ApiModelProperty(value="应答文件包含，0: 成功 其他: 错误代码",dataType="Integer",name="result")
    private Integer result;
	/**应答文件包含，错误描述*/
	@TableField(value = "error_description")
    @ApiModelProperty(value="应答文件包含，错误描述",dataType="String",name="errorDescription")
    private String errorDescription;
	/**主公单ID*/
	@TableField(value = "correlate_id")
    @ApiModelProperty(value="主公单ID",dataType="String",name="correlateId")
    private String correlateId;
}
