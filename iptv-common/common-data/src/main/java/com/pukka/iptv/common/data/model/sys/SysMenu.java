package com.pukka.iptv.common.data.model.sys;

import com.baomidou.mybatisplus.annotation.*;
import com.pukka.iptv.common.data.vo.sys.SysMenuVo;
import io.swagger.annotations.ApiModelProperty;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.NoArgsConstructor;
import lombok.EqualsAndHashCode;
import java.util.Date;
import lombok.Data;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;
import org.springframework.util.Assert;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 *
 * @author: zhengcl
 * @date: 2021-8-17 18:55:46
 */

@Data
@EqualsAndHashCode
@JsonIgnoreProperties(value = { "handler" })
@NoArgsConstructor
@TableName(value = "sys_menu",autoResultMap=true)
@Accessors(chain = true)
public class SysMenu extends Model<SysMenu> implements java.io.Serializable{

    private static final long serialVersionUID = 1L;
	/**ID*/
    @TableId(type = IdType.AUTO)
	@TableField(value = "id")
    @ApiModelProperty(value="ID",dataType="Long",name="id")
    private Long id;
	/**元数据信息*/
	@TableField(value = "meta")
    @ApiModelProperty(value="元数据信息",dataType="String",name="meta")
    private String meta;
	/**重定向地址*/
	@TableField(value = "redirect")
    @ApiModelProperty(value="重定向地址",dataType="String",name="redirect")
    private String redirect;
	/**前端转换地址*/
	@TableField(value = "component")
    @ApiModelProperty(value="前端转换地址",dataType="String",name="component")
    private String component;
	/**名称*/
	@TableField(value = "name")
    @ApiModelProperty(value="名称",dataType="String",name="name")
    private String name;
	/**父菜单ID*/
	@TableField(value = "parent_id")
    @ApiModelProperty(value="父菜单ID",dataType="Long",name="parentId")
    private Long parentId;
	/**页面编码*/
	@TableField(value = "group")
    @ApiModelProperty(value="分组",dataType="String",name="group")
    private String group;
	/**权限*/
	@TableField(value = "permission")
    @ApiModelProperty(value="权限",dataType="String",name="permission")
    private String permission;
	/**路径*/
	@TableField(value = "path")
    @ApiModelProperty(value="路径",dataType="String",name="path")
    private String path;
	/**图标*/
	@TableField(value = "icon")
    @ApiModelProperty(value="图标",dataType="String",name="icon")
    private String icon;
	/**序号*/
	@TableField(value = "sequence")
    @ApiModelProperty(value="序号",dataType="Long",name="sequence")
    private Long sequence;
	/**类型 1：导航 2：菜单 3：工具栏按钮 4：操作列按钮*/
	@TableField(value = "type")
    @ApiModelProperty(value="类型 1：导航 2：菜单 3：工具栏按钮 4：操作列按钮",dataType="Integer",name="type")
    private Integer type;
	/**状态 1：有效 2：无效*/
	@TableField(value = "status")
    @ApiModelProperty(value="状态 1：有效 2：无效",dataType="Integer",name="status")
    private Integer status;
	/**租户ID*/
	@TableField(value = "tenant_id")
    @ApiModelProperty(value="租户ID",dataType="Long",name="tenantId")
    private Long tenantId;
	/**租户*/
	@TableField(value = "tenant_name")
    @ApiModelProperty(value="租户",dataType="String",name="tenantName")
    private String tenantName;
	/**作者ID*/
	@TableField(value = "creator_id", fill = FieldFill.INSERT)
    @ApiModelProperty(value="作者ID",dataType="Long",name="creatorId")
    private Long creatorId;
	/**作者*/
	@TableField(value = "creator_name", fill = FieldFill.INSERT)
    @ApiModelProperty(value="作者",dataType="String",name="creatorName")
    private String creatorName;
	/**创建时间*/
	@TableField(value = "create_time", fill = FieldFill.INSERT)
    @ApiModelProperty(value="创建时间",dataType="String",name="createTime")
    @JsonFormat(shape = JsonFormat.Shape.STRING,pattern = "yyyy-MM-dd HH:mm:ss",timezone="GMT+8")
    private Date createTime;
	/**更新时间*/
	@TableField(value = "update_time")
    @ApiModelProperty(value="更新时间",dataType="String",name="updateTime")
    @JsonFormat(shape = JsonFormat.Shape.STRING,pattern = "yyyy-MM-dd HH:mm:ss",timezone="GMT+8")
    private Date updateTime;
	/**备注*/
	@TableField(value = "description")
    @ApiModelProperty(value="描述",dataType="String",name="description")
    private String description;
	/**是否选中*/
    @TableField(exist = false)
    @ApiModelProperty(value="是否选中",dataType="Integer",name="checked")
    private Integer checked;
    /**角色菜单ID*/
    @TableField(exist = false)
    @ApiModelProperty(value="角色菜单ID",dataType="Long",name="roleMenuId")
    private Long roleMenuId;

    public void valid(){
        Assert.notNull(name, "名称不能为空");
        Assert.notNull(permission, "权限不能为空");
        Assert.notNull(path, "地址不能为空");
        Assert.notNull(group, "分组不能为空");
        Assert.notNull(parentId, "父菜单不能为空");
        Assert.notNull(type, "类型不能为空");
        Assert.notNull(icon, "图标不能为空");
    }

}
