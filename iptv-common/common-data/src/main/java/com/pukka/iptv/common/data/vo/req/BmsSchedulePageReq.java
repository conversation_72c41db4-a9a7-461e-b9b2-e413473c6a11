package com.pukka.iptv.common.data.vo.req;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.Getter;
import lombok.Setter;

import javax.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2021/9/23
 */
@Getter
@Setter
public class BmsSchedulePageReq extends Page implements Serializable
{
    /**
     *频道名称
     */
    private String name;
    /**
     *频道发布状态
     */
    private Integer publishStatus;
    @NotNull
    private Long spId;
    /**
     *状态标志
     * 0:失效 1:生效
     */
    private Integer status;
    /**
     *节目单所属频道id
     */
    @NotNull
    private Long channelId;

    /**
     * 开播时间开始时间
     *
     */
    private String startDate;

    /**
     * 开播时间结束时间
     */
    private String endDate;

    /**
     * cpId
     */
    private Long cpId;
}
