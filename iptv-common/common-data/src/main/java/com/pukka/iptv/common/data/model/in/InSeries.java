package com.pukka.iptv.common.data.model.in;

import com.alibaba.excel.annotation.ExcelIgnore;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.NoArgsConstructor;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Date;

/**
 *
 * @author: tan
 * @date: 2021-8-27 17:26:11
 */

@Data
@EqualsAndHashCode
@JsonIgnoreProperties(value = { "handler" })
@NoArgsConstructor
@TableName(value = "in_series",autoResultMap=true)
public class InSeries extends Model<InSeries> implements java.io.Serializable{

    private static final long serialVersionUID = 1L;
	/**主键*/
	@TableField(value = "id")
    @TableId(type = IdType.AUTO)
    @ApiModelProperty(value="主键",dataType="Long",name="id")
    private Long id;
	/**全局唯一标识*/
	@TableField(value = "code")
    @ApiModelProperty(value="全局唯一标识",dataType="String",name="code")
    private String code;
	/**节目名*/
	@TableField(value = "name")
    @ApiModelProperty(value="节目名",dataType="String",name="name")
    private String name;
	/**订购编号*/
	@TableField(value = "order_number")
    @ApiModelProperty(value="订购编号",dataType="String",name="orderNumber")
    private String orderNumber;
	/**原名*/
	@TableField(value = "original_name")
    @ApiModelProperty(value="原名",dataType="String",name="originalName")
    private String originalName;
	/**排序名称*/
	@TableField(value = "sort_name")
    @ApiModelProperty(value="排序名称",dataType="String",name="sortName")
    private String sortName;
	/**搜索名*/
	@TableField(value = "search_name")
    @ApiModelProperty(value="搜索名",dataType="String",name="searchName")
    private String searchName;
	/**首播日期*/
	@TableField(value = "org_air_date")
    @ApiModelProperty(value="首播日期",dataType="String",name="orgAirDate")
    private String orgAirDate;
	/**有效订购开始时间*/
	@TableField(value = "licensing_window_start")
    @ApiModelProperty(value="有效订购开始时间",dataType="String",name="licensingWindowStart")
    private String licensingWindowStart;
	/**有效订购结束时间*/
	@TableField(value = "licensing_window_end")
    @ApiModelProperty(value="有效订购结束时间",dataType="String",name="licensingWindowEnd")
    private String licensingWindowEnd;
	/**新到天数*/
	@TableField(value = "display_as_new")
    @ApiModelProperty(value="新到天数",dataType="Integer",name="displayAsNew")
    private Integer displayAsNew;
	/**剩余天数 */
	@TableField(value = "display_as_last_chance")
    @ApiModelProperty(value="剩余天数 ",dataType="Integer",name="displayAsLastChance")
    private Integer displayAsLastChance;
	/**拷贝保护标志(0/1)*/
	@TableField(value = "macrovision")
    @ApiModelProperty(value="拷贝保护标志(0/1)",dataType="Integer",name="macrovision")
    private Integer macrovision;
	/**含税定价*/
	@TableField(value = "price")
    @ApiModelProperty(value="含税定价",dataType="java.math.BigDecimal",name="price")
    private java.math.BigDecimal price;
	/**总集数*/
	@TableField(value = "volumn_count")
    @ApiModelProperty(value="总集数",dataType="Integer",name="volumnCount")
    private Integer volumnCount;
	/**状态标识0：失效；1生效*/
	@TableField(value = "status")
    @ApiModelProperty(value="状态标识0：失效；1生效",dataType="Integer",name="status")
    private Integer status;
	/**描述信息*/
	@TableField(value = "description")
    @ApiModelProperty(value="描述信息",dataType="String",name="description")
    private String description;
	/**主要人物*/
	@TableField(value = "kpeople")
    @ApiModelProperty(value="主要人物",dataType="String",name="kpeople")
    private String kpeople;
	/**导演*/
	@TableField(value = "director")
    @ApiModelProperty(value="导演",dataType="String",name="director")
    private String director;
	/**编剧*/
	@TableField(value = "script_writer")
    @ApiModelProperty(value="编剧",dataType="String",name="scriptWriter")
    private String scriptWriter;
	/**节目 主持人*/
	@TableField(value = "compere")
    @ApiModelProperty(value="节目 主持人",dataType="String",name="compere")
    private String compere;
	/**受访者*/
	@TableField(value = "guest")
    @ApiModelProperty(value="受访者",dataType="String",name="guest")
    private String guest;
	/**记者*/
	@TableField(value = "reporter")
    @ApiModelProperty(value="记者",dataType="String",name="reporter")
    private String reporter;
	/**其他责任人*/
	@TableField(value = "op_incharge")
    @ApiModelProperty(value="其他责任人",dataType="String",name="opIncharge")
    private String opIncharge;
	/**0：连续剧，1：系列片 默认连续剧*/
	@TableField(value = "series_type")
    @ApiModelProperty(value="0：连续剧，1：系列片 默认连续剧",dataType="Integer",name="seriesType")
    private Integer seriesType;
	/**版权方标识*/
	@TableField(value = "copy_right")
    @ApiModelProperty(value="版权方标识",dataType="String",name="copyRight")
    private String copyRight;
	/**内容提供商标识*/
	@TableField(value = "content_provider")
    @ApiModelProperty(value="内容提供商标识",dataType="String",name="contentProvider")
    private String contentProvider;
	/**应答文件包含0: 成功 其他: 错误代码*/
	@TableField(value = "result")
    @ApiModelProperty(value="应答文件包含0: 成功 其他: 错误代码",dataType="Integer",name="result")
    private Integer result;
	/**应答文件包含，错误描述*/
	@TableField(value = "error_description")
    @ApiModelProperty(value="应答文件包含，错误描述",dataType="String",name="errorDescription")
    private String errorDescription;
	/**内容服务平台标识*/
	@TableField(value = "vsp_code")
    @ApiModelProperty(value="内容服务平台标识",dataType="String",name="vspCode")
    private String vspCode;
	/** 评分，0 到 10，最多一位 小数 */
	@TableField(value = "rating")
    @ApiModelProperty(value=" 评分，0 到 10，最多一位 小数 ",dataType="String",name="rating")
    private String rating;
	/**格式： 1;2;3;4;5 说明： 1.五个价格均 可空，用分号 隔开。极端示 例：;;;;。四个; 表示五个空的 价格。 2.五个价格顺 序固定：价格; 政企价格 1;政 企价格 2;优惠 价格 1;优惠价 格 2。*/
	@TableField(value = "new_price")
    @ApiModelProperty(value="格式： 1;2;3;4;5 说明： 1.五个价格均 可空，用分号 隔开。极端示 例：;;;;。四个; 表示五个空的 价格。 2.五个价格顺 序固定：价格; 政企价格 1;政 企价格 2;优惠 价格 1;优惠价 格 2。",dataType="String",name="newPrice")
    private String newPrice;
	/**创建时间*/
	@TableField(value = "create_time")
    @ApiModelProperty(value="创建时间",dataType="String",name="createTime")
    @JsonFormat(shape = JsonFormat.Shape.STRING,pattern = "yyyy-MM-dd HH:mm:ss",timezone="GMT+8")
    private Date createTime;
	/**更新时间*/
	@TableField(value = "update_time")
    @ApiModelProperty(value="更新时间",dataType="String",name="updateTime")
    @JsonFormat(shape = JsonFormat.Shape.STRING,pattern = "yyyy-MM-dd HH:mm:ss",timezone="GMT+8")
    private Date updateTime;
	/**动作 1：REGIST，2：UPDATE，3：DELETE*/
	@TableField(value = "action")
    @ApiModelProperty(value="动作 1：REGIST，2：UPDATE，3：DELETE",dataType="Integer",name="action")
    private Integer action;
	/**correlateId*/
	@TableField(value = "correlate_id")
    @ApiModelProperty(value="correlateId",dataType="String",name="correlateId")
    private String correlateId;
	/**节目形态*/
	@TableField(value = "pgm_category")
    @ApiModelProperty(value="节目形态",dataType="String",name="pgmCategory")
    private String pgmCategory;
	/**二级标签*/
	@TableField(value = "pgm_snd_class")
    @ApiModelProperty(value="二级标签",dataType="String",name="pgmSndClass")
    private String pgmSndClass;

    /**Type*/
    @ExcelIgnore
    @TableField(value = "type")
    @ApiModelProperty(value="type 节目形态",dataType="String",name="correlateId")
    private String type;
    /**Tags*/
    @ExcelIgnore
    @TableField(value = "tags")
    @ApiModelProperty(value="tags 二级分类，使用空格隔开",dataType="String",name="tags")
    private String tags;

    /**节目清晰度标识*/
    @ExcelIgnore
    @TableField(value = "definition_flag")
    @ApiModelProperty(value="节目清晰度标识 0：标清 1：高清  2：超清 3: 4K  4: 杜比（4K+杜比）\n",dataType="Integer",name="definitionFlag")
    private Integer definitionFlag;

    /**语言*/
    @TableField(value = "language")
    @ApiModelProperty(value="语言",dataType="String",name="language")
    private String language;
}
