package com.pukka.iptv.common.data.model.baidu.vcr.response;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.pukka.iptv.common.data.model.baidu.vcr.response.items.Preset;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @Description TODO
 * @Date 2024/11/11 15:00
 * @Version V1.0
 **/
@Data
public class PresetResponse {
    @JsonProperty("presets")
    private List<Preset> presets;

    public List<Preset> getPresets() {
        return presets;
    }

    public void setPresets(List<Preset> presets) {
        this.presets = presets;
    }
}
