package com.pukka.iptv.common.data.util;

import java.util.Optional;

/**
 * @PACKAGE_NAME: Utility.Tooles.Tuples
 * @NAME: Tuple4 四元数组
 * @USER: wangbo
 * @DATE: 2021/9/06
 **/
public class Tuple4<A, B, C, D> extends Tuple {
    private A a;
    private B b;
    private C c;
    private D d;

    public Tuple4(A a, B b, C c, D d) {
        this.a = a;
        this.b = b;
        this.c = c;
        this.d = d;
    }

    @Override
    public Optional<A> getA() {
        return Optional.ofNullable(a);
    }

    @Override
    public Optional<B> getB() {
        return Optional.ofNullable(b);
    }

    @Override
    public Optional<C> getC() {
        return Optional.ofNullable(c);
    }

    @Override
    public Optional<D> getD() {
        return Optional.ofNullable(d);
    }

    @Override
    public String toString() {
        return "Tuple3{" + "a=" + a + ", b=" + b + ", c=" + c + ",d=" + d + "}";
    }
}