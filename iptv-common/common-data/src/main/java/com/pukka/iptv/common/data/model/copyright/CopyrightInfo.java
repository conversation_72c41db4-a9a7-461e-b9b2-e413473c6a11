package com.pukka.iptv.common.data.model.copyright;

import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelProperty;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.pukka.iptv.common.data.converter.bms.LicensingConverter;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.Date;

/**
 * 版权信息实体
 *
 * <AUTHOR>
 * @email
 * @date 2022-07-12 18:01:29
 */
@Data
@NoArgsConstructor
@EqualsAndHashCode
@Accessors(chain = true)
@JsonIgnoreProperties(value = {"handler"})
public class CopyrightInfo implements Serializable {
    private static final long serialVersionUID = 1223034862636307774L;

    /**
     * id
     */
    @ExcelIgnore
    @ApiModelProperty(value = "id", dataType = "Long", name = "id")
    private Long id;
    /**
     * 媒资code
     */
    @ExcelIgnore
    @ApiModelProperty(value = "媒资code", dataType = "String", name = "code")
    private String code;
    /**
     * 节目名称
     */
    @ExcelProperty("媒资名称")
    @ApiModelProperty(value = "节目名", dataType = "String", name = "name")
    private String name;
    /**
     * 节目类型
     */
    @ExcelIgnore
    @ApiModelProperty(value = "1: 单集 3: 剧集", dataType = "Integer", name = "contentType")
    private Integer contentType;
    /**
     * 节目类型
     */
    @ExcelProperty("节目类型")
    @ApiModelProperty(value = "1: 单集 3: 剧集", dataType = "String", name = "contentTypeName")
    private String contentTypeName;
    /**
     * CP
     */
    @ExcelIgnore
    @ApiModelProperty(value = "CP", dataType = "Long", name = "cpId")
    private Long cpId;
    /**
     * CP名称
     */
    @ExcelProperty("所属CP")
    @ApiModelProperty(value = "CP名称", dataType = "String", name = "cpName")
    private String cpName;
    /**
     * 内容提供商标识
     */
    @ExcelProperty("内容提供商")
    @ApiModelProperty(value = "内容提供商标识", dataType = "String", name = "contentProvider")
    private String contentProvider;
    /**
     * 导演
     */
    @ExcelProperty("导演")
    @ApiModelProperty(value = "导演", dataType = "String", name = "director")
    private String director;
    /**
     * 演员列表
     */
    @ExcelProperty("演员")
    @ApiModelProperty(value = "演员列表", dataType = "String", name = "kpeople")
    private String kpeople;
    /**
     * 内容字号
     */
    @ExcelProperty("内容字号")
    @ApiModelProperty(value = "内容字号", dataType = "String", name = "approval")
    private String approval;
    /**
     * 版权方标识
     */
    @ExcelProperty("版权方")
    @ApiModelProperty(value = "版权方标识", dataType = "String", name = "copyRight")
    private String copyRight;
    /**
     * 发行方
     */
    @ExcelProperty("发行方")
    @ApiModelProperty(value = "发行方", dataType = "String", name = "publisher")
    private String publisher;
    /**
     * 总集数
     */
    @ExcelProperty("总集数")
    @ApiModelProperty(value = "总集数", dataType = "Long", name = "volumnCount")
    private Integer volumnCount;
    /**
     * 有效订购开始时间
     */
    @ExcelProperty(value = "授权开始时间",converter = LicensingConverter.class)
    @ApiModelProperty(value = "有效订购开始时间", dataType = "String", name = "licensingWindowStart")
    private String licensingWindowStart;
    /**
     * 有效订购结束时间
     */
    @ExcelProperty(value = "授权结束时间",converter = LicensingConverter.class)
    @ApiModelProperty(value = "有效订购结束时间", dataType = "String", name = "licensingWindowEnd")
    private String licensingWindowEnd;
    /**
     * 剩余天数
     */
    @ExcelProperty("剩余天数")
    @ApiModelProperty(value = "剩余天数 ", dataType = "Long", name = "displayAsLastChance")
    private Long displayAsLastChance;
    /**
     * 媒资分类ID，来自pgm_category表
     */
    @ExcelIgnore
    @ApiModelProperty(value = "媒资分类ID，来自pgm_category表", dataType = "Long", name = "pgmCategoryId")
    private Long pgmCategoryId;
    /**
     * 节目形态，如：新闻，电影
     */
    @ExcelProperty("媒资分类")
    @ApiModelProperty(value = "节目形态，如：新闻，电影", dataType = "String", name = "pgmCategory")
    private String pgmCategory;
    /**
     * 上映年份
     */
    @ExcelProperty("年份")
    @ApiModelProperty(value = "上映年份", dataType = "String", name = "releaseYear")
    private String releaseYear;
    /**
     * 产地id，来自于sys_dictionary_item表
     */
    @ExcelIgnore
    @ApiModelProperty(value = "产地id，来自于sys_dictionary_item表", dataType = "Long", name = "originalCountryId")
    private Long originalCountryId;
    /**
     * 产地名称，来自于sys_dictionary_item表
     */
    @ExcelProperty("产地")
    @ApiModelProperty(value = "产地名称，来自于sys_dictionary_item表", dataType = "String", name = "originalCountry")
    private String originalCountry;
    /**
     * 创建时间
     */
    @ExcelProperty("注入时间")
    @ApiModelProperty(value = "创建时间", dataType = "String", name = "createTime")
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date createTime;
    /**
     * 更新时间
     */
    @ExcelProperty("更新时间")
    @ApiModelProperty(value = "更新时间", dataType = "String", name = "updateTime")
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date updateTime;
}
