package com.pukka.iptv.common.data.vo.req;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import io.swagger.annotations.ApiModel;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

@Data
@NoArgsConstructor
@Accessors(chain = true)
@JsonIgnoreProperties(value = { "handler" })
@ApiModel("CopyrightInfoReq")
public class CopyrightInfoReq {
    /**
     * 媒资名称
     */
    private String contentName;
    /**
     * 艺人名称
     */
    private String actorName;
    /**
     * 节目类型
     */
    private Integer contentType;
    /**
     * 媒资分类ID
     */
    private Long pgmCategoryId;
    /**
     * 年份
     */
    private String releaseYear;
    /**
     * 剩余版权天数
     */
    private Integer lastCopyrightDay;
    /**
     * 内容字号
     */
    private String approval;
    /**
     * 所属CP
     */
    private long cpId;
    /**
     * 产地
     */
    private String originalCountryId;
    /**
     * 授权结束时间 YYYYMMDDHH24MiSS
     */
    private String authorizationEndTime;
}
