package com.pukka.iptv.common.data.model.sys;

import com.baomidou.mybatisplus.annotation.*;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.NoArgsConstructor;
import lombok.EqualsAndHashCode;
import lombok.Data;

import java.util.Date;

/**
 *
 * @author: chenyudong
 * @date: 2021-9-13 16:58:09
 */

@Data
@EqualsAndHashCode
@JsonIgnoreProperties(value = { "handler" })
@NoArgsConstructor
@TableName(value = "sys_in_passage",autoResultMap=true)
public class SysInPassage extends Model<SysInPassage> implements java.io.Serializable{

    private static final long serialVersionUID = 1L;
	/**id*/
    @TableId(type = IdType.AUTO)
	@TableField(value = "id")
    @ApiModelProperty(value="id",dataType="Long",name="id")
    private Long id;
	/**分发通道名称 注入通道名称*/
	@TableField(value = "name")
    @ApiModelProperty(value="分发通道名称 注入通道名称",dataType="String",name="name")
    private String name;
	/**CSPID*/
	@TableField(value = "csp_id")
    @ApiModelProperty(value="CSPID",dataType="String",name="cspId")
    private String cspId;
	/**上游对接地址*/
	@TableField(value = "report_url")
    @ApiModelProperty(value="上游对接地址",dataType="String",name="reportUrl")
    private String reportUrl;
	/**创建人*/
	@TableField(value = "creator_name", fill = FieldFill.INSERT)
    @ApiModelProperty(value="创建人",dataType="String",name="creatorName")
    private String creatorName;
	/**creatorId*/
	@TableField(value = "creator_id", fill = FieldFill.INSERT)
    @ApiModelProperty(value="creatorId",dataType="Long",name="creatorId")
    private Long creatorId;
	/**优先级，值越大越高，默认0*/
	@TableField(value = "priority")
    @ApiModelProperty(value="优先级，值越大越高，默认0",dataType="Integer",name="priority")
    private Integer priority;
	/**创建时间*/
	@TableField(value = "create_time")
    @ApiModelProperty(value="创建时间",dataType="String",name="createTime")
    @JsonFormat(shape = JsonFormat.Shape.STRING,pattern = "yyyy-MM-dd HH:mm:ss",timezone="GMT+8")
    private Date createTime;
	/**更新时间*/
	@TableField(value = "update_time")
    @ApiModelProperty(value="更新时间",dataType="String",name="updateTime")
    @JsonFormat(shape = JsonFormat.Shape.STRING,pattern = "yyyy-MM-dd HH:mm:ss",timezone="GMT+8")
    private Date updateTime;
	/**CPID，来自于cms_cp*/
	@TableField(value = "cp_id")
    @ApiModelProperty(value="CPID，来自于cms_cp",dataType="Long",name="cpId")
    private Long cpId;
	/**关联CP名称，来自于cms_cp*/
	@TableField(value = "cp_name")
    @ApiModelProperty(value="关联CP名称，来自于cms_cp",dataType="String",name="cpName")
    private String cpName;

    @TableField(value = "cp_source_type")
    @ApiModelProperty(value="1:从关联CP获取CP信息(根据cp_id)  2：从工单VSPCode获取CP信息(根据工单里的VSPCode关联cp_code)",dataType="Integer",name="cpSourceType")
    private Integer cpSourceType;

    @TableField(value = "sp_publish_type")
    @ApiModelProperty(value="1:注入内容默认未发布,2:注入内容默认发布成功",dataType="Integer",name="spPublishType")
    private Integer spPublishType;

	/**状态   1可用 2:停用 255：已删除*/
	@TableField(value = "status")
    @ApiModelProperty(value="状态   1可用 2:停用 255：已删除",dataType="Integer",name="status")
    private Integer status;
	/**每次处理条数*/
	@TableField(value = "deal_count")
    @ApiModelProperty(value="每次处理条数",dataType="Long",name="dealCount")
    private Long dealCount;
	/**备注*/
	@TableField(value = "description")
    @ApiModelProperty(value="备注",dataType="String",name="description")
    private String description;

	@TableField(value = "movie_download_type")
    @ApiModelProperty(value = "注入工单视频文件下载类型   1：下载   2：透传", dataType = "Integer", name = "movieDownloadType")
    private Integer movieDownloadType;

    @TableField(value = "order_publish_type")
    @ApiModelProperty(value = "注入工单处理类型 1.解析 2.透传加解析 3.解析并自动下发", dataType = "Integer", name = "orderPublishType")
    private Integer orderPublishType;

    @TableField(value = "out_passage_ids")
    @ApiModelProperty(value = "注入工单透传通道id", dataType = "String", name = "outPassageIds")
    private String outPassageIds;

    @TableField(value = "out_passage_names")
    @ApiModelProperty(value = "注入工单透传通道名称", dataType = "String", name = "outPassageNames")
    private String outPassageNames;

    /**自动下发，直播类型*/
    @TableField(value = "order_publish_live_ids")
    @ApiModelProperty(value="自动下发，直播类型",dataType="String",name="orderPublishLiveIds")
    private String orderPublishLiveIds;
    /**自动下发，点播类型*/
    @TableField(value = "order_publish_record_ids")
    @ApiModelProperty(value="自动下发，点播类型",dataType="String",name="orderPulblishRecordIds")
    private String orderPublishRecordIds;

    @TableField(value = "realtime_sp_ids")
    @ApiModelProperty(value = "自动发布直播sp集合", dataType = "String", name = "realTimeSpIds")
    private String realTimeSpIds;

    @TableField(value = "realtime_sp_names")
    @ApiModelProperty(value = "自动发布直播sp名称", dataType = "String", name = "realTimeSpNames")
    private String realTimeSpNames;

    @TableField(value = "delaytime_sp_ids")
    @ApiModelProperty(value = "自动发布点播sp集合", dataType = "String", name = "delayTimeSpIds")
    private String delayTimeSpIds;

    @TableField(value = "delaytime_sp_names")
    @ApiModelProperty(value = "自动发布点播sp名称", dataType = "String", name = "delayTimeSpNames")
    private String delayTimeSpNames;

    /**运营类型 -1:所有 1：媒资内容 2：直播频道*/
    @TableField(value = "business_type")
    @ApiModelProperty(value="运营类型 1：媒资内容 2：直播频道",dataType="Integer",name="businessType")
    private Integer businessType;
}
