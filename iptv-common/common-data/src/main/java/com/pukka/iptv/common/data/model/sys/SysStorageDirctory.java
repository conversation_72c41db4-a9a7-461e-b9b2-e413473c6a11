package com.pukka.iptv.common.data.model.sys;

import com.baomidou.mybatisplus.annotation.*;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.NoArgsConstructor;
import lombok.EqualsAndHashCode;
import lombok.Data;

import java.util.Date;

/**
 *
 * @author: chenyudong
 * @date: 2021-9-8 18:12:15
 */

@Data
@EqualsAndHashCode
@JsonIgnoreProperties(value = { "handler" })
@NoArgsConstructor
@TableName(value = "sys_storage_directory",autoResultMap=true)
public class SysStorageDirctory extends Model<SysStorageDirctory> implements java.io.Serializable{

    private static final long serialVersionUID = 1L;
	/**id*/
    @TableId(type = IdType.AUTO)
	@TableField(value = "id")
    @ApiModelProperty(value="id",dataType="Long",name="id")
    private Long id;
	/**所属存储名称*/
	@TableField(value = "storage_name")
    @ApiModelProperty(value="所属存储名称",dataType="String",name="storageName")
    private String storageName;
	/**storageId*/
	@TableField(value = "storage_id")
    @ApiModelProperty(value="storageId",dataType="Long",name="storageId")
    private Long storageId;
	/**权限账号*/
	@TableField(value = "account")
    @ApiModelProperty(value="权限账号",dataType="String",name="account")
    private String account;
	/**权限账号密码*/
	@TableField(value = "password")
    @ApiModelProperty(value="权限账号密码",dataType="String",name="password")
    private String password;
	/**目录类型 1：源片存储目录  2：成片存储目录 3：图片存储目录 4：文档存储目录 5：工单存储目录*/
	@TableField(value = "type")
    @ApiModelProperty(value="目录类型 1：源片存储目录  2：成片存储目录 3：图片存储目录 4：文档存储目录 5：工单存储目录",dataType="Integer",name="type")
    private Integer type;
	/**读写权限类型 1：只读 2：读写*/
	@TableField(value = "authority_type")
    @ApiModelProperty(value="读写权限类型 1：只读 2：读写",dataType="Integer",name="authorityType")
    private Integer authorityType;
	/**备注*/
	@TableField(value = "description")
    @ApiModelProperty(value="备注",dataType="String",name="description")
    private String description;
    /**状态1：正常 0：停用 255：删除失效 */
    @TableField(value = "status")
    @ApiModelProperty(value="状态1：正常 0：停用 255##失效##ISSEARCH",dataType="Integer",name="status")
    private Integer status;
    /**创建人来自admin表*/
    @TableField(value = "creator_name", fill = FieldFill.INSERT)
    @ApiModelProperty(value="创建人来自admin表",dataType="String",name="creatorName")
    private String creatorName;
    /**来自admin表*/
    @TableField(value = "creator_id", fill = FieldFill.INSERT)
    @ApiModelProperty(value="来自admin表",dataType="Long",name="creatorId")
    private Long creatorId;
    /**创建时间*/
    @TableField(value = "create_time", fill = FieldFill.INSERT)
    @ApiModelProperty(value="创建时间",dataType="String",name="createTime")
    @JsonFormat(shape = JsonFormat.Shape.STRING,pattern = "yyyy-MM-dd HH:mm:ss",timezone="GMT+8")
    private Date createTime;
    /**更新时间*/
    @TableField(value = "update_time")
    @ApiModelProperty(value="更新时间",dataType="String",name="updateTime")
    @JsonFormat(shape = JsonFormat.Shape.STRING,pattern = "yyyy-MM-dd HH:mm:ss",timezone="GMT+8")
    private Date updateTime;
}
