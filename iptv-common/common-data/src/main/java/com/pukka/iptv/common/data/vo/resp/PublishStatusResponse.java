package com.pukka.iptv.common.data.vo.resp;

import com.pukka.iptv.common.base.enums.MqAckStatusEnum;
import lombok.Getter;
import lombok.Setter;

/**
 * <AUTHOR>
 * @Date 2021/10/28 10:10 上午
 * @description:
 * @Version 1.0
 */
@Getter
@Setter
public class PublishStatusResponse {
    /**
     * mq响应应答
     */
    private MqAckStatusEnum mqAckStatusEnum;
    /**
     * 错误描述
     */
    private String errorDescription;
    /**
     * 消息实体
     */
    private Object data;

    public PublishStatusResponse() {
        this.mqAckStatusEnum = MqAckStatusEnum.SUCCESS;
    }

    /**
     * 回退状态下，填写描述信息
     * @param errorMsg
     */
    public void setRollBackErrorDesc(String errorMsg){
        this.setMqAckStatusEnum(MqAckStatusEnum.ERR_ROLLBACK);
        this.setErrorDescription(errorMsg);
    }

    /**
     * 不回退状态下，填写描述信息
     * @param errorMsg
     */
    public void setUnRollBackErrorDesc(String errorMsg){
        this.setMqAckStatusEnum(MqAckStatusEnum.ERR_UNROLLBACK);
        this.setErrorDescription(errorMsg);
    }
}
