package com.pukka.iptv.common.data.model.copyright;

import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.util.List;

/**
 * @Author: chiron
 * @Date: 2022/08/23/11:10
 * @Description:
 */
@Data
@NoArgsConstructor
@Accessors(chain = true)
public class CopyrightWrapperResponse {
    /**
     * 总条数
     */
    long total;
    /**
     * 版权信息实体集合
     */
    private List<CopyrightInfo> copyrightInfoList;
}
