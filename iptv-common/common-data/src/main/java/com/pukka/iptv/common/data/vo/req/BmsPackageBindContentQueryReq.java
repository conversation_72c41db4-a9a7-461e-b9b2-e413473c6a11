package com.pukka.iptv.common.data.vo.req;

import com.pukka.iptv.common.data.vo.bms.CheckNameApi;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import javax.validation.constraints.Pattern;

import static com.pukka.iptv.common.data.util.RegexUtil.ALL_CHAR_REGEX;

/**
 * <AUTHOR>
 * @create 2021-09-14 18:42
 * 产品包模块绑定内容查询参数
 */
@Getter
@Setter
public class BmsPackageBindContentQueryReq implements CheckNameApi
{
    @ApiModelProperty("产品包ID")
    private Long packageId;

    @ApiModelProperty("cpId")
    private Long cpId;

    @ApiModelProperty("媒资名称")
    private String name;

    @ApiModelProperty("媒资分类ID")
    private Long pgmCategoryId;

    //1:电影 3:电视剧 4:系列片 5:片花
    @ApiModelProperty("媒资类型,前端只有单集和剧集：1 单集 2 剧集")
    private Integer contentType;

    @ApiModelProperty("发布状态")
    private Integer publishStatus;

    @ApiModelProperty("内容id(媒资编排查询媒资的包关系使用)")
    private Long bmsContentId;

    private String[] names;

    @ApiModelProperty("原名")
    private String originalName;

    @Pattern(regexp = ALL_CHAR_REGEX + "{0,5000}")
    @ApiModelProperty("产品包名称")
    private String packageName;
    private String[] packageNames;

    @Override
    public String getCategoryName() {
        return null;
    }

    @Override
    public void setCategoryName(String categoryName) {

    }

    @Override
    public void setCategoryNames(String[] names) {

    }
}
