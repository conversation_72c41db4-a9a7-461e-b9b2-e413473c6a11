package com.pukka.iptv.common.data.vo.copyright;

import com.alibaba.excel.annotation.ExcelProperty;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * 违禁规则表
 *
 * <AUTHOR>
 * @email
 * @date 2022-07-12 18:01:29
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@JsonIgnoreProperties(value = { "handler" })
@ApiModel("ArtistProhibitImportVo")
public class RuleProhibitImportVo implements Serializable {

    /**
     * 违禁规则名称
     */
    @ExcelProperty("违禁规则名称")
    @ApiModelProperty(value = "违禁规则名称", dataType = "String", name = "showName")
    private String showName;
    /**
     * 节目形态，如：新闻，电影
     */
    @ExcelProperty("媒资分类")
    @ApiModelProperty(value = "节目形态，如：新闻，电影", dataType = "String", name = "pgmCategory")
    private String pgmCategory;
    /**
     * 违禁规则节目类型
     */
    @ExcelProperty("节目类型")
    @ApiModelProperty(value = "违禁规则节目类型", dataType = "String", name = "contentType")
    private String contentTypes;
    /**
     * 主要人物
     */
    @ExcelProperty("主演")
    @ApiModelProperty(value = "主要人物", dataType = "String", name = "kpeople")
    private String kpeople;
    /**
     * 导演
     */
    @ExcelProperty("导演")
    @ApiModelProperty(value = "导演", dataType = "String", name = "director")
    private String director;
    /**
     * 产地名称，来自于sys_dictionary_item表
     */
    @ExcelProperty("产地")
    @ApiModelProperty(value = "产地名称，来自于sys_dictionary_item表", dataType = "String", name = "originalCountry")
    private String originalCountry;
    /**
     * 上映年份
     */
    @ExcelProperty("年份")
    @ApiModelProperty(value = "上映年份", dataType = "String", name = "releaseYear")
    private String releaseYear;

}
