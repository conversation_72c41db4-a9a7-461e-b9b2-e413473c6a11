package com.pukka.iptv.common.data.model.sys;

import com.baomidou.mybatisplus.annotation.*;
import io.swagger.annotations.ApiModelProperty;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.NoArgsConstructor;
import lombok.EqualsAndHashCode;
import java.util.Date;
import lombok.Data;
import com.fasterxml.jackson.annotation.JsonFormat;

/**
 *
 * @author: zhengcl
 * @date: 2021-8-17 18:55:47
 */

@Data
@EqualsAndHashCode
@JsonIgnoreProperties(value = { "handler" })
@NoArgsConstructor
@TableName(value = "sys_role_menu",autoResultMap=true)
public class SysRoleMenu extends Model<SysRoleMenu> implements java.io.Serializable{

    private static final long serialVersionUID = 1L;
	/**ID*/
	@TableId(type = IdType.AUTO)
	@TableField(value = "id")
    @ApiModelProperty(value="ID",dataType="Long",name="id")
    private Long id;
	/**角色ID*/
	@TableField(value = "role_id")
    @ApiModelProperty(value="角色ID",dataType="Long",name="roleId")
    private Long roleId;
	/**菜单ID*/
	@TableField(value = "menu_id")
    @ApiModelProperty(value="菜单ID",dataType="Long",name="menuId")
    private Long menuId;
	/**createTime*/
	@TableField(value = "create_time", fill = FieldFill.INSERT)
    @ApiModelProperty(value="createTime",dataType="String",name="createTime")
    @JsonFormat(shape = JsonFormat.Shape.STRING,pattern = "yyyy-MM-dd HH:mm:ss",timezone="GMT+8")
    private Date createTime;
	/**updateTime*/
	@TableField(value = "update_time")
    @ApiModelProperty(value="updateTime",dataType="String",name="updateTime")
    @JsonFormat(shape = JsonFormat.Shape.STRING,pattern = "yyyy-MM-dd HH:mm:ss",timezone="GMT+8")
    private Date updateTime;
}
