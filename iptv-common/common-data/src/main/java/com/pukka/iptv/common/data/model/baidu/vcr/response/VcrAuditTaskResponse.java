package com.pukka.iptv.common.data.model.baidu.vcr.response;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.pukka.iptv.common.data.model.baidu.vcr.response.items.Result;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @Description TODO
 * @Date 2024/10/25 10:45
 * @Version V1.0
 **/
@Data
@EqualsAndHashCode
@JsonIgnoreProperties(value = { "handler" })
@NoArgsConstructor
@Accessors(chain = true)
public class VcrAuditTaskResponse implements java.io.Serializable{
    private static final long serialVersionUID = -61052944880370204L;
    /**
     * 用户名
     */
    @JsonProperty("userName")
    @ApiModelProperty(value = "用户名", dataType = "String", name = "userName")
    private String userName;

    /**
     * 任务ID
     */
    @JsonProperty("taskId")
    @ApiModelProperty(value = "任务ID", dataType = "String", name = "taskId")
    private String taskId;

    /**
     * 视频唯一ID
     */
    @JsonProperty("mediaId")
    @ApiModelProperty(value = "视频唯一ID", dataType = "String", name = "mediaId")
    private String mediaId;


    /**
     * 描述信息
     */
    @JsonProperty("description")
    @ApiModelProperty(value = "描述信息", dataType = "String", name = "description")
    private String description;

    /**
     * 预设值
     */
    @JsonProperty("preset")
    @ApiModelProperty(value = "预设值", dataType = "String", name = "preset")
    private String preset;

    /**
     * 任务状态
     */
    @JsonProperty("status")
    @ApiModelProperty(value = "任务状态", dataType = "String", name = "status")
    private String status;

    /**
     * 标签信息
     */
    @JsonProperty("label")
    @ApiModelProperty(value = "标签信息", dataType = "String", name = "label")
    private String label;


    /**
     * 视频源，可选值包括 http、https 等可访问的视频 URL 地址。
     */
    @JsonProperty("source")
    @ApiModelProperty(value = "视频源，可选值包括 http、https 等可访问的视频 URL 地址。", dataType = "String", name = "source")
    private String source;


    /**
     * 视频源，可选值包括 http、https 等可访问的视频 URL 地址。
     */
    @JsonProperty("url")
    @ApiModelProperty(value = "视频地址", dataType = "String", name = "url")
    private String url;

    /**
     * 视频时长，以秒为单位
     */
    @JsonProperty("duration")
    @ApiModelProperty(value = "视频时长，以秒为单位", dataType = "int", name = "duration")
    private int duration;

    /**
     * 任务优先级，可选值为 IDLE、NORMAL、HIGH，默认值为 NORMAL。
     */
    @JsonProperty("priority")
    @ApiModelProperty(value = "任务优先级", dataType = "String", name = "priority")
    private String priority;

    /**
     * 识别结果列表
     */
    @JsonProperty("results")
    @ApiModelProperty(value = "识别结果列表", dataType = "List", name = "results")
    private List<Result> results;

    /**
     * 任务创建时间
     */
    @JsonProperty("createTime")
    @ApiModelProperty(value = "任务创建时间", dataType = "String", name = "createTime")
    private Date createTime;

    /**
     * 任务开始时间
     */
    @JsonProperty("startTime")
    @ApiModelProperty(value = "任务开始时间", dataType = "String", name = "startTime")
    private Date startTime;

    /**
     * 任务完成时间
     */
    @JsonProperty("finishTime")
    @ApiModelProperty(value = "任务完成时间", dataType = "String", name = "finishTime")
    private Date finishTime;

    /**
     * 错误码
     */
    @JsonProperty("code")
    @ApiModelProperty(value = "错误码", dataType = "String", name = "code")
    private String code;

    /**
     * 回调类型
     */
    @JsonProperty("message")
    @ApiModelProperty(value = "错误信息", dataType = "String", name = "message")
    private String message;

}
