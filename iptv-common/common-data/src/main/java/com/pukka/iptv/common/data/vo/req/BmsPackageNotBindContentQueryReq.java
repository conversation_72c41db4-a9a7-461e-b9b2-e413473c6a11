package com.pukka.iptv.common.data.vo.req;

import com.pukka.iptv.common.data.vo.bms.CheckNameApi;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import javax.validation.constraints.NotNull;

/**
 * <AUTHOR>
 * @create 2021-09-15 14:23
 * 产品包模块 未绑定 并 发布成功 内容查询参数
 */
@Getter
@Setter
public class BmsPackageNotBindContentQueryReq implements CheckNameApi
{
    @ApiModelProperty("产品包ID")
    @NotNull
    private Long packageId;

    @ApiModelProperty("spId")
    @NotNull
    private Long spId;

    @ApiModelProperty("cpId")
    private Long cpId;

    @ApiModelProperty("媒资名称")
    private String name;

    @ApiModelProperty("媒资分类ID")
    private Long pgmCategoryId;

    //1:电影 3:电视剧 4:系列片 5:片花
    @ApiModelProperty("媒资类型,前端只有单集和剧集：1 单集 2 剧集")
    private Integer contentType;

    @ApiModelProperty("终审状态")
    private Integer opCheckStatus;

    // 发布状态 当前发布状态固定为 发布成功，所查询的所有媒资均为发布成功的状态
    private Integer publishStatus;

    private String[] names;

    @ApiModelProperty(value = "原名", dataType = "String", name = "originalName")
    private String originalName;

    private String contentName;

    @Override
    public String getPackageName() {
        return null;
    }

    @Override
    public String getCategoryName() {
        return null;
    }

    @Override
    public void setPackageName(String packageName) {

    }

    @Override
    public void setCategoryName(String categoryName) {

    }

    @Override
    public void setCategoryNames(String[] names) {

    }

    @Override
    public void setPackageNames(String[] names) {

    }
}
