package com.pukka.iptv.common.data.config;

import com.baomidou.mybatisplus.annotation.DbType;
import com.baomidou.mybatisplus.autoconfigure.ConfigurationCustomizer;
import com.baomidou.mybatisplus.core.handlers.MetaObjectHandler;
import com.baomidou.mybatisplus.extension.plugins.MybatisPlusInterceptor;
import com.baomidou.mybatisplus.extension.plugins.inner.OptimisticLockerInnerInterceptor;
import com.baomidou.mybatisplus.extension.plugins.inner.PaginationInnerInterceptor;
import com.pukka.iptv.common.base.util.JwtTokenUtil;
import com.pukka.iptv.common.base.util.UUID;
import com.pukka.iptv.common.base.vo.SecurityUser;
import com.pukka.iptv.common.core.util.DateUtils;
import com.pukka.iptv.common.data.resolver.SqlFilterArgumentResolver;
import com.pukka.iptv.common.data.util.RedisOperaUtil;
import com.pukka.iptv.common.data.util.TimeUtil;
import org.apache.ibatis.reflection.MetaObject;
import org.mybatis.spring.annotation.MapperScan;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.transaction.annotation.EnableTransactionManagement;
import org.springframework.web.method.support.HandlerMethodArgumentResolver;
import org.springframework.web.servlet.config.annotation.WebMvcConfigurer;

import java.time.LocalDateTime;
import java.util.Date;
import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 * @date 2021/8/6
 */

@EnableTransactionManagement(proxyTargetClass = true)
@MapperScan(basePackages = {"com.pukka.iptv.**.mapper", "com.xxl.job.executor.**.mapper"})
@Configuration(proxyBeanMethods = false)
public class MybatisConfig implements WebMvcConfigurer, MetaObjectHandler {

    /**
     * SQL 过滤器避免SQL 注入
     *
     * @param argumentResolvers
     */
    @Override
    public void addArgumentResolvers(List<HandlerMethodArgumentResolver> argumentResolvers) {
        argumentResolvers.add(new SqlFilterArgumentResolver());
    }

    /***
     * plus 的性能优化
     * @return
     */
/*    @Bean
    public PerformanceMonitorInterceptor performanceInterceptor() {
        PerformanceMonitorInterceptor performanceInterceptor = new PerformanceMonitorInterceptor();
        *//*<!-- SQL 执行性能分析，开发环境使用，线上不推荐。 maxTime 指的是 sql 最大执行时长 -->*//*
        performanceInterceptor.setLoggerName("SLF4J");
        *//*<!--SQL是否格式化 默认false-->*//*
        return performanceInterceptor;
    }*/

    /**
     * 分页插件, 对于单一数据库类型来说,都建议配置该值,避免每次分页都去抓取数据库类型
     */
    @Bean
    public MybatisPlusInterceptor mybatisPlusInterceptor() {
        MybatisPlusInterceptor interceptor = new MybatisPlusInterceptor();
        /*// 多租户支持
        TenantLineInnerInterceptor tenantLineInnerInterceptor = new TenantLineInnerInterceptor();
        tenantLineInnerInterceptor.setTenantLineHandler(tenantHandler());
        interceptor.addInnerInterceptor(tenantLineInnerInterceptor);
        // 数据权限
        DataScopeInnerInterceptor dataScopeInnerInterceptor = new DataScopeInnerInterceptor();
        dataScopeInnerInterceptor.setDataScopeHandle(dataScopeHandle());
        interceptor.addInnerInterceptor(dataScopeInnerInterceptor);*/
        // 乐观锁插件
        interceptor.addInnerInterceptor(optimisticLockerInnerInterceptor());
        // 分页插件
        interceptor.addInnerInterceptor(paginationInnerInterceptor(DbType.MYSQL));
        // 数据权限
        /*DataScopeInnerInterceptor dataScopeInnerInterceptor = new DataScopeInnerInterceptor();
        dataScopeInnerInterceptor.setDataScopeHandle(dataScopeHandle());
        interceptor.addInnerInterceptor(dataScopeInnerInterceptor);*/
        return interceptor;
    }

    /**
     * 分页插件，自动识别数据库类型
     */
    public PaginationInnerInterceptor paginationInnerInterceptor(DbType dbType) {
        PaginationInnerInterceptor paginationInnerInterceptor = new PaginationInnerInterceptor();
        // 设置数据库类型为mysql
        paginationInnerInterceptor.setDbType(dbType);
        paginationInnerInterceptor.setOverflow(true);
        // 设置最大单页限制数量，默认 500 条，-1 不受限制
        paginationInnerInterceptor.setMaxLimit(-1L);
        return paginationInnerInterceptor;
    }

    /**
     * 乐观锁插件
     */
    public OptimisticLockerInnerInterceptor optimisticLockerInnerInterceptor() {
        return new OptimisticLockerInnerInterceptor();
    }

    /**
     * 数据权限插件
     *
     * @return DataScopeInterceptor
     */
   /* public DataScopeInterceptor dataScopeInterceptor() {
        return new DataScopeInterceptor();
    }*/
    @Bean
    public ConfigurationCustomizer configurationCustomizer() {
        return configuration -> configuration.setUseGeneratedShortKey(false);
    }

    //在执行mybatisPlus的insert()时，为我们自动给某些字段填充值，这样的话，我们就不需要手动给insert()里的实体类赋值了
    @Override
    public void insertFill(MetaObject metaObject) {
        Date now = new Date();
        String nowStr = TimeUtil.formatDate(now);
        //其中方法参数中第一个是前面自动填充所对应的字段，第二个是要自动填充的值。第三个是指定实体类的对象
        this.strictInsertFill(metaObject, "createTime", Date.class, now);
        this.strictInsertFill(metaObject, "createTime", String.class, nowStr);
        this.strictInsertFill(metaObject, "checkTime", Date.class, now);
        this.strictInsertFill(metaObject, "checkTime", String.class, nowStr);
        this.strictUpdateFill(metaObject, "updatedTime", LocalDateTime.class, LocalDateTime.now());
        this.strictUpdateFill(metaObject, "updatedTime", String.class, nowStr);
        this.strictInsertFill(metaObject, "code", String.class, UUID.generalPlatformUUID());
        //新增epg文件夹/文件唯一id填充
        this.strictInsertFill(metaObject, "fileSetId", String.class, DateUtils.dateTimeNow(DateUtils.YYYYMMDDHHMMSS) + RedisOperaUtil.getAtomicInteger());
        this.strictInsertFill(metaObject, "fileId", String.class, DateUtils.dateTimeNow(DateUtils.YYYYMMDDHHMMSS) + RedisOperaUtil.getAtomicInteger());
        SecurityUser securityUser = JwtTokenUtil.getSecurityUser();
        if (!Objects.isNull(securityUser)) {
            this.strictInsertFill(metaObject, "creatorId", Long.class, securityUser.getId());
            this.strictInsertFill(metaObject, "creatorName", String.class, securityUser.getName());
            this.strictInsertFill(metaObject, "checkerId", Long.class, securityUser.getId());
            this.strictInsertFill(metaObject, "checkerName", String.class, securityUser.getName());
        }
    }

    //在执行mybatisPlus的update()时，为我们自动给某些字段填充值，这样的话，我们就不需要手动给update()里的实体类赋值了
    @Override
    public void updateFill(MetaObject metaObject) {
        /** 验证字段不存在 */
        Date now = new Date();
        /** 有值也会覆盖 */
        metaObject.setValue("updateTime", now);
//        this.strictUpdateFill(metaObject, "updateTime", LocalDateTime.class, LocalDateTime.now());
    }
}