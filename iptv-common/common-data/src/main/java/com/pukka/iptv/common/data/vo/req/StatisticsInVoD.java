package com.pukka.iptv.common.data.vo.req;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.pukka.iptv.common.data.model.statistics.StatisticsIn;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * @ClassName StatisticsInVoD
 * @Description TODO
 * <AUTHOR>
 * @Date 2022/7/15 10:50
 * @Version
 */
@Data
public class StatisticsInVoD  implements Serializable {

    /**
     * 开始时间
     */
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private String startTime;
    /**
     * 结束时间
     */
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private String endTime;

    private static final long serialVersionUID = 1L;

    /**统计类型*/
    @ApiModelProperty(value="统计类型",dataType="Integer",name="type")
    private Integer type;
    /**媒资类型*/
    @ApiModelProperty(value="媒资类型",dataType="Integer",name="contentType")
    private Integer contentType;

    /**cpId*/
    @ApiModelProperty(value="cpId",dataType="Long",name="cpId")
    private Long cpId;
    /**cpName*/
    @ApiModelProperty(value="cpName",dataType="String",name="cpName")
    private String cpName;

    @ApiModelProperty(value="操作员",dataType="String",name="operator")
    private String operator;
}
