package com.pukka.iptv.common.data.vo.req;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;
import org.springframework.util.Assert;

import javax.validation.constraints.NotEmpty;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @Date 2021-10-28 10:30
 */
@Getter
@Setter
@Accessors(chain = true)
public class PackageContentPublishReq {

    @ApiModelProperty(value = "媒资id列表")
    @NotEmpty
    private List<Long> contentIds;

    @NotEmpty
    @ApiModelProperty(value = "产品包id列表")
    private List<Long> packageIds;

    @ApiModelProperty(value = "产品包和内容关系表主键id")
    private List<Long> relationIds;

    @ApiModelProperty("定时发布时间(不为null则为定时发布) yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone="GMT+8")
    private Date timing;

    @ApiModelProperty("来源 1：专线注入 2：人工绑定")
    private Integer source;

    public void validSource() {
        Assert.state(source != null && (source==1 || source == 2), "field \"source\" illegal");
    }
}
