package com.pukka.iptv.common.data.converter.bms;

import com.alibaba.excel.converters.Converter;
import com.alibaba.excel.enums.CellDataTypeEnum;
import com.alibaba.excel.metadata.GlobalConfiguration;
import com.alibaba.excel.metadata.data.WriteCellData;
import com.alibaba.excel.metadata.property.ExcelContentProperty;
import org.apache.commons.lang3.StringUtils;

import java.util.HashMap;
import java.util.Map;

/**
 * @Author: wangbo
 * @Date: 2022/4/19 10:19
 */
public class CheckStatusConverter implements Converter<Integer> {

    private static final Map<Integer, String> checkStatusMap = new HashMap<>();

    static {
        checkStatusMap.put(1, "未审核");
        checkStatusMap.put(2, "审核中");
        checkStatusMap.put(3, "审核通过");
        checkStatusMap.put(4, "审核未通过");
    }

    @Override
    public Class supportJavaTypeKey() {
        return Integer.class;
    }

    @Override
    public CellDataTypeEnum supportExcelTypeKey() {
        return CellDataTypeEnum.STRING;
    }



    /**
     * 这里是写的时候会调用
     */
    @Override
    public WriteCellData<?> convertToExcelData(Integer value, ExcelContentProperty contentProperty, GlobalConfiguration globalConfiguration) throws Exception {
        return new WriteCellData(getStatus(value));
    }

    private String getStatus(Integer value) {
        String result = checkStatusMap.get(value);
        result = StringUtils.isEmpty(result) ? "" : result;
        return result;
    }
}
