package com.pukka.iptv.common.data.model.baidu.vcr;

import com.alibaba.fastjson.annotation.JSONField;
import com.baomidou.mybatisplus.annotation.*;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.util.Date;

/**
 * <AUTHOR>
 * @Description TODO
 * @Date 2024/10/24 15:52
 * @Version V1.0
 **/

@Data
@JsonIgnoreProperties(value = { "handler" })
@NoArgsConstructor
@TableName("audit_task")
@ApiModel(value = "VcrAuditTaskEntity", description = "审核任务表")
@Accessors(chain = true)
public class VcrAuditTaskEntity implements java.io.Serializable{

    private static final long serialVersionUID = -1017890130075482559L;
    /**ObjectID*/
    @TableId(type = IdType.AUTO)
    @TableField(value = "id")
    @ApiModelProperty(value="ObjectID",dataType="Long",name="id")
    private Long id;

    /**关联ID*/
    @TableField(value = "code")
    @ApiModelProperty(value="全局唯一标识",dataType="String",name="code")
    private String code;

    /**处理结果0: 成功 其他: 错误代码*/
    @TableField(value = "result")
    @ApiModelProperty(value="审核结果,1: 待审核, 2:审核中,3:审核完成, 99:审核失败",dataType="Integer",name="result")
    private Integer result;

    /**
     * 描述信息
     */
    @TableField(value = "remarks")
    @ApiModelProperty(value = "描述信息", dataType = "String", name = "remarks")
    private String remarks;
    /**
     * 创建人
     */
    @TableField(value = "creator_name", fill = FieldFill.INSERT)
    @ApiModelProperty(value = "创建人", dataType = "String", name = "creatorName")
    private String creatorName;

    /**
     * creatorId
     */
    @TableField(value = "creator_id", fill = FieldFill.INSERT)
    @ApiModelProperty(value = "creatorId", dataType = "Long", name = "creatorId")
    private Long creatorId;


    /**
     * 创建时间
     */
    @TableField(value = "create_time", fill = FieldFill.INSERT)
    @ApiModelProperty(value = "创建时间", dataType = "String", name = "createTime")
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @JSONField
    private Date createTime;

    /**
     * 更新时间
     */
    @TableField(value = "update_time", fill = FieldFill.UPDATE)
    @ApiModelProperty(value = "更新时间", dataType = "String", name = "updateTime")
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date updateTime;

}
