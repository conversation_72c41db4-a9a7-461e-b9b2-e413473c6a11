package com.pukka.iptv.common.data.vo.req;

import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

@Data
@NoArgsConstructor
@Accessors(chain = true)
public class VirtualChannelSearchReq {
    /**
     * 媒资唯一编码
     */
    private String code;
    /**
     *  媒资名称
     */
    private String name;
    /**
     * 查询方式
     * 1：按创建时间搜索；2：按剧集名称搜索
     */
    private Integer orderflag;
}
