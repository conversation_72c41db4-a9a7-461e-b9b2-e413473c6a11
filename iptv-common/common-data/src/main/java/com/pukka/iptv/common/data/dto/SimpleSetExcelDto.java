package com.pukka.iptv.common.data.dto;

import cn.hutool.core.util.ObjectUtil;
import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelProperty;
import com.pukka.iptv.common.data.model.cms.CmsProgram;
import lombok.Data;

import java.io.Serializable;

@Data
public class SimpleSetExcelDto implements Serializable {
    @ExcelProperty("正片名称")
    private String movName;
    @ExcelProperty("预览片名称")
    private String previewName;

    @ExcelProperty("Code")
    private String code;
    /**
     * 节目名称
     */
    @ExcelProperty("媒资名称")
    private String name;
    /**
     * 媒资别名
     */
    @ExcelProperty("媒资别名")
    private String originalName;

    /**
     * 索引发布时间供页面排序
     */
    @ExcelProperty("索引排序")
    private String sortName;
    /**
     * 搜索名称，供页面搜索
     */
    @ExcelProperty("搜索标识")
    private String searchName;
    /**
     * 演员列表
     */
    @ExcelProperty("演员")
    private String kpeople;
    /**
     * 作者列表
     */
    @ExcelProperty("作者")
    private String writerDisplay;
    /**
     * 产地名称，来自于sys_dictionary_item表
     */
    @ExcelProperty("产地")
    private String originalCountry;
    /**
     * 上映年份
     */
    @ExcelProperty("上映年份")
    private String releaseYear;
    /**
     * 首播时间
     */
    @ExcelProperty("首播时间")
    private String orgAirDate;
    /**
     * 有效开始时间（YYYYMMDDHH24MiSS）
     */
    @ExcelProperty("授权开始时间")
    private String licensingWindowStart;
    /**
     * 有效结束时间（YYYYMMDDHH24MiSS）
     */
    @ExcelProperty("授权结束时间")
    private String licensingWindowEnd;
    /**
     * 节目描述
     */
    @ExcelProperty("描述")
    private String description;
    /**
     * 节目形态，如：新闻，电影
     */
    @ExcelProperty("媒资一级分类")
    private String pgmCategory;
    /**
     * 二级标签，如：动作，科幻
     */
    @ExcelProperty("媒资二级分类")
    private String pgmSndClass;
    /**
     * 列表定价
     */
    @ExcelProperty("列表定价")
    private java.math.BigDecimal priceTaxIn;
    /**
     * 导演
     */
    @ExcelProperty("导演")
    private String director;
    /**
     * 编剧
     */
    @ExcelProperty("编剧")
    private String scriptWriter;
    /**
     * 节目主持人
     */
    @ExcelProperty("媒资主持人")
    private String compere;
    /**
     * 受访者
     */
    @ExcelProperty("受访者")
    private String guest;
    /**
     * 记者
     */
    @ExcelProperty("记者")
    private String reporter;
    /**
     * 其他责任人
     */
    @ExcelProperty("其他责任人")
    private String opIncharge;
    /**
     * 版权方标识
     */
    @ExcelProperty("版权方")
    private String copyRight;
    /**
     * 内容提供商标识
     */
    @ExcelProperty("内容提供商")
    private String contentProvider;
    /**
     * 节目时长（分钟）
     */
    @ExcelProperty("时长")
    private Integer duration;
    /**
     * 评分，0 到 10，最多一位 小数
     */
    @ExcelProperty("评分")
    private String rating;
    /**
     * CP名称
     */
    @ExcelProperty("所属CP")
    private String cpName;
    /**
     * 联通价格 格式： 1;2;3;4;5 说明： 1.五个价格均 可空，用分号 隔开。极端示 例：;;;;。四个; 表示五个空的 价格。 2.五个价格顺 序固定：价格; 政企价格 1;政 企价格 2;优惠 价格 1;优惠价 格 2。
     */
    @ExcelProperty("联通价格")
    private String cuccPrice;
    /**
     * 电信价格 格式： 1;2;3;4;5 说明： 1.五个价格均 可空，用分号 隔开。极端示 例：;;;;。四个; 表示五个空的 价格。 2.五个价格顺 序固定：价格; 政企价格 1;政 企价格 2;优惠 价格 1;优惠价 格 2。
     */
    @ExcelProperty("电信价格")
    private String ctccPrice;
    /**
     * 移动价格 格式： 1;2;3;4;5 说明： 1.五个价格均 可空，用分号 隔开。极端示 例：;;;;。四个; 表示五个空的 价格。 2.五个价格顺 序固定：价格; 政企价格 1;政 企价格 2;优惠 价格 1;优惠价 格 2。
     */
    @ExcelProperty("移动价格")
    private String cmccPrice;
    /**
     * 发行方
     */
    @ExcelProperty("发行方")
    private String publisher;
    /**
     * 内容字号
     */
    @ExcelProperty("内容字号")
    private String approval;
    /**
     * 清晰度名，高清、4K、标清等
     */
    @ExcelProperty("清晰度")
    private String definitionFlag;
    /**
     * 语言
     */
    @ExcelProperty("语言")
    private String language;

    @ExcelProperty("创建人")
    private String creatorName;
    @ExcelProperty("创建人ID")
    private Long creatorId;

    /**
     * 是否为违禁片
     */
    @ExcelIgnore
    private Integer isProhibit;

    /**
     * 违禁状态
     */
    @ExcelIgnore
    private Integer prohibitStatus;

    /**
     * CP
     */
    private Long cpId;
    /**
     * 内容服务平台标识
     */
    private String vspCode;

    //清晰度
    public void def(CmsProgram cmsProgram) {
        if (ObjectUtil.isNotEmpty(definitionFlag)) {
            if (definitionFlag.equals("标清")) {
                cmsProgram.setDefinitionFlag(0);
            }
            if (definitionFlag.equals("高清")) {
                cmsProgram.setDefinitionFlag(1);
            }
            if (definitionFlag.equals("超清")) {
                cmsProgram.setDefinitionFlag(2);
            }
            if (definitionFlag.equals("4K")) {
                cmsProgram.setDefinitionFlag(3);
            }
            if (definitionFlag.equals("杜比")|| definitionFlag.equals("杜比（4K+杜比）")) {
                cmsProgram.setDefinitionFlag(4);
            }
            if(definitionFlag.equals("H264")){
                cmsProgram.setDefinitionFlag(5);
            }
            if(definitionFlag.equals("H264(标清)")){
                cmsProgram.setDefinitionFlag(6);
            }
            if(definitionFlag.equals("H264(高清)")){
                cmsProgram.setDefinitionFlag(7);
            }
            if(definitionFlag.equals("H264(4K)")){
                cmsProgram.setDefinitionFlag(8);
            }
            if(definitionFlag.equals("H265(高清)")){
                cmsProgram.setDefinitionFlag(9);
            }
            if(definitionFlag.equals("H265(4K)")){
                cmsProgram.setDefinitionFlag(10);
            }
        }else{
               cmsProgram.setDefinitionFlag(5);

        }
    }
}
