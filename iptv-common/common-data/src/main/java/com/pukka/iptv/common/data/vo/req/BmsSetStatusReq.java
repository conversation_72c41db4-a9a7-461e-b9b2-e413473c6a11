package com.pukka.iptv.common.data.vo.req;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * @Author: wz
 * @Date: 2021/11/6 20:11
 * @Description:
 */
@Setter
@Getter
@JsonIgnoreProperties(ignoreUnknown = true)
public class BmsSetStatusReq {
    @NotEmpty
    List<Long> idList;

    @NotNull
    Integer status;

    @ApiModelProperty("是否发布 true发布 false只修改")
     boolean needPublish;


}
