package com.pukka.iptv.common.data.model;

import com.baomidou.mybatisplus.annotation.*;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.NoArgsConstructor;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Date;

/**
 *
 * @author: tan
 * @date: 2021-9-1 16:08:21
 */

@Data
@EqualsAndHashCode
@JsonIgnoreProperties(value = { "handler" })
@NoArgsConstructor
@TableName(value = "out_order_item",autoResultMap=true)
public class OutOrderItem extends Model<OutOrderItem> implements java.io.Serializable{

    private static final long serialVersionUID = 1L;
	/**任务分发*/
    @TableId(type = IdType.AUTO)
	@TableField(value = "id")
    @ApiModelProperty(value="任务分发",dataType="Long",name="id")
    private Long id;
	/**baseOrderId*/
	@TableField(value = "base_order_id")
    @ApiModelProperty(value="baseOrderId",dataType="Long",name="baseOrderId")
    private Long baseOrderId;
	/**关联ID*/
	@TableField(value = "correlate_id")
    @ApiModelProperty(value="关联ID",dataType="String",name="correlateId")
    private String correlateId;
	/**动作 1：REGIST，2：UPDATE，3：DELETE*/
	@TableField(value = "action")
    @ApiModelProperty(value="动作 1：REGIST，2：UPDATE，3：DELETE",dataType="Integer",name="action")
    private Integer action;
	/**显示名称 ##IsSearch*/
	@TableField(value = "show_name")
    @ApiModelProperty(value="显示名称 ##IsSearch",dataType="String",name="showName")
    private String showName;
	/**bms的内容ID*/
	@TableField(value = "bms_content_id")
    @ApiModelProperty(value="bms的内容ID",dataType="Long",name="bmsContentId")
    private Long bmsContentId;
    /**content_code全局唯一标识*/
    @TableField(value = "bms_content_code")
    @ApiModelProperty(value="content_code全局唯一标识",dataType="String",name="bmsContentCode")
    private String bmsContentCode;
	/**1：电影 2：子集 3：电视剧 4：系列片 5：片花 6：直播 7：物理频道 8：栏目 9：产品包 10：节目单 11：图片 12：视频介质 13：节目图片 14：剧集图片 15：产品包图片 16：栏目图片 17：频道图片 18：栏目节目 19：栏目剧集 20：栏目频道 21：产品包节目 22：产品包剧集*/
	@TableField(value = "content_type")
    @ApiModelProperty(value="1：电影 2：子集 3：电视剧 4：系列片 5：片花 6：直播 7：物理频道 8：栏目 9：产品包 10：节目单 11：图片 12：视频介质 13：节目图片 14：剧集图片 15：产品包图片 16：栏目图片 17：频道图片 18：栏目节目 19：栏目剧集 20：栏目频道 21：产品包节目 22：产品包剧集",dataType="String",name="contentType")
    private String contentType;
	/**CSPID*/
	@TableField(value = "csp_id")
    @ApiModelProperty(value="CSPID",dataType="String",name="cspId")
    private String cspId;
	/**LSPID*/
	@TableField(value = "lsp_id")
    @ApiModelProperty(value="LSPID",dataType="String",name="lspId")
    private String lspId;
	/**分发通道ID，*/
	@TableField(value = "out_passage_id")
    @ApiModelProperty(value="分发通道ID，",dataType="Long",name="outPassageId")
    private Long outPassageId;
    /**分发通道code*/
    @TableField(value = "out_passage_code")
    @ApiModelProperty(value="分发通道code",dataType="String",name="outPassageCode")
    private String outPassageCode;
    /**分发通道*/
	@TableField(value = "out_passage_name")
    @ApiModelProperty(value="分发通道",dataType="String",name="outPassageName")
    private String outPassageName;
	/**工单文件*/
	@TableField(value = "cmd_file_url")
    @ApiModelProperty(value="工单文件",dataType="String",name="cmdFileUrl")
    private String cmdFileUrl;
	/**状态 1：待处理 2：处理中 3：处理成功 4：处理失败*/
	@TableField(value = "status")
    @ApiModelProperty(value="状态 1：待处理 2：处理中 3：处理成功 4：处理失败",dataType="Integer",name="status")
    private Integer status;
	/**状态描述*/
	@TableField(value = "status_description")
    @ApiModelProperty(value="状态描述",dataType="String",name="statusDescription")
    private String statusDescription;
	/**优先级 ##SelectList{1:低,5:中:8:高:10:紧急}*/
	@TableField(value = "priority")
    @ApiModelProperty(value="优先级 ##SelectList{1:低,5:中:8:高:10:紧急}",dataType="Integer",name="priority")
    private Integer priority;
	/**1：普通发布 2：队列发布*/
	@TableField(value = "publish_type")
    @ApiModelProperty(value="1：普通发布 2：队列发布",dataType="String",name="publishType")
    private String publishType;
	/**接口结果 {0:成功,-1:失败}*/
	@TableField(value = "result")
    @ApiModelProperty(value="接口结果 {0:成功,-1:失败}",dataType="Integer",name="result")
    private Integer result;
	/**错误描述*/
	@TableField(value = "error_description")
    @ApiModelProperty(value="错误描述",dataType="String",name="errorDescription")
    private String errorDescription;
    /**错误描述*/
    @TableField(value = "error_info")
    @ApiModelProperty(value="错误反馈",dataType="String",name="errorInfo")
    private String errorInfo;
	/**创建时间*/
	@TableField(value = "create_time", fill = FieldFill.INSERT)
    @ApiModelProperty(value="创建时间",dataType="String",name="createTime")
    @JsonFormat(shape = JsonFormat.Shape.STRING,pattern = "yyyy-MM-dd HH:mm:ss",timezone="GMT+8")
    private Date createTime;
	/**重发次数*/
	@TableField(value = "retry_count")
    @ApiModelProperty(value="重发次数",dataType="Integer",name="retryCount")
    private Integer retryCount;
	/**更新时间*/
	@TableField(value = "update_time", fill = FieldFill.UPDATE)
    @ApiModelProperty(value="更新时间",dataType="String",name="updateTime")
    @JsonFormat(shape = JsonFormat.Shape.STRING,pattern = "yyyy-MM-dd HH:mm:ss",timezone="GMT+8")
    private Date updateTime;
	/**操作员id*/
	@TableField(value = "creator_id", fill = FieldFill.INSERT)
    @ApiModelProperty(value="操作员id",dataType="Long",name="creatorId")
    private Long creatorId;
	/**操作员name*/
	@TableField(value = "creator_name", fill = FieldFill.INSERT)
    @ApiModelProperty(value="操作员name",dataType="String",name="creatorName")
    private String creatorName;
	/**渠道id，来自sys_dictionary_itemsl表*/
	@TableField(value = "bms_sp_channel_id")
    @ApiModelProperty(value="渠道id，来自sys _dictionary_itemsl表",dataType="Long",name="bmsSpChannelId")
    private Long bmsSpChannelId;
	/**所属渠道 */
	@TableField(value = "bms_sp_channel_name")
    @ApiModelProperty(value="所属渠道 ",dataType="String",name="bmsSpChannelName")
    private String bmsSpChannelName;
    /**所属渠道 */
    @TableField(value = "bms_sp_channel_code")
    @ApiModelProperty(value="所属渠道 ",dataType="String",name="bmsSpChannelCode")
    private String bmsSpChannelCode;
    /**13：节目图片 14：剧集图片 15：产品包图片 16：栏目图片 17：频道图片*/
    @TableField(value = "publish_content_type")
    @ApiModelProperty(value="类型 ",dataType="String",name="publishContentType")
    private String publishContentType;
    /**spName*/
    @TableField(value = "sp_name")
    @ApiModelProperty(value="类型 ",dataType="String",name="spName")
    private String spName;
    /**cpName*/
    @TableField(value = "cp_name")
    @ApiModelProperty(value="类型 ",dataType="String",name="cpName")
    private String cpName;
    /**重发时间*/
    @TableField(value = "retry_time")
    @ApiModelProperty(value="重发时间",dataType="String",name="retryTime")
    @JsonFormat(shape = JsonFormat.Shape.STRING,pattern = "yyyy-MM-dd HH:mm:ss",timezone="GMT+8")
    private Date retryTime;
    /**是否存在子节点*/
    @TableField(value = "child_node_exist")
    @ApiModelProperty(value="是否存在子节点",dataType="Integer",name="childNodeExist")
    private Integer childNodeExist;
    /**父节点唯一ID**/
    @TableField(value = "parent_correlate_id")
    @ApiModelProperty(value="父节点唯一ID ",dataType="String",name="parentCorrelateId")
    private String parentCorrelateId;
    /**发布时长(秒)**/
    @TableField(value = "duration")
    @ApiModelProperty(value="发布时长 ",dataType="Long",name="duration")
    private Integer duration;
    /**工单下游反馈完成时间*/
    @TableField(value = "feedback_time")
    @ApiModelProperty(value="工单下游反馈完成时间",dataType="String",name="feedbackTime")
    @JsonFormat(shape = JsonFormat.Shape.STRING,pattern = "yyyy-MM-dd HH:mm:ss",timezone="GMT+8")
    private Date feedbackTime;
}
