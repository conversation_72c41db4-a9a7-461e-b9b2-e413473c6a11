package com.pukka.iptv.common.data.model.cms;

import com.baomidou.mybatisplus.annotation.*;
import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.annotations.ApiModelProperty;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.NoArgsConstructor;
import lombok.EqualsAndHashCode;
import lombok.Data;

import java.util.Date;

/**
 *
 *
 * @author: zhoul
 * @date: 2021-11-3 14:33:05
 */

@Data
@EqualsAndHashCode
@JsonIgnoreProperties(value = { "handler" })
@NoArgsConstructor
@TableName(value = "cms_player_slice",autoResultMap=true)
public class CmsPlayerSlice extends Model<CmsPlayerSlice> implements java.io.Serializable{

    private static final long serialVersionUID = 1L;
	/**主键*/
	@TableField(value = "id")
    @TableId(type = IdType.AUTO)
    @ApiModelProperty(value="主键",dataType="Long",name="id")
    private Long id;
	/**描述信息*/
	@TableField(value = "description")
    @ApiModelProperty(value="描述信息",dataType="String",name="description")
    private String description;
	/**Movie表的ID*/
	@TableField(value = "movie_id")
    @ApiModelProperty(value="Movie表的ID",dataType="Long",name="movieId")
    private Long movieId;
	/**视频类型  1：正片，2预览片*/
	@TableField(value = "movie_type")
    @ApiModelProperty(value="视频类型  1：正片，2预览片",dataType="Integer",name="movieType")
    private Integer movieType;
	/**持续时长，时分秒*/
	@TableField(value = "time")
    @ApiModelProperty(value="持续时长，时分秒",dataType="String",name="time")
    private String time;
	/**开始时间，时分秒*/
	@TableField(value = "slice_begin_time")
    @ApiModelProperty(value="开始时间，时分秒",dataType="String",name="sliceBeginTime")
    private String sliceBeginTime;
	/**结束时间，时分秒*/
	@TableField(value = "slice_end_time")
    @ApiModelProperty(value="结束时间，时分秒",dataType="String",name="sliceEndTime")
    private String sliceEndTime;
	/**创建人来自admin表*/
	@TableField(value = "creator_name", fill = FieldFill.INSERT)
    @ApiModelProperty(value="创建人来自admin表",dataType="String",name="creatorName")
    private String creatorName;
	/**来自admin表*/
	@TableField(value = "creator_id", fill = FieldFill.INSERT)
    @ApiModelProperty(value="来自admin表",dataType="Long",name="creatorId")
    private Long creatorId;
	/**创建时间*/
	@TableField(value = "create_time", fill = FieldFill.INSERT)
    @ApiModelProperty(value="创建时间",dataType="String",name="createTime")
    private Date createTime;
	/**updateTime*/
	@TableField(value = "update_time", fill = FieldFill.UPDATE)
    @ApiModelProperty(value="updateTime",dataType="String",name="updateTime")
    private Date updateTime;
	/**movieCode*/
	@TableField(value = "movie_code")
    @ApiModelProperty(value="movieCode",dataType="String",name="movieCode")
    private String movieCode;
	/**cpId*/
	@TableField(value = "cp_id")
    @ApiModelProperty(value="cpId",dataType="Long",name="cpId")
    private Long cpId;
	/**cpName*/
	@TableField(value = "cp_name")
    @ApiModelProperty(value="cpName",dataType="String",name="cpName")
    private String cpName;
	/**sliceUrl*/
	@TableField(value = "slice_url")
    @ApiModelProperty(value="sliceUrl",dataType="String",name="sliceUrl")
    private String sliceUrl;
	/**sliceStorageId*/
	@TableField(value = "slice_storage_id")
    @ApiModelProperty(value="sliceStorageId",dataType="Long",name="sliceStorageId")
    private Long sliceStorageId;
	/**sliceConstantTime*/
	@TableField(value = "slice_constant_time")
    @ApiModelProperty(value="sliceConstantTime",dataType="String",name="sliceConstantTime")
    private String sliceConstantTime;
	/**sliceCode*/
	@TableField(value = "slice_code")
    @ApiModelProperty(value="sliceCode",dataType="String",name="sliceCode")
    private String sliceCode;
}
