package com.pukka.iptv.common.data.dto;

import com.baomidou.mybatisplus.annotation.TableField;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @Description 透传参数
 */
@Data
public class OutParamsDto implements Serializable {
    /**
     * CSPID
     */
    private String cspId;
    /**
     * LSPID
     */
    private String lspId;
    /**
     * 工单唯一id
     */
    private String correlateId;
    /**
     * 分发通道id,以逗号分隔
     */
    private String outPassageIds;
    /**
     * 工单地址
     */
    private String cmdFileUrl;
}
