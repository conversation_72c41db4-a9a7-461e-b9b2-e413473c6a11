package com.pukka.iptv.common.data.model.sys;

import com.baomidou.mybatisplus.annotation.*;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.NoArgsConstructor;
import lombok.EqualsAndHashCode;
import lombok.Data;

import java.util.Date;

/**
 *
 * @author: chenyudong
 * @date: 2021-9-13 17:59:15
 */

@Data
@EqualsAndHashCode
@JsonIgnoreProperties(value = { "handler" })
@NoArgsConstructor
@TableName(value = "sys_dictionary_item",autoResultMap=true)
public class SysDictionaryItem extends Model<SysDictionaryItem> implements java.io.Serializable{

    private static final long serialVersionUID = 1L;
	/**ID*/
    @TableId(type = IdType.AUTO)
	@TableField(value = "id")
    @ApiModelProperty(value="ID",dataType="Long",name="id")
    private Long id;
	/**编码*/
	@TableField(value = "code")
    @ApiModelProperty(value="编码",dataType="String",name="code")
    private String code;
	/**名称*/
	@TableField(value = "name")
    @ApiModelProperty(value="名称",dataType="String",name="name")
    private String name;
	/**序号*/
	@TableField(value = "sequence")
    @ApiModelProperty(value="序号",dataType="Long",name="sequence")
    private Long sequence;
	/**类型 1：系统 2：租户*/
	@TableField(value = "type",fill = FieldFill.INSERT)
    @ApiModelProperty(value="类型 1：系统 2：租户",dataType="Integer",name="type")
    private Integer type;
	/**状态 1：有效 2：无效 255：已删除*/
	@TableField(value = "status")
    @ApiModelProperty(value="状态 1：有效 2：无效 255：已删除",dataType="Integer",name="status")
    private Integer status;
	/**父字典ID*/
	@TableField(value = "parent_id")
    @ApiModelProperty(value="父字典ID",dataType="Long",name="parentId")
    private Long parentId;
	/**字典项ID*/
	@TableField(value = "dictionary_base_id")
    @ApiModelProperty(value="字典项ID",dataType="Long",name="dictionaryBaseId")
    private Long dictionaryBaseId;
	/**租户ID*/
	@TableField(value = "tenant_id",fill = FieldFill.INSERT)
    @ApiModelProperty(value="租户ID",dataType="Long",name="tenantId")
    private Long tenantId;
	/**作者ID*/
	@TableField(value = "creator_id", fill = FieldFill.INSERT)
    @ApiModelProperty(value="作者ID",dataType="Long",name="creatorId")
    private Long creatorId;
	/**作者*/
	@TableField(value = "creator_name", fill = FieldFill.INSERT)
    @ApiModelProperty(value="作者",dataType="String",name="creatorName")
    private String creatorName;
	/**创建时间*/
	@TableField(value = "create_time")
    @ApiModelProperty(value="创建时间",dataType="String",name="createTime")
    @JsonFormat(shape = JsonFormat.Shape.STRING,pattern = "yyyy-MM-dd HH:mm:ss",timezone="GMT+8")
    private Date createTime;
	/**更新时间*/
	@TableField(value = "update_time")
    @ApiModelProperty(value="更新时间",dataType="String",name="updateTime")
    @JsonFormat(shape = JsonFormat.Shape.STRING,pattern = "yyyy-MM-dd HH:mm:ss",timezone="GMT+8")
    private Date updateTime;
	/**备注*/
	@TableField(value = "description")
    @ApiModelProperty(value="备注",dataType="String",name="description")
    private String description;
}
