package com.pukka.iptv.common.data.vo.bms;

import com.baomidou.mybatisplus.annotation.TableField;
import com.pukka.iptv.common.data.model.bms.BmsSchedule;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

@Setter
@Getter
public class BmsSchedulePageVO extends BmsSchedule implements java.io.Serializable
{
    /**
     * 逻辑频道发布通道ID，多个ID以英文逗号隔开
     */
    @TableField(value = "channel_out_passage_ids")
    @ApiModelProperty(value = "发布通道ID，多个ID以英文逗号隔开", dataType = "String", name = "outPassageIds")
    private String channelOutPassageIds;
    /**
     * 逻辑频道分发通道名称以英文逗号 隔开
     */
    @TableField(value = "channel_out_passage_names")
    @ApiModelProperty(value = "分发通道名称以英文逗号 隔开", dataType = "String", name = "outPassageNames")
    private String channelOutPassageNames;

    /**
     * 逻辑频道发布状态  1：待发布 2：发布中 3：发布成功 4：发布失败 5：待更新 6：更新中 7：更新失败 8：回收中 9：回收成功 10：回收失败
     */
    @TableField(value = "channel_publish_status")
    @ApiModelProperty(value = "发布状态  1：待发布 2：发布中 3：发布成功 4：发布失败 5：待更新 6：更新中 7：更新失败 8：回收中 9：回收成功 10：回收失败", dataType = "Integer", name = "publishStatus")
    private Integer channelPublishStatus;

    /**
     * 逻辑频道名称
     */
    @TableField(value = "channel_name")
    @ApiModelProperty(value = "频道名称", dataType = "String", name = "name")
    private String channelName;

    @TableField(value = "channel_id")
    @ApiModelProperty(value = "频道id", dataType = "Long", name = "id")
    private Long channelId;
}
