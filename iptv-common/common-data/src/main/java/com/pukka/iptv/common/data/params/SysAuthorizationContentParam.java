package com.pukka.iptv.common.data.params;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.NoArgsConstructor;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import java.util.Date;
import lombok.Data;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;

/**
 * TODO 引入媒资参数
 *
 * <AUTHOR>
 * @date 2021/9/2 16:52
 */

@Data
@NoArgsConstructor
@AllArgsConstructor
@JsonIgnoreProperties(value = { "handler" })
@ApiModel("引入媒资参数")
public class SysAuthorizationContentParam implements Serializable {

    @ApiModelProperty(value = "",name = "idsCell")
    private List<SysAuthorizationCell> idsCell;

    @ApiModelProperty(value = "剧集id集合,用(,)分隔",name = "seriesIds")
    private List<Long> ids;
    @ApiModelProperty(value = "0-单集,1-剧集",name = "programIds")
    private Integer seriesFlag;
    @ApiModelProperty(value = "要授权的spId",name = "spId")
    private Long spId;
    @ApiModelProperty(value = "授权cpId",name = "cpId")
    private Long cpId;
    @ApiModelProperty(value = "要授权的sp名称",name = "spName")
    private String spName;
    @ApiModelProperty(value = "发布通道ID，多个ID以英文逗号隔开", dataType = "String", name = "outPassageIds")
    private String outPassageIds;
    @ApiModelProperty(value = "分发通道名称以英文逗号 隔开", dataType = "String", name = "outPassageNames")
    private String outPassageNames;
    @ApiModelProperty(value="渠道id，来自sys_dictionary_itemsl表",dataType="Long",name="bmsSpChannelId")
    private Long bmsSpChannelId;
    @ApiModelProperty(value="所属渠道 ",dataType="String",name="bmsSpChannelName")
    private String bmsSpChannelName;

    private String spIds;

    /**
     * 1 已授权  0 未授权
     */
    private Integer authorize;
}
