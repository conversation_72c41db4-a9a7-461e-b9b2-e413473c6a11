package com.pukka.iptv.common.data.model.statistics;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModelProperty;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.EqualsAndHashCode;
import lombok.Data;

/**
 *
 * @author: tan
 * @date: 2022-8-11 17:21:44
 */

@Data
@EqualsAndHashCode
@JsonIgnoreProperties(value = { "handler" })
@TableName(value = "statistics_in_again_init",autoResultMap=true)
public class StatisticsInAgainInit extends Model<StatisticsInAgainInit> implements java.io.Serializable{

    private static final long serialVersionUID = 1L;
	/**id*/
	@TableField(value = "id")
    @TableId(type = IdType.AUTO)
    @ApiModelProperty(value="id",dataType="Long",name="id")
    private Long id;
	/**需要统计的日期*/
	@TableField(value = "statistic_date")
    @ApiModelProperty(value="需要统计的日期",dataType="String",name="statisticDate")
    private String statisticDate;
	/**status*/
	@TableField(value = "status")
    @ApiModelProperty(value="status",dataType="Integer",name="status")
    private Integer status;
	/**createTime*/
	@TableField(value = "create_time")
    @ApiModelProperty(value="createTime",dataType="String",name="createTime")
    private String createTime;
	/**updateTime*/
	@TableField(value = "update_time")
    @ApiModelProperty(value="updateTime",dataType="String",name="updateTime")
    private String updateTime;
}
