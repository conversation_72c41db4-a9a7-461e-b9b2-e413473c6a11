package com.pukka.iptv.common.data.vo.req;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.pukka.iptv.common.data.vo.epg.EpgOutOrderVo;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import javax.validation.constraints.Max;
import javax.validation.constraints.Min;
import javax.validation.constraints.Pattern;

import static com.pukka.iptv.common.data.util.RegexUtil.ALL_CHAR_REGEX;


/**
 * @author: wz
 * @date: 2021/8/31 11:43
 * @description: 内容查询vo
 */
@Getter
@Setter
@JsonIgnoreProperties(ignoreUnknown = true)
public class EpgOrderQueryReq extends Page<EpgOutOrderVo> {
    /**
     * EPG模版名称
     */
    @Pattern(regexp = ALL_CHAR_REGEX + "{0,5000}")
    @ApiModelProperty(value = "templateName", dataType = "String", name = "EPG模版名称")
    private String templateName;
    /**
     * 状态
     */
    @Min(0)
    @Max(4)
    @ApiModelProperty(value = "状态标志 1:生效 0:失效", dataType = "Integer", name = "status")
    private Integer status;

    /**
     * 唯一编码
     */
    @ApiModelProperty(value = "唯一编码", dataType = "String", name = "fileSetId")
    private String fileSetId;
    /**
     * 创建人
     */
    @ApiModelProperty(value = "创建人", dataType = "String", name = "creatorName")
    private String creatorName;
    /**
     * 开始时间
     */
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private String startTime;
    /**
     * 结束时间
     */
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private String endTime;

}
