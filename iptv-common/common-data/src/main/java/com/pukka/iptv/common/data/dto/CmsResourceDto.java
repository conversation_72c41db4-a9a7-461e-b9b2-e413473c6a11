package com.pukka.iptv.common.data.dto;

import com.pukka.iptv.common.data.model.cms.CmsResource;
import lombok.Data;
import org.springframework.util.Assert;

import java.io.Serializable;

@Data
public class CmsResourceDto extends CmsResource implements Serializable {
    //1为新增 2为修改
    private Integer saveOrUpStatus;

    private String ids;


    //源片编辑参数校验
    public void validResourceSave(){
        Assert.notNull(saveOrUpStatus,"参数不能为空");
        Assert.notNull(getCpId(), "所属不能为空");
        Assert.notNull(getType(), "媒体类型不能为空");
    }
}
