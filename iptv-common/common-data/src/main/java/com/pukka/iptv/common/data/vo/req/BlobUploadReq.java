package com.pukka.iptv.common.data.vo.req;


import com.pukka.iptv.common.data.model.cms.CmsResource;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import java.io.Serializable;

/**
 * 旧组件规范，参数名不可更改
 */
@Data
public class BlobUploadReq implements Serializable {

    private static final long serialVersionUID = 31481767685592300L;

	/**
	 * cpId
	 */
	@NotBlank
	private String cpid;

	/** 视频码率，对应央视规范Video Bitrate */
	private Integer bitrate;


    /** 存储账号名称 */
    @NotBlank
	private String blob;


    /** md5加密的token,注入工单中的md5值 */
	private String token;

    /** 文件地址 */
    @NotBlank
	private String fileurl;

	
    /** blob编码后的文件地址 */ 
	private String fileurlencode;

	
    /** 媒体信息 */
    @NotBlank
	private String mediainfo;

	
    /** md5，实际读取到的md5 */
	private String md5;

	
    /** 影片时长(分钟) */ 
	private String duration;


	/** 分辨率宽*/
	private String width;

	
	/** 分辨率高*/
	private String height;

	
	/** 文件长度Byte,其实就是file size*/
	private String length;

	
	/** 帧率*/
	private String framerate;

	
	/** 文件名字 （urlencode）*/
	@NotBlank
	private String filename;




	private String vid;

    private String fileid;


	/** 影片类型:1-正片；2-预览片，xpload目前只有正片上传 */
//	@NotEmpty
	private Integer type;


	/***********************************************************************************/
	/** 以下字段为本平台独有， 注入视频才有，手动上传视频无 */

	/** 视频注入的code */
	private String code;
	/** 片头片尾 */
//	private Long movieHeadDuration;
//	private Long movieTailDuration;
//	@ApiModelProperty(value="来源1：注入， 2上传",dataType="Integer",name="source")
//	private Integer source;
//
//	@ApiModelProperty(value="媒体格式描述符, Envelope-BitrateType-VideoCodec-VideoBitrate-Resolution-FrameRate-AudioCodec-AudioBitrate",dataType="String",name="mediaSpec")
//	private String mediaSpec;
//
//	@ApiModelProperty(value="0: No DRM 1: BES DRM",dataType="Integer",name="sourceDrmType")
//	private Integer sourceDrmType;
//
//	/**0: No DRM 1: BES DRM*/
//	@ApiModelProperty(value="0: No DRM 1: BES DRM",dataType="Integer",name="destDrmType")
//	private Integer destDrmType;
//
//	/**0: 其他 1: Monaural 单声道 2: Stereo 多声道 3: Two-nation monaural 双单 声道 4: Two-nation stereo 双多声 道 5: AC3(5:1 channel) AC3 声道*/
//	@ApiModelProperty(value="0: 其他 1: Monaural 单声道 2: Stereo 多声道 3: Two-nation monaural 双单 声道 4: Two-nation stereo 双多声 道 5: AC3(5:1 channel) AC3 声道",dataType="Integer",name="audioType")
//	private Integer audioType;
//
//	/**0: 4x3 1: 16x9(Wide)*/
//	@ApiModelProperty(value="0: 4x3 1: 16x9(Wide)",dataType="Integer",name="screenFormat")
//	private Integer screenFormat;
//
//	/**字幕标志 0:无字幕 1:有字幕*/
//	@ApiModelProperty(value="字幕标志 0:无字幕 1:有字幕",dataType="Integer",name="closedCaptioning")
//	private Integer closedCaptioning;
//
//	@ApiModelProperty(value="媒体码率描述符 0：其它 1：400K 2：700K 3：1.3M 4：2M 5：2.5M 6：8M 7：10M 8：12M 9：16M 16:8M(杜比5.1) 51：1.3M（标清VBR） 52：2M（标清VBR） 53：4M（高清VBR） 54： 6M（高清VBR） 55： 2.3M（标清VBR） 56：25M（4K H264 VBR） 57：50M（4K H264 VBR） 58：15M（4K H265 VBR） 59：30M（4K H265 VBR）",dataType="Integer",name="bitRateType")
//	private Integer bitRateType;

}
