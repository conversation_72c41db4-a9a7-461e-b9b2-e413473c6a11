package com.pukka.iptv.common.data.model.baidu.vcr.response.items;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

/**
 * <AUTHOR>
 * @Description TODO
 * @Date 2024/10/25 10:40
 * @Version V1.0
 **/
@Data
@EqualsAndHashCode
@JsonIgnoreProperties(value = { "handler" })
@NoArgsConstructor
@Accessors(chain = true)
public class Location  implements java.io.Serializable{
    private static final long serialVersionUID = -6467072934958831450L;
    /**
     * 左侧偏移量，以像素为单位
     */
    private int leftOffsetInPixel;

    /**
     * 顶部偏移量，以像素为单位
     */
    private int topOffsetInPixel;

    /**
     * 区域宽度，以像素为单位
     */
    private int widthInPixel;

    /**
     * 区域高度，以像素为单位
     */
    private int heightInPixel;
}
