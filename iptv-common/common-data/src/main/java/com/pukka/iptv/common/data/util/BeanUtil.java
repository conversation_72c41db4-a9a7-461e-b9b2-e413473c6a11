package com.pukka.iptv.common.data.util;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.springframework.util.CollectionUtils;

import java.util.List;
import java.util.stream.Collectors;

/**
 * @Title: mode转换成vo工具类
 * @Author:Cha<PERSON>
 * @Date: 2021/1/12 0012 下午 21:13
 */
@Slf4j
public abstract class BeanUtil {

    /**
     * 将model集合转换成vo集合
     *
     * @param src models
     * @param destClz   目的字节码
     * @param <R>       目的
     * @param <T>       源
     * @return
     */
    public static <R, T> List<R> convertVos(List<T> src, Class<R> destClz) {
        if (CollectionUtils.isEmpty(src)) {
            return Lists.newArrayList();
        }
        List<R> collect = src.stream().filter(x->x!=null).map(x-> JSON.parseObject(JSON.toJSONString(x),destClz)).collect(Collectors.toList());
        return collect;
    }


}
