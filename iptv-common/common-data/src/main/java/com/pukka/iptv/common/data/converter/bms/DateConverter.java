package com.pukka.iptv.common.data.converter.bms;

import com.alibaba.excel.converters.Converter;
import com.alibaba.excel.enums.CellDataTypeEnum;
import com.alibaba.excel.metadata.GlobalConfiguration;
import com.alibaba.excel.metadata.data.WriteCellData;
import com.alibaba.excel.metadata.property.ExcelContentProperty;
import com.pukka.iptv.common.core.util.DateUtils;
import java.util.Date;

/**
 * @Author: wangbo
 * @Date: 2022/4/19 15:19
 */
public class DateConverter implements Converter<String> {

    @Override
    public Class supportJavaTypeKey() {
        return String.class;
    }

    @Override
    public CellDataTypeEnum supportExcelTypeKey() {
        return CellDataTypeEnum.STRING;
    }


    @Override
    public WriteCellData<?> convertToExcelData(String s, ExcelContentProperty excelContentProperty,
            GlobalConfiguration globalConfiguration) throws Exception {
        if(s == null || s.isEmpty()){
            return new WriteCellData<>("");
        }
        Date date = DateUtils.dateTime(DateUtils.YYYYMMDD,s);
        if (date == null) {
            return new WriteCellData<>("");
        }
        String dateStr = null;
        try {
            dateStr = DateUtils.getFormatDate(date, DateUtils.YYYY_MM_DD);
        } catch (Exception e) {
            dateStr = "";
        }
        return new WriteCellData<>(dateStr);
    }
}
