package com.pukka.iptv.common.data.dto;

import com.alibaba.excel.annotation.ExcelProperty;
import lombok.Data;

import java.io.Serializable;

@Data
public class SubSetExcelDto implements Serializable {

    @ExcelProperty("媒资名称")
    private String name;

    @ExcelProperty("Code")
    private String code;

    @ExcelProperty("所属CP")
    private String cpName;

    @ExcelProperty("媒资别名")
    private String originalName;

    @ExcelProperty("集数")
    private Integer episodeIndex;

    @ExcelProperty("描述")
    private String description;

    @ExcelProperty("时长")
    private Integer duration;

    @ExcelProperty("片头")
    private Long movieHeadDuration;

    @ExcelProperty("片尾")
    private Long movieTailDuration;

    @ExcelProperty("内容字号")
    private String approval;

    @ExcelProperty("正片名称")
    private String movName;

    @ExcelProperty("创建人")
    private String creatorName;

    @ExcelProperty("创建人ID")
    private Long creatorId;

    /**
     * 内容服务平台标识
     */
    private String vspCode;
}
