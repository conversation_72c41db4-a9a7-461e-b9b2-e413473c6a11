package com.pukka.iptv.common.data.dto;

import com.pukka.iptv.common.base.enums.RequestResourceEnum;
import com.pukka.iptv.common.base.enums.SourceEnum;
import com.pukka.iptv.common.data.model.cms.CmsChannel;
import com.pukka.iptv.common.data.model.cms.CmsPicture;
import com.pukka.iptv.common.data.model.in.InChannel;
import lombok.Data;
import org.springframework.beans.BeanUtils;
import org.springframework.util.Assert;

import java.io.Serializable;
import java.util.List;

@Data
public class CmsChannelDto extends CmsChannel implements Serializable {

    private List<CmsPictureDto> picArray;

    // 专线创建1  人工创建2
    private Integer requestResource;

    private String ids;

    private String cspId;

    private String httpUrl;

    public void validRequestResource(){
        Assert.notNull(requestResource, "请选择人工创建或专线创建");
    }

    // 频道新增参数校验
    public void validChannelSave() {
        if (requestResource.equals(RequestResourceEnum.SYSTEM.getCode())) {
            validChannel();
            channelNumberCheck();
            setName(getName().trim());
        }
    }


    // 频道编辑参数校验
    public void validChannelUpdate() {
        if (requestResource.equals(RequestResourceEnum.SYSTEM.getCode())) {
            Assert.notNull(getId(), "频道ID不能为空");
            validChannel();
            channelNumberCheck();
            setName(getName().trim());
        }
    }


    public CmsChannelDto(InChannel inChannel) {
        BeanUtils.copyProperties(inChannel, this, "id", "createTime", "updateTime");
        this.setSource(SourceEnum.SYSWORK.getValue());
    }

    public CmsChannelDto() {
    }


    //频道基础参数校验
    private void validChannel() {
        Assert.hasText(getName(), "频道名称不能为空");
        Assert.hasText(getChannelNumber(), "频道号不能为空");
        Assert.notNull(getCpId(), "CpId不能为空");
    }

    private void channelNumberCheck() {
        if (getChannelNumber().length() > 3) {
            throw new IllegalArgumentException("频道号长度不能超过三位");
        }
    }
}
