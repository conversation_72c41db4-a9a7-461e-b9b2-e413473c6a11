package com.pukka.iptv.common.data.dto;

import cn.hutool.core.util.ObjectUtil;
import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelProperty;
import com.pukka.iptv.common.base.enums.TeleplayExcelDtoSeriesTypeEnum;
import com.pukka.iptv.common.core.constant.CommonConstants;
import com.pukka.iptv.common.core.util.StringUtils;
import com.pukka.iptv.common.data.model.cms.CmsSeries;
import lombok.Data;

import java.io.Serializable;

@Data
public class TeleplayExcelDto implements Serializable {

    /**
     * 节目名
     */
    @ExcelProperty("媒资名称")
    private String name;

    @ExcelProperty("Code")
    private String code;
    /**
     * CP名称
     */
    @ExcelProperty("所属CP")
    private String cpName;

    /**
     * 内容提供商标识
     */
    @ExcelProperty("内容提供商")
    private String contentProvider;
    /**
     * 媒资别名
     */
    @ExcelProperty("媒资别名")
    private String originalName;
    /**
     * 总集数
     */
    @ExcelProperty("总集数")
    private Integer volumnCount;

    /**
     * 媒资一级分类
     */
    @ExcelProperty("媒资一级分类")
    private String pgmCategory;

    /**
     * 媒资二级分类
     */
    @ExcelProperty("媒资二级分类")
    private String pgmSndClass;

    /**
     * 描述信息
     */
    @ExcelProperty("描述")
    private String description;

    /**
     * 有效订购开始时间
     */
    @ExcelProperty("授权开始时间")
    private String licensingWindowStart;

    /**
     * 有效订购结束时间
     */
    @ExcelProperty("授权结束时间")
    private String licensingWindowEnd;

    @ExcelProperty("版权方")
    private String copyRight;

    /**
     * 内容字号
     */
    @ExcelProperty("内容字号")
    private String approval;

    /**
     * 发行方
     */
    @ExcelProperty("发行方")
    private String publisher;

    /**
     * 0：连续剧，1：系列片 默认连续剧
     */
    @ExcelProperty("剧集类型")
    private String seriesTypeString;

    @ExcelIgnore
    private Integer seriesType;

    /**
     * 评分，0 到 10，最多一位 小数
     */
    @ExcelProperty("评分")
    private String rating;

    /**
     * 搜索名
     */
    @ExcelProperty("搜索标识")
    private String searchName;

    /**
     * 语言
     */
    @ExcelProperty("语言")
    private String language;

    /**
     * 产地名称，来自于sys_dictionary_item表
     */
    @ExcelProperty("产地")
    private String originalCountry;

    /**
     * 排序名称
     */
    @ExcelProperty("索引排序")
    private String sortName;

    /**
     * 上映年份
     */
    @ExcelProperty("上映年份")
    private String releaseYear;

    /**
     * 首播日期
     */
    @ExcelProperty("首播时间")
    private String orgAirDate;


    @ExcelProperty("演员")
    private String kpeople;

    /**
     * 导演
     */
    @ExcelProperty("导演")
    private String director;

    /**
     * 编剧
     */
    @ExcelProperty("编剧")
    private String scriptWriter;

    /**
     * 作者列表
     */
    @ExcelProperty("作者")
    private String writerDisplay;

    /**
     * 节目 主持人
     */
    @ExcelProperty("媒资主持人")
    private String compere;

    /**
     * 受访者
     */
    @ExcelProperty("受访者")
    private String guest;

    /**
     * 记者
     */
    @ExcelProperty("记者")
    private String reporter;

    /**
     * 其他责任人
     */
    @ExcelProperty("其他责任人")
    private String opIncharge;


    /**
     * 点播的片头时长，单位秒
     */
    @ExcelProperty("片头")
    private Long movieHeadDuration;

    /**
     * 点播的片尾时长，单位秒
     */
    @ExcelProperty("片尾")
    private Long movieTailDuration;

    /**
     * 含税定价
     */
    @ExcelProperty("含税定价")
    private java.math.BigDecimal price;

    @ExcelProperty("联通价格")
    private String cuccPrice;

    @ExcelProperty("电信价格")
    private String ctccPrice;

    @ExcelProperty("移动价格")
    private String cmccPrice;

    @ExcelProperty("预览片名称")
    private String previewName;
    @ExcelProperty("创建人")
    private String creatorName;
    @ExcelProperty("创建人ID")
    private Long creatorId;

    /**
     * 清晰度名，高清、4K、标清等
     */
    @ExcelProperty("清晰度")
    private String definitionFlag;

    /**
     * 是否为违禁片
     */
    @ExcelIgnore
    private Integer isProhibit;

    /**
     * 违禁状态
     */
    @ExcelIgnore
    private Integer prohibitStatus;

    /**
     * CP
     */
    private Long cpId;
    /**
     * 内容服务平台标识
     */
    private String vspCode;

    //清晰度
    public void def(CmsSeries cmsSeries) {
        if (ObjectUtil.isNotEmpty(definitionFlag)) {
            if (definitionFlag.equals("标清")) {
                cmsSeries.setDefinitionFlag(0);
            } else if (definitionFlag.equals("高清")) {
                cmsSeries.setDefinitionFlag(1);
            } else if (definitionFlag.equals("4K超高清") || definitionFlag.equals("4K")) {
                cmsSeries.setDefinitionFlag(3);
            } else if (definitionFlag.equals("4K杜比") || definitionFlag.equals("杜比") || definitionFlag.equals("杜比（4K+杜比）")) {
                cmsSeries.setDefinitionFlag(4);
            } else {
                // 其他所有情况都归类为"其他"
                cmsSeries.setDefinitionFlag(99);
            }
        }
    }

    public void traSeriesType(){
        if (StringUtils.isNotEmpty(seriesTypeString)){
            if (seriesTypeString.equals("连续剧")){
                this.seriesType = TeleplayExcelDtoSeriesTypeEnum.TELEPLAY.getCode();
            }else if (seriesTypeString.equals("系列片")){
                this.seriesType = TeleplayExcelDtoSeriesTypeEnum.SERIAL.getCode();
            }else {
                this.seriesType = TeleplayExcelDtoSeriesTypeEnum.FAIL.getCode();
            }
        }
    }
}
