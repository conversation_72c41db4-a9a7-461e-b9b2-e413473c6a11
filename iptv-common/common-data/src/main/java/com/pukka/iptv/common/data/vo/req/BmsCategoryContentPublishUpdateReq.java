package com.pukka.iptv.common.data.vo.req;

import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * <AUTHOR>
 * @Date 2021-10-27 10:12
 */
@Getter
@Setter
public class BmsCategoryContentPublishUpdateReq {
    @ApiModelProperty("关系主键id")
    @NotEmpty
    private List<Long> ids;
    @ApiModelProperty("发布状态： 1：待发布 2：发布成功 4：待更新 ")
    @NotNull
    private Integer publishStatus;
}
