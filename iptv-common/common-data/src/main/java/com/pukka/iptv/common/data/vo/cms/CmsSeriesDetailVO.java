package com.pukka.iptv.common.data.vo.cms;

import java.util.Date;

import com.pukka.iptv.common.data.model.cms.CmsSeries;
import lombok.Data;
import com.fasterxml.jackson.annotation.JsonFormat;

import java.io.Serializable;
import java.util.List;

/**
 * 剧集详情
 */
@Data
public class CmsSeriesDetailVO extends CmsSeries implements Serializable {

    /**
     * 电信价格 格式： 1;2;3;4;5 说明： 1.五个价格均 可空，用分号 隔开。极端示 例：;;;;。四个; 表示五个空的 价格。 2.五个价格顺 序固定：价格; 政企价格 1;政 企价格 2;优惠 价格 1;优惠价 格 2。
     */
    private List<String> ctcc;

    /**
     * 联通价格 格式： 1;2;3;4;5 说明： 1.五个价格均 可空，用分号 隔开。极端示 例：;;;;。四个; 表示五个空的 价格。 2.五个价格顺 序固定：价格; 政企价格 1;政 企价格 2;优惠 价格 1;优惠价 格 2。
     */
    private List<String> cucc;

    /**
     * 移动价格 格式： 1;2;3;4;5 说明： 1.五个价格均 可空，用分号 隔开。极端示 例：;;;;。四个; 表示五个空的 价格。 2.五个价格顺 序固定：价格; 政企价格 1;政 企价格 2;优惠 价格 1;优惠价 格 2。
     */
    private List<String> cmcc;
}
