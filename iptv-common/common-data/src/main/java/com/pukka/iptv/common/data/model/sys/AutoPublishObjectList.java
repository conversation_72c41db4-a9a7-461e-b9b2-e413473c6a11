package com.pukka.iptv.common.data.model.sys;

import com.pukka.iptv.common.data.model.order.SubOrderObjectsEntity;
import lombok.Data;

import java.util.ArrayList;
import java.util.List;

/**
 * 自动发布 Object 分类
 */
@Data
public class AutoPublishObjectList {

    /* 将处理的对象分类，一个工单中action可能是多样的，并不统一，每个对象单独处理 */
    private List<SubOrderObjectsEntity> programEntities = new ArrayList<>();

    private List<SubOrderObjectsEntity> seriesEntities = new ArrayList<>();

    private List<SubOrderObjectsEntity> channelEntities = new ArrayList<>();

    private List<SubOrderObjectsEntity> physicalChannelEntities = new ArrayList<>();

    private List<SubOrderObjectsEntity> scheduleEntities = new ArrayList<>();

    private List<SubOrderObjectsEntity> categoryEntities = new ArrayList<>();

    private List<SubOrderObjectsEntity> packageEntities = new ArrayList<>();

    private List<SubOrderObjectsEntity> pictureEntities = new ArrayList<>();

    private List<SubOrderObjectsEntity> movieEntities = new ArrayList<>();

    private List<String> delProgramCodes = new ArrayList<>();

    private List<String> delSeriesCodes = new ArrayList<>();

    private List<String> delChannelCodes = new ArrayList<>();

    private List<String> delPhysicalChannelCodes = new ArrayList<>();

    private List<String> delScheduleCodes = new ArrayList<>();

    private List<String> delCategoryCodes = new ArrayList<>();

    private List<String> delPackageCodes = new ArrayList<>();

    private List<String> delPictureCodes = new ArrayList<>();

    private List<String> delMovieCodes = new ArrayList<>();

}
