package com.pukka.iptv.common.data.vo.copyright;

import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelProperty;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * 违禁艺人表
 *
 * <AUTHOR>
 * @email
 * @date 2022-07-12 18:01:29
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@JsonIgnoreProperties(value = { "handler" })
@ApiModel("ArtistProhibitImportVo")
public class ArtistProhibitImportVo implements Serializable {

    /**
     * 艺人名称
     */
    @ExcelProperty(value = "艺人姓名",index = 0)
    private String artistName;
    /**
     * 艺人性别
     */
    @ExcelProperty(value = "艺人性别",index = 1)
    private String sex;
    @ExcelIgnore
    private Integer artistSex;
    /**
     * 出生日期（YYYYMMDDHH24MiSS）
     */
    @ExcelProperty(value = "出生年月",index = 2)
    private String birthday;
    /**
     * 简介
     */
    @ExcelProperty(value = "简介",index = 3)
    private String description;
    /**
     * 封杀原因
     */
    @ExcelProperty(value = "封杀原因",index = 4)
    private String prohibitCause;

}
