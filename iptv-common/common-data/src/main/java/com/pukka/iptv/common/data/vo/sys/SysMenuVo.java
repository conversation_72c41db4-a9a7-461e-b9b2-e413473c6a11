package com.pukka.iptv.common.data.vo.sys;

import com.pukka.iptv.common.data.model.sys.SysMenu;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;
import lombok.NoArgsConstructor;
import java.util.Date;
import lombok.Data;
import com.fasterxml.jackson.annotation.JsonFormat;

import java.util.ArrayList;
import java.util.List;

/**
 *
 * @author: zhengcl
 * @date: 2021-8-18 10:37:52
 */

@Data
@AllArgsConstructor
@JsonIgnoreProperties(value = { "handler" })
@ApiModel("SysMenuVo")
public class SysMenuVo extends SysMenu implements java.io.Serializable{

}
