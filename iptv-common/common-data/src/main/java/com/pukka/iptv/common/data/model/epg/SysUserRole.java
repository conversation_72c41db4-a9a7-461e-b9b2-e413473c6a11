package com.pukka.iptv.common.data.model.epg;

import com.baomidou.mybatisplus.annotation.*;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import java.util.Date;

/**
 *
 * @author: zhengcl
 * @date: 2021-8-17 18:55:49
 */

@Data
@EqualsAndHashCode
@JsonIgnoreProperties(value = { "handler" })
@NoArgsConstructor
@TableName(value = "sys_user_role",autoResultMap=true)
public class SysUserRole extends Model<SysUserRole> implements java.io.Serializable{

    private static final long serialVersionUID = 1L;
	/**ID*/
    @TableId(type = IdType.AUTO)
	@TableField(value = "id")
    @ApiModelProperty(value="ID",dataType="Long",name="id")
    private Long id;
	/**角色ID*/
	@TableField(value = "role_id")
    @ApiModelProperty(value="角色ID",dataType="Long",name="roleId")
    private Long roleId;
	/**菜单ID*/
	@TableField(value = "user_id")
    @ApiModelProperty(value="菜单ID",dataType="Long",name="userId")
    private Long userId;
	/**createTime*/
	@TableField(value = "create_time", fill = FieldFill.INSERT)
    @ApiModelProperty(value="createTime",dataType="String",name="createTime")
    @JsonFormat(shape = JsonFormat.Shape.STRING,pattern = "yyyy-MM-dd HH:mm:ss",timezone="GMT+8")
    private Date createTime;
	/**updateTime*/
	@TableField(value = "update_time")
    @ApiModelProperty(value="updateTime",dataType="String",name="updateTime")
    @JsonFormat(shape = JsonFormat.Shape.STRING,pattern = "yyyy-MM-dd HH:mm:ss",timezone="GMT+8")
    private Date updateTime;
	/**description*/
	@TableField(value = "description")
    @ApiModelProperty(value="description",dataType="String",name="description")
    private String description;
}
