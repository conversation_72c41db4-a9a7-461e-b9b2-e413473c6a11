package com.pukka.iptv.common.data.model.order;

import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @Date 2021/8/31 3:54 下午
 * @description: xml信息体
 * @Version 1.0
 */
@Getter
@Setter
@ToString
public class SubOrderXmlEntity implements Serializable {
    private static final long serialVersionUID = 2659436112407185304L;
    /**
     * xml文件名信息
     */
    private String xmlDestination;
    /**
     * 1：电影 2：子集 3：电视剧 4：系列片 5：片花 6：直播 7：物理频道 8：栏目 9：产品包 10：节目单 11：图片 12：视频介质 13：节目图片 14：剧集图片 15：产品包图片 16：栏目图片 17：频道图片 18：栏目节目 19：栏目剧集 20：栏目频道 21：产品包节目 22：产品包剧集
     */
    private Integer contentType;
    /**
     * 对象上游cpid
     */
    private Long cpId;
    /**
     * 主工单xml文件地址
     */
    private String cmdFilePath;
    /**
     * 发布内容带图片下发
     * 1：是
     * 2：否
     */
    private Integer publishWithPicture;
    /**
     * 发布内容带视频下发
     * 1：是
     * 2：否
     */
    private Integer publishWithMovie;
    /**
     * object
     */
    private List<SubOrderObjectsEntity> subOrderObjectsEntities;
    /**
     * mapping
     */
    private List<SubOrderMappingsEntity> subOrderMappingsEntities;
    /**
     * 补充参数
     */
    private Map<String, String> param;

    public SubOrderXmlEntity() {
        this.subOrderObjectsEntities = new ArrayList<>();
        this.subOrderMappingsEntities = new ArrayList<>();
        param = new HashMap<>();
    }
}
