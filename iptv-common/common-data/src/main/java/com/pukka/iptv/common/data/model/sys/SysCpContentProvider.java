package com.pukka.iptv.common.data.model.sys;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModelProperty;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.NoArgsConstructor;
import lombok.EqualsAndHashCode;
import lombok.Data;

/**
 *
 * @author: chenyudong
 * @date: 2021-12-2 15:04:04
 */

@Data
@EqualsAndHashCode
@JsonIgnoreProperties(value = { "handler" })
@NoArgsConstructor
@TableName(value = "sys_cp_content_provider",autoResultMap=true)
public class SysCpContentProvider extends Model<SysCpContentProvider> implements java.io.Serializable{

    private static final long serialVersionUID = 1L;
	/**内容提供商ID*/
	@TableField(value = "id")
    @TableId(type = IdType.AUTO)
    @ApiModelProperty(value="内容提供商ID",dataType="Long",name="id")
    private Long id;
	/**内容提供商名称*/
	@TableField(value = "name")
    @ApiModelProperty(value="内容提供商名称",dataType="String",name="name")
    private String name;
	/**编码*/
	@TableField(value = "code")
    @ApiModelProperty(value="编码",dataType="String",name="code")
    private String code;
	/**cpId*/
	@TableField(value = "cp_id")
    @ApiModelProperty(value="cpId",dataType="Long",name="cpId")
    private Long cpId;
	/**cp名称*/
	@TableField(value = "cp_name")
    @ApiModelProperty(value="cp名称",dataType="String",name="cpName")
    private String cpName;
	/**类型 1：可用 2：停用*/
	@TableField(value = "status")
    @ApiModelProperty(value="类型 1：可用 2：停用",dataType="Integer",name="status")
    private Integer status;
	/**createTime*/
	@TableField(value = "create_time")
    @ApiModelProperty(value="createTime",dataType="String",name="createTime")
    private String createTime;
	/**updateTime*/
	@TableField(value = "update_time")
    @ApiModelProperty(value="updateTime",dataType="String",name="updateTime")
    private String updateTime;
}
