package com.pukka.iptv.common.data.vo.bms;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import io.swagger.annotations.ApiModel;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.List;

@Getter
@Setter
@JsonIgnoreProperties(value = { "handler" })
@NoArgsConstructor
@ApiModel("BmsPhysicalChannelQueryVO")
public class BmsPhysicalChannelQueryVO implements Serializable
{
    /**
     * bmschannel主键ids
     */
    @NotNull
    private List<Long> channelIds;
    @NotNull
    private Long spId;

    /**
     * 1  发布； 2  更新； 3  回收；
     */
    @NotNull
    private Integer action;
}
