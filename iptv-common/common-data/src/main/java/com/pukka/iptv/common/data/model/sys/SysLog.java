package com.pukka.iptv.common.data.model.sys;

import com.baomidou.mybatisplus.annotation.*;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.NoArgsConstructor;
import lombok.EqualsAndHashCode;
import lombok.Data;

import java.util.Date;

/**
 *
 * @author: chenyudong
 * @date: 2021-09-29 14:50:58
 */

@Data
@EqualsAndHashCode
@JsonIgnoreProperties(value = { "handler" })
@NoArgsConstructor
@TableName(value = "sys_log",autoResultMap=true)
public class SysLog extends Model<SysLog> implements java.io.Serializable{

    private static final long serialVersionUID = 1L;
	/**id*/
    @TableId(type = IdType.AUTO)
	@TableField(value = "id")
    @ApiModelProperty(value="id",dataType="Long",name="id")
    private Long id;
	/**操作类型*/
	@TableField(value = "operate_type")
    @ApiModelProperty(value="操作类型",dataType="String",name="operateType")
    private String operateType;
	/**对象名称*/
	@TableField(value = "object_names")
    @ApiModelProperty(value="对象名称",dataType="String",name="objectNames")
    private String objectNames;
	/**对象ID*/
	@TableField(value = "object_ids")
    @ApiModelProperty(value="对象ID",dataType="String",name="objectIds")
    private String objectIds;
	/**对象类型*/
	@TableField(value = "object_type")
    @ApiModelProperty(value="对象类型",dataType="String",name="objectType")
    private String objectType;
	/**请求方法*/
	@TableField(value = "request_function")
    @ApiModelProperty(value="请求方法",dataType="String",name="requestFunction")
    private String requestFunction;
	/**操作结果*/
	@TableField(value = "operate_result")
    @ApiModelProperty(value="操作结果",dataType="String",name="operateResult")
    private String operateResult;
    /**创建人*/
    @TableField(value = "creator_name", fill = FieldFill.INSERT)
    @ApiModelProperty(value="创建人",dataType="String",name="creatorName")
    private String creatorName;

    /**creatorId*/
    @TableField(value = "creator_id", fill = FieldFill.INSERT)
    @ApiModelProperty(value="creatorId",dataType="Long",name="creatorId")
    private Long creatorId;
	/**客户端地址*/
	@TableField(value = "client_address")
    @ApiModelProperty(value="客户端地址",dataType="String",name="clientAddress")
    private String clientAddress;
	/**服务端地址*/
	@TableField(value = "server_address")
    @ApiModelProperty(value="服务端地址",dataType="String",name="serverAddress")
    private String serverAddress;
    /**创建时间*/
    @TableField(value = "create_time", fill = FieldFill.INSERT)
    @ApiModelProperty(value="创建时间",dataType="String",name="createTime")
    @JsonFormat(shape = JsonFormat.Shape.STRING,pattern = "yyyy-MM-dd HH:mm:ss",timezone="GMT+8")
    private Date createTime;
    /**更新时间*/
    @TableField(value = "update_time", fill = FieldFill.UPDATE)
    @ApiModelProperty(value="更新时间",dataType="String",name="updateTime")
    @JsonFormat(shape = JsonFormat.Shape.STRING,pattern = "yyyy-MM-dd HH:mm:ss",timezone="GMT+8")
    private Date updateTime;
	/**描述*/
	@TableField(value = "description")
    @ApiModelProperty(value="描述",dataType="String",name="description")
    private String description;
	/**详情*/
	@TableField(value = "detail")
    @ApiModelProperty(value="详情",dataType="String",name="detail")
    private String detail;
	/**异常*/
	@TableField(value = "exception")
    @ApiModelProperty(value="异常",dataType="String",name="exception")
    private String exception;
	/**请求耗时*/
	@TableField(value = "duration")
    @ApiModelProperty(value="请求耗时",dataType="Long",name="duration")
    private Long duration;
	/**请求参数*/
	@TableField(value = "request_param")
    @ApiModelProperty(value="请求参数",dataType="String",name="requestParam")
    private String requestParam;
    /**请求方式*/
    @TableField(value = "request_method")
    @ApiModelProperty(value="请求方式",dataType="String",name="requestMethod")
    private String requestMethod;
    /**请求方式*/
    @TableField(value = "creator_username")
    @ApiModelProperty(value="操作员账号",dataType="String",name="creatorUsername")
    private String creatorUsername;

    @TableField(exist = false)
    @ApiModelProperty(value="开始时间",dataType="String",name="startTime")
    private String startTime;

    @TableField(exist = false)
    @ApiModelProperty(value="开始时间",dataType="String",name="startTime")
    private String endTime;

    @TableField(exist = false)
    @ApiModelProperty(value="来源",dataType="String",name="source")
    private Integer source;
}
