package com.pukka.iptv.common.data.vo.req;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import io.swagger.annotations.ApiModel;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

/**
 * @Author: chiron
 * @Date: 2022/07/15/11:31
 * @Description:
 */
@Data
@JsonIgnoreProperties(value = { "handler" })
@ApiModel("QueryContentReq")
@NoArgsConstructor
@Accessors(chain = true)
public class QueryContentReq {
    /**
     * 媒资code
     */
    private String code;
    /**
     * 节目类型
     */
    private Integer contentType;
}
