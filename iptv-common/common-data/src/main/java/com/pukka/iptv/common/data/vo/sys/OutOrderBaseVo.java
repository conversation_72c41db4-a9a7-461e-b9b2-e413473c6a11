package com.pukka.iptv.common.data.vo.sys;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.pukka.iptv.common.data.model.OutOrderBase;
import com.pukka.iptv.common.data.model.order.SubOrderXmlEntity;
import io.swagger.annotations.ApiModel;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * @author: tianzm
 * @date: 2021-9-1 16:08:20
 */

@Data
@EqualsAndHashCode
@NoArgsConstructor
@ApiModel("OutOrderBaseVo")
@JsonIgnoreProperties(ignoreUnknown = true)
public class OutOrderBaseVo extends OutOrderBase implements java.io.Serializable {
    /**
     * 开始时间
     */
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private String startTime;
    /**
     * 结束时间
     */
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private String endTime;

    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private String startLaunchTime;

    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private String endLaunchTime;


    /**
     * content_code全局唯一标识
     */
    private String bmsContentCode;

    /**
     * 重试次数
     */
    private Integer retryCount;

    /**
     * 兼容UT extraCode
     */
    private String extraCode;

    /**
     * 优先级
     */
    private Integer priority;

    /**
     * 剧集子集：剧集子工单集合
     */
    private List<OutOrderItemVo> itemVoList;

    /**
     * xml文件实体
     */
    private SubOrderXmlEntity subOrderXmlEntity;

    /**
     * CSPID
     */
    private String cspId;

    /**
     * 当包含换行符时，是多名称查询
     */
    private String[] nameList;

    /**
     * 模糊查询
     */
    private String nameLike;

    /**
     * 模糊精确查询标志
     */
    private Integer likeOrinFlag;

    /**
     * 查询上线时间是否为空标识  1：不为空 2：为空
     */
    private Integer firstLaunchTimeIsNull;
}
