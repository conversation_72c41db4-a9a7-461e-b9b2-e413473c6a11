package com.pukka.iptv.common.data.model.cms;

import com.baomidou.mybatisplus.annotation.*;
import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.annotations.ApiModelProperty;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.NoArgsConstructor;
import lombok.EqualsAndHashCode;
import java.util.Date;
import lombok.Data;
import com.fasterxml.jackson.annotation.JsonFormat;

/**
 * 图片
 *
 * @author: zhoul
 * @date: 2021-8-30 10:58:58
 */

@Data
@EqualsAndHashCode
@JsonIgnoreProperties(value = { "handler" })
@NoArgsConstructor
@TableName(value = "cms_picture",autoResultMap=true)
public class CmsPicture extends Model<CmsPicture> implements java.io.Serializable{

    private static final long serialVersionUID = 1L;
	/**主键*/
	@TableField(value = "id")
    @TableId(type = IdType.AUTO)
    @ApiModelProperty(value="主键",dataType="Long",name="id")
    private Long id;
	/**全局唯一标识*/
    @TableField(value = "code", fill = FieldFill.INSERT)
    @ApiModelProperty(value="全局唯一标识",dataType="String",name="code")
    private String code;
	/**图片文件 URL*/
	@TableField(value = "file_url")
    @ApiModelProperty(value="图片文件 URL",dataType="String",name="fileUrl")
    private String fileUrl;
    /**文件大小 单位 KB*/
    @TableField(value = "file_size")
    @ApiModelProperty(value="文件大小",dataType="Integer",name="fileSize")
    private Long fileSize;
	/**描述*/
	@TableField(value = "description")
    @ApiModelProperty(value="描述",dataType="String",name="description")
    private String description;
	/**1：电影 2：子集 3：电视剧 4：系列片 5：片花 6：直播 7：物理频道 8：栏目 9：产品包 10：节目单*/
	@TableField(value = "content_type")
    @ApiModelProperty(value="1：电影 2：子集 3：电视剧 4：系列片 5：片花 6：直播 7：物理频道 8：栏目 9：产品包 10：节目单",dataType="Integer",name="contentType")
    private Integer contentType;
	/**节目code*/
	@TableField(value = "content_code")
    @ApiModelProperty(value="节目code",dataType="String",name="contentCode")
    private String contentCode;
	/**节目ID，有可能来自于program，series，channel等表，需指定类型才可用*/
	@TableField(value = "content_id")
    @ApiModelProperty(value="节目ID，有可能来自于program，series，channel等表，需指定类型才可用",dataType="Long",name="contentId")
    private Long contentId;
	/**cpId*/
	@TableField(value = "cp_id")
    @ApiModelProperty(value="cpId",dataType="Long",name="cpId")
    private Long cpId;
	/**CP名称*/
	@TableField(value = "cp_name")
    @ApiModelProperty(value="CP名称",dataType="String",name="cpName")
    private String cpName;
	/**创建时间*/
    @TableField(value = "create_time", fill = FieldFill.INSERT)
    @ApiModelProperty(value="创建时间",dataType="String",name="createTime")
    @JsonFormat(shape = JsonFormat.Shape.STRING,pattern = "yyyy-MM-dd HH:mm:ss",timezone="GMT+8")
    private Date createTime;
	/**更新时间*/
	@TableField(value = "update_time",fill = FieldFill.INSERT_UPDATE)
    @ApiModelProperty(value="更新时间",dataType="String",name="updateTime")
    @JsonFormat(shape = JsonFormat.Shape.STRING,pattern = "yyyy-MM-dd HH:mm:ss",timezone="GMT+8")
    private Date updateTime;
	/**图片类型
     0: 缩略图
     1: 海报
     2: 剧照
     3: 图标
     4: 标题图
     5: 广告图
     6: 草图
     7: 背景图
     9: 频道图片
     10: 频道黑白图片
     11: 频道 Logo
     12: 频道名字图片
     20：首页推荐位 1
     21：首页推荐位 2
     22：首页推荐位 3
     23：专区推荐位 1
     24：专区推荐位 2
     25：专区推荐位 3
     26：海报 2
     27：专题海报
     28：剧照 1
     29：底图
     99: 其他*/
	@TableField(value = "type")
    @ApiModelProperty(value="图片类型  0: 缩略图 1: 海报 2: 剧照 3: 图标 4: 标题图 5: 广告图 6: 草图 7: 背景图 9: 频道图片 10: 频道黑白图片 11: 频道 Logo 12: 频道名字图片 20：首页推荐位 1  21：首页推荐位 2  22：首页推荐位 3  23：专区推荐位 1  24：专区推荐位 2  25：专区推荐位 3  26：海报 2   27：专题海报  28：剧照 1  29：底图  99: 其他",dataType="Integer",name="type")
    private Integer type;
	/**状态1：有效 255：删除*/
	@TableField(value = "status")
    @ApiModelProperty(value="状态1：有效 255：删除",dataType="Integer",name="status")
    private Integer status;
	/**分辨率 1280：720  */
	@TableField(value = "ratio")
    @ApiModelProperty(value="分辨率 1280：720  ",dataType="String",name="ratio")
    private String ratio;
	/**序号*/
	@TableField(value = "sequence")
    @ApiModelProperty(value="序号",dataType="Long",name="sequence")
    private Integer sequence;
	/**创建人，只人工上传时可用*/
    @TableField(value = "creator_name", fill = FieldFill.INSERT)
    @ApiModelProperty(value="创建人，只人工上传时可用",dataType="String",name="creatorName")
    private String creatorName;
	/**创建人ID，来自于user表*/
    @TableField(value = "creator_id", fill = FieldFill.INSERT)
    @ApiModelProperty(value="创建人ID，来自于user表",dataType="Long",name="creatorId")
    private Long creatorId;
    /**所属存储*/
    @TableField(value = "storage_name")
    @ApiModelProperty(value="所属存储",dataType="String",name="storageName")
    private String storageName;
    /**存储ID*/
    @TableField(value = "storage_id")
    @ApiModelProperty(value="存储ID",dataType="Long",name="storageId")
    private Long storageId;
}
