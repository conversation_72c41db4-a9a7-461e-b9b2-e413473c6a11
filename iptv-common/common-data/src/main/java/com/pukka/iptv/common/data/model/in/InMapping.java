package com.pukka.iptv.common.data.model.in;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.NoArgsConstructor;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Date;

/**
 *
 * @author: tan
 * @date: 2021-8-31 15:24:05
 */

@Data
@EqualsAndHashCode
@JsonIgnoreProperties(value = { "handler" })
@NoArgsConstructor
@TableName(value = "in_mapping",autoResultMap=true)
public class InMapping extends Model<InMapping> implements java.io.Serializable{

    private static final long serialVersionUID = 1L;
	/**id*/
	@TableField(value = "id")
    @TableId(type = IdType.AUTO)
    @ApiModelProperty(value="id",dataType="Long",name="id")
    private Long id;
	/**父元素类型*/
	@TableField(value = "parent_type")
    @ApiModelProperty(value="父元素类型",dataType="String",name="parentType")
    private String parentType;
	/**元素类型*/
	@TableField(value = "element_type")
    @ApiModelProperty(value="元素类型",dataType="String",name="elementType")
    private String elementType;
	/**父元素id*/
	@TableField(value = "parent_id")
    @ApiModelProperty(value="父元素id",dataType="String",name="parentId")
    private String parentId;
	/**元素id*/
	@TableField(value = "element_id")
    @ApiModelProperty(value="元素id",dataType="String",name="elementId")
    private String elementId;
	/**父元素 Code(全局唯一) */
	@TableField(value = "parent_code")
    @ApiModelProperty(value="父元素 Code(全局唯一) ",dataType="String",name="parentCode")
    private String parentCode;
	/**元素 Code(全局唯一)*/
	@TableField(value = "element_code")
    @ApiModelProperty(value="元素 Code(全局唯一)",dataType="String",name="elementCode")
    private String elementCode;
	/**映射时的类型 当 Mapping 的 ParentType 为 Picture 时： 0: 缩略图 1: 海报 2: 剧照 3: 图标 4: 标题图 5: 广告图 6: 草图 7: 背景图 9: 频道图片 10: 频道黑白图片 11: 频道 Logo 12: 频道名字图片 20：首页推荐位 1 21：首页推荐位 2 22：首页推荐位 3 23：专区推荐位 1 24：专区推荐位 2 25：专区推荐位 3 2Page 17 of 23 26：海报 2 27：专题海报 28：剧照 1 29：底图 99: 其他*/
	@TableField(value = "type")
    @ApiModelProperty(value="映射时的类型 当 Mapping 的 ParentType 为 Picture 时： 0: 缩略图 1: 海报 2: 剧照 3: 图标 4: 标题图 5: 广告图 6: 草图 7: 背景图 9: 频道图片 10: 频道黑白图片 11: 频道 Logo 12: 频道名字图片 20：首页推荐位 1 21：首页推荐位 2 22：首页推荐位 3 23：专区推荐位 1 24：专区推荐位 2 25：专区推荐位 3 2Page 17 of 23 26：海报 2 27：专题海报 28：剧照 1 29：底图 99: 其他",dataType="Integer",name="type")
    private Integer type;
	/**当 Mapping 的 ParentType 为 Package(SVOD)时, 标识 SVOD 节目的服务起始时间 (YYYYMMDDHH24MiSS)*/
	@TableField(value = "valid_start")
    @ApiModelProperty(value="当 Mapping 的 ParentType 为 Package(SVOD)时, 标识 SVOD 节目的服务起始时间 (YYYYMMDDHH24MiSS)",dataType="String",name="validStart")
    private String validStart;
	/**当 Mapping 的 ParentType 为 Package(SVOD)时, 标识 SVOD 节目的服务终止时间 (YYYYMMDDHH24MiSS)*/
	@TableField(value = "valid_end")
    @ApiModelProperty(value="当 Mapping 的 ParentType 为 Package(SVOD)时, 标识 SVOD 节目的服务终止时间 (YYYYMMDDHH24MiSS)",dataType="String",name="validEnd")
    private String validEnd;
	/**序列号*/
	@TableField(value = "sequence")
    @ApiModelProperty(value="序列号",dataType="Integer",name="sequence")
    private Integer sequence;
	/**应答文件包含，0: 成功 其他: 错误代码*/
	@TableField(value = "result")
    @ApiModelProperty(value="应答文件包含，0: 成功 其他: 错误代码",dataType="Integer",name="result")
    private Integer result;
	/**应答文件包含，错误描述*/
	@TableField(value = "error_description")
    @ApiModelProperty(value="应答文件包含，错误描述",dataType="String",name="errorDescription")
    private String errorDescription;
	/**动作 1：REGIST，2：UPDATE，3：DELETE*/
	@TableField(value = "action")
    @ApiModelProperty(value="动作 1：REGIST，2：UPDATE，3：DELETE",dataType="Integer",name="action")
    private Integer action;
	/**correlateId*/
	@TableField(value = "correlate_id")
    @ApiModelProperty(value="correlateId",dataType="String",name="correlateId")
    private String correlateId;
	/**父元素处理结果 1：待处理 2：处理中 3：处理完成 4：处理失败*/
	@TableField(value = "parent_status")
    @ApiModelProperty(value="父元素处理结果 1：待处理 2：处理中 3：处理完成 4：处理失败",dataType="Integer",name="parentStatus")
    private Integer parentStatus;
	/**子元素处理结果 1：待处理 2：处理中 3：处理完成 4：处理失败*/
	@TableField(value = "element_status")
    @ApiModelProperty(value="子元素处理结果 1：待处理 2：处理中 3：处理完成 4：处理失败",dataType="Integer",name="elementStatus")
    private Integer elementStatus;
	/**1：有效   2：失效*/
	@TableField(value = "status")
    @ApiModelProperty(value="1：有效   2：失效",dataType="Integer",name="status")
    private Integer status;
	/**createTime*/
	@TableField(value = "create_time")
    @ApiModelProperty(value="createTime",dataType="String",name="createTime")
    @JsonFormat(shape = JsonFormat.Shape.STRING,pattern = "yyyy-MM-dd HH:mm:ss",timezone="GMT+8")
    private Date createTime;
	/**updateTime*/
	@TableField(value = "update_time")
    @ApiModelProperty(value="updateTime",dataType="String",name="updateTime")
    @JsonFormat(shape = JsonFormat.Shape.STRING,pattern = "yyyy-MM-dd HH:mm:ss",timezone="GMT+8")
    private Date updateTime;
}
