package com.pukka.iptv.common.data.model.baidu.vcr.response.items;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

/**
 * <AUTHOR>
 * @Description TODO
 * @Date 2024/10/25 10:42
 * @Version V1.0
 **/
@Data
@EqualsAndHashCode
@JsonIgnoreProperties(value = { "handler" })
@NoArgsConstructor
@Accessors(chain = true)
public class Evidence implements java.io.Serializable {
    private static final long serialVersionUID = -3650992046511573146L;

    /**
     * 缩略图的 URL 地址
     */
    private String thumbnail;

    /**
     * 位置，表示图像中的具体区域
     */
    private Location location;
}
