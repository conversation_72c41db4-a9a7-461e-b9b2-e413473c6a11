package com.pukka.iptv.common.data.model.cms;

import com.baomidou.mybatisplus.annotation.*;
import io.swagger.annotations.ApiModelProperty;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.NoArgsConstructor;
import lombok.EqualsAndHashCode;
import java.util.Date;
import lombok.Data;
import com.fasterxml.jackson.annotation.JsonFormat;

import javax.validation.constraints.Max;


/**
 *
 * @author: luo
 * @date: 2021-8-31 17:15:56
 */

@Data
@EqualsAndHashCode
@JsonIgnoreProperties(value = { "handler" })
@NoArgsConstructor
@TableName(value = "cms_schedule",autoResultMap=true)
public class CmsSchedule extends Model<CmsSchedule> implements java.io.Serializable{

    private static final long serialVersionUID = 1L;
	/**主键*/
    @TableId(type= IdType.AUTO)
	@TableField(value = "id")
    @ApiModelProperty(value="主键",dataType="Long",name="id")
    private Long id;
	/**全局唯一标识*/
	@Max(value = 32,message = "Code不能超过32位")
    @TableField(value = "code", fill = FieldFill.INSERT)
    @ApiModelProperty(value="全局唯一标识",dataType="String",name="code")
    private String code;
	/**逻辑频道 Code*/
    @Max(value = 32,message = "逻辑频道Code不能超过32位")
	@TableField(value = "channel_code")
    @ApiModelProperty(value="逻辑频道 Code",dataType="String",name="channelCode")
    private String channelCode;
	/**频道名称*/
    @Max(value = 32,message = "频道名称不能超过32位")
	@TableField(value = "channel_name")
    @ApiModelProperty(value="频道名称",dataType="String",name="channelName")
    private String channelName;
	/**逻辑频道 ID*/
	@TableField(value = "channel_id")
    @ApiModelProperty(value="逻辑频道 ID",dataType="Long",name="channelId")
    private Long channelId;
	/**节目名称*/
    @Max(value = 128,message = "节目名称不能超过128位")
	@TableField(value = "program_name")
    @ApiModelProperty(value="节目名称",dataType="String",name="programName")
    private String programName;
	/**节目开播日期*/
    @Max(value = 10,message = "节目开播日期不能超过10位")
	@TableField(value = "start_date")
    @ApiModelProperty(value="节目开播日期",dataType="String",name="startDate")
    private String startDate;
	/**节目开播时间*/
    @Max(value = 10,message = "节目开播时间不能超过10位")
	@TableField(value = "start_time")
    @ApiModelProperty(value="节目开播时间",dataType="String",name="startTime")
    private String startTime;
    /**节目结束时间*/
    @Max(value = 10,message = "节目结束时间不能超过10位")
    @TableField(value = "end_time")
    @ApiModelProperty(value="节目结束时间",dataType="String",name="endTime")
    private String endTime;
	/**节目时长(HH24MISS) */
    @Max(value = 10,message = "节目时长不能超过10位")
	@TableField(value = "duration")
    @ApiModelProperty(value="节目时长(HH24MISS) ",dataType="String",name="duration")
    private String duration;
	/**TVOD 保存时长(小时) 缺省为空*/
	@TableField(value = "storage_duration",updateStrategy=FieldStrategy.IGNORED)
    @ApiModelProperty(value="TVOD 保存时长(小时) 缺省为空",dataType="Long",name="storageDuration")
    private Integer storageDuration;
	/**状态标志 1:生效 2:失效 */
	@TableField(value = "status")
    @ApiModelProperty(value="状态标志 1:生效 2:失效 ",dataType="Integer",name="status")
    private Integer status;
	/**描述信息*/
    @Max(value = 1024,message = "描述信息不能超过1024位")
	@TableField(value = "description")
    @ApiModelProperty(value="描述信息",dataType="String",name="description")
    private String description;
	/**来源 1：专线注入 2：人工创建*/
	@TableField(value = "source")
    @ApiModelProperty(value="来源 1：专线注入 2：人工创建",dataType="Integer",name="source")
    private Integer source;
	/**节目的分类标签，如“体育”， 多个标签用空格或“;”区分*/
    @Max(value = 128,message = "节目的分类标签不能超过128位")
	@TableField(value = "genre")
    @ApiModelProperty(value="节目的分类标签，如“体育”， 多个标签用空格或“;”区分",dataType="String",name="genre")
    private String genre;
	/**cpId*/
	@TableField(value = "cp_id")
    @ApiModelProperty(value="cpId",dataType="Long",name="cpId")
    private Long cpId;
	/**CP名称*/
    @Max(value = 64,message = "CP名称不能超过64位")
	@TableField(value = "cp_name")
    @ApiModelProperty(value="CP名称",dataType="String",name="cpName")
    private String cpName;
	/**创建时间*/
	@TableField(value = "create_time")
    @ApiModelProperty(value="创建时间",dataType="String",name="createTime")
    @JsonFormat(shape = JsonFormat.Shape.STRING,pattern = "yyyy-MM-dd HH:mm:ss",timezone="GMT+8")
    private Date createTime;
	/**更新时间*/
	@TableField(value = "update_time",fill = FieldFill.INSERT_UPDATE)
    @ApiModelProperty(value="更新时间",dataType="String",name="updateTime")
    @JsonFormat(shape = JsonFormat.Shape.STRING,pattern = "yyyy-MM-dd HH:mm:ss",timezone="GMT+8")
    private Date updateTime;
	/**创建人，只人工上传时可用*/
    @Max(value = 32,message = "创建人不能超过32位")
    @TableField(value = "creator_name", fill = FieldFill.INSERT)
    @ApiModelProperty(value="创建人，只人工上传时可用",dataType="String",name="creatorName")
    private String creatorName;
	/**创建人ID来自于sys_user*/
    @TableField(value = "creator_id", fill = FieldFill.INSERT)
    @ApiModelProperty(value="创建人ID来自于sys_user",dataType="Long",name="creatorId")
    private Long creatorId;
}
