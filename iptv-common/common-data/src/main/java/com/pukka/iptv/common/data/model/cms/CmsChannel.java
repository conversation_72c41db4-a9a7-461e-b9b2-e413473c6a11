package com.pukka.iptv.common.data.model.cms;

import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelProperty;
import com.baomidou.mybatisplus.annotation.*;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.pukka.iptv.common.data.converter.bms.StatusConverter;
import com.pukka.iptv.common.data.converter.cms.ChannelMacrovisionConverter;
import com.pukka.iptv.common.data.converter.cms.ChannelSubTypeConverter;
import com.pukka.iptv.common.data.converter.cms.ChannelTypeConverter;
import lombok.NoArgsConstructor;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.Max;
import java.util.Date;

/**
 *
 * @author: luo
 * @date: 2021-8-31 17:14:29
 */

@Data
@EqualsAndHashCode
@JsonIgnoreProperties(value = { "handler" })
@NoArgsConstructor
@TableName(value = "cms_channel",autoResultMap=true)
public class CmsChannel extends Model<CmsChannel> implements java.io.Serializable{
    private static final long serialVersionUID = 1L;
    /**主键*/
    @ExcelIgnore
    @TableId(type= IdType.AUTO)
    @TableField(value = "id")
    @ApiModelProperty(value="主键",dataType="Long",name="id")
    private Long id;
    /**全局唯一标识*/
    @ExcelIgnore
    @Max(value = 32,message = "Code不能超过32位")
    @TableField(value = "code", fill = FieldFill.INSERT)
    @ApiModelProperty(value="全局唯一标识",dataType="String",name="code")
    private String code;
    /**建议频道号*/
    @Max(value = 3,message = "建议频道号不能超过3位")
    @TableField(value = "channel_number")
    @ApiModelProperty(value="建议频道号",dataType="String",name="channelNumber")
    @ExcelProperty(value = "建议频道号",index = 1)
    private String channelNumber;
    /**频道名称*/
    @Max(value = 64,message = "频道名称不能超过64位")
    @TableField(value = "name")
    @ApiModelProperty(value="频道名称",dataType="String",name="name")
    @ExcelProperty(value = "频道名称",index = 0)
    private String name;
    /**台标名称*/
    @Max(value = 10,message = "台标名称不能超过10位")
    @TableField(value = "call_sign")
    @ApiModelProperty(value="台标名称",dataType="String",name="callSign")
    @ExcelProperty(value = "台标名称",index = 2)
    private String callSign;
    /**时移标志 0:不生效 1:生效*/
    @ExcelProperty(value = "时移标志",index = 3,converter = StatusConverter.class)
    @TableField(value = "time_shift")
    @ApiModelProperty(value="时移标志 0:不生效 1:生效",dataType="Integer",name="timeShift")
    private Integer timeShift;
    /**该频道的录制节目 保存时长，可用于 时移和回看，单位为小时。*/
    @ExcelProperty(value = "节目录制时长",index = 4)
    @TableField(value = "storage_duration")
    @ApiModelProperty(value="该频道的录制节目 保存时长，可用于 时移和回看，单位为小时。",dataType="Long",name="storageDuration")
    private Integer storageDuration;
    /**默认时移时长, 单位分钟 ,仅仅对 Timeshift 有效*/
    @ExcelProperty(value = "时移时长",index = 5)
    @TableField(value = "time_shift_duration")
    @ApiModelProperty(value="默认时移时长, 单位分钟 ,仅仅对 Timeshift 有效",dataType="Long",name="timeShiftDuration")
    private Integer timeShiftDuration;
    /**描述信息*/
    @ExcelProperty(value = "描述信息",index = 6)
    @Max(value = 1024,message = "描述信息不能超过1024位")
    @TableField(value = "description")
    @ApiModelProperty(value="描述信息",dataType="String",name="description")
    private String description;
    /**国家*/
    @ExcelProperty(value = "国家",index = 7)
    @Max(value = 10,message = "国家不能超过10位")
    @TableField(value = "country")
    @ApiModelProperty(value="国家",dataType="String",name="country")
    private String country;
    /**州/省 */
    @ExcelProperty(value = "省",index = 8)
    @Max(value = 10,message = "州/省不能超过10位")
    @TableField(value = "state")
    @ApiModelProperty(value="州/省 ",dataType="String",name="state")
    private String state;
    /**城市*/
    @ExcelProperty(value = "城市",index = 9)
    @Max(value = 10,message = "城市不能超过10位")
    @TableField(value = "city")
    @ApiModelProperty(value="城市",dataType="String",name="city")
    private String city;
    /**邮编 */
    @ExcelProperty(value = "邮编",index = 10)
    @Max(value = 10,message = "邮编不能超过10位")
    @TableField(value = "zip_code")
    @ApiModelProperty(value="邮编 ",dataType="String",name="zipCode")
    private String zipCode;
    /**频道类型 1:直播频道*/
    @ExcelProperty(value = "频道类型",index = 11,converter = ChannelTypeConverter.class)
    @TableField(value = "type")
    @ApiModelProperty(value="频道类型 1:直播频道",dataType="Integer",name="type")
    private Integer type;
    /**信号来源，当 Type 为 1(直播频道) 1: 信号源来自 live 直播  2: 信号源来自 virtual  虚拟*/
    @ExcelProperty(value = "频道类型",index = 12,converter = ChannelSubTypeConverter.class)
    @TableField(value = "sub_type",updateStrategy=FieldStrategy.IGNORED)
    @ApiModelProperty(value="信号来源，当 Type 为 1(直播频道) 1: 信号源来自 live 直播  2: 信号源来自 virtual  虚拟",dataType="Integer",name="subType")
    private Integer subType;
    /**语言*/
    @ExcelProperty(value = "语言",index = 13)
    @Max(value = 128,message = "语言不能超过128位")
    @TableField(value = "language")
    @ApiModelProperty(value="语言",dataType="String",name="language")
    private String language;
    /**状态标志 0:失效 1:生效*/
    @ExcelProperty(value = "状态标志",index = 14,converter = StatusConverter.class)
    @TableField(value = "status")
    @ApiModelProperty(value="状态标志 0:失效 1:生效",dataType="Integer",name="status")
    private Integer status;
    /**播放开始时间(HH24MI)*/
    @ExcelProperty(value = "播放开始时间",index = 15)
    @Max(value = 10,message = "播放开始时间不能超过10位")
    @TableField(value = "start_time")
    @ApiModelProperty(value="播放开始时间(HH24MI)",dataType="String",name="startTime")
    private String startTime;
    /**播放结束时间(HH24MI)*/
    @ExcelProperty(value = "播放结束时间",index = 16)
    @Max(value = 10,message = "播放结束时间不能超过10位")
    @TableField(value = "end_time")
    @ApiModelProperty(value="播放结束时间(HH24MI)",dataType="String",name="endTime")
    private String endTime;
    /**拷贝保护标志 0:无拷贝保护 1:有拷贝保护，默认0*/
    @ExcelProperty(value = "拷贝保护",index = 17,converter = ChannelMacrovisionConverter.class)
    @TableField(value = "macrovision")
    @ApiModelProperty(value="拷贝保护标志 0:无拷贝保护 1:有拷贝保护，默认0",dataType="Integer",name="macrovision")
    private Integer macrovision;
    /**双语标志(0否，1是)*/
    @ExcelIgnore
    @TableField(value = "bilingual")
    @ApiModelProperty(value="双语标志(0否，1是)",dataType="Integer",name="bilingual")
    private Integer bilingual;
    /**内容服务平台标识 */
    @ExcelProperty(value = "内容服务平台标识",index = 18)
    @Max(value = 32,message = "内容服务平台标识不能超过32位")
    @TableField(value = "vsp_code")
    @ApiModelProperty(value="内容服务平台标识 ",dataType="String",name="vspCode")
    private String vspCode;
    /**cpId*/
    @ExcelProperty(value = "cpId",index = 19)
    @TableField(value = "cp_id")
    @ApiModelProperty(value="cpId",dataType="Long",name="cpId")
    private Long cpId;
    /**CP名称*/
    @ExcelProperty(value = "CP名称",index = 20)
    @Max(value = 64,message = "CP名称不能超过64位")
    @TableField(value = "cp_name")
    @ApiModelProperty(value="CP名称",dataType="String",name="cpName")
    private String cpName;
    /**创建时间*/
    @ExcelIgnore
    @TableField(value = "create_time", fill = FieldFill.INSERT)
    @ApiModelProperty(value="创建时间",dataType="String",name="createTime")
    @JsonFormat(shape = JsonFormat.Shape.STRING,pattern = "yyyy-MM-dd HH:mm:ss",timezone="GMT+8")
    private Date createTime;
    /**更新时间*/
    @ExcelIgnore
    @TableField(value = "update_time",fill = FieldFill.INSERT_UPDATE)
    @ApiModelProperty(value="更新时间",dataType="String",name="updateTime")
    @JsonFormat(shape = JsonFormat.Shape.STRING,pattern = "yyyy-MM-dd HH:mm:ss",timezone="GMT+8")
    private Date updateTime;
    /**来源  1：专线注入 2：人工创建*/
    @ExcelIgnore
    @TableField(value = "source")
    @ApiModelProperty(value="来源  1：专线注入 2：人工创建",dataType="Integer",name="source")
    private Integer source;
    /**创建人，只人工上传时可用*/
    @ExcelIgnore
    @Max(value = 32,message = "创建人不能超过32位")
    @TableField(value = "creator_name", fill = FieldFill.INSERT)
    @ApiModelProperty(value="创建人，只人工上传时可用",dataType="String",name="creatorName")
    private String creatorName;
    /**创建人ID来自于sys_user*/
    @ExcelIgnore
    @TableField(value = "creator_id", fill = FieldFill.INSERT)
    @ApiModelProperty(value="创建人ID来自于sys_user",dataType="Long",name="creatorId")
    private Long creatorId;
    /**时区*/
    @ExcelIgnore
    @Max(value = 64,message = "时区不能超过64位")
    @TableField(value = "time_zone")
    @ApiModelProperty(value="时区",dataType="String",name="timeZone")
    private String timeZone;
}
