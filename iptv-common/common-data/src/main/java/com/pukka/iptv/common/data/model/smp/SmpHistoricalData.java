package com.pukka.iptv.common.data.model.smp;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

@Data
public class SmpHistoricalData implements Serializable {
    private Integer id;
    @ApiModelProperty(value="媒资名称",dataType="String",name="name")
    private String name;
    @ApiModelProperty(value="唯一标识",dataType="String",name="code")
    private String code;
    @ApiModelProperty(value="CP名称",dataType="String",name="cpName")
    private String cpName;
    @ApiModelProperty(value="媒资类型，单集0/子集1/剧集2",dataType="String",name="type")
    private String type;
    @ApiModelProperty(value="注入时间",dataType="String",name="injectionTime")
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date injectionTime;
/*    @ApiModelProperty(value="上线时间",dataType="String",name="onlineTime")
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date onlineTime;
    @ApiModelProperty(value="下线时间",dataType="String",name="downlineTime")
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date downlineTime;
    @ApiModelProperty(value="上线平台",dataType="String",name="spName")
    private String spName;
    @ApiModelProperty(value="分发通道",dataType="String",name="spChannelName")
    private String spChannelName;*/

}
