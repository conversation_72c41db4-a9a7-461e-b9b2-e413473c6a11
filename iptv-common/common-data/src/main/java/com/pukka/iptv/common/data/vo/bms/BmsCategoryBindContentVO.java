package com.pukka.iptv.common.data.vo.bms;

import com.baomidou.mybatisplus.annotation.TableField;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.util.Date;

/**
 * <AUTHOR>
 * @create 2021-09-14 16:16
 */
@Getter
@Setter
public class BmsCategoryBindContentVO implements Serializable {


    /**
     * 以下为 bms_package_content 表字段
     * 因为字段名有重复的, bms_category_content 的字段使用较少，所以选择将重复字段前缀统一使用 bcc 开头
     */
    @ApiModelProperty(value = "关系表ID")
    private Long id;

    @ApiModelProperty(value = "关系发布通道id")
    @TableField(value = "bcc_out_passage_ids")
    private String bccOutPassageIds;

    @ApiModelProperty(value = "关系发布通道名称")
    @TableField(value = "bcc_out_passage_names")
    private String bccOutPassageNames;

    @ApiModelProperty(value = "关系发布状态")
    @TableField(value = "bcc_publish_status")
    private Integer bccPublishStatus;

    @ApiModelProperty(value = "关系发布时间")
    @TableField(value = "bcc_publish_time")
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date bccPublishTime;

    @ApiModelProperty(value = "关系发布描述")
    @TableField(value = "bcc_publish_description")
    private String bccPublishDescription;

    @ApiModelProperty(value = "关系定时发布状态,1-定时发布 0-取消定时发布")
    @TableField(value = "timed_publish_status")
    private Integer bccTimedPublishStatus;

    @ApiModelProperty(value = "关系定时发布时间")
    @TableField(value = "timed_publish")
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date bccTimedPublish;

    @ApiModelProperty(value = "关系定时发布描述")
    @TableField(value = "timed_publish_description")
    private String bccTimedPublishDescription;

    @ApiModelProperty(value = "关系排序值")
    @TableField(value = "bcc_sequence")
    private Integer bccSequence;


    @TableField(value = "pgm_category")
    @ApiModelProperty(value = "媒资分类：电影 电视剧")
    private String pgmCategory;

    @TableField(value = "licensing_window_start")
    @ApiModelProperty(value = "有效开始时间（YYYYMMDDHH24MiSS）")
    private String licensingWindowStart;

    @TableField(value = "licensing_window_end")
    @ApiModelProperty(value = "有效结束时间（YYYYMMDDHH24MiSS）")
    private String licensingWindowEnd;

    @TableField(value = "publish_status")
    @ApiModelProperty(value = "发布状态  1：待发布 2：发布中 3：发布成功 4：发布失败 5：待更新 6：更新中 7：更新失败 8：回收中 9：回收成功 10：回收失败")
    private Integer publishStatus;


    @TableField(value = "out_passage_ids")
    @ApiModelProperty(value = "发布通道id")
    private String outPassageIds;
    @TableField(value = "out_passage_names")
    @ApiModelProperty(value = "发布通道名称")
    private String outPassageNames;


    @TableField(value = "sp_name")
    @ApiModelProperty(value = "SP名称", dataType = "String")
    private String spName;

    @TableField(value = "cp_name")
    @ApiModelProperty(value = "CP名称", dataType = "String")
    private String cpName;

    @TableField(value = "content_provider")
    @ApiModelProperty(value = "内容提供商标识", dataType = "String")
    private String contentProvider;

    @TableField(value = "display_as_last_chance")
    @ApiModelProperty(value = "版权剩余天数", dataType = "int")
    private Integer displayAsLastChance;

    @TableField(value = "op_check_status")
    @ApiModelProperty(value = "终审状态", dataType = "int")
    private Integer opCheckStatus;

    /**
     * 栏目ID集合，以英文逗号分割
     */
    @TableField(value = "category_ids")
    @ApiModelProperty(value = "栏目ID集合，以英文逗号分割", dataType = "String", name = "categoryIds")
    private String categoryIds;
    /**
     * 栏目Name集合，以英文逗号分割
     */
    @TableField(value = "category_names")
    @ApiModelProperty(value = "栏目Name集合，以英文逗号分割", dataType = "String", name = "categoryNames")
    private String categoryNames;

    @TableField(value = "category_name")
    @ApiModelProperty(value = "栏目Name", dataType = "String", name = "categoryName")
    private String categoryName;
    /**
     * 排序值
     */
    @TableField(value = "sequence")
    @ApiModelProperty(value = "排序值")
    private int sequence;

    /**
     * 内容名称
     */
    @TableField(value = "content_name")
    @ApiModelProperty(value = "媒资名称")
    private String contentName;


    @TableField(value = "content_type")
    @ApiModelProperty(value = "媒资类型 1：电影 3：电视剧 4：系列片  5：片花")
    private String contentType;

    /**
     * cms 表code
     */
    @TableField(value = "cms_content_code")
    @ApiModelProperty(value = "cms 表code", dataType = "String", name = "cmsContentCode")
    private String cmsContentCode;
    @TableField(value = "cms_content_id")
    @ApiModelProperty(value = "cms 表id", dataType = "String", name = "cmsContentId")
    private String cmsContentId;


    @ApiModelProperty(value = "媒资来源 1:专线注入 2:人工创建")
    private Integer source;

    @ApiModelProperty(value = "关系绑定人员")
    @TableField(value = "creator_name")
    private String creatorName;

    @TableField(value = "create_time")
    @ApiModelProperty(value = "关系绑定时间")
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date createTime;

    /**
     * 状态标志 0:失效 1:生效
     */
    @TableField(value = "status")
    @ApiModelProperty(value = "状态标志 0:失效 1:生效", dataType = "Integer", name = "status")
    private Integer status;

    @TableField(value = "original_name")
    @ApiModelProperty(value = "原名", dataType = "String", name = "originalName")
    private String originalName;
}
