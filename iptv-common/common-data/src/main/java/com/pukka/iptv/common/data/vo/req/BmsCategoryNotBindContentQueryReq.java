package com.pukka.iptv.common.data.vo.req;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.pukka.iptv.common.data.model.bms.BmsCategoryContent;
import com.pukka.iptv.common.data.vo.bms.CheckNameApi;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import javax.validation.constraints.Max;
import javax.validation.constraints.Min;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Pattern;

import static com.pukka.iptv.common.data.util.RegexUtil.ALL_CHAR_REGEX;


/**
 * @author: wz
 * @date: 2021/8/31 11:43
 * @description: 栏目内容关系查询vo
 */
@Getter
@Setter
public class BmsCategoryNotBindContentQueryReq extends Page<BmsCategoryContent> implements CheckNameApi {

    @NotNull
    @Min(0)
    @Max(Long.MAX_VALUE)
    @ApiModelProperty(value = "spId", dataType = "Long", name = "spId")
    private Long spId;

    @Min(0)
    @Max(Long.MAX_VALUE)
    @ApiModelProperty(value = "cpId", dataType = "Long", name = "cpId")
    private Long cpId;

    @Min(0)
    @Max(Long.MAX_VALUE)
    @ApiModelProperty(value = "categoryId", dataType = "Long", name = "栏目id")
    private Long categoryId;
    
    @Min(0)
    @Max(50)
    @ApiModelProperty(value = "1：电影 3：电视剧 4：系列片 5：片花 此处没有子集，子集由program表单独保存", dataType = "Integer", name = "contentType")
    private Integer contentType;

    @Pattern(regexp = ALL_CHAR_REGEX + "{0,5000}")
    @ApiModelProperty(value = "name", dataType = "String", name = "媒资名称")
    private String name;

    private String[] names;

    @Min(0)
    @Max(Long.MAX_VALUE)
    @ApiModelProperty(value = "媒资分类ID，来自pgm_category表", dataType = "Long", name = "pgmCategoryId")
    private Long pgmCategoryId;

    @Min(0)
    @Max(4)
    @ApiModelProperty(value = "op审核 1：op未审核 2：审核中 3：审核未通过 4：审核通过", dataType = "Integer", name = "opCheckStatus")
    private Integer opCheckStatus;

    @ApiModelProperty(value = "原名", dataType = "String", name = "originalName")
    private String originalName;

    @Override
    public String getPackageName() {
        return null;
    }

    @Override
    public String getCategoryName() {
        return null;
    }

    @Override
    public void setPackageName(String packageName) {

    }

    @Override
    public void setCategoryName(String categoryName) {

    }

    @Override
    public void setCategoryNames(String[] names) {

    }

    @Override
    public void setPackageNames(String[] names) {

    }
}
