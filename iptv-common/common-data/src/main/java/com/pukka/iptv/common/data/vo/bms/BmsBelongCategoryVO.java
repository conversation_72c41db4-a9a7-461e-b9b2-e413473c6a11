package com.pukka.iptv.common.data.vo.bms;

import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @Date 2021-11-15 11:52
 * @Description 所属栏目树
 */


@Getter
@Setter
@Accessors(chain = true)
public class BmsBelongCategoryVO implements Serializable {
    /**
     * 栏目id
     */
    private Long id;
    /**
     * 栏目code
     */
    private String code;
    /**
     * 父栏目id
     */
    private Long parentId;
    /**
     * 父节点code
     */
    private String parentCode;
    /**
     * 父栏目名称
     */
    private String name;
    /**
     * spId
     */
    private Long spId;
    /**
     * sp名称
     */
    private String spName;
    /**
     * 是否是叶子结点  0:非叶子节点  1:叶子节点
     */
    private Integer leafNode;

    private List<BmsBelongCategoryVO> children;
}
