package com.pukka.iptv.common.data.vo.req;

import com.pukka.iptv.common.base.enums.SourceEnum;
import com.pukka.iptv.common.data.model.bms.BmsPackage;
import com.pukka.iptv.common.data.model.in.InPackage;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;
import org.springframework.beans.BeanUtils;

/**
 * <AUTHOR>
 * @Date 2021-10-29 18:36
 */
@Setter
@Getter
@Accessors(chain = true)
public class BmsPackageInjectionReq extends BmsPackage {

    private String cspId;

    public BmsPackageInjectionReq(){
    }

    public BmsPackageInjectionReq(InPackage inPackage) {
        BeanUtils.copyProperties(inPackage, this, "id", "updateTime", "crateTime");
        this.setSource(SourceEnum.SYSWORK.getValue());
    }
}
