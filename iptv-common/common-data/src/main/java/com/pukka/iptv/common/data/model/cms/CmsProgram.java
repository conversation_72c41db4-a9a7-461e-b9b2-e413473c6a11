package com.pukka.iptv.common.data.model.cms;

import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import com.alibaba.excel.annotation.write.style.ContentRowHeight;
import com.alibaba.excel.annotation.write.style.HeadRowHeight;
import com.baomidou.mybatisplus.annotation.*;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import com.pukka.iptv.common.data.converter.cms.SettlementConverter;
import com.pukka.iptv.common.data.converter.bms.LicensingConverter;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Date;

/**
 * @author: luo
 * @date: 2021-9-2 15:51:26
 */

@Data
@EqualsAndHashCode
@JsonIgnoreProperties(value = {"handler"})
@ContentRowHeight(20)
@HeadRowHeight(20)
@ColumnWidth(20)
@TableName(value = "cms_program", autoResultMap = true)
public class CmsProgram extends Model<CmsProgram> implements java.io.Serializable {


    private static final long serialVersionUID = -7773487343931755470L;
    /**
     * 主键
     */
    @ExcelIgnore
    @TableId(type = IdType.AUTO)
    @TableField(value = "id")
    @ApiModelProperty(value = "主键", dataType = "Long", name = "id")
    @JsonSerialize(using = ToStringSerializer.class)
    private Long id;
    /**
     * 全局唯一标识
     */
    @ExcelProperty("媒资code")
    @TableField(value = "code", fill = FieldFill.INSERT)
    @ApiModelProperty(value = "全局唯一标识", dataType = "String", name = "code")
    private String code;
    /**
     * 节目名称
     */
    @ExcelProperty("媒资名称")
    @TableField(value = "name")
    @ApiModelProperty(value = "节目名称", dataType = "String", name = "name")
    private String name;
    /**
     * 节目订购编号
     */
    @ExcelIgnore
    @TableField(value = "order_number")
    @ApiModelProperty(value = "节目订购编号", dataType = "String", name = "orderNumber")
    private String orderNumber;
    /**
     * 原名
     */
    @ExcelProperty("别名")
    @TableField(value = "original_name")
    @ApiModelProperty(value = "原名", dataType = "String", name = "originalName")
    private String originalName;
    /**
     * 正片关联状态 1：待关联 2：已关联
     */
    @ExcelIgnore
    @TableField(value = "release_status")
    @ApiModelProperty(value = "正片关联状态 1：待关联 2：已关联", dataType = "Integer", name = "releaseStatus")
    private Integer releaseStatus;
    /**
     * 预览片关联状态 1：待关联 2：已关联
     */
    @ExcelIgnore
    @TableField(value = "preview_status")
    @ApiModelProperty(value = "预览片关联状态 1：待关联 2：已关联", dataType = "Integer", name = "previewStatus")
    private Integer previewStatus;
    /**
     * 索引发布时间供页面排序
     */
    @ExcelProperty("索引排序")
    @TableField(value = "sort_name")
    @ApiModelProperty(value = "索引发布时间供页面排序", dataType = "String", name = "sortName")
    private String sortName;
    /**
     * 搜索名称，供页面搜索
     */
    @ExcelProperty("搜索标识")
    @TableField(value = "search_name")
    @ApiModelProperty(value = "搜索名称，供页面搜索", dataType = "String", name = "searchName")
    private String searchName;
    /**
     * 演员列表
     */
    @ExcelProperty("演员")
    @TableField(value = "actor_display")
    @ApiModelProperty(value = "演员列表", dataType = "String", name = "actorDisplay")
    private String actorDisplay;
    /**
     * 作者列表
     */
    @ExcelProperty("作者")
    @TableField(value = "writer_display")
    @ApiModelProperty(value = "作者列表", dataType = "String", name = "writerDisplay")
    private String writerDisplay;
    /**
     * 产地id，来自于sys_dictionary_item表
     */
    @ExcelIgnore
    @TableField(value = "original_country_id")
    @ApiModelProperty(value = "产地id，来自于sys_dictionary_item表", dataType = "Long", name = "originalCountryId")
    private Long originalCountryId;
    /**
     * 产地名称，来自于sys_dictionary_item表
     */
    @ExcelProperty("产地")
    @TableField(value = "original_country")
    @ApiModelProperty(value = "产地名称，来自于sys_dictionary_item表", dataType = "String", name = "originalCountry")
    private String originalCountry;
    /**
     * 语言
     */
    @ExcelProperty("语言")
    @TableField(value = "language")
    @ApiModelProperty(value = "语言", dataType = "String", name = "language")
    private String language;
    /**
     * 上映年份
     */
    @ExcelProperty("上映年份")
    @TableField(value = "release_year")
    @ApiModelProperty(value = "上映年份", dataType = "String", name = "releaseYear")
    private String releaseYear;
    /**
     * 首播时间
     */
    @ExcelProperty("首播时间")
    @TableField(value = "org_air_date")
    @ApiModelProperty(value = "首播时间", dataType = "String", name = "orgAirDate")
    private String orgAirDate;
    /**
     * 有效开始时间（YYYYMMDDHH24MiSS）
     */
    @ExcelProperty(value = "授权开始时间",converter = LicensingConverter.class)
    @TableField(value = "licensing_window_start")
    @ApiModelProperty(value = "有效开始时间（YYYYMMDDHH24MiSS）", dataType = "String", name = "licensingWindowStart")
    private String licensingWindowStart;
    /**
     * 有效结束时间（YYYYMMDDHH24MiSS）
     */
    @ExcelProperty(value = "授权结束时间",converter = LicensingConverter.class)
    @TableField(value = "licensing_window_end")
    @ApiModelProperty(value = "有效结束时间（YYYYMMDDHH24MiSS）", dataType = "String", name = "licensingWindowEnd")
    private String licensingWindowEnd;
    /**
     * 新到天数
     */
    @ExcelIgnore
    @TableField(value = "display_as_new")
    @ApiModelProperty(value = "新到天数", dataType = "Long", name = "displayAsNew")
    private Integer displayAsNew;
    /**
     * 剩余天数
     */
    @ExcelIgnore
    @TableField(value = "display_as_last_chance")
    @ApiModelProperty(value = "剩余天数 ", dataType = "Long", name = "displayAsLastChance")
    private Integer displayAsLastChance;
    /**
     * 拷贝保护标志 0:无拷贝保护 1:有拷贝保护
     */
    @ExcelIgnore
    @TableField(value = "macrovision")
    @ApiModelProperty(value = "拷贝保护标志 0:无拷贝保护 1:有拷贝保护", dataType = "Integer", name = "macrovision")
    private Integer macrovision;
    /**
     * 节目描述
     */
    @ExcelProperty("描述")
    @TableField(value = "description")
    @ApiModelProperty(value = "节目描述", dataType = "String", name = "description")
    private String description;
    /**
     * 媒资分类ID，来自sys_dictionary_item表
     */
    @ExcelIgnore
    @TableField(value = "pgm_category_id")
    @ApiModelProperty(value = "媒资分类ID，来自sys_dictionary_item表", dataType = "Long", name = "pgmCategoryId")
    private Long pgmCategoryId;
    /**
     * 节目形态，如：新闻，电影
     */
    @ExcelProperty("媒资一级分类")
    @TableField(value = "pgm_category")
    @ApiModelProperty(value = "节目形态，如：新闻，电影", dataType = "String", name = "pgmCategory")
    private String pgmCategory;
    /**
     * 媒资类型ID，来自sys_dictionary_item表
     */
    @ExcelIgnore
    @TableField(value = "pgm_snd_class_id")
    @ApiModelProperty(value = "媒资类型ID，来自sys_dictionary_item表", dataType = "Long", name = "pgmSndClassId")
    private String pgmSndClassId;
    /**
     * 二级标签，如：动作，科幻
     */
    @ExcelProperty("媒资二级分类")
    @TableField(value = "pgm_snd_class")
    @ApiModelProperty(value = "二级标签，如：动作，科幻", dataType = "String", name = "pgmSndClass")
    private String pgmSndClass;
    /**
     * 列表定价
     */
    @ExcelProperty("列表定价")
    @TableField(value = "price_tax_in",updateStrategy=FieldStrategy.IGNORED)
    @ApiModelProperty(value = "列表定价", dataType = "java.math.BigDecimal", name = "priceTaxIn")
    private java.math.BigDecimal priceTaxIn;
    /**
     * 状态标志 0:失效 1:生效
     */
    @ExcelIgnore
    @TableField(value = "status")
    @ApiModelProperty(value = "状态标志 0:失效 1:生效", dataType = "Integer", name = "status")
    private Integer status;
    /**
     * 节目类型1: 视频类节目 2: 图文类节目
     */
    @ExcelIgnore
    @TableField(value = "source_type")
    @ApiModelProperty(value = "节目类型1: 视频类节目 2: 图文类节目", dataType = "Integer", name = "sourceType")
    private Integer sourceType;
    /**
     * 0: 单集 1: 子集
     */
    @ExcelIgnore
    @TableField(value = "series_flag")
    @ApiModelProperty(value = "0: 单集 1: 子集", dataType = "Integer", name = "seriesFlag")
    private Integer seriesFlag;
    /**
     * 集数，连续剧第几集
     */
    @ExcelIgnore
    @TableField(value = "episode_index")
    @ApiModelProperty(value = "集数，连续剧第几集", dataType = "Integer", name = "episodeIndex")
    private Integer episodeIndex;
    /**
     * 剧头id
     */
    @ExcelIgnore
    @TableField(value = "series_id")
    @ApiModelProperty(value = "剧头id", dataType = "Long", name = "seriesId")
    private Long seriesId;
    /**
     * 剧头code
     */
    @ExcelIgnore
    @TableField(value = "series_code")
    @ApiModelProperty(value = "剧头code", dataType = "String", name = "seriesCode")
    private String seriesCode;
    /**
     * 所属剧头
     */
    @ExcelIgnore
    @TableField(value = "series_name")
    @ApiModelProperty(value = "所属剧头", dataType = "String", name = "seriesName")
    private String seriesName;
    /**
     * 主要人物
     */
    @ExcelIgnore
    @TableField(value = "kpeople")
    @ApiModelProperty(value = "主要人物", dataType = "String", name = "kpeople")
    private String kpeople;
    /**
     * 导演
     */
    @ExcelProperty("导演")
    @TableField(value = "director")
    @ApiModelProperty(value = "导演", dataType = "String", name = "director")
    private String director;
    /**
     * 编剧
     */
    @TableField(value = "script_writer")
    @ApiModelProperty(value = "编剧", dataType = "String", name = "scriptWriter")
    private String scriptWriter;
    /**
     * 节目主持人
     */
    @ExcelProperty("媒资主持人")
    @TableField(value = "compere")
    @ApiModelProperty(value = "节目主持人", dataType = "String", name = "compere")
    private String compere;
    /**
     * 受访者
     */
    @ExcelProperty("受访者")
    @TableField(value = "guest")
    @ApiModelProperty(value = "受访者", dataType = "String", name = "guest")
    private String guest;
    /**
     * 记者
     */
    @ExcelProperty("记者")
    @TableField(value = "reporter")
    @ApiModelProperty(value = "记者", dataType = "String", name = "reporter")
    private String reporter;
    /**
     * 其他责任人
     */
    @ExcelProperty("其他责任人")
    @TableField(value = "op_incharge")
    @ApiModelProperty(value = "其他责任人", dataType = "String", name = "opIncharge")
    private String opIncharge;
    /**
     * 内容服务平台标识
     */
    @ExcelIgnore
    @TableField(value = "vsp_code")
    @ApiModelProperty(value = "内容服务平台标识", dataType = "String", name = "vspCode")
    private String vspCode;
    /**
     * 版权方标识
     */
    @ExcelProperty("版权方")
    @TableField(value = "copy_right")
    @ApiModelProperty(value = "版权方标识", dataType = "String", name = "copyRight")
    private String copyRight;
    /**
     * 内容提供商标识
     */
    @ExcelProperty("内容提供商")
    @TableField(value = "content_provider")
    @ApiModelProperty(value = "内容提供商标识", dataType = "String", name = "contentProvider")
    private String contentProvider;
    /**
     * 节目时长（分钟）
     */
    @ExcelProperty("时长")
    @TableField(value = "duration")
    @ApiModelProperty(value = "节目时长（分钟）", dataType = "Long", name = "duration")
    private Integer duration;
    /**
     * 评分，0 到 10，最多一位 小数
     */
    @ExcelProperty("评分")
    @TableField(value = "rating")
    @ApiModelProperty(value = " 评分，0 到 10，最多一位 小数 ", dataType = "String", name = "rating")
    private String rating;
    /**
     * cpId
     */
    @ExcelIgnore
    @TableField(value = "cp_id")
    @ApiModelProperty(value = "cpId", dataType = "Long", name = "cpId")
    private Long cpId;
    /**
     * CP名称
     */
    @ExcelProperty("所属CP")
    @TableField(value = "cp_name")
    @ApiModelProperty(value = "CP名称", dataType = "String", name = "cpName")
    private String cpName;
    /**
     * 创建时间
     */
    @ExcelIgnore
    @TableField(value = "create_time", fill = FieldFill.INSERT)
    @ApiModelProperty(value = "创建时间", dataType = "String", name = "createTime")
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date createTime;
    /**
     * 更新时间
     */
    @ExcelIgnore
    @TableField(value = "update_time", fill = FieldFill.INSERT_UPDATE)
    @ApiModelProperty(value = "更新时间", dataType = "String", name = "updateTime")
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date updateTime;
    /**
     * 审核状态 1：待审核 2：审核中 3：未通过 4：通过
     */
    @ExcelIgnore
    @TableField(value = "cp_check_status")
    @ApiModelProperty(value = "审核状态 1：待审核 2：审核中 3：未通过 4：通过", dataType = "Integer", name = "cpCheckStatus")
    private Integer cpCheckStatus;
    /**
     * 自审描述
     */
    @ExcelIgnore
    @TableField(value = "cp_check_desc")
    @ApiModelProperty(value = "自审描述", dataType = "String", name = "cpCheckDesc")
    private String cpCheckDesc;
    /**
     * 审核时间
     */
    @ExcelIgnore
    @TableField(value = "cp_check_time")
    @ApiModelProperty(value = "审核时间", dataType = "String", name = "cpCheckTime")
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date cpCheckTime;
//    private String cpCheckTime;
    /**
     * 审核人
     */
    @ExcelIgnore
    @TableField(value = "cp_checker")
    @ApiModelProperty(value = "审核人", dataType = "String", name = "cpChecker")
    private String cpChecker;
    /**
     * op审核  1：待审核  2：审核中  3：未通过  4：通过
     */
    @ExcelIgnore
    @TableField(value = "op_check_status")
    @ApiModelProperty(value = "op审核  1：待审核  2：审核中  3：未通过  4：通过", dataType = "Integer", name = "opCheckStatus")
    private Integer opCheckStatus;
    /**
     * op审核描述
     */
    @ExcelIgnore
    @TableField(value = "op_check_desc")
    @ApiModelProperty(value = "op审核描述", dataType = "String", name = "opCheckDesc")
    private String opCheckDesc;
    /**
     * 终审人员
     */
    @ExcelIgnore
    @TableField(value = "op_checker")
    @ApiModelProperty(value = "终审人员", dataType = "String", name = "opChecker")
    private String opChecker;
    /**
     * 终审时间
     */
    @ExcelIgnore
    @TableField(value = "op_check_time")
    @ApiModelProperty(value = "终审时间", dataType = "String", name = "opCheckTime")
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date opCheckTime;
//    private String opCheckTime;
    /**
     * 来源 1：专线注入 2：人工创建
     */
    @ExcelIgnore
    @TableField(value = "source")
    @ApiModelProperty(value = "来源 1：专线注入 2：人工创建", dataType = "Integer", name = "source")
    private Integer source;
    /**
     * 创建人，只人工上传时可用
     */
    @ExcelIgnore
    @TableField(value = "creator_name", fill = FieldFill.INSERT)
    @ApiModelProperty(value = "创建人，只人工上传时可用", dataType = "String", name = "creatorName")
    private String creatorName;
    /**
     * creatorId
     */
    @ExcelIgnore
    @TableField(value = "creator_id", fill = FieldFill.INSERT)
    @ApiModelProperty(value = "creatorId", dataType = "Long", name = "creatorId")
    private Long creatorId;
    /**
     * 内容编辑锁状态 1:未锁定 2:已锁定
     */
    @ExcelIgnore
    @TableField(value = "lock_status")
    @ApiModelProperty(value = "内容编辑锁状态 1:未锁定 2:已锁定", dataType = "Integer", name = "lockStatus")
    private Integer lockStatus;
    /**
     * 联通价格 格式： 1;2;3;4;5 说明： 1.五个价格均 可空，用分号 隔开。极端示 例：;;;;。四个; 表示五个空的 价格。 2.五个价格顺 序固定：价格; 政企价格 1;政 企价格 2;优惠 价格 1;优惠价 格 2。
     */
    @ExcelProperty("联通价格")
    @TableField(value = "cucc_price")
    @ApiModelProperty(value = "联通价格 格式： 1;2;3;4;5 说明： 1.五个价格均 可空，用分号 隔开。极端示 例：;;;;。四个; 表示五个空的 价格。 2.五个价格顺 序固定：价格; 政企价格 1;政 企价格 2;优惠 价格 1;优惠价 格 2。", dataType = "String", name = "cuccPrice")
    private String cuccPrice;
    /**
     * 电信价格 格式： 1;2;3;4;5 说明： 1.五个价格均 可空，用分号 隔开。极端示 例：;;;;。四个; 表示五个空的 价格。 2.五个价格顺 序固定：价格; 政企价格 1;政 企价格 2;优惠 价格 1;优惠价 格 2。
     */
    @ExcelProperty("电信价格")
    @TableField(value = "ctcc_price")
    @ApiModelProperty(value = "电信价格 格式： 1;2;3;4;5 说明： 1.五个价格均 可空，用分号 隔开。极端示 例：;;;;。四个; 表示五个空的 价格。 2.五个价格顺 序固定：价格; 政企价格 1;政 企价格 2;优惠 价格 1;优惠价 格 2。", dataType = "String", name = "ctccPrice")
    private String ctccPrice;
    /**
     * 移动价格 格式： 1;2;3;4;5 说明： 1.五个价格均 可空，用分号 隔开。极端示 例：;;;;。四个; 表示五个空的 价格。 2.五个价格顺 序固定：价格; 政企价格 1;政 企价格 2;优惠 价格 1;优惠价 格 2。
     */
    @ExcelProperty("移动价格")
    @TableField(value = "cmcc_price")
    @ApiModelProperty(value = "移动价格 格式： 1;2;3;4;5 说明： 1.五个价格均 可空，用分号 隔开。极端示 例：;;;;。四个; 表示五个空的 价格。 2.五个价格顺 序固定：价格; 政企价格 1;政 企价格 2;优惠 价格 1;优惠价 格 2。", dataType = "String", name = "cmccPrice")
    private String cmccPrice;
    /**
     * 发行方
     */
    @ExcelProperty("发行方")
    @TableField(value = "publisher")
    @ApiModelProperty(value = "发行方", dataType = "String", name = "publisher")
    private String publisher;
    /**
     * 内容字号
     */
    @ExcelProperty("内容字号")
    @TableField(value = "approval")
    @ApiModelProperty(value = "内容字号", dataType = "String", name = "approval")
    private String approval;

    /**
     * 正片Code，cms_resource表的code，用于视频上报时关联
     */
    @ExcelIgnore
    @TableField(value = "resource_release_code")
    @ApiModelProperty(value = "正片Code，cms_resource表的code，用于视频上报时关联", dataType = "String", name = "resourceReleaseCode")
    private String resourceReleaseCode;

    /**
     * 预览片Code，cms_resource表的code，用于视频上报时关联
     */
    @ExcelIgnore
    @TableField(value = "resource_preview_code")
    @ApiModelProperty(value = "预览片Code，cms_resource表的code，用于视频上报时关联", dataType = "String", name = "resourcePreviewCode")
    private String resourcePreviewCode;
    /**
     * 点播的片头时长，单位秒
     */
    @ExcelIgnore
    @TableField(value = "movie_head_duration")
    @ApiModelProperty(value = "点播的片头时长，单位秒", dataType = "Long", name = "movieHeadDuration")
    private Long movieHeadDuration;
    /**
     * 点播的片尾时长，单位秒
     */
    @ExcelIgnore
    @TableField(value = "movie_tail_duration")
    @ApiModelProperty(value = "点播的片尾时长，单位秒", dataType = "Long", name = "movieTailDuration")
    private Long movieTailDuration;
    /**
     * 格式： 1;2;3;4;5 说明： 1.五个价格均 可空，用分号 隔开。极端示 例：;;;;。四个; 表示五个空的 价格。 2.五个价格顺 序固定：价格; 政企价格 1;政 企价格 2;优惠 价格 1;优惠价 格 2。
     */
    @ExcelIgnore
    @TableField(value = "new_price")
    @ApiModelProperty(value = "格式： 1;2;3;4;5 说明： 1.五个价格均 可空，用分号 隔开。极端示 例：;;;;。四个; 表示五个空的 价格。 2.五个价格顺 序固定：价格; 政企价格 1;政 企价格 2;优惠 价格 1;优惠价 格 2。", dataType = "String", name = "newPrice")
    private String newPrice;

    /**
     * Type
     */
    @ExcelIgnore
    @TableField(value = "type")
    @ApiModelProperty(value = "type 节目形态", dataType = "String", name = "correlateId")
    private String type;
    /**
     * Tags
     */
    @ExcelIgnore
    @TableField(value = "tags")
    @ApiModelProperty(value = "tags 二级分类，使用空格隔开", dataType = "String", name = "tags")
    private String tags;

    /**
     * 节目清晰度标识
     */
    @TableField(value = "definition_flag",updateStrategy=FieldStrategy.IGNORED)
    @ExcelProperty(value = "清晰度", converter = SettlementConverter.class)
    @ApiModelProperty(value = "节目清晰度标识 0：标清 1：高清  2：超清 3: 4K  4: 杜比（4K+杜比）\n", dataType = "Integer", name = "definitionFlag")
    private Integer definitionFlag;

    /**
     * 是否为违禁片
     */
    @TableField(value = "is_prohibit")
    @ApiModelProperty(value = "是否为违禁片,0:不为违禁片1:违禁片2:待检测", dataType = "Integer", name = "isProhibit")
    private Integer isProhibit;

    /**
     * 违禁状态
     */
    @TableField(value = "prohibit_status")
    @ApiModelProperty(value = "违禁状态,0:未处理(未添加违禁片库) 1:已处理(已添加违禁片库)", dataType = "Integer", name = "prohibitStatus")
    private Integer prohibitStatus;

    /**
     * 预留字段
     */
    @TableField(value = "extendInfoList")
    @ApiModelProperty(value = "预留字段", dataType = "String", name = "extendInfoList")
    private String extendInfoList;

    /**
     * 节目试看(秒)
     */
    @TableField(value = "preDuration")
    @ApiModelProperty(value = "节目试看(秒)", dataType = "Long", name = "preDuration")
    private Long preDuration;

    @TableField(exist = false)
    @ApiModelProperty(value="媒体格式描述符",dataType="String",name="mediaSpec")
    private String mediaSpec;

    /**
     * 审核结果，1：待审核，2：审核中，3：通过，4：不通过，5：人工复审通过，99：审核失败/取消
     */
    @TableField(value = "audit_status")
    @ApiModelProperty(value = "审核结果", dataType = "Integer", name = "auditStatus")
    private Integer auditStatus;

    /**
     * 备注
     */
    @TableField(value = "audit_description")
    @ApiModelProperty(value = "备注", dataType = "String", name = "auditDescription")
    private String auditDescription;

    /**
     * 内容评级
     */
    @TableField(value = "content_rating")
    @ApiModelProperty(value = "内容评级", dataType = "String", name = "contentRating")
    private String contentRating;

    @TableField(value = "expand_element")
    @ApiModelProperty(value = "扩展字段", dataType = "String", name = "expandElement")
    private String expandElement;
}
