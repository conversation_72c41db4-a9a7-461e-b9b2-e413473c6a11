package com.pukka.iptv.common.data.dto;

import com.baomidou.mybatisplus.annotation.TableLogic;
import lombok.Data;
import org.springframework.util.Assert;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * @Author: liaowj
 * @Description: UT 请求参数
 * @CreateDate: 2021/11/2 16:18
 * @Version: 1.0
*/
@Data
public class UTFeedback {

    /** 内容提供商节目 code  唯一，内容提供商下发的内容 code */
//    @NotBlank
    private String contentCode;

    /** 节目名称 */
//    @NotBlank
    private String contentName;

    /** 下平台标识 唯一，指：运营商或厂商, 对应SP Code */
//    @NotBlank
    private String branchCode;

    /** 内容状态 0：下线 1：上线 */
//    @NotNull
    private int status;

    @TableLogic
    /** 播放地址 */
    private String playUrl;


    public void valid() {
        Assert.notNull(contentCode, "节目 code 不能为空");
        Assert.notNull(contentName, "不能为空");
        Assert.notNull(branchCode, "地址不能为空");
        Assert.notNull(status, "页面编码不能为空");
    }
}
