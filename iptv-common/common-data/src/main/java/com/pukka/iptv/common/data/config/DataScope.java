package com.pukka.iptv.common.data.config;

import java.util.Date;
import lombok.Data;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.EqualsAndHashCode;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;

/**
 * @Author: zhengcl
 * @Date: 2021/9/6 14:46
 */

@Data
@EqualsAndHashCode(callSuper = true)
public class DataScope extends HashMap {

    /**
     * 限制范围的字段名称
     */
    private String scopeName = "user_id";

    /**
     * 具体的数据范围
     */
    private List<String> users = new ArrayList<>();

    /**
     * 是否只查询本部门
     */
    private Boolean isOnly = false;

    /**
     * 函数名称，默认 SELECT * ;
     *
     * <ul>
     * <li>COUNT(1)</li>
     * </ul>
     */
    private DataScopeEnum func = DataScopeEnum.ALL;
}