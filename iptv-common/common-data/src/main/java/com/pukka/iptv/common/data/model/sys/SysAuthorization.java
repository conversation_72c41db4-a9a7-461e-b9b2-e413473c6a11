package com.pukka.iptv.common.data.model.sys;

import com.baomidou.mybatisplus.annotation.*;
import io.swagger.annotations.ApiModelProperty;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.NoArgsConstructor;
import lombok.EqualsAndHashCode;
import java.util.Date;
import lombok.Data;
import com.fasterxml.jackson.annotation.JsonFormat;

/**
 *
 * @author: luo
 * @date: 2021-8-27 22:15:03
 */

@Data
@EqualsAndHashCode
@JsonIgnoreProperties(value = { "handler" })
@NoArgsConstructor
@TableName(value = "sys_authorization",autoResultMap=true)
public class SysAuthorization extends Model<SysAuthorization> implements java.io.Serializable{

    private static final long serialVersionUID = 1L;
	/**id*/
    @TableId(type= IdType.AUTO)
	@TableField(value = "id")
    @ApiModelProperty(value="id",dataType="Long",name="id")
    private Long id;
	/**全局唯一标号*/
    @TableField(value = "code", fill = FieldFill.INSERT)
    @ApiModelProperty(value="全局唯一标号",dataType="String",name="code")
    private String code;
	/**合同号*/
	@TableField(value = "name")
    @ApiModelProperty(value="合同号",dataType="String",name="name")
    private String name;
	/**cpId*/
	@TableField(value = "cp_id")
    @ApiModelProperty(value="cpId",dataType="Long",name="cpId")
    private Long cpId;
	/**CP名称*/
	@TableField(value = "cp_name")
    @ApiModelProperty(value="CP名称",dataType="String",name="cpName")
    private String cpName;
	/**spId*/
	@TableField(value = "sp_id")
    @ApiModelProperty(value="spId",dataType="Long",name="spId")
    private Long spId;
	/**SP名称*/
	@TableField(value = "sp_name")
    @ApiModelProperty(value="SP名称",dataType="String",name="spName")
    private String spName;
	/**自动授权 1：是 2：否*/
	@TableField(value = "auto_authorize")
    @ApiModelProperty(value="自动授权 1：是 2：否",dataType="Integer",name="autoAuthorize")
    private Integer autoAuthorize;
	/**创建时间*/
	@TableField(value = "create_time")
    @ApiModelProperty(value="创建时间",dataType="String",name="createTime")
    @JsonFormat(shape = JsonFormat.Shape.STRING,pattern = "yyyy-MM-dd HH:mm:ss",timezone="GMT+8")
    private Date createTime;
	/**更新时间*/
	@TableField(value = "update_time")
    @ApiModelProperty(value="更新时间",dataType="String",name="updateTime")
    @JsonFormat(shape = JsonFormat.Shape.STRING,pattern = "yyyy-MM-dd HH:mm:ss",timezone="GMT+8")
    private Date updateTime;
	/**描述*/
	@TableField(value = "description")
    @ApiModelProperty(value="描述",dataType="String",name="description")
    private String description;
	/**是否删除,1否 255是*/
	@TableField(value = "status")
    @ApiModelProperty(value="是否删除,1否 255是",dataType="Integer",name="status",hidden = true)
    private Integer status;
}
