package com.pukka.iptv.common.redis.config;

import org.springframework.core.convert.converter.Converter;
import org.springframework.core.serializer.support.DeserializingConverter;
import org.springframework.core.serializer.support.SerializingConverter;
import org.springframework.data.redis.serializer.RedisSerializer;
import org.springframework.data.redis.serializer.SerializationException;

/**
 * @Author: zhengcl
 * @Date: 2021/7/25 7:54
 */
public class ObjectSerializer<T> implements RedisSerializer<Object> {

   /* @Nullable
    @Override
    public byte[] serialize(@Nullable T t) throws SerializationException {
        ByteArrayOutputStream byteArrayOutputStream = null;
        ObjectOutputStream objectOutputStream = null;
        try {
            byteArrayOutputStream = new ByteArrayOutputStream();
            objectOutputStream = new ObjectOutputStream(byteArrayOutputStream);
            objectOutputStream.writeObject(t);

            byte[] bytes = byteArrayOutputStream.toByteArray();
            return bytes;
        } catch (IOException e) {
            e.printStackTrace();
        }finally {
            try {
                objectOutputStream.close();
                byteArrayOutputStream.close();
            } catch (IOException e) {
                e.printStackTrace();
            }
        }
        return new byte[0];
    }

    @Nullable
    @Override
    public T deserialize(@Nullable byte[] bytes) throws SerializationException {
        InputStream inputstream = null;
        ObjectInputStream objectInputStream = null;
        try {
            inputstream = new ByteArrayInputStream(bytes);
            objectInputStream = new ObjectInputStream(inputstream);
            Object o = objectInputStream.readObject();
            T t = (T) o;
            return t;
        } catch (Exception e) {
            e.printStackTrace();
        }finally {
            try {
                objectInputStream.close();
                inputstream.close();
            } catch (IOException e) {
                e.printStackTrace();
            }
        }
        return null;
    }*/

    static final byte[] EMPTY_ARRAY = new byte[0];
    private Converter<Object,byte[]> serializingConverter = new SerializingConverter();
    private Converter<byte[],Object> deserializingConverter = new DeserializingConverter();
    @Override
    public Object deserialize(byte[] bytes) {
        if (isEmpty(bytes)) {
            return null;
        }

        try {
            return deserializingConverter.convert(bytes);
        } catch (Exception ex) {
            throw new SerializationException("Cannot deserialize", ex);
        }
    }

    @Override
    public byte[] serialize(Object object) {
        if (object == null) {
            return EMPTY_ARRAY;
        }

        try {
            return serializingConverter.convert(object);
        } catch (Exception ex) {
            return EMPTY_ARRAY;
        }
    }

    private boolean isEmpty(byte[] data) {
        return (data == null || data.length == 0);
    }

}