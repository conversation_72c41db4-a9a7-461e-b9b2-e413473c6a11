package com.pukka.iptv.common.log.util;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.extension.service.IService;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.google.common.base.CaseFormat;
import com.pukka.iptv.common.base.enums.CommonResponseEnum;
import com.pukka.iptv.common.base.enums.SystemSourceEnums;
import com.pukka.iptv.common.base.util.JwtTokenUtil;
import com.pukka.iptv.common.base.vo.CommonResponse;
import com.pukka.iptv.common.base.vo.SecurityUser;
import com.pukka.iptv.common.core.util.CollectionUtil;
import com.pukka.iptv.common.core.util.SafeUtil;
import com.pukka.iptv.common.core.util.SpringUtils;
import com.pukka.iptv.common.core.util.ip.IpUtils;
import com.pukka.iptv.common.data.model.sys.SysLog;
import com.pukka.iptv.common.log.enums.OperateObjectEnum;
import com.pukka.iptv.common.log.enums.OperateTypeEnum;
import lombok.experimental.UtilityClass;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.reflect.CodeSignature;
import org.aspectj.lang.reflect.MethodSignature;
import org.springframework.core.DefaultParameterNameDiscoverer;
import org.springframework.expression.EvaluationContext;
import org.springframework.expression.Expression;
import org.springframework.expression.ExpressionParser;
import org.springframework.expression.spel.standard.SpelExpressionParser;
import org.springframework.expression.spel.support.StandardEvaluationContext;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;
import org.springframework.validation.BindingResult;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.ServletInputStream;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.BufferedReader;
import java.io.InputStreamReader;
import java.lang.reflect.Method;
import java.util.*;

/**
 * <AUTHOR>
 * @date 2021/8/28
 * 系统日志工具类
 */

@Slf4j
@UtilityClass
public class SysLogUtils {
    //创建解析器
    private ExpressionParser parser = new SpelExpressionParser();
    //获取运行时参数的名称
    private DefaultParameterNameDiscoverer discoverer = new DefaultParameterNameDiscoverer();

    private String serviceImpSuffix = "ServiceImpl";
    private String objectNameField = "name";
    private String responseCode = "code";
    private String responseMessage = "message";
    private String channelNameField = "channelName";
    private String contentNameField = "contentName";
    private String packageNameField = "packageName";
    private String categoryNameField = "categoryName";
    private String scheduleNameField = "programName";
    private String epgFileNameField = "showName";
    private String epgNodeNameField = "name";
    private String epgTemplateNameField = "name";
    private String epgOutOrderNameField = "id";
    private String bmsCategoryContentTableName = "bms_category_content";
    private String bmsPackageContentTableName = "bms_package_content";
    private String bmsPackageChannelTableName = "bms_package_channel";
    private String bmsCategoryChannelTableName = "bms_category_channel";
    private String cmsScheduleTableName = "cms_schedule";
    private String bmsScheduleTableName = "bms_schedule";
    private String epgFileTableName = "epg_file";
    private String epgNodeTableName = "epg_node";
    private String epgTemplateTableName = "epg_template";
    private String epgOutOrderTableName = "epg_out_order";

    /**
     * 获取日志对象
     *
     * @param point
     * @param sysLog
     * @return
     */
    public SysLog getSysLog(ProceedingJoinPoint point, com.pukka.iptv.common.log.annotation.SysLog sysLog, HttpServletRequest request) throws JsonProcessingException {
        SysLog systemLog = new SysLog();
        try {
            String strClassName = point.getTarget().getClass().getName();
            String strMethodName = point.getSignature().getName();
            log.debug("[类名]:{},[方法]:{}", strClassName, strMethodName);
            systemLog.setClientAddress(IpUtils.getClientIpAddr(request));
            systemLog.setServerAddress(IpUtils.getLocalServer(request));
            systemLog.setOperateType(sysLog.operateType().getMessage());
            systemLog.setDescription(sysLog.description());
            //日志来源,区分不同平台日志
            systemLog.setSource(ObjectUtils.isNotEmpty(sysLog.source()) ? sysLog.source().getCode() : SystemSourceEnums.BOKONG.getCode());
            systemLog.setObjectIds(parseSpel(sysLog.objectIds(), point, "id"));
            systemLog.setObjectNames(parseSpel(sysLog.objectNames(), point, "name"));
            systemLog.setObjectType(sysLog.objectType().getMessage());
            systemLog.setRequestFunction(strClassName + "." + strMethodName + "()");
            systemLog.setRequestParam(getRequestParams(point, request));
            systemLog.setRequestMethod(request.getMethod());
            systemLog.setCreateTime(new Date());
            setSyslogDetail(systemLog);
        } catch (Exception e) {
            log.error("生成日志异常：{}", e.getMessage());
            log.error(e.getMessage(), e.fillInStackTrace());
        }
        return systemLog;
    }

    /**
     * 解析 spel 表达式
     *
     * @param spelKey
     * @param joinPoint
     * @param field
     * @return 执行spel表达式后的结果
     */
    private String parseSpel(String spelKey, ProceedingJoinPoint joinPoint, String field) {
        if (!StringUtils.hasLength(spelKey)) {
            return null;
        }
        // 通过joinPoint获取被注解方法
        MethodSignature methodSignature = (MethodSignature) joinPoint.getSignature();
        Method method = methodSignature.getMethod();
        //获取表达式
        Expression expression = parser.parseExpression(spelKey);
        //设置解析上下文(有哪些占位符，以及每种占位符的值)
        EvaluationContext context = new StandardEvaluationContext();
        //获取参数值
        Object[] args = joinPoint.getArgs();
        String[] parameterNames = discoverer.getParameterNames(method);
        for (int i = 0; i < parameterNames.length; i++) {
            context.setVariable(parameterNames[i], args[i]);
        }
        //解析,获取替换后的结果
        Object value = expression.getValue(context);
        return parseValue(value, field);
    }

    private String parseValue(Object object, String field) {
        String values = "";
        try {
            if (!Objects.isNull(object)) {
                if (object instanceof Collection) {
                    JSONArray objects = JSON.parseArray(JSON.toJSONString(object));
                    for (Object o : objects) {
                        String appendValues = "";
                        if (o instanceof Long || o instanceof Integer || o instanceof String) {
                            appendValues = o.toString();
                        } else {
                            JSONObject jsonObj = JSON.parseObject(JSON.toJSONString(o));
                            if (jsonObj.containsKey(field)) {
                                appendValues = jsonObj.getString(field);
                            } else if (jsonObj.containsKey(field + "s")) {
                                appendValues = jsonObj.getString(field + "s");
                            }
                        }
                        if (StringUtils.hasLength(appendValues)) {
                            if (StringUtils.hasLength(values)) {
                                values += ",";
                            }
                            values += appendValues;
                        }
                    }
                } else {
                    values = object.toString();
                }
            }
        } catch (Exception e) {
            log.error(e.getMessage(), e.fillInStackTrace());
        }
        return values;
    }

    /**
     * 设置响应
     *
     * @param sysLog
     * @param object
     * @return
     */
    public void setOperateResult(SysLog sysLog, Object object) {
        if (Objects.nonNull(object)) {
            if (object instanceof CommonResponse) {
                JSONObject jsonObject = JSONObject.parseObject(JSONObject.toJSONString(object));
                String code = jsonObject.getString(responseCode);
                String message = jsonObject.getString(responseMessage);
                if (CommonResponseEnum.SUCCESS.getCode().toString().equals(code)) {
                    sysLog.setOperateResult(CommonResponseEnum.SUCCESS.getMsg());
                    sysLog.setDescription(message);
                } else {
                    sysLog.setOperateResult(CommonResponseEnum.FAIL.getMsg());
                    sysLog.setDescription(message);
                }
            } else {
                sysLog.setOperateResult(CommonResponseEnum.SUCCESS.getMsg());
                sysLog.setDescription(JSONObject.toJSONString(object));
            }

        }
    }

    /**
     * 设置异常
     *
     * @param sysLog
     * @param exception
     */
    public void setOperateException(SysLog sysLog, Exception exception) {
        if (Objects.nonNull(exception)) {
            sysLog.setOperateResult(CommonResponseEnum.FAIL.getMsg());
            sysLog.setDescription(exception.getMessage());
            sysLog.setException(exception.getMessage());
        }
    }

    /**
     * 获取响应详情
     *
     * @param request
     * @param sysLog
     * @param object
     * @return
     */
    public String getSysLogDetailJson(HttpServletRequest request, SysLog sysLog, Object object) {
        JSONObject jsonObject = new JSONObject();
        jsonObject.put("creatorId", sysLog.getCreatorId());
        jsonObject.put("creatorName", sysLog.getCreatorName());
        jsonObject.put("clientAddress", sysLog.getClientAddress());
        jsonObject.put("serverAddress", sysLog.getServerAddress());
        jsonObject.put("requestMethod", request.getMethod());
        jsonObject.put("operatorResult", sysLog.getOperateResult());
        jsonObject.put("requestParams", sysLog.getRequestParam());
        jsonObject.put("user-agent", request.getHeader("user-agent"));
        jsonObject.put("requestUrl", request.getRequestURI());
        jsonObject.put("requestResult", object);
        jsonObject.put("duration", sysLog.getDuration());
        return jsonObject.toString();
    }

    /**
     * 获取请求参数
     */
    public String getRequestParams(ProceedingJoinPoint point, HttpServletRequest request) throws JsonProcessingException {
        JSONObject jsonParams = new JSONObject();
        if (!Objects.isNull(request) && !Objects.isNull(point)) {
            Map<String, Object> params = getParams(point);
            jsonParams.putAll(params);
        }
        return jsonParams.toJSONString();
    }


    /**
     * 方法执行完流已关闭
     *
     * @param request
     * @return
     */
    public String getInputString(HttpServletRequest request) {
        //String body = StreamUtils.copyToString(request.getInputStream(), StandardCharsets.UTF_8);
        StringBuilder builder = new StringBuilder("");
        BufferedReader reader = null;
        ServletInputStream input = null;
        try {
            input = request.getInputStream();
            reader = new BufferedReader(new InputStreamReader(input, "UTF-8"));
            String lineStr;
            while ((lineStr = reader.readLine()) != null) {
                builder.append(lineStr);
            }
        } catch (Exception e) {
            e.printStackTrace();
        } finally {
            try {
                if (reader != null) {
                    reader.close();
                }
            } catch (Exception e) {
                e.printStackTrace();
            }
            try {
                if (input != null) {
                    input.close();
                }
            } catch (Exception e) {
                e.printStackTrace();
            }
        }
        return builder.toString();
    }


    /**
     * 获取参数
     *
     * @param point
     * @return
     */

    private Map<String, Object> getParams(ProceedingJoinPoint point) throws JsonProcessingException {
        Map<String, Object> map = new HashMap<String, Object>();
        Object[] values = point.getArgs();
        Integer ignoreNum = null;
        Object index = null;
        String[] names = ((CodeSignature) point.getSignature()).getParameterNames();
        Optional<Object> first = Arrays.stream(values).filter(p -> (p instanceof HttpServletResponse || p instanceof HttpServletRequest)).findFirst();
        if (first.isPresent()) {
            index = first.get();
            if (ObjectUtils.isNotEmpty(index)) {
                ignoreNum = Arrays.asList(values).indexOf(index);
            }
        }
        for (int i = 0; i < names.length; i++) {
            if (ObjectUtils.isNotEmpty(index) && ObjectUtils.isNotEmpty(ignoreNum) && ignoreNum.equals(i)) {
                continue;
            }
            if (MultipartFile.class.isInstance(values[i])) {
                map.put(names[i], ((MultipartFile) values[i]).getOriginalFilename());
            } else {
                map.put(names[i], values[i]);
            }
        }
        return map;
    }

    /**
     * 获取request参数
     *
     * @param map
     * @return
     */
    public String paramMapToString(Map<String, String[]> map) {
        String param = "";
        for (Map.Entry<String, String[]> entry : map.entrySet()) {
            String[] values = entry.getValue();
            if (StringUtils.hasLength(param)) {
                param += "&";
            }
            param += entry.getKey() + "=";
            if (!Objects.isNull(values) && values.length > 0) {
                param += values[0];
            }
        }
        return param;
    }


    /**
     * 参数拼装
     */
    public String argsArrayToString(Object[] paramsArray) {
        String params = "";
        if (paramsArray != null && paramsArray.length > 0) {
            for (int i = 0; i < paramsArray.length; i++) {
                if (!Objects.isNull(paramsArray[i]) && !isFilterObject(paramsArray[i])) {
                    try {
                        Object jsonObj = JSON.toJSON(paramsArray[i]);
                        params += jsonObj.toString() + " ";
                    } catch (Exception e) {
                    }
                }
            }
        }
        return params.trim();
    }

    /**
     * 判断是否需要过滤的对象。
     *
     * @param o 对象信息。
     * @return 如果是需要过滤的对象，则返回true；否则返回false。
     */
    @SuppressWarnings("rawtypes")
    public boolean isFilterObject(final Object o) {
        Class<?> clazz = o.getClass();
        if (clazz.isArray()) {
            return clazz.getComponentType().isAssignableFrom(MultipartFile.class);
        } else if (Collection.class.isAssignableFrom(clazz)) {
            Collection collection = (Collection) o;
            for (Iterator iter = collection.iterator(); iter.hasNext(); ) {
                return iter.next() instanceof MultipartFile;
            }
        } else if (Map.class.isAssignableFrom(clazz)) {
            Map map = (Map) o;
            for (Iterator iter = map.entrySet().iterator(); iter.hasNext(); ) {
                Map.Entry entry = (Map.Entry) iter.next();
                return entry.getValue() instanceof MultipartFile;
            }
        }
        return o instanceof MultipartFile || o instanceof HttpServletRequest || o instanceof HttpServletResponse
                || o instanceof BindingResult;
    }

    /**
     * 获取用户名称
     *
     * @return username
     */
    private String getUsername() {
        Authentication authentication = SecurityContextHolder.getContext().getAuthentication();
        if (authentication == null) {
            return null;
        }
        return authentication.getName();
    }

    /**
     * 设置日志明细
     */
    private void setSyslogDetail(SysLog sysLog) {
        try {
            if (OperateTypeEnum.LOGIN.getMessage().equals(sysLog.getOperateType()) || OperateTypeEnum.LOGOUT.getMessage().equals(sysLog.getOperateType())) {
                sysLog.setCreatorId(SafeUtil.getLong(sysLog.getObjectIds()));
                sysLog.setCreatorUsername(sysLog.getObjectNames());
                setSyslogObjectNames(sysLog);
            } else {
                SecurityUser securityUser = JwtTokenUtil.getSecurityUser();
                if (Objects.nonNull(securityUser)) {
                    sysLog.setCreatorId(securityUser.getId());
                    sysLog.setCreatorName(securityUser.getName());
                    sysLog.setCreatorUsername(securityUser.getUsername());
                    setSyslogObjectNames(sysLog);
                }
            }
        } catch (Exception e) {
            log.error("日志获取对象名称异常：{}", e.getMessage());
            log.error(e.getMessage(), e.fillInStackTrace());
        }

    }

    /**
     * 设置日志对象
     */
    private void setSyslogObjectNames(SysLog sysLog) {
        try {
            String objectType = sysLog.getObjectType();
            String objectIds = sysLog.getObjectIds();
            if (Objects.nonNull(objectType) && StringUtils.hasLength(objectIds)) {
                String tableName = OperateObjectEnum.getCodeByMessage(objectType);
                if (StringUtils.hasLength(tableName)) {
                    if (OperateTypeEnum.LOGIN.getMessage().equals(sysLog.getOperateType()) || OperateTypeEnum.LOGOUT.getMessage().equals(sysLog.getOperateType())) {
                        List objectList = getObjectList(tableName, objectIds);
                        if (!CollectionUtils.isEmpty(objectList)) {
                            String objectNames = getObjectNames(objectList, tableName);
                            sysLog.setCreatorName(objectNames);
                            sysLog.setObjectNames(objectNames);
                        }
                    } else {
                        String objectNames = sysLog.getObjectNames();
                        if (!StringUtils.hasLength(objectNames)) {
                            List objectList = getObjectList(tableName, objectIds);
                            if (!CollectionUtils.isEmpty(objectList)) {
                                sysLog.setObjectNames(getObjectNames(objectList, tableName));
                            }
                        }
                    }
                }
            }
        } catch (Exception e) {
            log.error("日志获取对象名称异常：{}", e.getMessage());
            log.error(e.getMessage(), e.fillInStackTrace());
        }

    }

    /**
     * 获取对象列表
     */
    private List getObjectList(String tableName, String objectIds) {
        if (StringUtils.hasLength(tableName) && StringUtils.hasLength(objectIds)) {
            Set<Long> ids = CollectionUtil.stringToLongSet(objectIds, ",");
            IService service = SpringUtils.getBean(CaseFormat.LOWER_UNDERSCORE.to(CaseFormat.LOWER_CAMEL, tableName) + serviceImpSuffix);
//			ProxyHandler handler = new ProxyHandler(service);
//			IService proxy = (IService) Proxy.newProxyInstance(service.getClass().getClassLoader(), service.getClass().getInterfaces(), handler);
            return service.listByIds(ids);
        }
        return null;
    }

    private String getObjectNames(List objectList, String tableName) {
        if (bmsCategoryContentTableName.equals(tableName)) {
            return dealBmsCategoryContent(objectList);
        } else if (bmsPackageContentTableName.equals(tableName)) {
            return dealBmsPackageContent(objectList);
        } else if (bmsCategoryChannelTableName.equals(tableName)) {
            return dealBmsCategoryChannel(objectList);
        } else if (bmsPackageChannelTableName.equals(tableName)) {
            return dealBmsPackageChannel(objectList);
        } else if (cmsScheduleTableName.equals(tableName) || bmsScheduleTableName.equals(tableName)) {
            return dealCmsAndBmsSchedule(objectList);
        } else if (epgNodeTableName.equals(tableName)) {
            return dealEpgNode(objectList);
        } else if (epgOutOrderTableName.equals(tableName)) {
            return dealEpgOutOrder(objectList);
        } else if (epgTemplateTableName.equals(tableName)) {
            return dealEpgTemplate(objectList);
        } else if (epgFileTableName.equals(tableName)) {
            return dealEpgFile(objectList);
        } else {
            return dealObject(objectList);
        }
    }

    private String dealBmsCategoryContent(List objectList) {
        String objectNames = "";
        if (!CollectionUtils.isEmpty(objectList)) {
            for (Object object : objectList) {
                JSONObject jsonObject = JSONObject.parseObject(JSONObject.toJSONString(object));
                String categoryName = jsonObject.getString(categoryNameField);
                String contentName = jsonObject.getString(contentNameField);
                if (StringUtils.hasLength(categoryName) || StringUtils.hasLength(contentName)) {
                    if (StringUtils.hasLength(objectNames)) {
                        objectNames += ",";
                    }
                    if (StringUtils.hasLength(categoryName)) {
                        objectNames += "【栏目】" + categoryName;
                    }
                    if (StringUtils.hasLength(contentName)) {
                        objectNames += "【节目】" + contentName;
                    }
                }
            }
        }
        return objectNames;
    }

    private String dealBmsCategoryChannel(List objectList) {
        String objectNames = "";
        if (!CollectionUtils.isEmpty(objectList)) {
            for (Object object : objectList) {
                JSONObject jsonObject = JSONObject.parseObject(JSONObject.toJSONString(object));
                String categoryName = jsonObject.getString(categoryNameField);
                String channelName = jsonObject.getString(channelNameField);
                if (StringUtils.hasLength(categoryName) || StringUtils.hasLength(channelName)) {
                    if (StringUtils.hasLength(objectNames)) {
                        objectNames += ",";
                    }
                    if (StringUtils.hasLength(categoryName)) {
                        objectNames += "【栏目】" + categoryName;
                    }
                    if (StringUtils.hasLength(channelName)) {
                        objectNames += "【频道】" + channelName;
                    }
                }
            }
        }
        return objectNames;
    }

    private String dealBmsPackageContent(List objectList) {
        String objectNames = "";
        if (!CollectionUtils.isEmpty(objectList)) {
            for (Object object : objectList) {
                JSONObject jsonObject = JSONObject.parseObject(JSONObject.toJSONString(object));
                String packageName = jsonObject.getString(packageNameField);
                String contentName = jsonObject.getString(contentNameField);
                if (StringUtils.hasLength(packageName) || StringUtils.hasLength(contentName)) {
                    if (StringUtils.hasLength(objectNames)) {
                        objectNames += ",";
                    }
                    if (StringUtils.hasLength(packageName)) {
                        objectNames += "【产品包】" + packageName;
                    }
                    if (StringUtils.hasLength(contentName)) {
                        objectNames += "【节目】" + contentName;
                    }
                }
            }
        }
        return objectNames;
    }

    private String dealBmsPackageChannel(List objectList) {
        String objectNames = "";
        if (!CollectionUtils.isEmpty(objectList)) {
            for (Object object : objectList) {
                JSONObject jsonObject = JSONObject.parseObject(JSONObject.toJSONString(object));
                String packageName = jsonObject.getString(packageNameField);
                String channelName = jsonObject.getString(channelNameField);
                if (StringUtils.hasLength(packageName) || StringUtils.hasLength(channelName)) {
                    if (StringUtils.hasLength(objectNames)) {
                        objectNames += ",";
                    }
                    if (StringUtils.hasLength(packageName)) {
                        objectNames += "【产品包】" + packageName;
                    }
                    if (StringUtils.hasLength(channelName)) {
                        objectNames += "【频道】" + channelName;
                    }
                }
            }
        }
        return objectNames;
    }

    private String dealCmsAndBmsSchedule(List objectList) {
        String objectNames = "";
        if (!CollectionUtils.isEmpty(objectList)) {
            for (Object object : objectList) {
                JSONObject jsonObject = JSONObject.parseObject(JSONObject.toJSONString(object));
                String scheduleName = jsonObject.getString(scheduleNameField);
                if (StringUtils.hasLength(scheduleName)) {
                    if (StringUtils.hasLength(objectNames)) {
                        objectNames += ",";
                    }
                    objectNames += scheduleName;
                }
            }
        }
        return objectNames;
    }

    /**
     * 处理epg_node表
     *
     * @param objectList
     * @return
     */
    private String dealEpgNode(List objectList) {
        String objectNames = "";
        if (!CollectionUtils.isEmpty(objectList)) {
            for (Object object : objectList) {
                JSONObject jsonObject = JSONObject.parseObject(JSONObject.toJSONString(object));
                String nodeName = jsonObject.getString(epgNodeNameField);
                if (StringUtils.hasLength(nodeName)) {
                    if (StringUtils.hasLength(objectNames)) {
                        objectNames += ",";
                    }
                    objectNames += nodeName;
                }
            }
        }
        return objectNames;
    }

    /**
     * 处理epg_out_order表
     *
     * @param objectList
     * @return
     */
    private String dealEpgOutOrder(List objectList) {
        String objectNames = "";
        if (!CollectionUtils.isEmpty(objectList)) {
            for (Object object : objectList) {
                JSONObject jsonObject = JSONObject.parseObject(JSONObject.toJSONString(object));
                String outOrderName = jsonObject.getString(epgOutOrderNameField);
                if (StringUtils.hasLength(outOrderName)) {
                    if (StringUtils.hasLength(objectNames)) {
                        objectNames += ",";
                    }
                    objectNames += outOrderName;
                }
            }
        }
        return objectNames;
    }

    /**
     * 处理epg_template表
     *
     * @param objectList
     * @return
     */
    private String dealEpgTemplate(List objectList) {
        String objectNames = "";
        if (!CollectionUtils.isEmpty(objectList)) {
            for (Object object : objectList) {
                JSONObject jsonObject = JSONObject.parseObject(JSONObject.toJSONString(object));
                String templateName = jsonObject.getString(epgTemplateNameField);
                if (StringUtils.hasLength(templateName)) {
                    if (StringUtils.hasLength(objectNames)) {
                        objectNames += ",";
                    }
                    objectNames += templateName;
                }
            }
        }
        return objectNames;
    }

    /**
     * 处理epg_file表
     *
     * @param objectList
     * @return
     */
    private String dealEpgFile(List objectList) {
        String objectNames = "";
        if (!CollectionUtils.isEmpty(objectList)) {
            for (Object object : objectList) {
                JSONObject jsonObject = JSONObject.parseObject(JSONObject.toJSONString(object));
                String fileName = jsonObject.getString(epgFileNameField);
                if (StringUtils.hasLength(fileName)) {
                    if (StringUtils.hasLength(objectNames)) {
                        objectNames += ",";
                    }
                    objectNames += fileName;
                }
            }
        }
        return objectNames;
    }


    private String dealObject(List objectList) {
        String objectNames = "";
        if (!CollectionUtils.isEmpty(objectList)) {
            for (Object object : objectList) {
                JSONObject jsonObject = JSONObject.parseObject(JSONObject.toJSONString(object));
                String objectName = jsonObject.getString(objectNameField);
                if (StringUtils.hasLength(objectName)) {
                    if (StringUtils.hasLength(objectNames)) {
                        objectNames += ",";
                    }
                    objectNames += objectName;
                }
            }
        }
        return objectNames;
    }

}
