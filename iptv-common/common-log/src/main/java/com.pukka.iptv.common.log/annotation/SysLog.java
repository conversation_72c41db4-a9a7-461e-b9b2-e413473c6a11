package com.pukka.iptv.common.log.annotation;

import com.pukka.iptv.common.base.enums.SystemSourceEnums;
import com.pukka.iptv.common.log.enums.OperateObjectEnum;
import com.pukka.iptv.common.log.enums.OperateTypeEnum;

import java.lang.annotation.*;

/**
 * <AUTHOR>
 * @date 2021/8/28
 * 操作日志注解
 */
@Target(ElementType.METHOD)
@Retention(RetentionPolicy.RUNTIME)
@Documented
public @interface SysLog {

	/**
	 * 操作类型
	 */
	public OperateTypeEnum operateType();

	/**
	 * 对象类型
	 */
	public OperateObjectEnum objectType();

	/**
	 * 对象ids
	 */
	public String objectIds() default "";

	/**
	 * 对象names
	 */
	public String objectNames() default "";

	/**
	 * 描述
	 */
	public String description() default "";

	/**
	 * 来源 0:播控 1:EPG
	 * 默认 0
	 * @return
	 */
	public SystemSourceEnums source() default SystemSourceEnums.BOKONG;
}
