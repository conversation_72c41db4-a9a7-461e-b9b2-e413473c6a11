package com.pukka.iptv.common.log.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * @Author: zhengcl
 * @Date: 2021/8/30 10:27
 * 操作类型枚举
 */

@Getter
@AllArgsConstructor
public enum OperateTypeEnum {

    /**
     * 登录
     */
    LOGIN("login","登录"),
    /**
     * 退出
     */
    LOGOUT("logout","退出"),
    /**
     * 刷新令牌
     * */
    REFRESH_TOKEN("refreshToken","刷新令牌"),
    /**
     * 新增
     */
    SAVE("save","新增"),
    /**
     * 修改
     */
    UPDATE("update","修改"),
    /**
     * 修改密码
     */
    UPDATE_PASSWORD("updatePassword","修改密码"),
    /**
     * 重置密码
     */
    RESET_PASSWORD("resetPassword","重置密码"),
    /**
     * 重置密码
     */
    RESET_STATE("resetState","重置状态"),
    /**
     * 删除
     */
    DELETE("delete","删除"),
    /**
     * 导入
     */
    IMPORT("import","导入"),
    /**
     * 导出
     */
    EXPORT("export","导出"),

    /**
     *下载
     */
    DOWNLOAD("download","下载存储文件"),
    /**
     * 生效
     */
    ACTIVE("active","生效"),
    /**
     * 失效
     */
    POSITIVE("positive","失效"),
    /**
     *
     * */
    ACTIVE_OR_POSITIVE("activeOrPositive","生效/失效"),
    /**
     * 锁定
     */
    LOCK("lock","锁定"),
    /**
     * 解锁
     */
    UNLOCK("unlock","解锁"),
    /**
     * 锁定解锁
     * */
    LOCK_OR_UNLOCK("lockOrUnlock","锁定/解锁"),
    /**
     * 设置为正片
     */
    SET_RELEASE("setRelease","设置为正片"),
    /**
     * 设置为预览片
     */
    SET_PREVIEW("setPreview","设置为预览片"),
    /**
     * 自审
     */
    CP_CHECK("cpCheck","自审"),
    /**
     * 终审
     */
    OP_CHECK("opCheck","终审"),
    /**
     * 终审重审
     */
    OP_RECHECK("opRecheck","重审"),
    /**
     * 授权内容引入
     */
    AUTHORIZATION_CONTENT_IMPORT("authorizationContentImport","授权内容引入"),
    /**
     * 授权内容移除
     */
    AUTHORIZATION_CONTENT_REMOVE("authorizationContentRemove","授权内容移除"),
    /**
     * 授权频道引入
     */
    AUTHORIZATION_CHANNEL_IMPORT("authorizationChannelImport","授权频道引入"),
    /**
     * 授权频道移除
     */
    AUTHORIZATION_CHANNEL_REMOVE("authorizationChannelRemove","授权频道移除"),
    /**
     * 授权通道修改
     */
    AUTHORIZATION_PASSAGE_UPDATE("authorizationPassageUpdate","授权通道修改"),
    /**
     * 栏目引入内容
     */
    CATEGORY_CONTENT_IMPORT("categoryContentImport","栏目引入内容"),
    /**
     * 栏目引入频道
     */
    CATEGORY_CHANNEL_IMPORT("categoryChannelImport","栏目引入频道"),
    /**
     * 包引入内容
     */
    PACKAGE_CONTENT_IMPORT("packageContentImport","包引入内容"),
    /**
     * 内容绑栏目
     */
    CONTENT_CATEOGRY_BIND("contentCategoryBind","内容绑栏目"),
    /**
     * 栏目内容排序
     */
    CONTENT_CATEGORY_SORT("categoryContentSort","栏目内容排序"),
    /**
     * 内容绑包
     */
    CONTENT_PACKAGE_BIND("contentPackageBind","内容绑包"),
    /**
     * 内容移除栏目
     */
    CATEGORY_CONTENT_REMOVE("categoryContentRemove","内容移除栏目"),
    /**
     * 内容移除包
     */
    PACKAGE_CONTENT_REMOVE("packageContentRemove","内容移除包"),
    /**
     * 发布状态重置
     */
    PUBLISH_STATUS_RESET("publishStatusReset","发布状态重置"),
    /**
     * 发布状态修改
     */
    PUBLISH_STATUS_UPDATE("publishStatusUpdate","发布状态修改"),
    /**
     * 栏目发布
     * */
    CATEGORY_PUBLISH("categoryPublish","栏目发布"),
    /**
     * 栏目重置发布
     * */
    CATEGORY_RESET_PUBLISH("categoryResetPublish","栏目重置发布状态"),
    /**
     * 栏目发布
     * */
    CATEGORY_UPDATE_PUBLISH_STATUS("categoryUpdatePublishStatus","栏目修改发布状态"),
    /**
     * 栏目生效失效
     * */
    CATEGORY_ACTIVE_OR_POSITIVE("categoryActiveOrPositive", "栏目生效/失效"),
    /**
     * 栏目生效失效
     * */
    CATEGORY_SORT("categorySort", "栏目多选排序"),
    /**
     * 栏目锁定/解锁
     * */
    CATEGORY_LOCK_OR_UNLOCK("categoryLockOrUnlock", "栏目锁定/解锁"),
    /**
     * 栏目修改
     * */
    CATEGORY_UPDATE("categoryUpdate","栏目修改"),
    /**
     * 栏目新增
     * */
    CATEGORY_SAVE("categorySave","栏目新增"),
    /**
     * 栏目回收
     * */
    CATEGORY_RECYCLE("categoryRecycle","栏目回收"),
    /**
     * 栏目回收
     * */
    CATEGORY_DELETE("categoryDelete","栏目删除"),
    /**
     * 产品包发布
     * */
    PACKAGE_PUBLISH("packagePublish","产品包发布"),
    /**
     * 产品包新增
     * */
    PACKAGE_SAVE("packageSave","产品包新增"),
    /**
     * 产品包修改
     * */
    PACKAGE_UPDATE("packageUpdate","产品包修改"),
    /**
     * 产品包删除
     * */
    PACKAGE_DELETE("packageDelete", "产品包删除"),
    /**
     * 产品包锁定/解锁
     * */
    PACKAGE_LOCK_OR_UNLOCK("packageLockOrUnlock", "产品包锁定/解锁"),
    /**
     * 产品包回收
     * */
    PACKAGE_RECYCLE("packageRecycle","产品包回收"),
    PACKAGE_RESET_PUBLISH_STATUS("packageResetPublishStatus","产品包重置发布状态"),
    PACKAGE_UPDATE_PUBLISH_STATUS("packageUpdatePublishStatus","产品包修改发布状态"),
    PACKAGE_ACTIVE_OR_POSITIVE("packageActiveOrPositive","产品包置为生效/失效"),
    /**
     * 内容发布
     */
    CONTENT_PUBLISH("contentPublish","内容发布"),
    PRIORITY_CONTENT_PUBLISH("priorityContentPublish","优先发布"),
    SERIES_CONTENT_PUBLISH("seriescontentPublish","内容发布（单发剧头）"),
    CONTENT_LOCK_OR_UNLOCK("contentLockOrUnlock","媒资锁定/解锁"),
    SP_CONTENT_RECYCLE_ALL("cpContentRecycleAll","媒资单集/剧集一键回收"),
    CP_MOVIE_RECYCLE_ALL("cpMovieRecycleAll","cp侧单集一键回收"),
    CP_BATCH_MOVIE_RECYCLE_ALL("cpBatchMovieRecycleAll","cp侧批量一键回收"),
    CP_EPISODES_RECYCLE_ALL("spContentRecycleAll","cp侧剧集一键回收"),

    CONTENT_UPDATE_PUBLISH_STATUS("contentUpdatePublishStatus","媒资修改发布状态"),
    CONTENT_RESET_PUBLISH_STATUS("contentResetPublishStatus","媒资重置发布状态"),
    CONTENT_ACTIVE_OR_POSITIVE("contentActiveOrPositive","媒资置为生效/失效"),
    CONTENT_CANCEL_TIMED_PUBLISH("ContentCancelTimedPublish","媒资取消定时发布"),
    /**
     * 内容回收
     */
    CONTENT_RECYCLE("contentRecycle","内容回收"),
    /**
     * SP一键回收
     */
    SP_RECYCLE_ALL("spRecycleAll","SP一键回收"),
    /**
     * CP一键回收
     */
    CP_RECYCLE_ALL("cpRecycleAll","CP一键回收"),
    /**
     * 子集发布
     * */
    PROGRAM_PUBLISH("programPublish","子集发布"),
    /**
     * 子集回收
     * */
    PROGRAM_RECYCLE("programRecycle","子集回收"),
    PROGRAM_UPDATE_PUBLISH_STATUS("programUpdatePublishStatus","子集修改发布状态"),
    PROGRAM_RESET_PUBLISH_STATUS("programResetPublishStatus","子集重置发布状态"),
    PROGRAM_ACTIVE_OR_POSITIVE("programActiveOrPositive","子集置为生效/失效"),
    CP_PROGRAM_RECYCLE_ALL("cpProgramRecycleAll","cp侧子集一键回收"),
    /**
     * 栏目内容关系发布
     */
    CATEGORY_CONTENT_PUBLISH("categoryContentPublish","栏目内容关系发布"),
    /**
     * 栏目内容关系修改发布状态
     * */
    CATEGORY_CONTENT_UPDATE_PUBLISH_STATUS("categoryContentUpdatePublishStatus","栏目内容关系修改发布状态"),
    /**
     * 栏目内容关系修改发布状态
     * */
    CATEGORY_CONTENT_SORT("categoryContentSort","栏目内容关系多选排序"),
    /**
     * 栏目内容关系修改发布状态
     * */
    CATEGORY_CONTENT_RESET_PUBLISH_STATUS("categoryContentResetPublishStatus","栏目内容关系重置发布状态"),
    /**
     * 栏目内容关系重置发布状态
     */
    CATEGORY_CONTENT_RESET_PUBLISH("categoryContentResetPublish","栏目内容关系重置发布状态"),
    /**
     * 栏目内容关系发布
     */
    CATEGORY_CONTENT_UPDATE_PUBLISH("categoryContentUpdatePublish","栏目内容关系修改发布状态"),
    /**
     * 包内容关系发布
     */
    PACKAGE_CONTENT_PUBLISH("packageContentPublish","包内容关系发布"),
    PACKAGE_CONTENT_CANCEL_TIMED_PUBLISH("packageContentCancelTimedPublish","包内容关系取消定时发布"),
    /**
     * 包内容关系重置发布状态
     */
    PACKAGE_CONTENT_RESET_PUBLISH("packageContentResetPublish","包内容关系重置发布状态"),
    /**
     * 包内容关系重置发布状态
     */
    PACKAGE_CONTENT_UPDATE_PUBLISH("packageContentUpdatePublish","包内容关系修改发布状态"),
    /**
     * 包频道关系发布
     */
    PACKAGE_CHANNEL_PUBLISH("packageContentPublish","包内容关系发布"),
    /**
     * 栏目内容关系回收
     */
    CATEOGY_CONTENT_RECYCLE("categoryContentRecycle","栏目内容关系回收"),
    /**
     * 包内容关系回收
     */
    PACKAGE_CONTENT_RECYCLE("packageContentRecycle","包内容关系回收"),
    /**
     * 栏目频道关系回收
     */
    CATEOGY_CHANNEL_RECYCLE("categoryChannelRecycle","栏目频道关系回收"),
    /**
     * 包频道关系回收
     */
    PACKAGE_CHANNEL_RECYCLE("packageChannelRecycle","包频道关系回收"),

    /**
     * 视频上报
     */
    RESOURCE_REPORT("resourceReport","视频下载上报"),
    /**
     * EPG事件发布
     * */
    EPG_ORDER_PUBLISH("epgOrderPublish","EPG事件发布"),

    /**
     * 其他
     */
    OTHER("other","其他"),

    /**
     * 频道发布
     */
    PUBLISH_CHANNEL("publishChannel","频道发布"),
    /**
     * 频道回收
     */
    ROLLBACK_CHANNEL("rollbackChannel","频道回收"),

    /**
     *频道一键回收
     */
    ROLLBACK_ALL_CHANNEL("rollbackAllChannel","频道一键回收"),

    CATEGORY_BIND_CHANNEL("categoryBindChannel","频道绑定栏目"),
    PUBLISH("publish","发布"),
    ROLLBACK("rollback","回收"),

    RESET_PUBLISH_STATUS("resetPublishStatus","重置发布状态"),

    UPDATE_PUBLISH_STATUS("updatePublishStatus","修改发布状态"),

    /**
     * 栏目频道排序
     */
    CHANNEL_CATEGORY_SORT("categoryCHANNELSort","栏目频道关系排序"),

    DELETE_TIMED("deleteTimed","取消定时发布"),

    MOVIE_DOWNLOAD("movieDownload","视频介质下载"),

    /**
     * 百度云智能审核
     */
    BAIDU_CLOUD_INTELLIGENT_AUDIT_SAVE("baiduCloudIntelligentAuditSave","百度云智能审核"),
    BAIDU_CLOUD_INTELLIGENT_AUDIT_CANCEL("baiduCloudIntelligentAuditCancel","百度云智能审核取消");
    

    /**
     * 类型
     */
    private String code;

    /**
     * 描述
     */
    private String message;


    /**
     * 根据code获取去value
     * @param code
     * @return
     */
    public static String getMessageByCode(String code){
        for(OperateObjectEnum operateObjectEnum:OperateObjectEnum.values()){
            if(code.equals(operateObjectEnum.getCode())){
                return operateObjectEnum.getMessage();
            }
        }
        return  null;
    }

    /**
     * 根据code获取去value
     * @param message
     * @return
     */
    public static String getCodeByMessage(String message){
        for(OperateObjectEnum operateObjectEnum:OperateObjectEnum.values()){
            if(message.equals(operateObjectEnum.getMessage())){
                return operateObjectEnum.getCode();
            }
        }
        return  null;
    }
}
