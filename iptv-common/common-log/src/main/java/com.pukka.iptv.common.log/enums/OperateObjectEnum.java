package com.pukka.iptv.common.log.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * @Author: zhengcl
 * @Date: 2021/8/30 10:58
 * code:表名
 * message:描述
 */

@Getter
@AllArgsConstructor
public enum OperateObjectEnum {


    /**
     * 单集
     */
    CMS_PROGRAM("cms_program", "CMS单集/子集"),
    /**
     * 剧头
     */
    CMS_SERIES("cms_series", "CMS剧头"),
    /**
     * 频道
     */
    CMS_CHANNEL("cms_channel", "CMS频道"),
    /**
     * 物理频道
     */
    CMS_PHYSICAL_CHANNEL("cms_physical_channel", "CMS物理频道"),
    /**
     * 节目单
     */
    CMS_SCHEDULE("cms_schedule", "CMS节目单"),
    /**
     * 下载上报
     */
    CMS_RESOURCE("cms_resource", "源片"),
    /**
     * 单集
     */
    BMS_PROGRAM("bms_program", "BMS子集"),
    /**
     * 内容
     */
    BMS_CONTENT("bms_content", "BMS内容"),
    /**
     * 频道
     */
    BMS_CHANNEL("bms_channel", "BMS频道"),


    BMS_CATEGORY_CHANNEL("bms_category_channel", "BMS频道与栏目关系"),
    /**
     * 节目单
     */
    BMS_SCHEDULE("bms_schedule", "BMS节目单"),
    /**
     * 物理频道
     */
    BMS_PHYSICAL_CHANNEL("bms_physical_channel", "BMS物理频道"),
    /**
     * 栏目
     */
    BMS_CATEGORY("bms_category", "BMS栏目"),
    /**
     * 产品包
     */
    BMS_PACKAGE("bms_package", "BMS产品包"),
    /**
     * 栏目
     */
    BMS_CATEGORY_CONTENT("bms_category_content", "BMS栏目内容"),
    CMS_SERIES_EXPROT("cms_series_exprot", "剧集导出"),
    CMS_MIVIE_EXPROT("cms_movie_exprot", "单集导出"),
    BMS_CONTENT_EXPROT("cms_content_exprot", "编排媒资导出"),
    /**
     * 产品包
     */
    BMS_PACKAGE_CONTENT("bms_package_content", "BMS产品包内容"),
    /**
     * 授权合同
     */
    SYS_AUTHORIZATION("sys_authorization", "授权合同"),
    /**
     * CP
     */
    SYS_CP("sys_cp", "CP"),
    /**
     * SP
     */
    SYS_SP("sys_sp", "SP"),
    /**
     * 用户
     */
    SYS_USER("sys_user", "用户"),
    /**
     * 角色
     */
    SYS_ROLE("sys_role", "角色"),
    /**
     * 菜单
     */
    SYS_MENU("sys_menu", "菜单"),
    /**
     * 角色菜单
     */
    SYS_ROLE_MENU("sys_role_menu", "角色菜单"),
    /**
     * 用户角色
     */
    SYS_USER_ROLE("sys_user_role", "用户角色"),
    /**
     * 租户
     */
    SYS_TENANT("sys_tenant", "租户"),
    /**
     * 用户租户
     */
    SYS_USER_TENANT("sys_user_tenant", "用户租户"),
    /**
     * 注入通道
     */
    SYS_IN_PASSAGE("sys_in_passage", "注入通道"),
    /**
     * 分发通道
     */
    SYS_OUT_PASSAGE("sys_out_passage", "分发通道"),
    /**
     * 存储
     */
    SYS_STORAGE("sys_storage", "存储"),

    SYS_STORAGE_DIRCTORY("sys_storage_dirctory", "存储账号"),
    /**
     * 数据字典项
     */
    SYS_DICTIONARY_BASE("sys_dictionary_base", "数据字典"),
    /**
     * 数据字典值
     */
    SYS_DICTIONARY_ITEM("sys_dictionary_item", "数据字典子项"),
    /**
     * 系统日志
     */
    SYS_LOG("sys_log", "日志管理"),
    /**
     * CP内容提供商
     */
    SYS_CP_CONTENT_PROVIDER("sys_cp_content_provider", "CP内容提供商"),
    /**
     * 版权信息表
     */
    COPYRIGHT_INFO("copyright_info","版权信息"),
    /**
     * 违禁艺人表
     */
    ARTIST_PROHIBIT("artist_prohibit","违禁艺人"),
    /**
     * 违禁片表
     */
    CMS_PROHIBIT("cms_prohibit","违禁片"),
    /**
     * 违禁规则
     */
    RULE_PROHIBIT("Rule_prohibit","违禁规则"),
    /**
     * EPG分发事件表
     */
    EPG_OUT_ORDER("epg_out_order", "EPG分发事件表"),
    /**
     * EPG节点表
     */
    EPG_NODE("epg_node", "EPG节点表"),
    /**
     * EPG模板表
     */
    EPG_TEMPLATE("epg_template", "EPG模板表"),
    /**
     * EPG文件表
     */
    EPG_FILE("epg_file", "EPG文件表"),
    /**
     * EPG文件表
     */
    SYS_USER_TEMPLATE("sys_user_template", "EPG模板关联用户表"),
    /**
     * 百度云智能审核
     */
    BAIDU_CLOUD_INTELLIGENT_AUDIT("baidu_cloud_intelligent_audit", "百度云智能审核"),
    /**
     * 其他
     */
    OTHER("other", "其他");
    /**
     * 类型
     */
    private String code;

    /**
     * 描述
     */
    private String message;

    /**
     * 根据code获取去value
     *
     * @param code
     * @return
     */
    public static String getMessageByCode(String code) {
        for (OperateObjectEnum operateObjectEnum : OperateObjectEnum.values()) {
            if (code.equals(operateObjectEnum.getCode())) {
                return operateObjectEnum.getMessage();
            }
        }
        return null;
    }

    /**
     * 根据code获取去value
     *
     * @param message
     * @return
     */
    public static String getCodeByMessage(String message) {
        for (OperateObjectEnum operateObjectEnum : OperateObjectEnum.values()) {
            if (message.equals(operateObjectEnum.getMessage())) {
                return operateObjectEnum.getCode();
            }
        }
        return null;
    }

}
