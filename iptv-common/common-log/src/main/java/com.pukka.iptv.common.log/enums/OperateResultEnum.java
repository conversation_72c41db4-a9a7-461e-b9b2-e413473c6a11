package com.pukka.iptv.common.log.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * @Author: zhengcl
 * @Date: 2021/8/30 10:53
 * 操作结果枚举
 */

@Getter
@AllArgsConstructor
public enum OperateResultEnum {

    /**
     * 成功
     */
    SUCCESS("success","成功"),

    /**
     * 失败
     */
    FAILURE("failure","失败");

    /**
     * 类型
     */
    private String code;

    /**
     * 描述
     */
    private String message;
}
