package com.pukka.iptv.common.log.config;

import com.pukka.iptv.common.api.feign.epg.EpgLogFeignClient;
import com.pukka.iptv.common.api.feign.sys.SysLogFeignClient;
import com.pukka.iptv.common.log.aspect.SysLogAspect;
import com.pukka.iptv.common.log.event.SysLogListener;
import lombok.AllArgsConstructor;
import org.springframework.boot.autoconfigure.condition.ConditionalOnWebApplication;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.scheduling.annotation.EnableAsync;

/**
 * <AUTHOR>
 * @date 2021/8/28
 * 日志自动配置
 */

@EnableAsync
@Configuration
@AllArgsConstructor
@ConditionalOnWebApplication
public class LogAutoConfiguration {

    private final SysLogFeignClient sysLogFeignClient;
    private final EpgLogFeignClient epgLogFeignClient;

    @Bean
    public SysLogListener sysLogListener() {
        return new SysLogListener(sysLogFeignClient, epgLogFeignClient);
    }

    @Bean
    public SysLogAspect sysLogAspect(ApplicationEventPublisher publisher) {
        return new SysLogAspect(publisher);
    }

}
