package com.pukka.iptv.common.log.proxy;

import com.baomidou.mybatisplus.extension.service.IService;
import com.pukka.iptv.common.core.util.SpringUtils;
import net.sf.cglib.proxy.Enhancer;

import java.lang.reflect.Proxy;
import java.util.ArrayList;

/**
 * @Author: zhengcl
 * @Date: 2021/11/30 9:41
 */
public class Test {
    public static void main(String[] args) {

        IService service = SpringUtils.getBean("sysUserServiceImpl");
        JdkProxyHandler jdkProxyHandler = new JdkProxyHandler(service);
        IService userServiceProxy = (IService) Proxy.newProxyInstance(service.getClass().getClassLoader(),
                service.getClass().getInterfaces(), jdkProxyHandler);
        userServiceProxy.listByIds(new ArrayList<>());

        CglibProxyHandler cglibProxyHandler = new CglibProxyHandler();
        Enhancer enhancer = new Enhancer();
        enhancer.setSuperclass(service.getClass());
        enhancer.setCallback(cglibProxyHandler);
        IService o = (IService)enhancer.create();
        o.listByIds(new ArrayList<>());
    }

}
