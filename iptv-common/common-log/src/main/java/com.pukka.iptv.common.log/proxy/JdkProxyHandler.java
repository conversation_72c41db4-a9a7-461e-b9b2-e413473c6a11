package com.pukka.iptv.common.log.proxy;

import lombok.extern.slf4j.Slf4j;
import org.springframework.transaction.annotation.Transactional;

import java.lang.reflect.InvocationHandler;
import java.lang.reflect.Method;

/**
 * @Author: zhengcl
 * @Date: 2021/11/29 11:49
 */

@Slf4j
public class JdkProxyHandler implements InvocationHandler {

    private Object object;
    public JdkProxyHandler(Object object){
        this.object = object;
    }

    @Override
    public Object invoke(Object proxy, Method method, Object[] args) throws Throwable {
        //　　在代理真实对象前我们可以添加一些自己的操作
        System.out.println("++++++before " + method.getName() + "++++++");
        //    当代理对象调用真实对象的方法时，其会自动的跳转到代理对象关联的handler对象的invoke方法来进行调用
        Object invoke = method.invoke(object, args);
        //　　在代理真实对象后我们也可以添加一些自己的操作
        System.out.println("++++++before " + method.getName() + "++++++");

        return invoke;
    }

    public static Object getEnumMessage(Class clazz, Object code){
        Object[] enumConstants = clazz.getEnumConstants();
        try {
            for(Object object : enumConstants){
                //获取对象的公开方法，参数标识和方法名称
                Method codeMethod = clazz.getMethod("getCode");
                Method messageMethod = clazz.getMethod("getMessage");
                if(code.equals(codeMethod.invoke(object))){
                    return messageMethod.invoke(object);
                }
            }
        }catch (Exception e){
            e.printStackTrace();
        }
        return null;
    }

    public static Object getEnumCode(Class clazz, Object message){
        Object[] enumConstants = clazz.getEnumConstants();
        try {
            for(Object object : enumConstants){
                //获取对象的公开方法，参数标识和方法名称
                Method codeMethod = clazz.getMethod("getCode");
                Method messageMethod = clazz.getMethod("getMessage");
                if(message.equals(messageMethod.invoke(object))){
                    return codeMethod.invoke(object);
                }
            }
        }catch (Exception e){
            e.printStackTrace();
        }
        return null;
    }
}
