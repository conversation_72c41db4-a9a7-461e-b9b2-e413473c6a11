package com.pukka.iptv.common.log.proxy;

import net.sf.cglib.proxy.Enhancer;
import net.sf.cglib.proxy.MethodInterceptor;
import net.sf.cglib.proxy.MethodProxy;

import java.lang.reflect.Method;

/**
 * @Author: zhengcl
 * @Date: 2021/11/30 9:39
 */

public class CglibProxyHandler implements MethodInterceptor {

    @Override
    public Object intercept(Object object, Method method, Object[] args, MethodProxy methodProxy) throws Throwable {
        System.out.println("++++++before " + methodProxy.getSuperName() + "++++++");
        Object result = methodProxy.invokeSuper(object, args);
        System.out.println("++++++before " + methodProxy.getSuperName() + "++++++");
        return result;
    }
}
