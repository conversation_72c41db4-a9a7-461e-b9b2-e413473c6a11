package com.pukka.iptv.common.log.aspect;

import com.pukka.iptv.common.log.enums.OperateResultEnum;
import com.pukka.iptv.common.log.event.SysLogEvent;
import com.pukka.iptv.common.data.model.sys.SysLog;
import com.pukka.iptv.common.log.util.SysLogUtils;
import lombok.RequiredArgsConstructor;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;

import org.springframework.context.ApplicationEventPublisher;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import javax.servlet.http.HttpServletRequest;
import java.util.Objects;

/**
 * <AUTHOR>
 * @date 2021/8/28
 * 操作日志使用spring event异步入库
 */

@Slf4j
@Aspect
@RequiredArgsConstructor
public class SysLogAspect {

	private final ApplicationEventPublisher publisher;

	@SneakyThrows
	@Around("@annotation(sysLog)")
	public Object around(ProceedingJoinPoint point, com.pukka.iptv.common.log.annotation.SysLog sysLog) {
		// 发送异步日志事件
		Object obj = null;
		Long startTime = System.currentTimeMillis();
		HttpServletRequest request = ((ServletRequestAttributes) Objects
				.requireNonNull(RequestContextHolder.getRequestAttributes())).getRequest();
		SysLog systemLog = SysLogUtils.getSysLog(point,sysLog,request);
		try {
			obj = point.proceed();
		} catch (Exception e) {
			SysLogUtils.setOperateException(systemLog,e);
			throw e;
		} finally {
			try {
				Long endTime = System.currentTimeMillis();
				systemLog.setDuration(endTime - startTime);
				SysLogUtils.setOperateResult(systemLog,obj);
//			systemLog.setDetail(SysLogUtils.getSysLogDetailJson(request,systemLog,obj));
				publisher.publishEvent(new SysLogEvent(systemLog));
			} catch (Exception e) {
				log.error("发布日志异常：{}",e.getMessage());
			}
		}
		return obj;
	}

}
