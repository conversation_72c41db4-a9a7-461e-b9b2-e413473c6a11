spring:
  datasource:
    platform: h2
    driverClassName: org.h2.Driver
    url: jdbc:h2:mem:testdb;MODE=MYSQL;DB_CLOSE_DELAY=-1;DATABASE_TO_LOWER=TRUE
    username: test
    password: test
    schema: classpath:db/schema.sql
    data: classpath:db/data.sql
    initialization-mode: always
  h2:
    console:
      settings:
        trace: true
        web-allow-others: true
      enabled: true
      path: /h2-console

mybatis-plus:
  #mapper文件位置
  mapper-locations: classpath*:mapper/**/*Mapper.xml
  #实体扫描，多个package用逗号或者分号分隔
  #typeAliasesPackage: com.tdx.account_service.entity
  configuration:
    #是否开启自动驼峰命名规则（camel case）映射
    map-underscore-to-camel-case: true
    #配置控制台打印日志Debug，开启sql日志
    #log-impl: org.apache.ibatis.logging.stdout.StdOutImpl
    #这个配置会将执行的sql打印出来，这个可以存放在文件中 StdOutImpl的是只能打印到控制台
    log-impl: org.apache.ibatis.logging.slf4j.Slf4jImpl