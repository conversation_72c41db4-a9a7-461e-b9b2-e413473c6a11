set MODE=MySQL;
CREATE TABLE `rule_prohibit_schedule` (
                                          `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键',
                                          `code` varchar(32) DEFAULT NULL COMMENT '规则唯一标识',
                                          `channel_code` varchar(32) DEFAULT NULL COMMENT '频道Code',
                                          `channel_name` varchar(32) DEFAULT NULL COMMENT '频道名称',
                                          `channel_id` bigint DEFAULT NULL COMMENT '逻辑频道 ID',
                                          `name` varchar(128) DEFAULT NULL COMMENT '规则名称',
                                          `prohibited_words` text COMMENT '违禁词',
                                          `cp_id` bigint DEFAULT NULL,
                                          `cp_name` varchar(128) DEFAULT NULL COMMENT 'CP名称',
                                          `status` tinyint DEFAULT '1' COMMENT '状态1：有效 2：删除',
                                          `match_status` tinyint DEFAULT '1' COMMENT '状态1：精确 2：模糊',
                                          `creator_name` varchar(32) DEFAULT NULL COMMENT '创建人',
                                          `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
                                          `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间'
);

CREATE TABLE `cms_prohibit_schedule` (
                                         `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键',
                                         `code` varchar(32) DEFAULT NULL COMMENT '唯一标识',
                                         `rule_prohibit_code` varchar(32) DEFAULT NULL COMMENT '节目单违禁规则表唯一标识',
                                         `channel_code` varchar(32) DEFAULT NULL COMMENT '频道Code',
                                         `channel_name` varchar(32) DEFAULT NULL COMMENT '频道名称',
                                         `channel_id` bigint DEFAULT NULL COMMENT '逻辑频道 ID',
                                         `program_name` varchar(128) DEFAULT NULL COMMENT '节目名称',
                                         `start_date` varchar(10) DEFAULT NULL COMMENT '节目开播日期',
                                         `start_time` varchar(10) DEFAULT NULL COMMENT '节目开播时间',
                                         `end_time` varchar(10) DEFAULT NULL COMMENT '节目结束时间',
                                         `duration` varchar(10) DEFAULT NULL COMMENT '节目时长(HH24MISS) ',
                                         `storage_duration` int DEFAULT NULL COMMENT 'TVOD 保存时长(小时)缺省为空',
                                         `status` tinyint DEFAULT '1' COMMENT '状态标志1:生效2:失效 ',
                                         `description` varchar(1024) DEFAULT NULL COMMENT '描述信息',
                                         `source` tinyint DEFAULT NULL COMMENT '来源 1：专线注入 2：人工创建',
                                         `genre` varchar(128) DEFAULT NULL COMMENT '节目的分类标签，如“体育”，多个标签用空格或“;”区分',
                                         `cp_id` bigint DEFAULT NULL,
                                         `cp_name` varchar(64) DEFAULT NULL COMMENT 'CP名称',
                                         `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
                                         `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
                                         `creator_name` varchar(32) DEFAULT NULL COMMENT '创建人，只人工上传时可用',
                                         `creator_id` bigint DEFAULT NULL COMMENT '创建人ID来自于sys_user'
);