package com.pukka.iptv.manage;

import com.pukka.iptv.common.data.model.cms.CmsSeries;
import com.pukka.iptv.manage.service.sys.SysDictionaryItemService;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

/**
 * <AUTHOR>
 * @Date 2021/12/1 9:17
 * @Description
 */
@RunWith(SpringRunner.class)
@SpringBootTest(classes = ManageApplication.class)
@Slf4j
public class SysDictionaryItemServiceTest {
    @Autowired
    SysDictionaryItemService sysDictionaryItemService;

    @Test
    public void test(){
        CmsSeries series = new CmsSeries();


        series.setOriginalCountry("China");
        series.setPgmSndClass("古装1");
        sysDictionaryItemService.setDictionaryId(series);

        CmsSeries series1 = new CmsSeries();
        series1.setOriginalCountry("French");
        sysDictionaryItemService.setDictionaryId(series1);
        System.out.println("series: "+  series.toString());
        System.out.println("series1: "+  series1.toString());





    }
}
