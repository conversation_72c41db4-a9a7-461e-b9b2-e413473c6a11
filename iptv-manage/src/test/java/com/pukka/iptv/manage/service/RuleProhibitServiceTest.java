package com.pukka.iptv.manage.service;

import cn.hutool.core.lang.UUID;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.pukka.iptv.common.base.exception.CommonResponseException;
import com.pukka.iptv.common.data.dto.RuleProhibitScheduleDTO;
import com.pukka.iptv.common.data.model.copyright.RuleProhibitSchedule;
import com.pukka.iptv.common.data.model.sys.SysCp;
import com.pukka.iptv.common.redis.service.RedisService;
import com.pukka.iptv.manage.mapper.cms.CmsChannelMapper;
import com.pukka.iptv.manage.mapper.copyright.prohibit.RuleProhibitScheduleMapper;
import com.pukka.iptv.manage.service.copyright.prohibit.impl.RuleProhibitScheduleServiceImpl;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import static org.mockito.ArgumentMatchers.any;
import org.mockito.Mock;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;
import org.mockito.junit.MockitoJUnitRunner;
import static org.mockito.Mockito.when;

import java.util.Arrays;
import java.util.Collections;
import java.util.List;

/**
 * <AUTHOR>
 * @Title: RuleProhibitServiceTest
 * @ProjectName iptv-cloud
 * @date 11/12/2023 10:34 am
 */
@RunWith(MockitoJUnitRunner.class)
public class RuleProhibitServiceTest {

    @Mock
    private RuleProhibitScheduleMapper ruleProhibitScheduleMapper;
    @Mock
    private RedisService redisService;
    @Mock
    private CmsChannelMapper cmsChannelMapper;

    private RuleProhibitScheduleServiceImpl ruleProhibitScheduleService;

    @Before
    public void setUp(){
        ruleProhibitScheduleService = new RuleProhibitScheduleServiceImpl(ruleProhibitScheduleMapper,redisService,cmsChannelMapper);
    }

    @Test
    public void selectListTest(){
        RuleProhibitSchedule prohibitSchedule = new RuleProhibitSchedule();
        prohibitSchedule.setId(1L);
        prohibitSchedule.setCode(UUID.randomUUID().toString());
        prohibitSchedule.setCpId(1L);
        prohibitSchedule.setCpName("通用CP");
        prohibitSchedule.setChannelId(1L);
        prohibitSchedule.setChannelCode("723");
        prohibitSchedule.setChannelName("723");
        prohibitSchedule.setMatchStatus(1);
        prohibitSchedule.setStatus(1);
        prohibitSchedule.setName("芝麻开门违禁词规则");
        prohibitSchedule.setProhibitedWords("芝麻开门");

        Page<RuleProhibitSchedule> page = new Page<>();
        page.setTotal(1);
        page.setSize(1);
        when(ruleProhibitScheduleMapper.selectLists(any(),any())).thenReturn(Arrays.asList(prohibitSchedule));
        IPage<RuleProhibitSchedule> result = ruleProhibitScheduleService.selectList(new Page(),new RuleProhibitScheduleDTO());

        Assert.assertEquals(1,result.getRecords().size());
        Assert.assertEquals(1,result.getTotal());
    }

    @Test
    public void selectListForNameTest(){
        RuleProhibitSchedule prohibitSchedule = new RuleProhibitSchedule();
        prohibitSchedule.setId(1L);
        prohibitSchedule.setCode(UUID.randomUUID().toString());
        prohibitSchedule.setCpId(1L);
        prohibitSchedule.setCpName("通用CP");
        prohibitSchedule.setChannelId(1L);
        prohibitSchedule.setChannelCode("723");
        prohibitSchedule.setChannelName("723");
        prohibitSchedule.setMatchStatus(1);
        prohibitSchedule.setStatus(1);
        prohibitSchedule.setName("芝麻开门违禁词规则");
        prohibitSchedule.setProhibitedWords("芝麻开门");

        RuleProhibitScheduleDTO param = new RuleProhibitScheduleDTO();
        param.setName("芝麻开门违禁词规则\n芝麻开门");

        Page<RuleProhibitSchedule> page = new Page<>();
        page.setTotal(1);
        page.setSize(1);

        when(ruleProhibitScheduleMapper.selectLists(any(),any())).thenReturn(Arrays.asList(prohibitSchedule));
        IPage<RuleProhibitSchedule> result = ruleProhibitScheduleService.selectList(new Page(),param);

        Assert.assertEquals(2,param.getNameList().length);
        Assert.assertNull(param.getName());
        Assert.assertEquals(1,result.getRecords().size());
        Assert.assertEquals(1,result.getTotal());
    }

    @Test
    public void selectListForName2Test(){
        RuleProhibitSchedule prohibitSchedule = new RuleProhibitSchedule();
        prohibitSchedule.setId(1L);
        prohibitSchedule.setCode(UUID.randomUUID().toString());
        prohibitSchedule.setCpId(1L);
        prohibitSchedule.setCpName("通用CP");
        prohibitSchedule.setChannelId(1L);
        prohibitSchedule.setChannelCode("723");
        prohibitSchedule.setChannelName("723");
        prohibitSchedule.setMatchStatus(1);
        prohibitSchedule.setStatus(1);
        prohibitSchedule.setName("芝麻开门违禁词规则");
        prohibitSchedule.setProhibitedWords("芝麻开门");

        RuleProhibitScheduleDTO param = new RuleProhibitScheduleDTO();
        param.setName("芝麻开门违禁词规则");

        Page<RuleProhibitSchedule> page = new Page<>();
        page.setTotal(1);
        page.setSize(1);

        when(ruleProhibitScheduleMapper.selectLists(any(),any())).thenReturn(Collections.singletonList(prohibitSchedule));
        IPage<RuleProhibitSchedule> result = ruleProhibitScheduleService.selectList(new Page(),param);

        Assert.assertEquals("芝麻开门违禁词规则",param.getName());
        Assert.assertNull(param.getNameList());
        Assert.assertEquals(1,result.getRecords().size());
        Assert.assertEquals(1,result.getTotal());
    }

    @Test
    public void saveTest(){
        RuleProhibitSchedule prohibitSchedule = new RuleProhibitSchedule();
        prohibitSchedule.setId(1L);
        prohibitSchedule.setCpId(1L);
        prohibitSchedule.setChannelId(1L);
        prohibitSchedule.setChannelCode("723");
        prohibitSchedule.setChannelName("723");
        prohibitSchedule.setMatchStatus(1);
        prohibitSchedule.setStatus(1);
        prohibitSchedule.setName("芝麻开门违禁词规则");
        prohibitSchedule.setProhibitedWords("芝麻开门");

        SysCp sysCp = new SysCp();
        sysCp.setName("通用CP");

        when(redisService.getCacheMapValue(any(),any())).thenReturn(sysCp);
        ruleProhibitScheduleService.insert(prohibitSchedule);

        Assert.assertNotNull(prohibitSchedule.getCode());
        Assert.assertEquals("通用CP",prohibitSchedule.getCpName());
        verify(ruleProhibitScheduleMapper,times(1)).insert(any());
    }

    @Test
    public void saveForParamTest(){
        RuleProhibitSchedule prohibitSchedule = new RuleProhibitSchedule();
        prohibitSchedule.setId(1L);
        prohibitSchedule.setChannelId(1L);
        prohibitSchedule.setChannelCode("723");
        prohibitSchedule.setChannelName("723");
        prohibitSchedule.setMatchStatus(1);
        prohibitSchedule.setStatus(1);
        prohibitSchedule.setName("芝麻开门违禁词规则");
        prohibitSchedule.setProhibitedWords("芝麻开门");

        SysCp sysCp = new SysCp();
        sysCp.setName("通用CP");

        try {
            ruleProhibitScheduleService.insert(prohibitSchedule);
        }catch (Exception e){
            Assert.assertEquals(CommonResponseException.class,e.getClass());
            Assert.assertEquals("缺少cp相关的参数",e.getMessage());
        }
        verify(ruleProhibitScheduleMapper,times(0)).insert(any());
    }

    @Test
    public void saveForParam1Test(){
        RuleProhibitSchedule prohibitSchedule = new RuleProhibitSchedule();
        prohibitSchedule.setId(1L);
        prohibitSchedule.setChannelCode("723");
        prohibitSchedule.setChannelName("723");
        prohibitSchedule.setMatchStatus(1);
        prohibitSchedule.setStatus(1);
        prohibitSchedule.setName("芝麻开门违禁词规则");
        prohibitSchedule.setProhibitedWords("芝麻开门");

        SysCp sysCp = new SysCp();
        sysCp.setName("通用CP");
        try {
            ruleProhibitScheduleService.insert(prohibitSchedule);
        }catch (Exception e){
            Assert.assertEquals(CommonResponseException.class,e.getClass());
            Assert.assertEquals("缺少频道相关的参数",e.getMessage());
        }
        verify(ruleProhibitScheduleMapper,times(0)).insert(any());
    }

    @Test
    public void saveForParam2Test(){
        RuleProhibitSchedule prohibitSchedule = new RuleProhibitSchedule();
        prohibitSchedule.setId(1L);
        prohibitSchedule.setChannelId(1L);
        prohibitSchedule.setChannelName("723");
        prohibitSchedule.setMatchStatus(1);
        prohibitSchedule.setStatus(1);
        prohibitSchedule.setName("芝麻开门违禁词规则");
        prohibitSchedule.setProhibitedWords("芝麻开门");

        SysCp sysCp = new SysCp();
        sysCp.setName("通用CP");

        try {
            ruleProhibitScheduleService.insert(prohibitSchedule);
        }catch (Exception e){
            Assert.assertEquals(CommonResponseException.class,e.getClass());
            Assert.assertEquals("缺少频道相关的参数",e.getMessage());
        }
        verify(ruleProhibitScheduleMapper,times(0)).insert(any());
    }

    @Test
    public void saveForParam3Test(){
        RuleProhibitSchedule prohibitSchedule = new RuleProhibitSchedule();
        prohibitSchedule.setId(1L);
        prohibitSchedule.setChannelId(1L);
        prohibitSchedule.setChannelCode("723");
        prohibitSchedule.setMatchStatus(1);
        prohibitSchedule.setStatus(1);
        prohibitSchedule.setName("芝麻开门违禁词规则");
        prohibitSchedule.setProhibitedWords("芝麻开门");

        SysCp sysCp = new SysCp();
        sysCp.setName("通用CP");

        try {
            ruleProhibitScheduleService.insert(prohibitSchedule);
        }catch (Exception e){
            Assert.assertEquals(CommonResponseException.class,e.getClass());
            Assert.assertEquals("缺少频道相关的参数",e.getMessage());
        }
        verify(ruleProhibitScheduleMapper,times(0)).insert(any());
    }

    @Test
    public void saveForParam4Test(){
        RuleProhibitSchedule prohibitSchedule = new RuleProhibitSchedule();
        prohibitSchedule.setId(1L);
        prohibitSchedule.setChannelId(1L);
        prohibitSchedule.setChannelCode("723");
        prohibitSchedule.setChannelName("723");
        prohibitSchedule.setCpId(1L);
        prohibitSchedule.setMatchStatus(1);
        prohibitSchedule.setStatus(1);
        prohibitSchedule.setName("芝麻开门违禁词规则");
        prohibitSchedule.setProhibitedWords("芝麻开门");

        SysCp sysCp = new SysCp();
        sysCp.setName("通用CP");

        when(redisService.getCacheMapValue(any(),any())).thenReturn(null);
        try {
            ruleProhibitScheduleService.insert(prohibitSchedule);
        }catch (Exception e){
            Assert.assertEquals(CommonResponseException.class,e.getClass());
            Assert.assertEquals("未找到对应的cp名称",e.getMessage());
        }
        verify(ruleProhibitScheduleMapper,times(0)).insert(any());
    }

    @Test
    public void saveForParam5Test(){
        RuleProhibitSchedule prohibitSchedule = new RuleProhibitSchedule();
        prohibitSchedule.setId(1L);
        prohibitSchedule.setChannelId(1L);
        prohibitSchedule.setChannelCode("723");
        prohibitSchedule.setChannelName("723");
        prohibitSchedule.setCpId(1L);
        prohibitSchedule.setMatchStatus(1);
        prohibitSchedule.setStatus(1);
        prohibitSchedule.setName("芝麻开门违禁词规则");

        SysCp sysCp = new SysCp();
        sysCp.setName("通用CP");

        when(redisService.getCacheMapValue(any(),any())).thenReturn(sysCp);
        try {
            ruleProhibitScheduleService.insert(prohibitSchedule);
        }catch (Exception e){
            Assert.assertEquals(CommonResponseException.class,e.getClass());
            Assert.assertEquals("缺少违禁词参数",e.getMessage());
        }
        verify(ruleProhibitScheduleMapper,times(0)).insert(any());
    }

    @Test
    public void saveForParam6Test(){
        RuleProhibitSchedule prohibitSchedule = new RuleProhibitSchedule();
        prohibitSchedule.setId(1L);
        prohibitSchedule.setChannelId(1L);
        prohibitSchedule.setChannelCode("723");
        prohibitSchedule.setChannelName("723");
        prohibitSchedule.setCpId(1L);
        prohibitSchedule.setStatus(1);
        prohibitSchedule.setName("芝麻开门违禁词规则");
        prohibitSchedule.setProhibitedWords("芝麻开门");

        SysCp sysCp = new SysCp();
        sysCp.setName("通用CP");

        when(redisService.getCacheMapValue(any(),any())).thenReturn(sysCp);
        try {
            ruleProhibitScheduleService.insert(prohibitSchedule);
        }catch (Exception e){
            Assert.assertEquals(CommonResponseException.class,e.getClass());
            Assert.assertEquals("缺少违禁词匹配规则",e.getMessage());
        }
        verify(ruleProhibitScheduleMapper,times(0)).insert(any());
    }

    @Test
    public void saveForParam7Test(){
        RuleProhibitSchedule prohibitSchedule = new RuleProhibitSchedule();
        prohibitSchedule.setId(1L);
        prohibitSchedule.setChannelId(1L);
        prohibitSchedule.setChannelCode("723");
        prohibitSchedule.setChannelName("723");
        prohibitSchedule.setCpId(1L);
        prohibitSchedule.setMatchStatus(1);
        prohibitSchedule.setName("芝麻开门违禁词规则");
        prohibitSchedule.setProhibitedWords("芝麻开门");

        SysCp sysCp = new SysCp();
        sysCp.setName("通用CP");

        when(redisService.getCacheMapValue(any(),any())).thenReturn(sysCp);
        try {
            ruleProhibitScheduleService.insert(prohibitSchedule);
        }catch (Exception e){
            Assert.assertEquals(CommonResponseException.class,e.getClass());
            Assert.assertEquals("缺少违禁词规则启动状态",e.getMessage());
        }
        verify(ruleProhibitScheduleMapper,times(0)).insert(any());
    }

    @Test
    public void updateTest(){
        RuleProhibitSchedule prohibitSchedule = new RuleProhibitSchedule();
        prohibitSchedule.setId(1L);
        prohibitSchedule.setMatchStatus(1);
        prohibitSchedule.setStatus(1);

        ruleProhibitScheduleService.update(prohibitSchedule);
        verify(ruleProhibitScheduleMapper,times(1)).update(any());
    }

    @Test
    public void updateForParamTest(){
        RuleProhibitSchedule prohibitSchedule = new RuleProhibitSchedule();
        prohibitSchedule.setMatchStatus(1);
        prohibitSchedule.setStatus(1);

        try {
            ruleProhibitScheduleService.update(prohibitSchedule);
        }catch (Exception e){
            Assert.assertEquals(CommonResponseException.class,e.getClass());
            Assert.assertEquals("更新操作id不能为空",e.getMessage());
        }
        verify(ruleProhibitScheduleMapper,times(0)).update(any());
    }

    @Test
    public void selectByCodeTest(){
        RuleProhibitSchedule prohibitSchedule = new RuleProhibitSchedule();
        prohibitSchedule.setId(1L);
        prohibitSchedule.setCode(UUID.randomUUID().toString());
        prohibitSchedule.setCpId(1L);
        prohibitSchedule.setCpName("通用CP");
        prohibitSchedule.setChannelId(1L);
        prohibitSchedule.setChannelCode("723");
        prohibitSchedule.setChannelName("723");
        prohibitSchedule.setMatchStatus(1);
        prohibitSchedule.setStatus(1);
        prohibitSchedule.setName("芝麻开门违禁词规则");
        prohibitSchedule.setProhibitedWords("芝麻开门");

        when(ruleProhibitScheduleMapper.selectByCode(any())).thenReturn(prohibitSchedule);

        ruleProhibitScheduleService.selectByCode("111");
        verify(ruleProhibitScheduleMapper,times(1)).selectByCode(any());
    }

    @Test
    public void selectByIdTest(){
        RuleProhibitSchedule prohibitSchedule = new RuleProhibitSchedule();
        prohibitSchedule.setId(1L);
        prohibitSchedule.setCode(UUID.randomUUID().toString());
        prohibitSchedule.setCpId(1L);
        prohibitSchedule.setCpName("通用CP");
        prohibitSchedule.setChannelId(1L);
        prohibitSchedule.setChannelCode("723");
        prohibitSchedule.setChannelName("723");
        prohibitSchedule.setMatchStatus(1);
        prohibitSchedule.setStatus(1);
        prohibitSchedule.setName("芝麻开门违禁词规则");
        prohibitSchedule.setProhibitedWords("芝麻开门");

        when(ruleProhibitScheduleMapper.selectById(any())).thenReturn(prohibitSchedule);

        ruleProhibitScheduleService.selectById(1L);
        verify(ruleProhibitScheduleMapper,times(1)).selectById(any());
    }


    @Test
    public void deleteByIdsTest(){
        when(ruleProhibitScheduleMapper.deleteBatchIds(any())).thenReturn(1);

        ruleProhibitScheduleService.deleteById(Collections.singletonList(1L));
        verify(ruleProhibitScheduleMapper,times(1)).deleteBatchIds(any());
    }

    @Test
    public void selectByChannelCodeTest(){
        RuleProhibitSchedule prohibitSchedule = new RuleProhibitSchedule();
        prohibitSchedule.setId(1L);
        prohibitSchedule.setCode(UUID.randomUUID().toString());
        prohibitSchedule.setCpId(1L);
        prohibitSchedule.setCpName("通用CP");
        prohibitSchedule.setChannelId(1L);
        prohibitSchedule.setChannelCode("723");
        prohibitSchedule.setChannelName("723");
        prohibitSchedule.setMatchStatus(1);
        prohibitSchedule.setStatus(1);
        prohibitSchedule.setName("芝麻开门违禁词规则");
        prohibitSchedule.setProhibitedWords("芝麻开门");

        when(ruleProhibitScheduleMapper.selectByChannelCode(any())).thenReturn(Collections.singletonList(prohibitSchedule));

        List<RuleProhibitSchedule> result = ruleProhibitScheduleService.selectByChannelCode("723");
        Assert.assertEquals(1,result.size());
        verify(ruleProhibitScheduleMapper,times(1)).selectByChannelCode(any());
    }
}
