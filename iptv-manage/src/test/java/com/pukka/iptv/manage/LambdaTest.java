package com.pukka.iptv.manage;

import com.baomidou.mybatisplus.core.exceptions.MybatisPlusException;
import com.baomidou.mybatisplus.core.toolkit.ReflectionKit;
import com.baomidou.mybatisplus.core.toolkit.support.IdeaProxyLambdaMeta;
import com.baomidou.mybatisplus.core.toolkit.support.LambdaMeta;
import com.baomidou.mybatisplus.core.toolkit.support.ReflectLambdaMeta;
import com.baomidou.mybatisplus.core.toolkit.support.ShadowLambdaMeta;
import com.pukka.iptv.common.data.model.bms.BmsPicture;
import org.apache.ibatis.reflection.property.PropertyNamer;

import java.io.*;
import java.lang.invoke.SerializedLambda;
import java.lang.reflect.InvocationTargetException;
import java.lang.reflect.Method;

/**
 * @Author: wz
 * @Date: 2021/11/8 19:48
 * @Description:
 */
public class LambdaTest {

    public static void main(String[] args) {

    }

    interface VFunc<P, R> extends Serializable {
        R callback(P p);
    }

    private static <P, R> R cc(VFunc<P, R> func, P id) {
        return func.callback(id);
    }

    private static <P, R> void cc2(VFunc<P, R> func) {
        Class<? extends VFunc> clazz = func.getClass();
        String name = clazz.getName();
        System.out.println(name);
        System.out.println(func);
        LambdaMeta meta = tranLambda(func);
        String fieldName = PropertyNamer.methodToProperty(meta.getImplMethodName());
        System.out.println(fieldName);
    }

    private static <P, R> LambdaMeta tranLambda(VFunc<P, R> func) {
        try {
            Method method = func.getClass().getDeclaredMethod("writeReplace");
            return new ReflectLambdaMeta((SerializedLambda) ReflectionKit.setAccessible(method).invoke(func));
        } catch (NoSuchMethodException e) {
            // IDEA 调试模式下 lambda 表达式是一个代理
            if (func instanceof java.lang.reflect.Proxy) return new IdeaProxyLambdaMeta((java.lang.reflect.Proxy) func);
            String message = "Cannot find method writeReplace, please make sure that the lambda composite class is currently passed in";
            //throw new MybatisPlusException(message);
        } catch (InvocationTargetException | IllegalAccessException e) {
            // throw new MybatisPlusException(e);
        } catch (RuntimeException e) {
            // JDK 16 模块化后不能访问 java.lang.invoke.SerializedLambda 了，走序列化路线
            // https://gitee.com/baomidou/mybatis-plus/issues/I3XDT9
            if (e.getClass().getName().equals("java.lang.reflect.InaccessibleObjectException")) {
                return new ShadowLambdaMeta(setSerializable(func));
            }
            //throw e;
        }
        return null;
    }

    private static com.baomidou.mybatisplus.core.toolkit.support.SerializedLambda setSerializable(Serializable serializable) {
        try (ByteArrayOutputStream baos = new ByteArrayOutputStream();
             ObjectOutputStream oos = new ObjectOutputStream(baos)) {
            oos.writeObject(serializable);
            oos.flush();
            try (ObjectInputStream ois = new ObjectInputStream(new ByteArrayInputStream(baos.toByteArray())) {
                @Override
                protected Class<?> resolveClass(ObjectStreamClass desc) throws IOException, ClassNotFoundException {
                    Class<?> clazz = super.resolveClass(desc);
                    return clazz == java.lang.invoke.SerializedLambda.class ? com.baomidou.mybatisplus.core.toolkit.support.SerializedLambda.class : clazz;
                }
            }) {
                return (com.baomidou.mybatisplus.core.toolkit.support.SerializedLambda) ois.readObject();
            }
        } catch (IOException | ClassNotFoundException e) {
            throw new MybatisPlusException(e);
        }
    }

    static {
        Long id = 1L;
        Long cc = cc(aLong -> aLong, id);
        System.out.println(cc);

        cc2(BmsPicture::getId);
    }
}
