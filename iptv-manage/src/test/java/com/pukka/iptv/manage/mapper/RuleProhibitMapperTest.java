package com.pukka.iptv.manage.mapper;

import cn.hutool.json.JSON;
import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.pukka.iptv.common.data.dto.RuleProhibitScheduleDTO;
import com.pukka.iptv.common.data.model.copyright.RuleProhibitSchedule;
import com.pukka.iptv.manage.DbTestConfiguration;
import com.pukka.iptv.manage.mapper.copyright.prohibit.RuleProhibitScheduleMapper;
import lombok.extern.slf4j.Slf4j;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.junit.jupiter.MockitoSettings;
import org.mockito.quality.Strictness;
import org.powermock.modules.junit4.PowerMockRunnerDelegate;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;
import org.springframework.test.context.junit4.SpringRunner;

import java.util.List;
import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @Title: RuleProhibitMapperTest
 * @ProjectName iptv-cloud
 * @date 12/12/2023 4:59 pm
 */

@RunWith(SpringRunner.class)
@PowerMockRunnerDelegate(SpringJUnit4ClassRunner.class)
@MockitoSettings(strictness = Strictness.LENIENT)
@ContextConfiguration(classes = DbTestConfiguration.class)
@Slf4j
public class RuleProhibitMapperTest {

    @Test
    public void testGetById(){
        RuleProhibitSchedule ruleProhibitSchedule = prohibitScheduleMapper.selectByCode("HBGD9301026516826275845015949297");
        Assert.assertNotNull(ruleProhibitSchedule);
        Assert.assertEquals("湖南卫视test",ruleProhibitSchedule.getChannelName());
    }

    @Test
    public void updateTest(){
        RuleProhibitScheduleDTO ruleProhibitScheduleDTO = new RuleProhibitScheduleDTO();
        ruleProhibitScheduleDTO.setStatus(1);
        ruleProhibitScheduleDTO.setMatchStatus(1);
        ruleProhibitScheduleDTO.setId(19L);
        int result = prohibitScheduleMapper.update(ruleProhibitScheduleDTO);
        Assert.assertEquals(1,result);
    }

    @Test
    public void selectByChannelCodeTest(){
        List<RuleProhibitSchedule> result = prohibitScheduleMapper.selectByChannelCode("HBGD8387677589692006401970348391");
        Assert.assertEquals(1,result.size());
    }

    @Test
    public void insertTest(){
        RuleProhibitSchedule ruleProhibitSchedule = new RuleProhibitSchedule();
        ruleProhibitSchedule.setStatus(1);
        ruleProhibitSchedule.setMatchStatus(1);
        ruleProhibitSchedule.setChannelName("711");
        ruleProhibitSchedule.setChannelCode("711");
        ruleProhibitSchedule.setChannelId(711L);
        ruleProhibitSchedule.setCpId(7137L);
        ruleProhibitSchedule.setCpName("CP1");
        ruleProhibitSchedule.setStatus(1);
        ruleProhibitSchedule.setMatchStatus(1);
        ruleProhibitSchedule.setProhibitedWords("测试");
        ruleProhibitSchedule.setName("测试");
        int result = prohibitScheduleMapper.insert(ruleProhibitSchedule);
        Assert.assertEquals(1,result);
    }

    @Test
    public void selectListTest(){
        Page page = new Page();
        page.setSize(10);
        page.setPages(1);

        List<RuleProhibitSchedule> result = prohibitScheduleMapper.selectLists(page,new RuleProhibitScheduleDTO());
        Assert.assertNotNull(result);
        Assert.assertEquals(2,result.size());
    }

    @Test
    public void selectListForStatusTest(){
        Page page = new Page();
        page.setSize(10);
        page.setPages(1);

        RuleProhibitScheduleDTO ruleProhibitScheduleDTO = new RuleProhibitScheduleDTO();
        ruleProhibitScheduleDTO.setStatus(1);
        List<RuleProhibitSchedule> result = prohibitScheduleMapper.selectLists(page,ruleProhibitScheduleDTO);
        Assert.assertNotNull(result);
        Assert.assertEquals(3,result.size());
    }

    @Test
    public void selectListForStartTimeTest(){
        Page page = new Page();
        page.setSize(10);
        page.setPages(1);

        RuleProhibitScheduleDTO ruleProhibitScheduleDTO = new RuleProhibitScheduleDTO();
        ruleProhibitScheduleDTO.setStartTime("2023-11-06");
        List<RuleProhibitSchedule> result = prohibitScheduleMapper.selectLists(page,ruleProhibitScheduleDTO);
        Assert.assertNotNull(result);
        Assert.assertEquals(3,result.size());
    }

    @Test
    public void selectListForEndTimeTest(){
        Page page = new Page();
        page.setSize(10);
        page.setPages(1);

        RuleProhibitScheduleDTO ruleProhibitScheduleDTO = new RuleProhibitScheduleDTO();
        ruleProhibitScheduleDTO.setEndTime("2023-12-06");
        List<RuleProhibitSchedule> result = prohibitScheduleMapper.selectLists(page,ruleProhibitScheduleDTO);
        Assert.assertEquals(0,result.size());
    }

    @Test
    public void selectListForChannelNameTest(){
        Page page = new Page();
        page.setSize(10);
        page.setPages(1);

        RuleProhibitScheduleDTO ruleProhibitScheduleDTO = new RuleProhibitScheduleDTO();
        ruleProhibitScheduleDTO.setChannelName("湖南卫视test");
        List<RuleProhibitSchedule> result = prohibitScheduleMapper.selectLists(page,ruleProhibitScheduleDTO);
        Assert.assertNotNull(result);
        Assert.assertEquals(1,result.size());
    }

    @Test
    public void selectListForNameListTest(){
        Page page = new Page();
        page.setSize(10);
        page.setPages(1);

        RuleProhibitScheduleDTO ruleProhibitScheduleDTO = new RuleProhibitScheduleDTO();
        ruleProhibitScheduleDTO.setNameList(new String[]{"测试","11"});
        List<RuleProhibitSchedule> result = prohibitScheduleMapper.selectLists(page,ruleProhibitScheduleDTO);
        Assert.assertNotNull(result);
        Assert.assertEquals(2,result.size());
    }

    @Test
    public void selectListForNameTest(){
        Page page = new Page();
        page.setSize(10);
        page.setPages(1);

        RuleProhibitScheduleDTO ruleProhibitScheduleDTO = new RuleProhibitScheduleDTO();
        ruleProhibitScheduleDTO.setName("测试");
        List<RuleProhibitSchedule> result = prohibitScheduleMapper.selectLists(page,ruleProhibitScheduleDTO);
        Assert.assertNotNull(result);
        Assert.assertEquals(1,result.size());
    }

    @Test
    public void selectListForAllTest(){
        Page page = new Page();
        page.setSize(10);
        page.setPages(1);

        RuleProhibitScheduleDTO ruleProhibitScheduleDTO = new RuleProhibitScheduleDTO();
        ruleProhibitScheduleDTO.setStatus(1);
        ruleProhibitScheduleDTO.setStartTime("2023-11-06");
        ruleProhibitScheduleDTO.setEndTime("2023-12-06");
        ruleProhibitScheduleDTO.setChannelName("湖南卫视test");
        ruleProhibitScheduleDTO.setName("测试");
        List<RuleProhibitSchedule> result = prohibitScheduleMapper.selectLists(page,ruleProhibitScheduleDTO);
        Assert.assertEquals(0,result.size());
    }

    @Resource
    RuleProhibitScheduleMapper prohibitScheduleMapper;
}
