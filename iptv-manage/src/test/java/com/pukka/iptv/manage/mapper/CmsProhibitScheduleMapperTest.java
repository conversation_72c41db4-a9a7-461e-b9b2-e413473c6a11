package com.pukka.iptv.manage.mapper;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.pukka.iptv.common.base.util.UUID;
import com.pukka.iptv.common.data.model.cms.CmsProhibitSchedule;
import com.pukka.iptv.common.data.model.cms.CmsProhibitScheduleDTO;
import com.pukka.iptv.manage.DbTestConfiguration;
import com.pukka.iptv.manage.mapper.cms.CmsProhibitScheduleMapper;
import lombok.extern.slf4j.Slf4j;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.junit.jupiter.MockitoSettings;
import org.mockito.quality.Strictness;
import org.powermock.modules.junit4.PowerMockRunnerDelegate;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;
import org.springframework.test.context.junit4.SpringRunner;

import java.util.Arrays;
import java.util.List;
import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @Title: CmsProhibitScheduleMapperTest
 * @ProjectName iptv-cloud
 * @date 14/12/2023 10:14 am
 */
@RunWith(SpringRunner.class)
@PowerMockRunnerDelegate(SpringJUnit4ClassRunner.class)
@MockitoSettings(strictness = Strictness.LENIENT)
@ContextConfiguration(classes = DbTestConfiguration.class)
@Slf4j
public class CmsProhibitScheduleMapperTest {

    @Test
    public void  batchInsertTest(){
        CmsProhibitSchedule cmsProhibitSchedule = new CmsProhibitSchedule();
        cmsProhibitSchedule.setRuleProhibitCode(UUID.generalPlatformUUID());
        cmsProhibitSchedule.setCode(UUID.generalPlatformUUID());
        cmsProhibitSchedule.setProgramName("大山的儿女");
        cmsProhibitSchedule.setCpName("通用cp");
        cmsProhibitSchedule.setCpId(1L);
        cmsProhibitSchedule.setChannelId(1L);
        cmsProhibitSchedule.setChannelCode("723");
        cmsProhibitSchedule.setChannelName("723");
        cmsProhibitSchedule.setStartDate("20230908");
        cmsProhibitSchedule.setStartTime("190000");
        cmsProhibitSchedule.setEndTime("200000");
        cmsProhibitSchedule.setStorageDuration(1);
        cmsProhibitSchedule.setDuration("12");
        cmsProhibitSchedule.setDescription("");
        cmsProhibitSchedule.setSource(1);

        CmsProhibitSchedule cmsProhibitSchedule1 = new CmsProhibitSchedule();
        cmsProhibitSchedule1.setRuleProhibitCode(UUID.generalPlatformUUID());
        cmsProhibitSchedule1.setCode(UUID.generalPlatformUUID());
        cmsProhibitSchedule1.setProgramName("大山的儿女2");
        cmsProhibitSchedule1.setCpName("通用cp");
        cmsProhibitSchedule1.setCpId(1L);
        cmsProhibitSchedule1.setChannelId(1L);
        cmsProhibitSchedule1.setChannelCode("723");
        cmsProhibitSchedule1.setChannelName("723");
        cmsProhibitSchedule1.setStartDate("20230908");
        cmsProhibitSchedule1.setStartTime("200000");
        cmsProhibitSchedule1.setEndTime("210000");
        cmsProhibitSchedule1.setStorageDuration(1);
        cmsProhibitSchedule1.setDuration("12");
        cmsProhibitSchedule1.setDescription("");
        cmsProhibitSchedule1.setSource(1);

        cmsProhibitScheduleMapper.batchInsert(Arrays.asList(cmsProhibitSchedule,cmsProhibitSchedule1));
    }

    @Test
    public void testDeleteByCodeANDStartTime(){
        CmsProhibitSchedule cmsProhibitSchedule = new CmsProhibitSchedule();
        cmsProhibitSchedule.setStartDate("20220626");
        cmsProhibitSchedule.setStartTime("055000");

        CmsProhibitSchedule cmsProhibitSchedule1 = new CmsProhibitSchedule();
        cmsProhibitSchedule1.setStartDate("20220626");
        cmsProhibitSchedule1.setStartTime("065000");
        cmsProhibitScheduleMapper.deleteByChannelCodeAndStartTime("99180001000000050000000000000221", Arrays.asList(cmsProhibitSchedule,cmsProhibitSchedule1));
    }

    @Test
    public void testDelete(){
        cmsProhibitScheduleMapper.delete(Arrays.asList(1L,2L));
    }

    @Test
    public void testList(){
        List<CmsProhibitSchedule> result = cmsProhibitScheduleMapper.selectList(new Page(),new CmsProhibitScheduleDTO());
        Assert.assertNotNull(result);
        Assert.assertEquals(22,result.size());
    }

    @Test
    public void testListForProgramName(){
        CmsProhibitScheduleDTO cmsProhibitScheduleDTO = new CmsProhibitScheduleDTO();
        cmsProhibitScheduleDTO.setProgramName("故事中国");
        List<CmsProhibitSchedule> result = cmsProhibitScheduleMapper.selectList(new Page(),cmsProhibitScheduleDTO);
        Assert.assertNotNull(result);
        Assert.assertEquals(4,result.size());
    }

    @Test
    public void testListForProgramNameList(){
        CmsProhibitScheduleDTO cmsProhibitScheduleDTO = new CmsProhibitScheduleDTO();
        cmsProhibitScheduleDTO.setProgramNameList(new String[]{"故事中国","故事中国2"});
        List<CmsProhibitSchedule> result = cmsProhibitScheduleMapper.selectList(new Page(),cmsProhibitScheduleDTO);
        Assert.assertNotNull(result);
        Assert.assertEquals(3,result.size());
    }

    @Test
    public void testListForStartDateTime(){
        CmsProhibitScheduleDTO cmsProhibitScheduleDTO = new CmsProhibitScheduleDTO();
        cmsProhibitScheduleDTO.setStartDateTime("2022-06-25");
        List<CmsProhibitSchedule> result = cmsProhibitScheduleMapper.selectList(new Page(),cmsProhibitScheduleDTO);
        Assert.assertNotNull(result);
        Assert.assertEquals(23,result.size());
    }

    @Test
    public void testListForEndDateTime(){
        CmsProhibitScheduleDTO cmsProhibitScheduleDTO = new CmsProhibitScheduleDTO();
        cmsProhibitScheduleDTO.setEndDateTime("2022-06-25");
        List<CmsProhibitSchedule> result = cmsProhibitScheduleMapper.selectList(new Page(),cmsProhibitScheduleDTO);
        Assert.assertEquals(0,result.size());
    }

    @Test
    public void testListForCreatorName(){
        CmsProhibitScheduleDTO cmsProhibitScheduleDTO = new CmsProhibitScheduleDTO();
        cmsProhibitScheduleDTO.setCreatorName("测试");
        List<CmsProhibitSchedule> result = cmsProhibitScheduleMapper.selectList(new Page(),cmsProhibitScheduleDTO);
        Assert.assertEquals(0,result.size());
    }

    @Test
    public void testListForChannelName(){
        CmsProhibitScheduleDTO cmsProhibitScheduleDTO = new CmsProhibitScheduleDTO();
        cmsProhibitScheduleDTO.setChannelName("CCTV9");
        List<CmsProhibitSchedule> result = cmsProhibitScheduleMapper.selectList(new Page(),cmsProhibitScheduleDTO);
        Assert.assertEquals(20,result.size());
    }

    @Test
    public void testListForCpName(){
        CmsProhibitScheduleDTO cmsProhibitScheduleDTO = new CmsProhibitScheduleDTO();
        cmsProhibitScheduleDTO.setCpName("欢网节目单");
        List<CmsProhibitSchedule> result = cmsProhibitScheduleMapper.selectList(new Page(),cmsProhibitScheduleDTO);
        Assert.assertEquals(21,result.size());
    }

    @Test
    public void testListForRuleProhibitCode(){
        CmsProhibitScheduleDTO cmsProhibitScheduleDTO = new CmsProhibitScheduleDTO();
        cmsProhibitScheduleDTO.setRuleProhibitCode("HBGD9294322353289871362947810043");
        List<CmsProhibitSchedule> result = cmsProhibitScheduleMapper.selectList(new Page(),cmsProhibitScheduleDTO);
        Assert.assertEquals(22,result.size());
    }

    @Test
    public void testListForAll(){
        CmsProhibitScheduleDTO cmsProhibitScheduleDTO = new CmsProhibitScheduleDTO();
        cmsProhibitScheduleDTO.setChannelName("CCTV9");
        cmsProhibitScheduleDTO.setCpName("欢网节目单");
        cmsProhibitScheduleDTO.setEndDateTime("2022-06-26");
        cmsProhibitScheduleDTO.setStartDateTime("2022-06-25");
        cmsProhibitScheduleDTO.setProgramNameList(new String[]{"故事中国","故事中国2"});
        List<CmsProhibitSchedule> result = cmsProhibitScheduleMapper.selectList(new Page(),cmsProhibitScheduleDTO);
        Assert.assertEquals(3,result.size());
    }

    @Resource
    CmsProhibitScheduleMapper cmsProhibitScheduleMapper;
}
