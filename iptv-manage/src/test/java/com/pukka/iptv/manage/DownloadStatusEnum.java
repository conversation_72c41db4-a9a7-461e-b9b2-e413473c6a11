package com.pukka.iptv.manage;

/**
 * @Author: zhengcl
 * @Date: 2021/10/21 18:22
 */
public enum DownloadStatusEnum {

    DOWNLOAD_WAIT(1,"待下载"),
    DOWNLOAD_PROCESSING(2,"下载中"),
    DOWNLOAD_SUCCESS(3,"下载成功"),
    DOWNLOAD_FAILURE(4,"下载失败");

    public Integer code;
    public String msg;

    DownloadStatusEnum(Integer code, String msg) {
        this.code = code;
        this.msg = msg;
    }

    public Integer getCode() {
        return code;
    }

    public void setCode(Integer code) {
        this.code = code;
    }

    public String getMsg() {
        return msg;
    }

    public void setMsg(String msg) {
        this.msg = msg;
    }
}
