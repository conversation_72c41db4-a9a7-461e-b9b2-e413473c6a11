package com.pukka.iptv.manage;

import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.stereotype.Component;
import org.springframework.test.context.junit4.SpringRunner;

import java.io.IOException;
import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.*;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * @Author: zhengcl
 * @Date: 2021/10/21 17:25
 */

@RunWith(SpringRunner.class)
@SpringBootTest
@Slf4j
@Component
public class M3U8Util {

    /**
     * @Description 解析m3u8文件，支持单层m3u8文件解析，嵌套自行实现
     * <AUTHOR>
     * @Date 2021-10-21 14:58:24
     * @param content m3u8文件内容
     * */
    public static List resolveSource(String content) throws Exception {
        Pattern pattern = Pattern.compile(".*ts");
        Matcher ma = pattern.matcher(content);
        List<String> list = new ArrayList<String>();
        while (ma.find()) {
            list.add(ma.group());
        }
        return list;
    }


    @Test
    public void testDownload() throws IOException {
        String url = "http://big4.ddooo.com/photoshopcc2017_118916.rar";
        HttpUtil.getAndCreateDownloadDirectory("soft/aaa/","D:/data/");
        HttpUtil.httpDownload(url,"D:/data/soft/aaa/111.rar");
    }


    /**
     * @Description 下载ts列表，单线程下载文件，并将下载失败的文件地址记录
     * <AUTHOR>
     * @Date 2021-10-21 14:58:24
     * @param urlList 解析的Url列表
     * @param sourceUrlHttpPrefix 源文件的http前缀，http://***********/m3u8/abcdef/index.m3u8的前缀为http://***********/m3u8/abcdef/
     * @param targetUrlRelativePrefix 目标文件相对路径前缀http://***********/m3u8/xyz/index.m3u8的前缀为m3u8/xyz
     * @param localUrlAbsolutePrefix 本地存储绝对路径前缀,如/data/storage/vstore/,则下载后的本地m3u8文件为/data/storage/vstore/m3u8/xyz/index.m3u8
     * */
    public static String tsHttpDownload(final List<String> urlList, String sourceUrlHttpPrefix,String targetUrlRelativePrefix, String localUrlAbsolutePrefix) {
        String error="";
        for (String url : urlList) {
            try {
                String completeSourceHttpUrl = sourceUrlHttpPrefix + url;
                String completeTargetLocalUrl = localUrlAbsolutePrefix + targetUrlRelativePrefix + url;
                HttpUtil.httpDownload(completeSourceHttpUrl, completeTargetLocalUrl);
            } catch (Exception e) {
                log.error(url + DownloadStatusEnum.DOWNLOAD_FAILURE.getMsg() + e.getMessage());
            }
        }
        return error;
    }


    /**
     * @Description 下载ts列表，单线程下载文件，并将下载失败的文件地址记录
     * <AUTHOR>
     * @Date 2021-10-21 14:58:24
     * @param urlList 解析的Url列表
     * @param sourceUrlHttpPrefix 源文件的http前缀，http://***********/m3u8/abcdef/index.m3u8的前缀为http://***********/m3u8/abcdef/
     * @param targetUrlRelativePrefix 目标文件相对路径前缀http://***********/m3u8/xyz/index.m3u8的前缀为m3u8/xyz
     * @param localUrlAbsolutePrefix 本地存储绝对路径前缀,如/data/storage/vstore/,则下载后的本地m3u8文件为/data/storage/vstore/m3u8/xyz/index.m3u8
     * */
    public static String tsHttpThreadDownload(final List<String> urlList, String sourceUrlHttpPrefix,String targetUrlRelativePrefix, String localUrlAbsolutePrefix) {
        String error = "";
        try {
            List<Future> futures = new ArrayList<Future>();
            ThreadPoolExecutor poolExecutor = new ThreadPoolExecutor(5,
                    10,
                    10,
                    TimeUnit.SECONDS,
                    new LinkedBlockingQueue<Runnable>(5),
                    new ThreadPoolExecutor.CallerRunsPolicy());
            for (String url : urlList) {
                String completeSourceHttpUrl = sourceUrlHttpPrefix + url;
                String completeTargetLocalUrl = localUrlAbsolutePrefix + targetUrlRelativePrefix + url;
                Future<String> submit = poolExecutor.submit(new M3U8Download(completeSourceHttpUrl,completeTargetLocalUrl));
                // error = submit.get();
                futures.add(submit);
            }
            poolExecutor.shutdown();
            while (true) {
                TimeUnit.SECONDS.sleep(1);
                int activeCount = poolExecutor.getActiveCount();
                long taskCount = poolExecutor.getTaskCount();
                long completedTaskCount = poolExecutor.getCompletedTaskCount();
                log.info(" 存活线程数： " + activeCount + ", 总线程数： " + taskCount + ", 已完成线程数： " + completedTaskCount);
                if (poolExecutor.isTerminated()) {
                    String result = StringUtils.join(futures, "");
                    if(org.springframework.util.StringUtils.hasLength(result)){
                        //任务失败
                        log.info(sourceUrlHttpPrefix+DownloadStatusEnum.DOWNLOAD_FAILURE.getMsg()+result);
                    }else{
                        //任务成功
                        log.info(sourceUrlHttpPrefix+DownloadStatusEnum.DOWNLOAD_SUCCESS.getMsg());
                    }
                    break;
                }
            }
        }catch (Exception e){
            log.error(DownloadStatusEnum.DOWNLOAD_FAILURE.getMsg(), e.fillInStackTrace());
        }
        return error;
    }

    protected static class M3U8Download implements Callable<String> {
        private String completeSourceHttpUrl;
        private String completeTargetLocalUrl;

        public M3U8Download(String completeSourceHttpUrl, String completeTargetLocalUrl) {
            this.completeSourceHttpUrl = completeSourceHttpUrl;
            this.completeTargetLocalUrl = completeTargetLocalUrl;
        }

        @Override
        public String call() throws Exception {
            String result = "";
            try {
                boolean r = HttpUtil.httpDownload(completeSourceHttpUrl,completeTargetLocalUrl);
                if (!r) {
                    result = completeSourceHttpUrl+DownloadStatusEnum.DOWNLOAD_FAILURE.getMsg();
                }
            } catch (Exception e) {
                log.error(completeSourceHttpUrl + DownloadStatusEnum.DOWNLOAD_FAILURE.getMsg() + e.getMessage());
                result = completeSourceHttpUrl+DownloadStatusEnum.DOWNLOAD_FAILURE.getMsg();
            }
            return result;
        }
    }

}
