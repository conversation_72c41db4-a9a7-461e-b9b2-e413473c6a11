package com.pukka.iptv.manage;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.pukka.iptv.common.data.model.bms.BmsPackage;
import com.pukka.iptv.manage.service.bms.BmsPackageService;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import java.util.ArrayList;
import java.util.List;

@Slf4j
@RunWith(SpringRunner.class)
@SpringBootTest()
public class TestCopyPackage {

    @Autowired
    BmsPackageService bmsPackageService;


    @Test
    public void test() {
       /* LambdaQueryWrapper<BmsPackage> sourceQuery = Wrappers.lambdaQuery();
        sourceQuery.eq(BmsPackage::getCode, "99180001000000110000000000000758").eq(BmsPackage::getSpId, 57L);
        BmsPackage sourcePackage = bmsPackageService.getOne(sourceQuery);

        LambdaQueryWrapper<BmsPackage> targetQuery = Wrappers.lambdaQuery();
        targetQuery.in(BmsPackage::getCode, "99180001000000110000000000000765").eq(BmsPackage::getSpId, 57L);
        List<BmsPackage> targetPackage = bmsPackageService.list(targetQuery);

        bmsPackageService.copyPackageContent(sourcePackage, targetPackage);*/
        List<String> codes = new ArrayList<>();
        codes.add("99180001000000110000000000000586");
        codes.add("99180001000000110000000000000587");
        codes.add("99180001000000110000000000000774");
        codes.add("99180001000000110000000000000695");
        codes.add("99180001000000110000000000000696");
        codes.add("99180001000000110000000000000756");
        codes.add("99180001000000110000000000000756");

        LambdaQueryWrapper<BmsPackage> sourceQuery1 = Wrappers.lambdaQuery();
        sourceQuery1.in(BmsPackage::getCode, codes).eq(BmsPackage::getSpId, 75L);
        List<BmsPackage> sourcePackage1 = bmsPackageService.list(sourceQuery1);


        LambdaQueryWrapper<BmsPackage> targetQuery1 = Wrappers.lambdaQuery();
        targetQuery1.in(BmsPackage::getCode, "99180001000000110000000000000802").eq(BmsPackage::getSpId, 75L);
        List<BmsPackage> targetPackage1 = bmsPackageService.list(targetQuery1);

        sourcePackage1.forEach(a -> System.out.println(a.toString()));
        targetPackage1.forEach(a -> System.out.println("target" + a.toString()));

        bmsPackageService.copyPackageContent(sourcePackage1, targetPackage1);

    }

}
