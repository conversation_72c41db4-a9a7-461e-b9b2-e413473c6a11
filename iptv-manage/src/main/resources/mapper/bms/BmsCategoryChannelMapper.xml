<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.pukka.iptv.manage.mapper.bms.BmsCategoryChannelMapper">

    <!-- 可根据自己的需求，是否要使用 -->
    <resultMap type="com.pukka.iptv.common.data.model.bms.BmsCategoryChannel" id="bmsCategoryChannelMap">
        <result property="id" column="id"/>
        <result property="bmsChannelId" column="bms_channel_id"/>
        <result property="cmsChannelCode" column="cms_channel_code"/>
        <result property="channelName" column="channel_name"/>
        <result property="categoryId" column="category_id"/>
        <result property="categoryCode" column="category_code"/>
        <result property="categoryName" column="category_name"/>
        <result property="spId" column="sp_id"/>
        <result property="spName" column="sp_name"/>
        <result property="status" column="status"/>
        <result property="createTime" column="create_time"/>
        <result property="updateTime" column="update_time"/>
        <result property="sequence" column="sequence"/>
        <result property="publishStatus" column="publish_status"/>
        <result property="outPassageNames" column="out_passage_names"/>
        <result property="outPassageIds" column="out_passage_ids"/>
        <result property="source" column="source"/>
        <result property="publishTime" column="publish_time"/>
        <result property="publishDescription" column="publish_description"/>
        <result property="creatorName" column="creator_name"/>
        <result property="creatorId" column="creator_id"/>
        <result property="contentType" column="content_type"/>
    </resultMap>
    <resultMap type="com.pukka.iptv.common.data.vo.bms.BmsCategoryChannelPageVO" id="bmsCategoryChannelPageMap">
        <result property="id" column="id"/>
        <result property="bmsChannelId" column="bms_channel_id"/>
        <result property="cmsChannelCode" column="cms_channel_code"/>
        <result property="channelName" column="channel_name"/>
        <result property="categoryId" column="category_id"/>
        <result property="categoryCode" column="category_code"/>
        <result property="categoryName" column="category_name"/>
        <result property="spId" column="sp_id"/>
        <result property="spName" column="sp_name"/>
        <result property="status" column="status"/>
        <result property="createTime" column="create_time"/>
        <result property="updateTime" column="update_time"/>
        <result property="sequence" column="sequence"/>
        <result property="publishStatus" column="publish_status"/>
        <result property="outPassageNames" column="out_passage_names"/>
        <result property="outPassageIds" column="out_passage_ids"/>
        <result property="source" column="source"/>
        <result property="publishTime" column="publish_time"/>
        <result property="publishDescription" column="publish_description"/>
        <result property="creatorName" column="creator_name"/>
        <result property="creatorId" column="creator_id"/>
        <result property="contentType" column="content_type"/>
        <result property="channelPublishStatus" column="channel_publish_status"/>
        <result property="channelOutPassageNames" column="channel_out_passage_names"/>
        <result property="channelNumber" column="channel_number"/>
    </resultMap>
    <select id="getCategoryContentPage" resultMap="bmsCategoryChannelPageMap">
        select distinct a.*,b.publish_status as channel_publish_status,b.out_passage_names as
        channel_out_passage_names,b.channel_number
        from bms_category_channel a
        left join bms_channel b on a.bms_channel_id = b.id
        where
        a.sp_id = #{bmsCategoryChannel.spId}
        <if test="bmsCategoryChannel.categoryId != null">
            and a.category_id = #{bmsCategoryChannel.categoryId}
        </if>
        <choose>
            <when test="names != null">
                and b.`name` in
                <foreach collection="names" item="n" open="(" separator="," close=")">
                    #{n}
                </foreach>
            </when>
            <otherwise>
                <if test="name != null">
                    and b.`name` like concat('%',#{name},'%')
                </if>
            </otherwise>
        </choose>
        <if test="bmsCategoryChannel.publishStatus != null">
            and a.publish_status = #{bmsCategoryChannel.publishStatus}
        </if>
        <if test="channelCpId != null">
            and b.cp_id = #{channelCpId}
        </if>
        <if test="bmsCategoryChannel.bmsChannelId != null">
            and a.bms_channel_id = #{bmsCategoryChannel.bmsChannelId}
        </if>
        order by sequence,id desc
    </select>

    <select id="getlockStatusByRelationId" resultMap="bmsCategoryChannelMap">
        SELECT
	        b.name
        FROM
	        bms_category_channel a
	        LEFT JOIN bms_category b ON a.category_id = b.id
	        where a.id = #{id} and b.lock_status=2
	        limit 1
</select>

    <update id="updatePublishStatus">
        update bms_category_channel
        <trim prefix="set" suffixOverrides=",">
            <trim prefix="publish_status = case" suffix="end,">
                <foreach collection="entityList" item="item" index="index">
                    <if test="item.publishStatus != null and item.parentCode != null" >
                        when category_code = #{item.parentCode}
                        <if test="item.elementCode != null">
                            and cms_channel_code = #{item.elementCode}
                        </if>
                        then #{item.publishStatus}
                    </if>
                </foreach>
            </trim>
            publish_time = CURRENT_TIMESTAMP
        </trim>
        <where>
            <trim prefix="AND category_code IN">
                <foreach collection="entityList" index="index" item="item" separator="," open="(" close=")">
                    #{item.parentCode}
                </foreach>
            </trim>
            <trim prefix="AND cms_channel_code IN">
                <foreach collection="entityList" index="index" item="item" separator="," open="(" close=")">
                    #{item.elementCode}
                </foreach>
            </trim>
            <trim prefix="AND sp_id IN">
                <foreach collection="spIdList" index="index" item="spId" separator="," open="(" close=")">
                    #{spId}
                </foreach>
            </trim>
        </where>
    </update>

    <delete id="deleteByBmsChannelIdAndSpId">
        DELETE FROM  bms_category_channel
        WHERE
        (bms_channel_id, sp_id) IN
        <foreach collection="bmsChannels" item="item" index="index" separator="," open="(" close=")">
            (#{item.id}, #{item.spId})
        </foreach>
    </delete>
</mapper>