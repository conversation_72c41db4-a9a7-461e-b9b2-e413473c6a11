<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.pukka.iptv.manage.mapper.bms.BmsPackageMapper">

	<!-- 可根据自己的需求，是否要使用 -->
    <resultMap type="com.pukka.iptv.common.data.model.bms.BmsPackage" id="bmsPackageMap">
        <result property="id" column="id"/>
        <result property="code" column="code"/>
        <result property="name" column="name"/>
        <result property="type" column="type"/>
        <result property="classifiation" column="classifiation"/>
        <result property="sortName" column="sort_name"/>
        <result property="searchName" column="search_name"/>
        <result property="rentalPeriod" column="rental_period"/>
        <result property="orderNumber" column="order_number"/>
        <result property="licensingWindowStart" column="licensing_window_start"/>
        <result property="licensingWindowEnd" column="licensing_window_end"/>
        <result property="price" column="price"/>
        <result property="status" column="status"/>
        <result property="description" column="description"/>
        <result property="createTime" column="create_time"/>
        <result property="updateTime" column="update_time"/>
        <result property="spId" column="sp_id"/>
        <result property="spName" column="sp_name"/>
        <result property="bmsSpChannelName" column="bms_sp_channel_name"/>
        <result property="outPassageIds" column="out_passage_ids"/>
        <result property="outPassageNames" column="out_passage_names"/>
        <result property="publishStatus" column="publish_status"/>
        <result property="publishTime" column="publish_time"/>
        <result property="publishDescription" column="publish_description"/>
        <result property="creatorName" column="creator_name"/>
        <result property="creatorId" column="creator_id"/>
        <result property="source" column="source"/>
        <result property="priceType" column="price_type"/>
        <result property="lockStatus" column="lock_status"/>
        <result property="sequence" column="sequence"/>
    </resultMap>
    <update id="updatePublishStatus">
        update bms_package
        <trim prefix="set" suffixOverrides=",">
            <trim prefix="publish_status = case" suffix="end,">
                <foreach collection="entityList" item="item" index="index">
                    <if test="item.publishStatus !=null">
                        when code=#{item.code} then #{item.publishStatus}
                    </if>
                </foreach>
            </trim>
            publish_time = CURRENT_TIMESTAMP
        </trim>
        <where>
            <trim prefix="AND code IN">
                <foreach collection="entityList" index="index" item="item" separator="," open="(" close=")">
                    #{item.code}
                </foreach>
            </trim>
            <trim prefix="AND sp_id IN">
                <foreach collection="spIdList" index="index" item="item" separator="," open="(" close=")">
                    #{item}
                </foreach>
            </trim>
        </where>
    </update>
</mapper>