<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.pukka.iptv.manage.mapper.bms.BmsCategoryContentMapper">

    <!-- 可根据自己的需求，是否要使用 -->
    <resultMap type="com.pukka.iptv.common.data.model.bms.BmsCategoryContent" id="bmsCategoryContentMap">
        <result property="id" column="id"/>
        <result property="bmsContentId" column="content_id"/>
        <result property="cmsContentCode" column="cms_content_code"/>
        <result property="contentType" column="content_type"/>
        <result property="categoryId" column="category_id"/>
        <result property="categoryCode" column="category_code"/>
        <result property="categoryName" column="category_name"/>
        <result property="spId" column="sp_id"/>
        <result property="spName" column="sp_name"/>
        <result property="status" column="status"/>
        <result property="createTime" column="create_time"/>
        <result property="updateTime" column="update_time"/>
        <result property="sequence" column="sequence"/>
        <result property="contentName" column="content_name"/>
        <result property="publishStatus" column="publish_status"/>
        <result property="outPassageNames" column="out_passage_names"/>
        <result property="outPassageIds" column="out_passage_ids"/>
        <result property="source" column="source"/>
        <result property="publishTime" column="publish_time"/>
        <result property="publishDescription" column="publish_description"/>
        <result property="creatorName" column="creator_name"/>
        <result property="creatorId" column="creator_id"/>
    </resultMap>


    <!-- 已绑定的栏目内容 -->
    <resultMap type="com.pukka.iptv.common.data.vo.bms.BmsCategoryBindContentVO" id="bmsCategoryBindContentVOMap">
        <!-- bms_category_content 表字段 -->
        <!-- 重复字段使用 bcc 前缀开头 -->
        <result column="bcc_out_passage_ids" property="bccOutPassageIds"/>
        <result column="bcc_out_passage_names" property="bccOutPassageNames"/>
        <result column="bcc_publish_status" property="bccPublishStatus"/>
        <result column="bcc_publish_time" property="bccPublishTime"/>
        <result column="bcc_publish_description" property="bccPublishDescription"/>
        <result column="bcc_sequence" property="bccSequence"/>
        <result column="creator_name" property="creatorName"/>
        <result column="create_time" property="createTime"/>
        <result column="category_name" property="categoryName"/>
        <result column="bcc_timed_publish" property="bccTimedPublish"/>
        <result column="bcc_timed_publish_status" property="bccTimedPublishStatus"/>
        <result column="bcc_timed_publish_description" property="bccTimedPublishDescription"/>
        <!-- bms_content 表字段 -->

        <result column="content_name" property="contentName"/>
        <result column="content_type" property="contentType"/>
        <result column="cms_content_code" property="cmsContentCode"/>
        <result column="cms_content_id" property="cmsContentId"/>
        <result column="pgm_category" property="pgmCategory"/>
        <result column="publish_status" property="publishStatus"/>
        <result column="out_passage_names" property="outPassageNames"/>
        <result column="licensing_window_start" property="licensingWindowStart"/>
        <result column="licensing_window_end" property="licensingWindowEnd"/>
        <result column="sp_name" property="spName"/>
        <result column="cp_name" property="cpName"/>
        <result column="content_provider" property="contentProvider"/>
        <result column="display_as_last_chance" property="displayAsLastChance"/>
        <result column="op_check_status" property="opCheckStatus"/>
        <result column="out_passage_names" property="outPassageNames"/>
        <result column="out_passage_ids" property="outPassageIds"/>
        <result column="status" property="status"/>
        <result column="original_name" property="originalName"/>
        <!-- 关联栏目-->
        <result column="category_ids" property="categoryIds"/>
        <result column="category_names" property="categoryNames"/>
    </resultMap>

    <!--    BmsContentNotBindCategoryVO-->
    <resultMap type="com.pukka.iptv.common.data.vo.bms.BmsContentNotBindCategoryVO" id="bmsContentNotBindCategoryVO">
        <result property="id" column="id"/>
        <result property="name" column="name"/>
        <result property="pgmCategory" column="pgm_category"/>
        <result property="pgmSndClass" column="pgm_snd_class"/>
        <result property="cpId" column="cp_id"/>
        <result property="cpName" column="cp_name"/>
        <result property="createTime" column="create_time"/>
        <result property="updateTime" column="update_time"/>
        <result property="opCheckStatus" column="op_check_status"/>
        <result property="publishStatus" column="publish_status"/>
        <result property="originalName" column="original_name"/>
    </resultMap>

    <!--查询 栏目内容关系 -->
    <select id="queryCategoryBindContentList" resultMap="bmsCategoryBindContentVOMap">
        select
        bcc.id,
        bcc.out_passage_ids as bcc_out_passage_ids,
        bcc.out_passage_names as bcc_out_passage_names,
        bcc.publish_status as bcc_publish_status,
        bcc.publish_time as bcc_publish_time,
        bcc.publish_description as bcc_publish_description,
        bcc.category_name,
        bcc.source,
        bcc.creator_name,
        bcc.create_time,
        bcc.sequence as bcc_sequence,
        bcc.timed_publish as bcc_timed_publish,
        bcc.timed_publish_status as bcc_timed_publish_status,
        bcc.timed_publish_description as bcc_timed_publish_description,
        bc.name as content_name,
        bc.content_type, bc.publish_status,
        bc.cms_content_code,
        bc.cms_content_id,
        bc.original_name,
        bc.out_passage_ids,bc.out_passage_names,
        bc.pgm_category, bc.sp_name,
        bc.cp_name, bc.content_provider, bc.licensing_window_start, bc.licensing_window_end,
        bc.display_as_last_chance,bc.op_check_status,
        bc.category_ids,bc.category_names,bc.status
        from
        bms_category_content as bcc left join bms_content as bc
        on
        bcc.bms_content_id = bc.id
        <where>
            <if test="queryReq.contentId != null">
                and bc.id = #{queryReq.contentId}
            </if>
            <if test="queryReq.categoryId != null">
                and bcc.category_id = #{queryReq.categoryId}
            </if>
            <choose>
                <when test="queryReq.names != null">
                    and bc.`name` in
                    <foreach collection="queryReq.names" item="n" open="(" separator="," close=")">
                        #{n}
                    </foreach>
                </when>
                <otherwise>
                    <if test="queryReq.name != null">
                        and bc.`name` LIKE CONCAT('%',trim(#{queryReq.name}),'%')
                    </if>
                </otherwise>
            </choose>

            <choose>
                <when test="queryReq.categoryNames != null">
                    and bcc.category_name in
                    <foreach collection="queryReq.categoryNames" item="n" open="(" separator="," close=")">
                        #{n}
                    </foreach>
                </when>
                <otherwise>
                    <if test="queryReq.categoryName != null">
                        and bcc.category_name LIKE CONCAT('%',trim(#{queryReq.categoryName}),'%')
                    </if>
                </otherwise>
            </choose>


            <if test="queryReq.originalName != null and queryReq.originalName != ''">
                and bc.original_name like CONCAT('%',#{queryReq.originalName},'%')
            </if>
            <if test="queryReq.cpId != null">
                and bc.cp_id = #{queryReq.cpId}
            </if>
            <if test="queryReq.spId != null">
                and bc.sp_id = #{queryReq.spId}
            </if>
            <if test="queryReq.contentType != null">
                and bc.content_type
                <if test="queryReq.contentType == 1">
                    in(1,5)
                </if>
                <if test="queryReq.contentType == 3">
                    in(3,4)
                </if>
            </if>
            <if test="queryReq.publishStatus != null">
                and bcc.publish_status = #{queryReq.publishStatus}
            </if>
            <if test="queryReq.pgmCategoryId != null">
                and bc.pgm_category_id = #{queryReq.pgmCategoryId}
            </if>
        </where>
        order by bcc.sequence ,id desc
    </select>

    <select id="queryCategoryBindContentListForExport" resultMap="bmsCategoryBindContentVOMap">
        select
        bcc.id,
        bcc.out_passage_ids as bcc_out_passage_ids,
        bcc.out_passage_names as bcc_out_passage_names,
        bcc.publish_status as bcc_publish_status,
        bcc.publish_time as bcc_publish_time,
        bcc.publish_description as bcc_publish_description,
        bcc.category_name,
        bcc.source,
        bcc.creator_name,
        bcc.create_time,
        bcc.sequence as bcc_sequence,
        bcc.timed_publish as bcc_timed_publish,
        bcc.timed_publish_status as bcc_timed_publish_status,
        bcc.timed_publish_description as bcc_timed_publish_description,
        bc.name as content_name,
        bc.content_type, bc.publish_status,
        bc.cms_content_code,
        bc.cms_content_id,
        bc.original_name,
        bc.out_passage_ids,bc.out_passage_names,
        bc.pgm_category, bc.sp_name,
        bc.cp_name, bc.content_provider, bc.licensing_window_start, bc.licensing_window_end,
        bc.display_as_last_chance,bc.op_check_status,
        bc.category_ids,bc.category_names,bc.status
        from
        bms_category_content as bcc left join bms_content as bc
        on
        bcc.bms_content_id = bc.id
        <where>
            <if test="queryReq.contentId != null">
                and bc.id = #{queryReq.contentId}
            </if>
            <if test="queryReq.categoryId != null">
                and bcc.category_id = #{queryReq.categoryId}
            </if>
            <choose>
                <when test="queryReq.names != null">
                    and bc.`name` in
                    <foreach collection="queryReq.names" item="n" open="(" separator="," close=")">
                        #{n}
                    </foreach>
                </when>
                <otherwise>
                    <if test="queryReq.name != null">
                        and bc.`name` LIKE CONCAT('%',trim(#{queryReq.name}),'%')
                    </if>
                </otherwise>
            </choose>

            <choose>
                <when test="queryReq.categoryNames != null">
                    and bcc.category_name in
                    <foreach collection="queryReq.categoryNames" item="n" open="(" separator="," close=")">
                        #{n}
                    </foreach>
                </when>
                <otherwise>
                    <if test="queryReq.categoryName != null">
                        and bcc.category_name LIKE CONCAT('%',trim(#{queryReq.categoryName}),'%')
                    </if>
                </otherwise>
            </choose>


            <if test="queryReq.originalName != null and queryReq.originalName != ''">
                and bc.original_name like CONCAT('%',#{queryReq.originalName},'%')
            </if>
            <if test="queryReq.cpId != null">
                and bc.cp_id = #{queryReq.cpId}
            </if>
            <if test="queryReq.spId != null">
                and bc.sp_id = #{queryReq.spId}
            </if>
            <if test="queryReq.contentType != null">
                and bc.content_type
                <if test="queryReq.contentType == 1">
                    in(1,5)
                </if>
                <if test="queryReq.contentType == 3">
                    in(3,4)
                </if>
            </if>
            <if test="queryReq.publishStatus != null">
                and bcc.publish_status = #{queryReq.publishStatus}
            </if>
            <if test="queryReq.pgmCategoryId != null">
                and bc.pgm_category_id = #{queryReq.pgmCategoryId}
            </if>
        </where>
        order by bcc.sequence ,id desc
    </select>

    <select id="queryContentsWhichNotBindThatCategory" resultMap="bmsContentNotBindCategoryVO">
        select id,`name`,content_type,cp_id,cp_name,
        pgm_category,pgm_snd_class,publish_status,op_check_status,
        update_time,create_time,original_name
        from bms_content
        <where>
            id not in
            (select bms_content_id from bms_category_content
            where category_id=#{req.categoryId})

            <trim prefix="and" prefixOverrides="and">
                <if test="req.cpId!=null">
                    and cp_id=#{req.cpId}
                </if>
                <if test="req.spId!=null">
                    and sp_id=#{req.spId}
                </if>
                <if test="req.contentType != null">
                    and content_type
                    <if test="req.contentType == 1">
                        in(1,5)
                    </if>
                    <if test="req.contentType == 3">
                        in(3,4)
                    </if>
                </if>
                <choose>
                    <when test="req.names != null">
                        and `name` in
                        <foreach collection="req.names" item="n" open="(" separator="," close=")">
                            #{n}
                        </foreach>
                    </when>
                    <otherwise>
                        <if test="req.name != null">
                            and `name` LIKE CONCAT('%',TRIM(#{req.name}),'%')
                        </if>
                    </otherwise>
                </choose>
                <if test="req.originalName != null and req.originalName != ''">
                    and original_name like CONCAT('%',TRIM(#{req.originalName}),'%')
                </if>
                <if test="req.pgmCategoryId!=null">
                    and pgm_category_id=#{req.pgmCategoryId}
                </if>
                <if test="req.opCheckStatus!=null">
                    and op_check_status=#{req.opCheckStatus}
                </if>
            </trim>
        </where>

    </select>


    <update id="updatePublishStatus">
        update bms_category_content
        <trim prefix="set" suffixOverrides=",">
            <trim prefix="publish_status = case" suffix="end,">
                <foreach collection="entityList" item="item" index="index">
                    <if test="item.publishStatus != null and item.parentCode != null" >
                        when category_code = #{item.parentCode}
                        <if test="item.elementCode != null">
                            and cms_content_code = #{item.elementCode}
                        </if>
                        then #{item.publishStatus}
                    </if>
                </foreach>
            </trim>
            publish_time = CURRENT_TIMESTAMP 
        </trim>
        <where>
            <trim prefix="AND category_code IN">
                <foreach collection="entityList" index="index" item="item" separator="," open="(" close=")">
                    #{item.parentCode}
                </foreach>
            </trim>
            <trim prefix="AND cms_content_code IN">
                <foreach collection="entityList" index="index" item="item" separator="," open="(" close=")">
                    #{item.elementCode}
                </foreach>
            </trim>
            <trim prefix="AND sp_id IN">
                <foreach collection="spIdList" index="index" item="spId" separator="," open="(" close=")">
                    #{spId}
                </foreach>
            </trim>
        </where>
    </update>

    <select id="listValidMediaCodeByDate" resultMap="bmsCategoryContentMap">
        SELECT cms_content_code,content_type
        from (
            SELECT cms_content_code,sp_id,content_type
            FROM bms_category_content
            WHERE sp_id in
            <foreach collection="spIdList" index="index" item="spId" separator="," open="(" close=")">
                #{spId}
            </foreach>
            and publish_status in ('3','5','6','7','10')
            GROUP BY cms_content_code,sp_id
        ) as b
        GROUP BY cms_content_code
        HAVING count(cms_content_code) = #{spNum};
    </select>



    <select id="listSubscriptionMediaCodeBySp" resultMap="bmsCategoryContentMap">
        SELECT    bc.cms_content_code ,
        GROUP_CONCAT(bcc.sp_id) AS  sp_name
        FROM bms_content bc
        LEFT JOIN bms_category_content bcc ON bc.cms_content_code = bcc.cms_content_code AND bc.sp_id = bcc.sp_id
        LEFT JOIN bms_package_content bpc ON bc.cms_content_code = bpc.cms_content_code AND bc.sp_id = bpc.sp_id
        WHERE bc.sp_id IN
        <foreach collection="spIds" index="index" item="spId" separator="," open="(" close=")">
            #{spId}
        </foreach>
         AND bc.content_type IN (3, 4)
        AND bc.publish_status IN
        <foreach collection="publishStatusList" index="index" item="status" separator="," open="(" close=")">
            #{status}
        </foreach>
        AND bcc.publish_status IN
        <foreach collection="publishStatusList" index="index" item="status" separator="," open="(" close=")">
                #{status}
        </foreach>
          AND bpc.publish_status IN
        <foreach collection="publishStatusList" index="index" item="status" separator="," open="(" close=")">
                #{status}
        </foreach>
        <if test="filterCode != null">
            AND bcc.cms_content_code IN
            <foreach collection="filterCode" index="index" item="code" separator="," open="(" close=")">
                #{code}
            </foreach>
        </if>
        GROUP by bcc.cms_content_code

    </select>

    <select id="filtrationListSubscriptionMediaCodeBySp" resultMap="bmsCategoryContentMap">
        SELECT    bc.cms_content_code ,
        GROUP_CONCAT(bcc.sp_id) AS  sp_name
        FROM bms_content bc
        LEFT JOIN bms_category_content bcc ON bc.cms_content_code = bcc.cms_content_code AND bc.sp_id = bcc.sp_id
        LEFT JOIN bms_package_content bpc ON bc.cms_content_code = bpc.cms_content_code AND bc.sp_id = bpc.sp_id
        WHERE bc.sp_id IN
        <foreach collection="spIds" index="index" item="spId" separator="," open="(" close=")">
            #{spId}
        </foreach>
        AND bc.content_type IN (3, 4)
        AND bc.publish_status IN
        <foreach collection="publishStatusList" index="index" item="status" separator="," open="(" close=")">
            #{status}
        </foreach>

        <if test="filterCode != null">
            AND bcc.cms_content_code IN
            <foreach collection="filterCode" index="index" item="code" separator="," open="(" close=")">
                #{code}
            </foreach>
        </if>
        GROUP by bcc.cms_content_code
    </select>
</mapper>