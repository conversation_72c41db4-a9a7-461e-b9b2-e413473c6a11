<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.pukka.iptv.manage.mapper.bms.BmsChannelMapper">

    <resultMap type="com.pukka.iptv.common.data.model.bms.BmsChannel" id="bmsChannelMap">
        <result property="id" column="id"/>
        <result property="code" column="code"/>
        <result property="channelNumber" column="channel_number"/>
        <result property="name" column="name"/>
        <result property="cpId" column="cp_id"/>
        <result property="cpName" column="cp_name"/>
        <result property="createTime" column="create_time"/>
        <result property="updateTime" column="update_time"/>
        <result property="spId" column="sp_id"/>
        <result property="spName" column="sp_name"/>
        <result property="outPassageIds" column="out_passage_ids"/>
        <result property="outPassageNames" column="out_passage_names"/>
        <result property="publishStatus" column="publish_status"/>
        <result property="publishTime" column="publish_time"/>
        <result property="publishDescription" column="publish_description"/>
        <result property="cmsChannelCode" column="cms_channel_code"/>
        <result property="cmsChannelId" column="cms_channel_id"/>
        <result property="contentProvider" column="content_provider"/>
        <result property="categoryIds" column="category_ids"/>
        <result property="categoryNames" column="category_names"/>
        <result property="packageIds" column="package_ids"/>
        <result property="status" column="status"/>
    </resultMap>

    <select id="getChannelsPage" resultMap="bmsChannelMap">
        select DISTINCT a.* from bms_channel a left join bms_category_channel b on a.id =b.bms_channel_id where
        a.sp_id = #{bmsChannel.spId}
        <if test="cpIdSet != null and !cpIdSet.isEmpty()">
            AND a.cp_id IN
            <foreach item="cpId" collection="cpIdSet" open="(" separator="," close=")">
                #{cpId}
            </foreach>
        </if>
        <if test="categoryId != null">
            and b.category_id =#{categoryId}
        </if>
        <choose>
            <when test="names != null">
                and a.`name` in
                <foreach collection="names" item="n" open="(" separator="," close=")">
                    #{n}
                </foreach>
            </when>
            <otherwise>
                <if test="bmsChannel.name != null">
                    and a.`name` LIKE CONCAT('%',#{bmsChannel.name},'%')
                </if>
            </otherwise>
        </choose>
        <if test="bmsChannel.cpId != null">
            and a.cp_id = #{bmsChannel.cpId}
        </if>
        <if test="status !=null">
            and a.status = #{status}
        </if>
        <if test="bmsChannel.publishStatus != null">
            and a.publish_status = #{bmsChannel.publishStatus}
        </if>
        order by a.id desc
    </select>

    <select id="getUnauthorizedChannelPage" parameterType="com.pukka.iptv.common.data.params.SysAuthorizationParam"
            resultType="com.pukka.iptv.common.data.dto.SysAuthorizationChannelDto">

        SELECT
        cc.id,
        cc.name,
        cc.channel_number AS channelNumber,
        cc.code,
        cc.cp_name AS cpName,
        cc.type
        FROM
        cms_channel cc
        LEFT JOIN bms_channel bc ON cc.id = bc.cms_channel_id and bc.sp_id = #{param.spId}
        WHERE cc.cp_id = #{param.cpId} and (bc.id IS NULL or bc.cp_id &lt;&gt; #{param.cpId})
        <if test="param.name != null and param.name != ''">
            AND cc.name LIKE CONCAT('%',#{param.name},'%')
        </if>
        <if test="param.nameList != null and param.nameList.length > 0">
            <trim prefix="AND cc.name in (" suffix=")">
                <foreach collection="param.nameList" item="item" index="index" separator=",">
                    <if test="item !=null">
                        #{item}
                    </if>
                </foreach>
            </trim>
        </if>
        <if test="param.channelNumber != null and param.channelNumber != ''">
            AND cc.channel_number LIKE CONCAT('%',#{param.channelNumber},'%')
        </if>
    </select>

    <select id="getNotBindChannelsPage" resultMap="bmsChannelMap">
        SELECT
        *
        FROM
        bms_channel a
        WHERE
        a.id NOT IN (
        SELECT
        b.bms_channel_id
        FROM
        bms_category_channel b
        WHERE
        b.category_id = #{categoryId}
        ) and a.sp_id =#{spId}
        <if test="channelName!=null">
            and a.`name` LIKE CONCAT('%',#{channelName},'%')
        </if>
        <if test="cpId!=null">
            and a.cp_id =#{cpId}
        </if>
        order by a.id desc
    </select>

    <select id="checkRollback" resultType="java.lang.String">

        SELECT
        a.`name`
        FROM
        ( SELECT * FROM bms_channel  WHERE id IN
        <foreach item='id' index='index' collection='ids' open='(' separator=',' close=')'>
            #{id}
        </foreach>
        ) a
        LEFT JOIN bms_category_channel b ON a.id = b.bms_channel_id
        LEFT JOIN bms_picture c ON c.bms_content_id = a.id
        LEFT JOIN bms_physical_channel d ON a.cms_channel_id = d.cms_channel_id
        WHERE
        c.content_type = 6
        AND a.sp_id = d.sp_id
        AND (
        a.publish_status NOT IN ( 3, 5, 7, 10 )
        OR b.publish_status NOT IN ( 3, 5, 7, 10 )
        OR c.publish_status NOT IN ( 3, 5, 7, 10 )
        OR d.publish_status NOT IN ( 3, 5, 7, 10 ))
        limit 1
    </select>

    <select id="getAuthorizationChannelList" parameterType="com.pukka.iptv.common.data.params.SysAuthorizationParam"
            resultType="com.pukka.iptv.common.data.dto.SysAuthorizationChannelDto">
        SELECT
        bc.id,
        bc.NAME,
        bc.CODE,
        bc.channel_number AS channelNumber,
        bc.out_passage_names AS outPassageNames,
        bc.publish_status AS publishStatus,
        bc.cp_name AS cpName,
        bc.type,
        bc.sp_id AS spId,
        bc.out_passage_ids AS outPassageIds
        FROM
        bms_channel bc
        WHERE bc.cp_id = #{param.cpId}
        <if test="param.spId != null ">
            AND bc.sp_id = #{param.spId}
        </if>
        <if test="param.name != null and param.name != ''">
            AND bc.name LIKE CONCAT('%',#{param.name},'%')
        </if>
        <if test="param.nameList != null and param.nameList.length > 0">
            <trim prefix="AND bc.name in (" suffix=")">
                <foreach collection="param.nameList" item="item" index="index" separator=",">
                    <if test="item !=null">
                        #{item}
                    </if>
                </foreach>
            </trim>
        </if>
        <if test="param.outPassageId != null and param.outPassageId != '' ">
            AND bc.out_passage_ids LIKE CONCAT('%',#{param.outPassageId},'%')
        </if>
        <if test="param.publishStatus != null">
            AND bc.publish_status = #{param.publishStatus}
        </if>
        <if test="spIdSet != null and !spIdSet.isEmpty()">
            AND bc.sp_id IN
            <foreach item="spId" collection="spIdSet" open="(" separator="," close=")">
                #{spId}
            </foreach>
        </if>
    </select>
    <select id="getChannelsTotal" resultType="java.lang.Long">
        select count(DISTINCT a.id) from bms_channel a , bms_category_channel b where a.id =b.bms_channel_id and
        a.sp_id = #{bmsChannel.spId}
        <if test="categoryId != null">
            and b.category_id =#{categoryId}
        </if>
        <choose>
            <when test="names != null">
                and a.`name` in
                <foreach collection="names" item="n" open="(" separator="," close=")">
                    #{n}
                </foreach>
            </when>
            <otherwise>
                <if test="bmsChannel.name != null">
                    and a.`name` LIKE CONCAT('%',#{bmsChannel.name},'%')
                </if>
            </otherwise>
        </choose>
        <if test="bmsChannel.cpId != null">
            and a.cp_id = #{bmsChannel.cpId}
        </if>
        <if test="status !=null">
            and a.status = #{status}
        </if>
        <if test="bmsChannel.publishStatus != null">
            and a.publish_status = #{bmsChannel.publishStatus}
        </if>
        order by a.id desc
    </select>

    <update id="updatePublishStatus">
        update bms_channel
        <trim prefix="set" suffixOverrides=",">
            <trim prefix="publish_status = case" suffix="end,">
                <foreach collection="entityList" item="item" index="index">
                    <if test="item.publishStatus !=null">
                        when cms_channel_code=#{item.code} then #{item.publishStatus}
                    </if>
                </foreach>
            </trim>
            publish_time = CURRENT_TIMESTAMP
        </trim>
        <where>
            <trim prefix="AND cms_channel_code IN">
                <foreach collection="entityList" index="index" item="item" separator="," open="(" close=")">
                    #{item.code}
                </foreach>
            </trim>
            <trim prefix="AND sp_id IN">
                <foreach collection="spIdList" index="index" item="item" separator="," open="(" close=")">
                    #{item}
                </foreach>
            </trim>
        </where>
    </update>
</mapper>