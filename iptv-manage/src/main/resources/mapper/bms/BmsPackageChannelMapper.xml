<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.pukka.iptv.manage.mapper.bms.BmsPackageChannelMapper">

	<!-- 可根据自己的需求，是否要使用 -->
    <resultMap type="com.pukka.iptv.common.data.model.bms.BmsPackageChannel" id="bmsPackageChannelMap">
        <result property="id" column="id"/>
        <result property="bmsChannelId" column="bms_channel_id"/>
        <result property="cmsChannelCode" column="cms_channel_code"/>
        <result property="channelName" column="channel_name"/>
        <result property="packageName" column="package_name"/>
        <result property="packageId" column="package_id"/>
        <result property="packageCode" column="package_code"/>
        <result property="spId" column="sp_id"/>
        <result property="spName" column="sp_name"/>
        <result property="status" column="status"/>
        <result property="createTime" column="create_time"/>
        <result property="updateTime" column="update_time"/>
        <result property="publishStatus" column="publish_status"/>
        <result property="outPassageNames" column="out_passage_names"/>
        <result property="outPassageIds" column="out_passage_ids"/>
        <result property="source" column="source"/>
        <result property="publishTime" column="publish_time"/>
        <result property="publishDescription" column="publish_description"/>
        <result property="creatorName" column="creator_name"/>
        <result property="creatorId" column="creator_id"/>
        <result property="contentType" column="content_type"/>
    </resultMap>


</mapper>