<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">


<!-- 不使用namespace的话sql搜索定位会比较方便 -->
<mapper namespace="com.pukka.iptv.manage.mapper.bms.BmsPictureMapper">

    <!-- 表名 -->
    <sql id="tableName">
        bms_picture
    </sql>

    <sql id="columns">
        `id`,`code`,`file_url`,`description`,`content_type`,`content_code`,`bms_content_id`,`cp_id`,`cp_name`,`create_time`,`update_time`,`type`,`sp_id`,`sp_name`,`status`,`sequence`,`ratio`,`out_passage_ids`,`out_passage_names`,`publish_status`,`publish_time`,`publish_description`,`cms_picture_id`,`cms_picture_code`
    </sql>

    <sql id="where">
        <where>
            <if test="null != id">
                AND `id` = #{id}
            </if>
            <if test="null != code and '' != code">
                AND `code` = #{code}
            </if>
            <if test="null != fileUrl and '' != fileUrl">
                AND `file_url` = #{fileUrl}
            </if>
            <if test="null != description and '' != description">
                AND `description` = #{description}
            </if>
            <if test="null != contentType">
                AND `content_type` = #{contentType}
            </if>
            <if test="null != contentCode and '' != contentCode">
                AND `content_code` = #{contentCode}
            </if>
            <if test="null != bmsContentId">
                AND `bms_content_id` = #{bmsContentId}
            </if>
            <if test="null != cpId">
                AND `cp_id` = #{cpId}
            </if>
            <if test="null != cpName and '' != cpName">
                AND `cp_name` = #{cpName}
            </if>
            <if test="null != createTime and '' != createTime">
                AND `create_time` = #{createTime}
            </if>
            <if test="null != updateTime and '' != updateTime">
                AND `update_time` = #{updateTime}
            </if>
            <if test="null != type">
                AND `type` = #{type}
            </if>
            <if test="null != spId">
                AND `sp_id` = #{spId}
            </if>
            <if test="null != spName and '' != spName">
                AND `sp_name` = #{spName}
            </if>
            <if test="null != status">
                AND `status` = #{status}
            </if>
            <if test="null != sequence">
                AND `sequence` = #{sequence}
            </if>
            <if test="null != ratio and '' != ratio">
                AND `ratio` = #{ratio}
            </if>
            <if test="null != outPassageIds and '' != outPassageIds">
                AND `out_passage_ids` = #{outPassageIds}
            </if>
            <if test="null != outPassageNames and '' != outPassageNames">
                AND `out_passage_names` = #{outPassageNames}
            </if>
            <if test="null != publishStatus">
                AND `publish_status` = #{publishStatus}
            </if>
            <if test="null != publishTime and '' != publishTime">
                AND `publish_time` = #{publishTime}
            </if>
            <if test="null != publishDescription and '' != publishDescription">
                AND `publish_description` = #{publishDescription}
            </if>
            <if test="null != cmsPictureId">
                AND `cms_picture_id` = #{cmsPictureId}
            </if>
            <if test="null != cmsPictureCode and '' != cmsPictureCode">
                AND `cms_picture_code` = #{cmsPictureCode}
            </if>
        </where>
    </sql>
    <update id="updatePublishStatus">
        update bms_picture
        <trim prefix="set" suffixOverrides=",">
            <trim prefix="publish_status = case" suffix="end,">
                <foreach collection="entityList" item="item" index="index">
                    <if test="item.publishStatus != null and item.parentCode != null" >
                        when cms_picture_code = #{item.parentCode}
                        <if test="item.elementCode != null">
                            and content_code = #{item.elementCode}
                        </if>
                        then #{item.publishStatus}
                    </if>
                </foreach>
            </trim>
            publish_time = CURRENT_TIMESTAMP
        </trim>
        <where>
            <trim prefix="AND cms_picture_code IN">
                <foreach collection="entityList" index="index" item="item" separator="," open="(" close=")">
                    #{item.parentCode}
                </foreach>
            </trim>
            <trim prefix="AND content_code IN">
                <foreach collection="entityList" index="index" item="item" separator="," open="(" close=")">
                    #{item.elementCode}
                </foreach>
            </trim>
            <trim prefix="AND sp_id IN">
                <foreach collection="spIdList" index="index" item="item" separator="," open="(" close=")">
                    #{item}
                </foreach>
            </trim>
        </where>
    </update>

    <delete id="deleteByBmsContentIdAndCpIdAndSpId">
        DELETE FROM  bms_picture
        WHERE
        (bms_content_id,cp_id, sp_id) IN
        <foreach collection="bmsChannels" item="item" index="index" separator="," open="(" close=")">
            (#{item.id}, #{item.cpId}, #{item.spId})
        </foreach>
    </delete>

</mapper>

