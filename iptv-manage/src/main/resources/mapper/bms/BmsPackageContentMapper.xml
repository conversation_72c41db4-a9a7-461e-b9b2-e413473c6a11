<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.pukka.iptv.manage.mapper.bms.BmsPackageContentMapper">

	<!-- 可根据自己的需求，是否要使用 -->
    <resultMap type="com.pukka.iptv.common.data.model.bms.BmsPackageContent" id="bmsPackageContentMap">
        <result property="id" column="id"/>
        <result property="bmsContentId" column="bms_content_id"/>
        <result property="cmsContentCode" column="cms_content_code"/>
        <result property="contentType" column="content_type"/>
        <result property="contentName" column="content_name"/>
        <result property="packageName" column="package_name"/>
        <result property="packageId" column="package_id"/>
        <result property="packageCode" column="package_code"/>
        <result property="spId" column="sp_id"/>
        <result property="spName" column="sp_name"/>
        <result property="status" column="status"/>
        <result property="createTime" column="create_time"/>
        <result property="updateTime" column="update_time"/>
        <result property="publishStatus" column="publish_status"/>
        <result property="outPassageNames" column="out_passage_names"/>
        <result property="outPassageIds" column="out_passage_ids"/>
        <result property="source" column="source"/>
        <result property="publishTime" column="publish_time"/>
        <result property="publishDescription" column="publish_description"/>
        <result property="creatorName" column="creator_name"/>
        <result property="creatorId" column="creator_id"/>
        <result property="timedPublish" column="timed_publish"/>
    </resultMap>

    <!-- 已绑定的产品包内容 -->
    <resultMap type="com.pukka.iptv.common.data.vo.bms.BmsPackageBindContentVO" id="bmsPackageBindContentVOMap">
        <!-- bms_package_content 表字段 -->
        <!-- 重复字段使用 bpc 前缀开头 -->
        <result column="id" property="id"/>
        <result column="bms_content_id" property="bmsContentId"/>
        <result column="cms_content_code" property="cmsContentCode"/>
        <result column="content_type" property="contentType"/>
        <result column="content_name" property="contentName"/>
        <result column="bpc_publish_status" property="bpcPublishStatus"/>
        <result column="bpc_out_passage_names" property="bpcOutPassageNames"/>
        <result column="bpc_publish_time" property="bpcPublishTime"/>
        <result column="bpc_publish_description" property="bpcPublishDescription"/>
        <result column="creator_name" property="creatorName"/>
        <result column="create_time" property="createTime"/>
        <result column="package_id" property="packageId"/>
        <result column="package_name" property="packageName"/>
        <result column="package_code" property="packageCode"/>
        <result column="source" property="source"/>
        <result column="bpc_timed_publish" property="bpcTimedPublish"/>
        <result column="bpc_timed_publish_status" property="bpcTimedPublishStatus"/>
        <result column="bpc_timed_publish_description" property="bpcTimedPublishDescription"/>
        <!-- bms_content 表字段 -->
        <result column="pgm_category" property="pgmCategory"/>
        <result column="publish_status" property="publishStatus"/>
        <result column="out_passage_names" property="outPassageNames"/>
        <result column="sp_name" property="spName"/>
        <result column="cp_name" property="cpName"/>
        <result column="original_name" property="originalName"/>
        <result column="content_provider" property="contentProvider"/>
        <result column="display_as_last_chance" property="displayAsLastChance"/>
        <result column="licensing_window_start" property="licensingWindowStart"/>
        <result column="licensing_window_end" property="licensingWindowEnd"/>
    </resultMap>

    <!-- 产品包 查询绑定的内容 -->
    <select id="queryBindContent" resultMap="bmsPackageBindContentVOMap">
        select
            bpc.id,
            bpc.bms_content_id,
            bpc.cms_content_code,
            bpc.content_type,
            bpc.content_name,
            bpc.publish_status as bpc_publish_status,
            bpc.out_passage_ids as bpc_out_passage_ids,
            bpc.out_passage_names as bpc_out_passage_names,
            bpc.publish_time as bpc_publish_time,
            bpc.publish_description as bpc_publish_description,
            bpc.source,
            bpc.creator_name,
            bpc.create_time,
            bpc.package_id,
            bpc.package_name,
            bpc.package_code,
            bpc.timed_publish as bpc_timed_publish,
            bpc.timed_publish_status as bpc_timed_publish_status,
            bpc.timed_publish_description as bpc_timed_publish_description,
            bc.pgm_category, bc.publish_status, bc.out_passage_names, bc.sp_name, bc.cp_name,
            bc.content_provider, bc.display_as_last_chance, bc.licensing_window_start, bc.licensing_window_end,
            bc.original_name
        from
            bms_package_content as bpc left join bms_content as bc
        on
            bpc.bms_content_id= bc.id
        <where>
            <if test="queryReq.packageId != null">
                and bpc.package_id = #{queryReq.packageId}
            </if>
            <if test="queryReq.bmsContentId != null">
                and bpc.bms_content_id = #{queryReq.bmsContentId}
            </if>
            <if test="queryReq.originalName != null and queryReq.originalName != ''">
                and bc.original_name LIKE CONCAT('%',#{queryReq.originalName},'%')
            </if>
            <choose>
                <when test="queryReq.names != null">
                    and bc.`name` in
                    <foreach collection="queryReq.names" item="n" open="(" separator="," close=")">
                        #{n}
                    </foreach>
                </when>
                <otherwise>
                    <if test="queryReq.name != null and queryReq.name != ''">
                        and bc.`name` LIKE CONCAT('%',#{queryReq.name},'%')
                    </if>
                </otherwise>
            </choose>

            <choose>
                <when test="queryReq.packageNames != null">
                    and bpc.package_name in
                    <foreach collection="queryReq.packageNames" item="n" open="(" separator="," close=")">
                        #{n}
                    </foreach>
                </when>
                <otherwise>
                    <if test="queryReq.packageName != null">
                        and bpc.package_name LIKE CONCAT('%',trim(#{queryReq.packageName}),'%')
                    </if>
                </otherwise>
            </choose>

            <if test="queryReq.cpId != null">
                and bc.cp_id = #{queryReq.cpId}
            </if>
            <if test="queryReq.contentType != null">
                and bc.content_type
                <if test="queryReq.contentType == 1">
                    in(1,5)
                </if>
                <if test="queryReq.contentType == 2">
                    in(3,4)
                </if>
            </if>
            <if test="queryReq.publishStatus != null">
                and bpc.publish_status = #{queryReq.publishStatus}
            </if>
            <if test="queryReq.pgmCategoryId != null">
                and bc.pgm_category_id = #{queryReq.pgmCategoryId}
            </if>
        </where>
        order by bpc.id desc
    </select>

    <!-- 产品包 查询 未绑定并发布成功 的内容 -->
    <select id="queryNotBindContent" resultType="com.pukka.iptv.common.data.model.bms.BmsContent">
        select
          bc.`id`, bc.`name`, bc.original_name, bc.content_type, bc.pgm_category, bc.op_check_status, bc.cp_name ,bc.publish_status
        from
          bms_content bc
        left join bms_package_content bpc on bc.id = bpc.bms_content_id and bpc.package_id = #{queryReq.packageId}
        where bpc.id IS NULL and bc.sp_id = #{queryReq.spId}
        <if test="queryReq.publishStatus != null">
            and bc.publish_status = #{queryReq.publishStatus}
        </if>
        <if test="queryReq.originalName != null and queryReq.originalName != ''">
            and bc.original_name LIKE CONCAT('%',#{queryReq.originalName},'%')
        </if>
        <choose>
            <when test="queryReq.names != null">
                and bc.`name` in
                <foreach collection="queryReq.names" item="n" open="(" separator="," close=")">
                    #{n}
                </foreach>
            </when>
            <otherwise>
                <if test="queryReq.name != null and queryReq.name != ''">
                    and bc.`name` LIKE CONCAT('%',#{queryReq.name},'%')
                </if>
            </otherwise>
        </choose>
        <if test="queryReq.cpId != null">
            and bc.cp_id = #{queryReq.cpId}
        </if>
        <if test="queryReq.contentType != null">
            and bc.content_type
            <if test="queryReq.contentType == 1">
                in(1,5)
            </if>
            <if test="queryReq.contentType == 2">
                in(3,4)
            </if>
        </if>
        <if test="queryReq.opCheckStatus != null">
            and bc.op_check_status = #{queryReq.opCheckStatus}
        </if>
        <if test="queryReq.pgmCategoryId != null">
            and bc.pgm_category_id = #{queryReq.pgmCategoryId}
        </if>
        order by bc.id desc
    </select>

    <update id="updatePublishStatus">
        update bms_package_content
        <trim prefix="set" suffixOverrides=",">
            <trim prefix="publish_status = case" suffix="end,">
                <foreach collection="entityList" item="item" index="index">
                    <if test="item.publishStatus != null and item.parentCode != null" >
                        when package_code = #{item.parentCode}
                        <if test="item.elementCode != null">
                            and cms_content_code = #{item.elementCode}
                        </if>
                        then #{item.publishStatus}
                    </if>
                </foreach>
            </trim>
            publish_time = CURRENT_TIMESTAMP
        </trim>
        <where>
            <trim prefix="AND package_code IN">
                <foreach collection="entityList" index="index" item="item" separator="," open="(" close=")">
                    #{item.parentCode}
                </foreach>
            </trim>
            <trim prefix="AND cms_content_code IN">
                <foreach collection="entityList" index="index" item="item" separator="," open="(" close=")">
                    #{item.elementCode}
                </foreach>
            </trim>
            <trim prefix="AND sp_id IN">
                <foreach collection="spIdList" index="index" item="spId" separator="," open="(" close=")">
                    #{spId}
                </foreach>
            </trim>
        </where>
    </update>

    <select id="listValidMediaCodeByDate" resultMap="bmsPackageContentMap" >
        SELECT cms_content_code,content_type
        from (
            SELECT cms_content_code,sp_id,content_type
            FROM bms_package_content
            WHERE sp_id in
            <foreach collection="spIdList" index="index" item="spId" separator="," open="(" close=")">
                #{spId}
            </foreach>
            and publish_status in ('3','5','6','7','10')
            GROUP BY cms_content_code,sp_id
        ) as b
        GROUP BY cms_content_code
        HAVING count(cms_content_code) = #{spNum};
    </select>

</mapper>