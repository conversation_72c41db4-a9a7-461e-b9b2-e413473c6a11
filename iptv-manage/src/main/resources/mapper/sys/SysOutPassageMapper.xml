<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//ibatis.apache.org//DTD Mapper 3.0//EN" "http://ibatis.apache.org/dtd/ibatis-3-mapper.dtd">


<!-- 不使用namespace的话sql搜索定位会比较方便 -->
<mapper namespace="com.pukka.iptv.manage.mapper.sys.SysOutPassageMapper">

	<!-- 表名 -->
	<sql id="tableName">
        sys_out_passage
    </sql>

    <sql id="columns">
        `id`,`name`,`code`,`creator_name`,`creator_id`,`path`,`deal_count`,`priority`,`create_time`,`update_time`,`lsp_id`,`deal_report_result`
    </sql>

    <sql id="where">
        <where>
	        <if test="null != id">
            AND `id` = #{id}
            </if>
	        <if test="null != name and '' != name">
            AND `name` = #{name}
            </if>
	        <if test="null != code and '' != code">
            AND `code` = #{code}
            </if>
	        <if test="null != creatorName and '' != creatorName">
            AND `creator_name` = #{creatorName}
            </if>
	        <if test="null != creatorId">
            AND `creator_id` = #{creatorId}
            </if>
	        <if test="null != path and '' != path">
            AND `path` = #{path}
            </if>
	        <if test="null != dealCount">
            AND `deal_count` = #{dealCount}
            </if>
	        <if test="null != priority">
            AND `priority` = #{priority}
            </if>
	        <if test="null != createTime and '' != createTime">
            AND `create_time` = #{createTime}
            </if>
	        <if test="null != updateTime and '' != updateTime">
            AND `update_time` = #{updateTime}
            </if>
	        <if test="null != lspId and '' != lspId">
            AND `lsp_id` = #{lspId}
            </if>
	        <if test="null != dealReportResult">
            AND `deal_report_result` = #{dealReportResult}
            </if>
        </where>
    </sql>

</mapper>

