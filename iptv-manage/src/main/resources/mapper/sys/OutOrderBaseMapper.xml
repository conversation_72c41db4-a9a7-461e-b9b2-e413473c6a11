<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">


<!-- 不使用namespace的话sql搜索定位会比较方便 -->
<mapper namespace="com.pukka.iptv.manage.mapper.sys.OutOrderBaseMapper">

    <!--插入主工单 -->
    <insert id="insertBase" useGeneratedKeys="true" keyProperty="id">
        insert into out_order_base(out_passage_ids, sp_id, sp_name, bms_content_id,
                                   content_type, status, action, show_name, creator_id,
                                   creator_name, bms_sp_channel_id, bms_sp_channel_name,order_type,order_correlate_id)
        values (#{param.outPassageIds}, #{param.spId}, #{param.spName}, #{param.bmsContentId},
                #{param.contentType}, #{param.status}, #{param.action}, #{param.showName}, #{param.creatorId},
                #{param.creatorName}, #{param.bmsSpChannelId}, #{param.bmsSpChannelName},#{param.orderType},#{param.orderCorrelateId})
    </insert>
    <!--批量插入主工单 -->
    <insert id="insertBatchBase" useGeneratedKeys="true" keyProperty="id">
        insert into out_order_base(out_passage_ids,sp_id,sp_name,bms_content_id,
        content_type,status,action,show_name,creator_id,
        creator_name,bms_sp_channel_id,bms_sp_channel_name,order_type,order_correlate_id)
        values
        <foreach collection="params" item="param" index="index" separator=",">
            (#{param.outPassageIds},#{param.spId},#{param.spName},#{param.bmsContentId},
            #{param.contentType},#{param.status},#{param.action},#{param.showName},#{param.creatorId},
            #{param.creatorName},#{param.bmsSpChannelId},#{param.bmsSpChannelName},#{param.orderType},#{param.orderCorrelateId})
        </foreach>
    </insert>
    <select id="findListByPage" resultType="com.pukka.iptv.common.data.vo.sys.OutOrderItemVo">
        SELECT *
        FROM out_order_item${code}
        WHERE base_order_id = #{baseOrderId}
        <if test="null != status and status != ''">
        and status =#{status}
        </if>
        <if test="null != action and action != ''">
            and action = #{action}
        </if>
        ORDER BY create_time DESC
        LIMIT #{page}, #{size}
    </select>
    <select id="findList" resultType="long">
        SELECT COUNT(1)
        FROM out_order_item${code}
        WHERE base_order_id = #{baseOrderId}
        <if test="null != status and status != ''">
            and status =#{status}
        </if>
        <if test="null != action and action != ''">
            and action = #{action}
        </if>
        ORDER BY create_time DESC
    </select>
    <select id="getPage" resultType="com.pukka.iptv.common.data.model.OutOrderBase">
        SELECT *
        FROM out_order_base
        <trim prefix="where" prefixOverrides="and|or">
            <if test="outOrderBaseVo.nameList != null and outOrderBaseVo.nameList.length > 0">
                <if test="outOrderBaseVo.likeOrinFlag == 1">
                    MATCH(show_name) AGAINST(#{outOrderBaseVo.nameLike} IN NATURAL LANGUAGE MODE)
                </if>
                <if test="outOrderBaseVo.likeOrinFlag == 2">
                    <trim prefix="show_name in (" suffix=")">
                        <foreach collection="outOrderBaseVo.nameList" item="item" index="index" separator=",">
                            <if test="item !=null">
                                #{item}
                            </if>
                        </foreach>
                    </trim>
                </if>

            </if>
            <if test="outOrderBaseVo.action != null">
                and action = #{outOrderBaseVo.action}
            </if>
            <if test="outOrderBaseVo.result != null">
                and result = #{outOrderBaseVo.result}
            </if>
            <if test="outOrderBaseVo.contentType != null">
                <choose>
                    <when test="outOrderBaseVo.contentType == '30'.toString()">
                        <!--枚举类型为关系时-->
                        and (content_type in (18,19,20,21,22))
                    </when>
                    <otherwise>
                        and content_type = #{outOrderBaseVo.contentType}
                    </otherwise>
                </choose>
            </if>
            <if test="outOrderBaseVo.creatorName != null and outOrderBaseVo.creatorName.trim() != ''">
                and `creator_name` = #{outOrderBaseVo.creatorName}
            </if>
            <if test="startTime != null and startTime.trim() != ''">
                <![CDATA[   and create_time >=  #{startTime} ]]>
            </if>
            <if test="endTime != null and endTime.trim() != ''">
                <![CDATA[  and create_time <= #{endTime} ]]>
            </if>
            <if test="outOrderBaseVo.status != null">
                and status = #{outOrderBaseVo.status}
            </if>
            <if test="outOrderBaseVo.bmsSpChannelId != null">
                and bms_sp_channel_id = #{outOrderBaseVo.bmsSpChannelId}
            </if>
            <if test="outOrderBaseVo.spId != null">
                and sp_id = #{outOrderBaseVo.spId}
            </if>
            <if test="outOrderBaseVo.cpId != null">
                and cp_id = #{outOrderBaseVo.cpId}
            </if>
            <if test="outOrderBaseVo.orderCorrelateId != null and outOrderBaseVo.orderCorrelateId.trim() != ''">
                and order_correlate_id = #{outOrderBaseVo.orderCorrelateId}
            </if>
            <if test="outOrderBaseVo.orderType != null">
                and order_type = #{outOrderBaseVo.orderType}
            </if>
        </trim>
        ORDER BY create_time DESC
        LIMIT #{page}, #{size}
    </select>
    <select id="getPageSize" resultType="long">
        SELECT COUNT(*)
        FROM out_order_base
        <trim prefix="where" prefixOverrides="and|or">
            <if test="outOrderBaseVo.nameList != null and outOrderBaseVo.nameList.length > 0">
                <if test="outOrderBaseVo.likeOrinFlag == 1">
                    MATCH(show_name) AGAINST(#{outOrderBaseVo.nameLike} IN NATURAL LANGUAGE MODE)
                </if>
                <if test="outOrderBaseVo.likeOrinFlag == 2">
                    <trim prefix="show_name in (" suffix=")">
                        <foreach collection="outOrderBaseVo.nameList" item="item" index="index" separator=",">
                            <if test="item !=null">
                                #{item}
                            </if>
                        </foreach>
                    </trim>
                </if>

            </if>
            <if test="outOrderBaseVo.action != null">
                and action = #{outOrderBaseVo.action}
            </if>
            <if test="outOrderBaseVo.result != null">
                and result = #{outOrderBaseVo.result}
            </if>
            <if test="outOrderBaseVo.contentType != null">
                <choose>
                    <when test="outOrderBaseVo.contentType == '30'.toString()">
                        <!--枚举类型为关系时-->
                        and (content_type in (18,19,20,21,22))
                    </when>
                    <otherwise>
                        and content_type = #{outOrderBaseVo.contentType}
                    </otherwise>
                </choose>
            </if>
            <if test="outOrderBaseVo.creatorName != null and outOrderBaseVo.creatorName.trim() != ''">
                and `creator_name` = #{outOrderBaseVo.creatorName}
            </if>
            <if test="startTime != null and startTime.trim() != ''">
                <![CDATA[   and create_time >=  #{startTime} ]]>
            </if>
            <if test="endTime != null and endTime.trim() != ''">
                <![CDATA[  and create_time <= #{endTime} ]]>
            </if>
            <if test="outOrderBaseVo.status != null">
                and status = #{outOrderBaseVo.status}
            </if>
            <if test="outOrderBaseVo.bmsSpChannelId != null">
                and bms_sp_channel_id = #{outOrderBaseVo.bmsSpChannelId}
            </if>
            <if test="outOrderBaseVo.spId != null">
                and sp_id = #{outOrderBaseVo.spId}
            </if>
            <if test="outOrderBaseVo.cpId != null">
                and cp_id = #{outOrderBaseVo.cpId}
            </if>
            <if test="outOrderBaseVo.orderCorrelateId != null and outOrderBaseVo.orderCorrelateId.trim() != ''">
                and order_correlate_id = #{outOrderBaseVo.orderCorrelateId}
            </if>
            <if test="outOrderBaseVo.orderType != null">
                and order_type = #{outOrderBaseVo.orderType}
            </if>
        </trim>
    </select>
    <select id="selectLatestForEachBmsContentId" resultType="com.pukka.iptv.common.data.model.OutOrderBase">
        SELECT o.*
        FROM out_order_base o
        INNER JOIN (
        SELECT bms_content_id, MAX(create_time) AS max_create_time
        FROM out_order_base
        WHERE bms_content_id IN
        <foreach item="item" index="index" collection="idList" open="(" separator="," close=")">
            #{item}
        </foreach>
        AND content_type = #{contentType}
        AND action = #{action}
        AND result IN
        <foreach item="result" index="index" collection="results" open="(" separator="," close=")">
            #{result}
        </foreach>
        GROUP BY bms_content_id
        ) max_times
        ON o.bms_content_id = max_times.bms_content_id AND o.create_time = max_times.max_create_time
        WHERE o.content_type = #{contentType}
        AND o.action = #{action}
        AND o.result IN
        <foreach item="result" index="index" collection="results" open="(" separator="," close=")">
            #{result}
        </foreach>
    </select>

</mapper>

