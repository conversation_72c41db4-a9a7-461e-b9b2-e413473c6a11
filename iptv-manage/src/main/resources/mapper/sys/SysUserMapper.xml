<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//ibatis.apache.org//DTD Mapper 3.0//EN" "http://ibatis.apache.org/dtd/ibatis-3-mapper.dtd">


<!-- 不使用namespace的话sql搜索定位会比较方便 -->
<mapper namespace="com.pukka.iptv.manage.mapper.sys.SysUserMapper">

	<!-- 表名 -->
	<sql id="tableName">
        sys_user
    </sql>
    
    <sql id="columns">
        `id`,`name`,`username`,`password`,`gender`,`avatar`,`birthday`,`phone`,`email`,`type`,`status`,`creator_id`,`creator_name`,`create_time`,`update_time`,`description`
    </sql>

    <sql id="where">
        <where>
	        <if test="null != id">
            AND `id` = #{id}
            </if>
	        <if test="null != name and '' != name">
            AND `name` = #{name}
            </if>
	        <if test="null != username and '' != username">
            AND `username` = #{username}
            </if>
	        <if test="null != password and '' != password">
            AND `password` = #{password}
            </if>
	        <if test="null != gender">
            AND `gender` = #{gender}
            </if>
	        <if test="null != avatar and '' != avatar">
            AND `avatar` = #{avatar}
            </if>
	        <if test="null != birthday and '' != birthday">
            AND `birthday` = #{birthday}
            </if>
	        <if test="null != phone and '' != phone">
            AND `phone` = #{phone}
            </if>
	        <if test="null != email and '' != email">
            AND `email` = #{email}
            </if>
	        <if test="null != type">
            AND `type` = #{type}
            </if>
	        <if test="null != status">
            AND `status` = #{status}
            </if>
	        <if test="null != creatorId">
            AND `creator_id` = #{creatorId}
            </if>
	        <if test="null != creatorName and '' != creatorName">
            AND `creator_name` = #{creatorName}
            </if>
	        <if test="null != createTime and '' != createTime">
            AND `create_time` = #{createTime}
            </if>
	        <if test="null != updateTime and '' != updateTime">
            AND `update_time` = #{updateTime}
            </if>
	        <if test="null != description and '' != description">
            AND `description` = #{description}
            </if>
        </where>
    </sql>


    <select id="listUserRoleTenant" parameterType="java.util.List" resultType="com.pukka.iptv.common.data.model.sys.SysUser">
        SELECT u.`id`,u.`name`,u.`username`,u.`password`,u.`gender`,u.`avatar`,u.`birthday`,u.`phone`,u.`email`,u.`type`,
        u.`status`,u.`creator_id`,u.`creator_name`,u.`create_time`,u.`update_time`,u.`description`,
        GROUP_CONCAT(DISTINCT ur.role_id ORDER BY ur.role_id) as role_ids,GROUP_CONCAT(DISTINCT r.`name` ORDER BY ur.role_id) as role_names,
        GROUP_CONCAT(DISTINCT ut.tenant_id ORDER BY ut.tenant_id) as tenant_ids ,GROUP_CONCAT(DISTINCT t.`name` ORDER BY ut.tenant_id) as tenant_names
        FROM sys_user u LEFT JOIN sys_user_role ur
        ON u.id = ur.user_id
        LEFT JOIN sys_role r
        on r.id = ur.role_id
        LEFT JOIN sys_user_tenant ut
        on u.id = ut.user_id
        LEFT JOIN sys_tenant t
        ON ut.tenant_id = t.id
        <where>
            <trim prefix="u.id in("  suffix=")" suffixOverrides=",">
                <foreach collection="userIds" item="item" index="index">
                    <if test="item !=null">
                        #{item},
                    </if>
                </foreach>
            </trim>
        </where>
        GROUP BY u.id
        ORDER BY u.id DESC
    </select>
</mapper>

