<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//ibatis.apache.org//DTD Mapper 3.0//EN" "http://ibatis.apache.org/dtd/ibatis-3-mapper.dtd">


<!-- 不使用namespace的话sql搜索定位会比较方便 -->
<mapper namespace="com.pukka.iptv.manage.mapper.sys.SysMenuMapper">

	<!-- 表名 -->
	<sql id="tableName">
        sys_menu
    </sql>
    
    <sql id="columns">
        `id`,`meta`,`redirect`,`component`,`name`,`parent_id`,`group`,`permission`,`path`,`icon`,`sequence`,`type`,`status`,`tenant_id`,`tenant_name`,`creator_id`,`creator_name`,`create_time`,`update_time`,`description`
    </sql>
    
    <sql id="where">
        <where>
	        <if test="null != id">
            AND `id` = #{id}
            </if>
	        <if test="null != meta and '' != meta">
            AND `meta` = #{meta}
            </if>
	        <if test="null != redirect and '' != redirect">
            AND `redirect` = #{redirect}
            </if>
	        <if test="null != component and '' != component">
            AND `component` = #{component}
            </if>
	        <if test="null != name and '' != name">
            AND `name` = #{name}
            </if>
	        <if test="null != parentId">
            AND `parent_id` = #{parentId}
            </if>
	        <if test="null != group and '' != group">
            AND `group` = #{group}
            </if>
	        <if test="null != permission and '' != permission">
            AND `permission` = #{permission}
            </if>
	        <if test="null != path and '' != path">
            AND `path` = #{path}
            </if>
	        <if test="null != icon and '' != icon">
            AND `icon` = #{icon}
            </if>
	        <if test="null != sequence">
            AND `sequence` = #{sequence}
            </if>
	        <if test="null != type">
            AND `type` = #{type}
            </if>
	        <if test="null != status">
            AND `status` = #{status}
            </if>
	        <if test="null != tenantId">
            AND `tenant_id` = #{tenantId}
            </if>
	        <if test="null != tenantName and '' != tenantName">
            AND `tenant_name` = #{tenantName}
            </if>
	        <if test="null != creatorId">
            AND `creator_id` = #{creatorId}
            </if>
	        <if test="null != creatorName and '' != creatorName">
            AND `creator_name` = #{creatorName}
            </if>
	        <if test="null != createTime and '' != createTime">
            AND `create_time` = #{createTime}
            </if>
	        <if test="null != updateTime and '' != updateTime">
            AND `update_time` = #{updateTime}
            </if>
	        <if test="null != description and '' != description">
            AND `description` = #{description}
            </if>
        </where>
    </sql>
	
	<select id="listByRoleIds" resultType="com.pukka.iptv.common.data.model.sys.SysMenu">
        <!--SELECT <include refid="columns"></include>-->
        SELECT
            DISTINCT
               sm.id,
               sm.name,
               sm.parent_id,
               sm.group,
               sm.permission,
               sm.path,
               sm.icon,
               sm.sequence,
               sm.type,
               sm.status,
               sm.component,
               sm.redirect,
               sm.meta,
               sm.creator_id,
               sm.creator_name,
               sm.create_time,
               sm.update_time,
               sm.description
        FROM sys_menu sm
        <if test="null != roleIds and roleIds.size() > 0">
            INNER JOIN sys_role_menu srm
            ON sm.id = srm.menu_id
        </if>
        <where>
            sm.status = 1
            <if test="null != roleIds and roleIds.size() > 0">
                AND srm.role_id IN
                <foreach item="roleId" index="index" collection="roleIds" open="(" separator="," close=")">
                    #{roleId}
                </foreach>
            </if>
        </where>
           <!-- <foreach collection="roles" item="role" open="(" close=")" separator="," >
                #{role.id}
            </foreach>-->
        ORDER BY sm.sequence asc
    </select>

	<select id="listAll" resultType="com.pukka.iptv.common.data.model.sys.SysMenu">
        <!--SELECT <include refid="columns"></include>-->
        SELECT
               sm.id,
               sm.name,
               sm.parent_id,
               sm.group,
               sm.permission,
               sm.path,
               sm.icon,
               sm.sequence,
               sm.type,
               sm.status,
               sm.component,
               sm.redirect,
               sm.meta,
               sm.creator_id,
               sm.creator_name,
               sm.create_time,
               sm.update_time,
               sm.description,
               srm.id AS roleMenuId,
               CASE WHEN srm.id is not null THEN 1 else 0 END AS checked
        FROM sys_menu sm
            LEFT JOIN sys_role_menu srm
            ON sm.id = srm.menu_id
            AND srm.role_id = #{roleId}
        WHERE sm.status = 1
        ORDER BY sm.sequence asc
    </select>

</mapper>

