<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">


<!-- 不使用namespace的话sql搜索定位会比较方便 -->
<mapper namespace="com.pukka.iptv.manage.mapper.sys.OutResultMapper">

    <select id="findByCorrelateId" resultType="com.pukka.iptv.common.data.model.OutResult">
        select *
        from out_result
        where correlate_id = #{correlateId}
    </select>

</mapper>

