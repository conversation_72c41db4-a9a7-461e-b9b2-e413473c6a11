<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.pukka.iptv.manage.mapper.api.VirtualSeriesMapper">

    <resultMap type="com.pukka.iptv.common.data.model.virtual.VirtualSubset" id="VirtualSubsetMap">
        <result property="code" column="code"/>
        <result property="index" column="episode_index"/>
        <result property="name" column="name"/>
        <result property="seriesCode" column="series_code"/>
        <result property="duration" column="duration"/>
    </resultMap>

    <select id="getVirtualSeriesByPage" resultType="com.pukka.iptv.common.data.vo.virtual.VirtualSeriesVo">
        SELECT code,content_type,type,show_name as name,vsp_name as vspname,episode_num as
        episodenum,price,create_time from virtual_series v
        <trim prefix="where" prefixOverrides="and|or">
            <if test="req.code != null and req.code != ''">
                v.code =#{req.code}
            </if>
            <if test="req.name != null and req.name != ''">
                and v.show_name LIKE
                replace(replace(replace(CONCAT('%',trim(#{req.name}),'%'),char(13),''),char(10),''),char(9),'')
            </if>
        </trim>
        <if test="req.orderflag != null">
            <choose>
                <when test="(req.code != null and req.code != '') or (req.name != null and req.name != '') or req.orderflag == '1'.toString()">
                    order by v.create_time desc, v.vsp_name asc
                </when>
                <otherwise>
                    <!--order by convert(v.show_name USING gbk) COLLATE gbk_chinese_ci asc-->
                    order by v.vsp_name asc ,v.show_name asc
                </otherwise>
            </choose>
        </if>
        LIMIT #{page}, #{size}
    </select>

    <select id="getVirtualSeriesSize" resultType="integer">
        SELECT count(*) from virtual_series v
        <trim prefix="where" prefixOverrides="and|or">
            <if test="req.code != null and req.code != ''">
                v.code =#{req.code}
            </if>
            <if test="req.name != null and req.name != ''">
                and v.show_name LIKE
                replace(replace(replace(CONCAT('%',trim(#{req.name}),'%'),char(13),''),char(10),''),char(9),'')
            </if>
        </trim>
    </select>

    <select id="getVirtualSubsetByCode" resultMap="VirtualSubsetMap">
        SELECT
        v.code code,
        v.episode_index episode_index,
        v.show_name name,
        v.series_code series_code,
        v.duration duration
        FROM
        virtual_program v
        <where>
            v.content_type = 2 and v.series_code in
            <foreach item="item" index="index" collection="ids"
                     open="(" separator="," close=")">#{item}
            </foreach>
        </where>
        order by v.episode_index asc
    </select>

    <select id="getSeriesInfo" resultType="com.pukka.iptv.common.data.model.virtual.VirtualSeries">
        SELECT DISTINCT
        t.cms_content_code code,
        '3' content_type,
        '剧集' type,
        t.name name,
        t.content_provider vspname,
        s.volumn_count episodenum,
        CASE

        WHEN s.price > 0.00 THEN
        '1' ELSE '0'
        END price,
        t.create_time createTime
        FROM
        bms_content t
        LEFT JOIN cms_series s ON t.cms_content_code = s.code
        WHERE
        t.id = #{bmsContentId}
        AND t.sp_id in
        <foreach item="item" index="index" collection="ids"
                 open="(" separator="," close=")">#{item}
        </foreach>
        AND ( t.content_type = 3 OR t.content_type = 4 OR t.content_type = 5 )
    </select>
</mapper>