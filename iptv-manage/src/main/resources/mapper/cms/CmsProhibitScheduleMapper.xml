<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.pukka.iptv.manage.mapper.cms.CmsProhibitScheduleMapper">

    <resultMap id="BaseResultMap" type="com.pukka.iptv.common.data.model.cms.CmsProhibitSchedule">
        <id property="id" column="id" jdbcType="BIGINT"/>
        <result property="code" column="code" jdbcType="VARCHAR"/>
        <result property="ruleProhibitCode" column="rule_prohibit_code" jdbcType="VARCHAR"/>
        <result property="channelCode" column="channel_code" jdbcType="VARCHAR"/>
        <result property="channelName" column="channel_name" jdbcType="VARCHAR"/>
        <result property="channelId" column="channel_id" jdbcType="BIGINT"/>
        <result property="programName" column="program_name" jdbcType="VARCHAR"/>
        <result property="startDate" column="start_date" jdbcType="VARCHAR"/>
        <result property="startTime" column="start_time" jdbcType="VARCHAR"/>
        <result property="endTime" column="end_time" jdbcType="VARCHAR"/>
        <result property="duration" column="duration" jdbcType="VARCHAR"/>
        <result property="storageDuration" column="storage_duration" jdbcType="INTEGER"/>
        <result property="status" column="status" jdbcType="TINYINT"/>
        <result property="description" column="description" jdbcType="VARCHAR"/>
        <result property="source" column="source" jdbcType="TINYINT"/>
        <result property="genre" column="genre" jdbcType="VARCHAR"/>
        <result property="cpId" column="cp_id" jdbcType="BIGINT"/>
        <result property="cpName" column="cp_name" jdbcType="VARCHAR"/>
        <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
        <result property="updateTime" column="update_time" jdbcType="TIMESTAMP"/>
        <result property="creatorName" column="creator_name" jdbcType="VARCHAR"/>
        <result property="creatorId" column="creator_id" jdbcType="BIGINT"/>
    </resultMap>

    <sql id="Base_Column_List">
        id
        ,code,rule_prohibit_code,
        channel_code,channel_name,channel_id,
        program_name,start_date,start_time,
        end_time,duration,storage_duration,
        status,description,source,
        genre,cp_id,cp_name,
        create_time,update_time,creator_name,
        creator_id
    </sql>

    <select id="selectList" resultMap="BaseResultMap">
        select
        *
        from
        cms_prohibit_schedule
        where
        1=1
        <if test="vo.programName != null and vo.programName != ''">
            and program_name like CONCAT('%', #{vo.programName}, '%')
        </if>
        <if test="vo.programNameList != null">
            and program_name in
            <foreach collection="vo.programNameList" separator="," open="(" close=")" item="item">
                #{item}
            </foreach>
        </if>
        <if test="vo.startDateTime != null and vo.startDateTime != ''">
            and create_time >= #{vo.startDateTime}
        </if>
        <if test="vo.endDateTime != null and vo.endDateTime != ''">
            and create_time &lt;= #{vo.endDateTime}
        </if>
        <if test="vo.creatorName != null">
            and creator_name = #{vo.creatorName}
        </if>
        <if test="vo.channelName != null and vo.channelName != ''">
            and channel_name = #{vo.channelName}
        </if>
        <if test="vo.cpId != null and vo.cpId != ''">
            and cp_id = #{vo.cpId}
        </if>
        <if test="vo.ruleProhibitCode != null and vo.ruleProhibitCode != ''">
            and rule_prohibit_code = #{vo.ruleProhibitCode}
        </if>
        order by id desc
    </select>

    <delete id="delete">
        delete from
            cms_prohibit_schedule
        where
            id in
            <foreach collection="idList" separator="," open="(" close=")" item="id">
                #{id}
            </foreach>
    </delete>

    <delete id="deleteByChannelCodeAndStartTime">
        delete from
            cms_prohibit_schedule
        WHERE
            channel_code = #{channelCode}
            and
            (start_date,start_time) IN
        <foreach collection="list" item="item" open="(" close=")" separator=",">
            (#{item.startDate},#{item.startTime})
        </foreach>
    </delete>

    <insert id="batchInsert">
        insert into cms_prohibit_schedule(code,rule_prohibit_code,
                                          channel_code,channel_name,channel_id,
                                          program_name,start_date,start_time,
                                          end_time,duration,storage_duration,
                                          status,description,source,
                                          genre,cp_id,cp_name,
                                          create_time,update_time,creator_name,
                                          creator_id)
        values
        <foreach collection="list" separator="," item="i">
            (#{i.code},#{i.ruleProhibitCode},#{i.channelCode},#{i.channelName},#{i.channelId},#{i.programName},
            #{i.startDate},#{i.startTime},#{i.endTime},#{i.duration},#{i.storageDuration},#{i.status},#{i.description},
            #{i.source},#{i.genre},#{i.cpId},#{i.cpName},now(),now(),#{i.creatorName},#{i.creatorId})
        </foreach>
    </insert>
</mapper>
