<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//ibatis.apache.org//DTD Mapper 3.0//EN" "http://ibatis.apache.org/dtd/ibatis-3-mapper.dtd">


<!-- 不使用namespace的话sql搜索定位会比较方便 -->
<mapper namespace="com.pukka.iptv.manage.mapper.cms.CmsPlayerPictureMapper">

	<!-- 表名 -->
	<sql id="tableName">
        cms_player_picture
    </sql>
    
    <sql id="columns">
        `id`,`movie_id`,`movie_type`,`picture_url`,`description`,`create_time`,`creator_name`,`creator_id`,`movie_code`,`cp_id`,`cp_name`,`picture_storage_id`,`picture_timestamp`,`update_time`,`paly_url`,`picture_code`
    </sql>
    
    <sql id="where">
        <where>
	        <if test="null != id">
            AND `id` = #{id}
            </if>
	        <if test="null != movieId">
            AND `movie_id` = #{movieId}
            </if>
	        <if test="null != movieType">
            AND `movie_type` = #{movieType}
            </if>
	        <if test="null != pictureUrl and '' != pictureUrl">
            AND `picture_url` = #{pictureUrl}
            </if>
	        <if test="null != description and '' != description">
            AND `description` = #{description}
            </if>
	        <if test="null != createTime and '' != createTime">
            AND `create_time` = #{createTime}
            </if>
	        <if test="null != creatorName and '' != creatorName">
            AND `creator_name` = #{creatorName}
            </if>
	        <if test="null != creatorId">
            AND `creator_id` = #{creatorId}
            </if>
	        <if test="null != movieCode and '' != movieCode">
            AND `movie_code` = #{movieCode}
            </if>
	        <if test="null != cpId">
            AND `cp_id` = #{cpId}
            </if>
	        <if test="null != cpName and '' != cpName">
            AND `cp_name` = #{cpName}
            </if>
	        <if test="null != pictureStorageId">
            AND `picture_storage_id` = #{pictureStorageId}
            </if>
	        <if test="null != pictureTimestamp and '' != pictureTimestamp">
            AND `picture_timestamp` = #{pictureTimestamp}
            </if>
	        <if test="null != updateTime and '' != updateTime">
            AND `update_time` = #{updateTime}
            </if>
	        <if test="null != palyUrl and '' != palyUrl">
            AND `paly_url` = #{palyUrl}
            </if>
	        <if test="null != pictureCode and '' != pictureCode">
            AND `picture_code` = #{pictureCode}
            </if>
        </where>
    </sql>

</mapper>

