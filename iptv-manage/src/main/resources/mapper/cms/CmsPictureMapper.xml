<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//ibatis.apache.org//DTD Mapper 3.0//EN" "http://ibatis.apache.org/dtd/ibatis-3-mapper.dtd">


<!-- 不使用namespace的话sql搜索定位会比较方便 -->
<mapper namespace="com.pukka.iptv.manage.mapper.cms.CmsPictureMapper">

	<!-- 表名 -->
	<sql id="tableName">
        cms_picture
    </sql>
    
    <sql id="columns">
        `id`,`code`,`file_url`,`description`,`content_type`,`content_code`,`content_id`,`cp_id`,`cp_name`,`create_time`,`update_time`,`type`,`status`,`ratio`,`sequence`,`creator_name`,`creator_id`
    </sql>
    
    <sql id="where">
        <where>
	        <if test="null != id">
            AND `id` = #{id}
            </if>
	        <if test="null != code and '' != code">
            AND `code` = #{code}
            </if>
	        <if test="null != fileUrl and '' != fileUrl">
            AND `file_url` = #{fileUrl}
            </if>
	        <if test="null != description and '' != description">
            AND `description` = #{description}
            </if>
	        <if test="null != contentType">
            AND `content_type` = #{contentType}
            </if>
	        <if test="null != contentCode and '' != contentCode">
            AND `content_code` = #{contentCode}
            </if>
	        <if test="null != contentId">
            AND `content_id` = #{contentId}
            </if>
	        <if test="null != cpId">
            AND `cp_id` = #{cpId}
            </if>
	        <if test="null != cpName and '' != cpName">
            AND `cp_name` = #{cpName}
            </if>
	        <if test="null != createTime and '' != createTime">
            AND `create_time` = #{createTime}
            </if>
	        <if test="null != updateTime and '' != updateTime">
            AND `update_time` = #{updateTime}
            </if>
	        <if test="null != type">
            AND `type` = #{type}
            </if>
	        <if test="null != status">
            AND `status` = #{status}
            </if>
	        <if test="null != ratio and '' != ratio">
            AND `ratio` = #{ratio}
            </if>
	        <if test="null != sequence">
            AND `sequence` = #{sequence}
            </if>
	        <if test="null != creatorName and '' != creatorName">
            AND `creator_name` = #{creatorName}
            </if>
	        <if test="null != creatorId">
            AND `creator_id` = #{creatorId}
            </if>
        </where>
    </sql>

</mapper>

