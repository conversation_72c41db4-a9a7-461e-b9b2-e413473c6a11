<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//ibatis.apache.org//DTD Mapper 3.0//EN" "http://ibatis.apache.org/dtd/ibatis-3-mapper.dtd">


<!-- 不使用namespace的话sql搜索定位会比较方便 -->
<mapper namespace="com.pukka.iptv.manage.mapper.cms.CmsProgramMapper">

    <!-- 表名 -->
    <sql id="tableName">
        cms_program
    </sql>

    <sql id="columns">
        `id`
        ,`code`,`name`,`order_number`,`original_name`,`release_status`,`preview_status`,`sort_name`,`search_name`,`actor_display`,`writer_display`,`original_country_id`,`original_country`,`language`,`release_year`,`org_air_date`,`licensing_window_start`,`licensing_window_end`,`display_as_new`,`display_as_last_chance`,`macrovision`,`description`,`pgm_category_id`,`pgm_category`,`pgm_snd_class_id`,`pgm_snd_class`,`price_tax_in`,`status`,`source_type`,`series_flag`,`episode_index`,`series_id`,`series_code`,`series_name`,`kpeople`,`director`,`script_writer`,`compere`,`guest`,`reporter`,`op_incharge`,`vsp_code`,`copy_right`,`content_provider`,`duration`,`rating`,`cp_id`,`cp_name`,`create_time`,`update_time`,`cp_check_status`,`cp_check_desc`,`cp_check_time`,`cp_checker`,`op_check_status`,`op_check_desc`,`op_checker`,`op_check_time`,`source`,`creator_name`,`creator_id`,`lock_status`,`cucc_price`,`ctcc_price`,`cmcc_price`,`publisher`,`approval`,`definition_flag`,`movie_trailer_code`,`movie_status`,`resource_code`,`movie_head_duration`,`movie_tail_duration`,`new_price`,`type`,`tags`
    </sql>

    <sql id="where">
        <where>
            <if test="null != id">
                AND `id` = #{id}
            </if>
            <if test="null != code and '' != code">
                AND `code` = #{code}
            </if>
            <if test="null != name and '' != name">
                AND `name` = #{name}
            </if>
            <if test="null != orderNumber and '' != orderNumber">
                AND `order_number` = #{orderNumber}
            </if>
            <if test="null != originalName and '' != originalName">
                AND `original_name` = #{originalName}
            </if>
            <if test="null != releaseStatus">
                AND `release_status` = #{releaseStatus}
            </if>
            <if test="null != previewStatus">
                AND `preview_status` = #{previewStatus}
            </if>
            <if test="null != sortName and '' != sortName">
                AND `sort_name` = #{sortName}
            </if>
            <if test="null != searchName and '' != searchName">
                AND `search_name` = #{searchName}
            </if>
            <if test="null != actorDisplay and '' != actorDisplay">
                AND `actor_display` = #{actorDisplay}
            </if>
            <if test="null != writerDisplay and '' != writerDisplay">
                AND `writer_display` = #{writerDisplay}
            </if>
            <if test="null != originalCountryId">
                AND `original_country_id` = #{originalCountryId}
            </if>
            <if test="null != originalCountry and '' != originalCountry">
                AND `original_country` = #{originalCountry}
            </if>
            <if test="null != language and '' != language">
                AND `language` = #{language}
            </if>
            <if test="null != releaseYear and '' != releaseYear">
                AND `release_year` = #{releaseYear}
            </if>
            <if test="null != orgAirDate and '' != orgAirDate">
                AND `org_air_date` = #{orgAirDate}
            </if>
            <if test="null != licensingWindowStart and '' != licensingWindowStart">
                AND `licensing_window_start` = #{licensingWindowStart}
            </if>
            <if test="null != licensingWindowEnd and '' != licensingWindowEnd">
                AND `licensing_window_end` = #{licensingWindowEnd}
            </if>
            <if test="null != displayAsNew">
                AND `display_as_new` = #{displayAsNew}
            </if>
            <if test="null != displayAsLastChance">
                AND `display_as_last_chance` = #{displayAsLastChance}
            </if>
            <if test="null != macrovision">
                AND `macrovision` = #{macrovision}
            </if>
            <if test="null != description and '' != description">
                AND `description` = #{description}
            </if>
            <if test="null != pgmCategoryId">
                AND `pgm_category_id` = #{pgmCategoryId}
            </if>
            <if test="null != pgmCategory and '' != pgmCategory">
                AND `pgm_category` = #{pgmCategory}
            </if>
            <if test="null != pgmSndClassId">
                AND `pgm_snd_class_id` = #{pgmSndClassId}
            </if>
            <if test="null != pgmSndClass and '' != pgmSndClass">
                AND `pgm_snd_class` = #{pgmSndClass}
            </if>
            <if test="null != priceTaxIn and '' != priceTaxIn">
                AND `price_tax_in` = #{priceTaxIn}
            </if>
            <if test="null != status">
                AND `status` = #{status}
            </if>
            <if test="null != sourceType">
                AND `source_type` = #{sourceType}
            </if>
            <if test="null != seriesFlag">
                AND `series_flag` = #{seriesFlag}
            </if>
            <if test="null != episodeIndex">
                AND `episode_index` = #{episodeIndex}
            </if>
            <if test="null != seriesId">
                AND `series_id` = #{seriesId}
            </if>
            <if test="null != seriesCode and '' != seriesCode">
                AND `series_code` = #{seriesCode}
            </if>
            <if test="null != seriesName and '' != seriesName">
                AND `series_name` = #{seriesName}
            </if>
            <if test="null != kpeople and '' != kpeople">
                AND `kpeople` = #{kpeople}
            </if>
            <if test="null != director and '' != director">
                AND `director` = #{director}
            </if>
            <if test="null != scriptWriter and '' != scriptWriter">
                AND `script_writer` = #{scriptWriter}
            </if>
            <if test="null != compere and '' != compere">
                AND `compere` = #{compere}
            </if>
            <if test="null != guest and '' != guest">
                AND `guest` = #{guest}
            </if>
            <if test="null != reporter and '' != reporter">
                AND `reporter` = #{reporter}
            </if>
            <if test="null != opIncharge and '' != opIncharge">
                AND `op_incharge` = #{opIncharge}
            </if>
            <if test="null != vspCode and '' != vspCode">
                AND `vsp_code` = #{vspCode}
            </if>
            <if test="null != copyRight and '' != copyRight">
                AND `copy_right` = #{copyRight}
            </if>
            <if test="null != contentProvider and '' != contentProvider">
                AND `content_provider` = #{contentProvider}
            </if>
            <if test="null != duration">
                AND `duration` = #{duration}
            </if>
            <if test="null != rating and '' != rating">
                AND `rating` = #{rating}
            </if>
            <if test="null != cpId">
                AND `cp_id` = #{cpId}
            </if>
            <if test="null != cpName and '' != cpName">
                AND `cp_name` = #{cpName}
            </if>
            <if test="null != createTime and '' != createTime">
                AND `create_time` = #{createTime}
            </if>
            <if test="null != updateTime and '' != updateTime">
                AND `update_time` = #{updateTime}
            </if>
            <if test="null != cpCheckStatus">
                AND `cp_check_status` = #{cpCheckStatus}
            </if>
            <if test="null != cpCheckDesc and '' != cpCheckDesc">
                AND `cp_check_desc` = #{cpCheckDesc}
            </if>
            <if test="null != cpCheckTime and '' != cpCheckTime">
                AND `cp_check_time` = #{cpCheckTime}
            </if>
            <if test="null != cpChecker and '' != cpChecker">
                AND `cp_checker` = #{cpChecker}
            </if>
            <if test="null != opCheckStatus">
                AND `op_check_status` = #{opCheckStatus}
            </if>
            <if test="null != opCheckDesc and '' != opCheckDesc">
                AND `op_check_desc` = #{opCheckDesc}
            </if>
            <if test="null != opChecker and '' != opChecker">
                AND `op_checker` = #{opChecker}
            </if>
            <if test="null != opCheckTime and '' != opCheckTime">
                AND `op_check_time` = #{opCheckTime}
            </if>
            <if test="null != source">
                AND `source` = #{source}
            </if>
            <if test="null != creatorName and '' != creatorName">
                AND `creator_name` = #{creatorName}
            </if>
            <if test="null != creatorId">
                AND `creator_id` = #{creatorId}
            </if>
            <if test="null != lockStatus">
                AND `lock_status` = #{lockStatus}
            </if>
            <if test="null != cuccPrice and '' != cuccPrice">
                AND `cucc_price` = #{cuccPrice}
            </if>
            <if test="null != ctccPrice and '' != ctccPrice">
                AND `ctcc_price` = #{ctccPrice}
            </if>
            <if test="null != cmccPrice and '' != cmccPrice">
                AND `cmcc_price` = #{cmccPrice}
            </if>
            <if test="null != publisher and '' != publisher">
                AND `publisher` = #{publisher}
            </if>
            <if test="null != approval and '' != approval">
                AND `approval` = #{approval}
            </if>
            <if test="null != definitionFlag and '' != definitionFlag">
                AND `definition_flag` = #{definitionFlag}
            </if>
            <if test="null != movieTrailerCode and '' != movieTrailerCode">
                AND `movie_trailer_code` = #{movieTrailerCode}
            </if>
            <if test="null != movieStatus">
                AND `movie_status` = #{movieStatus}
            </if>
            <if test="null != resourceCode and '' != resourceCode">
                AND `resource_code` = #{resourceCode}
            </if>
            <if test="null != movieHeadDuration">
                AND `movie_head_duration` = #{movieHeadDuration}
            </if>
            <if test="null != movieTailDuration">
                AND `movie_tail_duration` = #{movieTailDuration}
            </if>
            <if test="null != newPrice and '' != newPrice">
                AND `new_price` = #{newPrice}
            </if>
        </where>
    </sql>

    <resultMap id="ContentResourceDto" type="com.pukka.iptv.common.data.dto.ContentResourceDto">
        <result property="contentId" column="content_id"/>
        <result property="contentName" column="content_name"/>
        <result property="contentType" column="content_type"/>
        <result property="contentCode" column="content_code"/>
        <result property="resourceCode" column="resource_code"/>
        <result property="resourceId" column="resource_id"/>
        <result property="cpId" column="cp_id"/>
        <result property="resourceType" column="resource_type"/>
    </resultMap>

    <select id="getReleaseList" resultMap="ContentResourceDto">
        select
               res.id                    as resource_id,
               pro.id                    as content_id,
               case pro.series_flag
                   when 0 then 1
                   when 1 then 2
                   else null end
                                         as content_type,
               pro.code                  as content_code,
               pro.resource_release_code as resource_code,
               pro.cp_id,
               res.type                  as resource_type,
               pro.name                  as content_name
        from cms_program pro
                 inner join cms_resource res
                      on pro.resource_release_code = res.code
                          and pro.cp_id = res.cp_id
                          and res.type = 1
                          and pro.release_status = #{releaseStatus}
                          and res.content_status = #{contentStatus}
                          group by resource_code
        limit #{relateCount}
    </select>


    <select id="getPreviewList" resultMap="ContentResourceDto">
        select distinct
               res.id                    as resource_id,
               pro.id                    as content_id,
               case pro.series_flag
                   when 0 then 1
                   when 1 then 2
                   else null end
                                         as content_type,
               pro.code                  as content_code,
               pro.resource_preview_code as resource_code,
               pro.cp_id,
               res.type                  as resource_type,
               pro.name                  as content_name
        from cms_program pro
                 inner join cms_resource res
                      on pro.resource_preview_code = res.code
                          and pro.cp_id = res.cp_id
                          and res.type = 2
                          and pro.preview_status = #{previewStatus}
                          and res.content_status = #{contentStatus}
        limit #{relateCount}
    </select>


    <select id="selectName" resultType="java.lang.String">
        select name from cms_program
        <where>
            cp_id = #{cpId}
            and name = #{name}
            and series_flag = #{value}
            and id != #{id}
            limit 1
        </where>
    </select>
    <select id="selectNewName" resultType="java.lang.String">
        select name
        from cms_program
        where cp_id = #{cpId}
          and name = #{name}
          and series_flag = #{value} limit 1
    </select>

    <select id="getProgramStatistics"  parameterType="com.pukka.iptv.common.data.vo.req.StatisticsInVo"  resultType="com.pukka.iptv.common.data.model.statistics.StatisticsIn">
        select t.cp_name as cpName, t.cp_id as cpId,  t.pgm_category_id as contentType, sum(t.duration) as duration, count(*) as count
        from (
        select DISTINCT(p.id), p.name ,p.resource_release_code ,p.pgm_category_id, p.pgm_category,
        p.series_flag,p.cp_id,p.cp_name, r.code, r.duration
        from cms_program  p  left join cms_resource r on p.resource_release_code = r.code
        where 1=1 and p.series_flag =0  and p.release_status=2
        <if test="param.startTime != null and param.startTime.trim() != ''">
            <![CDATA[   and p.create_time >=  (#{param.startTime})  ]]>
        </if>
        <if test="param.endTime != null and param.endTime.trim() != ''">
            <![CDATA[  and p.create_time <= (#{param.endTime})    ]]>
        </if>
        ) t
        group by t.cp_name, t.pgm_category_id
    </select>

    <select id="getSelfProgramStatistics"  parameterType="com.pukka.iptv.common.data.vo.req.StatisticsInVo"  resultType="com.pukka.iptv.common.data.model.statistics.StatisticsInCheck">
        select t.cp_name as cpName, t.cp_id as cpId,  t.pgm_category_id as contentType, sum(t.duration) as duration, count(*) as count
        from (
        select p.id, p.name ,p.resource_release_code ,p.pgm_category_id, p.pgm_category,
        p.series_flag,p.cp_id,p.cp_name, r.code, r.duration
        from cms_program  p  left join cms_resource r on p.resource_release_code = r.code
        where 1=1 and p.series_flag =0 and p.cp_check_status=3
        <if test="param.startTime != null and param.startTime.trim() != ''">
            <![CDATA[   and p.create_time >=  (#{param.startTime})  ]]>
        </if>
        <if test="param.endTime != null and param.endTime.trim() != ''">
            <![CDATA[  and p.create_time <= (#{param.endTime})    ]]>
        </if>
        ) t
        group by t.cp_name, t.pgm_category_id
    </select>

    <select id="getSelfProgramStatisticsAndSelf"  parameterType="com.pukka.iptv.common.data.vo.req.StatisticsInVo"  resultType="com.pukka.iptv.common.data.model.statistics.StatisticsInCheck">
        (select 0 as passType, t.cp_name as cpName, t.cp_id as cpId,  t.pgm_category_id as contentType, sum(t.duration) as duration, count(*) as count
        from (
        select * from

        (
        select p.id, p.name ,p.resource_release_code ,p.pgm_category_id, p.pgm_category,
        p.series_flag,p.cp_id,p.cp_name, r.code, r.duration,  h.checker_name, h.check_time, h.check_status, h.constant
        from cms_program  p  left join cms_resource r on p.resource_release_code = r.code
        left join (select (content_code),checker_name,type,check_time, check_status,  1 as constant from cms_check_history where type =1 and content_type =1 and check_status = 3) h on p.code =
        h.content_code

        where 1=1 and p.series_flag =0 and p.cp_check_status=3
        <if test="param.startTime != null and param.startTime.trim() != ''">
            <![CDATA[   and p.cp_check_time >=  (#{param.startTime})  ]]>
        </if>
        <if test="param.endTime != null and param.endTime.trim() != ''">
            <![CDATA[  and p.cp_check_time <= (#{param.endTime})    ]]>
        </if>
        )tt
        where 1=1  and ( 1=1
        <if test="param.startTime != null and param.startTime.trim() != ''">
            <![CDATA[   and DATE_FORMAT(tt.check_time, '%Y/%m/%d %H:%i:%S') >=  DATE_FORMAT(#{param.startTime}, '%Y/%m/%d %H:%i:%S')   ]]>
        </if>
        <if test="param.endTime != null and param.endTime.trim() != ''">
            <![CDATA[  and DATE_FORMAT(tt.check_time, '%Y/%m/%d %H:%i:%S') <= DATE_FORMAT(#{param.endTime}, '%Y/%m/%d %H:%i:%S')]]>
        </if>
        or  tt.check_time is null) and (tt.check_status is null or tt.check_status = 3)
        ) t
        group by t.cp_name, t.pgm_category_id)

        union all

        (
            select 1 as passType, t.cp_name as cpName, t.cp_id as cpId,  t.pgm_category_id as contentType, sum(t.duration) as duration, count(*) as count
            from (
            select * from

            (
            select p.id, p.name ,p.resource_release_code ,p.pgm_category_id, p.pgm_category,
            p.series_flag,p.cp_id,p.cp_name, r.code, r.duration,  h.checker_name, h.check_time, h.check_status, h.constant
            from cms_program  p  left join cms_resource r on p.resource_release_code = r.code
            left join (select (content_code),checker_name,type,check_time, check_status,  0 as constant from cms_check_history where type =1 and content_type =1 and check_status = 4) h on p.code =
            h.content_code

            where 1=1 and p.series_flag =0
            <if test="param.startTime != null and param.startTime.trim() != ''">
                <![CDATA[   and p.cp_check_time >=  (#{param.startTime})  ]]>
            </if>
            <if test="param.endTime != null and param.endTime.trim() != ''">
                <![CDATA[  and p.cp_check_time <= (#{param.endTime})    ]]>
            </if>
            )tt
            where 1=1  and ( 1=1
            <if test="param.startTime != null and param.startTime.trim() != ''">
                <![CDATA[   and DATE_FORMAT(tt.check_time, '%Y/%m/%d %H:%i:%S') >=  DATE_FORMAT(#{param.startTime}, '%Y/%m/%d %H:%i:%S')   ]]>
            </if>
            <if test="param.endTime != null and param.endTime.trim() != ''">
                <![CDATA[  and DATE_FORMAT(tt.check_time, '%Y/%m/%d %H:%i:%S') <= DATE_FORMAT(#{param.endTime}, '%Y/%m/%d %H:%i:%S')]]>
            </if>
            or  tt.check_time is null) and (tt.check_status is null or tt.check_status = 4)
            ) t
            group by t.cp_name, t.pgm_category_id
        )


    </select>

    <select id="getSelfSubStatistics"  parameterType="com.pukka.iptv.common.data.vo.req.StatisticsInVo"  resultType="com.pukka.iptv.common.data.model.statistics.StatisticsInCheck">
        select t.cp_name as cpName, t.cp_id as cpId,  t.pgm_category_id as contentType, sum(t.duration) as duration, count(*) as count
        from (
        select p.id, p.name ,p.resource_release_code ,p.pgm_category_id, p.pgm_category,
        p.series_flag,p.cp_id,p.cp_name, r.code, r.duration
        from cms_program  p  left join cms_resource r on p.resource_release_code = r.code
        where 1=1 and p.series_flag =1 and p.cp_check_status=3
        <if test="param.startTime != null and param.startTime.trim() != ''">
            <![CDATA[   and p.create_time >=  (#{param.startTime})  ]]>
        </if>
        <if test="param.endTime != null and param.endTime.trim() != ''">
            <![CDATA[  and p.create_time <= (#{param.endTime})    ]]>
        </if>
        ) t
        group by t.cp_name, t.pgm_category_id
    </select>


    <select id="getSelfSubStatisticsAndSelf"  parameterType="com.pukka.iptv.common.data.vo.req.StatisticsInVo"  resultType="com.pukka.iptv.common.data.model.statistics.StatisticsInCheck">
        select 0 as passType, t.cp_name as cpName, t.cp_id as cpId, t.pgm_category_id as contentType, sum(t.duration) as
        duration, count(*) as count
        from (

        select * from
        (

        select p.id, p.name ,p.resource_release_code ,p.pgm_category_id, p.pgm_category,
        p.series_flag,p.cp_id,p.cp_name, r.code, r.duration, h.checker_name, h.check_time,h.check_status, h.constant
        from cms_program p left join cms_resource r on p.resource_release_code = r.code
        left join (select (content_code),checker_name,type, check_time,  check_status,  1 as constant  from cms_check_history where type =1) h on p.code =
        h.content_code
        where 1=1 and p.series_flag =1 and p.cp_check_status=3
        <if test="param.startTime != null and param.startTime.trim() != ''">
            <![CDATA[   and p.cp_check_time >=  (#{param.startTime})  ]]>
        </if>
        <if test="param.endTime != null and param.endTime.trim() != ''">
            <![CDATA[  and p.cp_check_time <= (#{param.endTime})    ]]>
        </if>
        )tt
        where 1=1 and ( 1=1
        <if test="param.startTime != null and param.startTime.trim() != ''">
            <![CDATA[   and DATE_FORMAT(tt.check_time, '%Y/%m/%d %H:%i:%S') >=  DATE_FORMAT(#{param.startTime}, '%Y/%m/%d %H:%i:%S')   ]]>
        </if>
        <if test="param.endTime != null and param.endTime.trim() != ''">
            <![CDATA[  and DATE_FORMAT(tt.check_time, '%Y/%m/%d %H:%i:%S') <= DATE_FORMAT(#{param.endTime}, '%Y/%m/%d %H:%i:%S')]]>
        </if>
        or  tt.check_time is null ) and (tt.check_status is null or tt.check_status = 3)

        ) t
        group by t.cp_name, t.pgm_category_id

        union all


        select 1 as passType, t.cp_name as cpName, t.cp_id as cpId, t.pgm_category_id as contentType, sum(t.duration) as
        duration, count(*) as count
        from (
        select (p.id), p.name ,p.resource_release_code ,p.pgm_category_id, p.pgm_category,
        p.series_flag,p.cp_id,p.cp_name, r.code, r.duration, h.checker_name
        from cms_program p left join cms_resource r on p.resource_release_code = r.code
        left join (select (content_code),checker_name,type, check_status from cms_check_history where type =1 and content_type =2 and check_status = 4) h
        on p.code = h.content_code
        where 1=1 and p.series_flag =1
        <if test="param.startTime != null and param.startTime.trim() != ''">
            <![CDATA[   and p.cp_check_time >=  (#{param.startTime})  ]]>
        </if>
        <if test="param.endTime != null and param.endTime.trim() != ''">
            <![CDATA[  and p.cp_check_time <= (#{param.endTime})    ]]>
        </if>
        ) t
        group by t.cp_name, t.pgm_category_id
    </select>


    <select id="getFinalProgramStatistics"  parameterType="com.pukka.iptv.common.data.vo.req.StatisticsInVo"  resultType="com.pukka.iptv.common.data.model.statistics.StatisticsInFinal">
        ( select 0 as passType, t.cp_name as cpName, t.cp_id as cpId, t.pgm_category_id as contentType, sum(t.duration)
        as duration, count(*) as count, t.checker_name as operator
        from (

        select * from
        (

        select p.id, p.name ,p.resource_release_code ,p.pgm_category_id, p.pgm_category,
        p.series_flag,p.cp_id,p.cp_name, r.code, r.duration, h.checker_name, h.check_time, h.check_status, h.constant
        from cms_program p
        left join cms_resource r on p.resource_release_code = r.code
        left join (select distinct(content_code),checker_name,type,check_time, check_status,  1 as constant from cms_check_history where type =2) h on p.code =
        h.content_code
        where 1=1 and p.series_flag =0 and p.op_check_status=3
        <if test="param.startTime != null and param.startTime.trim() != ''">
            <![CDATA[   and p.op_check_time >=  (#{param.startTime})  ]]>
            </if>
            <if test="param.endTime != null and param.endTime.trim() != ''">
                <![CDATA[  and p.op_check_time <= (#{param.endTime})    ]]>
            </if>
        )tt
        where 1=1  and ( 1=1
        <if test="param.startTime != null and param.startTime.trim() != ''">
            <![CDATA[   and DATE_FORMAT(tt.check_time, '%Y/%m/%d %H:%i:%S') >=  DATE_FORMAT(#{param.startTime}, '%Y/%m/%d %H:%i:%S')   ]]>
        </if>
        <if test="param.endTime != null and param.endTime.trim() != ''">
            <![CDATA[  and DATE_FORMAT(tt.check_time, '%Y/%m/%d %H:%i:%S') <= DATE_FORMAT(#{param.endTime}, '%Y/%m/%d %H:%i:%S')]]>
        </if>
        or  tt.check_time is null) and (tt.check_status is null or tt.check_status = 3)



        ) t
        group by t.cp_name, t.pgm_category_id, t.checker_name)

        union all

        (select 1 as passType, t.cp_name as cpName, t.cp_id as cpId, t.pgm_category_id as contentType, sum(t.duration)
        as duration, count(*) as count, t.checker_name as operator
        from (
        select distinct(p.id), p.name ,p.resource_release_code ,p.pgm_category_id, p.pgm_category,
        p.series_flag,p.cp_id,p.cp_name, r.code, r.duration, h.checker_name
        from cms_program p left join cms_resource r on p.resource_release_code = r.code
        left join (select distinct(content_code),checker_name,type,check_status from cms_check_history where type =2) h on
        p.code = h.content_code
        where 1=1 and p.series_flag =0 and h.check_status = 4
        <if test="param.startTime != null and param.startTime.trim() != ''">
            <![CDATA[   and p.op_check_time >=  (#{param.startTime})  ]]>
        </if>
        <if test="param.endTime != null and param.endTime.trim() != ''">
            <![CDATA[  and p.op_check_time <= (#{param.endTime})    ]]>
        </if>
        ) t
        group by t.cp_name, t.pgm_category_id, t.checker_name)
    </select>

    <select id="getFinalSubStatistics"  parameterType="com.pukka.iptv.common.data.vo.req.StatisticsInVo"  resultType="com.pukka.iptv.common.data.model.statistics.StatisticsInFinal">
        select 0 as passType, t.cp_name as cpName, t.cp_id as cpId, t.pgm_category_id as contentType, sum(t.duration) as
        duration, count(*) as count, t.checker_name as operator
        from (

        select * from
        (

        select p.id, p.name ,p.resource_release_code ,p.pgm_category_id, p.pgm_category,
        p.series_flag,p.cp_id,p.cp_name, r.code, r.duration, h.checker_name, h.check_time,h.check_status, h.constant
        from cms_program p left join cms_resource r on p.resource_release_code = r.code
        left join (select distinct(content_code),checker_name,type, check_time,  check_status,  1 as constant  from cms_check_history where type =2) h on p.code =
        h.content_code
        where 1=1 and p.series_flag =1 and p.op_check_status=3
        <if test="param.startTime != null and param.startTime.trim() != ''">
            <![CDATA[   and p.op_check_time >=  (#{param.startTime})  ]]>
        </if>
        <if test="param.endTime != null and param.endTime.trim() != ''">
            <![CDATA[  and p.op_check_time <= (#{param.endTime})    ]]>
        </if>
        )tt
        where 1=1 and ( 1=1
             <if test="param.startTime != null and param.startTime.trim() != ''">
            <![CDATA[   and DATE_FORMAT(tt.check_time, '%Y/%m/%d %H:%i:%S') >=  DATE_FORMAT(#{param.startTime}, '%Y/%m/%d %H:%i:%S')   ]]>
            </if>
            <if test="param.endTime != null and param.endTime.trim() != ''">
                <![CDATA[  and DATE_FORMAT(tt.check_time, '%Y/%m/%d %H:%i:%S') <= DATE_FORMAT(#{param.endTime}, '%Y/%m/%d %H:%i:%S')]]>
            </if>
           or  tt.check_time is null ) and (tt.check_status is null or tt.check_status = 3)

        ) t
        group by t.cp_name, t.pgm_category_id, t.checker_name

        union all


        select 1 as passType, t.cp_name as cpName, t.cp_id as cpId, t.pgm_category_id as contentType, sum(t.duration) as
        duration, count(*) as count, t.checker_name as operator
        from (
        select distinct(p.id), p.name ,p.resource_release_code ,p.pgm_category_id, p.pgm_category,
        p.series_flag,p.cp_id,p.cp_name, r.code, r.duration, h.checker_name
        from cms_program p left join cms_resource r on p.resource_release_code = r.code
        left join (select distinct(content_code),checker_name,type, check_status from cms_check_history where type =2) h
        on p.code = h.content_code
        where 1=1 and p.series_flag =1 and h.check_status = 4
        <if test="param.startTime != null and param.startTime.trim() != ''">
            <![CDATA[   and p.op_check_time >=  (#{param.startTime})  ]]>
        </if>
        <if test="param.endTime != null and param.endTime.trim() != ''">
            <![CDATA[  and p.op_check_time <= (#{param.endTime})    ]]>
        </if>
        ) t
        group by t.cp_name, t.pgm_category_id, t.checker_name

    </select>


    <select id="getAgainProgramStatistics"  parameterType="com.pukka.iptv.common.data.vo.req.StatisticsInVo"  resultType="com.pukka.iptv.common.data.model.statistics.StatisticsInAgain">
        ( select 1 as passType, t.cp_name as cpName, t.cp_id as cpId,  t.pgm_category_id as contentType, sum(t.duration) as duration, count(*) as count, t.checker_name as operator
        from (
        select p.id, p.name ,p.resource_release_code ,p.pgm_category_id, p.pgm_category,
        p.series_flag,p.cp_id,p.cp_name, r.code, r.duration, h.checker_name
        from cms_check_history h
        left join cms_program  p on h.content_id = p.id
        left join cms_resource r on p.resource_release_code = r.code
        where 1=1 and p.series_flag =0 and h.type = 3 and h.content_type = 1 and h.check_status = 4
        <if test="param.startTime != null and param.startTime.trim() != ''">
            <![CDATA[   and DATE_FORMAT(h.check_time, '%Y/%m/%d %H:%i:%S') >=  DATE_FORMAT(#{param.startTime}, '%Y/%m/%d %H:%i:%S')   ]]>
        </if>
        <if test="param.endTime != null and param.endTime.trim() != ''">
            <![CDATA[  and DATE_FORMAT(h.check_time, '%Y/%m/%d %H:%i:%S') <= DATE_FORMAT(#{param.endTime}, '%Y/%m/%d %H:%i:%S')    ]]>
        </if>
        ) t
        group by t.cp_name, t.pgm_category_id, t.checker_name)

        union all

        ( select 0 as passType, t.cp_name as cpName, t.cp_id as cpId,  t.pgm_category_id as contentType, sum(t.duration) as duration, count(*) as count, t.checker_name as operator
        from (
        select p.id, p.name ,p.resource_release_code ,p.pgm_category_id, p.pgm_category,
        p.series_flag,p.cp_id,p.cp_name, r.code, r.duration, h.checker_name
        from cms_check_history h
        left join cms_program  p on h.content_id = p.id
        left join cms_resource r on p.resource_release_code = r.code
        where 1=1 and p.series_flag =0 and h.type = 3 and h.content_type = 1 and h.check_status = 3
        <if test="param.startTime != null and param.startTime.trim() != ''">
            <![CDATA[   and DATE_FORMAT(h.check_time, '%Y/%m/%d %H:%i:%S') >=  DATE_FORMAT(#{param.startTime}, '%Y/%m/%d %H:%i:%S')   ]]>
        </if>
        <if test="param.endTime != null and param.endTime.trim() != ''">
            <![CDATA[  and DATE_FORMAT(h.check_time, '%Y/%m/%d %H:%i:%S') <= DATE_FORMAT(#{param.endTime}, '%Y/%m/%d %H:%i:%S')    ]]>
        </if>
        ) t
        group by t.cp_name, t.pgm_category_id, t.checker_name)
    </select>


    <select id="getAgainSubStatistics"  parameterType="com.pukka.iptv.common.data.vo.req.StatisticsInVo"  resultType="com.pukka.iptv.common.data.model.statistics.StatisticsInAgain">
        ( select 1 as passType, t.cp_name as cpName, t.cp_id as cpId,  t.pgm_category_id as contentType, sum(t.duration) as duration, count(*) as count,  t.checker_name as operator
        from (
        select p.id, p.name ,p.resource_release_code ,p.pgm_category_id, p.pgm_category,
        p.series_flag,p.cp_id,p.cp_name, r.code, r.duration, h.checker_name
        from cms_check_history h
        left join cms_program  p on h.content_id = p.id
        left join cms_resource r on p.resource_release_code = r.code
        where 1=1 and p.series_flag =1 and h.type = 3 and h.content_type = 2 and h.check_status = 4
        <if test="param.startTime != null and param.startTime.trim() != ''">
            <![CDATA[   and DATE_FORMAT(h.check_time, '%Y/%m/%d %H:%i:%S') >=  DATE_FORMAT(#{param.startTime}, '%Y/%m/%d %H:%i:%S')   ]]>
        </if>
        <if test="param.endTime != null and param.endTime.trim() != ''">
            <![CDATA[  and DATE_FORMAT(h.check_time, '%Y/%m/%d %H:%i:%S') <= DATE_FORMAT(#{param.endTime}, '%Y/%m/%d %H:%i:%S')    ]]>
        </if>
        ) t
        group by t.cp_name, t.pgm_category_id, t.checker_name)

        union all

        ( select 0 as passType, t.cp_name as cpName, t.cp_id as cpId,  t.pgm_category_id as contentType, sum(t.duration) as duration, count(*) as count, t.checker_name as operator
        from (
        select p.id, p.name ,p.resource_release_code ,p.pgm_category_id, p.pgm_category,
        p.series_flag,p.cp_id,p.cp_name, r.code, r.duration, h.checker_name
        from cms_check_history h
        left join cms_program  p on h.content_id = p.id
        left join cms_resource r on p.resource_release_code = r.code
        where 1=1 and p.series_flag =1 and h.type = 3 and h.content_type = 2 and h.check_status = 3
        <if test="param.startTime != null and param.startTime.trim() != ''">
            <![CDATA[   and DATE_FORMAT(h.check_time, '%Y/%m/%d %H:%i:%S') >=  DATE_FORMAT(#{param.startTime}, '%Y/%m/%d %H:%i:%S')   ]]>
        </if>
        <if test="param.endTime != null and param.endTime.trim() != ''">
            <![CDATA[  and DATE_FORMAT(h.check_time, '%Y/%m/%d %H:%i:%S') <= DATE_FORMAT(#{param.endTime}, '%Y/%m/%d %H:%i:%S')    ]]>
        </if>

        ) t
        group by t.cp_name, t.pgm_category_id, t.checker_name)
    </select>

    <select id="getSubAndProgramStatistics"  parameterType="com.pukka.iptv.common.data.vo.req.StatisticsInVo"  resultType="com.pukka.iptv.common.data.model.statistics.StatisticsIn">
        select t.cp_name as cpName, t.cp_id as cpId,  t.pgm_category_id as contentType, sum(t.duration) as duration, count(*) as count
        from (
        select p.id, p.name ,p.resource_release_code ,p.pgm_category_id, p.pgm_category,
        p.series_flag,p.cp_id,p.cp_name, r.code, r.duration
        from cms_program  p  left join cms_resource r on p.resource_release_code = r.code
        where 1=1
        <if test="param.startTime != null and param.startTime.trim() != ''">
            <![CDATA[   and DATE_FORMAT(p.create_time, '%Y/%m/%d %H:%i:%S') >=  DATE_FORMAT(#{param.startTime}, '%Y/%m/%d %H:%i:%S')   ]]>
        </if>
        <if test="param.endTime != null and param.endTime.trim() != ''">
            <![CDATA[  and DATE_FORMAT(p.create_time, '%Y/%m/%d %H:%i:%S') <= DATE_FORMAT(#{param.endTime}, '%Y/%m/%d %H:%i:%S')    ]]>
        </if>
        ) t
        group by t.cp_name, t.pgm_category_id
    </select>

    <select id="getSubAndProgramStatisticsByResource"  parameterType="com.pukka.iptv.common.data.vo.req.StatisticsInVo"  resultType="com.pukka.iptv.common.data.model.statistics.StatisticsIn">
        select t.cp_name as cpName, t.cp_id as cpId,  t.pgm_category_id as contentType, sum(t.duration) as duration, count(*) as count
        from (
          select distinct(r.id),p.pgm_category_id, p.pgm_category,r.cp_id,r.cp_name, r.duration
          from cms_resource r
          left join cms_program p on p.resource_release_code = r.code and  0=1
            where 1=1
            <if test="param.startTime != null and param.startTime.trim() != ''">
                <![CDATA[   and r.create_time >=  (#{param.startTime})  ]]>
            </if>
            <if test="param.endTime != null and param.endTime.trim() != ''">
                <![CDATA[  and r.create_time <= (#{param.endTime})    ]]>
            </if>
        ) t
        group by t.cp_name, t.pgm_category_id
    </select>

    <select id="getSubStatistics"  parameterType="com.pukka.iptv.common.data.vo.req.StatisticsInVo"  resultType="com.pukka.iptv.common.data.model.statistics.StatisticsIn">
        select t.cp_name as cpName, t.cp_id as cpId,  t.pgm_category_id as contentType, sum(t.duration) as duration, count(*) as count
        from (
        select DISTINCT (p.id), p.name ,p.resource_release_code ,p.pgm_category_id, p.pgm_category,
        p.series_flag,p.cp_id,p.cp_name, r.code, r.duration
        from cms_program  p  left join cms_resource r on p.resource_release_code = r.code
        where 1=1 and p.series_flag =1  and p.release_status=2
        <if test="param.startTime != null and param.startTime.trim() != ''">
            <![CDATA[   and p.create_time >=  (#{param.startTime})  ]]>
        </if>
        <if test="param.endTime != null and param.endTime.trim() != ''">
            <![CDATA[  and p.create_time <= (#{param.endTime})    ]]>
        </if>
        ) t
        group by t.cp_name, t.pgm_category_id
    </select>

    <!-- 分页查询未引入的媒资 -->
    <select id="getUnauthorizedContentByCmsProgram" parameterType="com.pukka.iptv.common.data.dto.CmsProgramDO" resultType="com.pukka.iptv.common.data.model.cms.CmsProgram">
        <if test="param.seriesFlag == 0">
            SELECT
            <!--<include refid="columns" />-->
            cs.*
            FROM
            cms_program cs
            WHERE cs.series_flag = #{param.seriesFlag}
            <if test="param.code != null and param.code != ''">
                AND cs.code = #{param.code}
            </if>
            <if test="param.contentRating != null and param.contentRating != ''">
                AND cs.content_rating = #{param.contentRating}
            </if>
            <if test="param.auditStatus != null and param.auditStatus != ''">
                AND cs.audit_status = #{param.auditStatus}
            </if>
            <if test="param.pageType != null and param.pageType == 2">
                AND cs.prohibit_status = 0
            </if>
            <if test="param.cpCheckStatus != null and param.cpCheckStatus != ''">
                AND cs.cp_check_status = #{param.cpCheckStatus}
            </if>
            <if test="param.releaseStatus != null and param.releaseStatus != ''">
                AND cs.release_status = #{param.releaseStatus}
            </if>
            <if test="param.opCheckStatus != null and param.opCheckStatus != ''">
                AND cs.op_check_status = #{param.opCheckStatus}
            </if>
            <if test="param.pgmCategoryId != null">
                AND cs.pgm_category_id = #{param.pgmCategoryId}
            </if>
            <if test="param.cpId != null">
                AND cs.cp_id = #{param.cpId}
            </if>
            <if test="param.language != null and param.language != ''">
                AND cs.language = #{param.language}
            </if>
            <if test="param.originalName != null and param.originalName != ''">
                AND cs.original_name LIKE CONCAT('%',#{param.originalName},'%')
            </if>

            <if test="param.cpName != null">
                AND cs.cp_name = #{param.cpName}
            </if>

            <if test="param.pgmCategory != null">
                AND cs.pgm_category = #{param.pgmCategory}
            </if>
            <if test="param.contentProvider != null and param.contentProvider != ''">
                AND cs.content_provider LIKE CONCAT('%',#{param.contentProvider},'%')
            </if>

            <if test="param.startTime != null and param.startTime.trim() != ''">
                <![CDATA[   and DATE_FORMAT(cs.create_time, '%Y/%m/%d %H:%i:%S') >=  DATE_FORMAT(#{param.startTime}, '%Y/%m/%d %H:%i:%S')   ]]>
            </if>
            <if test="param.endTime != null and param.endTime.trim() != ''">
                <![CDATA[  and DATE_FORMAT(cs.create_time, '%Y/%m/%d %H:%i:%S') <= DATE_FORMAT(#{param.endTime}, '%Y/%m/%d %H:%i:%S')    ]]>
            </if>
            <if test="param.definitionFlag != null and param.definitionFlag != ''">
                AND cs.definition_flag = #{param.definitionFlag}
            </if>
            <if test="param.nameList != null and param.nameList.length > 0">
                <if test="param.likeOrinFlag == 1">
                    AND cs.name LIKE CONCAT('%',#{param.nameLike},'%')
                </if>

                <if test="param.likeOrinFlag == 2">
                    <trim prefix="AND cs.name in (" suffix=")">
                        <foreach collection="param.nameList" item="item" index="index" separator=",">
                            <if test="item !=null">
                                #{item}
                            </if>
                        </foreach>
                    </trim>
                </if>

            </if>

            <if test="param.grant != null">

                <if test="param.grant == 0" >
                    and  cs.id not in (SELECT distinct bms.cms_content_id from bms_content bms left join cms_program cms  on bms.cms_content_id=cms.id )
                </if>
                <if test="param.grant == 1" >
                    and  cs.id  in (SELECT distinct bms.cms_content_id from bms_content bms left join cms_program cms  on bms.cms_content_id=cms.id )
                </if>
            </if>
            <if test="param.isProhibit != null">
                AND cs.is_prohibit = #{param.isProhibit}
            </if>
            order by cs.id  desc
        </if>

        <if test="param.seriesFlag == 1">
            SELECT
            <!--<include refid="columns" />-->
            cs.*
            FROM
            cms_program  cs
            WHERE cs.series_flag = #{param.seriesFlag}
            <if test="param.code != null and param.code != ''">
                AND cs.code = #{param.code}
            </if>
            <if test="param.auditStatus != null and param.auditStatus != ''">
                AND cs.audit_status = #{param.auditStatus}
            </if>
            <if test="param.pageType != null and param.pageType == 2">
                AND cs.prohibit_status = 0
            </if>
            <if test="param.cpCheckStatus != null and param.cpCheckStatus != ''">
                AND cs.cp_check_status = #{param.cpCheckStatus}
            </if>
            <if test="param.releaseStatus != null and param.releaseStatus != ''">
                AND cs.release_status = #{param.releaseStatus}
            </if>
            <if test="param.opCheckStatus != null and param.opCheckStatus != ''">
                AND cs.op_check_status = #{param.opCheckStatus}
            </if>
            <if test="param.pgmCategoryId != null">
                AND cs.pgm_category_id = #{param.pgmCategoryId}
            </if>
            <if test="param.cpId != null">
                AND cs.cp_id = #{param.cpId}
            </if>

            <if test="param.originalName != null and param.originalName != ''">
                AND cs.original_name LIKE CONCAT('%',#{param.originalName},'%')
            </if>

            <if test="param.cpName != null">
                AND cs.cp_name = #{param.cpName}
            </if>

            <if test="param.pgmCategory != null">
                AND cs.pgm_category = #{param.pgmCategory}
            </if>

            <if test="param.seriesName != null and param.seriesName != '' ">
                AND cs.series_name = #{param.seriesName}
            </if>

            <if test="param.contentProvider != null and param.contentProvider != ''">
                AND cs.content_provider LIKE CONCAT('%',#{param.contentProvider},'%')
            </if>

            <if test="param.startTime != null and param.startTime.trim() != ''">
                <![CDATA[   and DATE_FORMAT(cs.create_time, '%Y/%m/%d %H:%i:%S') >=  DATE_FORMAT(#{param.startTime}, '%Y/%m/%d %H:%i:%S')   ]]>
            </if>
            <if test="param.endTime != null and param.endTime.trim() != ''">
                <![CDATA[  and DATE_FORMAT(cs.create_time, '%Y/%m/%d %H:%i:%S') <= DATE_FORMAT(#{param.endTime}, '%Y/%m/%d %H:%i:%S')    ]]>
            </if>


            <if test="param.nameList != null and param.nameList.length > 0">
                <if test="param.likeOrinFlag == 1">
                    AND cs.name LIKE CONCAT('%',#{param.nameLike},'%')
                </if>

                <if test="param.likeOrinFlag == 2">
                    <trim prefix="AND cs.name in (" suffix=")">
                        <foreach collection="param.nameList" item="item" index="index" separator=",">
                            <if test="item !=null">
                                #{item}
                            </if>
                        </foreach>
                    </trim>
                </if>

            </if>

            <if test="param.grant != null">

                <if test="param.grant == 0" >
                    and  cs.id not in (SELECT distinct bms.cms_content_id from bms_program bms left join cms_program cms  on bms.cms_content_id=cms.id )
                </if>
                <if test="param.grant == 1" >
                    and  cs.id  in (SELECT distinct bms.cms_content_id from bms_program bms left join cms_program cms  on bms.cms_content_id=cms.id )
                </if>
            </if>

            order by cs.id  desc
        </if>

    </select>

</mapper>

