<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//ibatis.apache.org//DTD Mapper 3.0//EN" "http://ibatis.apache.org/dtd/ibatis-3-mapper.dtd">


<!-- 不使用namespace的话sql搜索定位会比较方便 -->
<mapper namespace="com.pukka.iptv.manage.mapper.cms.CmsCheckHistoryMapper">

	<!-- 表名 -->
	<sql id="tableName">
        cms_check_history
    </sql>
    
    <sql id="columns">
        `id`,`checker_name`,`checker_id`,`description`,`type`,`check_time`,`content_id`,`content_code`,`content_type`,`check_status`
    </sql>
    
    <sql id="where">
        <where>
	        <if test="null != id">
            AND `id` = #{id}
            </if>
	        <if test="null != checkerName and '' != checkerName">
            AND `checker_name` = #{checkerName}
            </if>
	        <if test="null != checkerId">
            AND `checker_id` = #{checkerId}
            </if>
	        <if test="null != description and '' != description">
            AND `description` = #{description}
            </if>
	        <if test="null != type">
            AND `type` = #{type}
            </if>
	        <if test="null != checkTime and '' != checkTime">
            AND `check_time` = #{checkTime}
            </if>
	        <if test="null != contentId">
            AND `content_id` = #{contentId}
            </if>
	        <if test="null != contentCode and '' != contentCode">
            AND `content_code` = #{contentCode}
            </if>
	        <if test="null != contentType">
            AND `content_type` = #{contentType}
            </if>
	        <if test="null != checkStatus">
            AND `check_status` = #{checkStatus}
            </if>
        </where>
    </sql>

</mapper>

