<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//ibatis.apache.org//DTD Mapper 3.0//EN" "http://ibatis.apache.org/dtd/ibatis-3-mapper.dtd">


<!-- 不使用namespace的话sql搜索定位会比较方便 -->
<mapper namespace="com.pukka.iptv.manage.mapper.CmsDownloadMapper">

	<!-- 表名 -->
	<sql id="tableName">
        cms_download
    </sql>
    
    <sql id="columns">
        `id`,`priority`,`name`,`cms_content_code`,`type`,`file_size`,`content_name`,`status`,`description`,`target_address`,`source_address`,`create_time`,`update_time`,`in_passage_name`,`in_passage_id`,`creator_id`,`creator_name`,`param`,`storage_id`
    </sql>
    
    <sql id="where">
        <where>
	        <if test="null != id">
            AND `id` = #{id}
            </if>
	        <if test="null != priority">
            AND `priority` = #{priority}
            </if>
	        <if test="null != name and '' != name">
            AND `name` = #{name}
            </if>
	        <if test="null != cmsContentCode and '' != cmsContentCode">
            AND `cms_content_code` = #{cmsContentCode}
            </if>
	        <if test="null != type">
            AND `type` = #{type}
            </if>
	        <if test="null != fileSize">
            AND `file_size` = #{fileSize}
            </if>
	        <if test="null != contentName and '' != contentName">
            AND `content_name` = #{contentName}
            </if>
	        <if test="null != status">
            AND `status` = #{status}
            </if>
	        <if test="null != description and '' != description">
            AND `description` = #{description}
            </if>
	        <if test="null != targetAddress and '' != targetAddress">
            AND `target_address` = #{targetAddress}
            </if>
	        <if test="null != sourceAddress and '' != sourceAddress">
            AND `source_address` = #{sourceAddress}
            </if>
	        <if test="null != createTime and '' != createTime">
            AND `create_time` = #{createTime}
            </if>
	        <if test="null != updateTime and '' != updateTime">
            AND `update_time` = #{updateTime}
            </if>
	        <if test="null != inPassageName and '' != inPassageName">
            AND `in_passage_name` = #{inPassageName}
            </if>
	        <if test="null != inPassageId">
            AND `in_passage_id` = #{inPassageId}
            </if>
	        <if test="null != creatorId">
            AND `creator_id` = #{creatorId}
            </if>
	        <if test="null != creatorName and '' != creatorName">
            AND `creator_name` = #{creatorName}
            </if>
	        <if test="null != param and '' != param">
            AND `param` = #{param}
            </if>
	        <if test="null != storageId">
            AND `storage_id` = #{storageId}
            </if>
        </where>
    </sql>

</mapper>

