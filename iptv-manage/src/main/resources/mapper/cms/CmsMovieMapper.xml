<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//ibatis.apache.org//DTD Mapper 3.0//EN" "http://ibatis.apache.org/dtd/ibatis-3-mapper.dtd">


<!-- 不使用namespace的话sql搜索定位会比较方便 -->
<mapper namespace="com.pukka.iptv.manage.mapper.cms.CmsMovieMapper">

	<!-- 表名 -->
	<sql id="tableName">
        cms_movie
    </sql>
    
    <sql id="columns">
        `id`,`code`,`name`,`type`,`resource_id`,`file_url`,`play_url`,`source_drm_type`,`dest_drm_type`,`audio_type`,`screen_format`,`closed_caption`,`media_spec`,`bit_rate_type`,`movie_head_duration`,`movie_tail_duration`,`cp_id`,`cp_name`,`create_time`,`update_time`,`status`,`duration`,`creator_name`,`creator_id`,`envelope`,`bitrate_type`,`video_codec`,`video_bitrate`,`resolution`,`frame_rate`,`audio_codec`,`audio_bitrate`,`content_id`,`content_type`,`content_status`,`file_size`,`vid`,`fileid`,`width`,`height`,`mediainfo`
    </sql>
    
    <sql id="where">
        <where>
	        <if test="null != id">
            AND `id` = #{id}
            </if>
	        <if test="null != code and '' != code">
            AND `code` = #{code}
            </if>
	        <if test="null != name and '' != name">
            AND `name` = #{name}
            </if>
	        <if test="null != type">
            AND `type` = #{type}
            </if>
	        <if test="null != resourceId">
            AND `resource_id` = #{resourceId}
            </if>
	        <if test="null != fileUrl and '' != fileUrl">
            AND `file_url` = #{fileUrl}
            </if>
	        <if test="null != playUrl and '' != playUrl">
            AND `play_url` = #{playUrl}
            </if>
	        <if test="null != sourceDrmType">
            AND `source_drm_type` = #{sourceDrmType}
            </if>
	        <if test="null != destDrmType">
            AND `dest_drm_type` = #{destDrmType}
            </if>
	        <if test="null != audioType">
            AND `audio_type` = #{audioType}
            </if>
	        <if test="null != screenFormat">
            AND `screen_format` = #{screenFormat}
            </if>
	        <if test="null != closedCaption">
            AND `closed_caption` = #{closedCaption}
            </if>
	        <if test="null != mediaSpec and '' != mediaSpec">
            AND `media_spec` = #{mediaSpec}
            </if>
	        <if test="null != bitRateType">
            AND `bit_rate_type` = #{bitRateType}
            </if>
	        <if test="null != movieHeadDuration">
            AND `movie_head_duration` = #{movieHeadDuration}
            </if>
	        <if test="null != movieTailDuration">
            AND `movie_tail_duration` = #{movieTailDuration}
            </if>
	        <if test="null != cpId">
            AND `cp_id` = #{cpId}
            </if>
	        <if test="null != cpName and '' != cpName">
            AND `cp_name` = #{cpName}
            </if>
	        <if test="null != createTime and '' != createTime">
            AND `create_time` = #{createTime}
            </if>
	        <if test="null != updateTime and '' != updateTime">
            AND `update_time` = #{updateTime}
            </if>
	        <if test="null != status">
            AND `status` = #{status}
            </if>
	        <if test="null != duration">
            AND `duration` = #{duration}
            </if>
	        <if test="null != creatorName and '' != creatorName">
            AND `creator_name` = #{creatorName}
            </if>
	        <if test="null != creatorId">
            AND `creator_id` = #{creatorId}
            </if>
	        <if test="null != envelope and '' != envelope">
            AND `envelope` = #{envelope}
            </if>
<!--	        <if test="null != bitrateType and '' != bitrateType">-->
<!--            AND `bitrate_type` = #{bitrateType}-->
<!--            </if>-->
	        <if test="null != videoCodec and '' != videoCodec">
            AND `video_codec` = #{videoCodec}
            </if>
	        <if test="null != videoBitrate and '' != videoBitrate">
            AND `video_bitrate` = #{videoBitrate}
            </if>
	        <if test="null != resolution and '' != resolution">
            AND `resolution` = #{resolution}
            </if>
	        <if test="null != frameRate">
            AND `frame_rate` = #{frameRate}
            </if>
	        <if test="null != audioCodec and '' != audioCodec">
            AND `audio_codec` = #{audioCodec}
            </if>
	        <if test="null != audioBitrate and '' != audioBitrate">
            AND `audio_bitrate` = #{audioBitrate}
            </if>
	        <if test="null != contentId">
            AND `content_id` = #{contentId}
            </if>
	        <if test="null != contentType">
            AND `content_type` = #{contentType}
            </if>
	        <if test="null != contentStatus">
            AND `content_status` = #{contentStatus}
            </if>
	        <if test="null != fileSize">
            AND `file_size` = #{fileSize}
            </if>
            <if test="null != vid">
                AND `vid` = #{vid}
            </if>
            <if test="null != fileid">
                AND `fileid` = #{fileid}
            </if>
            <if test="null != width">
                AND `width` = #{width}
            </if>
            <if test="null != height">
                AND `height` = #{height}
            </if>
            <if test="null != mediainfo">
                AND `mediainfo` = #{mediainfo}
            </if>
        </where>
    </sql>
    <!--视频列表查询 -->
    <select id="listById" parameterType="com.pukka.iptv.common.data.model.cms.CmsMovie"  resultType="com.pukka.iptv.common.data.model.cms.CmsMovie">
       <if test="param.cmsContentId !=null and param.contentType != null">
           SELECT id,file_url,type,video_codec,resolution,duration,file_url,file_size,content_status,storage_id
           FROM
           cms_movie
           WHERE
           content_id=#{param.contentId}
           AND
           content_type=#{param.contentType}
       </if>
    </select>


   <!-- <update id="updateBatchById" parameterType="java.util.List">
        <foreach collection="movieList" item="movie" separator=";">
            update cms_movie
            <set>
                <trim suffixOverrides=",">
                    <if test="#{movie.contentType} != null">
                        content_type = #{movie.contentType}
                    </if>
                    <if test="#{movie.contentId} != null">
                        cms_content_id = #{movie.contentId}
                    </if>
                    <if test="#{movie.contentCode} != null">
                        content_code = #{movie.contentCode}
                    </if>
                    <if test="#{movie.contentType} != null">
                        content_status = #{movie.releaseStatus}
                    </if>
                </trim>
            </set>
            <where>
                <if test="#{movie.resourceId} != null">
                    id = #{movie.resourceId}
                </if>
            </where>
        </foreach>
    </update>-->


    <update id="updateBatchByResourceId" parameterType="java.util.List">
        update cms_movie
        <trim prefix="set" suffixOverrides=",">
            <trim prefix="content_type = case" suffix="end,">
                <foreach collection="movieList" item="item" index="index">
                    <if test="item.contentType !=null">
                        when resource_id=#{item.resourceId} then #{item.contentType}
                    </if>
                </foreach>
            </trim>
            <trim prefix="content_id = case" suffix="end,">
                <foreach collection="movieList" item="item" index="index">
                    <if test="item.contentId !=null">
                        when resource_id=#{item.resourceId} then #{item.contentId}
                    </if>
                </foreach>
            </trim>
            <trim prefix="content_code = case" suffix="end,">
                <foreach collection="movieList" item="item" index="index">
                    <if test="item.contentCode !=null">
                        when resource_id=#{item.resourceId} then #{item.contentCode}
                    </if>
                </foreach>
            </trim>
            <trim prefix="content_status = case" suffix="end,">
                <foreach collection="movieList" item="item" index="index">
                    <if test="item.releaseStatus !=null">
                        when resource_id=#{item.resourceId} then #{item.releaseStatus}
                    </if>
                </foreach>
            </trim>
        </trim>
        where resource_id in
        <foreach collection="movieList" index="index" item="item" separator="," open="(" close=")">
            #{item.resourceId,jdbcType=BIGINT}
        </foreach>
    </update>
</mapper>

