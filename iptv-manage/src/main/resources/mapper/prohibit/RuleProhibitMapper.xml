<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//ibatis.apache.org//DTD Mapper 3.0//EN" "http://ibatis.apache.org/dtd/ibatis-3-mapper.dtd">


<!-- 不使用namespace的话sql搜索定位会比较方便 -->
<mapper namespace="com.pukka.iptv.manage.mapper.copyright.prohibit.RuleProhibitMapper">

	<!-- 表名 -->
	<sql id="tableName">
        rule_prohibit
    </sql>
    
    <sql id="columns">
        `id`,`code`,`show_name`,`pgm_category_id`,`pgm_category`,`content_type`,`kpeople`,`director`,`original_country_id`,`original_country`,`release_year`,`creator_name`,`create_time`,`update_time`
    </sql>
    
    <sql id="where">
        <where>
	        <if test="null != id">
            AND `id` = #{id}
            </if>
	        <if test="null != code and '' != code">
            AND `code` = #{code}
            </if>
	        <if test="null != showName and '' != showName">
            AND `show_name` = #{showName}
            </if>
	        <if test="null != pgmCategoryId">
            AND `pgm_category_id` = #{pgmCategoryId}
            </if>
	        <if test="null != pgmCategory and '' != pgmCategory">
            AND `pgm_category` = #{pgmCategory}
            </if>
	        <if test="null != contentType">
            AND `content_type` = #{contentType}
            </if>
	        <if test="null != kpeople and '' != kpeople">
            AND `kpeople` = #{kpeople}
            </if>
	        <if test="null != director and '' != director">
            AND `director` = #{director}
            </if>
	        <if test="null != originalCountryId">
            AND `original_country_id` = #{originalCountryId}
            </if>
	        <if test="null != originalCountry and '' != originalCountry">
            AND `original_country` = #{originalCountry}
            </if>
	        <if test="null != releaseYear and '' != releaseYear">
            AND `release_year` = #{releaseYear}
            </if>
	        <if test="null != creatorName and '' != creatorName">
            AND `creator_name` = #{creatorName}
            </if>
	        <if test="null != createTime and '' != createTime">
            AND `create_time` = #{createTime}
            </if>
	        <if test="null != updateTime and '' != updateTime">
            AND `update_time` = #{updateTime}
            </if>
        </where>
    </sql>

    <insert id="batchInsert">
        insert ignore into <include refid="tableName"/>
        (`id`,`code`,`show_name`,`pgm_category_id`,`pgm_category`,`content_type`,`kpeople`,`director`,`original_country_id`,`original_country`,`release_year`,`creator_name`)
        values
        <foreach collection="records" item="item" separator=",">
            (#{item.id},#{item.code}, #{item.showName},#{item.pgmCategoryId},#{item.pgmCategory},#{item.contentType},#{item.kpeople},#{item.director},#{item.originalCountryId},#{item.originalCountry},#{item.releaseYear},#{item.creatorName})
        </foreach>
    </insert>

    <select id="selectListPage" resultType="com.pukka.iptv.common.data.model.copyright.RuleProhibit">
        select
        <include refid="columns"/>
        from
        <include refid="tableName"/>
        <where>
            <if test="null != vo.kpeople and '' != vo.kpeople">
                AND instr(`kpeople`,#{vo.kpeople})
            </if>
            <if test="vo.nameList != null and vo.nameList.length > 0">
                <if test="vo.likeOrinFlag == 1">
                    AND `show_name` like concat("%", #{vo.nameList[0]},"%")
                </if>
                <if test="vo.likeOrinFlag == 2">
                    <trim prefix="AND show_name in (" suffix=")">
                        <foreach collection="vo.nameList" item="item" index="index" separator=",">
                            <if test="item !=null">
                                #{item}
                            </if>
                        </foreach>
                    </trim>
                </if>
            </if>
            <if test="null != vo.director and '' != vo.director">
                AND instr(`director`,#{vo.director})
            </if>
            <if test="null != vo.originalCountryId">
                AND `original_country_id` = #{vo.originalCountryId}
            </if>
            <if test="null != vo.contentType and '' != vo.contentType and 0 != vo.contentType">
                AND `content_type` = #{vo.contentType}
            </if>
            <if test="null != vo.pgmCategoryId">
                AND `pgm_category_id` = #{vo.pgmCategoryId}
            </if>

        </where>
        order by `id` desc
    </select>

</mapper>

