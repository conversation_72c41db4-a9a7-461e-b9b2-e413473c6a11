<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//ibatis.apache.org//DTD Mapper 3.0//EN" "http://ibatis.apache.org/dtd/ibatis-3-mapper.dtd">


<!-- 不使用namespace的话sql搜索定位会比较方便 -->
<mapper namespace="com.pukka.iptv.manage.mapper.copyright.prohibit.ArtistProhibitMapper">

	<!-- 表名 -->
	<sql id="tableName">
        artist_prohibit
    </sql>
    
    <sql id="columns">
        `id`,`code`,`artist_name`,`artist_sex`,`birthday`,`description`,`prohibit_cause`,`creator_name`,`create_time`,`update_time`
    </sql>
    
    <sql id="where">
        <where>
	        <if test="null != id">
            AND `id` = #{id}
            </if>
	        <if test="null != code and '' != code">
            AND `code` = #{code}
            </if>
	        <if test="null != artistName and '' != artistName">
            AND `artist_name` = #{artistName}
            </if>
	        <if test="null != artistSex and '' != artistSex">
            AND `artist_sex` = #{artistSex}
            </if>
	        <if test="null != birthday and '' != birthday">
            AND `birthday` = #{birthday}
            </if>
	        <if test="null != description and '' != description">
            AND `description` = #{description}
            </if>
	        <if test="null != prohibitCause and '' != prohibitCause">
            AND `prohibit_cause` = #{prohibitCause}
            </if>
	        <if test="null != creatorName and '' != creatorName">
            AND `creator_name` = #{creatorName}
            </if>
	        <if test="null != createTime and '' != createTime">
            AND `create_time` = #{createTime}
            </if>
	        <if test="null != updateTime and '' != updateTime">
            AND `update_time` = #{updateTime}
            </if>
        </where>
    </sql>

    <insert id="batchInsert">
        insert ignore into <include refid="tableName"/>
        (`id`,`code`,`artist_name`,`artist_sex`,`birthday`,`description`,`prohibit_cause`,
        `creator_name`,`create_time`,`update_time`)
        values
        <foreach collection="artistProhibits" item="item" separator=",">
            (#{item.id},#{item.code},#{item.artistName}, #{item.artistSex},#{item.birthday},#{item.description},#{item.prohibitCause},
            #{item.creatorName},#{item.createTime},#{item.updateTime})
        </foreach>
    </insert>

</mapper>

