<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.pukka.iptv.manage.mapper.copyright.copyright.CopyrightInfoMapper">

    <resultMap type="com.pukka.iptv.common.data.model.copyright.CopyrightInfo" id="CopyrightMap">
        <result property="id" column="id"/>
        <result property="code" column="code"/>
        <result property="name" column="name"/>
        <result property="contentType" column="content_type"/>
        <result property="cpId" column="cp_id"/>
        <result property="cpName" column="cp_name"/>
        <result property="contentProvider" column="content_provider"/>
        <result property="director" column="director"/>
        <result property="kpeople" column="kpeople"/>
        <result property="approval" column="approval"/>
        <result property="copyRight" column="copy_right"/>
        <result property="publisher" column="publisher"/>
        <result property="volumnCount" column="volumn_count"/>
        <result property="licensingWindowStart" column="licensing_window_start"/>
        <result property="licensingWindowEnd" column="licensing_window_end"/>
        <result property="displayAsLastChance" column="displayAsLastChance"/>
        <result property="pgmCategoryId" column="pgm_category_id"/>
        <result property="pgmCategory" column="pgm_category"/>
        <result property="releaseYear" column="release_year"/>
        <result property="originalCountryId" column="original_country_id"/>
        <result property="originalCountry" column="original_country"/>
        <result property="createTime" column="create_time"/>
        <result property="updateTime" column="update_time"/>
    </resultMap>

    <select id="getCopyrightInfoList" resultMap="CopyrightMap">
        (select
        s.id,
        s.code,
        s.name,
        3 as content_type,
        s.cp_id,
        s.cp_name,
        s.content_provider,
        s.director,
        s.kpeople,
        s.approval,
        s.copy_right,
        s.publisher,
        s.volumn_count,
        s.licensing_window_start,
        s.licensing_window_end,
        datediff(s.licensing_window_end,now()) as displayAsLastChance,
        s.pgm_category_id,
        s.pgm_category,
        s.release_year,
        s.original_country_id,
        s.original_country,
        s.create_time,
        s.update_time
        from cms_series s
        <trim prefix="where" prefixOverrides="and|or">
            <if test="names != null and names.size() >0">
                <choose>
                    <when test="status == 0">
                        <foreach collection="names" item="item" index="index" open="(" separator="or" close=")">
                            s.name LIKE CONCAT('%',#{item},'%')
                        </foreach>
                    </when>
                    <otherwise>
                        s.name in
                        <foreach item="item" index="index" collection="names"
                                 open="(" separator="," close=")">#{item}
                        </foreach>
                    </otherwise>
                </choose>
            </if>
            and s.prohibit_status = 0
            <if test="req.pgmCategoryId != null and req.pgmCategoryId != ''">
                and s.pgm_category_id =#{req.pgmCategoryId}
            </if>
            <if test="req.releaseYear != null and req.releaseYear != ''">
                and s.release_year =#{req.releaseYear}
            </if>
            <if test="req.approval != null and req.approval != ''">
                and s.approval =#{req.approval}
            </if>
            <if test="req.cpId != null and req.cpId != ''">
                and s.cp_id =#{req.cpId}
            </if>
            <if test="req.originalCountryId != null">
                and s.original_country_id =#{req.originalCountryId}
            </if>
            <if test="req.actorName != null and req.actorName != ''">
                and (FIND_IN_SET(#{req.actorName},REPLACE(REPLACE(s.kpeople,' ',','),'、',',')) or FIND_IN_SET(#{req.actorName},REPLACE(REPLACE(s.director,' ',','),'、',',')))
            </if>
            <if test="req.lastCopyrightDay != null">
                <choose>
                    <when test="req.lastCopyrightDay > 0">
                        and datediff(s.licensing_window_end,now()) between 0 and #{req.lastCopyrightDay}
                    </when>
                    <otherwise>
                        and datediff(s.licensing_window_end,now()) &lt; #{req.lastCopyrightDay}
                    </otherwise>
                </choose>
            </if>
            <if test="req.authorizationEndTime != null and req.authorizationEndTime != ''">
                and DATE_FORMAT(s.licensing_window_end, '%Y-%m-%d') =  #{req.authorizationEndTime}
            </if>
            <if test="cpIdSet != null and !cpIdSet.isEmpty()">
                AND s.cp_id IN
                <foreach item="cpId" collection="cpIdSet" open="(" separator="," close=")">
                    #{cpId}
                </foreach>
            </if>
        </trim>
        ORDER BY
        s.licensing_window_end ASC
        )
        UNION ALL
        (select
        p.id,
        p.code,
        p.name,
        1 as content_type,
        p.cp_id,
        p.cp_name,
        p.content_provider,
        p.director,
        p.kpeople,
        p.approval,
        p.copy_right,
        p.publisher,
        null as volumn_count,
        p.licensing_window_start,
        p.licensing_window_end,
        datediff(p.licensing_window_end,now()) as displayAsLastChance,
        p.pgm_category_id,
        p.pgm_category,
        p.release_year,
        p.original_country_id,
        p.original_country,
        p.create_time,
        p.update_time
        from cms_program p
        <trim prefix="where" prefixOverrides="and|or">
            <if test="names != null and names.size() >0">
                <choose>
                    <when test="status == 0">
                        <foreach collection="names" item="item" index="index" open="(" separator="or" close=")">
                            p.name LIKE CONCAT('%',#{item},'%')
                        </foreach>
                    </when>
                    <otherwise>
                        p.name in
                        <foreach item="item" index="index" collection="names"
                                 open="(" separator="," close=")">#{item}
                        </foreach>
                    </otherwise>
                </choose>
            </if>
            and p.series_flag = 0 and p.prohibit_status = 0
            <if test="req.pgmCategoryId != null and req.pgmCategoryId != ''">
                and p.pgm_category_id =#{req.pgmCategoryId}
            </if>
            <if test="req.releaseYear != null and req.releaseYear != ''">
                and p.release_year =#{req.releaseYear}
            </if>
            <if test="req.approval != null and req.approval != ''">
                and p.approval =#{req.approval}
            </if>
            <if test="req.cpId != null and req.cpId != ''">
                and p.cp_id =#{req.cpId}
            </if>
            <if test="req.originalCountryId != null">
                and p.original_country_id =#{req.originalCountryId}
            </if>
            <if test="req.actorName != null and req.actorName != ''">
                and p.id IN (
                SELECT
                id
                FROM
                cms_program
                WHERE
                FIND_IN_SET( #{req.actorName}, REPLACE ( REPLACE ( kpeople, ' ', ',' ) , '、',',') ) UNION
                SELECT
                id
                FROM
                cms_program
                WHERE
                FIND_IN_SET( #{req.actorName}, REPLACE ( REPLACE ( director, ' ', ',' ) , '、',',') )
                )
            </if>
            <if test="req.lastCopyrightDay != null">
                <choose>
                    <when test="req.lastCopyrightDay > 0">
                        and datediff(p.licensing_window_end,now()) between 0 and #{req.lastCopyrightDay}
                    </when>
                    <otherwise>
                        and datediff(p.licensing_window_end,now()) &lt; #{req.lastCopyrightDay}
                    </otherwise>
                </choose>
            </if>
            <if test="req.authorizationEndTime != null and req.authorizationEndTime != ''">
                and DATE_FORMAT(p.licensing_window_end, '%Y-%m-%d') =  #{req.authorizationEndTime}
            </if>
            <if test="cpIdSet != null and !cpIdSet.isEmpty()">
                AND p.cp_id IN
                <foreach item="cpId" collection="cpIdSet" open="(" separator="," close=")">
                    #{cpId}
                </foreach>
            </if>
        </trim>
        ORDER BY
        p.licensing_window_end ASC
        ) LIMIT #{page}, #{size}
    </select>
    <select id="getListSize" resultType="long">
        select SUM(num) as count from(
        select
        count(*) as num
        from cms_program p
        <trim prefix="where" prefixOverrides="and|or">
            <if test="names != null and names.size() >0">
                <choose>
                    <when test="status == 0">
                        <foreach collection="names" item="item" index="index" open="(" separator="or" close=")">
                            p.name LIKE CONCAT('%',#{item},'%')
                        </foreach>
                    </when>
                    <otherwise>
                        p.name in
                        <foreach item="item" index="index" collection="names"
                                 open="(" separator="," close=")">#{item}
                        </foreach>
                    </otherwise>
                </choose>
            </if>
            and p.series_flag = 0 and p.prohibit_status = 0
            <if test="req.pgmCategoryId != null and req.pgmCategoryId != ''">
                and p.pgm_category_id =#{req.pgmCategoryId}
            </if>
            <if test="req.releaseYear != null and req.releaseYear != ''">
                and p.release_year =#{req.releaseYear}
            </if>
            <if test="req.approval != null and req.approval != ''">
                and p.approval =#{req.approval}
            </if>
            <if test="req.cpId != null and req.cpId != ''">
                and p.cp_id =#{req.cpId}
            </if>
            <if test="req.originalCountryId != null">
                and p.original_country_id =#{req.originalCountryId}
            </if>
            <if test="req.actorName != null and req.actorName != ''">
                and p.id IN (
                SELECT
                id
                FROM
                cms_program
                WHERE
                FIND_IN_SET( #{req.actorName}, REPLACE ( REPLACE ( kpeople, ' ', ',' ) , '、',',') ) UNION
                SELECT
                id
                FROM
                cms_program
                WHERE
                FIND_IN_SET( #{req.actorName}, REPLACE ( REPLACE ( director, ' ', ',' ) , '、',',') )
                )
            </if>
            <if test="req.lastCopyrightDay != null">
                <choose>
                    <when test="req.lastCopyrightDay > 0">
                        and datediff(p.licensing_window_end,now()) between 0 and #{req.lastCopyrightDay}
                    </when>
                    <otherwise>
                        and datediff(p.licensing_window_end,now()) &lt; #{req.lastCopyrightDay}
                    </otherwise>
                </choose>
            </if>
            <if test="req.authorizationEndTime != null and req.authorizationEndTime != ''">
                and DATE_FORMAT(p.licensing_window_end, '%Y-%m-%d') = #{req.authorizationEndTime}
            </if>
            <if test="cpIdSet != null and !cpIdSet.isEmpty()">
                AND p.cp_id IN
                <foreach item="cpId" collection="cpIdSet" open="(" separator="," close=")">
                    #{cpId}
                </foreach>
            </if>
        </trim>
        UNION ALL
        select
        count(*) as num
        from cms_series s
        <trim prefix="where" prefixOverrides="and|or">
            <if test="names != null and names.size() >0">
                <choose>
                    <when test="status == 0">
                        <foreach collection="names" item="item" index="index" open="(" separator="or" close=")">
                            s.name LIKE CONCAT('%',#{item},'%')
                        </foreach>
                    </when>
                    <otherwise>
                        s.name in
                        <foreach item="item" index="index" collection="names"
                                 open="(" separator="," close=")">#{item}
                        </foreach>
                    </otherwise>
                </choose>
            </if>
            and s.prohibit_status = 0
            <if test="req.pgmCategoryId != null and req.pgmCategoryId != ''">
                and s.pgm_category_id =#{req.pgmCategoryId}
            </if>
            <if test="req.releaseYear != null and req.releaseYear != ''">
                and s.release_year =#{req.releaseYear}
            </if>
            <if test="req.approval != null and req.approval != ''">
                and s.approval =#{req.approval}
            </if>
            <if test="req.cpId != null and req.cpId != ''">
                and s.cp_id =#{req.cpId}
            </if>
            <if test="req.originalCountryId != null">
                and s.original_country_id =#{req.originalCountryId}
            </if>
            <if test="req.actorName != null and req.actorName != ''">
                and (FIND_IN_SET(#{req.actorName},REPLACE(REPLACE(s.kpeople,' ',','),'、',',')) or FIND_IN_SET(#{req.actorName},REPLACE(REPLACE(s.director,' ',','),'、',',')))
            </if>
            <if test="req.lastCopyrightDay != null">
                <choose>
                    <when test="req.lastCopyrightDay > 0">
                        and datediff(s.licensing_window_end,now()) between 0 and #{req.lastCopyrightDay}
                    </when>
                    <otherwise>
                        and datediff(s.licensing_window_end,now()) &lt; #{req.lastCopyrightDay}
                    </otherwise>
                </choose>
            </if>
            <if test="req.authorizationEndTime != null and req.authorizationEndTime != ''">
                and DATE_FORMAT(s.licensing_window_end, '%Y-%m-%d') = #{req.authorizationEndTime}
            </if>
            <if test="cpIdSet != null and !cpIdSet.isEmpty()">
                AND s.cp_id IN
                <foreach item="cpId" collection="cpIdSet" open="(" separator="," close=")">
                    #{cpId}
                </foreach>
            </if>
        </trim>
        ) t
    </select>
    <select id="getExpireSize" resultType="com.pukka.iptv.common.data.vo.req.CopyrightInfoSizeReq">
        select SUM(t.licensing) as expired , SUM(t.licensing_end) as expire from (
        select
        SUM(
        case when datediff(p.licensing_window_end,now()) &lt; 0
        then 1 else 0 end) as licensing,
        SUM(
        case when datediff(p.licensing_window_end,now()) between 0 and #{day}
        then 1 else 0 end) as licensing_end
        from cms_program p where p.series_flag = 0 and p.prohibit_status = 0
        <if test="cpid != null and cpid != 0">
            and p.cp_id = #{cpid}
        </if>
        union all
        select
        SUM(
        case when datediff(s.licensing_window_end,now()) &lt; 0
        then 1 else 0 end) as licensing,
        SUM(
        case when datediff(s.licensing_window_end,now()) between 0 and #{day}
        then 1 else 0 end) as licensing_end
        from cms_series s where s.prohibit_status = 0
        <if test="cpid != null and cpid != 0">
            and s.cp_id = #{cpid}
        </if>
        ) t
    </select>
    <select id="getProgramIdsByActorName" resultType="Long">
        SELECT id
        FROM cms_program
        WHERE FIND_IN_SET(#{actorname},REPLACE ( REPLACE ( kpeople, ' ', ',' ) , '、',',') )
        UNION
        SELECT id
        FROM cms_program
        WHERE FIND_IN_SET(#{actorname}, REPLACE ( REPLACE ( director, ' ', ',' ) , '、',','))
    </select>
    <select id="getSeriesIdsByActorName" resultType="Long">
        SELECT id
        FROM cms_series
        WHERE FIND_IN_SET(#{actorname},REPLACE ( REPLACE ( kpeople, ' ', ',' ) , '、',',') )
        UNION
        SELECT id
        FROM cms_series
        WHERE FIND_IN_SET(#{actorname}, REPLACE ( REPLACE ( director, ' ', ',' ) , '、',','))
    </select>
</mapper>