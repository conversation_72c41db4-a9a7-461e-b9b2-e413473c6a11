<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">


<!-- 不使用namespace的话sql搜索定位会比较方便 -->
<mapper namespace="com.pukka.iptv.manage.mapper.copyright.copyright.CopyrightDataMapper">

    <resultMap id="BmsPictueFtpMap" type="com.pukka.iptv.common.data.vo.bms.BmsPictueFtpVo">
        <result property="storageId" column="storage_id"/>
        <result property="storageName" column="storage_name"/>
        <result property="pictureHttpPrefix" column="picture_http_prefix"/>
        <result property="innerUrl" column="inner_url"/>
        <result property="folderCode" column="folder_code"/>
        <result property="account" column="account"/>
        <result property="password" column="password"/>
    </resultMap>

    <select id="getStorageFtpUrl" resultMap="BmsPictueFtpMap">
        SELECT b.id   as storage_id,
               b.name as storage_name,
               b.picture_http_prefix,
               b.inner_url,
               a.code as folder_code,
               c.account,
               c.`password`
        FROM sys_cp a
                 LEFT JOIN sys_storage b ON a.storage_id = b.id
                 LEFT JOIN sys_storage_directory c ON b.id = c.storage_id
        where a.id = #{cpId}
          and b.status = 1
          and c.type = 3
          and c.authority_type = 2
          and b.picture_http_prefix is not null
          and b.inner_url is not null
          and a.name is not null
          and c.account is not null
          and c.`password` is not null LIMIT 1
    </select>
</mapper>

