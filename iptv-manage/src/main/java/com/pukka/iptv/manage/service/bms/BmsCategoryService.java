package com.pukka.iptv.manage.service.bms;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.pukka.iptv.common.base.enums.ActionEnums;
import com.pukka.iptv.common.base.vo.CommonResponse;
import com.pukka.iptv.common.data.model.OutParamExpand;
import com.pukka.iptv.common.data.model.bms.BmsCategory;
import com.pukka.iptv.common.data.model.order.SubOrderObjectsEntity;
import com.pukka.iptv.common.data.util.Tuple2;
import com.pukka.iptv.common.data.vo.bms.BmsBelongCategorySpVO;
import com.pukka.iptv.common.data.vo.bms.BmsCategoryVO;
import com.pukka.iptv.common.data.vo.req.BmsCategoryCommonReq;
import com.pukka.iptv.common.data.vo.req.BmsCategoryOperationReq;
import com.pukka.iptv.common.data.vo.req.BmsCategoryQueryReq;
import java.util.Collection;
import java.util.List;
import java.util.Map;

/**
 * 栏目
 *
 * <AUTHOR>
 * @date 2021-08-27 10:20:58
 */
public interface BmsCategoryService extends IService<BmsCategory>, PriorityPublishApi,
    BmsPublishParamApi {

  boolean updatePublishStatus(List<SubOrderObjectsEntity> orderObjectsEntities,
      List<Long> spIdList);

  // 栏目解锁/锁定
  boolean lock(BmsCategoryOperationReq bmsCategoryOperationReq);

  // 栏目分页查询
  IPage<BmsCategory> listByParentId(Page<BmsCategory> page, BmsCategoryQueryReq queryReq);

  // 栏目多选排序
  boolean sort(List<BmsCategoryVO> categoryList);

  // 栏目新增
  boolean saveCategory(BmsCategoryCommonReq commonReq);

  // 栏目修改
  boolean updateCategory(BmsCategoryCommonReq commonReq);

  // 图片保存
  void savePicture(BmsCategoryCommonReq commonReq, Long contentId);

  // 栏目删除
  boolean delete(List<Long> idList);

  // 栏目发布
  boolean publish(List<Long> idList, Map<String, OutParamExpand> paramMap);

  // 栏目回收
  boolean recycle(List<Long> idList);

  // 栏目设置生效/失效
  boolean status(BmsCategoryOperationReq bmsCategoryOperationReq);

  // 栏目重置发布状态
  boolean resetPublishStatus(List<Long> idList);

  // 栏目修改发布状态
  boolean updatePublishStatus(BmsCategoryOperationReq bmsCategoryOperationReq);

  BmsCategory getExtracodeById(Long id);

  /**
   * 根据栏目id获取到所有列表
   *
   * @param ids
   * @return
   * <AUTHOR>
   */
  Tuple2<Boolean, List<BmsCategory>> getLockStatus(String[] ids);

  //修改栏目的发布状态
  boolean modifyPublishStatus(List<Long> ids, Integer publishStatus);

  BmsCategoryVO findCategoryById(Long id);

  // 下发工单
  boolean sendCategoryOrder(ActionEnums actionEnums, List<Long> categories, Long spId,
      String spName, Map<String, OutParamExpand> paramMap);

  // 查询所有间接父栏目
  Collection<BmsBelongCategorySpVO> getBelongCategory(List<Long> categoryIds, List<Long> spIds);

  // 查询栏目树
  Collection<BmsBelongCategorySpVO> getCategoryTree(List<Long> spIds);


  boolean deleteByCodeAndSp(List<String> delCategoryCodes, List<Long> spIdList, boolean isRollbak);

  boolean autoFeedBackCategoryDel(List<String> codeList, List<Long> spIdList);

  CommonResponse checkCategoryName(Long categoryId, String name, Long spId, Long parentId);

  List<BmsCategory> getByCode(String code, Long spId, Boolean isExtraCode);
}

