package com.pukka.iptv.manage.virtual;

import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationContext;
import org.springframework.stereotype.Component;

@Slf4j
@Component
public class VirtualStrategyManager implements IVirtualStrategyManager{
    @Autowired
    private ApplicationContext applicationContext;

    @Override
    public IVirtualLogic createVirtualStrategy(String beanPrefix) {
        String builderStrategyBeanName = getVirtualStrategyBeanName(beanPrefix);
        if (applicationContext.containsBean(builderStrategyBeanName)) {
            return applicationContext.getBean(builderStrategyBeanName, IVirtualLogic.class);
        }
        builderStrategyBeanName = getVirtualStrategyBeanName();
        if (applicationContext.containsBean(builderStrategyBeanName)) {
            return applicationContext.getBean(builderStrategyBeanName, IVirtualLogic.class);
        }
        log.error("未找到对应的xml生成处理类，beanPrefix:{}!", beanPrefix);
        return null;
    }
}
