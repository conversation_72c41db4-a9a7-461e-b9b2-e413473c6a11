package com.pukka.iptv.manage.listener.mq;

import cn.hutool.core.collection.CollUtil;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.pukka.iptv.common.base.constant.ObjectsTypeConstants;
import com.pukka.iptv.common.base.enums.*;
import com.pukka.iptv.common.data.dto.PublishParamsDto;
import com.pukka.iptv.common.data.model.OutOrderBase;
import com.pukka.iptv.common.data.model.bms.*;
import com.pukka.iptv.common.data.model.order.SubOrderMappingsEntity;
import com.pukka.iptv.common.data.model.order.SubOrderObjectsEntity;
import com.pukka.iptv.common.data.model.order.SubOrderXmlEntity;
import com.pukka.iptv.common.rabbitmq.config.FeedbackMQConfig;
import com.pukka.iptv.common.rabbitmq.config.StatisticsOnlineMQConfig;
import com.pukka.iptv.common.rabbitmq.constans.FeedbackConstant;
import com.pukka.iptv.manage.mapper.bms.BmsCategoryContentMapper;
import com.pukka.iptv.manage.mapper.bms.BmsContentMapper;
import com.pukka.iptv.manage.mapper.bms.BmsPackageContentMapper;
import com.pukka.iptv.manage.mapper.bms.BmsProgramMapper;
import com.pukka.iptv.manage.service.sys.AutoPublishService;
import com.pukka.iptv.manage.service.sys.OutOrderBaseService;
import com.rabbitmq.client.Channel;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.amqp.core.ExchangeTypes;
import org.springframework.amqp.rabbit.annotation.Exchange;
import org.springframework.amqp.rabbit.annotation.Queue;
import org.springframework.amqp.rabbit.annotation.QueueBinding;
import org.springframework.amqp.rabbit.annotation.RabbitListener;
import org.springframework.amqp.rabbit.core.RabbitTemplate;
import org.springframework.amqp.support.AmqpHeaders;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.messaging.handler.annotation.Header;
import org.springframework.messaging.handler.annotation.Payload;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.stream.Collectors;

/**
 * @Author: liaowj
 * @Description: 自动发布 上报反馈 mq消费监听器
 * @CreateDate: 2022/1/17 14:24
 * @Version: 1.0
 */
@Component
@Slf4j
public class AutoPublishFeedbackMQListener {

    @Autowired
    private AutoPublishService autoPublishService;
    @Autowired
    private OutOrderBaseService outOrderBaseService;
    @Autowired
    private BmsContentMapper bmsContentMapper;
    @Autowired
    private BmsProgramMapper bmsProgramMapper;
    @Autowired
    private BmsCategoryContentMapper bmsCategoryContentMapper;
    @Autowired
    private BmsPackageContentMapper bmsPackageContentMapper;
    @Autowired
    private RabbitTemplate rabbitTemplate;

    @RabbitListener(bindings = {@QueueBinding(
            value = @Queue(value = FeedbackConstant.FEEDBACK_AUTO_PUBLISH_QUEUE),
            exchange = @Exchange(value = FeedbackConstant.FEEDBACK_EXCHANGE, type = ExchangeTypes.TOPIC),
            key = FeedbackMQConfig.FEEDBACK_AUTO_PUBLISH_ROUTING)},
            containerFactory = "rabbitListenerContainerFactory"
    )
    public void recieved(@Payload PublishParamsDto params, Channel channel, @Header(AmqpHeaders.DELIVERY_TAG) Long deliveryTag) throws Exception {
        log.info("自动发布 上报反馈接口 接收 到参数：{}", params);
        try {
            //判断主工单是否存在
            if (StringUtils.isNotEmpty(params.getBaseOrderId())) {
                //更新工单结果表上报状态为上报成功
                outOrderBaseService.update(
                        Wrappers.lambdaUpdate(OutOrderBase.class)
                                .set(OutOrderBase::getReportStatus, ReportStatusEnum.SuccessReport.getValue())
                                .eq(OutOrderBase::getId, params.getBaseOrderId())
                );
            }
            log.info("自动发布 上报反馈接口 更新工单结果表上报状态为上报成功 参数：{}", params);
            HashMap<Integer, List<Long>> map = new HashMap<>();
            if (Boolean.TRUE.equals(params.getIsFinish())) {
                map = getBmsId(params);
            }
            autoPublishService.dealContent(params.getCorrelateId(), params.getSpIds(), params.getResult(), params.getIsFinish());
            if (Boolean.TRUE.equals(params.getIsFinish())) {
                sendMQ(map);
            }
            log.info("自动发布 上报反馈接口 流程处理结束 correlate ID = {}", params.getCorrelateId());
        } catch (Exception e) {
            log.error("自动发布 上报反馈接口 处理异常，参数：{}, 错误原因：{}", params, e.fillInStackTrace());
        } finally {
            channel.basicAck(deliveryTag, false);
        }
    }

    private HashMap<Integer, List<Long>> getBmsId(PublishParamsDto params) {
        HashMap<Integer, List<Long>> map = new HashMap<>();
        SubOrderXmlEntity orderXmlEntityPublic = autoPublishService.getOrderXmlEntityPublic(params.getCorrelateId());
        if (ObjectUtils.isEmpty(orderXmlEntityPublic)) {
            log.error("自动发布correlateId = {} 的注入工单对象为空！", params.getCorrelateId());
            return map;
        }
        List<SubOrderObjectsEntity> subOrderObjectsEntities = orderXmlEntityPublic.getSubOrderObjectsEntities();
        List<Long> spIds = Arrays.stream(params.getSpIds().split(",")).map(Long::parseLong).collect(Collectors.toList());
        List<SubOrderMappingsEntity> subOrderMappingsEntities = orderXmlEntityPublic.getSubOrderMappingsEntities();
        for (SubOrderObjectsEntity subOrderObjectsEntity : subOrderObjectsEntities) {
            switch (subOrderObjectsEntity.getElementType()) {
                case ObjectsTypeConstants.SERIES:
                    List<BmsContent> bmsContents = bmsContentMapper.selectList(new LambdaQueryWrapper<BmsContent>()
                            .eq(BmsContent::getCmsContentCode, subOrderObjectsEntity.getCode())
                            .in(BmsContent::getSpId, spIds));
                    if (CollUtil.isNotEmpty(bmsContents)) {
                        map.put(ContentTypeEnum.TELEPLAY.getValue(), bmsContents.stream().map(BmsContent::getId).collect(Collectors.toList()));
                    }
                    break;
                case ObjectsTypeConstants.PROGRAM:
                    List<BmsContent> bmsContentPrograms = bmsContentMapper.selectList(new LambdaQueryWrapper<BmsContent>()
                            .eq(BmsContent::getCmsContentCode, subOrderObjectsEntity.getCode())
                            .in(BmsContent::getSpId, spIds));
                    if (CollUtil.isNotEmpty(bmsContentPrograms)) {
                        map.put(ContentTypeEnum.FILM.getValue(), bmsContentPrograms.stream().map(BmsContent::getId).collect(Collectors.toList()));
                    }
                    List<BmsProgram> bmsPrograms = bmsProgramMapper.selectList(new LambdaQueryWrapper<BmsProgram>()
                            .eq(BmsProgram::getCmsContentCode, subOrderObjectsEntity.getCode())
                            .in(BmsProgram::getSpId, spIds));
                    if (CollUtil.isNotEmpty(bmsPrograms)) {
                        map.put(ContentTypeEnum.SUBSET.getValue(), bmsPrograms.stream().map(BmsProgram::getId).collect(Collectors.toList()));
                    }
                    break;
                default:
                    log.info("不支持的对象类型 ：{}", subOrderObjectsEntity.getElementType());
                    break;
            }
        }
        for (SubOrderMappingsEntity subOrderMappingsEntity : subOrderMappingsEntities) {
            String elementType = subOrderMappingsEntity.getElementType();
            String parentType = subOrderMappingsEntity.getParentType();
            switch (parentType) {
                case ObjectsTypeConstants.PACKAGE:
                    List<BmsPackageContent> bmsPackageContents = bmsPackageContentMapper.selectList(new LambdaQueryWrapper<BmsPackageContent>()
                            .eq(BmsPackageContent::getCmsContentCode, subOrderMappingsEntity.getElementCode())
                            .eq(BmsPackageContent::getPackageCode, subOrderMappingsEntity.getParentCode())
                            .in(BmsPackageContent::getSpId, spIds));
                    if (CollUtil.isNotEmpty(bmsPackageContents)) {
                        if (ObjectsTypeConstants.SERIES.equals(elementType)) {
                            map.put(ContentTypeEnum.PACKAGE_SERIES.getValue(), bmsPackageContents.stream().map(BmsPackageContent::getId).collect(Collectors.toList()));
                        } else if (ObjectsTypeConstants.PROGRAM.equals(elementType)) {
                            map.put(ContentTypeEnum.PACKAGE_PROGRAM.getValue(), bmsPackageContents.stream().map(BmsPackageContent::getId).collect(Collectors.toList()));
                        }
                    }
                    break;
                case ObjectsTypeConstants.CATEGORY:
                    List<BmsCategoryContent> bmsCategoryContents = bmsCategoryContentMapper.selectList(new LambdaQueryWrapper<BmsCategoryContent>()
                            .eq(BmsCategoryContent::getCmsContentCode, subOrderMappingsEntity.getElementCode())
                            .eq(BmsCategoryContent::getCategoryCode, subOrderMappingsEntity.getParentCode())
                            .in(BmsCategoryContent::getSpId, spIds));
                    if (CollUtil.isNotEmpty(bmsCategoryContents)) {
                        if (ObjectsTypeConstants.SERIES.equals(elementType)) {
                            map.put(ContentTypeEnum.CATEGORY_SERIES.getValue(), bmsCategoryContents.stream().map(BmsCategoryContent::getId).collect(Collectors.toList()));
                        } else if (ObjectsTypeConstants.PROGRAM.equals(elementType)) {
                            map.put(ContentTypeEnum.CATEGORY_PROGRAM.getValue(), bmsCategoryContents.stream().map(BmsCategoryContent::getId).collect(Collectors.toList()));
                        }
                    }
                    break;
                default:
                    log.info("不支持的Mapping类型 ParentType = {}， ElementType = {} ", parentType, elementType);
                    break;
            }
        }
        return map;
    }

    private void sendMQ(HashMap<Integer, List<Long>> inMap) {
        log.error("AutoPublishFeedbackMQListener.sendMQ.inMap={}", JSON.toJSONString(inMap));
        if (CollUtil.isEmpty(inMap)) {
            return;
        }
        Set<Map.Entry<Integer, List<Long>>> entries = inMap.entrySet();
        for (Map.Entry<Integer, List<Long>> entry : entries) {
            Integer contentType = entry.getKey();
            List<Long> bmsIds = entry.getValue();
            for (Long bmsId : bmsIds) {
                Map<String, Object> map = new HashMap<>();
                if (ContentTypeEnum.FILM.getValue().equals(contentType) || ContentTypeEnum.TELEPLAY.getValue().equals(contentType)
                        || ContentTypeEnum.EPISODES.getValue().equals(contentType)) {
                    BmsContent bmsContent = bmsContentMapper.selectById(bmsId);
                    if (null == bmsContent) {
                        bmsContent = new BmsContent();
                        bmsContent.setId(bmsId);
                        map.put(StatisticsMediaMQEnum.BMS_CONTENT_RECYCLE.getValue(), bmsContent);
                    } else {
                        if (Boolean.FALSE.equals(verifyPublishStatus(bmsContent.getPublishStatus()))) {
                            return;
                        }
                        map.put(StatisticsMediaMQEnum.BMS_CONTENT.getValue(), bmsContent);
                    }
                } else if (ContentTypeEnum.SUBSET.getValue().equals(contentType)) {
                    BmsProgram bmsProgram = bmsProgramMapper.selectById(bmsId);
                    if (null == bmsProgram) {
                        bmsProgram = new BmsProgram();
                        bmsProgram.setId(bmsId);
                        map.put(StatisticsMediaMQEnum.BMS_PROGRAM_RECYCLE.getValue(), bmsProgram);
                    } else {
                        if (Boolean.FALSE.equals(verifyPublishStatus(bmsProgram.getPublishStatus()))) {
                            return;
                        }
                        map.put(StatisticsMediaMQEnum.BMS_PROGRAM.getValue(), bmsProgram);
                    }
                } else if (ContentTypeEnum.CATEGORY_PROGRAM.getValue().equals(contentType) || ContentTypeEnum.CATEGORY_SERIES.getValue().equals(contentType)) {
                    BmsCategoryContent bmsCategoryContent = bmsCategoryContentMapper.selectById(bmsId);
                    if (null == bmsCategoryContent) {
                        bmsCategoryContent = new BmsCategoryContent();
                        bmsCategoryContent.setId(bmsId);
                        map.put(StatisticsMediaMQEnum.BMS_CATEGORY_CONTENT_RECYCLE.getValue(), bmsCategoryContent);
                    } else {
                        if (Boolean.FALSE.equals(verifyPublishStatus(bmsCategoryContent.getPublishStatus()))) {
                            return;
                        }
                        map.put(StatisticsMediaMQEnum.BMS_CATEGORY_CONTENT.getValue(), bmsCategoryContent);
                    }
                } else if (ContentTypeEnum.PACKAGE_PROGRAM.getValue().equals(contentType) || ContentTypeEnum.PACKAGE_SERIES.getValue().equals(contentType)) {
                    BmsPackageContent bmsPackageContent = bmsPackageContentMapper.selectById(bmsId);
                    if (null == bmsPackageContent) {
                        bmsPackageContent = new BmsPackageContent();
                        bmsPackageContent.setId(bmsId);
                        map.put(StatisticsMediaMQEnum.BMS_PACKAGE_CONTENT_RECYCLE.getValue(), bmsPackageContent);
                    } else {
                        if (Boolean.FALSE.equals(verifyPublishStatus(bmsPackageContent.getPublishStatus()))) {
                            return;
                        }
                        map.put(StatisticsMediaMQEnum.BMS_PACKAGE_CONTENT.getValue(), bmsPackageContent);
                    }
                } else {
                    log.error("错误的消息类型。entry={}", JSON.toJSONString(entry));
                    return;
                }
                log.error("AutoPublishFeedbackMQListener.sendMQ.map={}", JSON.toJSONString(map));
                this.rabbitTemplate.convertAndSend(StatisticsOnlineMQConfig.STATISTIC_ONLINE_EXCHANGE, StatisticsOnlineMQConfig.STATISTIC_ONLINE_ROUTING, map);
            }
        }
    }

    private Boolean verifyPublishStatus(Integer publishStatus) {
        return PublishStatusEnum.PUBLISH.getCode().equals(publishStatus) || PublishStatusEnum.FAILPUBLISH.getCode().equals(publishStatus) ||
                PublishStatusEnum.ROLLBACK.getCode().equals(publishStatus) || PublishStatusEnum.FAILROLLBACK.getCode().equals(publishStatus);
    }

}
