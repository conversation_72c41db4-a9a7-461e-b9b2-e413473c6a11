package com.pukka.iptv.manage.service.sys;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.pukka.iptv.common.data.model.OutOrderBase;
import com.pukka.iptv.common.data.model.OutOrderItem;
import com.pukka.iptv.common.data.vo.sys.OutOrderBaseRepublish;
import com.pukka.iptv.common.data.vo.sys.OutOrderBaseVo;
import com.pukka.iptv.common.data.vo.sys.OutOrderItemVo;

import java.util.List;

/**
 * <AUTHOR>
 * @Date 2021/9/9 10:19 上午
 * @description: 主工单
 * @Version 1.0
 */
public interface OutOrderBaseService extends IService<OutOrderBase> {
    /**
     * 主工单重新发布
     * @param outOrderBaseRepublish
     * @return
     */
    boolean orderRepublish(OutOrderBaseRepublish outOrderBaseRepublish);
    /**
     * 主工单信息查询
     * @param contentId 内容id
     * @param contentType 内容类型
     * @return
     */
    List<OutOrderBase> getOrderBaseInfo(String contentId, String contentType);

    /**
     * 主工单分页查询
     * @param page
     * @param outOrderBaseVo
     * @return
     */
    IPage<OutOrderBase> listByOutOrderBaseProperty(Page<OutOrderBase> page, OutOrderBaseVo outOrderBaseVo);

    /**
     * 主工单分页查询
     * @param page
     * @param outOrderBase
     * @return
     */
    IPage<OutOrderBase> page(Page<OutOrderBase> page, OutOrderBase outOrderBase);

    /**
     * 明细查看
     * @param page
     * @param outOrderItem
     * @return
     */
    IPage<OutOrderItemVo> getOrderItemInfo(IPage<OutOrderItem> page, OutOrderItem outOrderItem);

    /**
     * 更新主工单状态
     * @param baseOutOrderIds
     * @param status
     * @param result
     * @return
     */
    Boolean updateBaseOutOrderStatus(List<String> baseOutOrderIds, Integer status, Integer result, String statusDscription, String errorDescription);

    /**
     * 根据关联ID查询主任务
     * @param correlateId
     * @return
     */
    List<OutOrderBase> getOrderByCorrelateId(String correlateId);

    /**
     * 查询物理频道id信息
     * @param baseOrderId
     * @return
     */
    List<Long> getPhysicalChannelIds(String baseOrderId);

    /**
     * 查询节目单id信息
     * @param baseOrderId
     * @return
     */
    List<Long> getScheduleIds(String baseOrderId);

    /**
     * 查询子集id信息
     * @param baseOrderId
     * @return
     */
    List<Long> getSubsetIds(String baseOrderId);

    /**
     * 获取主工单信息
     * @param idList
     * @param contentType
     * @param action
     * @return
     */
    List<OutOrderBase> getOrderBaseList(List<Long> idList, String contentType, Integer action);
}
