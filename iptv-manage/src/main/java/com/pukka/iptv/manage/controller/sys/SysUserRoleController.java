package com.pukka.iptv.manage.controller.sys;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.pukka.iptv.common.base.vo.CommonResponse;
import com.pukka.iptv.common.data.model.sys.SysUserRole;
import com.pukka.iptv.common.data.vo.IdList;
import com.pukka.iptv.common.log.annotation.SysLog;
import com.pukka.iptv.common.log.enums.OperateObjectEnum;
import com.pukka.iptv.common.log.enums.OperateTypeEnum;
import com.pukka.iptv.manage.service.sys.SysUserRoleService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.AllArgsConstructor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;

/**
 *
 * @author: zhengcl
 * @date: 2021-8-18 10:37:55
 */

@RestController
@AllArgsConstructor
@RequestMapping(value = "/sysUserRole", produces = MediaType.APPLICATION_JSON_VALUE)
@Api(tags="sysUserRole管理")
public class SysUserRoleController {

    @Autowired
    private SysUserRoleService sysUserRoleService;

    @ApiOperation(value = "分页")
    @GetMapping("/page" )
    public CommonResponse<Page> page(@Valid Page page, SysUserRole sysUserRole) {
        return  CommonResponse.success(sysUserRoleService.page(page, Wrappers.query(sysUserRole)));
    }

    @ApiOperation(value = "详情")
    @GetMapping("/getById")
    public CommonResponse<SysUserRole> getById(@Valid @RequestParam(name = "id", required = true)  Long id) {
        return CommonResponse.success(sysUserRoleService.getById(id));
    }

    @SysLog(objectType = OperateObjectEnum.SYS_USER_ROLE, operateType = OperateTypeEnum.SAVE)
    @ApiOperation(value = "新增")
    @PostMapping("/save")
    public CommonResponse<Boolean> save(@Valid @RequestBody SysUserRole sysUserRole) {
        return  CommonResponse.success(sysUserRoleService.save(sysUserRole));
    }

    @SysLog(objectType = OperateObjectEnum.SYS_USER_ROLE, operateType = OperateTypeEnum.UPDATE)
    @ApiOperation(value = "修改")
    @PutMapping("/update")
    public CommonResponse<Boolean> updateById(@Valid @RequestBody SysUserRole sysUserRole) {
        return CommonResponse.success(sysUserRoleService.updateById(sysUserRole));
    }

    @SysLog(objectType = OperateObjectEnum.SYS_USER_ROLE, operateType = OperateTypeEnum.DELETE)
    @ApiOperation(value = "删除")
    @DeleteMapping("/deleteById" )
    public CommonResponse<Boolean> deleteById(@Valid @RequestParam(name = "id", required = true) Long id) {
        return  CommonResponse.success(sysUserRoleService.removeById(id));
    }

    @SysLog(objectType = OperateObjectEnum.SYS_USER_ROLE, operateType = OperateTypeEnum.DELETE)
    @ApiOperation(value = "批量删除")
    @DeleteMapping("/deleteByIds" )
    public CommonResponse<Boolean> deleteByIds(@Valid @RequestBody IdList idList) {
        return  CommonResponse.success(sysUserRoleService.removeByIds(idList.getIds()));
    }


    @ApiOperation(value = "是否是管理员")
    @GetMapping("/isAdmin")
    public CommonResponse<Boolean> isAdmin(@Valid @RequestParam(name = "userId", required = true) Long userId) {
        return CommonResponse.success(sysUserRoleService.isAdmin(userId));
    }

}
