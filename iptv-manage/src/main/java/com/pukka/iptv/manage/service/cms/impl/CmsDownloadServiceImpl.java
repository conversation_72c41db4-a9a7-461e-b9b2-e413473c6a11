package com.pukka.iptv.manage.service.cms.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.pukka.iptv.common.base.vo.CommonResponse;
import com.pukka.iptv.common.core.enums.DownloadStatusEnum;
import com.pukka.iptv.common.core.util.StringUtils;
import com.pukka.iptv.common.data.dto.CmsDownloadDto;
import com.pukka.iptv.common.data.dto.DateFormatCompletionDto;
import com.pukka.iptv.common.data.model.cms.CmsDownload;
import com.pukka.iptv.manage.mapper.cms.CmsDownloadMapper;
import com.pukka.iptv.manage.service.cache.CacheServiceImpl;
import com.pukka.iptv.manage.service.cms.CmsDownloadService;
import com.pukka.iptv.manage.util.TimeResolutionUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.util.Arrays;
import java.util.List;

/**
 *
 * @author: liuli
 * @date: 2021年10月22日 下午3:56:03
 */

@Service
public class CmsDownloadServiceImpl extends ServiceImpl<CmsDownloadMapper, CmsDownload> implements CmsDownloadService {


    @Override
    public Page pageByCondition(Page page, CmsDownloadDto cmsDownload) {
        Integer status = cmsDownload.getStatus();
        String name = cmsDownload.getName();
        String contentName = cmsDownload.getContentName();
        Long inPassageId = cmsDownload.getInPassageId();
        //开始、结束时间格式化
        DateFormatCompletionDto dateFormatCompletionDto = new DateFormatCompletionDto().setStartTime(cmsDownload.getStartTime()).setEndTime(cmsDownload.getEndTime());
        Boolean aBoolean = TimeResolutionUtil.dateTimeCompare(dateFormatCompletionDto);
        LambdaQueryWrapper<CmsDownload> queryWrapper = Wrappers.lambdaQuery();
        if (status != null) {
            queryWrapper.eq(CmsDownload::getStatus, status);
        }
        //未换行，模糊查询,换行后精确查询
        if(StringUtils.isNotEmpty(name)){
            name = cmsDownload.getName().trim();
            String[] nameList = name.split("\n");
            for (int i = 0; i < nameList.length ; i++) {
                nameList[i] = nameList[i].trim();
            }
            if(nameList.length > 1){
                queryWrapper.in(CmsDownload::getName, Arrays.asList(nameList));
            } else {
                queryWrapper.like(CmsDownload::getName,nameList[0]);
            }
        }
        if (StringUtils.isNotEmpty(contentName)) {
            queryWrapper.like(CmsDownload::getContentName, contentName);
        }
        if (inPassageId != null) {
            queryWrapper.eq(CmsDownload::getInPassageId, inPassageId);
        }
        if (StringUtils.isNotEmpty(dateFormatCompletionDto.getEndTime())) {
            queryWrapper.apply("create_time <= {0}", dateFormatCompletionDto.getEndTime());
        }
        if (StringUtils.isNotEmpty(dateFormatCompletionDto.getStartTime())) {
            queryWrapper.apply("create_time >= {0}", dateFormatCompletionDto.getStartTime());
        }
        queryWrapper.orderByDesc(CmsDownload::getCreateTime);
        return page(page, queryWrapper);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public CommonResponse updatePriorityByIds(List<Long> idList, Integer priority) {
        if (idList.size() == 0) {
            return CommonResponse.commonfail("id为空");
        }
        List<CmsDownload> dbDataList = listByIds(idList);
        if (priority < 0 || priority > 10 ) {
            return CommonResponse.commonfail("优先级错误 0 - 10");
        }
        for (CmsDownload cmsDownload : dbDataList) {
            if (DownloadStatusEnum.DOWNLOAD_WAIT.getCode().equals(cmsDownload.getStatus())) {
                cmsDownload.setPriority(priority);
            } else {
                return CommonResponse.commonfail("只能设置下载状态为待下载的任务优先级");
            }
        }
        updateBatchById(dbDataList);
        return CommonResponse.success("设置成功");
    }

    @Override
    public CommonResponse updateStatusByIds(List<Long> idList) {
        List<CmsDownload> dbDataList = listByIds(idList);
        for (CmsDownload cmsDownload : dbDataList) {
            if (DownloadStatusEnum.DOWNLOAD_FAILURE.getCode().equals(cmsDownload.getStatus()) ||
                    DownloadStatusEnum.DOWNLOAD_ADD_FAIL.getCode().equals(cmsDownload.getStatus()) ||
                    DownloadStatusEnum.DOWNLOAD_DOWN_UP_FAIL.getCode().equals(cmsDownload.getStatus()) ) {
                cmsDownload.setStatus(DownloadStatusEnum.DOWNLOAD_WAIT.getCode());
            } else {
                return CommonResponse.commonfail("只能设置下载状态为失败的任务为待下载");
            }
        }
        updateBatchById(dbDataList);
        return CommonResponse.success("设置成功");
    }

    /**
     * 结束下载任务，将任务状态修改为“已取消”
     * @param idList
     * @return
     */
    @Override
    public CommonResponse updateStatusToCancelledByIds(List<Long> idList) {
        if (CollectionUtils.isEmpty(idList)) {
            return CommonResponse.commonfail("任务ID列表不能为空");
        }
        List<CmsDownload> dbDataList = listByIds(idList);
        for (CmsDownload cmsDownload : dbDataList) {
            if (DownloadStatusEnum.DOWNLOAD_SUCCESS.getCode().equals(cmsDownload.getStatus())) {
                return CommonResponse.commonfail("当前任务状态为下载成功，无法操作");
            } else {
                cmsDownload.setStatus(DownloadStatusEnum.DOWNLOAD_FAILURE.getCode());
            }
        }
        updateBatchById(dbDataList);
        return CommonResponse.success("设置成功");
    }
}


