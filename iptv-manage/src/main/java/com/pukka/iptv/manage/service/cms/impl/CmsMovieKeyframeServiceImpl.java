package com.pukka.iptv.manage.service.cms.impl;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.pukka.iptv.common.base.enums.CommonResponseEnum;
import com.pukka.iptv.common.base.enums.AuthorityTypeEnums;
import com.pukka.iptv.common.base.enums.StorageDirctoryTypeEnum;
import com.pukka.iptv.common.base.exception.CommonResponseException;
import com.pukka.iptv.common.core.enums.AuthTypeEnum;
import com.pukka.iptv.common.core.util.SftpTool;
import com.pukka.iptv.common.core.util.StringUtils;
import com.pukka.iptv.common.core.util.file.FileTypeUtils;
import com.pukka.iptv.common.data.model.sys.SysCp;
import com.pukka.iptv.common.data.model.sys.SysStorage;
import com.pukka.iptv.common.data.model.sys.SysStorageDirctory;
import com.pukka.iptv.common.data.model.cms.CmsMovie;
import com.pukka.iptv.common.data.model.cms.CmsMovieKeyframe;
import com.pukka.iptv.manage.mapper.sys.SysCpMapper;
import com.pukka.iptv.manage.mapper.sys.SysStorageDirctoryMapper;
import com.pukka.iptv.manage.mapper.sys.SysStorageMapper;
import com.pukka.iptv.manage.mapper.cms.CmsMovieKeyframeMapper;
import com.pukka.iptv.manage.mapper.cms.CmsMovieMapper;
import com.pukka.iptv.manage.service.cms.CmsMovieKeyframeService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import java.io.File;
import java.util.List;
import java.util.Objects;

/**
 * 视频关键帧
 */

@Service
public class CmsMovieKeyframeServiceImpl extends ServiceImpl<CmsMovieKeyframeMapper, CmsMovieKeyframe> implements CmsMovieKeyframeService {
    @Autowired
    private CmsMovieMapper cmsMovieMapper;
    @Autowired
    private SysCpMapper sysCpMapper;
    @Autowired
    private SysStorageMapper sysStorageMapper;
    @Autowired
    private SysStorageDirctoryMapper sysStorageDirctoryMapper;

    /**
     *  视频关键帧分页查询
     * @param page
     * @param cmsMovie
     * @return
     */
    @Override
    public IPage<CmsMovieKeyframe> listById(Page page, CmsMovie cmsMovie) {
        CmsMovie movie = cmsMovieMapper.selectOne(Wrappers.lambdaQuery(CmsMovie.class)
                .eq(Objects.nonNull(cmsMovie.getContentId()),CmsMovie::getContentId, cmsMovie.getContentId())
                .eq(Objects.nonNull(cmsMovie.getContentType()),CmsMovie::getContentType,cmsMovie.getContentType())
                .eq(Objects.nonNull(cmsMovie.getType()),CmsMovie::getType,cmsMovie.getType())
        );
        Page pageData = new Page();
        if(movie==null){
            pageData.setSize(0L).setCurrent(1L).setTotal(0L);
            return pageData;
        }
        page.setSize(page.getSize() == -1 ? Integer.MAX_VALUE : page.getSize());
        pageData = this.page(page, Wrappers.lambdaQuery(CmsMovieKeyframe.class)
                .eq(Objects.nonNull(movie.getId()),CmsMovieKeyframe::getMovieId, movie.getId())
                .eq(Objects.nonNull(cmsMovie.getType()),CmsMovieKeyframe::getMovieType, cmsMovie.getType())
                .orderByDesc(CmsMovieKeyframe::getCreateTime)
        );
        return pageData;
    }

    @Override
    public String uploadImg(MultipartFile file,Long cpId){
        String path = System.getProperty("user.dir") + File.separator + "uploadImg" ;
        //创建文件夹
        File savePath = new File(path);
        if(!savePath.exists()){
            savePath.mkdirs();
        }
        //拼接文件全路径并存储
        String fileName = System.currentTimeMillis() + "." + FileTypeUtils.getFileType(file.getOriginalFilename());
        String realPath = path + File.separator + fileName;
        File tempFile = new File(realPath);
        try{
            file.transferTo(tempFile);
        }catch (Exception e){
            throw new CommonResponseException(CommonResponseEnum.CREATE_ERROR.getCode(),"存储临时文件失败");
        }
        //获取cp信息
        SysCp sysCp = sysCpMapper.selectById(cpId);
        if(sysCp == null || sysCp.getId() == null){
            throw new CommonResponseException(CommonResponseEnum.EMPTY.getCode(),"获取sp数据错误");
        }
        //获取sftp存储信息
        SysStorage sysStorage = sysStorageMapper.selectById(sysCp.getStorageId());
        if(sysStorage == null || sysStorage.getId() == null){
            throw new CommonResponseException(CommonResponseEnum.EMPTY.getCode(),"获取存储目录数据错误");
        }
        //获取ftp内网地址
        String host = sysStorage.getInnerUrl();
        if(StringUtils.isEmpty(host) || StringUtils.isEmpty(sysStorage.getPictureHttpPrefix())){
            throw new CommonResponseException(CommonResponseEnum.EMPTY.getCode(),"sftp存储地址为空");
        }
        //获取存储目录账号信息，图片目录和读写权限
        List<SysStorageDirctory> sysStorageDirctoryList = sysStorageDirctoryMapper.selectList(Wrappers.<SysStorageDirctory>query().eq("storage_id",sysStorage.getId())
                .eq("type", StorageDirctoryTypeEnum.IMAGE_FILM.getCode()).eq("authority_type", AuthorityTypeEnums.READ_WRITE.getCode()));
        if(sysStorageDirctoryList == null || sysStorageDirctoryList.size() == 0){
            throw new CommonResponseException(CommonResponseEnum.EMPTY.getCode(),"没有存储目录账号");
        }
        //获取一个账号
        SysStorageDirctory sysStorageDirctory = sysStorageDirctoryList.get(0);
        if(StringUtils.isEmpty(sysStorageDirctory.getAccount()) || StringUtils.isEmpty(sysStorageDirctory.getPassword())){
            throw new CommonResponseException(CommonResponseEnum.EMPTY.getCode(),"存储目录账号账号或密码为空");
        }
        //创建sftp链接
        SftpTool sftpToolTwo = new SftpTool(host, sysStorageDirctory.getAccount(), 22);
        //获取上传地址
        String uploadPath = "http://" + host + File.separator + sysStorage.getPictureHttpPrefix();
        SftpTool.AuthTypeMode authTypeMode = sftpToolTwo.new AuthTypeMode(AuthTypeEnum.PASSWORD.getCode(),sysStorageDirctory.getPassword());
        try{
            sftpToolTwo.upload(uploadPath,realPath,authTypeMode);

            return uploadPath + File.separator + fileName;
        }catch (Exception e){
            throw new CommonResponseException(CommonResponseEnum.CREATE_ERROR.getCode(),"上传文件失败");
        }
    }


}


