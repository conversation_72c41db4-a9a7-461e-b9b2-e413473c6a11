package com.pukka.iptv.manage.controller.bms;

import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.pukka.iptv.common.base.constant.PublishParamTypeConstants;
import com.pukka.iptv.common.base.constant.RedisKeyConstants;
import com.pukka.iptv.common.base.enums.*;
import com.pukka.iptv.common.base.exception.CommonResponseException;
import com.pukka.iptv.common.base.util.JwtTokenUtil;
import com.pukka.iptv.common.base.vo.CommonResponse;
import com.pukka.iptv.common.base.vo.SecurityUser;
import com.pukka.iptv.common.data.model.OutParamExpand;
import com.pukka.iptv.common.data.model.bms.BmsContent;
import com.pukka.iptv.common.data.vo.CodeList;
import com.pukka.iptv.common.data.vo.IdList;
import com.pukka.iptv.common.data.vo.IdVO;
import com.pukka.iptv.common.data.vo.bms.CpFeedbackContentVO;
import com.pukka.iptv.common.data.vo.req.*;
import com.pukka.iptv.common.data.vo.resp.R;
import com.pukka.iptv.common.log.annotation.SysLog;
import com.pukka.iptv.common.log.enums.OperateObjectEnum;
import com.pukka.iptv.common.log.enums.OperateTypeEnum;
import com.pukka.iptv.common.redis.service.RedisService;
import com.pukka.iptv.manage.service.bms.BmsContentService;
import com.pukka.iptv.manage.service.bms.BmsPublishParamApi;
import com.pukka.iptv.manage.service.bms.dto.BmsContentRecycleDto;
import com.pukka.iptv.manage.service.sys.SysAuthorizationService;
import com.pukka.iptv.manage.util.scheduletypeutils.ScheduleTypeFactory;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.AllArgsConstructor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.util.Assert;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.TimeUnit;

/**
 * 内容表，不包含子集
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2021-08-27 10:20:57
 */

@RestController
@AllArgsConstructor
@RequestMapping(value = "/bmsContent", produces = MediaType.APPLICATION_JSON_VALUE)
@Api(tags = "BMS内容管理")
public class BmsContentController implements BmsPublishParamApi {
    @Autowired
    private BmsContentService bmsContentService;

    @Autowired
    private SysAuthorizationService sysAuthorizationService;

    @Autowired
    private RedisService redisService;

    @ApiOperation(value = "分页")
    @PostMapping("/page")
    public CommonResponse page(@Valid @RequestBody BmsContentQueryReq req) {
        req.validSpId();
        return R.page(bmsContentService.pageList(req));
    }

    @ApiOperation(value = "授权媒资里列表分页")
    @PostMapping("/sysAuthorizationPage")
    public CommonResponse sysAuthorizationPage(@Valid @RequestBody BmsContentQueryReq req) {
        return R.page(bmsContentService.pageList(req));
    }

    @ApiOperation(value = "授权媒资里列表分页")
    @PostMapping("/sysAuthorization")
    public CommonResponse sysAuthorization(@Valid @RequestBody BmsContentQueryReq req) {
        Page page = new Page();
        page.setSize(req.getSize());
        page.setCurrent(req.getCurrent());
        //查询页面做查询控制
        SecurityUser securityUser = JwtTokenUtil.getSecurityUser();
        String key = RedisKeyConstants.AUTH_LIMIT_VISIT_TIME + "_" +securityUser.getId();
        if (redisService.hasKey(key)){
            return CommonResponse.general(CommonResponseEnum.BUSY_QUERY,"", "");
        }else {
            redisService.setCacheObject(key, req.getName(), 7L, TimeUnit.SECONDS);
        }
        //处理未授权、已授权、剧集、单集
        if (ObjectUtil.isEmpty(req.getAuthorize()) || ObjectUtil.isEmpty(req.getSeriesFlag())) {
            return R.page(sysAuthorizationService.getUnauthorizedContentAndAuthBySp(page, req));
        } else {
            if (req.getAuthorize().equals(AuthorizeEnum.AUTHORIZE.getCode())) {
                return R.page(bmsContentService.getAuthorizedContentBySp(page, req));
            } else {
                return R.page(sysAuthorizationService.getUnauthorizedContentBySp(page, req));
            }
        }
    }

    @SysLog(objectType = OperateObjectEnum.BMS_CONTENT, operateType = OperateTypeEnum.CONTENT_LOCK_OR_UNLOCK, objectIds = "#req.ids")
    @ApiOperation(value = "解锁/锁定")
    @PutMapping("/lock")
    public CommonResponse<Boolean> lock(@Valid @RequestBody BmsContentModifyReq req) {
        List<Long> ids = req.getIds();
        return R.success(bmsContentService.modifyLockStatus(ids, req.getLockStatus()));
    }

    @SysLog(objectType = OperateObjectEnum.BMS_CONTENT, operateType = OperateTypeEnum.CONTENT_PUBLISH, objectIds = "#req.ids")
    @ApiOperation(value = "内容发布")
    @PutMapping("/publish")
    public CommonResponse<String> publish(@Valid @RequestBody BmsContentPublishReq req) {
        Map<String, OutParamExpand> paramMap = new HashMap<>();
        paramMap.put(PublishParamTypeConstants.PRIORITY, new OutParamExpand().setPriority(PriorityEnums.GENERAL.getValue()));
        boolean result;
        //若是单发剧头
        if (req.getAlonePublish()) {
            result = bmsContentService.seriesPublish(req.getIds(), false, req.getScheduleTime());
            return result ? R.success("发布成功") : R.commonfail("发布失败");
        }
        //若是发布关系
        else if (req.getPublishRelation()) {
            result = bmsContentService.publishRelation(req.getIds(), paramMap);
            return result ? R.success("发布成功") : R.commonfail("发布失败");
        } else {
            //若是普通发布  定时发布
            bmsContentService.publish(req.getIds(), req.getDoSchedule() == 1, req.getScheduleTime(), paramMap);
            return R.success("内容发布成功");
        }
    }

    @SysLog(objectType = OperateObjectEnum.BMS_CONTENT, operateType = OperateTypeEnum.SERIES_CONTENT_PUBLISH, objectIds = "#req.ids")
    @ApiOperation(value = "内容发布（单发剧头）")
    @PutMapping("/seriespublish")
    public CommonResponse<String> seriesPublish(@Valid @RequestBody BmsContentSeriesPublishReq req) {
        bmsContentService.seriesPublish(req.getIds(), false, req.getScheduleTime());
        return R.success("发布成功");
    }

    @ApiOperation(value = "定时发布")
    @PostMapping("/schedulePublish")
    public CommonResponse<Boolean> contentSchedulePublish() {
        try {
            Boolean apply = ScheduleTypeFactory.initMode(ContentTypeEnum.FILM).apply();
            return CommonResponse.success(apply);
        } catch (Exception e) {
            return CommonResponse.fail("单集/剧集定时发布失败");
        }
    }

    @SysLog(objectType = OperateObjectEnum.BMS_CONTENT, operateType = OperateTypeEnum.CONTENT_CANCEL_TIMED_PUBLISH, objectIds = "#id")
    @ApiOperation(value = "取消定时发布")
    @PutMapping("/cancelTimedPublish")
    public CommonResponse<Boolean> cancelTimedPublish(@RequestBody @Valid IdVO idVO) {
        return CommonResponse.success(bmsContentService.cancelTimedPublish(Long.parseLong(idVO.getId())));
    }

    @ApiOperation(value = "详情")
    @GetMapping("/getById")
    public CommonResponse<BmsContent> getById(@Valid @RequestParam(name = "id") Long id) {
        return R.success(bmsContentService.getById(id));
    }

    @SysLog(objectType = OperateObjectEnum.BMS_CONTENT, operateType = OperateTypeEnum.SP_CONTENT_RECYCLE_ALL, objectIds = "#ids")
    @ApiOperation(value = "媒资一键回收")
    @PutMapping("/contentRecycleAll")
    public CommonResponse<Boolean> contentRecycleAll(@RequestBody List<Long> ids) {
        bmsContentService
                .breakFlag(BmsContentRecycleDto.class)
                .setArgs(BmsContentRecycleDto.class, new BmsContentRecycleDto().setBmsContentIds(ids))
                .oneKeyRecycle();
        return R.success(true);
    }

    @SysLog(objectType = OperateObjectEnum.BMS_CONTENT, operateType = OperateTypeEnum.CP_EPISODES_RECYCLE_ALL, objectIds = "#ids")
    @ApiOperation(value = "剧集一键回收(cp侧)")
    @PutMapping("/episodesRecycleAll")
    public CommonResponse<Boolean> episodesRecycleAll(@RequestBody List<Long> ids) {
        //设置一键回收标记
        bmsContentService.triggerCpRecycle()
                .breakFlag(BmsContentRecycleDto.class)
                .setArgs(BmsContentRecycleDto.class, new BmsContentRecycleDto().setBmsContentIds(ids));
        return R.success(bmsContentService.oneKeyRecycle());
    }

    @SysLog(objectType = OperateObjectEnum.BMS_CONTENT, operateType = OperateTypeEnum.CP_MOVIE_RECYCLE_ALL, objectIds = "#ids")
    @ApiOperation(value = "单集一键回收(cp侧)")
    @PutMapping("/movieRecycleAll")
    public CommonResponse<Boolean> movieRecycleAll(@RequestBody List<Long> ids) {
        bmsContentService.triggerCpRecycle()
                .breakFlag(BmsContentRecycleDto.class)
                .setArgs(BmsContentRecycleDto.class, new BmsContentRecycleDto().setBmsContentIds(ids));
        return R.success(bmsContentService.oneKeyRecycle());
    }

    @SysLog(objectType = OperateObjectEnum.BMS_CONTENT, operateType = OperateTypeEnum.CP_BATCH_MOVIE_RECYCLE_ALL, objectIds = "#req.ids")
    @ApiOperation(value = "媒资批量一键回收(cp侧)")
    @PutMapping("/batchMovieRecycleAll")
    public CommonResponse<Boolean> batchMovieRecycleAll(@Valid @RequestBody BmsBatchRecycleAllReq req) {
        boolean result = bmsContentService.batchRecycle(req.getIds(), req.getType());
        return result ? R.success(true) : R.commonfail("回收失败");
    }

    @SysLog(objectType = OperateObjectEnum.BMS_CONTENT, operateType = OperateTypeEnum.CONTENT_UPDATE_PUBLISH_STATUS, objectIds = "#req.ids")
    @ApiOperation(value = "修改发布状态")
    @PutMapping("/updatePublishStatus")
    public CommonResponse updatePublishStatus(@Valid @RequestBody BmsContentModifyReq req) {
        Assert.notNull(req.getPublishStatus(), "发布状态不能为null");
        return R.success(bmsContentService.modifyPublishStatus(req.getIds(), req.getPublishStatus()));
    }

    @SysLog(objectType = OperateObjectEnum.BMS_CONTENT, operateType = OperateTypeEnum.CONTENT_RESET_PUBLISH_STATUS, objectIds = "#ids")
    @ApiOperation(value = "重置发布状态")
    @PutMapping("/resetPublishStatus")
    public CommonResponse resetPublishStatus(@RequestBody List<Long> ids) {
        return R.success(bmsContentService.resetPublishStatus(ids));
    }

    @SysLog(objectType = OperateObjectEnum.BMS_CONTENT, operateType = OperateTypeEnum.CONTENT_ACTIVE_OR_POSITIVE, objectIds = "#vo.idList")
    @ApiOperation(value = "设置有效/失效")
    @PutMapping("/status")
    public CommonResponse<Boolean> resetStatus(@Valid @RequestBody BmsSetStatusReq vo) {
        Map<String, OutParamExpand> paramMap = new HashMap<>();
        paramMap.put(PublishParamTypeConstants.PRIORITY, new OutParamExpand().setPriority(PriorityEnums.GENERAL.getValue()));
        List<Long> idList = vo.getIdList();
        Integer status = vo.getStatus();
        boolean ok = bmsContentService.status(idList, StatusEnum.trans(status));
        if (vo.isNeedPublish()) {
            bmsContentService.publish(idList, true, null, paramMap);
        }
        return ok ? R.success(true) : R.commonfail("设置失败");
    }


    @ApiOperation(value = "获取需要反馈给cp的内容信息")
    @GetMapping("/getCpFeedbackContents")
    CommonResponse<List<CpFeedbackContentVO>> getCpFeedbackContents() {
        return CommonResponse.success(bmsContentService.getCpFeedbackContents());
    }


    @ApiOperation(value = "更新CP反馈结果")
    @PutMapping("/cpFeedbackSuccess")
    CommonResponse<Boolean> cpFeedbackSuccess(@RequestBody IdList idList) {
        return CommonResponse.success(bmsContentService.updateCpFeedback(idList.getIds(), CpFeedbackEnum.FINISHED.getCode()));
    }

    @ApiOperation(value = "更新CP反馈结果")
    @PutMapping("/cpFeedbackFail")
    CommonResponse<Boolean> cpFeedbackFail(@RequestBody IdList idList) {
        return CommonResponse.success(bmsContentService.updateCpFeedback(idList.getIds(), CpFeedbackEnum.FAILED.getCode()));
    }

    @SysLog(objectType = OperateObjectEnum.BMS_CONTENT_EXPROT, operateType = OperateTypeEnum.EXPORT)
    @ApiOperation(value = "编排媒资导出")
    @PostMapping("/bmsContentExport")
    public CommonResponse export(@RequestBody BmsContentQueryReq req) throws Exception {
        Assert.notNull(req.getSpName(), "sp名称不可为空");

        return CommonResponse.success(bmsContentService.export(req));
    }

    /**
     * 查询特定发布状态和内容类型的剧头数据（分页）
     */
    @ApiOperation(value = "查询特定状态和类型的剧头数据")
    @GetMapping("/listContentByStatusAndType")
    public CommonResponse<Page<BmsContent>> listContentByStatusAndType(
            @RequestParam(value = "publishStatusList") List<Integer> publishStatusList,
            @RequestParam(value = "contentTypeList") List<Integer> contentTypeList,
            @RequestParam(value = "pageNo", defaultValue = "1") Integer pageNo,
            @RequestParam(value = "pageSize", defaultValue = "500") Integer pageSize) {

        return R.success(bmsContentService.listContentByStatusAndType(
                publishStatusList, contentTypeList, pageNo, pageSize));
    }

    @ApiOperation(value = "更新剧头信息")
    @PutMapping("/updateById")
    public CommonResponse<Boolean> updateById(@RequestBody BmsContent bmsContent) {
        Assert.notNull(bmsContent.getId() , "id不能为空");
        return R.success(bmsContentService.updateById(bmsContent));
    }

    //@GetMapping("/getByCode")
    //    CommonResponse<BmsContent> getByCode(@Valid @RequestParam(name = "spId", required = true) Long spId,
    //                                         @Valid @RequestParam(name = "seriesCode", required = true) String seriesCode);
    @ApiOperation(value = "根据code查询剧头")
    @GetMapping("/getByCode")
    public CommonResponse<BmsContent> getByCode(@Valid @RequestParam(name = "spId", required = true) Long spId,
                                                @Valid @RequestParam(name = "seriesCode", required = true) String seriesCode) {
        return R.success(bmsContentService.getByCode(spId, seriesCode));
    }

}
