package com.pukka.iptv.manage.service.cms;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.pukka.iptv.common.data.model.*;
import com.pukka.iptv.common.data.model.cms.CmsMovie;
import com.pukka.iptv.common.data.model.cms.CmsMovieCheck;

/**
 * 审核标记
 *
 * @author: zhoul
 * @date: 2021-8-30 10:16:44
 */

public interface CmsMovieCheckService extends IService<CmsMovieCheck> {

    IPage<CmsMovieCheck> listById(Page page, CmsMovie cmsMovie);
}


