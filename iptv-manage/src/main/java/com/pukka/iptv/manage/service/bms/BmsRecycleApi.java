package com.pukka.iptv.manage.service.bms;


import com.baomidou.mybatisplus.extension.service.IService;
import com.pukka.iptv.common.data.model.bms.BmsCategoryContent;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * @Author: wz
 * @Date: 2021/11/26 15:07
 * @Description: 回收接口
 */
public interface BmsRecycleApi<T, V> {
    //一件回收标记
    public static final ThreadLocal<Boolean> ONG_KEY_RECYCLE = new ThreadLocal<>();
    //设置递归的跳出点
    public static final ThreadLocal<String> END_FLAG = new ThreadLocal<>();
    //是否删除数据的标识
    public static final ThreadLocal<Boolean> DELETE_FLAG = new ThreadLocal<>();
    //临时数据（方法入参）
    public final static ThreadLocal<Map<String, Object>> TMP_DATA = new ThreadLocal<>();

    boolean preCheck();

    //找到可以一键回收的内容
    List<V> findCanRecycleList();

    //删除剩余不能回收的内容
    boolean deleteRemain();

    //标记删除
    List<Long> markDelete();

    //一键回收
    boolean oneKeyRecycle();

    default BmsRecycleApi<T, V> breakFlag(Class<T> clazz) {
        END_FLAG.set(clazz.toString());
        return this;
    }

    default boolean isBreak(Class<T> clazz) {
        String s = END_FLAG.get();
        return s != null && s.equalsIgnoreCase(clazz.toString());
    }


    //获取内部定时发布标识
    default boolean isCpRecycle() {
        Boolean flag = ONG_KEY_RECYCLE.get();
        return flag == null ? false : flag;
    }

    //是否是sp侧的回收
    default boolean isSpRecycle() {
        return !isCpRecycle();
    }

    default void clearArgs(Class<T> clazz) {
        Map<String, Object> store = TMP_DATA.get();
        if (store != null) {
            store.remove(clazz.toString());
        }
    }

    default void closeRecycle() {
        Map<String, Object> store = TMP_DATA.get();
        if (store != null) {
            store.clear();
        }
        TMP_DATA.remove();
        ONG_KEY_RECYCLE.remove();
        END_FLAG.remove();
        DELETE_FLAG.remove();
    }

    default void setArgs(String key, Object value) {
        Map<String, Object> store = TMP_DATA.get();
        if (store == null) {
            store = new HashMap<>();
            TMP_DATA.set(store);
        }
        store.put(key, value);
    }

    default <P> BmsRecycleApi<T, V> setArgs(Class<P> key, P value) {
        Map<String, Object> store = TMP_DATA.get();
        if (store == null) {
            store = new HashMap<>();
            TMP_DATA.set(store);
        }
        store.put(key.toString(), value);
        return this;
    }

    default <P> T getArgs(Class<P> key) {
        Map<String, Object> store = TMP_DATA.get();
        if (store == null) return null;
        Object data = store.get(key.toString());
        return data != null ? (T) data : null;
    }

    default T getArgs(String key) {
        Map<String, Object> store = TMP_DATA.get();
        if (store == null) return null;
        Object data = store.get(key);
        return data != null ? (T) data : null;
    }

    //不需要删除授权数据标识
    default BmsRecycleApi<T, V> noDelete() {
        DELETE_FLAG.set(false);
        return this;
    }

    //是否需要删除数据
    default boolean needDelete() {
        Boolean flag = DELETE_FLAG.get();
        return flag == null ? false : flag;
    }

    //设置Cp回收的触发器
    default BmsRecycleApi<T, V> triggerCpRecycle() {
        TMP_DATA.set(new HashMap<>(4));
        //设置一键回收标记
        ONG_KEY_RECYCLE.set(true);
        //默认CP侧一件回收需要删除SP侧内容
        DELETE_FLAG.set(true);
        return this;
    }


}
