package com.pukka.iptv.manage.service.bms.impl;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.nacos.common.utils.StringUtils;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.metadata.TableInfo;
import com.baomidou.mybatisplus.core.metadata.TableInfoHelper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.pukka.iptv.common.base.constant.RedisKeyConstants;
import com.pukka.iptv.common.base.enums.ActionEnums;
import com.pukka.iptv.common.base.enums.ContentTypeEnum;
import com.pukka.iptv.common.base.enums.ContentTypeItemEnum;
import com.pukka.iptv.common.base.enums.StatusEnum;
import com.pukka.iptv.common.base.exception.BizException;
import com.pukka.iptv.common.base.exception.CommonResponseException;
import com.pukka.iptv.common.base.vo.CommonResponse;
import com.pukka.iptv.common.core.constant.SymbolConstant;
import com.pukka.iptv.common.core.util.SafeUtil;
import com.pukka.iptv.common.data.model.OutParamExpand;
import com.pukka.iptv.common.data.model.bms.*;
import com.pukka.iptv.common.data.model.cms.CmsPicture;
import com.pukka.iptv.common.data.model.order.SubOrderMappingsEntity;
import com.pukka.iptv.common.data.model.sys.SysStorage;
import com.pukka.iptv.common.data.util.Tuple2;
import com.pukka.iptv.common.data.vo.bms.BmsPictueFtpVo;
import com.pukka.iptv.common.data.vo.bms.BmsPictureVO;
import com.pukka.iptv.common.data.vo.bms.ResultUrlVo;
import com.pukka.iptv.common.redis.service.RedisService;
import com.pukka.iptv.manage.mapper.bms.BmsPictureMapper;
import com.pukka.iptv.manage.mapper.cms.CmsPictureMapper;
import com.pukka.iptv.manage.mapper.sys.SysCpMapper;
import com.pukka.iptv.manage.service.bms.BmsContentService;
import com.pukka.iptv.manage.service.bms.BmsPictureService;
import com.pukka.iptv.manage.service.bms.common.LambdaTrans;
import com.pukka.iptv.manage.service.bms.common.WorkOrderOperation;
import com.pukka.iptv.manage.service.bms.dto.BmsPicRecycleDto;
import com.pukka.iptv.manage.service.common.FtpAnalysisUtil;
import com.pukka.iptv.manage.service.condition.RuleCondition;
import com.pukka.iptv.manage.service.condition.RuleResult;
import com.pukka.iptv.manage.service.condition.rule.LockStatusRule;
import com.pukka.iptv.manage.service.condition.rule.PublishStatusRule;
import com.pukka.iptv.manage.service.condition.rule.RecycleStatusRule;
import com.pukka.iptv.manage.util.CommonUtils;
import com.pukka.iptv.manage.util.DateTooles;
import com.pukka.iptv.manage.util.RuleUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;
import org.springframework.web.multipart.MultipartFile;

import javax.imageio.ImageIO;
import java.awt.image.BufferedImage;
import java.io.IOException;
import java.util.*;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import static com.pukka.iptv.common.base.enums.ActionEnums.REGIST;
import static com.pukka.iptv.common.base.enums.ActionEnums.UPDATE;
import static com.pukka.iptv.common.base.enums.PublishStatusEnum.*;
import static com.pukka.iptv.manage.service.condition.rule.enums.PublishCheck.PUBLISH_SUCCESS;

/**
 * <AUTHOR>
 * @create: 2021-09-01 14:38
 */
@Service
@Slf4j
public class BmsPictureServiceImpl extends ServiceImpl<BmsPictureMapper, BmsPicture> implements BmsPictureService {
    @Autowired
    private BmsPictureMapper bmsPictureMapper;
    @Autowired
    private SysCpMapper sysCpMapper;
    @Autowired
    private RedisService redisService;

    @Autowired
    private BmsContentService bmsContentService;
    @Autowired
    private WorkOrderOperation workOrderOperation;

    @Autowired
    private CmsPictureMapper cmsPictureMapper;


    @Override
    public List<BmsPicture> getByContentIdContentType(Long contentId, Integer contentType, Long spId) {
        LambdaQueryWrapper<BmsPicture> bmsPictureLambdaQueryWrapper = Wrappers.lambdaQuery(BmsPicture.class)
                .eq(BmsPicture::getBmsContentId, contentId)
                .eq(BmsPicture::getContentType, contentType.equals(ContentTypeEnum.CHANNEL_PHYSICAL.getValue()) ? ContentTypeEnum.CHANNEL.getValue() : contentType)
                .eq(Objects.nonNull(spId), BmsPicture::getSpId, spId);
        return this.list(bmsPictureLambdaQueryWrapper);
    }

    @Override
    public List<BmsPicture> getPicByContentIds(List<Long> contentIds, Integer contentType) {
        if (ObjectUtils.isEmpty(contentIds)) {
            return null;
        }
        LambdaUpdateWrapper<BmsPicture> picWrapper = Wrappers.lambdaUpdate(BmsPicture.class)
                .in(BmsPicture::getBmsContentId, contentIds)
                .eq(BmsPicture::getContentType, contentType);
        return this.list(picWrapper);
    }

    @Override
    public List<Long> getPicIdByContentIds(List<Long> contentIds, Integer contentType) {
        if (ObjectUtils.isEmpty(contentIds)) {
            return new ArrayList<>(0);
        }
        LambdaQueryWrapper<BmsPicture> select =
                Wrappers.lambdaQuery(BmsPicture.class).select(BmsPicture::getId)
                        .in(BmsPicture::getBmsContentId, contentIds)
                        .eq(BmsPicture::getContentType, contentType);
        return getPicIdByWrapper(select);
    }

    @Override
    public List<Long> getPicIdByWrapper(LambdaQueryWrapper<BmsPicture> wrappers) {
        List<Map<String, Object>> maps = this.listMaps(wrappers);
        LambdaTrans<BmsPicture> lt = LambdaTrans.instance(BmsPicture.class);
        return maps.stream().map(item -> lt.trans(item, BmsPicture::getId, Long.class)).collect(Collectors.toList());
    }


    @Override
    public CommonResponse<Boolean> picturePublish(List<Long> ids) {
        List<BmsPicture> bmsPictures = bmsPictureMapper.selectBatchIds(ids);
        if (CollectionUtil.isEmpty(bmsPictures)) return CommonResponse.commonfail("未查询到对应图片");
        bmsPictures.forEach(p -> {
            if (!PublishStatusRule.CAN_PUBLISH_LIST.contains(p.getPublishStatus()))
                throw new CommonResponseException("请检查发布状态，选择可发布的图片进行发布");
        });
        //检查媒资状态 0 发布
        List<BmsPicture> waitUpdateList = bmsPictures.stream().filter(
                        (BmsPicture s) -> PublishStatusRule.CAN_UPDATE_LIST.contains(s.getPublishStatus()))
                .collect(Collectors.toList());
        List<BmsPicture> publishList = bmsPictures.stream().filter(
                        (BmsPicture s) -> PublishStatusRule.PUBLISH_LIST.contains(s.getPublishStatus()))
                .collect(Collectors.toList());
        if (waitUpdateList.size() > 0) {
            checkAndPublishMedia(waitUpdateList, UPDATE);
        }
        if (publishList.size() > 0) {
            checkAndPublishMedia(publishList, REGIST);
        }
        return CommonResponse.success(true);
    }

    @Override
    public CommonResponse<Boolean> pictureRollback(List<Long> ids) {
        List<BmsPicture> bmsPictures = bmsPictureMapper.selectBatchIds(ids);
        if (CollectionUtil.isEmpty(bmsPictures)) return CommonResponse.commonfail("未查询到对应图片");
        bmsPictures.forEach(p -> {
            if (RecycleStatusRule.NOT_RECYCLE_LIST.contains(p.getPublishStatus()))
                throw new CommonResponseException("请检查发布状态，选择可回收的图片进行回收");
        });
        //检查媒资状态 1 回收
        checkAndPublishMedia(bmsPictures, ActionEnums.DELETE);
        return CommonResponse.success(true);
    }

    //修改图片的发布状态
    @Override
    public boolean modifyPublishStatus(List<Long> ids, Integer publishStatus) {
        if (CollectionUtils.isEmpty(ids))
            return true;
        List<BmsPicture> bmsPictures = bmsPictureMapper.selectBatchIds(ids);
        checkLock(bmsPictures);
//        RuleResult rr = RuleCondition.create()
//                //检查 图片是否有 发布中的
//                .and(PublishStatusRule.init(BmsPicture.class).data(ids)
//                        .col(BmsPicture::getBmsContentId).policy(ING)).execute();
//        if (!rr.isPass()) {
//            throw new BizException("发布状态为更新中、发布中、回收中的图片不可发布，请重选");
//        }
        //指定的图片都更新成指定发布状态
        LambdaUpdateWrapper<BmsPicture> updateWrapper = Wrappers.lambdaUpdate(BmsPicture.class)
                .in(BmsPicture::getId, ids)
                .set(BmsPicture::getPublishDescription, "")
                .set(BmsPicture::getPublishStatus, publishStatus);
        return this.update(updateWrapper);
    }

    public void checkLock(List<BmsPicture> bmsPictures) {
        if (!ContentTypeEnum.CHANNEL.getValue().equals(bmsPictures.get(0).getContentType())) {
            List<Long> ids = bmsPictures.stream().map(BmsPicture::getBmsContentId).distinct().collect(Collectors.toList());
            Integer contentType = bmsPictures.get(0).getContentType();
            switch (Objects.requireNonNull(ContentTypeEnum.getByValue(contentType))) {
                //当为栏目是则查询bms_category
                case CATEGORY:
                    RuleCondition.create()
                            .and(LockStatusRule.init(BmsCategory.class).data(ids))
                            .execute().check();
                    break;
                //其他类型则为bms_content
                case SUBSET:
                    List<Long> bmscontentIds = bmsContentService.getcontentIdsByProgramIds(ids);
                    RuleCondition.create()
                            .and(LockStatusRule.init(BmsContent.class).data(bmscontentIds))
                            .execute().check();
                    break;
                default:
                    RuleCondition.create()
                            .and(LockStatusRule.init(BmsContent.class).data(ids))
                            .execute().check();
                    break;
            }
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean resetPublishStatus(List<Long> ids) {
        if (ObjectUtil.isEmpty(ids))
            return true;
        //step2.1查询对应发布状态
        List<BmsPicture> bmsPictures = bmsPictureMapper.selectBatchIds(ids);
        List<Integer> statusList = Arrays.asList(PUBLISHING.getCode(), UPDATING.getCode(), ROLLBACKING.getCode());
        bmsPictures.forEach(b -> {
                    if (!statusList.contains(b.getPublishStatus())) {
                        throw new BizException("请检查发布状态，选择发布中、更新中、回收中的图片重置发布状态");
                    }
                }
        );
        checkLock(bmsPictures);
        LambdaQueryWrapper<BmsPicture> wrapper =
                Wrappers.lambdaQuery(BmsPicture.class).select(BmsPicture::getPublishStatus, BmsPicture::getId).in(BmsPicture::getId, ids);
        List<Map<String, Object>> list = this.listMaps(wrapper);
        if (CollectionUtils.isEmpty(list))
            return true;
        LambdaTrans<BmsPicture> lt = LambdaTrans.instance(BmsPicture.class);
        List<BmsPicture> updateList = list.stream().map(item ->
        {
            Long id = lt.trans(item, BmsPicture::getId, Long.class);
            Integer status = lt.trans(item, BmsPicture::getPublishStatus, Integer.class);
            return updatePublishTrans(id, status);
        }).collect(Collectors.toList());
        // List<BmsContent> list = this.list(wrapper);
        // List<BmsContent> updateList = list.stream().map(item -> updatePublishTrans(item, )).collect(Collectors.toList());

        //step2.2更新对应的发布状态
        return this.updateBatchById(updateList);
    }

    private BmsPicture updatePublishTrans(Long id, Integer publishStatus) {
        BmsPicture bmsContent = new BmsPicture();
        bmsContent.setId(id);
        bmsContent.setPublishStatus(CommonUtils.resetPublishStatus(publishStatus));
        bmsContent.setPublishDescription("");
        return bmsContent;
    }

    public String getFolder(BmsPictueFtpVo pictueFtpUrl) {
        Date date = new Date();
        return "pic/" + pictueFtpUrl.getFolderCode() + "/" + DateTooles.getYear(date) + "/" + DateTooles.getMonth(date) + "/" + DateTooles.getDay(date) + "/";
    }

    @Override
    public ResultUrlVo upload(MultipartFile file, Long cpId, Long spId) throws IOException {
        BmsPictueFtpVo pictueFtpUrl;
        pictueFtpUrl = sysCpMapper.getPictueFtpUrl(cpId, spId);
        if (pictueFtpUrl == null) {
            throw new CommonResponseException("未查询到ftp服务器");
        }
        // 后缀名
        String suffixName = file.getOriginalFilename().substring(file.getOriginalFilename().lastIndexOf("."));
        String url = "ftp://" + pictueFtpUrl.getAccount() + ":" + pictueFtpUrl.getPassword() + "@" + pictueFtpUrl.getInnerUrl() + "/";
        String fileName = getFolder(pictueFtpUrl) + UUID.randomUUID().toString().replace("-", "") + suffixName;
        String s = "";
        try {
            s = SafeUtil.pictureUpByFtp(file, fileName, url);
        } catch (IOException e) {
            e.printStackTrace();
        }
        if (StringUtils.isEmpty(s)) {
            throw new CommonResponseException("图片上传异常");
        }
        String httpUrl = pictueFtpUrl.getPictureHttpPrefix() + fileName;
        ResultUrlVo resultUrlVo = new ResultUrlVo();
        resultUrlVo.setStorageId(pictueFtpUrl.getStorageId());
        resultUrlVo.setStorageName(pictueFtpUrl.getStorageName());
        resultUrlVo.setFtpUrl(s);
        resultUrlVo.setHttpUrl(httpUrl);
        return resultUrlVo;
    }


    /**
     * 过滤不正常状态媒资
     *
     * @param bmsPictures
     * @param action      1为回收  0为发布
     * @return
     */
    private boolean checkAndPublishMedia(List<BmsPicture> bmsPictures, ActionEnums action) {
        List<Long> ids = bmsPictures.stream().map(BmsPicture::getBmsContentId)
                .collect(Collectors.toList()).stream().distinct().collect(Collectors.toList());
        List<Long> pcIds = bmsPictures.stream().map(BmsPicture::getId)
                .collect(Collectors.toList()).stream().distinct().collect(Collectors.toList());
        //单独的图片发布时，媒资类型是相同的 获取到第一个的图片媒资类型即可
        Integer contentType = bmsPictures.get(0).getContentType();
        switch (Objects.requireNonNull(ContentTypeEnum.getByValue(contentType))) {
            //当为子集时则代表是bms_program
            case SUBSET:
                checkPublishAndLockStatus(ids, BmsProgram.class, action);
                break;
            //当为直播时则是bms_channel  不检查锁定 但是要检查频道的发布状态
            case CHANNEL:
                checkPublishAndLockStatus(ids, BmsChannel.class, action);
                break;
            //当为栏目是则查询bms_category
            case CATEGORY:
                checkPublishAndLockStatus(ids, BmsCategory.class, action);
                break;
            //其他类型则为bms_content
            default:
                checkPublishAndLockStatus(ids, BmsContent.class, action);
                break;
        }
        picturePublishAndRollback(pcIds, bmsPictures.get(0).getSpId(), bmsPictures.get(0).getSpName(), action);
        return true;
    }

    @Override
    public Boolean picturePublishAndRollback(List<Long> picturesIds, Long spId, String spName, ActionEnums actionEnums) {
        Map<String, OutParamExpand> paramMap = getParamMap();
        return workOrderOperation.send(actionEnums, ContentTypeEnum.PICTURE, picturesIds, null, spId, spName,
                (success, publishStatus, description) -> {
                    this.update(new LambdaUpdateWrapper<BmsPicture>()
                            .in(BmsPicture::getId, picturesIds)
                            .set(BmsPicture::getPublishStatus, CommonUtils.getEnumByAction(actionEnums, success))
                            .set(BmsPicture::getPublishTime, new Date())
                            .set(BmsPicture::getPublishDescription, success ? "" : description)
                    );
                }, paramMap);
    }

    @Override
    public Tuple2<List<Long>, Map<String, String>> getAllpictures(List<BmsChannel> channels, Boolean actionStatus) {

        List<Long> channelIds = channels.stream().map(BmsChannel::getId).collect(Collectors.toList());
        LambdaQueryWrapper<BmsPicture> picturesQueryWrapper = Wrappers.lambdaQuery(BmsPicture.class).
                select(BmsPicture::getId, BmsPicture::getPublishStatus).in(BmsPicture::getBmsContentId, channelIds)
                .eq(BmsPicture::getContentType, ContentTypeEnum.CHANNEL.getValue());
        List<Long> pictureIds = new ArrayList<>();
        if (actionStatus) {
            picturesQueryWrapper.in(BmsPicture::getPublishStatus, PublishStatusRule.CAN_PUBLISH_LIST);
            List<BmsPicture> pictures = list(picturesQueryWrapper);
            Map<String, String> publishMap = new HashMap<>();
            if (CollectionUtils.isEmpty(pictures)) {
                return new Tuple2<>(pictureIds, publishMap);
            }
            pictures.forEach(p -> {
                        if (PublishStatusRule.PUBLISH_LIST.contains(p.getPublishStatus())) {
                            publishMap.put(p.getId() + "", REGIST.getCode() + "");
                            pictureIds.add(p.getId());
                        }
                        if (PublishStatusRule.CAN_UPDATE_LIST.contains(p.getPublishStatus())) {
                            publishMap.put(p.getId() + "", UPDATE.getCode() + "");
                            pictureIds.add(p.getId());
                        }
                    }
            );
            log.info("需要进行发布/更新操作的图片:{}", JSON.toJSON(publishMap));
            return new Tuple2<>(pictureIds, publishMap);
        } else {
            picturesQueryWrapper.notIn(BmsPicture::getPublishStatus, RecycleStatusRule.NOT_RECYCLE_LIST);
            List<BmsPicture> deletePictures = list(picturesQueryWrapper);
            Map<String, String> deleteMap = new HashMap<>();
            if (CollectionUtils.isEmpty(deletePictures)) {
                return new Tuple2<>(pictureIds, deleteMap);
            }
            deletePictures.forEach(p -> {
                        deleteMap.put(p.getId() + "", ActionEnums.DELETE.getCode() + "");
                        pictureIds.add(p.getId());
                    }
            );
            log.info("需要进行回收操作的图片:{}", JSON.toJSON(deleteMap));
            return new Tuple2<>(pictureIds, deleteMap);
        }
    }

    @Override
    public Page<BmsPicture> picturePage(Page page, BmsPicture bmsPicture) {
        LambdaQueryWrapper<BmsPicture> lambdaQuery = Wrappers.lambdaQuery(bmsPicture);
        Page<BmsPicture> page1 = this.page(page, lambdaQuery.orderByAsc(BmsPicture::getSequence).orderByDesc(BmsPicture::getId));
        if (page1.getRecords().size() == 0) {
            return page1;
        }
        SysStorage sysStorage = redisService.getCacheMapValue(RedisKeyConstants.SYS_STORAGE_KEY, String.valueOf(page1.getRecords().get(0).getStorageId()));
        if (sysStorage == null) {
            throw new BizException("从缓存获取存储失败 ,sysStorageId:" + page1.getRecords().get(0).getStorageId());
        }
        for (BmsPicture record : page1.getRecords()) {
            String fileUrl = record.getFileUrl();
            String httpPrefix = sysStorage.getPictureHttpPrefix();
            String[] split = fileUrl.split("/");
            if (split.length >= 3) {
                String u = fileUrl.substring(fileUrl.indexOf(split[3]));
                record.setFileUrl(httpPrefix + "/" + checkFolder(u));
            }
        }

        return page1;
    }

    public String checkFolder(String folder) {
        return folder.startsWith("/") ? folder.substring(1) : folder;
    }


    private BmsPicture createCheckPic(Long id, Integer contentType) {
        return new BmsPicture().setBmsContentId(id).setContentType(contentType);
    }

    /**
     * 使用条件构造器判断是否符合条件
     *
     * @param ids
     * @param clazz<?>
     * @param status   1为回收  0为发布
     * @return
     */
    private boolean checkPublishAndLockStatus(List<Long> ids, Class<?> clazz, ActionEnums status) {
        if (ActionEnums.DELETE.equals(status)) {
            TableInfo tableInfo = TableInfoHelper.getTableInfo(clazz);
            List<BmsPicture> pictures = ids.stream().map(item -> createCheckPic(item, ContentTypeItemEnum.getTableNameByContentType(tableInfo.getTableName()))).collect(Collectors.toList());
            checkLock(pictures);
        } else {
            if (clazz.isAssignableFrom(BmsChannel.class)) {
                RuleCondition.create()
                        .and(PublishStatusRule.init(BmsChannel.class).policy(PUBLISH_SUCCESS).col(BmsChannel::getName).data(ids))
                        .execute().check();
            } else if (clazz.isAssignableFrom(BmsProgram.class)) {
                //由于子集没有锁定状态但是剧头有锁定状态所以需要查询到剧头的ids进行判断是否锁定
                List<Long> contentIds = bmsContentService.getcontentIdsByProgramIds(ids);
                RuleCondition.create()
                        .and(PublishStatusRule.init(clazz).col("name").policy(PUBLISH_SUCCESS).data(ids))
                        .and(LockStatusRule.init(BmsContent.class).data(contentIds))
                        .execute().check();
            } else {
                RuleCondition.create()
                        .and(PublishStatusRule.init(clazz).policy(PUBLISH_SUCCESS).col("name").data(ids))
                        .and(LockStatusRule.init(clazz).data(ids))
                        .execute().check();
            }
        }
        return true;
    }


    /**
     * @Description 图片是否可回收
     * <AUTHOR>
     * @Date 2021-09-24 15:09:35
     * @Return 图片是否可以回收
     */
    @Override
    public List<Long> pictureCanBeRecycled(List<Long> bmsContentIds, ContentTypeEnum contentTypeEnum) {
        List<BmsPicture> pictures = this.list(Wrappers.lambdaQuery(BmsPicture.class)
                .select(BmsPicture::getId, BmsPicture::getPublishStatus, BmsPicture::getSpId, BmsPicture::getSpName)
                .eq(BmsPicture::getContentType, contentTypeEnum.getValue())
                // 跳过不需要回收的 发布失败|回收成功|待发布的|回收中
                .notIn(BmsPicture::getPublishStatus, RecycleStatusRule.NOT_RECYCLE_LIST)
                .in(BmsPicture::getBmsContentId, bmsContentIds));
        return pictures.stream().map(BmsPicture::getId).collect(Collectors.toList());
    }

    /**
     * @Description 图片信息标记回收 把所有回收中的图片标记为 需要删除
     * <AUTHOR>
     * @Date 2021-10-09 17:14:15
     */
    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = Exception.class)
    public boolean pictureMarkRecycled(List<Long> bmsPictureId, ContentTypeEnum contentTypeEnum) {
        return this.update(Wrappers.lambdaUpdate(BmsPicture.class)
                .set(BmsPicture::getStatus, StatusEnum.DELETE.getCode())
                .eq(BmsPicture::getType, contentTypeEnum.getValue())
                .in(BmsPicture::getId, bmsPictureId));
    }

    /**
     * @Description 图片信息删除
     * <AUTHOR>
     * @Date 2021-11-13 14:16:26
     */
    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = Exception.class)
    public boolean removePicture(List<Long> bmsContentIds, ContentTypeEnum contentTypeEnum, boolean deleteFTP) {
        //回收成功检查
        LambdaQueryWrapper<BmsPicture> coutQuery =
                Wrappers.lambdaQuery(BmsPicture.class)
                        .select(BmsPicture::getId)
                        .eq(BmsPicture::getContentType, contentTypeEnum.getValue())
                        .notIn(BmsPicture::getPublishStatus, RecycleStatusRule.CAN_DELETE_LIST)
                        .in(BmsPicture::getBmsContentId, bmsContentIds);
        long count = this.count(coutQuery);
        if (count != 0) {
            throw new BizException("请检查发布状态，存在未回收成功的图片");
        }
        if (!deleteFTP) {
            LambdaQueryWrapper<BmsPicture> deleteQuery = Wrappers.lambdaQuery(BmsPicture.class)
                    .eq(BmsPicture::getContentType, contentTypeEnum.getValue())
                    .in(BmsPicture::getBmsContentId, bmsContentIds);
            // 删除bms图片表信息
            return this.remove(deleteQuery);
        } else {

            // 需要删除FTP图片的
            LambdaQueryWrapper<BmsPicture> deleteQuery = Wrappers.lambdaQuery(BmsPicture.class)
                    .select(BmsPicture::getId, BmsPicture::getCmsPictureId, BmsPicture::getFileUrl, BmsPicture::getCmsPictureCode)
                    .eq(BmsPicture::getContentType, contentTypeEnum.getValue())
                    .in(BmsPicture::getBmsContentId, bmsContentIds);
            List<BmsPicture> list = this.list(deleteQuery);

            // 当BMS表存在其他域的图片时，不能删除cms表数据及ftp
            List<CmsPicture> cmsPictureNeedDeletedList = new ArrayList<>();

            if (!CollectionUtils.isEmpty(list)) {
                Set<String> cmsPictureCodeSet = list.stream().map(BmsPicture::getCmsPictureCode).collect(Collectors.toSet());
                for (String cmsPictureCode : cmsPictureCodeSet) {
                    addCmsPictureNeedDeleted(cmsPictureCode, cmsPictureNeedDeletedList);
                }
                // 删除cms表和ftp
                //删除cms表
                /*List<String> cmsPictureCode = list.stream().filter(p -> StringUtils.isNotEmpty(p.getCmsPictureCode()))
                    .map(BmsPicture::getCmsPictureCode)
                    .collect(Collectors.toList());*/
                if (!CollectionUtils.isEmpty(cmsPictureNeedDeletedList)) {
                    deleteCmsAndFtpPicture(cmsPictureNeedDeletedList, cmsPictureMapper);
                }
            }
            return this.removeByIds(list.stream().map(BmsPicture::getId).collect(Collectors.toList()));
        }
    }

    /**
     * @Description 图片信息删除
     * <AUTHOR>
     * @Date 2021-11-13 14:16:26
     */
    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = Exception.class)
    public boolean removeCategoryPicture(List<Long> ids, boolean deleteFTP) {
        List<BmsPicture> pictures = this.list(Wrappers.lambdaQuery(BmsPicture.class)
                .select(BmsPicture::getFileUrl, BmsPicture::getPublishStatus, BmsPicture::getCmsPictureId, BmsPicture::getCmsPictureCode, BmsPicture::getBmsContentId, BmsPicture::getContentType)
                .eq(BmsPicture::getContentType, ContentTypeEnum.CATEGORY.getValue())
                .in(BmsPicture::getPublishStatus, ROLLBACK.getCode(), WAITPUBLISH.getCode(), FAILPUBLISH.getCode())
                .in(BmsPicture::getId, ids));
        if (ids.size() != pictures.size()) {
            throw new BizException("请检查发布状态，存在未回收成功的图片");
        }
        List<Long> categoryIds = pictures.stream().map(BmsPicture::getBmsContentId).collect(Collectors.toList());
        // 栏目判断锁定
        RuleResult ruleResult = RuleUtil.checkLockStatus(BmsCategory.class, categoryIds);
        if (!ruleResult.isPass()) {
            throw new BizException(ruleResult.getMsg());
        }

        // 删除cms以及ftp
        if (deleteFTP) {
            // 当BMS表存在其他域的图片时，不能删除cms表数据及ftp
            List<CmsPicture> cmsPictureNeedDeletedList = new ArrayList<>();
            if (!CollectionUtils.isEmpty(pictures)) {
                Set<String> cmsPictureCodeSet = pictures.stream().map(BmsPicture::getCmsPictureCode).collect(Collectors.toSet());
                for (String cmsPictureCode : cmsPictureCodeSet) {
                    addCmsPictureNeedDeleted(cmsPictureCode, cmsPictureNeedDeletedList);
                }
                if (!CollectionUtils.isEmpty(cmsPictureNeedDeletedList)) {
                    deleteCmsAndFtpPicture(cmsPictureNeedDeletedList, cmsPictureMapper);
                }
            }
            // 非cms授权过来的图片 进行FTP物理删除
            /*List<String> ftpUrl = pictures.stream().filter(p -> Objects.isNull(p.getCmsPictureId())).map(BmsPicture::getFileUrl).collect(Collectors.toList());
            if (!CollectionUtils.isEmpty(ftpUrl)) {
                if (remove) {
                    ftpUrl.forEach(FtpAnalysisUtil::DelFtp);
                }
            }*/
        }
        // 删除bms图片表信息
        boolean remove = this.removeByIds(ids);
        return remove;
    }

    // 执行删除cms以及ftp
    private void deleteCmsAndFtpPicture(List<CmsPicture> cmsPictureNeedDeletedList, CmsPictureMapper cmsPictureMapper) {
        Set<String> cmsPictureCodeNeedDeletedList = cmsPictureNeedDeletedList.stream().map(CmsPicture::getCode).collect(Collectors.toSet());
        cmsPictureMapper.delete(Wrappers.lambdaQuery(CmsPicture.class).in(CmsPicture::getCode, cmsPictureCodeNeedDeletedList));

        // 非cms授权过来的图片 进行FTP物理删除
//                    List<String> ftpUrl = list.stream().filter(p -> Objects.isNull(p.getCmsPictureId())).map(BmsPicture::getFileUrl).collect(Collectors.toList());
        List<String> ftpUrl = cmsPictureNeedDeletedList.stream()
                .filter(p -> StringUtils.isNotEmpty(p.getFileUrl())).map(CmsPicture::getFileUrl).collect(Collectors.toList());
        if (!CollectionUtils.isEmpty(ftpUrl)) {
            ftpUrl.forEach(FtpAnalysisUtil::DelFtp);
        }
    }

    // 将需要删除的cmsPictureCode放入list
    private void addCmsPictureNeedDeleted(String cmsPictureCode, List<CmsPicture> cmsPictureNeedDeletedList) {
        LambdaQueryWrapper<BmsPicture> allSPPicturesQuery = Wrappers.lambdaQuery(BmsPicture.class)
                .select(BmsPicture::getId, BmsPicture::getFileUrl)
                .eq(BmsPicture::getCmsPictureCode, cmsPictureCode);
        List<BmsPicture> allSPPictureList = this.list(allSPPicturesQuery);

        // 如果通过cmsPictureCode查出来的bms表size <= 1，可以删除cms表数据及ftp
        if (!CollectionUtils.isEmpty(allSPPictureList) && allSPPictureList.size() <= 1) {
            // 放到要删除的List里
            CmsPicture cmsPicture = new CmsPicture();
            cmsPicture.setCode(cmsPictureCode);
            cmsPicture.setFileUrl(allSPPictureList.get(0).getFileUrl());
            cmsPictureNeedDeletedList.add(cmsPicture);
        }
    }

    @Override
    public IPage<BmsPictureVO> findPictureByCategoryId(Page<BmsPicture> page, Long categoryId) {
        Page<BmsPicture> picturePage = this.page(page, Wrappers.lambdaQuery(BmsPicture.class)
                .eq(BmsPicture::getBmsContentId, categoryId)
                .eq(BmsPicture::getContentType, ContentTypeEnum.CATEGORY.getValue())
                .orderByAsc(BmsPicture::getSequence)
                .orderByDesc(BmsPicture::getId));
        List<BmsPictureVO> pVO = new ArrayList<>();
        picturePage.getRecords().forEach(bmsPicture -> {
            String fileUrl = bmsPicture.getFileUrl();
            Long storageId = bmsPicture.getStorageId();
            SysStorage sysStorage = redisService.getCacheMapValue(RedisKeyConstants.SYS_STORAGE_KEY, String.valueOf(storageId));
            if (sysStorage == null) {
                throw new BizException("从缓存获取存储失败，存储id:" + storageId);
            }
            BmsPictureVO bmsPictureVO = new BmsPictureVO();
            BeanUtils.copyProperties(bmsPicture, bmsPictureVO);

            // 设置http访问地址
            String httpPrefix = sysStorage.getPictureHttpPrefix();
            if (httpPrefix.endsWith("/")) {
                // https://www.baidu.com/ 如果最后带“/” 则去除
                httpPrefix = httpPrefix.substring(0, httpPrefix.length() - 1);
            }
            // 切割地址*****************************/vstore/movie/demo001.mkv
            String[] split = fileUrl.split("/");
            if (split.length >= 3) {
                // vstore/movie/demo001.mkv
                String u = fileUrl.substring(fileUrl.indexOf(split[3]));
                bmsPictureVO.setHttpUrl(httpPrefix + "/" + u);
            }
            pVO.add(bmsPictureVO);
        });
        Page<BmsPictureVO> voPage = new Page<>();
        voPage.setCurrent(picturePage.getCurrent());
        voPage.setSize(picturePage.getSize());
        voPage.setTotal(picturePage.getTotal());
        voPage.setRecords(pVO);
        return voPage;
    }

    // 通过contentId查询可以组装发布|更新工单的Map
    @Override
    public Map<String, String> findCanPublishPicture(List<Long> contentIds, ContentTypeEnum typeEnum) {
        List<BmsPicture> bmsPictures = this.list(Wrappers.lambdaQuery(BmsPicture.class)
                .select(BmsPicture::getId, BmsPicture::getPublishStatus)
                .eq(BmsPicture::getContentType, typeEnum.getValue())
                .in(BmsPicture::getBmsContentId, contentIds));
        return buildPublishAndUpdateWorkOrderMap(bmsPictures);
    }

    /**
     * <AUTHOR>
     * @Date 2021-11-06 19:53:04
     * @Description 组建 发布|更新 所需的Map
     * 发布则不需要，发布操作可能关联到发布|更新工单的场景(列如发布媒资携带 待更新的图片|待发布的图片)
     * 通过查询过滤一起下发，不与回收共用
     */
    @Override
    public Map<String, String> buildPublishAndUpdateWorkOrderMap(List<BmsPicture> bmsPictures) {
        // 发布工单
        List<Long> publishIds = new ArrayList<>();
        // 更新工单
        List<Long> updateIds = new ArrayList<>();
        bmsPictures.forEach(picture ->
        {
            Integer publishStatus = picture.getPublishStatus();
            Long id = picture.getId();
            // 进行发布操作的
            if (WAITPUBLISH.getCode().equals(publishStatus) || FAILPUBLISH.getCode().equals(publishStatus) || ROLLBACK.getCode().equals(publishStatus)) {
                publishIds.add(id);
            }
            // 进行更新操作的
            else if (WAITUPDATE.getCode().equals(publishStatus) || FAILUPDATE.getCode().equals(publishStatus)) {
                updateIds.add(id);
            }
        });
        Map<String, String> picMap = new HashMap<>();
        if (!publishIds.isEmpty()) {
            publishIds.forEach(id -> picMap.put(id.toString(), REGIST.getCode().toString()));
        }
        if (!updateIds.isEmpty()) {
            updateIds.forEach(id -> picMap.put(id.toString(), UPDATE.getCode().toString()));
        }
        return picMap;
    }


    /**
     * <AUTHOR>
     * @Date 2021-11-06 19:56:15
     * @Description 组建 回收 所需的Map
     * 因为回收和更新的条件冲突，待更新和更新失败既可以发更新又可以发回收
     * 构建前调用this.pictureCanBeRecycled, 会返回所有可回收的图片
     */
    @Override
    public Map<String, String> buildRecycleWorkOrderMap(List<Long> ids) {
        Map<String, String> picMap = new HashMap<>();
        if (ObjectUtil.isNotEmpty(ids)) {
            ids.forEach(id -> picMap.put(id.toString(), ActionEnums.DELETE.getCode().toString()));
        }
        return picMap;
    }

    /**
     * @param success 是否调用下发接口成功
     * <AUTHOR>
     * @Date 2021-11-06 21:54:10
     * @Description // 处理图片下发后的修改状态以及描述
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean pictureIssuedProcess(boolean success, List<Long> publishPicIds, List<Long> updatePicIds, List<Long> recyclePicIds, String description) {
        boolean publish = true;
        boolean update = true;
        boolean recycle = true;
        // 发布中
        if (ObjectUtil.isNotEmpty(publishPicIds)) {
            publish = this.update(Wrappers.lambdaUpdate(BmsPicture.class)
                    .set(BmsPicture::getPublishStatus, success ? PUBLISHING.getCode() : FAILPUBLISH.getCode())
                    .set(BmsPicture::getPublishDescription, description)
                    .set(BmsPicture::getPublishTime, new Date())
                    .in(BmsPicture::getId, publishPicIds));
        }
        // 更新中
        if (ObjectUtil.isNotEmpty(updatePicIds)) {
            update = this.update(Wrappers.lambdaUpdate(BmsPicture.class)
                    .set(BmsPicture::getPublishStatus, success ? UPDATING.getCode() : FAILUPDATE.getCode())
                    .set(BmsPicture::getPublishDescription, description)
                    .set(BmsPicture::getPublishTime, new Date())
                    .in(BmsPicture::getId, updatePicIds));
        }
        // 回收中
        if (ObjectUtil.isNotEmpty(recyclePicIds)) {
            recycle = this.update(Wrappers.lambdaUpdate(BmsPicture.class)
                    .set(BmsPicture::getPublishStatus, success ? ROLLBACKING.getCode() : FAILROLLBACK.getCode())
                    .set(BmsPicture::getPublishDescription, description)
                    .set(BmsPicture::getPublishTime, new Date())
                    .in(BmsPicture::getId, recyclePicIds));
        }
        return publish && update && recycle;
    }

    @Override
    public Boolean reorder(BmsPicture bmsPicture) {
        BmsPicture getPictureById = bmsPictureMapper.selectById(bmsPicture.getId());
        if (PublishStatusRule.MUST_ING_LIST.contains(getPictureById.getPublishStatus())) {
            throw new BizException("请检查发布状态，请勿选择发布中、更新中、回收中的图片");
        }
        // 图片所属内容是否锁定
        List<BmsPicture> bmsPictures = new ArrayList<>();
        bmsPictures.add(getPictureById);
        checkLock(bmsPictures);

        return update(Wrappers.lambdaUpdate(BmsPicture.class)
                .eq(BmsPicture::getId, bmsPicture.getId())
                .set(BmsPicture::getSequence, bmsPicture.getSequence())
                .set(BmsPicture::getPublishStatus, RuleUtil.confirmPublishStatus(getPictureById.getPublishStatus())));
    }

    @Override
    public Boolean pictureCallback(Tuple2<List<Long>, Map<String, String>> picturesTuple, Boolean status) {
        List<Long> picturesIds = picturesTuple.getA().get();
        Map<String, String> pictureMap = picturesTuple.getB().get();
        List<BmsPicture> updatePicture = new ArrayList<>(picturesIds.size());

        picturesIds.forEach(pId -> {
            pictureMap.forEach((idStr, actionStr) -> {
                Long id = Long.parseLong(idStr);
                int action = Integer.parseInt(actionStr);
                ActionEnums value = ActionEnums.getByValue(action);
                if (id.equals(pId)) {
                    updatePicture.add(creatCallBackPic(id, CommonUtils.getEnumByAction(value, status)));
                }
            });
        });
        return this.updateBatchById(updatePicture);
    }

    private BmsPicture creatCallBackPic(Long id, int publishStatus) {
        return new BmsPicture().setId(id).setPublishTime(new Date()).setPublishDescription("").setPublishStatus(publishStatus);
    }

    @Override
    public boolean deleteByCodeAndSp(List<SubOrderMappingsEntity> deletePictureMappingEntities, List<Long> spIdList) {
        log.info("删除内容图片关系，sp做删除，cp做关系解绑，content相关字段置空");
        // 查询mapping关系的其他非自动发布sp情况
        // sql:
        //      sp_id not in ? and publish_status not in ? and ((content_code=? and code=?) or (content_code=? and code=?))
        /*LambdaQueryWrapper<BmsPicture> wrapper = Wrappers.lambdaQuery(BmsPicture.class)
                .notIn(BmsPicture::getSpId, spIdList)
                .notIn(BmsPicture::getPublishStatus,
                        WAITPUBLISH.getCode(),
                        ROLLBACK.getCode(),
                        FAILPUBLISH.getCode()
                ).and(queryWrapper ->
                    deletePictureMappingEntities.forEach(orderMappingsEntity ->
                        queryWrapper.or(child -> child
                                .eq(BmsPicture::getContentCode, orderMappingsEntity.getElementCode())
                                .eq(BmsPicture::getCode, orderMappingsEntity.getParentCode())
                        )
                    )
        );
        Long extendSpContentCount = bmsPictureMapper.selectCount(wrapper);
        boolean allSpRollback = true;
        if (extendSpContentCount != null && extendSpContentCount > 0) {
            log.error("该Cp 图片mapping已授权给其他非自动发布SP且已经发布到运营商");
            allSpRollback = false;
//            throw new CommonResponseException(CommonResponseEnum.DELETE_ERROR);
        }
        boolean finalAllSpRollback = allSpRollback;*/
        deletePictureMappingEntities.forEach(orderMappingsEntity -> {
            bmsPictureMapper.update(null, Wrappers.lambdaUpdate(BmsPicture.class)
                    .set(BmsPicture::getBmsContentId, null)
                    .eq(BmsPicture::getCmsPictureCode, orderMappingsEntity.getParentCode())
                    .eq(BmsPicture::getContentCode, orderMappingsEntity.getElementCode())
                    .in(BmsPicture::getSpId, spIdList)
            );
//            }
        });
        return true;
    }

    @Override
    public boolean delPicByCodeAndSp(List<String> codeList, List<Long> spIdList, boolean isRollback) {
        log.info("自动发布删除图片");

        if (isRollback) {
            bmsPictureMapper.update(null, Wrappers.lambdaUpdate(BmsPicture.class)
                    .set(BmsPicture::getPublishStatus, ROLLBACK.getCode())
                    .in(BmsPicture::getCmsPictureCode, codeList)
                    .in(BmsPicture::getSpId, spIdList)
            );
            return true;
        }
        bmsPictureMapper.delete(Wrappers.lambdaQuery(BmsPicture.class)
                .in(BmsPicture::getCmsPictureCode, codeList)
                .in(BmsPicture::getSpId, spIdList)
        );

        return true;
    }

    @Override
    public List<BmsPicture> listByCmsPictureId(Long id) {
        LambdaQueryWrapper<BmsPicture> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.eq(BmsPicture::getCmsPictureId, id);
        return list(queryWrapper);
    }

    @Override
    public void deleteByCmsPictureIds(Set<Long> seriesPictureIds) {
        if (CollectionUtil.isEmpty(seriesPictureIds)) {
            return;
        }
        LambdaQueryWrapper<BmsPicture> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.in(BmsPicture::getCmsPictureId, seriesPictureIds);
        remove(queryWrapper);
    }

    @Override
    public boolean preCheck() {
        BmsPicRecycleDto args = getArgs(BmsPicRecycleDto.class);
        ContentTypeEnum type = args.getType();
        List<Long> ids = args.getContentIds();
        boolean pass = args.isPreCheckPass();
        if (pass)
            return true;

        if (type == null || ObjectUtil.isEmpty(ids)) {
            log.warn("图片type或者ids为空!");
            return false;
        }
        args.setPreCheckPass(true);
        return true;
    }

    @Override
    public List<BmsPicture> findCanRecycleList() {
        BmsPicRecycleDto tempData = getArgs(BmsPicRecycleDto.class);
        ContentTypeEnum type = tempData.getType();
        List<Long> ids = tempData.getContentIds();
        if (type == null || ObjectUtil.isEmpty(ids)) {
            return null;
        }
        return this.list(Wrappers.lambdaQuery(BmsPicture.class)
                .select(BmsPicture::getId, BmsPicture::getPublishStatus, BmsPicture::getSpId, BmsPicture::getSpName)
                //where
                .eq(BmsPicture::getContentType, type.getValue())
                // 跳过不需要回收的 发布失败|回收成功|待发布的|回收中
                .notIn(BmsPicture::getPublishStatus, RecycleStatusRule.NOT_RECYCLE_LIST)
                .in(BmsPicture::getBmsContentId, ids));
    }

    @Override
    //图片直接删除
    public boolean deleteRemain() {
        this.preCheck();
        BmsPicRecycleDto tempData = getArgs(BmsPicRecycleDto.class);
        ContentTypeEnum type = tempData.getType();
        List<Long> ids = tempData.getContentIds();
        if (type == null || ObjectUtil.isEmpty(ids)) {
            return true;
        }
        return this.remove(Wrappers.lambdaQuery(BmsPicture.class)
                .select(BmsPicture::getId)
                //where
                .eq(BmsPicture::getContentType, type.getValue())
                .in(BmsPicture::getPublishStatus, RecycleStatusRule.CAN_DELETE_LIST)
                .in(BmsPicture::getBmsContentId, ids));
    }

    @Override
    //图片标记删除 一定是要调用接口后执行
    public List<Long> markDelete() {
        this.preCheck();
        BmsPicRecycleDto args = getArgs(BmsPicRecycleDto.class);
        ContentTypeEnum type = args.getType();
        List<Long> ids = args.getContentIds();
        if (type == null || ObjectUtil.isEmpty(ids)) {
            return null;
        }
        this.update(Wrappers.lambdaUpdate(BmsPicture.class)
                .set(BmsPicture::getStatus, StatusEnum.DELETE.getCode())
                //where
                .eq(BmsPicture::getType, type.getValue())
                .ne(BmsPicture::getStatus, StatusEnum.DELETE.getCode())
                .in(BmsPicture::getBmsContentId, ids)
                .in(BmsPicture::getPublishStatus, RecycleStatusRule.CAN_MARK_DELETE_LIST));
        return null;
    }

    @Override
    //图片一键回收
    public boolean oneKeyRecycle() {
        return false;
    }


    @Override
    public boolean updatePublishStatus(List<SubOrderMappingsEntity> orderMappingsEntities, List<Long> spIdList) {
        Integer result = bmsPictureMapper.updatePublishStatus(orderMappingsEntities, spIdList);
        return null != result && result >= 1;
    }


    /**
     * @param file
     * @param imageType
     * @throws IOException
     * @Description 图片校验
     */
    @Override
    public Boolean validatePicture(MultipartFile file, Integer imageType) throws IOException {
        // 校验文件名
        String originalFilename = Optional.ofNullable(file.getOriginalFilename())
                .filter(StringUtils::isNotBlank)
                .orElseThrow(() -> new CommonResponseException("图片文件名为空"));

        String suffixName = originalFilename.substring(originalFilename.lastIndexOf(SymbolConstant.PERIOD) + 1).toLowerCase();
        if (!isValidFormat(suffixName)) {
            throw new CommonResponseException("图片文件格式不符合规范");
        }

        // 校验文件大小
        long fileSizeInKb = file.getSize() / 1024;
        if (!isValidFileSize(fileSizeInKb, imageType)) {
            throw new CommonResponseException("图片储存KB值不符合规范");
        }

        // 校验图片尺寸
        BufferedImage image = ImageIO.read(file.getInputStream());
        int width = image.getWidth();
        int height = image.getHeight();
        if (!isValidImageSize(width, height, imageType)) {
            throw new CommonResponseException("图片尺寸不符合规范");
        }
        return true;
    }

    /**
     * 校验文件格式
     *
     * @param suffixName
     * @return
     */
    private boolean isValidFormat(String suffixName) {
        return Stream.of("png", "jpeg", "jpg", "gif").anyMatch(suffixName::equals);
    }

    /**
     * 校验图片文件大小
     *
     * @param fileSizeInKb
     * @param imageType
     * @return
     */
    private boolean isValidFileSize(long fileSizeInKb, int imageType) {
        switch (imageType) {
            // 海报
            case 1:
                // 横图
            case 20:
                return fileSizeInKb <= 80;
            // 标题图
            case 4:
                return fileSizeInKb <= 50;
            // 横图带logo
            case 22:
                // 人物抠图
            case 99:
                return fileSizeInKb <= 100;
            // 大横图带logo
            case 21:
                // 全屏图
            case 23:
                return fileSizeInKb <= 500;
            default:
                return true;
        }
    }

    /**
     * 校验图片尺寸
     *
     * @param width
     * @param height
     * @param imageType
     * @return
     */
    private boolean isValidImageSize(int width, int height, int imageType) {
        switch (imageType) {
            // 海报
            case 1:
                return width == 294 && height == 392;
            // 横图
            case 20:
                // 横图带logo
            case 22:
                return width == 560 && height == 315;
            // 大横图带logo
            case 21:
                return width == 860 && height == 360;
            // 全屏图
            case 23:
                return width == 1280 && height == 720;
            default:
                return true;
        }
    }
}
