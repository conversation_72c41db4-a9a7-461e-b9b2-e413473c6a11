package com.pukka.iptv.manage.service.sys;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.pukka.iptv.common.data.model.sys.SysOutPassage;

import java.util.List;

/**
 *
 * @author: tan
 * @date: 2021-9-2 23:02:27
 */

public interface SysOutPassageService extends IService<SysOutPassage> {

    Page<SysOutPassage> page(Page<SysOutPassage> page, SysOutPassage sysOutPassage);

    Page<SysOutPassage> pageFilter(Page<SysOutPassage> page, SysOutPassage sysOutPassage);

    Page<SysOutPassage> pageStatistics(Page<SysOutPassage> page, Long spId);

    /**
     * 通过ids删除
     * @param id
     * @return
     */
    boolean removeTwoById(Long id);

    /**
     * 通过ids删除
     * @param ids 分发通道id集合
     * @return bool
     */
    boolean removeByIds(List<Long> ids);

    /**
     * 通过lspid查询
     * @param lspId
     * @return
     */
    SysOutPassage getByLspId(String lspId);

    /**
     * 删除mysql&redis数据
     * @param ids
     * @return
     */
    boolean removeTwoByIds(List<Long> ids);

    /**
     * 新增mysql&redis数据
     * @param sysOutPassage
     * @return
     */
    boolean saveToCacheAndDB(SysOutPassage sysOutPassage);

    /**
     * 更新mysql&redis数据
     * @param sysOutPassage
     * @return
     */
    boolean updateToCacheAndDB(SysOutPassage sysOutPassage);
}


