package com.pukka.iptv.manage.service.sys.impl;

import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.pukka.iptv.common.base.enums.CommonResponseEnum;
import com.pukka.iptv.common.base.exception.CommonResponseException;
import com.pukka.iptv.common.base.util.JwtTokenUtil;
import com.pukka.iptv.common.base.util.Menu;
import com.pukka.iptv.common.base.util.MenuUtil;
import com.pukka.iptv.common.base.util.Meta;
import com.pukka.iptv.common.core.constant.MenuRedisKeyConstant;
import com.pukka.iptv.common.data.model.sys.SysMenu;
import com.pukka.iptv.manage.mapper.sys.SysMenuMapper;
import com.pukka.iptv.manage.service.sys.SysMenuService;
import io.jsonwebtoken.lang.Collections;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 *
 * @author: zhengcl
 * @date: 2021-8-17 16:48:32
 */

@Service
public class SysMenuServiceImpl extends ServiceImpl<SysMenuMapper, SysMenu> implements SysMenuService {

	@Autowired
	private SysMenuMapper sysMenuMapper;
	@Autowired
	private RedisTemplate redisTemplate;

	@Override
	public List<Menu> listByRoleIds(Set roleIds){
		List<SysMenu> sysMenus = sysMenuMapper.listByRoleIds(roleIds);
		return transMenu(sysMenus);
	}

	@Override
	public Object menuList(Long userId) {
		return redisTemplate.opsForHash().get(JwtTokenUtil.getUserRedisTokenKey(userId), MenuRedisKeyConstant.MENUS);
	}

	@Override
	public Object tabList(Long userId, Long parentId, String group) {
		Map tabs = (Map) redisTemplate.opsForHash().get(JwtTokenUtil.getUserRedisTokenKey(userId), MenuRedisKeyConstant.TABS);
		if(!Collections.isEmpty(tabs)){
			return tabs.get(MenuUtil.getRedisButtonKey(parentId,group));
		}
		return null;
	}

	@Override
	public Object groupList(Long userId, Long parentId, String group) {
		Map groups = (Map) redisTemplate.opsForHash().get(JwtTokenUtil.getUserRedisTokenKey(userId), MenuRedisKeyConstant.GROUPS);
		if(!Collections.isEmpty(groups)){
			return groups.get(MenuUtil.getRedisButtonKey(parentId,group));
		}
		return null;
	}

	@Override
	public Object toolButtonList(Long userId, Long parentId, String group) {
		Map buttons = (Map) redisTemplate.opsForHash().get(JwtTokenUtil.getUserRedisTokenKey(userId), MenuRedisKeyConstant.TOOL_BUTTONS);
		if(!Collections.isEmpty(buttons)){
			return buttons.get(MenuUtil.getRedisButtonKey(parentId,group));
		}
		return null;
	}

	@Override
	public Object columnButtonList(Long userId, Long parentId, String group) {
		Map buttons = (Map) redisTemplate.opsForHash().get(JwtTokenUtil.getUserRedisTokenKey(userId), MenuRedisKeyConstant.COLUMN_BUTTONS);
		if(!Collections.isEmpty(buttons)){
			return buttons.get(MenuUtil.getRedisButtonKey(parentId,group));
		}
		return null;
	}

	@Override
	public List<Menu> listTreeAll(Long roleId) {
		List<SysMenu> sysMenus = sysMenuMapper.listAll(roleId);
		List<Menu> menus = transMenu(sysMenus);
		List<Menu> treeList = new ArrayList<>();
		MenuUtil.getMenuTreeList(menus,treeList);
		return treeList;
	}

	@Override
	public List<Menu> listAll(Long roleId) {
		List<SysMenu> sysMenus = sysMenuMapper.listAll(roleId);
		List<Menu> menus = transMenu(sysMenus);
		return menus;
	}

	@Override
	@Transactional
	public boolean update(SysMenu sysMenu) {
		sysMenu.valid();

		long count = count(Wrappers.lambdaQuery(SysMenu.class)
				.eq(SysMenu::getName, sysMenu.getName()).ne(SysMenu::getId, sysMenu.getId()));

		if(count > 0){
			throw new CommonResponseException(CommonResponseEnum.FAIL,"菜单已存在");
		}
		sysMenuMapper.updateById(sysMenu);
		return true;
	}



	private SysMenu getByName(String name){
		QueryWrapper<SysMenu> query = new QueryWrapper<>();
		query.lambda().eq(SysMenu::getName, name).last(" limit 1");
		return this.sysMenuMapper.selectOne(query);
	}

	/**
	 * menu转换
	 * */
	private List<Menu> transMenu(List<SysMenu> sysMenus){
		List<Menu> menus = new ArrayList<>();
		if(!CollectionUtils.isEmpty(sysMenus)){
			for (SysMenu sysMenu : sysMenus) {
				Menu menu = new Menu();
				BeanUtils.copyProperties(sysMenu,menu);
				Meta meta = JSON.parseObject(sysMenu.getMeta(), Meta.class);
				menu.setMeta(meta);
				menus.add(menu);
			}
		}
		return menus;
	}

}


