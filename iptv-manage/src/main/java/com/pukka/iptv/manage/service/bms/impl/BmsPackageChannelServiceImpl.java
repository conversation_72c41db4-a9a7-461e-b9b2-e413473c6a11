package com.pukka.iptv.manage.service.bms.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.pukka.iptv.common.data.model.bms.BmsPackageChannel;
import com.pukka.iptv.manage.mapper.bms.BmsPackageChannelMapper;
import com.pukka.iptv.manage.service.bms.BmsPackageChannelService;
import org.springframework.stereotype.Service;

/**
 * 包内容
 *
 * <AUTHOR>
 * @date 2021-09-10 17:08:46
 */

@Service
public class BmsPackageChannelServiceImpl extends ServiceImpl<BmsPackageChannelMapper, BmsPackageChannel> implements BmsPackageChannelService {

}
