package com.pukka.iptv.manage.service.copyright.prohibit.impl;

import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.ObjectUtils;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.google.common.collect.Lists;
import com.pukka.iptv.common.base.constant.DictionaryBaseConstants;
import com.pukka.iptv.common.base.constant.RedisKeyConstants;
import com.pukka.iptv.common.base.enums.ContentTypeEnum;
import com.pukka.iptv.common.base.exception.CommonResponseException;
import com.pukka.iptv.common.base.util.JwtTokenUtil;
import com.pukka.iptv.common.base.util.UUID;
import com.pukka.iptv.common.base.vo.SecurityUser;
import com.pukka.iptv.common.core.constant.SymbolConstant;
import com.pukka.iptv.common.core.util.SafeUtil;
import com.pukka.iptv.common.core.util.easyexcel.EasyExcelUtil;
import com.pukka.iptv.common.data.dto.RuleProhibitDto;
import com.pukka.iptv.common.data.model.copyright.RuleProhibit;
import com.pukka.iptv.common.data.model.sys.SysDictionaryBase;
import com.pukka.iptv.common.data.model.sys.SysDictionaryItem;
import com.pukka.iptv.common.data.util.BeanUtil;
import com.pukka.iptv.common.data.vo.copyright.RuleProhibitImportVo;
import com.pukka.iptv.common.data.vo.copyright.RuleProhibitListVo;
import com.pukka.iptv.common.redis.service.RedisService;
import com.pukka.iptv.manage.export.model.ExcelTaskInfo;
import com.pukka.iptv.manage.export.model.ExportInfo;
import com.pukka.iptv.manage.export.task.ExportTask;
import com.pukka.iptv.manage.mapper.copyright.prohibit.RuleProhibitMapper;
import com.pukka.iptv.manage.service.cache.CacheServiceImpl;
import com.pukka.iptv.manage.service.copyright.prohibit.RuleProhibitService;
import com.pukka.iptv.manage.service.sys.SysDictionaryItemService;
import com.pukka.iptv.manage.util.CommonUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import java.io.ByteArrayOutputStream;
import java.util.*;

/**
 * @Author: chiron
 * @Date: 2022/07/25/10:08
 * @Description:
 */
@Slf4j
@Service
public class RuleProhibitServiceImpl extends ServiceImpl<RuleProhibitMapper, RuleProhibit>
        implements RuleProhibitService {
    @Autowired
    private CacheServiceImpl cacheService;
    @Autowired
    private ExportTask<RuleProhibit> exportTask;
    @Autowired
    private RuleProhibitMapper ruleProhibitMapper;
    @Autowired
    private SysDictionaryItemService sysDictionaryItemService;
    @Autowired
    private RedisService redisService;

    /**
     * 字典是否注册 true注册  false不注册
     */
    @Value("${dictionary.register.flag:false}")
    private boolean dictionaryRegisterFlag;

    /**
     * 检查并添加数据
     *
     * @param ruleProhibit
     * @return
     */
    @Override
    public Boolean saveAfterCheck(RuleProhibit ruleProhibit) {
        //检查规则是否存在
        boolean exist = isExists(ruleProhibit);
        if (exist) {
            log.warn("当前违禁规则存在重复，数据:{}", ruleProhibit);
            throw new CommonResponseException("当前违禁规则存在重复,规则添加失败");
        }
        if (StringUtils.isBlank(ruleProhibit.getShowName())) {
            throw new CommonResponseException("违禁规则名称不能为空或空格,规则添加失败");
        }
        ruleProhibit.setCode(UUID.nextSnow().toString());
        ruleProhibit.setId(null);
        SecurityUser user = JwtTokenUtil.getSecurityUser();
        if (null != user) {
            ruleProhibit.setCreatorName(user.getName());
        }
        ruleProhibit.setCreateTime(null);
        ruleProhibit.setUpdateTime(null);
        // 设置媒资分类和产地
        setPgmCategoryAndOriginalCountry(ruleProhibit);
        // 校验年份
        if (StringUtils.isNotBlank(ruleProhibit.getReleaseYear())) {
            checkReleaseYear(ruleProhibit.getReleaseYear());
        }

        int insert = ruleProhibitMapper.insert(ruleProhibit);
        //插入redis
        try {
            boolean b = cacheService.cacheRuleCache(Collections.singletonList(ruleProhibit));
        } catch (Exception exception) {
            log.error("更新RuleCache失败,Exception:{}", exception);
        }
        return insert > 0;
    }

    /**
     * 模板导出
     *
     * @param ids
     * @return
     */
    @Override
    public Object exportInfo(List<Long> ids) {
        //String[] idList = ids.split(",");
        LambdaQueryWrapper<RuleProhibit> lambdaQueryWrapper = new LambdaQueryWrapper();
        lambdaQueryWrapper.in(RuleProhibit::getId, ids);
        long ms = System.currentTimeMillis();
        ExportInfo<RuleProhibit> exportInfo = new ExportInfo<>();
        exportInfo.setOut(new ByteArrayOutputStream())
                .setSheetName("违禁规则")
                .setQueryWrapper(lambdaQueryWrapper)
                .setPojoClass(RuleProhibit.class)
                .setCacheKey(RedisKeyConstants.RULE_PROHIBIT_EXPORT)
                .setBaseMapper(ruleProhibitMapper);
        ExcelTaskInfo excelTaskInfo = exportTask.startProcess(exportInfo).dowork();
        log.info("总导出时长为 {} ms", (System.currentTimeMillis() - ms));
        return JSON.toJSON(excelTaskInfo);
    }

    @Override
    public void download(HttpServletResponse response) {
        String fileName = "违规规则导入模板";
        String sheetName = "违规规则";
        RuleProhibitImportVo vo1 = new RuleProhibitImportVo("蜘蛛侠：平行宇宙", "电影", "单集", "张三", "李四", "中国", "2022-01-01");
        RuleProhibitImportVo vo2 = new RuleProhibitImportVo("蜘蛛侠：平行宇宙2", "电影", "单集", "张三", "李四", "中国", "2022-01-02");
        List<RuleProhibitImportVo> vos = Arrays.asList(vo1, vo2);
        EasyExcelUtil.exportExcelSingleSheet(response, fileName, sheetName, RuleProhibitImportVo.class, vos);
    }

    @Override
    public int updateRuleProhibit(@Valid RuleProhibit ruleProhibit) {
        //检查规则是否存在
        boolean exist = isExists(ruleProhibit);
        if (exist) {
            throw new CommonResponseException("当前违禁规则存在重复,规则添加失败");
        }
        // 设置媒资分类和产地
        setPgmCategoryAndOriginalCountry(ruleProhibit);
        // 校验年份
        if (StringUtils.isNotBlank(ruleProhibit.getReleaseYear())) {
            checkReleaseYear(ruleProhibit.getReleaseYear());
        }
        if (StringUtils.isBlank(ruleProhibit.getShowName())) {
            throw new CommonResponseException("违禁规则名称不能为空或空格,规则添加失败");
        }
        int update = ruleProhibitMapper.updateById(ruleProhibit);
        //插入redis
        try {
            boolean b = cacheService.cacheRuleCache(Collections.singletonList(ruleProhibit));
        } catch (Exception exception) {
            log.error("更新RuleCache失败,Exception:{}", exception);
        }
        return update;
    }

    /**
     * 设置媒资分类和产地
     * @param ruleProhibit
     */
    private void setPgmCategoryAndOriginalCountry(RuleProhibit ruleProhibit) {
        // 违禁规则媒资分类ID
        if (Objects.nonNull(ruleProhibit.getPgmCategoryId())) {
            SysDictionaryItem one = sysDictionaryItemService.getOne(Wrappers.<SysDictionaryItem>lambdaQuery().eq(SysDictionaryItem::getId, ruleProhibit.getPgmCategoryId()).last("limit 1"));
            if (null == one) {
                throw new CommonResponseException("媒资分类id:" + ruleProhibit.getPgmCategoryId() + "不存在");
            }
            ruleProhibit.setPgmCategory(one.getName());
        } else {
            ruleProhibit.setPgmCategory(null);
        }
        // 产地id
        if (Objects.nonNull(ruleProhibit.getOriginalCountryId())) {
            SysDictionaryItem country = sysDictionaryItemService.getOne(Wrappers.<SysDictionaryItem>lambdaQuery().eq(SysDictionaryItem::getId, ruleProhibit.getOriginalCountryId()).last("limit 1"));
            if (null == country) {
                throw new CommonResponseException("产地id:" + ruleProhibit.getOriginalCountryId() + "不存在");
            }
            ruleProhibit.setOriginalCountry(country.getName());
        } else {
            ruleProhibit.setOriginalCountry(null);
        }
    }

    /**
     * 批量导入
     *
     * @param vos          批量插入VO
     * @param securityUser 用户
     */
    @Override
    public List<RuleProhibit> batchInsert(List<RuleProhibitImportVo> vos, SecurityUser securityUser) {
        List<RuleProhibit> ruleProhibits = BeanUtil.convertVos(vos, RuleProhibit.class);
        List<RuleProhibit> insertList = Lists.newArrayList();
        // 批量插入数据
        for (RuleProhibit ruleProhibit : ruleProhibits) {
            if (StringUtils.isBlank(ruleProhibit.getShowName()) && StringUtils.isBlank(ruleProhibit.getContentTypes())
                    && StringUtils.isBlank(ruleProhibit.getDirector()) && StringUtils.isBlank(ruleProhibit.getReleaseYear())
                    && StringUtils.isBlank(ruleProhibit.getKpeople()) && StringUtils.isBlank(ruleProhibit.getOriginalCountry())
                    && StringUtils.isBlank(ruleProhibit.getPgmCategory())) {
                continue;
            }
            // 校验是否重复
            if (StringUtils.isBlank(ruleProhibit.getShowName())) {
                throw new CommonResponseException("违禁规则名称不能为空或空格,规则添加失败");
            }
            if (StringUtils.isBlank(ruleProhibit.getContentTypes())) {
                throw new CommonResponseException("违禁规则节目类型不能为空或空格,规则添加失败");
            }
            if ("单集".equals(ruleProhibit.getContentTypes())) {
                ruleProhibit.setContentType(ContentTypeEnum.FILM.getValue());
            } else if ("剧集".equals(ruleProhibit.getContentTypes())) {
                ruleProhibit.setContentType(ContentTypeEnum.TELEPLAY.getValue());
            } else {
                throw new CommonResponseException("违禁规则节目类型错误,规则添加失败");
            }
            boolean exist = isExists(ruleProhibit);
            if (exist) {
                throw new CommonResponseException("当前违禁规则存在重复,规则添加失败");
            }
            // 违禁规则媒资分类ID
            if (StringUtils.isNotBlank(ruleProhibit.getPgmCategory())) {
                SysDictionaryItem one = getDictionaryItemObj(ruleProhibit.getPgmCategory(), DictionaryBaseConstants.PGM_CATEGORY);
                if (null == one) {
                    throw new CommonResponseException("媒资分类:" + ruleProhibit.getPgmCategory() + "不存在,规则添加失败");
                }
                ruleProhibit.setPgmCategoryId(one.getId());
            }
            // 产地id
            if (StringUtils.isNotBlank(ruleProhibit.getOriginalCountry())) {
                SysDictionaryItem country = getDictionaryItemObj(ruleProhibit.getOriginalCountry(), DictionaryBaseConstants.ORIGINAL_COUNTRY);
                if (null == country) {
                    throw new CommonResponseException("产地名称:" + ruleProhibit.getOriginalCountry() + "不存在,规则添加失败");
                }
                ruleProhibit.setOriginalCountryId(country.getId());
            }
            // 校验年份
            if (StringUtils.isNotBlank(ruleProhibit.getReleaseYear())) {
                checkReleaseYear(ruleProhibit.getReleaseYear());
            }

            ruleProhibit.setCode(UUID.nextSnow().toString());
            if (null != securityUser) {
                ruleProhibit.setCreatorName(securityUser.getName());
            }
            insertList.add(ruleProhibit);
        }
        ruleProhibits.clear();
        Integer res = EasyExcelUtil.countMap.get() + insertList.size();
        EasyExcelUtil.countMap.set(res);
        return insertList;
    }

    private void checkReleaseYear(String releaseYear) {
        if (4 != releaseYear.length()) {
            throw new CommonResponseException("年份格式:" + releaseYear + "不正确,规则添加失败");
        }
        try {
            Integer integer = Integer.valueOf(releaseYear);
        } catch (NumberFormatException e) {
            throw new CommonResponseException("年份格式:" + releaseYear + "不正确,规则添加失败");
        }
    }

    @Override
    public Boolean batchSave(@Valid RuleProhibitListVo ruleProhibitListVo) {
        int insert = 0;
        for (RuleProhibit ruleProhibit : ruleProhibitListVo.getList()) {
            ruleProhibit.setId(null);
            Boolean aBoolean = saveAfterCheck(ruleProhibit);
            if (aBoolean) {
                insert += 1;
            }
        }
        return insert > 0;
    }

    @Override
    public IPage<RuleProhibit> selectPage(Page page, RuleProhibitDto ruleProhibit) {
        if (ruleProhibit.getShowName() != null) {
            if (ruleProhibit.getShowName().contains("\n")) {
                //防止name为空字符串以及只有回车的情况  进行再一次校验
                ruleProhibit.setShowName(ruleProhibit.getShowName().trim());
                if (com.pukka.iptv.common.core.util.StringUtils.isNotEmpty(ruleProhibit.getShowName())) {
                    String[] names = CommonUtils.getNames(ruleProhibit.getShowName());
                    if (names.length > 0) {
                        ruleProhibit.setNameList(names);
                        ruleProhibit.setLikeOrinFlag(2);
                    }
                }
            } else {
                String[] names = CommonUtils.getNames(ruleProhibit.getShowName());
                if (names != null && names.length > 0) {
                    ruleProhibit.setNameList(names);
                    ruleProhibit.setLikeOrinFlag(1);
                }
            }
        }

        Page<RuleProhibit> pages = baseMapper.selectListPage(page, ruleProhibit);
        return pages;
    }

    private SysDictionaryItem getDictionaryItemObj(String str, String dictionaryBase) {
        SysDictionaryBase sysDictionaryBase = redisService.getCacheMapValue(RedisKeyConstants.SYS_DICTIONARY_BASE_KEY, dictionaryBase);
        SysDictionaryItem sysDictionaryItem = redisService.getCacheMapValue(RedisKeyConstants.SYS_DICTIONARY_ITEM_KEY, StringUtils.joinWith(":", sysDictionaryBase.getId(), str));
        if (sysDictionaryItem == null) {
            if (!dictionaryRegisterFlag) {
                // 缓存里没有 根据策略 不注册返回空
                return null;
            }
            SysDictionaryItem item = new SysDictionaryItem();
            item.setDictionaryBaseId(sysDictionaryBase.getId());
            item.setName(str);
            return saveToCacheAndDB(item);
        } else {
            return sysDictionaryItem;
        }
    }

    private SysDictionaryItem saveToCacheAndDB(SysDictionaryItem entity) {
        try {
            if (sysDictionaryItemService.save(entity)) {
                cacheService.cacheDictionaryItem(Collections.singletonList(entity));
                return entity;
            }
        } catch (Exception e) {
            throw new CommonResponseException("redis异常:" + e.getMessage());
        }

        return null;
    }

    /**
     * 移除map中的value空值
     *
     * @param ruleProhibit
     * @return
     */
    public RuleProhibit removeEmptyValue(RuleProhibit ruleProhibit) {
        try {
            Map<String, Object> map = JSON.parseObject(JSON.toJSONString(ruleProhibit));
            Set<String> set = map.keySet();
            for (Iterator<String> iterator = set.iterator(); iterator.hasNext(); ) {
                String key = iterator.next();
                Object values = map.get(key);
                if (values instanceof String) {
                    values = ((String) values).trim();
                    if (values.equals(SymbolConstant.EMPTY)) {
                        values = null;
                    }
                }
                map.put(key, values);
            }
            RuleProhibit entity = JSON.parseObject(JSON.toJSONString(map), RuleProhibit.class);
            BeanUtils.copyProperties(entity,ruleProhibit);
        } catch (Exception exception) {
            log.error("移除map中的value空值 -----> 失败,错误信息:{}", exception);
        }
        return ruleProhibit;
    }

    /**
     * 导入当前记录是否存在
     *
     * @param ruleProhibit
     * @return
     */
    private boolean isExists(RuleProhibit ruleProhibit) {
        //移除空值
        removeEmptyValue(ruleProhibit);
        // 校验是否重复
        LambdaQueryWrapper<RuleProhibit> lambdaQuery =
                Wrappers.lambdaQuery(RuleProhibit.class);
        if (ObjectUtils.isEmpty(ruleProhibit) || com.baomidou.mybatisplus.core.toolkit.StringUtils.isBlank(ruleProhibit.getShowName())) {
            log.warn("违禁规则名称不能为空或空格,message:{}", ruleProhibit);
            throw new CommonResponseException("违禁规则名称不能为空或空格,规则添加失败");
        }
        lambdaQuery.eq(RuleProhibit::getShowName, ruleProhibit.getShowName());
        if (Objects.isNull(ruleProhibit.getKpeople())) {
            lambdaQuery.isNull(RuleProhibit::getKpeople);
        } else {
            lambdaQuery.eq(RuleProhibit::getKpeople, SafeUtil.getString(ruleProhibit.getKpeople()));
        }
        if (Objects.isNull(ruleProhibit.getDirector())) {
            lambdaQuery.isNull(RuleProhibit::getDirector);
        } else {
            lambdaQuery.eq(RuleProhibit::getDirector, SafeUtil.getString(ruleProhibit.getDirector()));
        }
        if (Objects.isNull(ruleProhibit.getReleaseYear())) {
            lambdaQuery.isNull(RuleProhibit::getReleaseYear);
        } else {
            lambdaQuery.eq(RuleProhibit::getReleaseYear, SafeUtil.getString(ruleProhibit.getReleaseYear()));
        }
        if (Objects.isNull(ruleProhibit.getContentType())) {
            lambdaQuery.isNull(RuleProhibit::getContentType);
        } else {
            lambdaQuery.eq(RuleProhibit::getContentType, ruleProhibit.getContentType());
        }
        if (Objects.isNull(ruleProhibit.getPgmCategoryId())) {
            lambdaQuery.isNull(RuleProhibit::getPgmCategoryId);
        } else {
            lambdaQuery.eq(RuleProhibit::getPgmCategoryId, ruleProhibit.getPgmCategoryId());
        }
        if (Objects.isNull(ruleProhibit.getOriginalCountryId())) {
            lambdaQuery.isNull(RuleProhibit::getOriginalCountryId);
        } else {
            lambdaQuery.eq(RuleProhibit::getOriginalCountryId, ruleProhibit.getOriginalCountryId());
        }
        if (Objects.nonNull(ruleProhibit.getId())) {
            lambdaQuery.ne(RuleProhibit::getId, ruleProhibit.getId());
        }
        lambdaQuery.last(" limit 1");
        RuleProhibit prohibit = baseMapper.selectOne(lambdaQuery);
        return ObjectUtils.isNotEmpty(prohibit);
    }
}
