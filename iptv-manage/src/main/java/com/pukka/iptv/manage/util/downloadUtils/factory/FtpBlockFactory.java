package com.pukka.iptv.manage.util.downloadUtils.factory;

import com.pukka.iptv.manage.util.downloadUtils.config.FtpClientProperties;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.net.ftp.FTPClient;
import org.apache.commons.net.ftp.FTPReply;
import org.apache.commons.pool.BaseObjectPool;
import org.apache.commons.pool2.PooledObject;
import org.springframework.util.ObjectUtils;

import java.io.BufferedInputStream;
import java.io.IOException;
import java.io.InputStream;
import java.io.OutputStream;
import java.util.concurrent.ArrayBlockingQueue;
import java.util.concurrent.BlockingQueue;
import java.util.concurrent.TimeUnit;

/**
 * @author: chiron
 * Date: 2022/5/20 10:51
 * Description: FTP阻塞缓存队列
 */
@Slf4j
public class FtpBlockFactory extends BaseObjectPool<FTPClient> {

    private FtpClientProperties config;

    private final BlockingQueue<FTPClient> temporaryQueue;
    private final FtpClientTemplate ftpClientTemplate;

    public FtpBlockFactory(FtpClientProperties ftpClientProperties,FtpClientTemplate ftpClientTemplate) {
        this.config = ftpClientProperties;
        this.ftpClientTemplate = ftpClientTemplate;
        temporaryQueue = new ArrayBlockingQueue<>(config.getTempPoolSize());
        initPool(config.getTempPoolSize());
    }

    /**
     * 初始化连接池，需要注入一个工厂来提供FTPClient实例
     *
     * @param maxPoolSize 最大连接数
     */
    private void initPool(int maxPoolSize) {
        try {
            for (int i = 0; i < maxPoolSize; i++) {
                // 往池中添加对象
                // 插入对象到队列
                temporaryQueue.offer(ftpClientTemplate.create(), 3, TimeUnit.SECONDS);
            }
        } catch (Exception exception) {
            log.error("init temporary queue error:{}", exception);
        }
    }

    @Override
    public FTPClient borrowObject() throws Exception {
        FTPClient client = temporaryQueue.take();
        if (ObjectUtils.isEmpty(client)) {
            //创建对象
            client = ftpClientTemplate.create();
        }
        PooledObject<FTPClient> ftpClientPooled = ftpClientTemplate.wrap(client);
        if (!ftpClientTemplate.validateObject(ftpClientPooled)) {
            // 对无效的对象进行处理
            invalidateObject(client);
            // 创建新的对象
            client = ftpClientTemplate.create();
        }
        return client;
    }

    @Override
    public void returnObject(FTPClient client) throws Exception {
        try {
            long timeout = 3L;
            if (client != null && !temporaryQueue.offer(client, timeout, TimeUnit.SECONDS)) {
                ftpClientTemplate.destroyObject(ftpClientTemplate.wrap(client));
            }
        } catch (InterruptedException e) {
            log.error("return ftp client interrupted ...{}", e);
        }
    }

    @Override
    public void invalidateObject(FTPClient client) throws Exception {
        try {
            client.changeWorkingDirectory("/");
        } catch (IOException e) {
            e.printStackTrace();
        } finally {
            temporaryQueue.remove(client);
        }
    }

    /**
     * 检查FTPClient实例是否存在
     *
     * @return
     */
    public boolean checkFTPClientObject() {
        if (org.apache.commons.lang3.ObjectUtils.isNotEmpty(temporaryQueue)) {
            if (temporaryQueue.size() > 0) {
                return true;
            }
        } else {
            log.warn("FTPClient is Empty");
        }
        return false;
    }

    /**
     * 写入文件流至浏览器端
     *
     * @param ftpClient
     * @param remotePath
     * @param out
     * @param isContinue
     * @return
     */
    public Boolean streamTrans(FTPClient ftpClient, String remotePath, OutputStream out, Boolean isContinue) {
        InputStream inputStream = null;
        BufferedInputStream bufferedInputStream = null;
        try {
            int circle = 10;
            byte[] buf = new byte[1024];
            int len = 0;
            // 验证FTP服务器是否登录成功
            int replyCode = ftpClient.getReplyCode();
            if (!FTPReply.isPositiveCompletion(replyCode)) {
                log.warn("ftpServer refused connection, replyCode:{}", replyCode);
                return false;
            }
            inputStream = ftpClient.retrieveFileStream(remotePath);
            bufferedInputStream = new BufferedInputStream(inputStream);
            if (isContinue) {
                int count = 0;
                while (bufferedInputStream != null && (len = bufferedInputStream.read(buf)) > 0) {
                    if (count >= circle) {
                        out.write(buf, 0, len);
                    }
                    count++;
                }
                out.flush();
                out.close();
            } else {
                for (int i = 0; i < circle; i++) {
                    len = bufferedInputStream.read(buf);
                    out.write(buf, 0, len);
                    if (len < 1) {
                        break;
                    }
                }
            }
            //ftpClient.logout();
            return true;
        } catch (Exception exception) {
            log.error("下载视频介质 -----> retrieve file failure!,错误信息:{}", exception);
            return false;
        } finally {
            if (bufferedInputStream != null) {
                try {
                    bufferedInputStream.close();
                } catch (IOException e) {
                    log.error("下载视频介质 -----> 关闭buffer流失败,错误信息:{}", e);
                }
            }
            if (inputStream != null) {
                try {
                    inputStream.close();
                } catch (IOException e) {
                    log.error("下载视频介质 -----> 关闭inputStream流失败,错误信息:{}", e);
                }
            }
        }
    }

    /**
     * 针对平台业务:
     * 采用临时节点进行数据传输下载
     * 下载固定大小后释放链接
     * 默认大小：8Kb
     *
     * @param isContinue   是否继续
     * @param remotePath   远程路径
     * @param outputStream 字节流对象
     * @return
     */
    public boolean blockStrategy(Boolean isContinue, String remotePath, OutputStream outputStream) {
        if (isContinue) {

        }
        try {

        } catch (Exception exception) {

        } finally {

        }


        return false;
    }

}