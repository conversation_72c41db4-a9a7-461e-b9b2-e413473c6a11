package com.pukka.iptv.manage.service.sys.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.pukka.iptv.common.base.enums.StatusEnum;
import com.pukka.iptv.common.data.model.sys.SysCpContentProvider;
import com.pukka.iptv.manage.mapper.sys.SysCpContentProviderMapper;
import com.pukka.iptv.manage.service.sys.SysCpContentProviderService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 *
 * @author: chenyudong
 * @date: 2021-12-2 15:04:05
 */

@Service
public class SysCpContentProviderServiceImpl extends ServiceImpl<SysCpContentProviderMapper, SysCpContentProvider> implements SysCpContentProviderService {

    @Autowired
    private SysCpContentProviderMapper sysCpContentProviderMapper;

    @Override
    public List<SysCpContentProvider> getByCpId(Long id){
        QueryWrapper<SysCpContentProvider> wrappers = Wrappers.<SysCpContentProvider>query().eq("status", StatusEnum.COME.getCode()).eq("cp_id",id);
        List<SysCpContentProvider> list = sysCpContentProviderMapper.selectList(wrappers);
        return list;
    }

    @Override
    public boolean removeById(Long id){
        SysCpContentProvider entity = new SysCpContentProvider();
        entity.setId(id);
        entity.setStatus(StatusEnum.LOSE.getCode());
        sysCpContentProviderMapper.updateById(entity);
        return true;
    }
}


