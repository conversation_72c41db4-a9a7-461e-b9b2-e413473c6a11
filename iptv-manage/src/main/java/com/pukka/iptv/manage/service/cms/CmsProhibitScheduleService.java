package com.pukka.iptv.manage.service.cms;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.pukka.iptv.common.data.model.cms.CmsProhibitSchedule;
import com.pukka.iptv.common.data.model.cms.CmsProhibitScheduleDTO;
import com.pukka.iptv.common.data.vo.IdList;

import java.util.List;
import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;

/**
 * <AUTHOR>
 * @description 针对表【cms_prohibit_schedule(违禁节目单)】的数据库操作Service
 * @createDate 2023-11-17 14:32:11
 */
public interface CmsProhibitScheduleService extends IService<CmsProhibitSchedule> {

    /**
     * 查询节目库列表
     *
     * @param scheduleDTO
     * @return
     */
    IPage<CmsProhibitSchedule> selectList(Page page, CmsProhibitScheduleDTO scheduleDTO);

    /**
     * 导出节目单库
     *
     * @param idList
     * @param response
     * @return
     */
    Object exportList(IdList idList, HttpServletResponse response);

    /**
     * 删除指定id的数据
     *
     * @param idList
     */
    void delete(@Valid IdList idList);


    /**
     * 删除指定id的数据
     *
     * @param cmsProhibitSchedules
     */
    void save(List<CmsProhibitSchedule> cmsProhibitSchedules);
}
