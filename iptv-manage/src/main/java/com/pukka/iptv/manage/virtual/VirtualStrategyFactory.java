package com.pukka.iptv.manage.virtual;

import com.pukka.iptv.common.base.enums.ActionEnums;
import org.springframework.beans.BeansException;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.context.ApplicationContext;
import org.springframework.context.ApplicationContextAware;
import org.springframework.stereotype.Component;

import java.io.Serializable;
import java.util.EnumMap;
import java.util.Map;

/**
 * <AUTHOR>
 * @description: 发布策略处理接口
 * @create 2021-08-30
 */
@Component
public class VirtualStrategyFactory implements InitializingBean, ApplicationContextAware {

    private static final Map<ActionEnums, IVirtualStrategyHandler<Serializable>> VIRTUAL_STRATEGY_HANDLER_MAP = new EnumMap<>(ActionEnums.class);

    private ApplicationContext appContext;


    public IVirtualStrategyHandler<Serializable> getHandler(ActionEnums actionEnums) {
        return VIRTUAL_STRATEGY_HANDLER_MAP.get(actionEnums);
    }


    @Override
    public void afterPropertiesSet() throws Exception {
        appContext.getBeansOfType(IVirtualStrategyHandler.class)
                .values()
                .forEach(virtualStrategyHandler -> VIRTUAL_STRATEGY_HANDLER_MAP.put(virtualStrategyHandler.getContentType(), virtualStrategyHandler));
    }

    @Override
    public void setApplicationContext(ApplicationContext applicationContext) throws BeansException {
        appContext = applicationContext;
    }
}
