package com.pukka.iptv.manage.rule.handle;

import com.baomidou.mybatisplus.core.toolkit.ObjectUtils;
import com.pukka.iptv.common.base.enums.ContentTypeEnum;
import com.pukka.iptv.common.data.model.copyright.RuleProhibit;
import com.pukka.iptv.common.data.vo.copyright.RuleProhibitVo;
import com.pukka.iptv.manage.config.RuleProhibitConfig;
import com.pukka.iptv.manage.rule.match.IllegalWordsSearch;
import com.pukka.iptv.manage.rule.match.LevenshteinMatch;
import com.pukka.iptv.manage.rule.match.base.IllegalWordsSearchResult;
import com.pukka.iptv.manage.rule.strategy.IRuleStrategyHandler;
import com.pukka.iptv.manage.rule.util.RuleProhibitUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;

import javax.annotation.PostConstruct;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * @Author: chiron
 * @Date: 2022/08/03/09:18
 * @Description:
 */
@Slf4j
public class AbstractProhibitRuleHandle implements IRuleStrategyHandler<RuleProhibit> {

    public static Integer proportionValue = 80;

    public static Integer ratio = 0;

    @Autowired
    private RuleProhibitUtil ruleProhibitUtil;

    @Autowired
    private RuleProhibitConfig ruleProhibitConfig;

    @Autowired
    private IllegalWordsSearch illegalWordsSearch;

    @Override
    public ContentTypeEnum getContentType() {
        return null;
    }

    @PostConstruct
    public void init() {
        getRatio();
        getProportion();
    }

    /**
     * 获取规则占比
     *
     * @return
     */
    public Integer getProportion() {
        Integer proportion = ruleProhibitConfig.getProportion();
        if (org.apache.commons.lang3.ObjectUtils.isNotEmpty(proportion)) {
            proportionValue = proportion;
        }
        return proportionValue;
    }

    /**
     * 获取名称占比
     *
     * @return
     */
    public Integer getRatio() {
        if (ratio > 0) {
            return ratio;
        }
        int tmp = proportionValue - (100 - ruleProhibitConfig.getShowName());
        if (tmp < 0) {
            return ratio;
        }
        ratio = 100 - (tmp * 100 / ruleProhibitConfig.getShowName());
        return ratio;
    }

    /**
     * 通用整合规则集合
     *
     * @param ruleProhibitList
     * @return
     */
    public Map<String, RuleProhibit> integrationCollection(List<RuleProhibit> ruleProhibitList) {
        if (ObjectUtils.isEmpty(ruleProhibitList) || ruleProhibitList.size() < 0) {
            log.warn("AbstractProhibitRuleHandle -----> 传入实体集合为空!");
            return null;
        }
        Map<String, RuleProhibit> ruleProhibitMap = ruleProhibitList.stream().collect
                (Collectors.toMap(ruleProhibit -> ruleProhibit.getShowName(), ruleProhibit -> ruleProhibit, (key1, key2) -> key2));
        if (ObjectUtils.isEmpty(ruleProhibitMap) || ruleProhibitMap.size() < 0) {
            log.warn("AbstractProhibitRuleHandle -----> 实体集合转换字典为空!");
            return null;
        }
        return ruleProhibitMap;
    }

    @Override
    public Integer execute(List<RuleProhibit> ruleProhibitList, RuleProhibitVo entity) {
        int result = ruleProhibitConfig.getContentType();
        try {
            Map<String, RuleProhibit> handle = handle(ruleProhibitList);
            if (ObjectUtils.isEmpty(handle) || handle.size() < 1) {
                log.warn("规则处理 -----> 获取规则字典为空");
                return result;
            }
            //违禁规则实体信息补全
            Boolean aBoolean = ruleProhibitUtil.ruleProhibitDataCompletion(entity);
            //将名称整合为违禁片单集合
            List<String> keyArray = new ArrayList<>(handle.keySet());
            illegalWordsSearch.setKeywords(keyArray);
            IllegalWordsSearchResult first = illegalWordsSearch.findFirst(entity.getContentName().trim());
            if (ObjectUtils.isNotEmpty(first)) {
                int levenshtein = LevenshteinMatch.getLevenshtein(first.Keyword, first.MatchKeyword);
                if (ObjectUtils.isNotEmpty(ratio) && levenshtein < ratio) {
                    log.info("规则处理 -----> 当前媒资匹配规则名称:{},匹配违禁规则名:{}, 占比为:{},小于最小占比值:{},跳出违禁片检测,msg:{}"
                            , entity.getContentName(), first.MatchKeyword, levenshtein, ratio, entity);
                    return result;
                }
                result += levenshtein * ruleProhibitConfig.getShowName() / 100;
                RuleProhibit ruleProhibit = handle.get(first.MatchKeyword);
                //填充违禁规则信息到违禁片实体
                entity.setCode(ruleProhibit.getCode()).setShowName(ruleProhibit.getShowName());
                if (ObjectUtils.isEmpty(ruleProhibit.getPgmCategoryId()) || ObjectUtils.isEmpty(entity.getPgmCategoryId()) || ruleProhibit.getPgmCategoryId().equals(entity.getPgmCategoryId())) {
                    result += ruleProhibitConfig.getPgmCategory();
                }
                if (ObjectUtils.isEmpty(ruleProhibit.getKpeople()) || ObjectUtils.isEmpty(entity.getKpeople()) || ruleProhibit.getKpeople().equals(entity.getKpeople().trim())) {
                    result += ruleProhibitConfig.getKpeople();
                }
                if (ObjectUtils.isEmpty(ruleProhibit.getDirector()) || ObjectUtils.isEmpty(entity.getDirector()) || ruleProhibit.getDirector().equals(entity.getDirector().trim())) {
                    result += ruleProhibitConfig.getDirector();
                }
                if (ObjectUtils.isEmpty(ruleProhibit.getOriginalCountryId()) || ObjectUtils.isEmpty(entity.getOriginalCountryId()) || ruleProhibit.getOriginalCountryId().equals(entity.getOriginalCountryId())) {
                    result += ruleProhibitConfig.getOriginalCountry();
                }
                if (ObjectUtils.isEmpty(ruleProhibit.getReleaseYear()) || ObjectUtils.isEmpty(entity.getReleaseYear()) || ruleProhibit.getReleaseYear().equals(entity.getReleaseYear())) {
                    result += ruleProhibitConfig.getReleaseYear();
                }
                log.info("规则处理 -----> 当前媒资匹配规则名称:{},匹配违禁规则名:{}, 最终权重为:{},违禁片检测完毕,msg:{}"
                        , entity.getContentName(), first.MatchKeyword, result, entity);
                return result;
            }
            log.info("规则处理 -----> 当前媒资匹配规则名称:{},未匹配到违禁规则,跳出违禁片检测,msg:{}"
                    , entity.getContentName(), entity);
        } catch (Exception exception) {
            log.info("规则处理 -----> 当前媒资匹配规则名称:{},msg:{},违禁片检测失败,错误信息:{}"
                    , entity.getContentName(), entity, exception);
        }
        return result;
    }

    public Map<String, RuleProhibit> handle(List<RuleProhibit> ruleProhibitList) {
        return null;
    }
}
