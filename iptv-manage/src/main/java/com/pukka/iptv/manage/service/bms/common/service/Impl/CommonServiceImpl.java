package com.pukka.iptv.manage.service.bms.common.service.Impl;

import cn.hutool.core.collection.CollectionUtil;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.IService;
import com.pukka.iptv.common.base.constant.PublishParamTypeConstants;
import com.pukka.iptv.common.base.enums.ActionEnums;
import com.pukka.iptv.common.base.enums.ContentTypeEnum;
import com.pukka.iptv.common.base.enums.ContentTypeItemEnum;
import com.pukka.iptv.common.base.enums.SeriesFlagEnum;
import com.pukka.iptv.common.base.exception.CommonResponseException;
import com.pukka.iptv.common.base.vo.CommonResponse;
import com.pukka.iptv.common.core.util.SpringUtils;
import com.pukka.iptv.common.data.model.OutParamExpand;
import com.pukka.iptv.common.data.model.bms.BmsContent;
import com.pukka.iptv.common.data.model.bms.BmsPicture;
import com.pukka.iptv.common.data.model.bms.BmsProgram;
import com.pukka.iptv.common.data.model.cms.CmsProgram;
import com.pukka.iptv.common.data.model.cms.CmsSeries;
import com.pukka.iptv.manage.service.bms.*;
import com.pukka.iptv.manage.service.bms.common.config.PriorityConfig;
import com.pukka.iptv.manage.service.bms.common.model.PriorityAction;
import com.pukka.iptv.manage.service.bms.common.service.CommonService;
import com.pukka.iptv.manage.service.cms.CmsProgramService;
import com.pukka.iptv.manage.service.cms.CmsSeriesService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;

import java.lang.reflect.Field;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2021/9/22
 */
@Service
@Slf4j
public class CommonServiceImpl implements CommonService {

    @Autowired
    private PriorityConfig priorityConfig;


    /**
     * 根据媒资类型查询自己的bean
     *
     * @param contentType
     * @return
     */
    private PriorityPublishApi selectContent(int contentType) {
        switch (Objects.requireNonNull(ContentTypeEnum.getByValue(contentType))) {
            case CATEGORY:
                return SpringUtils.getBean(BmsCategoryService.class);
            case PACKAGE:
                return SpringUtils.getBean(BmsPackageService.class);
            case PACKAGE_SERIES:
                return SpringUtils.getBean(BmsPackageContentService.class);
            case CATEGORY_SERIES:
                return SpringUtils.getBean(BmsCategoryContentService.class);
            case SUBSET:
                return SpringUtils.getBean(BmsProgramService.class);
            case TELEPLAY:
            case FILM:
            case EPISODES:
                return SpringUtils.getBean(BmsContentService.class);
            default:
                throw new CommonResponseException("请选择优先发布类型");
        }
    }

    @Override
    public CommonResponse<Boolean> priortyPublish(List<Long> ids, Map<String, OutParamExpand> paramMap, List<Integer> contentType) {
        //根据内容类型找到自己的bean 默认取集合中第一个contentType
        setPriorityFlag();
        PriorityPublishApi publishService = this.selectContent(contentType.get(0));
        //调用自己的优先发布接口
        log.info("进行优先发布，优先发布等级:{},内容类型:{}，内容ids:{}", paramMap.get(PublishParamTypeConstants.PRIORITY), JSON.toJSON(contentType), JSON.toJSON(ids));
        CommonResponse<Boolean> response = publishService.priortyPublish(ids, paramMap);
        remove();
        return response;
    }

    //多态的做法 暂停
//    @Override
//    public Integer resetPublishStatus(List<Long> ids, List<UpdatePublishStatusInterface> list, IService<UpdatePublishStatusInterface> iService) {
//        LambdaQueryWrapper<UpdatePublishStatusInterface> wrapper =
//                Wrappers.lambdaQuery(UpdatePublishStatusInterface.class).select(UpdatePublishStatusInterface::getPublishStatus, UpdatePublishStatusInterface::getId)
//                        .in(UpdatePublishStatusInterface::getId, ids);
//
//        List<Map<String, Object>> list1 = iService.listMaps(wrapper);
//        LambdaTrans<UpdatePublishStatusInterface> lt = LambdaTrans.instance(UpdatePublishStatusInterface.class);
//        List<UpdatePublishStatusInterface> updateList = list1.stream().map(item -> {
//            Long id = lt.trans(item, UpdatePublishStatusInterface::getId, Long.class);
//            Integer status = lt.trans(item, UpdatePublishStatusInterface::getPublishStatus, Integer.class);
//            return updatePublishTrans(id, status);
//        }).collect(Collectors.toList());
//
//        // List<BmsContent> list = this.list(wrapper);
//        // List<BmsContent> updateList = list.stream().map(item -> updatePublishTrans(item, )).collect(Collectors.toList());
//        //step2.2更新对应内容的发布状态
//        return null;
//    }


    /*
     * @param contentType 6：直播   7：物理频道  17：频道图片 10：节目单
     * @param spId
     * @param spName
     * @param contentIds  bms表的主键ids
     * @param outParam    拓展参数
     * @param action      动作1：REGIST，2：UPDATE，3：DELETE
     * @return
     *//*
    @Override
    public CommonResponse<Boolean> sendOutPublish(Integer contentType, String spId, String spName, List<Long> contentIds, Integer action, Map<String, OutParamExpand> outParam) {
        String ids = StringUtils.join(contentIds, ",");
        OutPublish outPublish = new OutPublish();
        outPublish.setContentType(contentType);
        outPublish.setSpId(spId);
        if (StringUtils.isNotEmpty(spName)) {
            outPublish.setSpName(spName);
        }
        SecurityUser securityUser = JwtTokenUtil.getSecurityUser();
        if (ObjectUtil.isNotEmpty(securityUser)) {
            outPublish.setCreatorId(securityUser.getId() + "");
            outPublish.setCreatorName(securityUser.getName());
        }
        if (CollectionUtil.isNotEmpty(outParam)) {
            outPublish.setOutParam(outParam);
        }
        outPublish.setContentIds(ids);
        outPublish.setAction(action);
        CommonResponse<Boolean> publish = outPublishFeignClient.publish(outPublish);
        if (publish.getData()) {
            log.info("调用发布接口成功，发布类型：{}，发布媒资类型：{}，spId:{}，contentIds：{},拓展参数:{}", action, contentType, action, contentIds, JSON.toJSON(outParam));
        } else {
            log.error("调用发布接口失败，发布类型：{}，发布媒资类型：{}，spId:{}，contentIds：{},拓展参数:{},失败信息:{}", action, contentType, action, contentIds, JSON.toJSON(outParam), publish.getMessage());
        }
        return publish;
    }*/


    @Override
    public PriorityAction getPriorityByNacos(String table, ActionEnums actionType) {
        Map<String, Map<String, PriorityAction>> priority = priorityConfig.getPriority();
        return priority.get(table).get(actionType.getInfo());
    }

    @Lazy
    @Autowired
    private CmsProgramService cmsProgramService;
    @Autowired
    private BmsContentService bmsContentService;
    @Autowired
    private BmsPictureService bmsPictureService;
    @Lazy
    @Autowired
    private CmsSeriesService cmsSeriesService;

    @Autowired
    private BmsProgramService bmsProgramService;

    /**
     * 单集 修改cp信息
     * 1、根据code查询出id
     * 2、修改cms单集表中的cp信息
     * 3、根据cmsId查询出bms_content所有的媒资 并修改cp信息
     * 4、根据步骤3中查询出的id进行修改图片的cp信息
     * <p>
     * 剧集 修改cp信息
     * 1、根据code查询出id
     * 2、修改cms剧集表中的cp信息
     * 3、根据步骤1中查询出的id查询出所有的子集信息进行修改子集的cp信息
     * 4、根据步骤1中查询出的id查询出bms_content所有的媒资 并修改cp信息
     * 5、根据步骤3中查询出的子集id修改bms_program表中的子集的cp信息
     * 6、根据步骤4中查询出的剧集id进行修改图片的cp信息
     * 7、根据步骤5中查询出的子集id进行修改图片的cp信息
     *
     * @param codes
     * @param parameterMap
     * @param type
     */
    @Override
    public void updateCpDetail(List<String> codes, Map<String, Object> parameterMap, Integer type) {
        if (CollectionUtil.isNotEmpty(codes)) {
            if (type == 1) {
                LambdaQueryWrapper<CmsProgram> programWrapper = new LambdaQueryWrapper<>();
                programWrapper.in(CmsProgram::getCode, codes).eq(CmsProgram::getSeriesFlag, SeriesFlagEnum.SimpleSet.getValue());
                List<CmsProgram> cmsPrograms = cmsProgramService.list(programWrapper);
                updateCpDetail(cmsProgramService, cmsPrograms, parameterMap);

                if (CollectionUtil.isNotEmpty(cmsPrograms)) {
                    List<Long> cmsProgramIds = cmsPrograms.parallelStream().map(CmsProgram::getId).collect(Collectors.toList());
                    LambdaQueryWrapper<BmsContent> bmsProgramWrapper = new LambdaQueryWrapper<>();
                    bmsProgramWrapper.in(BmsContent::getCmsContentId, cmsProgramIds).in(BmsContent::getContentType, ContentTypeItemEnum.PROGRAM.getContentType(), ContentTypeItemEnum.FLOWER.getContentType());
                    List<BmsContent> bmsContents = bmsContentService.list(bmsProgramWrapper);
                    updateCpDetail(bmsContentService, bmsContents, parameterMap);

                    if (CollectionUtil.isNotEmpty(bmsContents)) {
                        List<Long> bmsProgramIds = bmsContents.parallelStream().map(BmsContent::getId).collect(Collectors.toList());
                        LambdaQueryWrapper<BmsPicture> bmsPictureLambdaQueryWrapper = new LambdaQueryWrapper<>();
                        bmsPictureLambdaQueryWrapper.in(BmsPicture::getBmsContentId, bmsProgramIds).in(BmsPicture::getContentType, ContentTypeItemEnum.PROGRAM.getContentType(), ContentTypeItemEnum.FLOWER.getContentType());
                        List<BmsPicture> bmsPictures = bmsPictureService.list(bmsPictureLambdaQueryWrapper);
                        updateCpDetail(bmsPictureService, bmsPictures, parameterMap);
                    }
                }

            }
            if (type == 2) {
                LambdaQueryWrapper<CmsSeries> seriesLambdaQueryWrapper = new LambdaQueryWrapper<>();
                seriesLambdaQueryWrapper.in(CmsSeries::getCode, codes);
                List<CmsSeries> cmsSeriess = cmsSeriesService.list(seriesLambdaQueryWrapper);
                updateCpDetail(cmsSeriesService, cmsSeriess, parameterMap);
                if (CollectionUtil.isNotEmpty(cmsSeriess)) {
                    List<Long> cmsSeriesIds = cmsSeriess.parallelStream().map(CmsSeries::getId).collect(Collectors.toList());
                    LambdaQueryWrapper<CmsProgram> cmsSeriesLambdaQueryWrapper = new LambdaQueryWrapper<>();
                    cmsSeriesLambdaQueryWrapper.in(CmsProgram::getSeriesId).eq(CmsProgram::getSeriesFlag, SeriesFlagEnum.Subset.getValue());
                    List<CmsProgram> cmsPrograms = cmsProgramService.listBySeriesIds(cmsSeriesIds);
                    updateCpDetail(cmsProgramService, cmsPrograms, parameterMap);
                    LambdaQueryWrapper<BmsContent> bmsContentLambdaQueryWrapper = new LambdaQueryWrapper<>();
                    bmsContentLambdaQueryWrapper.in(BmsContent::getCmsContentId, cmsSeriesIds).in(BmsContent::getContentType, ContentTypeItemEnum.SERIES3.getContentType(), ContentTypeItemEnum.SERIES4.getContentType());
                    List<BmsContent> bmsContents = bmsContentService.list(bmsContentLambdaQueryWrapper);
                    updateCpDetail(bmsContentService, bmsContents, parameterMap);
                    LambdaQueryWrapper<BmsPicture> bmsPictureLambdaQueryWrapper = new LambdaQueryWrapper<>();
                    List<BmsPicture> programPics = new ArrayList<>();
                    List<BmsPicture> seriesPics = new ArrayList<>();
                    if (CollectionUtil.isNotEmpty(bmsContents)) {
                        List<Long> bmsseriesIds = bmsContents.parallelStream().map(BmsContent::getId).collect(Collectors.toList());
                        bmsPictureLambdaQueryWrapper.in(BmsPicture::getBmsContentId, bmsseriesIds).in(BmsPicture::getContentType, ContentTypeItemEnum.SERIES3.getContentType(), ContentTypeItemEnum.SERIES4.getContentType());
                        seriesPics = bmsPictureService.list(bmsPictureLambdaQueryWrapper);
                    }

                    LambdaQueryWrapper<BmsProgram> bmsProgramLambdaQueryWrapper = new LambdaQueryWrapper<>();
                    bmsProgramLambdaQueryWrapper.in(BmsProgram::getCmsSeriesId, cmsSeriesIds);
                    List<BmsProgram> bmsPrograms = bmsProgramService.list(bmsProgramLambdaQueryWrapper);
                    updateCpDetail(bmsProgramService, bmsPrograms, parameterMap);
                    if (CollectionUtil.isNotEmpty(bmsPrograms)) {
                        bmsPictureLambdaQueryWrapper.clear();
                        List<Long> bmsprogramIds = bmsPrograms.parallelStream().map(BmsProgram::getId).collect(Collectors.toList());
                        bmsPictureLambdaQueryWrapper.in(BmsPicture::getBmsContentId, bmsprogramIds).eq(BmsPicture::getContentType, ContentTypeItemEnum.SUBSET.getContentType());
                        programPics = bmsPictureService.list(bmsPictureLambdaQueryWrapper);
                    }
                    if (CollectionUtil.isNotEmpty(programPics) || CollectionUtil.isNotEmpty(seriesPics)) {
                        List<BmsPicture> pictures = new ArrayList<>(programPics.size() + seriesPics.size());
                        pictures.addAll(programPics);
                        pictures.addAll(seriesPics);
                        updateCpDetail(bmsPictureService, pictures, parameterMap);
                    }

                }

            }
        }
    }

    private void setfeild(String key, Object value, Class<?> classType, Object c) {
        try {
            if (key.contains("Id")) {
                Field field = classType.getDeclaredField(key);
                field.setAccessible(true);
                field.set(c, Long.valueOf(value.toString()));
            } else {
                Field field = classType.getDeclaredField(key);
                field.setAccessible(true);
                field.set(c, value);
            }
        } catch (NoSuchFieldException exception) {
            log.error("忽略属性：{}", key);
        } catch (IllegalAccessException e) {
            e.printStackTrace();
        }
    }

    private <T> boolean updateCpDetail(IService<T> service, Collection<T> contents, Map<String, Object> parameterMap) {
        if (CollectionUtil.isEmpty(contents)) return false;
        contents.parallelStream().forEach(c -> {
            Class<?> classType = c.getClass();
            parameterMap.forEach((key, value) -> {
                setfeild(key, value, classType, c);
            });
        });
        return service.updateBatchById(contents);
    }
}
