package com.pukka.iptv.manage.config;


import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

@Configuration
@ConfigurationProperties(prefix = "rule.config")
public class RuleProhibitConfig {
    /**
     * 占比
     */
    private Integer proportion;
    /**
     * 名称
     */
    private Integer showName = 0;

    private Integer contentType;

    private Integer pgmCategory;

    private Integer kpeople;

    private Integer director;

    private Integer originalCountry;

    private Integer releaseYear;

    public Integer getProportion() {
        return proportion;
    }

    public void setProportion(Integer proportion) {
        this.proportion = proportion;
    }

    public Integer getShowName() {
        return showName;
    }

    public void setShowName(Integer showName) {
        this.showName = showName;
    }

    public Integer getContentType() {
        return contentType;
    }

    public void setContentType(Integer contentType) {
        this.contentType = contentType;
    }

    public Integer getPgmCategory() {
        return pgmCategory;
    }

    public void setPgmCategory(Integer pgmCategory) {
        this.pgmCategory = pgmCategory;
    }

    public Integer getKpeople() {
        return kpeople;
    }

    public void setKpeople(Integer kpeople) {
        this.kpeople = kpeople;
    }

    public Integer getDirector() {
        return director;
    }

    public void setDirector(Integer director) {
        this.director = director;
    }

    public Integer getOriginalCountry() {
        return originalCountry;
    }

    public void setOriginalCountry(Integer originalCountry) {
        this.originalCountry = originalCountry;
    }

    public Integer getReleaseYear() {
        return releaseYear;
    }

    public void setReleaseYear(Integer releaseYear) {
        this.releaseYear = releaseYear;
    }
}
