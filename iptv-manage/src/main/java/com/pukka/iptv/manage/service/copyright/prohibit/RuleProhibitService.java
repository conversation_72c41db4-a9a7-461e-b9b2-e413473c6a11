package com.pukka.iptv.manage.service.copyright.prohibit;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.pukka.iptv.common.base.vo.SecurityUser;
import com.pukka.iptv.common.data.dto.RuleProhibitDto;
import com.pukka.iptv.common.data.model.copyright.RuleProhibit;
import com.pukka.iptv.common.data.vo.copyright.RuleProhibitImportVo;
import com.pukka.iptv.common.data.vo.copyright.RuleProhibitListVo;

import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import java.util.List;

/**
 * @Author: chiron
 * @Date: 2022/07/25/10:08
 * @Description: 违禁规则
 */

public interface RuleProhibitService extends IService<RuleProhibit> {

    /**
     * 检查并新增
     * @param ruleProhibit
     * @return
     */
    Boolean saveAfterCheck(RuleProhibit ruleProhibit);
    /**
     * 导出
     * @param ids
     * @return
     */
    Object exportInfo(List<Long> ids);

    /**
     * 下载模板
     * @param
     * @return
     */
    void download(HttpServletResponse response);

    /**
     * 修改
     * @param
     * @return
     */
    int updateRuleProhibit(@Valid RuleProhibit ruleProhibit);

    /**
     * 批量插入
     * @param vos 批量插入VO
     * @param securityUser 用户
     */
    List<RuleProhibit> batchInsert(List<RuleProhibitImportVo> vos, SecurityUser securityUser);

    /**
     * 批量插入
     * @param ruleProhibitListVo 批量插入VO
     */
    Boolean batchSave(@Valid RuleProhibitListVo ruleProhibitListVo);

    /**
     * 查询
     * @param page
     * @param ruleProhibit
     * @return
     */
    IPage<RuleProhibit> selectPage(Page page, RuleProhibitDto ruleProhibit);
}
