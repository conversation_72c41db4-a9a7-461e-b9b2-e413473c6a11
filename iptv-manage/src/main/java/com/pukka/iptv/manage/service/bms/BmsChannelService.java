package com.pukka.iptv.manage.service.bms;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.pukka.iptv.common.base.enums.ActionEnums;
import com.pukka.iptv.common.base.enums.StatusEnum;
import com.pukka.iptv.common.data.model.bms.BmsChannel;
import com.pukka.iptv.common.data.model.order.SubOrderObjectsEntity;
import com.pukka.iptv.common.data.params.SysAuthorizationParam;
import com.pukka.iptv.common.data.util.Tuple2;

import java.text.ParseException;
import java.util.List;
import java.util.Map;

/**
 * 频道
 *
 * <AUTHOR>
 * @email
 * @date 2021-08-27 14:45:33
 */
public interface BmsChannelService extends IService<BmsChannel>,BmsPublishParamApi
{
    boolean updatePublishStatus(List<SubOrderObjectsEntity> orderObjectsEntities, List<Long> spIdList);

    /**
     * 根据id获取到该频道的状态
     * 发布状态 1：待发布2：发布中3：发布成功4：发布失败5：待更新6：更新中7：更新失败8：回收中9：回收成功10：回收失败
     *
     * @param ids
     * @return
     */
    List<BmsChannel> getStatusByIds(List<Long> ids);

    /**
     * 频道发布
     *
     * @param bmsChannel
     * @param doSchedule           是否定时发布 1定时 2 立即
     * @param scheduleTime         定时发布时间
     * @param widthPhysicalChannel 是否发布物理频道  1 发布 2 不发布
     * @return
     */
    boolean channelPublish(List<BmsChannel> bmsChannels, Integer doSchedule, String scheduleTime, String widthPhysicalChannel) throws ParseException;


    /**
     * 频道回收
     *
     * @param bmsChannels
     * @return
     */
    String channelRollback(List<BmsChannel> bmsChannels);

    /**
     * 频道一键回收
     *
     * @param bmsChannels
     * @return
     */
    String channelRollbackAll(List<BmsChannel> bmsChannels);

    /**
     * 频道分页查询
     *
     * @param page       分页参数
     * @param bmsChannel
     * @param categoryId
     * @return
     */
    IPage<BmsChannel> getChannelsPage(Page<BmsChannel> page, BmsChannel bmsChannel, String categoryId, Integer status);

    /**
     * 重置发布状态
     *
     * @param idList
     * @return
     */
    Boolean resetPublishStatus(List<Long> idList);

    /**
     * 修改发布状态
     *
     * @param idList
     * @return
     */
    Boolean updatePublishStatus(List<Long> idList, Integer updatePublishStatus);



    /**
     * 设置频道生效/失效
     *
     * @param ids
     * @param status 0：失效 1：有效
     * @return
     */
    Boolean setStatus(List<Long> ids, StatusEnum status);

    /**
     * 根据栏目id查询未绑定的频道
     *
     * @param page
     * @param categoryId 栏目id
     * @return
     */
    IPage<BmsChannel> getNotBindChannelsPage(Page<BmsChannel> page, Long categoryId, Long spId, String channelName,Long cpId);

    /**
     * 逻辑频道发布/回收
     *
     * @param bmsChannels
     * @param picturess
     * @param actionEnums
     * @return
     */
    Boolean logicChannelPublish(List<BmsChannel> bmsChannels, Tuple2<List<Long>, Map<String, String>> picturess, ActionEnums actionEnums,Integer contentCode);

    /**
     * 查询授权频道列表
     *
     * @param page 分页参数
     * @param param 查询参数
     * @return
     */
    Page getAuthorizationChannelList(Page page,SysAuthorizationParam param);

    List<Long> getChannelByCmsContentId(List<Long> cmsIds,List<Long> spIds);

    List<BmsChannel> getChannelById(Long cmsIds, Long spIds);

    boolean deleteByCodeAndSp(List<String> delChannelEntities, List<Long> spIdList, boolean isRollback);
}

