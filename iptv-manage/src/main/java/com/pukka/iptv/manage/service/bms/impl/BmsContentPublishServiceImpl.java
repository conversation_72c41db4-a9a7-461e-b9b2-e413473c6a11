package com.pukka.iptv.manage.service.bms.impl;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.pukka.iptv.common.base.exception.CommonResponseException;
import com.pukka.iptv.common.core.util.DateUtils;
import com.pukka.iptv.common.core.util.StringUtils;
import com.pukka.iptv.common.data.model.bms.*;
import com.pukka.iptv.common.data.vo.bms.BmsContentPublishStatusVO;
import com.pukka.iptv.common.data.vo.bms.BmsContentVO;
import com.pukka.iptv.manage.mapper.bms.*;
import com.pukka.iptv.manage.service.bms.BmsContentPublishService;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 发布详情
 *
 * <AUTHOR>
 * @date 2021-09-03 12:02:57
 */
@Service
public class BmsContentPublishServiceImpl extends ServiceImpl<BmsContentMapper, BmsContent> implements BmsContentPublishService {

    @Autowired
    private BmsContentMapper bmsContentMapper;

    @Autowired
    private BmsCategoryContentMapper bmsCategoryContentMapper;

    @Autowired
    private BmsPackageContentMapper bmsPackageContentMapper;

    @Autowired
    private BmsProgramMapper bmsProgramMapper;

    @Autowired
    private BmsCategoryMapper bmsCategoryMapper;

    //父类id为0时终止递归
    private static final int Parent_Id = 0;

    @Override
    public Page<BmsContentVO> selectByPublishDetail(Page page, String id, String type) {
        //分页参数检查
        checkout(page, id, type);
        Page iPage = new Page(page.getCurrent(), page.getSize());
        LambdaQueryWrapper<BmsContent> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(BmsContent::getCmsContentId, id)
                .eq(BmsContent::getContentType, type);
        Page contentPage = bmsContentMapper.selectPage(iPage, wrapper);

        List<BmsContentVO> resultRecords = getBmsContentVOS(type, contentPage);
        contentPage.setRecords(resultRecords);
        return contentPage;
    }

    /**
     * 获取BmsContentVO列表
     * 栏目关系发布状态 categoryParam 查询补充
     * 产品包关系发布状态 packageParam 查询补充
     * @param type 内容类型
     * @param contentPage 内容页面
     * @return BmsContentVO列表
     */
    private List<BmsContentVO> getBmsContentVOS(String type, Page contentPage) {
        List records = contentPage.getRecords();
        List<BmsContentVO> resultRecords = new ArrayList<>();
        if (records != null && records.size() > 0) {
            //获取所有子集id
            List<Long> bmsContentIdList = (List<Long>) records.stream()
                    .map(record -> (record instanceof BmsContent) ? ((BmsContent) record).getId() : null)
                    .filter(Objects::nonNull)
                    .collect(Collectors.toList());

            //查询栏目的关联状态
            LambdaQueryWrapper<BmsCategoryContent> BmsCategoryContentWrapper = new LambdaQueryWrapper<>();
            BmsCategoryContentWrapper.in(BmsCategoryContent::getBmsContentId, bmsContentIdList)
                    .eq(BmsCategoryContent::getContentType, type)
                    .orderByDesc(BmsCategoryContent::getPublishTime);
            List<BmsCategoryContent> categoryList = bmsCategoryContentMapper.selectList(BmsCategoryContentWrapper);
            //以BmsContentId聚合分组 避免多次查询
            Map<Long, List<BmsCategoryContent>> categoryByContentIdGrouped = categoryList.stream()
                    .collect(Collectors.groupingBy(BmsCategoryContent::getBmsContentId));

            //查询产品包的关联状态
            LambdaQueryWrapper<BmsPackageContent> bmsPackageContentWrapper = new LambdaQueryWrapper<>();
            bmsPackageContentWrapper.in(BmsPackageContent::getBmsContentId, bmsContentIdList)
                    .eq(BmsPackageContent::getContentType, type)
                    .orderByDesc(BmsPackageContent::getPublishTime);

            List<BmsPackageContent> bmsPackageList = bmsPackageContentMapper.selectList(bmsPackageContentWrapper);
            //以BmsContentId聚合分组 避免多次查询
            Map<Long, List<BmsPackageContent>> packageByContentIdGrouped = bmsPackageList.stream()
                    .collect(Collectors.groupingBy(BmsPackageContent::getBmsContentId));
            //分别获取关联关系
            for (Object record : records) {
                BmsContentVO vo = new BmsContentVO();
                BeanUtils.copyProperties(record, vo);
                Long bmsContentId = ((BmsContent) record).getId();
                //查询栏目关联关系
                List<BmsCategoryContent> bmsCategoryContents = categoryByContentIdGrouped.get(bmsContentId);
                if (bmsCategoryContents != null && !bmsCategoryContents.isEmpty()) {
                    List<BmsContentPublishStatusVO> categoryParam = bmsCategoryContents.stream()
                            .map(content -> new BmsContentPublishStatusVO(content.getCategoryId(), content.getCategoryName(), content.getPublishStatus(),content.getPublishTime()))
                            .collect(Collectors.toList());

                    //颜色排序
                    List<BmsContentPublishStatusVO> categoryParamSortedList = categoryParam.stream()
                            .sorted(Comparator.comparingInt((BmsContentPublishStatusVO content) -> getColor(content.getPublishStatus()))
                                    .thenComparingInt(BmsContentPublishStatusVO::getPublishStatus))
                            .collect(Collectors.toList());
                    vo.setCategoryParam(categoryParamSortedList);
                }

                //查询产品包关联关系
                List<BmsPackageContent> bmsPackageContents = packageByContentIdGrouped.get(bmsContentId);
                if (bmsPackageContents != null && !bmsPackageContents.isEmpty()) {
                    List<BmsContentPublishStatusVO> packageParam = bmsPackageContents.stream()
                            .map(content -> new BmsContentPublishStatusVO(content.getPackageId(), content.getPackageName(), content.getPublishStatus(), DateUtil.format(content.getPublishTime(), DateUtils.YYYY_MM_DD_HH_MM_SS)))
                            .collect(Collectors.toList());

                    //颜色排序
                    List<BmsContentPublishStatusVO> packageParamSortedList = packageParam.stream()
                            .sorted(Comparator.comparingInt((BmsContentPublishStatusVO content) -> getColor(content.getPublishStatus()))
                                    .thenComparingInt(BmsContentPublishStatusVO::getPublishStatus))
                            .collect(Collectors.toList());
                    vo.setPackageParam(packageParamSortedList);
                }
                resultRecords.add(vo);
            }

        }
        return resultRecords;
    }
    /**
     * 分组颜色
     *
     * @param status
     * @return 组别
     */
    private int getColor(int status) {
        if (status == 1 || status == 9) {
            return 1; // 灰色
        } else if (status == 2 || status == 8 || status == 6) {
            return 2; // 黄色
        } else if (status == 4 || status == 7 || status == 10) {
            return 3; // 红色
        } else if (status == 3 || status == 5) {
            return 4; // 绿色
        } else {
            return 5; // 其他颜色
        }
    }

    /**
     * 所属栏目
     *
     * @param page
     * @param id
     * @return
     */

    @Override
    public Page<BmsCategoryContent> selectByCategory(Page<BmsCategoryContent> page, String id, String type) {
        //分页参数检查
        checkout(page, id, type);
        LambdaQueryWrapper<BmsCategoryContent> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(BmsCategoryContent::getBmsContentId, id)
                .eq(BmsCategoryContent::getContentType, type)
                .orderByDesc(BmsCategoryContent::getPublishTime);

        Page<BmsCategoryContent> categoryPage = bmsCategoryContentMapper.selectPage(page, wrapper);

        List<BmsCategoryContent> records = categoryPage.getRecords();

        for (BmsCategoryContent categoryContent : records) {
            //category表的ID
            List<String> categoryNameList = new ArrayList<>();
            fab(categoryNameList, categoryContent.getCategoryId());
            if (ObjectUtil.isNotEmpty(categoryNameList)) {
                String join = StringUtils.reversedJoin(categoryNameList, "-->");
                categoryContent.setCategoryName(join);
            }
        }

        return categoryPage;
    }


    /**
     * 所属产品包
     *
     * @param page
     * @param id
     * @return
     */
    @Override
    public Page<BmsPackageContent> selectByPackage(Page page, String id, String type) {
        //分页参数检查
        checkout(page, id, type);
        LambdaQueryWrapper<BmsPackageContent> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(BmsPackageContent::getBmsContentId, id)
                .eq(BmsPackageContent::getContentType, type)
                .orderByDesc(BmsPackageContent::getPublishTime);

        Page iPage = new Page(page.getCurrent(), page.getSize());
        return bmsPackageContentMapper.selectPage(iPage, wrapper);
    }


    /**
     * 子集发布详情
     *
     * @param page
     * @param id
     * @return
     */
    @Override
    public Page<BmsProgram> selectBySubset(Page page, String id) {
        if (page.getCurrent() < 0) {
            page.setCurrent(1);
        }
        if (page.getSize() < 0 || page.getSize() > 500) {
            page.setSize(20);
        }
        if (ObjectUtil.isEmpty(id)) {
            throw new CommonResponseException("参数不能为空");
        }
        //拿着子集的id去bms_program表中查 是否被授权过去了
//        List<BmsProgram> bmsPrograms = bmsProgramMapper.selectList(Wrappers.<BmsProgram>lambdaQuery().eq(BmsProgram::getCmsContentId, id));
        //如果被授权过去了 就拿到所属剧头的id
  /*      Long cmsSeriesId = null;
        if (ObjectUtil.isNotEmpty(bmsPrograms)) {
            for (BmsProgram bmsProgram : bmsPrograms) {
                //剧头id
                cmsSeriesId = bmsProgram.getCmsSeriesId();
            }
        }*/
        LambdaQueryWrapper<BmsProgram> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(BmsProgram::getCmsContentId, id);
        Page contentPage = bmsProgramMapper.selectPage(page, wrapper);
        return contentPage;
    }

    /**
     * 递归查出所有栏目的父栏目
     *
     * @param categoryId
     * @return
     */

    private void fab(List<String> categoryNameList, Long categoryId) {
        BmsCategory bmsCategory = bmsCategoryMapper.selectById(categoryId);
        categoryNameList.add(bmsCategory.getName());
        if (bmsCategory.getParentId() != Parent_Id && bmsCategory.getParentId() != null) {
            fab(categoryNameList, bmsCategory.getParentId());
        }
    }


    /**
     * 参数检查
     *
     * @param page
     * @param id
     * @param type
     */
    private void checkout(Page page, String id, String type) {
        if (page.getCurrent() < 0) {
            page.setCurrent(1);
        }
        if (page.getSize() < 0 || page.getSize() > 500) {
            page.setSize(20);
        }
        if (ObjectUtil.isEmpty(id) || ObjectUtil.isEmpty(type)) {
            throw new CommonResponseException("参数不能为空");
        }
    }

}
