package com.pukka.iptv.manage.service.bms;

/**
 * @Author: wz
 * @Date: 2021/11/26 15:07
 * @Description:
 */
public interface BmsSchedulePublishApi {
    /**
     * 是否是定时发布调用的标识
     */
     ThreadLocal<Boolean> IS_SCHEDULE_PUBLISH = new ThreadLocal<>();

    /**
     * 是否发布子集
     */
     ThreadLocal<Boolean> IS_PROGRAM_PUBLISH = new ThreadLocal<>();

    /**
     * 是否检测状态
     */
    ThreadLocal<Boolean> IS_CHECK_PUBLISH = new ThreadLocal<>();


    /**
     * 获取内部定时发布标识
     * @return
     */
    default boolean isSchedule() {
        Boolean flag = IS_SCHEDULE_PUBLISH.get();
        IS_SCHEDULE_PUBLISH.remove();
        return flag == null ? false : flag;
    }

    /**
     * 获取是否检测标识
     * @return
     */
    default boolean isCheck() {
        Boolean flag = IS_CHECK_PUBLISH.get();
        IS_CHECK_PUBLISH.remove();
        return flag == null ? false : flag;
    }

    /**
     * 获取内部定时发布标识
     * @return
     */
    default boolean isProgram() {
        Boolean flag = IS_PROGRAM_PUBLISH.get();
        IS_PROGRAM_PUBLISH.remove();
        return flag == null ? false : flag;
    }

    /**
     * 设置内部定时发布标识
     */
    default void setScheduleFlag() {
        IS_SCHEDULE_PUBLISH.set(true);
    }
    default void setProgramFlag() {
        IS_PROGRAM_PUBLISH.set(true);
    }
    default void setCheckFlag() {
        IS_CHECK_PUBLISH.set(true);
    }

}
