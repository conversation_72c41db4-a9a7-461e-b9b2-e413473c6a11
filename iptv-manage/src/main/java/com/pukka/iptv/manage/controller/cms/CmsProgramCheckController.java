package com.pukka.iptv.manage.controller.cms;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.pukka.iptv.common.base.exception.CommonResponseException;
import com.pukka.iptv.common.base.vo.CommonResponse;
import com.pukka.iptv.common.core.util.DateUtils;
import com.pukka.iptv.common.data.dto.CmsProgramDO;
import com.pukka.iptv.common.data.dto.CmsProgramEndDto;
import com.pukka.iptv.common.data.vo.cms.CmsProgramVO;
import com.pukka.iptv.common.log.annotation.SysLog;
import com.pukka.iptv.common.log.enums.OperateObjectEnum;
import com.pukka.iptv.common.log.enums.OperateTypeEnum;
import com.pukka.iptv.manage.service.cms.CmsProgramCheckService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.AllArgsConstructor;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;
@RestController
@AllArgsConstructor
@RequestMapping(value = "/cmsProgramCheck", produces = MediaType.APPLICATION_JSON_VALUE)
@Api(tags = "cmsProgramCheck管理")
public class CmsProgramCheckController {

    @Resource
    private CmsProgramCheckService cmsProgramCheckService;
    @ApiOperation(value = "自审单集列表")
    @PostMapping("/simpleset/selfList")
    public CommonResponse selfList( @RequestBody CmsProgramDO cmsProgramDO){
        Page page = new Page();
        page.setCurrent(cmsProgramDO.getCurrent());
        page.setSize(cmsProgramDO.getSize());
        if (!DateUtils.checkDataPattern(DateUtils.YYYY_MM_DD, cmsProgramDO.getStartTime(), cmsProgramDO.getEndTime())) {
            return CommonResponse.commonfail("日期格式错误");
        }
        return CommonResponse.success(cmsProgramCheckService.selfList(page,cmsProgramDO, cmsProgramDO.getStartTime(), cmsProgramDO.getEndTime(), true));
//        return CommonResponse.success(cmsProgramCheckService.selfList(page,cmsProgram, startTime, endTime));
    }

    @ApiOperation(value = "自审子集列表")
    @PostMapping("/subset/selfList")
    public CommonResponse subsetSelfList( @RequestBody  CmsProgramDO cmsProgramDO){
        Page page = new Page();
        page.setCurrent(cmsProgramDO.getCurrent());
        page.setSize(cmsProgramDO.getSize());
        if (!DateUtils.checkDataPattern(DateUtils.YYYY_MM_DD, cmsProgramDO.getStartTime(), cmsProgramDO.getEndTime())) {
            return CommonResponse.commonfail("日期格式错误");
        }
        return CommonResponse.success(cmsProgramCheckService.subsetSelfList(page,cmsProgramDO, cmsProgramDO.getStartTime(), cmsProgramDO.getEndTime(), true));
//        return CommonResponse.success(cmsProgramCheckService.subsetSelfList(page,cmsProgram, startTime, endTime));
    }
    @ApiOperation(value = "子集管理列表")
    @GetMapping("/subset/manageList")
    public CommonResponse manageList(@Valid Page page,@RequestParam(value = "id",required = true) Long id,@RequestParam(value = "status",required = true) Integer status){
        return CommonResponse.success(cmsProgramCheckService.manageList(page,id,status));
    }
    @ApiOperation(value = "单集详情")
    @GetMapping("/simpleset/getById")
    public CommonResponse simplestById(@Valid @RequestParam("id") Long id){
        return CommonResponse.success(cmsProgramCheckService.simplesetById(id));
    }
    @ApiOperation(value = "子集详情")
    @GetMapping("/subset/getById")
    public CommonResponse subsetById(@Valid @RequestParam("id") Long id){
        return CommonResponse.success(cmsProgramCheckService.subsetById(id));
    }
    @ApiOperation(value = "终重单集列表")
    @GetMapping("/simpleset/endList")
    public CommonResponse endList(@Valid Page page,CmsProgramEndDto cmsProgramEndDto){
        return CommonResponse.success(cmsProgramCheckService.simplesetEndList(page, cmsProgramEndDto));
    }
    @ApiOperation(value = "终重子集列表")
    @GetMapping("/subset/endList")
    public CommonResponse subsetEndList(@Valid Page page,CmsProgramEndDto cmsProgramEndDto){
        return CommonResponse.success(cmsProgramCheckService.subsetEndList(page, cmsProgramEndDto));
    }

    @SysLog(objectType = OperateObjectEnum.CMS_MIVIE_EXPROT, operateType = OperateTypeEnum.EXPORT)
    @ApiOperation(value = "单集媒资导出")
    @PostMapping("/cmsSimpleSetExport")
    public CommonResponse export(@RequestBody  CmsProgramVO cmsProgramVO) throws Exception {
        if (!DateUtils.checkDataPattern(DateUtils.YYYY_MM_DD, cmsProgramVO.getStartTime(), cmsProgramVO.getEndTime())) {
            throw new CommonResponseException("日期格式错误");
        }
        return CommonResponse.success(cmsProgramCheckService.export( cmsProgramVO, cmsProgramVO.getStartTime(), cmsProgramVO.getEndTime()));
    }

}
