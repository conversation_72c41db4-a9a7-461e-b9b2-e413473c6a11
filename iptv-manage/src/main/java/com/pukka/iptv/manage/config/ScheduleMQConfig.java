package com.pukka.iptv.manage.config;

import org.springframework.amqp.core.Binding;
import org.springframework.amqp.core.BindingBuilder;
import org.springframework.amqp.core.DirectExchange;
import org.springframework.amqp.core.Queue;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

@Configuration
public class ScheduleMQConfig {

    /**
     * 消息的routing key与队列的binding key相同的队列
     */
    public static final String TXT_IMPORT_QUEUE = "schedule_import_queue";
    /**
     * direct 交换机
     */
    public static final String TXT_IMPORT_EXCHANGE = "schedule_import_exchange";
    /**
     * routing key
     */
    public static final String TXT_IMPORT_ROUTING = "schedule_import_routing";

    /**
     * 交换机
     *
     * @return
     */
    @Bean
    public DirectExchange directExchange() {
        return new DirectExchange(TXT_IMPORT_EXCHANGE);
    }

    /**
     * 创建一条持久化的、非排他的、非自动删除的队列
     *
     * @return
     */
    @Bean
    public Queue directQueue() {
        return new Queue(TXT_IMPORT_QUEUE);
    }

    /**
     * Binding,将该routing key的消息通过交换机转发到该队列
     *
     * @return
     */
    @Bean
    public Binding directBinding() {
        return BindingBuilder.bind(directQueue()).to(directExchange()).with(TXT_IMPORT_ROUTING);
    }
}
