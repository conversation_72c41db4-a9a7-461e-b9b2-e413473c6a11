package com.pukka.iptv.manage.service.cms;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.pukka.iptv.common.base.vo.CommonResponse;
import com.pukka.iptv.common.data.dto.CmsScheduleDto;
import com.pukka.iptv.common.data.model.bms.BmsSchedule;
import com.pukka.iptv.common.data.model.cms.CmsSchedule;
import com.pukka.iptv.common.data.vo.cms.CmsScheduleVO;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;

/**
 *
 * @author: luo
 * @date: 2021-9-15 11:39:55
 */

public interface CmsScheduleService extends IService<CmsSchedule> {
    boolean autoFeedBackScheduleDel(List<String> codeList, List<Long> spIdList);
    //ToDo:20230214 弃用方法
    boolean deleteByCodeAndSp(List<String> codeList, List<Long> spIdList);
    CommonResponse<Boolean> news(CmsScheduleDto cmsSchedule);

    CommonResponse<Boolean> del(String ids);

    Boolean importSchedule(MultipartFile file);

    CommonResponse up(CmsScheduleDto cmsSchedule);

    Page<CmsSchedule> pageList(Page page,CmsScheduleDto cmsScheduleDto);

    List<CmsSchedule> selectByChannelCodeAndStartDate(String channelCode, String startDate);

}


