package com.pukka.iptv.manage.controller.in;

import com.pukka.iptv.common.base.vo.CommonResponse;
import com.pukka.iptv.common.data.model.in.InSchedule;
import com.pukka.iptv.manage.mapper.in.InScheduleMapper;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.Valid;
import java.util.List;

/**
 * <AUTHOR>
 * @Description TODO
 * @Date 2024/8/30 10:08
 * @Version V1.0
 **/
@Slf4j
@RestController
@AllArgsConstructor
@RequestMapping(value = "/inSchedule", produces = MediaType.APPLICATION_JSON_VALUE)
@Api(tags = "InSchedule管理")
public class InScheduleController {

    @Autowired
    private InScheduleMapper inScheduleMapper;

    @ApiOperation(value = "根据correlateId查询节目单信息")
    @GetMapping("/getScheduleInfoByCorrelateId")
    public CommonResponse<List<InSchedule>> getScheduleInfoByCorrelateId(@Valid @RequestParam(name = "correlateId", required = true) String correlateId) {
        return CommonResponse.success(inScheduleMapper.getScheduleInfoByCorrelateId(correlateId));
    }
}
