package com.pukka.iptv.manage.util;

import com.pukka.iptv.common.data.params.SysAuthorizationContentParam;

import java.util.List;

/**
 * @ClassName AuthTask
 * @Description TODO
 * <AUTHOR>
 * @Date 2022/4/25 15:10
 * @Version
 */
public class AuthTask implements Runnable {

    private  List<String> items;
    private int cur = 0;

    private SysAuthorizationContentParam param;

    public List<String> getItems() {
        return items;
    }

    public int getCur() {
        return cur;
    }

    public SysAuthorizationContentParam getParam() {
        return param;
    }

    public AuthTask(){

    }

    public AuthTask(List<String> items){
      this.items = items;
    }

    public AuthTask(List<String> items, SysAuthorizationContentParam param){
        this.param = param;
    }

    @Override
    public void run() {
        for(int i=0; i < items.size(); i++ ){

        }

    }
}
