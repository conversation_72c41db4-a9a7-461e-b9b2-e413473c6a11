package com.pukka.iptv.manage.service.cms;

import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.pukka.iptv.common.base.vo.CommonResponse;
import com.pukka.iptv.common.data.dto.CmsResourceDto;
import com.pukka.iptv.common.data.model.cms.CmsResource;
import com.pukka.iptv.common.data.model.order.SubOrderMappingsEntity;
import com.pukka.iptv.common.data.vo.req.BlobUploadReq;
import com.pukka.iptv.common.data.vo.resp.ResultBlob;
import org.springframework.web.bind.annotation.RequestParam;

import java.net.MalformedURLException;
import java.util.List;

/**
 * @author: zhengcl
 * @date: 2021-9-15 15:13:01
 */

public interface CmsResourceService extends IService<CmsResource> {


    /**
     * 源片分页查询
     *
     * @param page        分页参数
     * @param cmsResource 分页条件
     * @return 返回结果
     */
    Page<CmsResource> pageList(Page page, CmsResource cmsResource);

    /**
     * 设为正片/预览片
     *
     * @param ids
     * @param type
     * @return
     */
    CommonResponse updateStatus(String ids, String type);

    /**
     * 修改为代删除状态
     * @param ids
     * @return
     */
    CommonResponse updateStatus(String ids);

    /**
     * 删除源片
     *
     * @param ids
     * @return
     */
    CommonResponse del(String ids) ;

    /**
     * 删除源片,只删除ts介质
     *
     * @param ids
     * @return
     */
    CommonResponse delOnlyTS(String ids);

    CommonResponse delOnlyTsById(String id);

    CommonResponse delOnlyTsBySelect();



    Page<CmsResource> movPageList(Page page, CmsResourceDto cmsResource);

    Boolean untie(CmsResource cmsResource);

    CommonResponse getMovList(String contentId,Integer type);

    boolean blobUpload(CmsResource cmsResource);

    ResultBlob clientBlobUpload(BlobUploadReq req);

    CommonResponse selectMov(Long id);

    boolean check(Long contentId, Integer type,Integer saveOrUpStatus);

    boolean delByCode(List<String> delMovieCodes, List<Long> spIdList);

    boolean deleteByCodeAndSp(List<SubOrderMappingsEntity> deleteMovieMappingEntities, List<Long> spIdList);

    List<CmsResource> listByCode(List<String> codeList);

    void deleteByIds(List<Long> resourceIdList);
}


