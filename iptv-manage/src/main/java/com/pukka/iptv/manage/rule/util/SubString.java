/**
 *
 */
package com.pukka.iptv.manage.rule.util;

import java.util.ArrayList;
import java.util.List;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

import static java.util.regex.Pattern.compile;

/**
 * 区分中英文截取字符串
 *
 * @Author: chiron
 * @Date: 2022/07/29/10:01
 * @company
 * @version 1.0
 */
public class SubString {
    /**
     * 字符串编码格式
     */
    public static final String ENCODE = "UTF-8";


    /**
     * 分隔中英文
     * @param str
     * @return
     */
    public static List<String> splitString(String str) {
        List<String> splitList = new ArrayList<>();
        // 英文组合
        Pattern p = compile("[a-zA-Z]+");
        Matcher m = p.matcher(str);
        String str1 = "";
        String str2 = "";
        while (true) {
            if (!m.find()) {
                break;
            }
            str1 += m.group();
        }
        splitList.add(str1);
        // 非英文组合
        p = compile("[^a-zA-Z]+");
        m = p.matcher(str);
        while (true) {
            if (!m.find()) {
                break;
            }
            str2 += m.group();
        }
        String s = str2.replaceAll("[\\pP\\p{Punct}]", "");
        splitList.add(s);
        return splitList;
    }

    /**
     * 测试
     * @param args
     */
    public static void main(String[] args) {
        System.out.println(splitString("[蜘蛛侠-侠影之谜 HD.mp4.1028*720p 超级清晰！！]"));
    }
}

