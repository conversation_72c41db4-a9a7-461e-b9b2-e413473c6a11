package com.pukka.iptv.manage.service.copyright.copyright;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.pukka.iptv.common.data.model.copyright.CopyrightInfo;
import com.pukka.iptv.common.data.vo.req.CopyrightInfoReq;
import com.pukka.iptv.common.data.vo.req.CopyrightInfoSizeReq;
import com.pukka.iptv.common.data.vo.req.QueryContentReq;

import java.util.List;

/**
 * <AUTHOR>
 */
public interface CopyrightInfoService {
    /**
     * 版权信息查询接口
     * @param req
     * @param b
     * @return
     */
    IPage<CopyrightInfo> getCopyrightInfoList(Page<CopyrightInfo> page, CopyrightInfoReq req, boolean b);

    /**
     * 授权时间设置接口
     * @param req
     * @param licensingWindowStart
     * @param licensingWindowEnd
     */
    Boolean setLicensingDateList(List<QueryContentReq> req, String licensingWindowStart, String licensingWindowEnd);

    /**
     * 版权信息导出
     * @param req
     * @return
     */
    Object exportInfo(List<QueryContentReq> req);

    /**
     * 版权到期时间查询
     * @param day
     * @return
     */
    CopyrightInfoSizeReq getCopyrightInfoSize(Integer day,Integer cpId);

}
