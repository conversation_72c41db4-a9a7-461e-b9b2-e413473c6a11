package com.pukka.iptv.manage.service.sys.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.pukka.iptv.common.base.constant.RedisKeyConstants;
import com.pukka.iptv.common.base.enums.CommonResponseEnum;
import com.pukka.iptv.common.base.enums.StatusEnum;
import com.pukka.iptv.common.base.exception.CommonResponseException;
import com.pukka.iptv.common.data.model.sys.SysCp;
import com.pukka.iptv.common.data.model.sys.SysSp;
import com.pukka.iptv.common.data.model.sys.SysTenant;
import com.pukka.iptv.common.data.model.sys.SysUserTenant;
import com.pukka.iptv.common.redis.service.RedisService;
import com.pukka.iptv.manage.mapper.sys.SysTenantMapper;
import com.pukka.iptv.manage.service.sys.SysTenantService;
import com.pukka.iptv.manage.service.sys.SysUserTenantService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

/**
 *
 * @author: zhengcl
 * @date: 2021-8-17 16:48:34
 */

@Service
public class SysTenantServiceImpl extends ServiceImpl<SysTenantMapper, SysTenant> implements SysTenantService {

	@Autowired
	private SysTenantMapper sysTenantMapper;
	@Autowired
	private SysUserTenantService sysUserTenantService;
	@Autowired
	private RedisService redisService;

	@Override
	public Page<SysTenant> page(Page<SysTenant> page, SysTenant sysTenant){
		QueryWrapper<SysTenant> queryWrapper = Wrappers.query();
		if (sysTenant.getStatus() != null) {
			queryWrapper.and(qw -> qw.eq("status", sysTenant.getStatus()));
		} else {
			queryWrapper.ne("status", StatusEnum.DELETE.getCode());
		}
		if (!StringUtils.isBlank(sysTenant.getName())) {
			queryWrapper.and(qw -> qw.like("name", sysTenant.getName()));
		}
		queryWrapper.orderByDesc("id");
		return sysTenant.selectPage(page, queryWrapper);
	}


	/**
    * 查询列表
    */
	@Override
	public List<SysTenant> listByUserId(Long userId){
		return sysTenantMapper.listByUserId(userId);

	}

	@Override
	@Transactional
	public boolean save(SysTenant sysTenant){
		sysTenant.valid();
		SysTenant otherTenant = getByName(sysTenant.getName());
		if(!Objects.isNull(otherTenant)){
			throw new CommonResponseException(CommonResponseEnum.FAIL,"租户已存在");
		}
		sysTenant.setStatus(StatusEnum.COME.getCode());
		setCpSPName(sysTenant);
		sysTenantMapper.insert(sysTenant);
		return true;
	}

	@Override
	public boolean update(SysTenant sysTenant){
		sysTenant.valid();
		Long count = sysTenantMapper.selectCount(Wrappers.lambdaQuery(SysTenant.class)
				.eq(SysTenant::getName, sysTenant.getName()).ne(SysTenant::getId, sysTenant.getId()));
		if(!Objects.isNull(count) && count >= 1){
			throw new CommonResponseException(CommonResponseEnum.FAIL,"租户已存在");
		}
		setCpSPName(sysTenant);
		this.update(Wrappers.<SysTenant>lambdaUpdate().set(SysTenant::getName,sysTenant.getName())
				.set(SysTenant::getCpIds,sysTenant.getCpIds()).set(SysTenant::getCpNames,sysTenant.getCpNames())
				.set(SysTenant::getSpIds,sysTenant.getSpIds()).set(SysTenant::getSpNames,sysTenant.getSpNames())
				.set(SysTenant::getDescription,sysTenant.getDescription()).eq(SysTenant::getId,sysTenant.getId()));
		return true;
	}

	@Override
	public List<SysTenant> listAll() {
		return list(Wrappers.lambdaQuery(SysTenant.class).eq(SysTenant::getStatus, StatusEnum.COME.getCode()));
	}

	@Override
	public boolean removeByIdWithValid(Long id) {
		long count = sysUserTenantService.count(Wrappers.lambdaQuery(SysUserTenant.class).eq(SysUserTenant::getTenantId, id).last("LIMIT 1"));
		if (count > 0) {
			throw new CommonResponseException("租户已被用户使用，不能删除！");
		}
		return removeById(id);
	}
	@Override
	public boolean removeByIdsWithValid(List<Long> ids) {
		long count = sysUserTenantService.count(Wrappers.lambdaQuery(SysUserTenant.class).in(SysUserTenant::getTenantId, ids).last("LIMIT 1"));
		if (count > 0) {
			throw new CommonResponseException("租户已被用户使用，不能删除！");
		}
		return removeByIds(ids);
	}

	private SysTenant getByName(String name){
		QueryWrapper<SysTenant> query = new QueryWrapper<>();
		query.lambda().eq(SysTenant::getName, name).last(" limit 1");
		return this.sysTenantMapper.selectOne(query);
	}

	private void setCpSPName(SysTenant tenant) {
		String cpIds = tenant.getCpIds();
		String spIds = tenant.getSpIds();
		if (StringUtils.isNotEmpty(cpIds)) {
			List<String> ids = new ArrayList();
			List<String> names = new ArrayList();
			for (String cpId : cpIds.split(",")) {
				SysCp cp = redisService.getCacheMapValue(RedisKeyConstants.SYS_CP, cpId);
				if (cp != null) {
					ids.add(cp.getId().toString());
					names.add(cp.getName());
				}
			}
			tenant.setCpIds(String.join(",", ids));
			tenant.setCpNames(String.join(",", names));
		}
		if (StringUtils.isNotEmpty(spIds)) {
			List<String> ids = new ArrayList();
			List<String> names = new ArrayList();
			for (String spId : spIds.split(",")) {
				SysSp sp = redisService.getCacheMapValue(RedisKeyConstants.SYS_SP, spId);
				if (sp != null) {
					ids.add(sp.getId().toString());
					names.add(sp.getName());
				}
			}
			tenant.setSpIds(String.join(",", ids));
			tenant.setSpNames(String.join(",", names));
		}
	}
}


