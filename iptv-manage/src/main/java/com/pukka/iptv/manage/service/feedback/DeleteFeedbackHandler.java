package com.pukka.iptv.manage.service.feedback;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.pukka.iptv.common.base.enums.ContentStatusEnum;
import com.pukka.iptv.common.base.enums.ContentTypeEnum;
import com.pukka.iptv.common.base.enums.OrderBaseResultEnum;
import com.pukka.iptv.common.base.enums.PublishStatusEnum;
import com.pukka.iptv.common.base.exception.ContentNotFoundException;
import com.pukka.iptv.common.base.vo.CommonResponse;
import com.pukka.iptv.common.core.util.ResultData;
import com.pukka.iptv.common.data.dto.PublishParamsDto;
import com.pukka.iptv.common.data.model.bms.*;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.util.ObjectUtils;

import javax.validation.constraints.NotNull;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * @Author: liaowj
 * @Description: 删除反馈执行器
 * 注意：所有的删除都要关联删除图片
 * @CreateDate: 2021/9/23 19:56
 * @Version: 1.0
 */
@Slf4j
@Component
public class DeleteFeedbackHandler extends FeedbackHandler {

    /**
     * 判断是不是需要删除SP的数据
     */
    private boolean deleteSpData = false;


    /**
     * 获取内容的回收类型，根据类型判定是否删除SP侧的数据
     * 只有content需要判断，category和package只回收下游，由运营操作关系删除
     */
    private boolean getDeleteType() {
        Long contentId = params.getContentId();
        Integer contentType = params.getContentType();
        Integer status = null;
        /** 单集，剧集、序列片回收 */
        if (ContentTypeEnum.FILM.getValue().equals(contentType)
                || ContentTypeEnum.TELEPLAY.getValue().equals(contentType)
                || ContentTypeEnum.EPISODES.getValue().equals(contentType)) {
            BmsContent content = bmsContentService.getById(contentId);
            if (content == null) {
                log.error("分发内容 BmsContent ID = {}, 不存在", contentId);
                throw new ContentNotFoundException("分发内容 BmsContent ID = " + contentId + ", 不存在");
            }
            status = content.getStatus();
        }
        /** 子集回收 */
        if (ContentTypeEnum.SUBSET.getValue().equals(contentType)) {
            BmsProgram program = bmsProgramService.getById(contentId);
            if (program == null) {
                log.error("分发内容 BmsProgram ID = {}, 不存在", contentId);
                throw new ContentNotFoundException("分发子集 BmsProgram ID = " + contentId + ",  不存在");
            }
            status = program.getStatus();
        }
        /** 频道回收 */
        /*if (ContentTypeEnum.Channel.getValue().equals(contentType)) {
            status = bmsChannelService.getById(contentId).getStatus();
        }*/
        return ContentStatusEnum.DELETE.getCode().equals(status) ? true : false;
    }


    /**
     * 节目的删除需要耦合删除图片和子集
     *
     * @return
     */
    @Override
    public FeedbackHandler dealContent() {
        Long contentId = params.getContentId();
        /** 下游处理成功时才处理内容相关的关系，避免工单关系单独发布，且已经回收成功的情况下，错误将关系改为回收失败 */
        /** 一键删除，回收下游同时删除SP侧信息 */
        if (deleteSpData && OrderBaseResultEnum.SUCCESS.getCode().equals(params.getResult())) {
            /** 一键删除 */
            bmsContentService.removeById(contentId);
            log.info("一键删除Content ID = {}， 成功", contentId);
        } else {
            setCpFeedbackFlag(params.getContentType(), contentId);
            bmsContentService.update(Wrappers.lambdaUpdate(BmsContent.class)
                    .set(BmsContent::getPublishStatus, status)
                    .set(cpFeedbackFlag != null, BmsContent::getCpFeedbackFlag, cpFeedbackFlag)
                    .set(BmsContent::getPublishDescription, params.getErrorDescription())
                    .eq(BmsContent::getId, contentId));
        }
        log.info("<回收工单> {} 处理 Content ID = {}， 结束", result, contentId);

        return this;
    }

    /**
     * 子集删除有两种一是子集单独回收，二是回收剧头带上子集回收
     *
     * @return
     */
    @Override
    public FeedbackHandler dealProgram() {
        Long contentOrProgramId = params.getContentId();
        Integer contentType = params.getContentType();
        /** 当下游内容回收成功，且为一键删除时 才处理聚集的子集关系 */
        if (ContentTypeEnum.EPISODES.getValue().equals(contentType)
                || ContentTypeEnum.TELEPLAY.getValue().equals(contentType)) {
            log.info("通过剧集删除子集，剧头ID，ContentId = {}，ContentType = {}", contentOrProgramId, contentType);
            BmsContent series = bmsContentService.getById(contentOrProgramId);

            List<Long> programIdList = new ArrayList();
            if (series == null) {
                log.error("分发反馈处理失败， 剧头ID = {} 不存在", contentOrProgramId);
                throw new ContentNotFoundException("分发内容 BmsContent ID = " + contentOrProgramId + ", 不存在");
            }
            /** 获取全部的子集ID，用于删除子集相关的图片  */
            bmsProgramService.list(Wrappers.lambdaQuery(BmsProgram.class)
                    .eq(BmsProgram::getCmsSeriesId, series.getCmsContentId())
                    .eq(BmsProgram::getSpId, series.getSpId()))
                    .forEach(bmsProgram -> {
                        programIdList.add(bmsProgram.getId());
                    });

            if(programIdList.isEmpty()) {
                return this;
            }
            /** 删除剧集，关联删除剧集的子集 */
            if (OrderBaseResultEnum.SUCCESS.getCode().equals(params.getResult())) {
                if (deleteSpData) {
                    bmsPictureService.remove(Wrappers.lambdaQuery(BmsPicture.class)
                            .eq(BmsPicture::getContentType, ContentTypeEnum.SUBSET.getValue())
                            .in(BmsPicture::getBmsContentId, programIdList));
                    log.info("删除子集 Program IDs = {} 图片 Picture 成功", programIdList);
                    /** 由于剧头和子集是通过cms_series_id和sp_id关联的，同时需要这两个字段才能确定此剧头所属的子集 */
                    bmsProgramService.remove(Wrappers.lambdaQuery(BmsProgram.class)
                            .eq(BmsProgram::getSpId, series.getSpId())
                            .eq(BmsProgram::getCmsSeriesId, series.getCmsContentId())
                    );
                    log.info("删除 剧头Content ID = {} 下的子集 Program IDs = {}， 成功", contentOrProgramId, programIdList);
                } else {
                    setCpFeedbackFlag(contentType, contentOrProgramId);
                    log.info("剧集一键回收 通过剧集工单删除子集，子集ID，ProgramId = {}, publish_status= {}", contentOrProgramId, status);
                    bmsPictureService.update(Wrappers.lambdaUpdate(BmsPicture.class)
                            .set(BmsPicture::getPublishStatus, status)
                            .eq(BmsPicture::getContentType, ContentTypeEnum.SUBSET.getValue())
                            .in(BmsPicture::getBmsContentId, programIdList));
                    bmsProgramService.update(Wrappers.lambdaUpdate(BmsProgram.class)
                            .set(BmsProgram::getPublishStatus, status)
                            .set(cpFeedbackFlag != null, BmsProgram::getCpFeedbackFlag, cpFeedbackFlag)
                            .set(BmsProgram::getPublishDescription, params.getErrorDescription())
                            .in(BmsProgram::getId, programIdList));
                    log.info("回收子集 {} 工单 处理 成功 Program IDs = {} ", contentOrProgramId);
                }
            } /*else { // 剧头失败不管子集
                log.info("子集一键回收 通过剧集工单删除子集，子集ID，ProgramId = {}", contentOrProgramId);
                bmsProgramService.update(Wrappers.lambdaUpdate(BmsProgram.class)
                        .set(BmsProgram::getPublishStatus, status)
                        .set(cpFeedbackFlag != null, BmsProgram::getCpFeedbackFlag, cpFeedbackFlag)
                        .set(BmsProgram::getPublishDescription, params.getErrorDescription())
                        .in(BmsProgram::getId, programIdList));
                log.info("回收子集 {} 工单 处理 成功 Program IDs = {} ", contentOrProgramId);
            }*/
            return this;
        }

        // 子集单独的回收工单
        if (OrderBaseResultEnum.SUCCESS.getCode().equals(params.getResult()) && deleteSpData) {
            // CP回收 且处理成功的情况下删除子集
            log.info("子集一键回收 通过工单删除子集，子集ID，ProgramId = {}", contentOrProgramId);
            bmsProgramService.removeById(contentOrProgramId);
        } else {
            // 失败 或 SP回收
            setCpFeedbackFlag(params.getContentType(), contentOrProgramId);
            log.info("通过工单删除子集，子集ID，ProgramId = {}", contentOrProgramId);
            /** 子集回收工单的反馈处理 */
            bmsProgramService.update(Wrappers.lambdaUpdate(BmsProgram.class)
                    .set(BmsProgram::getPublishStatus, status)
                    .set(cpFeedbackFlag != null, BmsProgram::getCpFeedbackFlag, cpFeedbackFlag)
                    .set(BmsProgram::getPublishDescription, params.getErrorDescription())
                    .eq(BmsProgram::getId, contentOrProgramId));
            log.info("回收子集 {} 工单 处理 成功 Program IDs = {} ", contentOrProgramId);
        }
        return this;
    }


    @Override
    public FeedbackHandler dealSchedule() {
        Long id = params.getContentId();

        Integer contentType = params.getContentType();
        if (ContentTypeEnum.CHANNEL.getValue().equals(contentType)) {
            log.info("根据Channel Id = {} 删除Schedule", id);
            //是channel时，说明通过channel删除physicalchannel
            BmsChannel bmsChannel = bmsChannelService.getById(id);
            if (deleteSpData && OrderBaseResultEnum.SUCCESS.getCode().equals(params.getResult())) {
                log.info("CP一键删除Channel相关 删除Schedule");
                bmsScheduleService.remove(Wrappers.lambdaQuery(BmsSchedule.class)
                        .eq(BmsSchedule::getCmsChannelId, bmsChannel.getCmsChannelId())
                        .eq(BmsSchedule::getSpId, bmsChannel.getSpId())
                );
            }
            log.info("根据Channel 修改 BmsSchedule ID = {} 回收状态： {} 成功，结束", id, status);
            return this;
        }

        CommonResponse<List<Long>> response = outOrderBaseFeignClient.getScheduleIds(params.getBaseOrderId());
        log.info("查询Schedule 列表返回 = {}", response);
        List<Long> ids = response.getData();
        if (!ObjectUtils.isEmpty(ids)) {
            // 独自的schedule回收工单
            /** 下游处理成功时才处理内容相关的关系，避免工单关系单独发布，且已经回收成功的情况下，错误将关系改为回收失败 */
            if (deleteSpData && OrderBaseResultEnum.SUCCESS.getCode().equals(params.getResult())) {
                /** 一键删除，回收下游同时删除SP侧信息 */
                /** 一键删除 */
                log.info("一键删除，Schedule ID = {}", id);
                bmsScheduleService.removeByIds(ids);
            } else {
                /** 下游失败，只处理频道失败，不管关系 */
                /** 节目单回收失败 */
                bmsScheduleService.update(Wrappers.lambdaUpdate(BmsSchedule.class)
                        .set(BmsSchedule::getPublishStatus, status)
                        .set(BmsSchedule::getPublishDescription, params.getErrorDescription())
                        .in(BmsSchedule::getId, ids));
                log.info("修改 Schedule ID = {} 回收状态 {} ，结束", id, status);
            }
            return this;
        }
        log.error("查询Schedule失败，或者Schedule为空， orderBaseId = {}", params.getBaseOrderId());
        return this;
    }

    @Override
    public FeedbackHandler dealChannel() {
        Long channelId = params.getContentId();

        /** 下游处理成功时才处理内容相关的关系，避免工单关系单独发布，且已经回收成功的情况下，错误将关系改为回收失败 */
        if (deleteSpData && OrderBaseResultEnum.SUCCESS.getCode().equals(params.getResult())) {
            /** 一键删除，回收下游同时删除SP侧信息 */
            /** 一键删除 */
            log.info("一键删除，Channel ID = {}", channelId);
            bmsChannelService.removeById(channelId);
        } else {
            /** 下游失败，只处理频道失败，不管关系 */
            /** 节目单回收失败 */
            bmsChannelService.update(Wrappers.lambdaUpdate(BmsChannel.class)
                    .set(BmsChannel::getPublishStatus, status)
                    .set(BmsChannel::getPublishDescription, params.getErrorDescription())
                    .eq(BmsChannel::getId, channelId));
            log.info("修改 Channel ID = {} 回收状态： {} 成功，结束", channelId, status);
        }
        return this;
    }

    @Override
    public FeedbackHandler dealPhysicalChannel() {
        Long id = params.getContentId();

        Integer contentType = params.getContentType();
        if (ContentTypeEnum.CHANNEL.getValue().equals(contentType)) {
            log.info("根据Channel Id = {} 删除PhysicalChannel", id);
            //是channel时，说明通过channel删除physicalchannel
            BmsChannel bmsChannel = bmsChannelService.getById(id);
            if (deleteSpData && OrderBaseResultEnum.SUCCESS.getCode().equals(params.getResult())) {
                log.info("CP一键删除Channel相关PhysicalChannel");
                bmsPhysicalChannelService.remove(Wrappers.lambdaQuery(BmsPhysicalChannel.class)
                        .eq(BmsPhysicalChannel::getCmsChannelId, bmsChannel.getCmsChannelId())
                        .eq(BmsPhysicalChannel::getSpId, bmsChannel.getSpId())
                );
            } /*else { //channel失败不管physicalchannel
                //失败或者非CP删除
                bmsPhysicalChannelService.update(Wrappers.lambdaUpdate(BmsPhysicalChannel.class)
                        .set(BmsPhysicalChannel::getPublishStatus, status)
                        .set(BmsPhysicalChannel::getPublishDescription, params.getErrorDescription())
                        .eq(BmsPhysicalChannel::getCmsChannelId, bmsChannel.getCmsChannelId())
                        .eq(BmsPhysicalChannel::getSpId, bmsChannel.getSpId())
                );
            }*/
            log.info("根据Channel 修改 PhysicalChannel ID = {} 回收状态： {} 成功，结束", id, status);
            return this;
        }

        //直接通过PhysicalChannel的删除工单
        /** 下游处理成功时才处理内容相关的关系，避免工单关系单独发布，且已经回收成功的情况下，错误将关系改为回收失败 */
        if (deleteSpData && OrderBaseResultEnum.SUCCESS.getCode().equals(params.getResult())) {
            /** 一键删除，回收下游同时删除SP侧信息 */
            /** 一键删除 */
            log.info("一键删除，PhysicalChannel ID = {}", id);
            bmsPhysicalChannelService.removeById(id);
        } else {
            /** 下游失败，只处理频道失败，不管关系 */
            /** 节目单回收失败 */
            bmsPhysicalChannelService.update(Wrappers.lambdaUpdate(BmsPhysicalChannel.class)
                    .set(BmsPhysicalChannel::getPublishStatus, status)
                    .set(BmsPhysicalChannel::getPublishDescription, params.getErrorDescription())
                    .eq(BmsPhysicalChannel::getId, id));
            log.info("修改 PhysicalChannel ID = {} 回收状态： {} 成功，结束", id, status);
        }
        return this;
    }


    /**
     * 内容关系中的回收图片处理
     */
    @Override
    public FeedbackHandler dealMappingPicture() {
        Long contentId = params.getContentId();
        /** 内容回收成功，才需要去验证关系的状态 */
        if (OrderBaseResultEnum.SUCCESS.getCode().equals(params.getResult())) {
            if (deleteSpData) {
                /** 当一键删除时，删除SP侧图片数据 */
                log.info("一键删除Content ID = {} 的 全部 BmsPicture", contentId);
                bmsPictureService.remove(Wrappers.lambdaQuery(BmsPicture.class)
                        .eq(BmsPicture::getBmsContentId, contentId)
                        .eq(BmsPicture::getContentType, params.getContentType()));
                return this;
            }
            List<Long> idList = new ArrayList<>();
            List<String> urlList = new ArrayList<>();
            bmsPictureService.list(Wrappers.lambdaQuery(BmsPicture.class)
                    .eq(BmsPicture::getBmsContentId, contentId)
                    .eq(BmsPicture::getContentType, params.getContentType())
                    .in(BmsPicture::getPublishStatus,
                            PublishStatusEnum.ROLLBACKING.getCode(),
                            PublishStatusEnum.FAILROLLBACK.getCode()
                    )
            ).forEach(picture -> {
                idList.add(picture.getId());
                urlList.add(picture.getFileUrl());
            });

            log.info("一键回收， 将内容ContentID = {} 的回收失败的内容图片 BmsPicture ID = {}， Picture URL = {}， 更改为回收成功",
                    contentId, idList, urlList);
            /** 一键回收，对应的在下游的关系都应该改为和内容同样的状态（此处把失败的关系更改为成功） */
            bmsPictureService.update(Wrappers.lambdaUpdate(BmsPicture.class)
                    .set(BmsPicture::getPublishStatus, status)
                    .set(BmsPicture::getPublishDescription, params.getErrorDescription())
                    .eq(BmsPicture::getBmsContentId, contentId)
                    .eq(BmsPicture::getContentType, params.getContentType())
                    .in(BmsPicture::getPublishStatus,
                            PublishStatusEnum.ROLLBACKING.getCode(),
                            PublishStatusEnum.FAILROLLBACK.getCode()
                    )
            );
            log.info("修改 ContentID = {} ID in {} 图片关系回收状态：{} 结束", contentId, idList, status);
        } else {
            /** 回收失败 */
            log.info("回收失败，处理Content ID = {} 的 BmsPicture 为 {}", contentId, result);
            bmsPictureService.update(Wrappers.lambdaUpdate(BmsPicture.class)
                    .set(BmsPicture::getPublishStatus, status)
                    .set(BmsPicture::getPublishDescription, params.getErrorDescription())
                    .eq(BmsPicture::getBmsContentId, contentId)
                    .eq(BmsPicture::getContentType, params.getContentType())
                    .eq(BmsPicture::getPublishStatus,
                            PublishStatusEnum.ROLLBACKING.getCode()
                    )
            );
        }
        return this;
    }

    /**
     * 删除栏目关系需要更新内容表中的category_ids和category_names字段
     * 删除内容栏目关系有两种，一种是关系工单的反馈，一种是删除内容的反馈
     *
     * @return
     */
    @Override
    public FeedbackHandler dealCategoryContent() {
        Long contentOrCategoryContentId = params.getContentId();
        Integer contentType = params.getContentType();
        /** 删除内容，关联删除栏目内容关系，
         * 只有内容成功时，才需要去管理关系的成败（避免关系回收失败，但内容却成功了的情况），
         * 当内容失败时，与关系的成败无关
         */
        if (OrderBaseResultEnum.SUCCESS.getCode().equals(params.getResult())
                && ContentTypeEnum.CATEGORY_PROGRAM.getValue() != contentType
                && ContentTypeEnum.CATEGORY_SERIES.getValue() != contentType) {
            /** 当下游内容回收成功，且为一键删除时 才删除栏目内容关系 */
            if (deleteSpData) {
                log.info("一键删除成功， 删除 ContentID = {}， contentType = {}，的全部栏目内容关系。",
                        contentOrCategoryContentId, contentType);
                bmsCategoryContentService.remove(Wrappers.lambdaQuery(BmsCategoryContent.class)
                                .eq(BmsCategoryContent::getBmsContentId, contentOrCategoryContentId)
//                        .eq(BmsCategoryContent::getPublishStatus, PublishStatusEnum.ROLLBACK.getCode())
                );
                /** 更新内容表中的category_ids和category_names字段 */
            } else {
                List<Long> idList = new ArrayList<>();
                List<String> nameList = new ArrayList<>();
                bmsCategoryContentService.list(Wrappers.lambdaQuery(BmsCategoryContent.class)
                        .eq(BmsCategoryContent::getBmsContentId, contentOrCategoryContentId)
                        .eq(BmsCategoryContent::getContentType, params.getContentType())
                        .in(BmsCategoryContent::getPublishStatus,
                                PublishStatusEnum.ROLLBACKING.getCode(),
                                PublishStatusEnum.FAILROLLBACK.getCode()
                        )
                ).forEach(categoryContent -> {
                    idList.add(categoryContent.getId());
                    nameList.add(categoryContent.getCategoryName());
                });
                /** 没有失败或不正常的关系 */
                if (idList.isEmpty()) {
                    return this;
                }
                log.info("一键回收， 将内容ContentID = {} 的回收失败的栏目内容关系 CategoryContent ID = {}， Category Name = {}， 更改为回收成功",
                        contentOrCategoryContentId, idList, nameList);
                /** 一键回收，对应的在下游的关系都应该改为和内容同样的状态（此处把失败的关系更改为成功） */
                bmsCategoryContentService.update(Wrappers.lambdaUpdate(BmsCategoryContent.class)
                        .set(BmsCategoryContent::getPublishStatus, status)
                        .set(BmsCategoryContent::getPublishDescription, params.getErrorDescription())
                        .eq(BmsCategoryContent::getBmsContentId, contentOrCategoryContentId)
                        .eq(BmsCategoryContent::getContentType, params.getContentType())
                        .in(BmsCategoryContent::getPublishStatus,
                                PublishStatusEnum.ROLLBACKING.getCode(),
                                PublishStatusEnum.FAILROLLBACK.getCode()
                        )
                );
            }
            return this;
        }

        /** 关系工单 */
        log.info("CategoryContent 《回收工单》 {} 反馈， 即将更新  ID in {}, 状态为publish_status = {}, 描述信息为 publish_description = {} ",
                result, contentOrCategoryContentId, status, params.getErrorDescription());
        bmsCategoryContentService.update(Wrappers.lambdaUpdate(BmsCategoryContent.class)
                .set(BmsCategoryContent::getPublishStatus, status)
                .set(BmsCategoryContent::getPublishDescription, params.getErrorDescription())
                .eq(BmsCategoryContent::getId, contentOrCategoryContentId));
        log.info("1{} 回收状态： {}，结束", contentOrCategoryContentId, status);
        return this;
    }


    /**
     * 删除产品包关系有两种，一种是关系工单的反馈，一种是删除内容的反馈
     *
     * @return
     */
    @Override
    public FeedbackHandler dealPackageContent() {
        Long contentOrPackageContentId = params.getContentId();
        Integer contentType = params.getContentType();
        /** 删除内容，关联删除产品包内容关系 */
        if (OrderBaseResultEnum.SUCCESS.getCode().equals(params.getResult())
                && ContentTypeEnum.PACKAGE_PROGRAM.getValue() != contentType
                && ContentTypeEnum.PACKAGE_SERIES.getValue() != contentType) {
            /** 当下游内容回收成功，且为一键删除时 才处理栏目内容关系 */
            if (deleteSpData) {
                bmsPackageContentService.remove(Wrappers.lambdaQuery(BmsPackageContent.class)
                                .eq(BmsPackageContent::getBmsContentId, contentOrPackageContentId)
//                        .eq(BmsPackageContent::getPublishStatus, PublishStatusEnum.ROLLBACK.getCode())
                );
            } else {
                List<Long> idList = new ArrayList<>();
                List<String> nameList = new ArrayList<>();
                bmsPackageContentService.list(Wrappers.lambdaQuery(BmsPackageContent.class)
                        .eq(BmsPackageContent::getBmsContentId, contentOrPackageContentId)
                        .eq(BmsPackageContent::getContentType, params.getContentType())
                        .in(BmsPackageContent::getPublishStatus,
                                PublishStatusEnum.ROLLBACKING.getCode(),
                                PublishStatusEnum.FAILROLLBACK.getCode()
                        )
                ).forEach(categoryContent -> {
                    idList.add(categoryContent.getId());
                    nameList.add(categoryContent.getPackageName());
                });
                /** 没有失败或不正常的关系 */
                if (idList.isEmpty()) {
                    return this;
                }
                log.info("一键回收， 将内容ContentID = {} 的回收失败的产品包内容关系 PackageContent ID = {}， Package Name = {}， 更改为回收成功",
                        contentOrPackageContentId, idList, nameList);
                /** 一键回收，对应的在下游的关系都应该改为和内容同样的状态（此处把失败的关系更改为成功） */
                bmsPackageContentService.update(Wrappers.lambdaUpdate(BmsPackageContent.class)
                        .set(BmsPackageContent::getPublishStatus, status)
                        .set(BmsPackageContent::getPublishDescription, params.getErrorDescription())
                        .eq(BmsPackageContent::getBmsContentId, contentOrPackageContentId)
                        .eq(BmsPackageContent::getContentType, params.getContentType())
                        .in(BmsPackageContent::getPublishStatus,
                                PublishStatusEnum.ROLLBACKING.getCode(),
                                PublishStatusEnum.FAILROLLBACK.getCode()
                        )
                );
            }
            return this;
        }


        /** 关系回收工单的反馈处理 */
        log.info("PackageContent 《回收工单》 {}  反馈， 即将更新  ID = {}, 状态为publish_status = {}, 描述信息为 publish_description = {} ",
                result, contentOrPackageContentId, status, params.getErrorDescription());
        bmsPackageContentService.update(Wrappers.lambdaUpdate(BmsPackageContent.class)
                .set(BmsPackageContent::getPublishStatus, status)
                .set(BmsPackageContent::getPublishDescription, params.getErrorDescription())
                .eq(BmsPackageContent::getId, contentOrPackageContentId));
        log.info("修改 PackageContent ID = {} 回收状态: {}，结束", contentOrPackageContentId, status);
        return this;
    }

    @Override
    public ResultData getResult() {
        return null;
    }

    @Override
    public void excute(ContentTypeEnum contentType, PublishParamsDto params) {
        initParams(params, PublishStatusEnum.ROLLBACK, PublishStatusEnum.FAILROLLBACK);
        deleteSpData = getDeleteType();
        switch (contentType) {
            // 单集剧头和序列片等bms_content内容
            case FILM:
                /** 一键删除时只有内容上有删除标识，所以在反馈成功时需要通过内容去处理内容相关的关系 */
                dealCategoryContent()      //首先删除栏目、产品包内容关系
                        .dealPackageContent()
                        .dealMappingPicture()
                        .dealContent();             //最后删节目
                break;
            case TELEPLAY:
            case EPISODES:
                /** 一键删除时只有内容上有删除标识，所以在反馈成功时需要通过内容去处理内容相关的关系 */
                dealCategoryContent()      //首先删除栏目、产品包内容关系
                        .dealPackageContent()
                        .dealMappingPicture()
                        .dealProgram()
                        .dealContent();             //最后删节目
                break;
            // 子集
            case SUBSET:
                dealMappingPicture().dealProgram();
                break;
            case CHANNEL:
                //频道
                dealMappingPicture().dealPhysicalChannel().dealSchedule().dealChannel();
                break;
            case PHYSICAL_CHANNEL:
                //物理频道
                dealPhysicalChannel();
                break;
            case CATEGORY:
                //栏目
                dealMappingPicture().dealCategory();
                break;
            case PACKAGE:
                //产品包
                dealMappingPicture().dealPackage();
                break;
            case SCHEDULE:
                //节目单
                dealSchedule();
                break;
            case PICTURE:
                dealPicture();
                break;
            case CATEGORY_PROGRAM:
            case CATEGORY_SERIES:
                //栏目单集/剧集
                dealCategoryContent();
                break;
            case CATEGORY_CHANNEL:
                //栏目频道
                dealCategryChannel();
                break;
            case PACKAGE_CHANNEL:
                //产品包频道
                dealPackageChannel();
                break;
            case PACKAGE_SERIES:
            case PACKAGE_PROGRAM:
                //产品包剧集
                dealPackageContent();
                break;
            default:
                log.error("处理内容反馈失败，ContentType={},失败原因:不存在的ContentType类型", contentType);
                break;

        }
    }

}
