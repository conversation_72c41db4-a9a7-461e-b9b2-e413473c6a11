package com.pukka.iptv.manage.controller.sys;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.pukka.iptv.common.base.vo.CommonResponse;
import com.pukka.iptv.common.data.model.sys.SysRoleMenu;
import com.pukka.iptv.common.data.vo.IdList;
import com.pukka.iptv.common.data.vo.req.RoleMenus;
import com.pukka.iptv.common.log.annotation.SysLog;
import com.pukka.iptv.common.log.enums.OperateObjectEnum;
import com.pukka.iptv.common.log.enums.OperateTypeEnum;
import com.pukka.iptv.manage.service.sys.SysRoleMenuService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.AllArgsConstructor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;

/**
 *
 * @author: zhengcl
 * @date: 2021-8-18 10:37:53
 */

@RestController
@AllArgsConstructor
@RequestMapping(value = "/sysRoleMenu", produces = MediaType.APPLICATION_JSON_VALUE)
@Api(tags="sysRoleMenu管理")
public class SysRoleMenuController {

    @Autowired
    private SysRoleMenuService sysRoleMenuService;

    @ApiOperation(value = "分页",hidden = true)
    @GetMapping("/page" )
    public CommonResponse<Page> page(@Valid Page page, SysRoleMenu sysRoleMenu) {
        return  CommonResponse.success(sysRoleMenuService.page(page, Wrappers.query(sysRoleMenu)));
    }

    @ApiOperation(value = "详情",hidden = true)
    @GetMapping("/getById")
    public CommonResponse<SysRoleMenu> getById(@Valid @RequestParam(name = "id", required = true)  Long id) {
        return CommonResponse.success(sysRoleMenuService.getById(id));
    }

    @SysLog(objectType = OperateObjectEnum.SYS_ROLE_MENU, operateType = OperateTypeEnum.SAVE)
    @ApiOperation(value = "新增",hidden = true)
    @PostMapping("/save")
    public CommonResponse<Boolean> save(@Valid @RequestBody SysRoleMenu sysRoleMenu) {
        return  CommonResponse.success(sysRoleMenuService.save(sysRoleMenu));
    }

    @SysLog(objectType = OperateObjectEnum.SYS_ROLE_MENU, operateType = OperateTypeEnum.UPDATE)
    @ApiOperation(value = "修改",hidden = true)
    @PutMapping("/update")
    public CommonResponse<Boolean> updateById(@Valid @RequestBody SysRoleMenu sysRoleMenu) {
        return CommonResponse.success(sysRoleMenuService.updateById(sysRoleMenu));
    }

    @SysLog(objectType = OperateObjectEnum.SYS_ROLE_MENU, operateType = OperateTypeEnum.DELETE)
    @ApiOperation(value = "删除",hidden = true)
    @DeleteMapping("/deleteById" )
    public CommonResponse<Boolean> deleteById(@Valid @RequestParam(name = "id", required = true) Long id) {
        return  CommonResponse.success(sysRoleMenuService.removeById(id));
    }

    @SysLog(objectType = OperateObjectEnum.SYS_ROLE_MENU, operateType = OperateTypeEnum.DELETE)
    @ApiOperation(value = "批量删除",hidden = true)
    @DeleteMapping("/deleteByIds" )
    public CommonResponse<Boolean> deleteByIds(@Valid @RequestBody IdList idList) {
        return  CommonResponse.success(sysRoleMenuService.removeByIds(idList.getIds()));
    }


    @SysLog(objectType = OperateObjectEnum.SYS_ROLE_MENU, operateType = OperateTypeEnum.DELETE)
    @ApiOperation(value ="更新角色权限" )
    @PutMapping("/updateRoleMenus")
    public CommonResponse<Boolean> updateRoleMenus(@RequestBody RoleMenus roleMenus) {
        return CommonResponse.success(sysRoleMenuService.updateRoleMenus(roleMenus));
    }


}
