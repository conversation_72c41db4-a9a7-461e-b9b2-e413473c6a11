package com.pukka.iptv.manage.listener.excel;

import cn.hutool.core.util.ObjectUtil;
import com.alibaba.excel.context.AnalysisContext;
import com.alibaba.excel.event.AnalysisEventListener;
import com.alibaba.fastjson.JSON;
import com.pukka.iptv.common.base.util.JwtTokenUtil;
import com.pukka.iptv.common.base.vo.SecurityUser;
import com.pukka.iptv.common.core.util.SpringUtils;
import com.pukka.iptv.common.core.util.StringUtils;
import com.pukka.iptv.common.data.dto.SubSetExcelDto;
import com.pukka.iptv.common.data.model.sys.SysCp;
import com.pukka.iptv.manage.config.SubSetMQConfig;
import com.pukka.iptv.manage.service.sys.SysCpService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.springframework.amqp.rabbit.connection.CorrelationData;
import org.springframework.amqp.rabbit.core.RabbitTemplate;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Component;

import java.util.*;

/**
 * 子集模板导入
 */
@Slf4j
@Component
@Scope("prototype")
public class SubSetReadListener extends AnalysisEventListener<SubSetExcelDto> {

    private RabbitTemplate rabbitTemplate = SpringUtils.getBean("rabbitTemplate");

    private SysCpService sysCpService = SpringUtils.getBean(SysCpService.class);

    List<SubSetExcelDto> excel = new ArrayList<>();

    Map<String, Integer> movieNameMap = new HashMap<>();

    private static final int BATCH_COUNT = 1000;

    //每读一次会调用一次invoke
    @Override
    public void invoke(SubSetExcelDto subSetExcelDto, AnalysisContext analysisContext) {
        if (StringUtils.isEmpty(subSetExcelDto.getName())
                && StringUtils.isEmpty(subSetExcelDto.getCpName())
                && StringUtils.isEmpty(subSetExcelDto.getEpisodeIndex())
                && StringUtils.isEmpty(subSetExcelDto.getDescription())
        ) {
            return;
        }
        if (StringUtils.isEmpty(subSetExcelDto.getName())
                || StringUtils.isEmpty(subSetExcelDto.getCpName())
                || StringUtils.isEmpty(subSetExcelDto.getEpisodeIndex())
                || StringUtils.isEmpty(subSetExcelDto.getDescription())
        ) {
            throw new RuntimeException("标红的关键字段不能为空！");
        }
        if(StringUtils.isNotEmpty(subSetExcelDto.getApproval())){
            if(subSetExcelDto.getApproval().length() >32){
                throw new RuntimeException("内容字号长度超过32字符！");
            }
        }

        SecurityUser securityUser = JwtTokenUtil.getSecurityUser();
        if (Objects.nonNull(securityUser)) {
            subSetExcelDto.setCreatorName(securityUser.getName());
            subSetExcelDto.setCreatorId(securityUser.getId());
        }

        //20220704 添加注入vspcode空值校验
        if (ObjectUtils.isNotEmpty(subSetExcelDto.getCpName())) {
            SysCp cp = sysCpService.getByName(subSetExcelDto.getCpName());
            if (ObjectUtils.isNotEmpty(cp)) {
                subSetExcelDto.setVspCode(cp.getCode());
            }
        }

        if (StringUtils.isNotEmpty(subSetExcelDto.getMovName())) {
            if (movieNameMap.containsKey(subSetExcelDto.getMovName())) {
                // 添加失败则说明有重复数据 报错
                String msg = "第" + analysisContext.readRowHolder().getRowIndex() + "行数据,正片名称与第" + movieNameMap.get(subSetExcelDto.getMovName()) + "行数据重复";
                clear();
                throw new RuntimeException(msg);
            }
            movieNameMap.put(subSetExcelDto.getMovName(), analysisContext.readRowHolder().getRowIndex());
        }
        excel.add(subSetExcelDto);
    }


    @Override
    public void doAfterAllAnalysed(AnalysisContext analysisContext) {
        //读取完成以后如果集合内没超过1000个则单独处理
        if (ObjectUtil.isNotEmpty(excel)) {
            send();
        }
        clear();
    }

    private void send() {
        //全局唯一 不然ReturnCallback 无效
        CorrelationData correlationData = new CorrelationData(java.util.UUID.randomUUID().toString());
        String excel = JSON.toJSONString(this.excel);
        //发送消息
        this.rabbitTemplate.convertAndSend(SubSetMQConfig.SUBSET_EXCEL_IMPORT_EXCHANGE,
                SubSetMQConfig.SUBSET_EXCEL_IMPORT_ROUTING,
                excel, correlationData
        );
    }

    void clear() {
        movieNameMap.clear();
        excel.clear();
    }
}
