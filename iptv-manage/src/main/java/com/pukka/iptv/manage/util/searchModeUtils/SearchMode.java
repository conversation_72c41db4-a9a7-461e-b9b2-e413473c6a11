package com.pukka.iptv.manage.util.searchModeUtils;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.pukka.iptv.common.base.enums.FtsSearchModeEnum;
import com.pukka.iptv.common.data.model.OutOrderBase;
import com.pukka.iptv.common.data.vo.sys.OutOrderBaseVo;
import com.pukka.iptv.downloader.util.SpringUtils;
import com.pukka.iptv.manage.service.sys.OutOrderBaseService;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;

import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.util.function.BiFunction;

/**
 * @Author: chiron
 * @Date: 2023/06/14/17:21
 * @Description:
 */

@Slf4j
@Data
public class SearchMode {

    private OutOrderBaseService outOrderBaseService = SpringUtils.getBean(OutOrderBaseService.class);

    private FtsSearchModeEnum ftsSearchModeEnum;

    private Page<OutOrderBase> page;

    private OutOrderBaseVo outOrderBaseVo;

    private static Map<FtsSearchModeEnum, BiFunction<Page<OutOrderBase>, OutOrderBaseVo, IPage<OutOrderBase>>> FUNC_MAP = new ConcurrentHashMap<>();

    public SearchMode() {
        log.info("SearchModeService init");
        init();
        log.info("SearchModeService init success");
    }

    public void init() {
        FUNC_MAP.put(FtsSearchModeEnum.DEFAULT, outOrderBaseService::listByOutOrderBaseProperty);
        FUNC_MAP.put(FtsSearchModeEnum.FTS, outOrderBaseService::page);
    }

    public IPage<OutOrderBase> apply() {
        return FUNC_MAP.get(ftsSearchModeEnum).apply(page, outOrderBaseVo);
    }
}
