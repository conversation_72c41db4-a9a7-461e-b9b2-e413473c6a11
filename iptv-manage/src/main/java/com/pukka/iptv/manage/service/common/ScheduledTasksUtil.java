package com.pukka.iptv.manage.service.common;

import com.alibaba.fastjson.JSON;
import com.pukka.iptv.common.data.model.OutScheduledTask;
import java.util.List;
import java.util.stream.Collectors;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @Description 定时任务工具类
 * @Date 2024/5/13 15:28
 * @Version V1.0
 **/
@Slf4j
@Component
public class ScheduledTasksUtil {

    /**
     * 过滤掉已经在ids中的任务
     *
     * @param scheduledTasks
     * @param ids
     * @return
     */
    public List<Long> filterScheduledTask(List<OutScheduledTask> scheduledTasks,
            List<Long> ids) {
        try {
            return scheduledTasks.stream()
                    .map(OutScheduledTask::getContentId)
                    .filter(contentId -> !ids.contains(contentId))
                    .collect(Collectors.toList());
        } catch (Exception e) {
            log.error("过滤定时任务失败，加载定时任务:{},媒资库:{}",
                    JSON.toJSONString(scheduledTasks), ids, e);
            return null;
        }
    }
}
