package com.pukka.iptv.manage.mapper.sys;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import com.pukka.iptv.common.data.model.*;
import org.apache.ibatis.annotations.Param;

/**
 * 下发工单结果
 *
 * @author: zhoul
 * @date: 2021-9-22 18:23:39
 */

@Mapper
public interface OutResultMapper extends BaseMapper<OutResult> {
    OutResult findByCorrelateId(@Param("correlateId") String correlateId);
}
