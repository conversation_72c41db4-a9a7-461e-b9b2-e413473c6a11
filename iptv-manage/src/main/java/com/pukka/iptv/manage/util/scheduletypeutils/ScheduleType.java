package com.pukka.iptv.manage.util.scheduletypeutils;

import com.pukka.iptv.common.base.constant.RedisKeyConstants;
import com.pukka.iptv.common.base.enums.ContentTypeEnum;
import com.pukka.iptv.common.base.enums.FtsSearchModeEnum;
import com.pukka.iptv.common.core.constant.SymbolConstant;
import com.pukka.iptv.common.redis.service.RedisService;
import com.pukka.iptv.downloader.util.SpringUtils;
import com.pukka.iptv.manage.service.bms.*;
import com.pukka.iptv.manage.util.DateUtils;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;

import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.TimeUnit;
import java.util.function.Supplier;

/**
 * @Author: chiron
 * @Date: 2023/06/14/17:21
 * @Description:
 */

@Slf4j
@Data
public class ScheduleType {

    private BmsCategoryContentService bmsCategoryContentService = SpringUtils.getBean(BmsCategoryContentService.class);
    private BmsContentService bmsContentService = SpringUtils.getBean(BmsContentService.class);
    private BmsPackageContentService bmsPackageContentService = SpringUtils.getBean(BmsPackageContentService.class);
    private BmsProgramService bmsProgramService = SpringUtils.getBean(BmsProgramService.class);
    private BmsScheduleService bmsScheduleService = SpringUtils.getBean(BmsScheduleService.class);
    private RedisService redisService = SpringUtils.getBean(RedisService.class);

    private ContentTypeEnum contentTypeEnum;

    private static Map<ContentTypeEnum, Supplier<Boolean>> SUPPLIER_MAP = new ConcurrentHashMap<>();

    public ScheduleType() {
        log.info("ScheduleType init Prepare");
        init();
        log.info("ScheduleType init success");
    }

    public void init() {
        SUPPLIER_MAP.put(ContentTypeEnum.CATEGORY_PROGRAM, bmsCategoryContentService::schedulePublish);
        SUPPLIER_MAP.put(ContentTypeEnum.FILM, bmsContentService::schedulePublish);
        SUPPLIER_MAP.put(ContentTypeEnum.PACKAGE_PROGRAM, bmsPackageContentService::schedulePublish);
        SUPPLIER_MAP.put(ContentTypeEnum.SUBSET, bmsProgramService::schedulePublish);
        SUPPLIER_MAP.put(ContentTypeEnum.SCHEDULE, bmsScheduleService::timedPublish);

    }

    public Boolean apply() {
        String lockKey = RedisKeyConstants.OUT_SCHEDULED_TASKS + SymbolConstant.COLON + contentTypeEnum.getValue();
        boolean isLocked = false;
        try {
            // 尝试获取锁，持有时间30秒
            isLocked = redisService.setIfAbsent(lockKey, DateUtils.getNowString(), 30, TimeUnit.SECONDS);
            if (Boolean.TRUE.equals(isLocked)) {
                log.info("获取定时发布任务成功，锁 key: {}", lockKey);
                return SUPPLIER_MAP.get(contentTypeEnum).get();
            } else {
                log.warn("获取锁失败，多个线程争抢资源，锁 key:{}", lockKey);
                return false;
            }
        } catch (Exception exception) {
            log.error("获取锁失败，，锁 key:{},异常信息：{}", lockKey, exception.getMessage());
            return false;
        } finally {
            if (Boolean.TRUE.equals(isLocked)) {
                // 释放锁
                log.info("释放锁，锁Key：" + lockKey);
                redisService.deleteObject(lockKey);
            }
        }
    }
}
