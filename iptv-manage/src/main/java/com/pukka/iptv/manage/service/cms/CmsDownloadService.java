package com.pukka.iptv.manage.service.cms;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.pukka.iptv.common.base.vo.CommonResponse;
import com.pukka.iptv.common.data.dto.CmsDownloadDto;
import com.pukka.iptv.common.data.model.*;
import com.pukka.iptv.common.data.model.cms.CmsDownload;

import java.util.List;

/**
 *
 * @author: liuli
 * @date: 2021年10月22日 下午3:56:03
 */

public interface CmsDownloadService extends IService<CmsDownload> {
    // 通过查询条件
    Page<CmsDownload> pageByCondition(Page page, CmsDownloadDto cmsDownload);

    CommonResponse updatePriorityByIds(List<Long> idList, Integer priority);

    CommonResponse updateStatusByIds(List<Long> idList);

    CommonResponse updateStatusToCancelledByIds(List<Long> idList);
}


