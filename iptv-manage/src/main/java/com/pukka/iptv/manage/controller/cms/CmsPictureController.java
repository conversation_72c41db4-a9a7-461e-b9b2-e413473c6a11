package com.pukka.iptv.manage.controller.cms;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.pukka.iptv.common.base.vo.CommonResponse;
import com.pukka.iptv.common.data.model.cms.CmsPicture;
import com.pukka.iptv.common.data.vo.IdList;
import com.pukka.iptv.common.data.vo.cms.CmsPictureVO;
import com.pukka.iptv.manage.service.cms.CmsPictureService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.AllArgsConstructor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.List;

/**
 * 图片
 *
 * @author: zhoul
 * @date: 2021-8-30 10:58:58
 */

@RestController
@AllArgsConstructor
@RequestMapping(value = "/cmsPicture", produces = MediaType.APPLICATION_JSON_VALUE)
@Api(tags = "cmsPicture管理")
public class CmsPictureController {

    @Autowired
    private CmsPictureService cmsPictureService;

    @ApiOperation(value = "分页")
    @GetMapping("/page")
    public CommonResponse<Page> page(@Valid Page page, CmsPicture cmsPicture) {
        return CommonResponse.success(cmsPictureService.page(page, Wrappers.query(cmsPicture)));
    }

    @ApiOperation(value = "详情")
    @GetMapping("/getById")
    public CommonResponse<CmsPicture> getById(@Valid @RequestParam(name = "id", required = true) Long id) {
        return CommonResponse.success(cmsPictureService.getById(id));
    }

    @ApiOperation(value = "图片列表")
    @GetMapping("/listById")
    public CommonResponse<List<CmsPictureVO>> listById(@Valid CmsPicture cmsPicture) {
        return CommonResponse.success(cmsPictureService.listById(cmsPicture));
    }

    @ApiOperation(value = "新增")
    @PostMapping("/save")
    public CommonResponse<Boolean> save(@Valid @RequestBody CmsPicture cmsPicture) {
        return CommonResponse.success(cmsPictureService.save(cmsPicture));
    }

    @ApiOperation(value = "修改 只修改序号和图片类型")
    @PutMapping("/update")
    public CommonResponse<String> updateById(@Valid @RequestBody CmsPicture cmsPicture) {
        return cmsPictureService.up(cmsPicture);
    }

    @ApiOperation(value = "删除")
    @DeleteMapping("/deleteById")
    public CommonResponse<Boolean> delPicture(@RequestBody CmsPicture cmsPicture) {
        return cmsPictureService.delPictureCheckStatus(cmsPicture);
    }

    @ApiOperation(value = "批量删除")
    @DeleteMapping("/deleteByIds")
    public CommonResponse<Boolean> deleteByIds(@Valid @RequestBody IdList idList) {
        return CommonResponse.success(cmsPictureService.removeByIds(idList.getIds()));
    }

    @ApiOperation(value = "根据contentId&contentType查询详情")
    @GetMapping("/getByContentIdContentType")
    public CommonResponse<List<CmsPicture>> getByContentIdContentType(@Valid @RequestParam(name = "contentId", required = true) Long contentId, @Valid @RequestParam(name = "contentType", required = true) Integer contentType) {
        return CommonResponse.success(cmsPictureService.getByContentIdContentType(contentId, contentType));
    }

    @ApiOperation(value = "根据code获取详情")
    @GetMapping("/getByCode")
    public CommonResponse<CmsPicture> getByCode(@Valid @RequestParam(name = "code", required = true) String code) {
        return CommonResponse.success(cmsPictureService.getByCode(code));
    }
}
