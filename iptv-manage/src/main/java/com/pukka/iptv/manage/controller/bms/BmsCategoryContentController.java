package com.pukka.iptv.manage.controller.bms;

import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.pukka.iptv.common.base.constant.PublishParamTypeConstants;
import com.pukka.iptv.common.base.enums.ContentTypeEnum;
import com.pukka.iptv.common.base.enums.FtsSearchModeEnum;
import com.pukka.iptv.common.base.enums.PriorityEnums;
import com.pukka.iptv.common.base.vo.CommonResponse;
import com.pukka.iptv.common.data.model.OutOrderBase;
import com.pukka.iptv.common.data.model.OutParamExpand;
import com.pukka.iptv.common.data.model.bms.BmsCategoryContent;
import com.pukka.iptv.common.data.vo.IdVO;
import com.pukka.iptv.common.data.vo.req.*;
import com.pukka.iptv.common.data.vo.resp.R;
import com.pukka.iptv.common.data.vo.resp.Rt;
import com.pukka.iptv.common.log.annotation.SysLog;
import com.pukka.iptv.common.log.enums.OperateObjectEnum;
import com.pukka.iptv.common.log.enums.OperateTypeEnum;
import com.pukka.iptv.manage.service.bms.BmsCategoryContentService;
import com.pukka.iptv.manage.service.bms.BmsPublishParamApi;
import com.pukka.iptv.manage.util.scheduletypeutils.ScheduleTypeFactory;
import com.pukka.iptv.manage.util.searchModeUtils.factory.SearchModeFactory;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.AllArgsConstructor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 栏目
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2021-08-27 10:20:57
 */

@RestController
@AllArgsConstructor
@RequestMapping(value = "/bmsCategoryContent", produces = MediaType.APPLICATION_JSON_VALUE)
@Api(tags = "BMS栏目和内容关系管理")
public class BmsCategoryContentController implements BmsPublishParamApi {


    @Autowired
    private BmsCategoryContentService bmsCCS;

    @ApiOperation(value = "分页")
    @GetMapping("/page")
    public CommonResponse<?> page(@Valid @RequestBody BmsCategoryContentQueryReq req) {
        return R.page(bmsCCS.pageList(req));
    }

    @ApiOperation(value = "详情")
    @GetMapping("/getById")
    public CommonResponse<BmsCategoryContent> getById(@Valid @RequestParam(name = "id", required = true) Long id) {
        return R.success(bmsCCS.getById(id));
    }


    @GetMapping("/getByCategoryContentId")
    @ApiOperation(value = "获取节目信息")
    public CommonResponse<List<BmsCategoryContent>> getByCategoryContentId(@Valid @RequestParam(name = "id", required = true) Long id) {
        return R.success(bmsCCS.getByCategoryContentId(id));
    }

    @PostMapping("/queryBindList")
    @ApiOperation(value = "查询栏目内容关系")
    public CommonResponse<?> queryBindList(@Valid @RequestBody BmsCategoryContentQueryReq req) {
        return R.page(bmsCCS.queryCategoryBindContentList(req));
    }

    @PostMapping("/export")
    @ApiOperation(value = "栏目关系导出")
    public CommonResponse export(@Valid @RequestBody BmsCategoryContentQueryReqExtent req) throws Exception {
            return CommonResponse.success(bmsCCS.export(req));
    }


    @PostMapping("/queryNotBindList")
    @ApiOperation(value = "查询未绑定该栏目的内容列表")
    public CommonResponse<?> queryContentsWhichNotBindThatCategory(@Valid @RequestBody BmsCategoryNotBindContentQueryReq req) {
        return R.page(bmsCCS.queryContentsWhichNotBindThatCategory(req));
    }

    @SysLog(objectType = OperateObjectEnum.BMS_CATEGORY_CONTENT, operateType = OperateTypeEnum.CATEGORY_CONTENT_REMOVE, objectIds = "#ids")
    @ApiOperation(value = "内容和栏目解绑")
    @PutMapping("/unbind")
    public CommonResponse<String> unbind(@Valid @RequestBody List<Long> ids) {
        boolean ok = bmsCCS.unbind(ids);
        return ok ? R.success("解绑成功！") : R.fail("解绑失败!");
    }

    @SysLog(operateType = OperateTypeEnum.CONTENT_CATEOGRY_BIND, objectType = OperateObjectEnum.BMS_CONTENT, objectIds = "#req.contentIds")
    @ApiOperation(value = "内容绑定栏目")
    @PutMapping("/bind")
    public CommonResponse<String> contentBind(@Valid @RequestBody BmsCategoryContentBindReq req) {

        Rt<List<Long>> rt = bmsCCS.bind(req.getCategoryIds(), req.getContentIds(), req.getSource());
        if (!rt.isOk()) {
            return R.fail("绑定失败！");
        }

        if (!ObjectUtil.isEmpty(rt.getData()) && req.isNeedPublish()) {
            Map<String, OutParamExpand> paramMap = new HashMap<>();
            paramMap.put(PublishParamTypeConstants.PRIORITY, new OutParamExpand().setPriority(PriorityEnums.GENERAL.getValue()));
            //进行发布
            bmsCCS.publish(rt.getData(), null, paramMap);
        }
        return R.success("绑定成功！");
    }

    @SysLog(operateType = OperateTypeEnum.CATEOGY_CONTENT_RECYCLE, objectType = OperateObjectEnum.BMS_CATEGORY_CONTENT, objectIds = "#ids")
    @ApiOperation(value = "栏目关系回收")
    @PutMapping("/recycle")
    public CommonResponse<Boolean> categoryContentRecycle(@RequestBody List<Long> ids) {
        setParam(PublishParamTypeConstants.PRIORITY, new OutParamExpand().setPriority(0));
        boolean result = bmsCCS.recycle(ids);
        clearParm();
        return CommonResponse.success(result);
    }

    @SysLog(objectType = OperateObjectEnum.BMS_CATEGORY_CONTENT, operateType = OperateTypeEnum.CATEGORY_CONTENT_PUBLISH, objectIds = "#publishReq.ids")
    @ApiOperation(value = "栏目关系发布")
    @PutMapping("/publish")
    public CommonResponse<Boolean> publish(@Valid @RequestBody BmsCategoryContentPublishReq publishReq) {
        Map<String, OutParamExpand> paramMap = new HashMap<>();
        paramMap.put(PublishParamTypeConstants.PRIORITY, new OutParamExpand().setPriority(PriorityEnums.GENERAL.getValue()));
        return CommonResponse.success(bmsCCS.publish(publishReq.getIds(), publishReq.getTiming(), paramMap));
    }

    @ApiOperation(value = "定时发布")
    @PostMapping("/schedulePublish")
    public CommonResponse<Boolean> contentSchedulePublish() {
        try {
            Boolean apply = ScheduleTypeFactory.initMode(ContentTypeEnum.CATEGORY_PROGRAM).apply();
            return CommonResponse.success(apply);
        } catch (Exception e) {
            return CommonResponse.fail("栏目内容定时发布失败");
        }
    }

    @SysLog(objectType = OperateObjectEnum.BMS_CONTENT, operateType = OperateTypeEnum.CONTENT_CANCEL_TIMED_PUBLISH, objectIds = "#id")
    @ApiOperation(value = "取消定时发布")
    @PutMapping("/cancelTimedPublish")
    public CommonResponse<String> cancelTimedPublish(@RequestBody @Valid IdVO idVO) {
        bmsCCS.cancelTimedPublish(Long.parseLong(idVO.getId()));
        return R.success("取消定时发布成功");
    }

    @SysLog(objectType = OperateObjectEnum.BMS_CATEGORY_CONTENT, operateType = OperateTypeEnum.CATEGORY_CONTENT_UPDATE_PUBLISH_STATUS, objectIds = "#updateReq.ids")
    @ApiOperation(value = "栏目关系修改发布状态")
    @PutMapping("/updatePublishStatus")
    public CommonResponse<Boolean> publish(@Valid @RequestBody BmsCategoryContentPublishUpdateReq updateReq) {
        return CommonResponse.success(bmsCCS.modifyPublishStatus(updateReq.getIds(), updateReq.getPublishStatus()));
    }

    @SysLog(operateType = OperateTypeEnum.CONTENT_CATEGORY_SORT, objectType = OperateObjectEnum.BMS_CATEGORY_CONTENT, objectIds = "#list")
    @ApiOperation(value = "栏目关系多选排序")
    @PutMapping("/sort")
    public CommonResponse<Boolean> sort(@Valid @RequestBody List<BmsCategoryContentSortReq> list) {
        return CommonResponse.success(bmsCCS.sort(list));
    }

    @SysLog(objectType = OperateObjectEnum.BMS_CATEGORY_CONTENT, operateType = OperateTypeEnum.CATEGORY_CONTENT_RESET_PUBLISH_STATUS, objectIds = "#ids")
    @ApiOperation(value = "栏目关系重置发布状态")
    @PutMapping("/resetPublishStatus")
    public CommonResponse<Boolean> resetPublishStatus(@RequestBody List<Long> ids) {
        return CommonResponse.success(bmsCCS.resetPublishStatus(ids));
    }
}
