package com.pukka.iptv.manage.controller.bms;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.ObjectUtils;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.pukka.iptv.common.base.constant.PublishParamTypeConstants;
import com.pukka.iptv.common.base.enums.StatusEnum;
import com.pukka.iptv.common.base.vo.CommonResponse;
import com.pukka.iptv.common.data.model.OutParamExpand;
import com.pukka.iptv.common.data.model.bms.BmsCategory;
import com.pukka.iptv.common.data.model.bms.BmsCategoryChannel;
import com.pukka.iptv.common.data.model.bms.BmsChannel;
import com.pukka.iptv.common.data.params.SysAuthorizationParam;
import com.pukka.iptv.common.data.util.Tuple2;
import com.pukka.iptv.common.data.vo.IdList;
import com.pukka.iptv.common.data.vo.bms.*;
import com.pukka.iptv.common.data.vo.req.BmsCategoryChanneldeleteReq;
import com.pukka.iptv.common.data.vo.req.BmsChannelUnbindReq;
import com.pukka.iptv.common.log.annotation.SysLog;
import com.pukka.iptv.common.log.enums.OperateObjectEnum;
import com.pukka.iptv.common.log.enums.OperateTypeEnum;
import com.pukka.iptv.manage.service.bms.BmsCategoryChannelService;
import com.pukka.iptv.manage.service.bms.BmsCategoryService;
import com.pukka.iptv.manage.service.bms.BmsChannelService;
import com.pukka.iptv.manage.service.bms.BmsPublishParamApi;
import com.pukka.iptv.manage.service.condition.rule.enums.PublishCheck;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.AllArgsConstructor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.text.ParseException;
import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;


/**
 * 频道
 *
 * <AUTHOR>
 * @email
 * @date 2021-08-27 14:45:33
 */
@RestController
@AllArgsConstructor
@RequestMapping(value = "/bmsChannel", produces = MediaType.APPLICATION_JSON_VALUE)
@Api(tags = "bmsChannel管理")
public class BmsChannelController implements BmsPublishParamApi
{
    @Autowired
    private BmsChannelService bmsChannelService;
    @Autowired
    private BmsCategoryService categoryService;
    @Autowired
    private BmsCategoryChannelService categoryChannelService;


    @ApiOperation(value = "频道查询")
    @GetMapping("/page")
    public CommonResponse<IPage<BmsChannel>> page(Page page, BmsChannel bmsChannel, String categoryId, Integer status) {
        if (bmsChannel.getSpId() == null) {
            CommonResponse.commonfail("spid不能为空");
        }
        return CommonResponse.success(bmsChannelService.getChannelsPage(page, bmsChannel, categoryId, status));
    }


    @ApiOperation(value = "详情")
    @GetMapping("/getById")
    public CommonResponse<BmsChannel> getById(@Valid @RequestParam(name = "id", required = true) Long id) {
        return CommonResponse.success(bmsChannelService.getById(id));
    }

    @ApiOperation(value = "新增")
    @PostMapping("/save")
    public CommonResponse<Boolean> save(@Valid @RequestBody BmsChannel bmsChannel) {
        return CommonResponse.success(bmsChannelService.save(bmsChannel));
    }

    @ApiOperation(value = "修改")
    @PutMapping("/update")
    public CommonResponse<Boolean> updateById(@Valid @RequestBody BmsChannel bmsChannel) {
        return CommonResponse.success(bmsChannelService.updateById(bmsChannel));
    }

    @ApiOperation(value = "删除")
    @DeleteMapping("/deleteById")
    public CommonResponse<Boolean> deleteById(@Valid @RequestParam(name = "id", required = true) Long id) {
        return CommonResponse.success(bmsChannelService.removeById(id));
    }

    @ApiOperation(value = "批量删除")
    @DeleteMapping("/deleteByIds")
    public CommonResponse<Boolean> deleteByIds(@Valid @RequestBody IdList idList) {
        return CommonResponse.success(bmsChannelService.removeByIds(idList.getIds()));
    }


    /**
     * 频道发布
     * ids  频道ids
     * doSchedule 是否定时发布 1定时 2 立即
     * scheduleTime 定时发布时间
     * widthPhysicalChannel 是否发布物理频道  1 发布 2 不发布
     *
     * @return
     */
    @SysLog(objectType = OperateObjectEnum.BMS_CHANNEL, operateType = OperateTypeEnum.PUBLISH, objectIds = "#vo.ids")
    @ApiOperation(value = "频道发布")
    @PutMapping("/publish")
    public CommonResponse<String> publishChannel(@Valid @RequestBody BmsChannelVo vo) throws ParseException
    {
        String ids = vo.getIds();
        Integer doSchedule = vo.getDoSchedule();
        String scheduleTime = vo.getScheduleTime();
        String widthPhysicalChannel = vo.getWidthPhysicalChannel();
        String[] channelIds = ids.split(",");
        List<Long> channelIdList = Arrays.stream(channelIds)
                .map(s -> Long.parseLong(s.trim())).collect(Collectors.toList());
        //查询到前端传来的id的所有发布状态
        List<BmsChannel> bmschannels = bmsChannelService.getStatusByIds(channelIdList);
        //频道发布逻辑
        setParam(PublishParamTypeConstants.PRIORITY,new OutParamExpand().setPriority(vo.getPriority()));
        boolean result = bmsChannelService.channelPublish(bmschannels, doSchedule, scheduleTime, widthPhysicalChannel);
        clearParm();
        return result ? CommonResponse.success("频道发布接口调用成功") :  CommonResponse.commonfail("发布失败");
    }

    @SysLog(objectType = OperateObjectEnum.BMS_CHANNEL, operateType = OperateTypeEnum.ROLLBACK, objectIds = "#ids")
    @ApiOperation(value = "频道回收")
    @PutMapping("/rollback")
    public CommonResponse<String> rollbackChannel(@RequestBody List<Long> ids) {
        if (ObjectUtils.isEmpty(ids)) {
            return CommonResponse.fail("参数错误");
        }
        //查询到前端传来的id的所有发布状态
        List<BmsChannel> bmschannels = bmsChannelService.getStatusByIds(ids);
        if (bmschannels.size() == 0) {
            return CommonResponse.commonfail("未查询到所选频道发布状态");
        }
        //频道回收
        setParam(PublishParamTypeConstants.PRIORITY,new OutParamExpand().setPriority(0));
        String result = bmsChannelService.channelRollback(bmschannels);
        clearParm();
        return CommonResponse.success(result);
    }

    @SysLog(objectType = OperateObjectEnum.BMS_CHANNEL, operateType = OperateTypeEnum.ROLLBACK_ALL_CHANNEL, objectIds = "#ids")
    @ApiOperation(value = "频道一键回收")
    @PutMapping("/rollabck_all")
    public CommonResponse<String> rollbackAllChannel(@RequestBody List<Long> ids) {
        //查询到前端传来的id的所有发布状态

        List<BmsChannel> bmschannels = bmsChannelService.getStatusByIds(ids);
        if (bmschannels.size() == 0) {
            return CommonResponse.commonfail("未查询到所选频道发布状态");
        }
        setParam(PublishParamTypeConstants.PRIORITY,new OutParamExpand().setPriority(0));
        bmsChannelService.channelRollbackAll(bmschannels);
        clearParm();
        return CommonResponse.success("频道回收成功");
    }


    @ApiOperation(value = "频道和栏目关系_查询关系列表")
    @GetMapping("/category/query")
    public CommonResponse<IPage<BmsCategoryChannelPageVO>> queryCategoryContentChannel(Page page, @Valid BmsCategoryChannel categoryChannel, String name, Long cpId) {
        IPage categoryContentChannelpage = categoryChannelService.getCategoryContentChannelpage(page, categoryChannel, name, cpId);
        categoryContentChannelpage.getRecords().remove(null);
        return CommonResponse.success(categoryContentChannelpage);
    }

    @SysLog(objectType = OperateObjectEnum.BMS_CHANNEL, operateType = OperateTypeEnum.CATEGORY_BIND_CHANNEL, objectIds = "#categoryContentVO.ids")
    @ApiOperation(value = "频道绑定栏目")
    @PostMapping("/category/bind")
    public CommonResponse<String> categoryBindChannel(@Valid @RequestBody BmsCategoryContentVO categoryContentVO) {
        String[] channelIds = categoryContentVO.getIds().split(",");
        String[] categoryIds = categoryContentVO.getCategoryIds().split(",");

        //判断栏目是否被锁定 二元数组第一个参数是被锁定或者无法查询到的ids，第二个参数是未被锁定的栏目list
        Tuple2<Boolean, List<BmsCategory>> lockStatus = categoryService.getLockStatus(categoryIds);
        if (!lockStatus.getA().get()) {
            return CommonResponse.commonfail(lockStatus.getB().get().get(0).getName() + "已被锁定");
        }
        List<Long> channels = Arrays.stream(channelIds)
                .map(s -> Long.parseLong(s.trim())).collect(Collectors.toList());
        //绑定之前先查询关系表中是否有该频道和栏目的绑定关系
        Tuple2<Boolean, List<Tuple2<Long, BmsCategory>>> bind = categoryChannelService.isRepeatBind(lockStatus.getB().get(), channels);
        //将未锁定的栏目id跟频道进行绑定
        if (!bind.getA().get())
        {
            return CommonResponse.commonfail("绑定失败");
        }
        Tuple2<Boolean, String> result = categoryChannelService.categoryBindChannel(bind.getB().get());
        return result.getA().get() ? CommonResponse.success("绑定成功") : CommonResponse.commonfail("绑定失败");

    }

    @SysLog(objectType = OperateObjectEnum.BMS_CATEGORY_CHANNEL, operateType = OperateTypeEnum.PUBLISH, objectIds = "#vo.ids")
    @ApiOperation(value = "频道和栏目关系_发布")
    @PutMapping("/category/publish")
    public CommonResponse<String> categoryChannelPublish(@Valid @RequestBody BmsCategoryChannelVo vo) throws ParseException {
        String[] ids = vo.getIds().split(",");
        Integer doSchedule = vo.getDoSchedule();
        String scheduleTime = vo.getScheduleTime();
        List<Long> categoryIds = vo.getCategoryIds();
        List<Long> channelIds = vo.getChannelIds();
        List<Long> publishIds = Arrays.stream(ids)
                .map(s -> Long.parseLong(s.trim())).collect(Collectors.toList());
        categoryChannelService.checkRelation(publishIds, categoryIds, PublishCheck.CAN_PUBLISH);
        //检查频道与栏目是否发布
        categoryChannelService.checkPulishStatus(channelIds, categoryIds);
        //关系发布
        setParam(PublishParamTypeConstants.PRIORITY,new OutParamExpand().setPriority(vo.getPriority()));
        boolean b = categoryChannelService.categorychannelPublish(publishIds, doSchedule, scheduleTime);
        clearParm();
        return b ? CommonResponse.success("发布成功") : CommonResponse.commonfail("发布失败");
    }

    @SysLog(objectType = OperateObjectEnum.BMS_CATEGORY_CHANNEL, operateType = OperateTypeEnum.ROLLBACK, objectIds = "#ids")
    @ApiOperation(value = "频道和栏目关系_回收")
    @PutMapping("/category/rollback")
    public CommonResponse<String> categoryChannelRollback(@RequestBody List<Long> ids) {
        if (ObjectUtils.isEmpty(ids)) {
            return CommonResponse.commonfail("ids不能为空");
        }
        //关系回收
        setParam(PublishParamTypeConstants.PRIORITY,new OutParamExpand().setPriority(0));
        boolean status = categoryChannelService.categorychannelRollback(ids);
        clearParm();
        return status ? CommonResponse.success("回收成功") : CommonResponse.commonfail("回收失败");

    }

    @SysLog(objectType = OperateObjectEnum.BMS_CHANNEL, operateType = OperateTypeEnum.RESET_PUBLISH_STATUS, objectIds = "#idList")
    @ApiOperation(value = "频道重置发布状态")
    @PutMapping("/reset_publish")
    public CommonResponse<Boolean> resetPublishStatus(@RequestBody List<Long> idList) {
        if (ObjectUtils.isEmpty(idList)) {
            CommonResponse.commonfail("id不能为空");
        }
        Boolean status = bmsChannelService.resetPublishStatus(idList);
        return status ? CommonResponse.success(true) : CommonResponse.commonfail("重置发布状态失败");
    }

    @SysLog(objectType = OperateObjectEnum.BMS_CHANNEL, operateType = OperateTypeEnum.UPDATE_PUBLISH_STATUS, objectIds = "#publishStatusVo.idList")
    @ApiOperation(value = "频道修改发布状态")
    @PutMapping("/change_publish_status")
    public CommonResponse<Boolean> updatePublishStatus(@RequestBody @Valid PublishStatusVo publishStatusVo) {
        List<Long> idList = publishStatusVo.getIdList();
        Integer publishStatus = publishStatusVo.getPublishStatus();
        Boolean status = bmsChannelService.updatePublishStatus(idList, publishStatus);
        return status ? CommonResponse.success(true) : CommonResponse.commonfail("修改发布状态失败");
    }

    @SysLog(objectType = OperateObjectEnum.BMS_CATEGORY_CHANNEL, operateType = OperateTypeEnum.RESET_PUBLISH_STATUS, objectIds = "#idList")
    @ApiOperation(value = "频道与栏目重置发布状态")
    @PutMapping("/category/publish_status_reset")
    public CommonResponse<Boolean> categoryChannelresetPublishStatus(@RequestBody List<Long> idList) {
        if (ObjectUtils.isEmpty(idList)) {
            CommonResponse.commonfail("id不能为空");
        }
        Boolean status = categoryChannelService.resetPublishStatus(idList);
        return status ? CommonResponse.success(true) : CommonResponse.commonfail("重置发布状态失败");

    }
    @SysLog(objectType = OperateObjectEnum.BMS_CATEGORY_CHANNEL, operateType = OperateTypeEnum.UPDATE_PUBLISH_STATUS, objectIds = "#publishStatusVo.idList")
    @ApiOperation(value = "频道与栏目修改发布状态")
    @PutMapping("/category/status")
    public CommonResponse<Boolean> categoryChannelupdatePublishStatus(@RequestBody @Valid PublishStatusVo publishStatusVo) {
        List<Long> idList = publishStatusVo.getIdList();
        Integer publishStatus = publishStatusVo.getPublishStatus();
        Boolean status = categoryChannelService.updatePublishStatus(idList, publishStatus);
        return status ? CommonResponse.success(true) : CommonResponse.commonfail("修改发布状态失败");
    }
    @ApiOperation(value = "频道和栏目关系_排序")
    @PutMapping("/category/sort")
    public CommonResponse<Boolean> categoryChannelSort(@RequestBody List<BmsCategoryChannel> list) {
        if (ObjectUtils.isEmpty(list)) {
            return CommonResponse.commonfail("参数不可为空");
        }
        Boolean reorder = categoryChannelService.reorder(list);
        return reorder ? CommonResponse.success(true) : CommonResponse.commonfail("重排序失败");
    }
    @SysLog(objectType = OperateObjectEnum.BMS_CATEGORY_CHANNEL, operateType = OperateTypeEnum.DELETE_TIMED, objectIds = "#id")
    @ApiOperation(value = "频道和栏目关系_取消定时发布")
    @PutMapping("/category/cancel_timed_publish")
    public CommonResponse<String> deleteTiming(@RequestBody String id) {
        Tuple2<Boolean, String> tuple2 = categoryChannelService.deleteTiming(id);
        return tuple2.getA().get() ? CommonResponse.success(tuple2.getB().get()) : CommonResponse.commonfail(tuple2.getB().get());
    }


    @SysLog(objectType = OperateObjectEnum.BMS_CATEGORY_CHANNEL, operateType = OperateTypeEnum.DELETE, objectIds = "#req.categoryIds")
    @ApiOperation(value = "频道和栏目关系_移除")
    @DeleteMapping("/category/delete")
    public CommonResponse<Boolean> delete(@Valid @RequestBody BmsCategoryChanneldeleteReq req) {

        //检测栏目是否锁定
        categoryChannelService.checkLockStatus(req.getCategoryIds());
        //删除栏目与频道关系
        Tuple2<Boolean, String> delete = categoryChannelService.delete(req.getCategoryIds(), req.getChannelIds());
        return delete.getA().get() ? CommonResponse.success(true) : CommonResponse.commonfail(delete.getB().get());
    }
    @SysLog(objectType = OperateObjectEnum.BMS_CHANNEL, operateType = OperateTypeEnum.ACTIVE_OR_POSITIVE, objectIds = "#publishStatusVo.idList")
    @ApiOperation(value = "频道设置有效/失效")
    @PutMapping("/reset_status")
    public CommonResponse<Boolean> resetStatus(@RequestBody @Valid PublishStatusVo publishStatusVo) {
        List<Long> idList = publishStatusVo.getIdList();
        Integer publishStatus = publishStatusVo.getPublishStatus();
        Boolean status = bmsChannelService.setStatus(idList, StatusEnum.trans(publishStatus));
        return status ? CommonResponse.success(true) : CommonResponse.commonfail("设置失败");
    }

    @ApiOperation(value = "频道和栏目关系_查询未添加列表")
    @GetMapping("/category/unbindList")
    public CommonResponse<IPage<BmsChannel>> queryCategoryContentChannel(Page page, @Valid BmsChannelUnbindReq req) {
        IPage notBindChannelsPage = bmsChannelService.getNotBindChannelsPage(page, req.getCategoryId(), req.getSpId(), req.getChannelName(), req.getCpId());
        notBindChannelsPage.getRecords().remove(null);
        return CommonResponse.success(notBindChannelsPage);
    }

    @ApiOperation(value = "授权频道-查询授权频道列表")
    @GetMapping("/getAuthorizationChannelList")
    public CommonResponse getAuthorizationChannelList(@Valid Page page, SysAuthorizationParam param) {
        return CommonResponse.success(bmsChannelService.getAuthorizationChannelList(page, param));
    }


}
