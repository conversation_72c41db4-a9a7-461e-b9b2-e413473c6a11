package com.pukka.iptv.manage.virtual;

import com.pukka.iptv.common.base.enums.ActionEnums;
import com.pukka.iptv.common.base.enums.ContentTypeEnum;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @description: 媒资类型
 * 1：单集   2：子集   3：电视剧   4：系列片   
 * 5：片花6：直播   7：物理频道   8：栏目   
 * 9：产品包   10：节目单   11：图片  12：视频介质   
 * 13：节目图片   14：剧集图片   15：产品包图片   16：栏目图片   17：频道图片
 * 18：栏目节目   19：栏目剧集   20：栏目频道   21：产品包节目   22：产品包剧集
 * @create 2021-08-30 16:11
 */
public interface IVirtualStrategyHandler<T extends Serializable> {

    /**
     * 策略类型的方法
     * @return
     */
    ActionEnums getContentType();


    /**
     * 处理方法
     * @param contentType
     * @param bmsContentId
     */
    void execute(ContentTypeEnum contentType, Long bmsContentId);
}
