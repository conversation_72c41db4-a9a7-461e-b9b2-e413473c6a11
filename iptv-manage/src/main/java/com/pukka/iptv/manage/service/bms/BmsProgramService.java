package com.pukka.iptv.manage.service.bms;


import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.pukka.iptv.common.base.enums.ActionEnums;
import com.pukka.iptv.common.data.model.OutParamExpand;
import com.pukka.iptv.common.data.model.bms.BmsContent;
import com.pukka.iptv.common.data.model.bms.BmsProgram;
import com.pukka.iptv.common.data.model.order.SubOrderMappingsEntity;
import com.pukka.iptv.common.data.model.order.SubOrderObjectsEntity;
import com.pukka.iptv.common.data.vo.CodeList;
import com.pukka.iptv.common.data.vo.req.BmsSubProgramQueryReq;
import com.pukka.iptv.manage.service.bms.dto.BmsProgramRecycleDto;

import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * 子集内容表
 *
 * <AUTHOR>
 * @date 2021-09-02 17:02:20 extends IService<BmsProgram>
 */
public interface BmsProgramService extends IService<BmsProgram>, BmsRecycleApi<BmsProgramRecycleDto, BmsProgram>, BmsPublishParamApi,PriorityPublishApi, BmsSchedulePublishApi {

    boolean updatePublishStatus(List<SubOrderObjectsEntity> orderObjectsEntities, List<Long> spIdList);

    IPage<?> pageList(BmsSubProgramQueryReq req);

    //子集发布接口
    boolean publish(List<Long> ids, Long pid, boolean doSchedule, Date scheduleTime,Map<String, OutParamExpand> paramMap);

    boolean schedulePublish();

    boolean recycle(List<Long> ids, Long pid);

    //修改发布状态
    boolean modifyPublishStatus(List<Long> ids, Long pid, Integer publishStatus);

    //重置发布状态
    boolean resetPublishStatus(List<Long> ids, Long pid);

    //根据剧头的cms_content_id+sp_id == 子集cms_series_id+sp_id 获取对应的子集
    List<Long> getProgramIds(List<BmsContent> list);

    List<Long> getProgramIds(Long cmsContentId, Long spId);


    // 子集生效失效
    boolean updateStatus(Long pid, List<Long> ids, Integer status, boolean needPublish);

    // 子集删除并删除图片
    boolean programRemove(List<Long> ids);

    boolean cancelTimedPublish(Long id);

    // 工单下发
    boolean sendProgramOrder(ActionEnums actionEnums, List<Long> programIds, Long spId, String spName, Map<String, OutParamExpand> paramMap);

    boolean updateCpFeedback(List<Long> programList, int result);

    /**
     * 子集一键回收
     * @param ids
     * @return
     */
    boolean batchRecycle(List<Long> ids);

    boolean deleteByCodeAndSp(List<String> delProgramEntities, List<Long> spIdList, boolean isRollback);

    boolean deleteSeriesProgramMapping(List<SubOrderMappingsEntity> deleteSeriesProgram, List<Long> spIdList);

    void deleteByCmsContentIds(List<Long> programIdList);

    BmsContent getSeriesBySubProgramIds(Long programId);

    List<BmsProgram> getByProgramCondition(CodeList codeList);
}

