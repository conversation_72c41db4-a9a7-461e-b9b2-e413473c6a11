package com.pukka.iptv.manage.controller.sys;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.pukka.iptv.common.base.vo.CommonResponse;
import com.pukka.iptv.common.data.model.sys.SysInPassage;
import com.pukka.iptv.common.data.vo.IdList;
import com.pukka.iptv.common.log.annotation.SysLog;
import com.pukka.iptv.common.log.enums.OperateObjectEnum;
import com.pukka.iptv.common.log.enums.OperateTypeEnum;
import com.pukka.iptv.manage.service.sys.SysInPassageService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.AllArgsConstructor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;

/**
 *
 * @author: chenyudong
 * @date: 2021-9-13 16:58:09
 */

@RestController
@AllArgsConstructor
@RequestMapping(value = "/sysInPassage", produces = MediaType.APPLICATION_JSON_VALUE)
@Api(tags="sysInPassage管理")
public class SysInPassageController {

    @Autowired
    private SysInPassageService sysInPassageService;

    @ApiOperation(value = "分页")
    @GetMapping("/page" )
    public CommonResponse<Page> page(@Valid Page<SysInPassage> page, SysInPassage sysInPassage) {
        return  CommonResponse.success(sysInPassageService.page(page, sysInPassage));
    }

    @ApiOperation(value = "详情")
    @GetMapping("/getById")
    public CommonResponse<SysInPassage> getById(@Valid @RequestParam(name = "id", required = true)  Long id) {
        return CommonResponse.success(sysInPassageService.getById(id));
    }

    @SysLog(objectType = OperateObjectEnum.SYS_IN_PASSAGE, operateType = OperateTypeEnum.SAVE, objectIds = "#sysInPassage.id", objectNames = "#sysInPassage.name")
    @ApiOperation(value = "新增")
    @PostMapping("/save")
    public CommonResponse<Boolean> save(@Valid @RequestBody SysInPassage sysInPassage) {
        return  CommonResponse.success(sysInPassageService.saveToCacheAndDB(sysInPassage));
    }

    @SysLog(objectType = OperateObjectEnum.SYS_IN_PASSAGE, operateType = OperateTypeEnum.UPDATE, objectIds = "#sysInPassage.id", objectNames = "#sysInPassage.name")
    @ApiOperation(value = "修改")
    @PutMapping("/update")
    public CommonResponse<Boolean> updateById(@Valid @RequestBody SysInPassage sysInPassage) {
        return CommonResponse.success(sysInPassageService.updateCacheAndDB(sysInPassage));
    }

    @SysLog(objectType = OperateObjectEnum.SYS_IN_PASSAGE, operateType = OperateTypeEnum.DELETE, objectIds = "#id")
    @ApiOperation(value = "删除" ,hidden = true)
    @DeleteMapping("/deleteById" )
    public CommonResponse<Boolean> deleteById(@Valid @RequestParam(name = "id", required = true) Long id) {
        return  CommonResponse.success(sysInPassageService.removeDBAndCache(id));
    }

    @SysLog(objectType = OperateObjectEnum.SYS_IN_PASSAGE, operateType = OperateTypeEnum.DELETE, objectIds = "#idList.ids")
    @ApiOperation(value = "批量删除")
    @DeleteMapping("/deleteByIds" )
    public CommonResponse<Boolean> deleteByIds(@Valid @RequestBody IdList idList) {
        return  CommonResponse.success(sysInPassageService.removeDBAndCache(idList.getIds()));
    }

    @ApiOperation(value = "详情")
    @GetMapping("/getByName")
    public CommonResponse<SysInPassage> getByName(@Valid @RequestParam(name = "name", required = true)  String name) {
        LambdaQueryWrapper<SysInPassage> wrapper =new LambdaQueryWrapper<>();
        wrapper.eq(SysInPassage::getCspId, name);
        return CommonResponse.success(sysInPassageService.getOne(wrapper));
    }

}
