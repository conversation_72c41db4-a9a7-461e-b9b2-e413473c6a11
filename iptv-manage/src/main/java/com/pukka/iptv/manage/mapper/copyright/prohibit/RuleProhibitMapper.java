package com.pukka.iptv.manage.mapper.copyright.prohibit;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.pukka.iptv.common.data.dto.RuleProhibitDto;
import com.pukka.iptv.common.data.model.copyright.RuleProhibit;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * @Author: chiron
 * @Date: 2022/07/25/10:12
 * @Description:
 */
@Mapper
public interface RuleProhibitMapper extends BaseMapper<RuleProhibit> {

    /**
     * 批量插入
     * @param records 记录
     * @return 结果
     */
    int batchInsert(@Param("records") List<RuleProhibit> records);

    /**
     * 查询
     * @param page
     * @param ruleProhibit
     * @return
     */
    Page<RuleProhibit> selectListPage(Page page, @Param("vo") RuleProhibitDto ruleProhibit);
}
