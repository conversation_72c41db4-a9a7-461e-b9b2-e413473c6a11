package com.pukka.iptv.manage.config;

import lombok.SneakyThrows;

import javax.servlet.*;
import javax.servlet.annotation.WebFilter;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletRequestWrapper;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.io.UnsupportedEncodingException;
import java.net.URLDecoder;

/**
 * @Author: zhengcl
 * @Date: 2021/12/23 22:05
 */

@WebFilter(urlPatterns = "/*",filterName = "CharacterEncodingFilter")
public class CharacterEncodingFilter implements Filter {
    @Override
    public void init(FilterConfig filterConfig) throws ServletException {
    }

    @Override
    public void doFilter(ServletRequest servletRequest, ServletResponse servletResponse, FilterChain filterChain) throws IOException, ServletException {
        HttpServletRequest request = (HttpServletRequest) servletRequest;
//        HttpServletRequestWrapper httpServletRequestWrapper = new HttpServletRequestWrapper(request);
        RequestParameterWrapper requestParameterWrapper = new RequestParameterWrapper(request);
        requestParameterWrapper.addParameters(request.getParameterMap());
        request.getRequestDispatcher(request.getRequestURI()).forward(requestParameterWrapper,servletResponse);
        /*requestParameterWrapper.addParameters(extraParams);
        httpRequest.getRequestDispatcher(path).forward(requestParameterWrapper,servletResponse);*/
//        {
//            /**
//             * 当调用request.getHeader("token")时，则获取请求参数中token值并当做Header的值返回
//             * @param
//             * @return
//             */
//           /* @Override
//            public String getParameter(String name) {
//                if("newParam".equals(name)){
//                    return "这是我新增加的参数";
//                }
//                return super.getParameter(name);
//            }
//            @Override
//            public Map<String, String[]> getParameterMap() {
//                HashMap<String, String[]> newMap = new HashMap<>();
//                newMap.putAll(super.getParameterMap());
//                newMap.put("newParam",new String[]{"这是我新增加的参数"}) ;
//                return Collections.unmodifiableMap(newMap);
//            }
//            @Override
//            public String[] getParameterValues(String name) {
//                if("newParam".equals(name)){
//                    return new String[]{"这是我新增加的参数"};
//                }
//                return super.getParameterValues(name);
//            }*/
//            /*@Override
//            public String getHeader(String name) {
//                // 先从原本的Request中获取头，如果为空且名字为token，则从参数中查找并返回
//                String superHeader = super.getHeader(name);
//                if("token".equals(name) && StringUtils.isEmpty(superHeader)){
//                    String token = request.getParameter("token");
//                    if (StringUtils.isNotEmpty(token)) {
//                        return token ;
//                    }
//                }
//                return superHeader;
//            }*/
//           /* @SneakyThrows
//            @Override
//            public String getQueryString() {
//                return URLDecoder.decode(super.getQueryString(),"UTF-8");
//            }*/
//        };
//        HttpServletResponse response = (HttpServletResponse) servletResponse;
//        request.setCharacterEncoding("UTF-8");
//        response.setCharacterEncoding("UTF-8");
//        filterChain.doFilter(httpServletRequestWrapper , response);
    }
    @Override
    public void destroy() {
    }
}