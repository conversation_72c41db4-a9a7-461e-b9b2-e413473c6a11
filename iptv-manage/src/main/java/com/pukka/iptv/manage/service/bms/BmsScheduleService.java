package com.pukka.iptv.manage.service.bms;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.pukka.iptv.common.base.enums.StatusEnum;
import com.pukka.iptv.common.base.vo.CommonResponse;
import com.pukka.iptv.common.data.model.bms.BmsSchedule;
import com.pukka.iptv.common.data.model.order.SubOrderObjectsEntity;
import com.pukka.iptv.common.data.util.Tuple2;
import com.pukka.iptv.common.data.vo.CodeList;
import com.pukka.iptv.common.data.vo.bms.BmsSchedulePageVO;
import com.pukka.iptv.common.data.vo.req.BmsSchedulePageReq;
import org.springframework.transaction.annotation.Transactional;

import java.text.ParseException;
import java.util.List;

/**
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2021/9/9
 */
public interface BmsScheduleService extends IService<BmsSchedule> ,BmsPublishParamApi {

    boolean updatePublishStatus(List<SubOrderObjectsEntity> orderObjectsEntities, List<Long> spIdList);

    /**
     * 节目单发布
     *
     * @param bmsScheduleIds
     * @param doSchedule     是否定时发布 1定时 2 立即
     * @param scheduleTime   定时发布时间
     * @return
     */
    CommonResponse<Boolean> schedulePublish(List<Long> bmsScheduleIds, Integer doSchedule, String scheduleTime) throws ParseException;

    /**
     * 根据节目单ids回收节目单
     *
     * @param bmsScheduleIds
     * @return
     */
    CommonResponse<Boolean> scheduleRollback(List<Long> bmsScheduleIds);

    /**
     * 重置发布状态
     *
     * @param idList
     * @return
     */
    Boolean resetPublishStatus(List<Long> idList);

    /**
     * 修改发布状态
     *
     * @param idList
     * @return
     */
    Boolean updatePublishStatus(List<Long> idList, Integer updatePublishStatus);


    /**
     * 设置频道生效/失效
     *
     * @param ids
     * @param status 0：失效 1：有效
     * @return
     */
    Boolean setStatus(List<Long> ids, StatusEnum status);

    /**
     * 分页查询
     *
     * @param bmsSchedulePageReq
     * @return
     */
    IPage<BmsSchedulePageVO> getChannelsPage(BmsSchedulePageReq bmsSchedulePageReq);



    List<BmsSchedule> getTimingPublish();

    boolean timedPublish();

    /**
     * 取消定时发布
     * @param id
     * @return
     */
    Tuple2<Boolean,String> deleteTiming(Long id);

    /**
     * 检查是否含有定时发布的节目单
     * @param ids
     * @return
     */
    List<BmsSchedule> checkTimed(List<Long> ids);

    boolean deleteByCodeAndSp(List<String> delScheduleCodes, List<Long> spIdList, boolean isRollback);

    /**
     * 根据节目单ids回收节目单
     *
     * @param bmsScheduleIds
     * @return
     */
    CommonResponse<Boolean> scheduleManageRollback(List<Long> bmsScheduleIds);

    List<BmsSchedule> selectByChannelCodeAndStartDate(String channelCode, String startDate);

    /**
     * 根据节目单编码获取节目单
     * @param codeList
     * @return
     */
    List<BmsSchedule> getByCodeList(CodeList codeList);

    /**
     * 根据节目单ids回收节目单
     * @param bmsScheduleIds
     * @return
     */
    CommonResponse<Boolean> unCheckScheduleManageRollback(List<Long> bmsScheduleIds);
}
