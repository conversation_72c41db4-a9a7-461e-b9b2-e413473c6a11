package com.pukka.iptv.manage.service.sys;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.pukka.iptv.common.data.model.sys.SysInPassage;

import java.util.List;

/**
 *
 * @author: chenyudong
 * @date: 2021-9-13 16:58:09
 */

public interface SysInPassageService extends IService<SysInPassage> {

    Page<SysInPassage> page(Page<SysInPassage> page, SysInPassage sysInPassage);

    /**
     * 通过ids删除
     *
     * @param ids 通道id集合
     * @return bool
     */
    boolean removeDBAndCache(List<Long> ids);

    boolean removeDBAndCache(Long id);

    boolean updateCacheAndDB(SysInPassage sysInPassage);

    boolean saveToCacheAndDB(SysInPassage sysInPassage);
}


