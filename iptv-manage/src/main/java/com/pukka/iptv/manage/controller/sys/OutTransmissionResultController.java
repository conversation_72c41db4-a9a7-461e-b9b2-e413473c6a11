package com.pukka.iptv.manage.controller.sys;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.pukka.iptv.common.base.vo.CommonResponse;
import com.pukka.iptv.common.data.model.OutResult;
import com.pukka.iptv.common.data.model.OutTransmissionResult;
import com.pukka.iptv.common.data.vo.IdList;
import com.pukka.iptv.manage.service.sys.OutTransmissionResultService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.AllArgsConstructor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;

/**
 * <AUTHOR>
 * @Date 2021/12/24 10:41 上午
 * @description:
 * @Version 1.0
 */
@RestController
@AllArgsConstructor
@RequestMapping(value = "/outTransmissionResult", produces = MediaType.APPLICATION_JSON_VALUE)
@Api(tags="outTransmissionResult管理")
public class OutTransmissionResultController {
    @Autowired
    private OutTransmissionResultService outTransmissionResultService;

    @ApiOperation(value = "分页",hidden = true)
    @GetMapping("/page" )
    public CommonResponse<Page> page(@Valid Page page, OutTransmissionResult outTransmissionResult) {
        return  CommonResponse.success(outTransmissionResultService.page(page, Wrappers.query(outTransmissionResult)));
    }

    @ApiOperation(value = "详情",hidden = true)
    @GetMapping("/getById")
    public CommonResponse<OutTransmissionResult> getById(@Valid @RequestParam(name = "id", required = true)  Long id) {
        return CommonResponse.success(outTransmissionResultService.getById(id));
    }

    @ApiOperation(value = "新增",hidden = true)
    @PostMapping("/save")
    public CommonResponse<Boolean> save(@Valid @RequestBody OutTransmissionResult outTransmissionResult) {
        return  CommonResponse.success(outTransmissionResultService.saveOrUpdate(outTransmissionResult));
    }

    @ApiOperation(value = "修改",hidden = true)
    @PutMapping("/update")
    public CommonResponse<Boolean> updateById(@Valid @RequestBody OutTransmissionResult outTransmissionResult) {
        return CommonResponse.success(outTransmissionResultService.updateById(outTransmissionResult));
    }

    @ApiOperation(value = "通过CorrelateId更新",hidden = true)
    @PutMapping("/updateByCorrelateId")
    public CommonResponse<Boolean> updateByCorrelateId(@Valid @RequestBody OutTransmissionResult outTransmissionResult){
        return CommonResponse.success(outTransmissionResultService.updateByCorrelateId(outTransmissionResult));
    }

    @ApiOperation(value = "删除",hidden = true)
    @DeleteMapping("/deleteById" )
    public CommonResponse<Boolean> deleteById(@Valid @RequestParam(name = "id", required = true) Long id) {
        return  CommonResponse.success(outTransmissionResultService.removeById(id));
    }

    @ApiOperation(value = "批量删除",hidden = true)
    @DeleteMapping("/deleteByIds" )
    public CommonResponse<Boolean> deleteByIds(@Valid @RequestBody IdList idList) {
        return  CommonResponse.success(outTransmissionResultService.removeByIds(idList.getIds()));
    }
}
