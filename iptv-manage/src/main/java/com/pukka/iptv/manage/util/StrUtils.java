package com.pukka.iptv.manage.util;

import org.apache.commons.lang3.StringUtils;

import java.util.*;

/**
 * <AUTHOR>
 * @Date 2021/9/10 10:08
 * @Description
 */
public class StrUtils {


    public static String mergeSet(String set, Collection<String> str) {
        Set<String> result;
        if (StringUtils.isBlank(set)) {
            result = new HashSet<>();
        } else {
            String[] split = set.split(",");
            result = new HashSet(Arrays.asList(split));
        }
        result.addAll(str);
        return StringUtils.join(result, ",");
    }

    public static boolean longIsNotEmpty(Long l){
        if (l == null || l == 0L) {
            return false;
        }
        return true;
    }

    public static String removeSet(String str, Set<String> set) {
        if (StringUtils.isBlank(str)) {
            return "";
        }
        String[] split = str.split(",");
        Set<String> result = new HashSet(Arrays.asList(split));
        for (String s : set) {
            result.remove(s);
        }
        return StringUtils.join(result, ",");
    }

    public static Boolean isParentCodeBlank(String parentCode) {
        if ("0".equals(parentCode) || StringUtils.isBlank(parentCode)) {
            return true;
        } else {
            return false;
        }
    }
}
