package com.pukka.iptv.manage.mapper.cms;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.pukka.iptv.common.data.annotation.DataPermission;
import com.pukka.iptv.common.data.dto.CmsProgramDO;
import com.pukka.iptv.common.data.dto.CmsProgramEndDto;
import com.pukka.iptv.common.data.dto.ContentResourceDto;
import com.pukka.iptv.common.data.model.cms.CmsProgram;
import com.pukka.iptv.common.data.model.statistics.StatisticsIn;
import com.pukka.iptv.common.data.model.statistics.StatisticsInAgain;
import com.pukka.iptv.common.data.model.statistics.StatisticsInCheck;
import com.pukka.iptv.common.data.model.statistics.StatisticsInFinal;
import com.pukka.iptv.common.data.vo.cms.*;
import com.pukka.iptv.common.data.vo.req.StatisticsInVo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * @author: luo
 * @date: 2021-8-27 9:19:00
 */

@Mapper
@DataPermission(config = "cp_id=cpIds")
public interface CmsProgramMapper extends BaseMapper<CmsProgram> {

    List<ContentResourceDto> getReleaseList(@Param("relateCount") Integer relateCount,
                                            @Param("releaseStatus") Integer releaseStatus,
                                            @Param("contentStatus") Integer contentStatus);

    List<ContentResourceDto> getPreviewList(@Param("relateCount") Integer relateCount,
                                            @Param("previewStatus") Integer previewStatus,
                                            @Param("contentStatus") Integer contentStatus);

    String selectName(@Param("cpId") Long cpId,
                      @Param("name") String name,
                      @Param("value") Integer value,
                      @Param("id") Long id);

    String selectNewName(@Param("cpId") Long cpId,
                      @Param("name") String name,
                      @Param("value") Integer value);
    @DataPermission(config = "cp_id=cpIds")
    IPage<CmsProgram> getUnauthorizedContentByCmsProgram(Page page, @Param("param") CmsProgramDO cmsProgramDO);

    List<StatisticsIn> getProgramStatistics(@Param("param") StatisticsInVo statisticsInVo);

    List<StatisticsIn> getSubStatistics(@Param("param") StatisticsInVo statisticsInVo);

    List<StatisticsIn> getSubAndProgramStatistics(@Param("param") StatisticsInVo statisticsInVo);

    List<StatisticsIn> getSubAndProgramStatisticsByResource(@Param("param") StatisticsInVo statisticsInVo);

    List<StatisticsInCheck> getSelfProgramStatistics(@Param("param") StatisticsInVo statisticsInVo);

    List<StatisticsInCheck> getSelfProgramStatisticsAndSelf(@Param("param") StatisticsInVo statisticsInVo);

    List<StatisticsInCheck> getSelfSubStatistics(@Param("param") StatisticsInVo statisticsInVo);

    List<StatisticsInCheck> getSelfSubStatisticsAndSelf(@Param("param") StatisticsInVo statisticsInVo);

    List<StatisticsInFinal> getFinalProgramStatistics(@Param("param") StatisticsInVo statisticsInVo);

    List<StatisticsInFinal> getFinalSubStatistics(@Param("param") StatisticsInVo statisticsInVo);

    List<StatisticsInAgain> getAgainProgramStatistics(@Param("param") StatisticsInVo statisticsInVo);

    List<StatisticsInAgain> getAgainSubStatistics(@Param("param") StatisticsInVo statisticsInVo);
}
