package com.pukka.iptv.manage.mapper.cms;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.pukka.iptv.common.data.model.cms.CmsProhibitSchedule;
import com.pukka.iptv.common.data.model.cms.CmsProhibitScheduleDTO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR>
 * @description 针对表【cms_prohibit_schedule(违禁节目单)】的数据库操作Mapper
 * @createDate 2023-11-17 14:32:11
 * @Entity generator.domain.CmsProhibitSchedule
 */
@Mapper
public interface CmsProhibitScheduleMapper extends BaseMapper<CmsProhibitSchedule> {

    /**
     * 查询List
     *
     * @return
     */
    List<CmsProhibitSchedule> selectList(Page page, @Param("vo") CmsProhibitScheduleDTO cmsProhibitScheduleDTO);

    /**
     * 通过id删除
     *
     * @param idList
     */
    void delete(@Param("idList") List<Long> idList);

    /**
     * 根据channelCode、startDate、startTime删除
     *
     * @param channelCode
     * @param cmsProhibitSchedules
     *
     */
    void deleteByChannelCodeAndStartTime(@Param("channelCode")String channelCode,@Param("list")List<CmsProhibitSchedule> cmsProhibitSchedules);

    void batchInsert(@Param("list")List<CmsProhibitSchedule> cmsProhibitSchedules);
}
