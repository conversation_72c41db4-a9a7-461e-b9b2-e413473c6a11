package com.pukka.iptv.manage.service.copyright.copyright;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.pukka.iptv.common.data.model.copyright.CopyrightData;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * @Author: chiron
 * @Date: 2022/07/21/09:03
 * @Description: 版权材料库
 */

public interface CopyrightDataService extends IService<CopyrightData> {

    IPage<CopyrightData> selectPage(Page page, CopyrightData copyrightData);
    /**
     * 新增版权文件
     * @param copyrightData
     * @return
     */
    Boolean createCopyrightData(CopyrightData copyrightData, MultipartFile file);
    /**
     * 查看版权文件
     * @param id
     * @return
     */
    List<String> getLicensingFile(Long id);

    /**
     * 下载版权文件介质
     * @param id
     * @param response
     * @return
     */
    Boolean retrieveFile(Long id, HttpServletResponse response);

    /**
     * 删除版权材料库
     * @param idList
     * @return
     */
    Boolean remove(List<Long> idList);
}
