package com.pukka.iptv.manage.service.sys;

import com.baomidou.mybatisplus.extension.service.IService;
import com.pukka.iptv.common.data.model.OutTransmissionResult;

/**
 * <AUTHOR>
 * @Date 2021/12/24 10:42 上午
 * @description:
 * @Version 1.0
 */
public interface OutTransmissionResultService extends IService<OutTransmissionResult> {

    /**
     * 更新数据
     * @param outTransmissionResult
     * @return
     * @throws Exception
     */
    boolean updateByCorrelateId(OutTransmissionResult outTransmissionResult);
}
