package com.pukka.iptv.manage.controller.sys;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.pukka.iptv.common.base.vo.CommonResponse;
import com.pukka.iptv.common.data.model.sys.SysCpContentProvider;
import com.pukka.iptv.common.data.vo.IdList;
import com.pukka.iptv.common.log.annotation.SysLog;
import com.pukka.iptv.common.log.enums.OperateObjectEnum;
import com.pukka.iptv.common.log.enums.OperateTypeEnum;
import com.pukka.iptv.manage.service.sys.SysCpContentProviderService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.AllArgsConstructor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.List;

/**
 *
 * @author: chenyudong
 * @date: 2021-12-2 15:04:04
 */

@RestController
@AllArgsConstructor
@RequestMapping(value = "/sysCpContentProvider", produces = MediaType.APPLICATION_JSON_VALUE)
@Api(tags="sysCpContentProvider管理")
public class SysCpContentProviderController {

    @Autowired
    private SysCpContentProviderService sysCpContentProviderService;

    @ApiOperation(value = "分页",hidden = true)
    @GetMapping("/page" )
    public CommonResponse<Page> page(@Valid Page page, SysCpContentProvider sysCpContentProvider) {
        return  CommonResponse.success(sysCpContentProviderService.page(page, Wrappers.query(sysCpContentProvider)));
    }

    @ApiOperation(value = "详情",hidden = true)
    @GetMapping("/getById")
    public CommonResponse<SysCpContentProvider> getById(@Valid @RequestParam(name = "id", required = true)  Long id) {
        return CommonResponse.success(sysCpContentProviderService.getById(id));
    }

    @ApiOperation(value = "通过CPID获取内容提供商列表")
    @GetMapping("/getByCpId")
    public CommonResponse<List<SysCpContentProvider>> getByCpId(@Valid @RequestParam(name = "id", required = true)  Long id) {
        return CommonResponse.success(sysCpContentProviderService.getByCpId(id));
    }

    @SysLog(objectType = OperateObjectEnum.SYS_CP_CONTENT_PROVIDER, operateType = OperateTypeEnum.SAVE)
    @ApiOperation(value = "新增",hidden = true)
    @PostMapping("/save")
    public CommonResponse<Boolean> save(@Valid @RequestBody SysCpContentProvider sysCpContentProvider) {
        return  CommonResponse.success(sysCpContentProviderService.save(sysCpContentProvider));
    }

    @SysLog(objectType = OperateObjectEnum.SYS_CP_CONTENT_PROVIDER, operateType = OperateTypeEnum.UPDATE)
    @ApiOperation(value = "修改",hidden = true)
    @PutMapping("/update")
    public CommonResponse<Boolean> updateById(@Valid @RequestBody SysCpContentProvider sysCpContentProvider) {
        return CommonResponse.success(sysCpContentProviderService.updateById(sysCpContentProvider));
    }

    @SysLog(objectType = OperateObjectEnum.SYS_CP_CONTENT_PROVIDER, operateType = OperateTypeEnum.DELETE)
    @ApiOperation(value = "删除")
    @DeleteMapping("/deleteById" )
    public CommonResponse<Boolean> deleteById(@Valid @RequestParam(name = "id", required = true) Long id) {
        return  CommonResponse.success(sysCpContentProviderService.removeById(id));
    }

    @SysLog(objectType = OperateObjectEnum.SYS_CP_CONTENT_PROVIDER, operateType = OperateTypeEnum.DELETE)
    @ApiOperation(value = "批量删除",hidden = true)
    @DeleteMapping("/deleteByIds" )
    public CommonResponse<Boolean> deleteByIds(@Valid @RequestBody IdList idList) {
        return  CommonResponse.success(sysCpContentProviderService.removeByIds(idList.getIds()));
    }

}
