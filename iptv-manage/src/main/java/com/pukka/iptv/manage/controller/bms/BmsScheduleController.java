package com.pukka.iptv.manage.controller.bms;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.pukka.iptv.common.base.constant.PublishParamTypeConstants;
import com.pukka.iptv.common.base.enums.ContentTypeEnum;
import com.pukka.iptv.common.base.enums.StatusEnum;
import com.pukka.iptv.common.base.vo.CommonResponse;
import com.pukka.iptv.common.data.model.OutParamExpand;
import com.pukka.iptv.common.data.model.bms.BmsSchedule;
import com.pukka.iptv.common.data.util.Tuple2;
import com.pukka.iptv.common.data.vo.CodeList;
import com.pukka.iptv.common.data.vo.IdList;
import com.pukka.iptv.common.data.vo.IdVO;
import com.pukka.iptv.common.data.vo.bms.BmsSchedulePageVO;
import com.pukka.iptv.common.data.vo.bms.PublishStatusVo;
import com.pukka.iptv.common.data.vo.req.BmsSchedulePageReq;
import com.pukka.iptv.common.data.vo.req.BmsSchedulePublishReq;
import com.pukka.iptv.common.data.vo.resp.R;
import com.pukka.iptv.common.log.annotation.SysLog;
import com.pukka.iptv.common.log.enums.OperateObjectEnum;
import com.pukka.iptv.common.log.enums.OperateTypeEnum;
import com.pukka.iptv.manage.service.bms.BmsPublishParamApi;
import com.pukka.iptv.manage.service.bms.BmsScheduleService;
import com.pukka.iptv.manage.util.scheduletypeutils.ScheduleTypeFactory;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.AllArgsConstructor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.text.ParseException;
import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

/**
 * @author: luo
 * @date: 2021-9-15 11:43:53
 */

@RestController
@AllArgsConstructor
@RequestMapping(value = "/bmsSchedule", produces = MediaType.APPLICATION_JSON_VALUE)
@Api(tags = "bms节目单管理")
public class BmsScheduleController implements BmsPublishParamApi {

    @Autowired
    private BmsScheduleService bmsScheduleService;

    @ApiOperation(value = "分页")
    @GetMapping("/page")
    public CommonResponse<Page> page(@Valid Page page, BmsSchedule bmsSchedule) {
        return CommonResponse.success(bmsScheduleService.page(page, Wrappers.query(bmsSchedule)));
    }

    @ApiOperation(value = "详情")
    @GetMapping("/getById")
    public CommonResponse<BmsSchedule> getById(@Valid @RequestParam(name = "id", required = true) Long id) {
        return CommonResponse.success(bmsScheduleService.getById(id));
    }


    @ApiOperation(value = "定时发布")
    @GetMapping("/getTiming")
    public CommonResponse<Boolean> getTiming() {
        try {
            Boolean apply = ScheduleTypeFactory.initMode(ContentTypeEnum.SCHEDULE).apply();
            return CommonResponse.success(apply);
        } catch (Exception e) {
            return CommonResponse.fail("节目单定时发布失败");
        }
    }

    @ApiOperation(value = "新增")
    @PostMapping("/save")
    public CommonResponse<Boolean> save(@Valid @RequestBody BmsSchedule bmsSchedule) {
        return CommonResponse.success(bmsScheduleService.save(bmsSchedule));
    }

    @ApiOperation(value = "修改")
    @PutMapping("/update")
    public CommonResponse<Boolean> updateById(@Valid @RequestBody List<BmsSchedule> bmsSchedules) {
        return CommonResponse.success(bmsScheduleService.updateBatchById(bmsSchedules));
    }

    @ApiOperation(value = "删除")
    @DeleteMapping("/deleteById")
    public CommonResponse<Boolean> deleteById(@Valid @RequestParam(name = "id", required = true) Long id) {
        return CommonResponse.success(bmsScheduleService.removeById(id));
    }

    @ApiOperation(value = "批量删除")
    @DeleteMapping("/deleteByIds")
    public CommonResponse<Boolean> deleteByIds(@Valid @RequestBody IdList idList) {
        return CommonResponse.success(bmsScheduleService.removeByIds(idList.getIds()));
    }

    @SysLog(objectType = OperateObjectEnum.BMS_SCHEDULE, operateType = OperateTypeEnum.PUBLISH, objectIds = "#bmsSchedule.ids")
    @ApiOperation(value = "节目单发布")
    @PutMapping("/schedule_publish")
    public CommonResponse<Boolean> schedulePublish(@Valid @RequestBody BmsSchedulePublishReq bmsSchedule) throws ParseException {
        String[] ids = bmsSchedule.getIds().split(",");
        List<Long> publishIds = Arrays.stream(ids)
                .map(s -> Long.parseLong(s.trim())).collect(Collectors.toList());
        setParam(PublishParamTypeConstants.PRIORITY, new OutParamExpand().setPriority(bmsSchedule.getPriority()));
        CommonResponse<Boolean> response = bmsScheduleService.schedulePublish(publishIds, bmsSchedule.getDoSchedule(), bmsSchedule.getScheduleTime());
        clearParm();
        return response;
    }

    @SysLog(objectType = OperateObjectEnum.BMS_SCHEDULE, operateType = OperateTypeEnum.ROLLBACK, objectIds = "#bmsSchedule.ids")
    @ApiOperation(value = "节目单回收")
    @PutMapping("/rollback")
    public CommonResponse<Boolean> scheduleRollback(@Valid @RequestBody BmsSchedulePublishReq bmsSchedule) throws ParseException {

        String[] ids = bmsSchedule.getIds().split(",");
        List<Long> rollbackIds = Arrays.stream(ids)
                .map(s -> Long.parseLong(s.trim())).collect(Collectors.toList());
        setParam(PublishParamTypeConstants.PRIORITY, new OutParamExpand().setPriority(bmsSchedule.getPriority()));
        CommonResponse<Boolean> response = bmsScheduleService.scheduleRollback(rollbackIds);
        clearParm();
        return response;
    }

    @SysLog(objectType = OperateObjectEnum.BMS_SCHEDULE, operateType = OperateTypeEnum.RESET_PUBLISH_STATUS, objectIds = "#ids")
    @ApiOperation(value = "节目单重置发布状态")
    @PutMapping("/publishStatusReset")
    public CommonResponse<Boolean> publishStatusReset(@RequestBody List<Long> ids) throws ParseException {
        Boolean status = bmsScheduleService.resetPublishStatus(ids);
        if (status) {
            return CommonResponse.success(true);
        }
        return CommonResponse.commonfail("重置状态失败");
    }

    @SysLog(objectType = OperateObjectEnum.BMS_SCHEDULE, operateType = OperateTypeEnum.UPDATE_PUBLISH_STATUS, objectIds = "#publishStatusVo.idList")
    @ApiOperation(value = "节目单修改发布状态")
    @PutMapping("/changePublishStatus")
    public CommonResponse<Boolean> updatePublishStatus(@RequestBody @Valid PublishStatusVo publishStatusVo) {
        List<Long> idList = publishStatusVo.getIdList();
        Integer publishStatus = publishStatusVo.getPublishStatus();

        Boolean status = bmsScheduleService.updatePublishStatus(idList, publishStatus);
        if (status) {
            return CommonResponse.success(true);
        }
        return CommonResponse.commonfail("修改发布状态失败");
    }

    @SysLog(objectType = OperateObjectEnum.BMS_SCHEDULE, operateType = OperateTypeEnum.ACTIVE_OR_POSITIVE, objectIds = "#publishStatusVo.idList")
    @ApiOperation(value = "节目单设置有效/失效")
    @PutMapping("/resetStatus")
    public CommonResponse<Boolean> resetStatus(@RequestBody @Valid PublishStatusVo publishStatusVo) throws ParseException {
        List<Long> idList = publishStatusVo.getIdList();
        Integer publishStatus = publishStatusVo.getPublishStatus();
        Boolean status = bmsScheduleService.setStatus(idList, StatusEnum.trans(publishStatus));
        return status ? CommonResponse.success(true) : CommonResponse.commonfail("设置失败");
    }

    @ApiOperation(value = "节目单查询")
    @PostMapping("/query")
    public CommonResponse<IPage<BmsSchedulePageVO>> page(@RequestBody @Valid BmsSchedulePageReq bmsSchedulePageReq) {
        IPage<BmsSchedulePageVO> channelsPage = bmsScheduleService.getChannelsPage(bmsSchedulePageReq);
        channelsPage.getRecords().remove(null);
        return R.page(channelsPage);
    }

    @SysLog(objectType = OperateObjectEnum.BMS_SCHEDULE, operateType = OperateTypeEnum.DELETE_TIMED, objectIds = "#id")
    @ApiOperation(value = "节目单_取消定时发布")
    @PutMapping("/cancelTimedPublish")
    public CommonResponse<String> categoryChannelSort(@RequestBody @Valid IdVO idVO) {
        Tuple2<Boolean, String> tuple2 = bmsScheduleService.deleteTiming(Long.parseLong(idVO.getId()));
        return tuple2.getA().get() ? CommonResponse.success(tuple2.getB().get()) : CommonResponse.commonfail(tuple2.getB().get());
    }

    @ApiOperation(value = "节目单拦截获取编排侧节目单信息")
    @PostMapping("/getByCodeList")
    public CommonResponse<List<BmsSchedule>> getByCodeList(@RequestBody CodeList codeList) {
        codeList.validate();
        return CommonResponse.success(bmsScheduleService.getByCodeList(codeList));
    }

}
