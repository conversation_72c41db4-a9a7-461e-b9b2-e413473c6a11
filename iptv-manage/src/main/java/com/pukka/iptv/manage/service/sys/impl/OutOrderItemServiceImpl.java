package com.pukka.iptv.manage.service.sys.impl;

import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.pukka.iptv.common.base.constant.ObjectsTypeConstants;
import com.pukka.iptv.common.base.enums.*;
import com.pukka.iptv.common.base.util.UUID;
import com.pukka.iptv.common.core.constant.SymbolConstant;
import com.pukka.iptv.common.core.util.StringUtils;
import com.pukka.iptv.common.data.dto.DateFormatCompletionDto;
import com.pukka.iptv.common.data.model.OutOrderBase;
import com.pukka.iptv.common.data.model.OutOrderItem;
import com.pukka.iptv.common.data.model.OutResult;
import com.pukka.iptv.common.data.model.sys.SysOutPassage;
import com.pukka.iptv.common.data.vo.sys.OutOrderItemRepublish;
import com.pukka.iptv.common.data.vo.sys.OutOrderItemVo;
import com.pukka.iptv.common.rabbitmq.constans.OutPublishConstant;
import com.pukka.iptv.manage.mapper.sys.OutOrderItemMapper;
import com.pukka.iptv.manage.mapper.sys.OutResultMapper;
import com.pukka.iptv.manage.mapper.sys.SysOutPassageMapper;
import com.pukka.iptv.manage.service.sys.OutOrderBaseService;
import com.pukka.iptv.manage.service.sys.OutOrderItemService;
import com.pukka.iptv.manage.util.CommonUtils;
import com.pukka.iptv.manage.util.TimeResolutionUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.springframework.amqp.rabbit.core.RabbitTemplate;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Objects;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * <AUTHOR>
 * @Date 2021/9/9 10:22 上午
 * @description: 子工单
 * @Version 1.0
 */
@Slf4j
@Service
@Transactional
public class OutOrderItemServiceImpl extends
    ServiceImpl<OutOrderItemMapper, OutOrderItem> implements OutOrderItemService {

  @Autowired
  private SysOutPassageMapper sysOutPassageMapper;
  @Autowired
  private OutOrderItemMapper outOrderItemMapper;
  @Autowired
  private OutResultMapper outResultMapper;
  @Autowired
  private OutOrderBaseService outOrderBaseService;
  @Autowired
  private OutOrderItemService outOrderItemService;
  @Autowired
  private RabbitTemplate rabbitTemplate;

  /**
   * 子工单重新发布
   *
   * @param outOrderItemRepublish
   * @return
   */
  @Override
  @Transactional(rollbackFor = Exception.class)
  public boolean orderRepublish(OutOrderItemRepublish outOrderItemRepublish) {
    try {
      //修改数据库状态
      SysOutPassage sysOutPassage = sysOutPassageMapper.selectById(
          outOrderItemRepublish.getOutPassageId());
      if (sysOutPassage == null || StringUtils.isEmpty(sysOutPassage.getCode())) {
        log.warn("传入分发通道id有误，未查询到相关数据");
        return false;
      }
      String code = "_" + sysOutPassage.getCode();
      String[] split = outOrderItemRepublish.getIds().split(",");
      List<String> stringList = Arrays.asList(split);
      //获取子工单所属工单任务id
      List<String> baseOrderId = outOrderItemMapper.getBaseOrderId(code, stringList);
      if (ObjectUtils.isEmpty(baseOrderId)) {
        log.warn("子工单重新发布 ->>>>> 子工单信息:{}.获取主工单信息失败！", stringList);
        return false;
      }
      //判断当前节点是否全部为忽略节点
      boolean equals = sysOutPassage.getDealReportResult()
          .equals(DealReportResultEnum.IGNORE.getCode());
      if (equals) {
        log.info("子工单重新发布 ->>>>> 子工单信息:{} 当前节点是忽略节点 不处理主工单状态!",
            stringList);
      } else {
        //更新主工单状态为发布中
        Boolean status = false;
        if (baseOrderId != null && baseOrderId.size() > 0) {
          status = outOrderBaseService.updateBaseOutOrderStatus(baseOrderId,
              ItemStatusEnum.InHandle.getValue(), null, "", "");
        }
        if (!status) {
          log.warn("子工单重新发布 ->>>>> 子工单信息:{}.重置主工单状态为发布中失败！", stringList);
          return false;
        }
      }
      for (String id : stringList) {
        OutOrderItemVo outOrderItemVo = outOrderItemMapper.getOrderItemById(code, id);
        if (ObjectUtils.isEmpty(outOrderItemVo)) {
          log.warn("子工单重新发布 ->>>>> 子工单id:{} 分发通道code:{}.查询子工单信息为空！", id,
              code);
          continue;
        }
        //业务ID前缀组成，采用雪花算法
        String correlateId = UUID.nextSnow().toString();
        outOrderItemVo.setCorrelateId(correlateId);
        boolean isRepublish = outOrderItemMapper.orderRepublish(code, Long.parseLong(id),
            correlateId);
        if (!isRepublish) {
          outOrderItemVo.setErrorDescription("重置子工单状态失败,重新发布失败");
          outOrderItemService.getReportEntity(outOrderItemVo);
          log.warn("子工单重新发布 ->>>>> 子工单信息:{}.重置子工单状态失败！", outOrderItemVo);
          continue;
        }
        try {
          if (ObjectUtils.isEmpty(outOrderItemVo) || ObjectUtils.isEmpty(outOrderItemVo.getCspId())
              || ObjectUtils.isEmpty(outOrderItemVo.getLspId()) || ObjectUtils.isEmpty(
              outOrderItemVo.getCorrelateId()) || ObjectUtils.isEmpty(
              outOrderItemVo.getCmdFileUrl())) {
            outOrderItemVo.setErrorDescription("获取子工单必须信息不全,重新发布失败");
            outOrderItemService.getReportEntity(outOrderItemVo);
            log.warn("子工单重新发布 ->>>>> 子工单ID:{} 获取子工单必须信息不全，重新发布失败！", id);
            continue;
          }
          outOrderItemVo.setPath(sysOutPassage.getPath());
          String routingKey = "";
          //******** 判断是否自动发布
          OutOrderBase orderBase = outOrderBaseService.getById(outOrderItemVo.getBaseOrderId());
          if (ObjectUtils.isNotEmpty(orderBase) && orderBase.getOrderType()
              .equals(OrderTypeEnums.AUTO.getValue())) {
            //自动下发直接
            outOrderItemVo.setCpId(orderBase.getCpId());
            //routingKey = OutPublishConstant.OUT_AUTO_SEND_ROUTING;
            routingKey = OutPublishConstant.OUT_ORDER_SEND_ROUTING + SymbolConstant.UNDER_SCORE
                + outOrderItemVo.getOutPassageCode() + ObjectsTypeConstants.OTHER_PRIORITY_QUEUE;
          } else {
            if (ObjectUtils.isNotEmpty(outOrderItemVo.getAction())) {
              if (outOrderItemVo.getAction().equals(ActionEnums.REGIST.getCode())) {
                routingKey = OutPublishConstant.OUT_ORDER_SEND_ROUTING + SymbolConstant.UNDER_SCORE
                    + outOrderItemVo.getOutPassageCode()
                    + ObjectsTypeConstants.REGIST_PRIORITY_QUEUE;
              } else {
                routingKey = OutPublishConstant.OUT_ORDER_SEND_ROUTING + SymbolConstant.UNDER_SCORE
                    + outOrderItemVo.getOutPassageCode()
                    + ObjectsTypeConstants.OTHER_PRIORITY_QUEUE;
              }
            } else {
              //子工单更新状态并发送反馈队列
              outOrderItemVo.setErrorDescription("操作类型[aciton]为空,重新发布失败");
              outOrderItemService.getReportEntity(outOrderItemVo);
              log.warn(
                  "子工单重新发布 ->>>>> 当前子工单信息，操作类型[aciton]为空，子工单发布至MQ任务队列失败!子工单:{}.",
                  JSON.toJSONString(outOrderItemVo));
              continue;
            }
            //添加cpid赋值
            OutOrderItemVo item = outOrderItemService.getBmsContentCodeAndName(
                String.valueOf(outOrderItemVo.getBmsContentId()),
                Integer.valueOf(outOrderItemVo.getContentType()));
            if (ObjectUtils.isNotEmpty(item)) {
              outOrderItemVo.setCpId(item.getCpId());
            }
          }
          if (StringUtils.isEmpty(routingKey)) {
            //子工单更新状态并发送反馈队列
            outOrderItemVo.setErrorDescription("组装队列routingKey为空,重新发布失败");
            outOrderItemService.getReportEntity(outOrderItemVo);
            log.warn(
                "子工单重新发布 ->>>>> 当前子工单信息,组装队列routingKey为空,子工单发布至MQ任务队列失败!子工单:{}.",
                JSON.toJSONString(outOrderItemVo));
            continue;
          }
          rabbitTemplate.convertAndSend(OutPublishConstant.OUT_EXCHANGE, routingKey, outOrderItemVo,
              message -> {
                message.getMessageProperties().setPriority(PriorityEnums.PRIORITY.getValue());
                return message;
              });
        } catch (Exception exception) {
          log.warn("子工单重新发布 ->>>>> 子工单信息:{}.子工单发布至MQ任务队列失败！错误信息:{}", id,
              exception);
        }
      }

    } catch (Exception exception) {
      log.error("子工单重新发布失败，错误信息:{}", exception);
      return false;
    }
    log.info("子工单重新发布成功");
    return true;
  }

  /**
   * 更新重新发布失败状态
   *
   * @param outOrderItemVo
   * @return
   */
  @Override
  public Boolean getReportEntity(OutOrderItemVo outOrderItemVo) {
    outOrderItemVo.setStatus(ItemStatusEnum.Fail.getValue());
    outOrderItemVo.setResult(ItemResultEnum.Fail.getValue());
    outOrderItemVo.setStatusDescription(ItemStatusEnum.Fail.toString());
    outOrderItemVo.setErrorDescription(outOrderItemVo.getErrorDescription());
    //子工单异常需要更新工单状态
    Boolean aBoolean = updateResult(outOrderItemVo);
    if (aBoolean) {
      try {
        rabbitTemplate.convertAndSend(OutPublishConstant.OUT_EXCHANGE,
            OutPublishConstant.OUT_BASE_FEEDBACK_ROUTING, outOrderItemVo);
      } catch (Exception exception) {
        aBoolean = false;
        log.error(
            "工单重新发布 ->>>>> 子工单:{}.更新重新发布失败状态失败,推送反馈队列失败,错误信息:{}",
            JSON.toJSONString(outOrderItemVo), exception);
      }
    } else {
      log.error("工单重新发布 ->>>>> 子工单:{}.更新重新发布失败状态失败,更新数据库子工单状态失败!",
          JSON.toJSONString(outOrderItemVo));
    }
    return aBoolean;
  }

  /**
   * 子工单信息分页查询
   *
   * @param page
   * @param outOrderItemVo
   * @return
   */
  @Override
  public IPage<OutOrderItemVo> listByOutOrderItemProperty(IPage<OutOrderItem> page,
      OutOrderItemVo outOrderItemVo, String startTime, String endTime) {
    try {
      Long outPassageid = outOrderItemVo.getOutPassageId();
      //获取子工单频道信息
      SysOutPassage sysOutPassage = sysOutPassageMapper.selectById(outPassageid);
      if (sysOutPassage == null || StringUtils.isEmpty(sysOutPassage.getCode())) {
        log.warn("传入分发通道id有误，未查询到相关数据");
        return null;
      }
      String code = SymbolConstant.UNDER_SCORE + sysOutPassage.getCode();
      //size等于-1时不分页
      page.setSize(page.getSize() == -1 ? Integer.MAX_VALUE : page.getSize());
      page.setCurrent(page.getCurrent() == -1 ? 1 : page.getCurrent());
      Page<OutOrderItemVo> outOrderItemPage = new Page<>();
      if (!StringUtils.isEmpty(outOrderItemVo.getShowName())) {
        if (outOrderItemVo.getShowName().contains("\n")) {
          //防止name为空字符串以及只有回车的情况  进行再一次校验
          outOrderItemVo.setShowName(outOrderItemVo.getShowName().trim());
          if (StringUtils.isNotEmpty(outOrderItemVo.getShowName())) {
            String[] names = CommonUtils.getNames(outOrderItemVo.getShowName());
            if (names.length > 0) {
              outOrderItemVo.setNameList(names);
              outOrderItemVo.setLikeOrinFlag(2);
            }
          }
        } else {
          String[] names = CommonUtils.getNames(outOrderItemVo.getShowName());
          if (names != null && names.length > 0) {
            outOrderItemVo.setNameList(names);
            outOrderItemVo.setNameLike(names[0]);
            outOrderItemVo.setLikeOrinFlag(1);
          }
        }
      }
      //开始、结束时间格式化
      DateFormatCompletionDto dateFormatCompletionDto = new DateFormatCompletionDto().setStartTime(
          startTime).setEndTime(endTime);
      Boolean aBoolean = TimeResolutionUtil.dateTimeCompare(dateFormatCompletionDto);
      List<OutOrderItemVo> outOrderItemVoList = outOrderItemMapper.findListByPage(code,
          outOrderItemVo, dateFormatCompletionDto.getStartTime(),
          dateFormatCompletionDto.getEndTime(),
          (page.getCurrent() - 1) * page.getSize(), page.getSize());
      if (ObjectUtils.isNotEmpty(outOrderItemVoList)) {
        outOrderItemVoList.stream().filter(item -> ObjectUtils.isNotEmpty(item.getResult()))
            .forEach(item -> {
              OutResult byCorrelateId = outResultMapper.findByCorrelateId(item.getCorrelateId());
              if (ObjectUtils.isNotEmpty(byCorrelateId) && StringUtils.isNotEmpty(
                  byCorrelateId.getResultFileUrl())) {
                item.setResultFileUrl(byCorrelateId.getResultFileUrl());
              }
            });
        outOrderItemPage.setRecords(outOrderItemVoList);
        outOrderItemPage.setSize(page.getSize());
        outOrderItemPage.setCurrent(page.getCurrent());
        outOrderItemPage.setTotal(outOrderItemMapper.findList(code, outOrderItemVo,
            dateFormatCompletionDto.getStartTime(), dateFormatCompletionDto.getEndTime()));
        return outOrderItemPage;
      }
    } catch (Exception exception) {
      log.error("子工单信息分页查询失败：", exception);
    }
    return null;
  }

  /**
   * 通过id，type查询contentId,contentCode
   *
   * @param contentIds
   * @param contentType
   * @return
   */
  @Override
  public List<OutOrderItem> getByIdType(String contentIds, Integer contentType) {
    String tableName = ContentTypeItemEnum.getEnum(contentType).getTableName();
    List<OutOrderItem> outOrderItems = new ArrayList<>();
    for (String contentId : contentIds.split(",")) {
      if (contentType <= ContentTypeItemEnum.PICTURE_CHANNEL.getContentType()
          && contentType >= ContentTypeItemEnum.PROGRAM.getContentType()) {
        OutOrderItem outOrderItem = outOrderItemMapper.selectByIdType(tableName,
            Long.parseLong(contentId));
        outOrderItems.add(outOrderItem);
      } else if (ContentTypeItemEnum.CATEGORY_PROGRAM.getContentType().equals(contentType)
          || ContentTypeItemEnum.CATEGORY_CHANNEL.getContentType().equals(contentType)
          || ContentTypeItemEnum.CATEGORY_SERIES.getContentType().equals(contentType)) {
        OutOrderItem outOrderItem = outOrderItemMapper.selectCategory(tableName,
            Long.parseLong(contentId));
        outOrderItems.add(outOrderItem);
      } else if (ContentTypeItemEnum.PACKAGE_PROGRAM.getContentType().equals(contentType)
          || ContentTypeItemEnum.PACKAGE_SERIES.getContentType().equals(contentType)) {
        OutOrderItem outOrderItem = outOrderItemMapper.selectPackage(tableName,
            Long.parseLong(contentId));
        outOrderItems.add(outOrderItem);
      } else {
        return null;
      }
    }
    return outOrderItems;
  }

  /**
   * 获取id，code,name
   */
  @Override
  public OutOrderItemVo getBmsContentCodeAndName(String contentId, Integer contentType) {
    String tableName = ContentTypeItemEnum.getEnum(contentType).getTableName();
    if (ContentTypeItemEnum.CATEGORY_PROGRAM.getContentType().equals(contentType)
        || ContentTypeItemEnum.CATEGORY_SERIES.getContentType().equals(contentType)
        || ContentTypeItemEnum.CATEGORY_CHANNEL.getContentType().equals(contentType)) {
      return outOrderItemMapper.selectCategoryCodeAndNameByIdType(tableName,
          Long.parseLong(contentId));
    }
    if (ContentTypeItemEnum.PACKAGE_PROGRAM.getContentType().equals(contentType)
        || ContentTypeItemEnum.PACKAGE_SERIES.getContentType().equals(contentType)) {
      return outOrderItemMapper.selectPackageCodeAndNameByIdType(tableName,
          Long.parseLong(contentId));
    }
    if (ContentTypeItemEnum.PHYSICAL_CHANNEL.getContentType().equals(contentType)) {
      return outOrderItemMapper.selectPhysicalChannelCodeAndNameByIdType(tableName,
          Long.parseLong(contentId));
    }
    if (ContentTypeItemEnum.SCHEDULE.getContentType().equals(contentType)) {
      return outOrderItemMapper.selectScheduleAndNameByIdType(tableName, Long.parseLong(contentId));
    }
    if (ContentTypeItemEnum.PICTURE.getContentType().equals(contentType)) {
      OutOrderItemVo outOrderItemVo = outOrderItemMapper.selectPictureAndNameByIdType(tableName,
          Long.parseLong(contentId));
      if (ObjectUtils.isEmpty(outOrderItemVo) || ObjectUtils.isEmpty(
          outOrderItemVo.getContentType()) || ObjectUtils.isEmpty(
          outOrderItemVo.getBmsContentId())) {
        return null;
      }
      String tableName1 = ContentTypeItemEnum.getEnum(
          Integer.valueOf(outOrderItemVo.getContentType())).getTableName();
      String showName = outOrderItemMapper.selectByIdType(tableName1,
          outOrderItemVo.getBmsContentId()).getShowName();
      outOrderItemVo.setShowName(showName);
      outOrderItemVo.setBmsContentId(outOrderItemVo.getId());
      return outOrderItemVo;
    }
    return outOrderItemMapper.selectByIdType(tableName, Long.parseLong(contentId));
  }

  /**
   * 更新子工单
   *
   * @param outOrderItemVo
   * @return
   */
  @Override
  public Boolean updateResult(OutOrderItemVo outOrderItemVo) {
    if (StringUtils.isEmpty(outOrderItemVo.getOutPassageCode())) {
      log.error("发布类型:{} 内容ID:{} 数据库子工单更新，传入通道为空，更新结果失败.",
          outOrderItemVo.getContentType(), outOrderItemVo.getBmsContentId());
      return false;
    }
    //更新工单结果
    Boolean aBoolean = outOrderItemMapper.updateResult(outOrderItemVo);
    if (!aBoolean) {
      log.warn("发布类型:{} 内容ID:{} 子工单id:{} OutPassageCode:{} 数据库子工单更新失败.",
          outOrderItemVo.getContentType(), outOrderItemVo.getBmsContentId(), outOrderItemVo.getId(),
          outOrderItemVo.getOutPassageCode());
      return false;
    }
    //获取工单信息
    OutOrderBase orderBase = outOrderBaseService.getById(outOrderItemVo.getBaseOrderId());
    //更新主工单状态
    if (Objects.equals(orderBase.getStatus(), ItemStatusEnum.StayHandle.getValue())) {
      outOrderBaseService.update(
          Wrappers.lambdaUpdate(OutOrderBase.class)
              .set(OutOrderBase::getStatus, ItemStatusEnum.InHandle.getValue())
              .eq(OutOrderBase::getId, outOrderItemVo.getBaseOrderId()));
    }
    return true;
  }

  /**
   * 查询子工单结果
   *
   * @param outOrderItemVo
   * @return
   */
  @Override
  public Boolean selectBaseResult(OutOrderItemVo outOrderItemVo) {
    //获取工单信息
    OutOrderItem orderItem = outOrderItemMapper.getItem(outOrderItemVo);
    OutOrderBase orderBase = outOrderBaseService.getById(orderItem.getBaseOrderId());
    if (Objects.nonNull(orderBase.getResult()) && orderBase.getResult()
        .equals(ItemResultEnum.Fail.getValue())) {
      return false;
    }
    //工单结果不为失败或者工单结果为空返回为true
    return true;
  }

  /**
   * 将string字符串中的换行符进行替换为""
   */
  public String replaceBlank(String str) {
    String dest = "";
    if (str != null) {
      Pattern p = Pattern.compile("\t|\r|\n");
      Matcher m = p.matcher(str);
      dest = m.replaceAll("");
    }
    return dest;
  }

}
