package com.pukka.iptv.manage.controller.sys;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.pukka.iptv.common.base.vo.CommonResponse;
import com.pukka.iptv.common.data.model.OutOrderItem;
import com.pukka.iptv.common.data.vo.sys.OutOrderItemRepublish;
import com.pukka.iptv.common.data.vo.sys.OutOrderItemVo;
import com.pukka.iptv.manage.service.sys.OutOrderItemService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.AllArgsConstructor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.List;

/**
 * <AUTHOR>
 * @Date 2021/9/9 10:18 上午
 * @description: 子工单查询，重新发布接口
 * @Version 1.0
 */
@RestController
@AllArgsConstructor
@RequestMapping(value = "/outOrderItem", produces = MediaType.APPLICATION_JSON_VALUE)
@Api(tags = "OutOrderItem管理")
public class OutOrderItemController {
    @Autowired
    OutOrderItemService outOrderItemService;

    @ApiOperation(value = "分页")
    @GetMapping("/listByOutOrderItemProperty")
    public CommonResponse<IPage<OutOrderItemVo>> page(Page<OutOrderItem> page, OutOrderItemVo outOrderItemVo, String startTime, String endTime) {
        return CommonResponse.success(outOrderItemService.listByOutOrderItemProperty(page, outOrderItemVo, startTime, endTime));
    }

    @ApiOperation(value = "子工单重新发布")
    @PutMapping("/orderRepublish")
    public CommonResponse<Boolean> outOrderItemRepublish(@Valid @RequestBody OutOrderItemRepublish outOrderItemRepublish) {
        boolean republish = outOrderItemService.orderRepublish(outOrderItemRepublish);
        if (republish) {
            return CommonResponse.success(true);
        }
        return CommonResponse.fail(false);
    }

    @ApiOperation(value = "根据id获取子工单信息")
    @PutMapping("/getById")
    public CommonResponse<OutOrderItem> getById(@Valid @RequestParam(name = "id", required = true) String id) {
        return CommonResponse.success(outOrderItemService.getById(id));
    }

    @ApiOperation(value = "获取信息")
    @GetMapping("/getByIdType")
    public CommonResponse<List<OutOrderItem>> getByIdType(@Valid @RequestParam(name = "contentIds", required = true) String contentIds, @Valid @RequestParam(name = "contentType", required = true) Integer contentType) {
        return CommonResponse.success(outOrderItemService.getByIdType(contentIds, contentType));
    }

    @ApiOperation(value = "查询主工单结果")
    @PostMapping("/selectBaseResult")
    public CommonResponse<Boolean> selectBaseResult(@Valid @RequestBody OutOrderItemVo outOrderItemVo) {
        return CommonResponse.success(outOrderItemService.selectBaseResult(outOrderItemVo));
    }
}
