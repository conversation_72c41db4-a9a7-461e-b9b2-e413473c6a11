package com.pukka.iptv.manage.service.bms;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.pukka.iptv.common.base.enums.ActionEnums;
import com.pukka.iptv.common.base.enums.ContentTypeEnum;
import com.pukka.iptv.common.base.enums.StatusEnum;
import com.pukka.iptv.common.data.model.OutParamExpand;
import com.pukka.iptv.common.data.model.bms.BmsContent;
import com.pukka.iptv.common.data.model.order.SubOrderObjectsEntity;
import com.pukka.iptv.common.data.vo.CodeList;
import com.pukka.iptv.common.data.vo.bms.CpFeedbackContentVO;
import com.pukka.iptv.common.data.vo.req.BmsContentQueryReq;
import com.pukka.iptv.common.data.vo.req.BmsContentQueryReqBySP;
import com.pukka.iptv.manage.service.bms.dto.BmsContentRecycleDto;

import java.util.List;
import java.util.Map;

/**
 * 内容表，不包含子集
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2021-08-27 10:20:57
 */
public interface BmsContentService extends IService<BmsContent>, BmsRecycleApi<BmsContentRecycleDto, BmsContent>, PriorityPublishApi, BmsPublishParamApi {

    boolean updatePublishStatus(List<SubOrderObjectsEntity> orderObjectsEntities, List<Long> spIdList);

    //分页查询内容
    IPage<BmsContentQueryReq> pageList(BmsContentQueryReq req);

    IPage<BmsContentQueryReq> pageListForSp(BmsContentQueryReq req);

    IPage<BmsContentQueryReqBySP> getAuthorizedContentBySp(Page page, BmsContentQueryReq param);

    //内容修改锁定状态
    boolean modifyLockStatus(List<Long> ids, Integer lockStatus);

    //修改发布状态
    boolean modifyPublishStatus(List<Long> ids, Integer publishStatus);

    //重置发布状态
    boolean resetPublishStatus(List<Long> ids);

    //内容发布接口
    boolean publish(List<Long> ids, boolean doSchedule, String scheduleTime, Map<String, OutParamExpand> paramMap);

    /**
     * 单发剧头
     *
     * @param ids
     * @param doSchedule
     * @param scheduleTime
     * @return
     */
    boolean seriesPublish(List<Long> ids, boolean doSchedule, String scheduleTime);


    //取消定时发布
    boolean cancelTimedPublish(Long id);


    // 工单下发
    boolean sendContentOrder(ActionEnums actionEnums, ContentTypeEnum typeEnum, List<Long> contents, Long spId, String spName, Map<String, OutParamExpand> paramMap);

    //设置为生效或失效
    boolean status(List<Long> ids, StatusEnum statusEnum);

    // 定时发布
    boolean schedulePublish();

    List<CpFeedbackContentVO> getCpFeedbackContents();

    boolean updateCpFeedback(List<Long> idList, int result);

    /**
     * 根据子集的ids获取到剧头的ids
     *
     * @param programIds
     * @return
     */
    List<Long> getcontentIdsByProgramIds(List<Long> programIds);

    /**
     * 批量一键回收
     *
     * @param ids
     * @param type
     * @return
     */
    boolean batchRecycle(List<Long> ids, Integer type);

    boolean deleteByCodeAndSp(List<String> delSeriesEntities, List<Long> spIdList, ContentTypeEnum contentType, boolean isRollback);

    void deletePicAndMov(List<String> codeList, Integer... contentTypes);

    List<BmsContent> listBySeriesIds(List<Long> idList);

    /**
     * 媒资导出
     * @param req   查询条件
     * @return
     * @throws Exception
     */
    Object export( BmsContentQueryReq req) throws Exception;

    boolean publishRelation(List<Long> ids,Map<String, OutParamExpand> paramMap);

    Page<BmsContent> listContentByStatusAndType(List<Integer> publishStatusList, List<Integer> contentTypeList, Integer pageNo, Integer pageSize);

    /**
     * 根据code查询剧头数据
     * @param spId
     * @param seriesCode
     * @return
     */
    BmsContent getByCode(Long spId, String seriesCode);
}

