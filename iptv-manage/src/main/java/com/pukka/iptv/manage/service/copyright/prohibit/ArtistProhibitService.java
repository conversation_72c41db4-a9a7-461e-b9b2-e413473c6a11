package com.pukka.iptv.manage.service.copyright.prohibit;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.pukka.iptv.common.base.vo.SecurityUser;
import com.pukka.iptv.common.data.model.copyright.ArtistProhibit;
import com.pukka.iptv.common.data.vo.copyright.ArtistProhibitImportVo;
import com.pukka.iptv.common.data.vo.IdList;

import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import java.util.List;

/**
 *
 * @author: zhengcl
 * @date: 2022-7-18 10:33:47
 */

public interface ArtistProhibitService extends IService<ArtistProhibit> {

    /**
     * 批量导出
     * @param body 导出数据
     * @param response Http响应流
     */
    Object export(@Valid IdList body, HttpServletResponse response);

    /**
     * 下载模板
     * @param response http响应流
     */
    void download(HttpServletResponse response);

    /**
     * 批量插入
     * @param vos 批量插入VO
     * @param securityUser 用户
     */
    List<ArtistProhibit> batchInsert(List<ArtistProhibitImportVo> vos, SecurityUser securityUser);

    /**
     * 插入
     * @param artistProhibit 插入VO
     */
    int add(@Valid ArtistProhibit artistProhibit);

    /**
     * 修改
     * @param artistProhibit
     */
    int updateArtistProhibit(@Valid ArtistProhibit artistProhibit);

    /**
     * 查询
     * @param page
     * @param artistProhibit
     * @return
     */
    IPage<ArtistProhibit> selectPage(Page page, ArtistProhibit artistProhibit);
}


