package com.pukka.iptv.manage.controller.bms;

import cn.hutool.core.thread.ThreadUtil;
import cn.hutool.core.util.RandomUtil;
import cn.hutool.json.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.pukka.iptv.common.api.feign.sys.DownloadFeignClient;
import com.pukka.iptv.common.base.vo.CommonResponse;
import com.pukka.iptv.common.core.util.SpringUtils;
import com.pukka.iptv.common.data.model.cms.CmsResource;
import com.pukka.iptv.common.data.model.sys.SysNat;
import com.pukka.iptv.common.data.vo.resp.R;
import com.pukka.iptv.common.proxy.config.SystemInProxyProperties;
import com.pukka.iptv.downloader.model.FileTask;
import com.pukka.iptv.downloader.model.Proxy;
import com.pukka.iptv.downloader.model.resp.DownloadNotifyResp;
import com.pukka.iptv.manage.mapper.cms.CmsResourceMapper;
import com.pukka.iptv.manage.mapper.sys.SysNatMapper;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.http.*;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.client.RestTemplate;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import java.util.List;
import java.util.UUID;

/**
 * @Author: wz
 * @Date: 2021/11/25 11:34
 * @Description:
 */
@RestController
@AllArgsConstructor
@RequestMapping(value = "/download", produces = MediaType.APPLICATION_JSON_VALUE)
@Slf4j
public class TestDownloadController {

    @Autowired
    private CmsResourceMapper cmsResourceMapper;
    @Autowired
    private SysNatMapper natMapper;
    @Autowired
    private DownloadFeignClient downloadFeignClient;
    @Autowired
    @Qualifier("restTemplate")
    private RestTemplate restTemplate;

    private static Proxy proxy;

    @PostConstruct
    private void init() {
        SystemInProxyProperties systemProxy = SpringUtils.getBean(SystemInProxyProperties.class);
        log.info("systemProxy=={}", systemProxy.toString());
        log.info("systemProxy=={}", systemProxy.toString());
        log.info("systemProxy=={}", systemProxy.toString());
        proxy = new Proxy().setEnable(true)
                .setHost(systemProxy.getHost())
                .setPort(systemProxy.getPort())
                .setUsername(systemProxy.getUsername())
                .setPassword(systemProxy.getPassword())
                .setNonHosts(systemProxy.getNonHosts());
    }

    private Proxy getProxy() {
        if (proxy == null) {
            init();
        }
        return proxy;
    }

    private final static String DOWNLOAD_CALLBACK = "http://downloader-server/api/testCallback";

    @GetMapping("/testBalance")
    public CommonResponse<String> testBalance(Integer count) {
        int resCount = 0;
        for (Integer i = 0; i < count; i++) {
            ThreadUtil.sleep(i % 2 == 0 ? 0 : 200);
            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.APPLICATION_JSON);
            ResponseEntity<String> result = restTemplate.postForEntity(DOWNLOAD_CALLBACK,
                    new HttpEntity<>(new JSONObject(), headers),
                    String.class);
            if (!HttpStatus.OK.equals(result.getStatusCode())) {
                log.error(" 【testBalance】 NOT 200 OK !code: {} body: {}", result.getStatusCode(), result.getBody());
            } else {
                log.info("{} 调用成功  code:{}", ++resCount, result.getStatusCode());
            }
        }
        return R.success("成功:" + resCount);
    }

    @GetMapping("/testFegin")
    public CommonResponse<String> testFegin(Integer count) {
        int resCount = 0;
        for (Integer i = 0; i < count; i++) {
            ThreadUtil.sleep(500);
            try {
                com.pukka.iptv.downloader.model.resp.R<DownloadNotifyResp> r =
                        new com.pukka.iptv.downloader.model.resp.R<DownloadNotifyResp>()
                                .setData(new DownloadNotifyResp().setFileCode(i + "_code"));
                com.pukka.iptv.downloader.model.resp.R<?> result = downloadFeignClient.testCallback(r);
                if (result.getCode() != 200) {
                    log.error("【testFegin】NOT 200 OK !code: {} data: {}", result.getCode(), result.getData());
                } else {
                    log.info("{} 调用成功  code:{}", ++resCount, result.getCode());
                }
            } catch (Exception e) {
                log.error(e.getMessage(), e);
            }
        }
        return R.success("成功:" + resCount);
    }

    @GetMapping("/pressureTest")
    public CommonResponse<Integer> pressureTest(Integer count, Integer fileType, Integer postType, String url) {
        if (fileType == null) fileType = -1;
        if (postType == null) postType = 2;//默认feign接口
        pressureTestHandler(count, fileType, postType, url);
        return R.success(count);
    }

    private List<SysNat> proxyFilter() {
        return natMapper.selectList(Wrappers.lambdaQuery(SysNat.class));
    }

    private void pressureTestHandler(Integer count, Integer fileType, Integer postType, String host) {

        LambdaQueryWrapper<CmsResource> last = Wrappers.lambdaQuery(CmsResource.class)
                .select(CmsResource::getFileUrl, CmsResource::getStorageId,
                        CmsResource::getContentCode, CmsResource::getStatus)
                .like(fileType == 1, CmsResource::getFileUrl, ".m3u8")
                .like(fileType == 2, CmsResource::getFileUrl, ".ts")
                .orderBy(true, false, CmsResource::getId)
                .last(" limit " + count);

        List<CmsResource> cmsResources = cmsResourceMapper.selectList(last);
        List<SysNat> sysNats = proxyFilter();
        for (CmsResource item : cmsResources) {
            new Thread(() -> {
                try {
                    log.info("pressureTest 发送fegin请求");
                    FileTask fileTask = generalFileTask(item, sysNats, host);
                    if (postType == 1) {
                        post(host + "/api/addTask", fileTask);
                    } else {
                        CommonResponse<DownloadNotifyResp> result = downloadFeignClient.save(fileTask);
                        log.info(result.toString());
                    }
                } catch (Exception e) {
                    log.error(e.getMessage(), e);
                } finally {
                    log.info("pressureTest 发送结束");
                }
            }).start();
        }
    }

    private void post(String url, Object param) {
        log.info("rest param{}", param.toString());
        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_JSON_UTF8);
        ResponseEntity<String> result = restTemplate.postForEntity(url, new HttpEntity<>(param, headers), String.class);
        HttpStatus statusCode = result.getStatusCode();
        if (statusCode.equals(HttpStatus.OK)) {
            log.info(" 成功！{}", result);
        } else {
            log.error(" 异常{}", result);
        }
    }


    private FileTask generalFileTask(CmsResource resource, List<SysNat> sysNats, String host) {
        FileTask fileTask = new FileTask();
        fileTask.setFileType(1)
                .setAsync(1)
                .setPriority(1)
                .setFileCode(resource.getContentCode())
                .setTargetUrl(generalTargetUrl(resource.getFileUrl()))
                .setSourceUrl(resource.getFileUrl())
                .setStoreId(resource.getStorageId())
                .setStatus(resource.getStatus())
                .setNotifyUrl(host + "/api/test/testCallback");
        //添加代理
        boolean parallel = sysNats.stream().filter(i -> resource.getFileUrl()
                .contains(i.getNatSource()))
                .isParallel();
        if (parallel) {
            fileTask.setProxy(getProxy());
        }
        return fileTask;
    }

    private static String generalTargetUrl(String url) {
        return "ftp://vstore:p@127.0.0.1:7777/wztest/" + RandomUtil.randomString(6) + "/" +
                parseFileName(url);
    }

    private static String parseFileName(String url) {
        //包含?要解析出文件名称
        int i = url.lastIndexOf("/") + 1;
        int j = url.indexOf("?");
        j = j < 0 ? url.length() : j;
        return url.substring(i, j);
    }
}
