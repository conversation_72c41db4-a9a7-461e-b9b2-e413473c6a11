package com.pukka.iptv.manage.service.sys;


import com.pukka.iptv.common.data.model.order.SubOrderXmlEntity;

/**
 * @Author: liaowj
 * @Description: 自动发布内容状态修改
 * @CreateDate: 2022/1/18 16:23
 * @Version: 1.0
*/
public interface AutoPublishService {

    Boolean dealContent(String correlateid, String spids, Integer result, boolean isFinish);

    SubOrderXmlEntity getOrderXmlEntityPublic(String correlateId);
}
