package com.pukka.iptv.manage.controller.sys;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.pukka.iptv.common.base.vo.CommonResponse;
import com.pukka.iptv.common.data.model.sys.SysStorageDirctory;
import com.pukka.iptv.common.data.vo.IdList;
import com.pukka.iptv.common.log.annotation.SysLog;
import com.pukka.iptv.common.log.enums.OperateObjectEnum;
import com.pukka.iptv.common.log.enums.OperateTypeEnum;
import com.pukka.iptv.manage.service.sys.SysStorageDirctoryService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.AllArgsConstructor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;

/**
 *
 * @author: zhengcl
 * @date: 2021-9-8 18:12:15
 */

@RestController
@AllArgsConstructor
@RequestMapping(value = "/sysStorageDirctory", produces = MediaType.APPLICATION_JSON_VALUE)
@Api(tags="sysStorageDirctory管理")
public class SysStorageDirctoryController {

    @Autowired
    private SysStorageDirctoryService sysStorageDirctoryService;

    @ApiOperation(value = "分页",notes = "必需传目录storageId")
    @GetMapping("/page" )
    public CommonResponse<Page> page(@Valid Page<SysStorageDirctory> page, SysStorageDirctory sysStorageDirctory) {
        return  CommonResponse.success(sysStorageDirctoryService.page(page, sysStorageDirctory));
    }

    @ApiOperation(value = "详情")
    @GetMapping("/getById")
    public CommonResponse<SysStorageDirctory> getById(@Valid @RequestParam(name = "id", required = true)  Long id) {
        return CommonResponse.success(sysStorageDirctoryService.getById(id));
    }

    @SysLog(objectType = OperateObjectEnum.SYS_STORAGE_DIRCTORY, operateType = OperateTypeEnum.SAVE, objectIds = "#sysStorageDirctory.id", objectNames = "#sysStorageDirctory.name")
    @ApiOperation(value = "新增")
    @PostMapping("/save")
    public CommonResponse<Boolean> save(@Valid @RequestBody SysStorageDirctory sysStorageDirctory) {
        return  CommonResponse.success(sysStorageDirctoryService.saveToCacheAndDB(sysStorageDirctory));
    }

    @SysLog(objectType = OperateObjectEnum.SYS_STORAGE_DIRCTORY, operateType = OperateTypeEnum.UPDATE, objectIds = "#sysStorageDirctory.id", objectNames = "#sysStorageDirctory.name")
    @ApiOperation(value = "修改")
    @PutMapping("/update")
    public CommonResponse<Boolean> updateById(@Valid @RequestBody SysStorageDirctory sysStorageDirctory) {
        return CommonResponse.success(sysStorageDirctoryService.updateCacheAndDB(sysStorageDirctory));
    }

    @SysLog(objectType = OperateObjectEnum.SYS_STORAGE_DIRCTORY, operateType = OperateTypeEnum.DELETE, objectIds = "#id")
    @ApiOperation(value = "删除",hidden = true)
    @DeleteMapping("/deleteById" )
    public CommonResponse<Boolean> deleteById(@Valid @RequestParam(name = "id", required = true) Long id) {
        return  CommonResponse.success(sysStorageDirctoryService.removeCacheAndDB(id));
    }

    @SysLog(objectType = OperateObjectEnum.SYS_STORAGE_DIRCTORY, operateType = OperateTypeEnum.DELETE, objectIds = "#idList.ids")
    @ApiOperation(value = "批量删除")
    @DeleteMapping("/deleteByIds" )
    public CommonResponse<Boolean> deleteByIds(@Valid @RequestBody IdList idList) {
        return  CommonResponse.success(sysStorageDirctoryService.removeCacheAndDB(idList.getIds()));
    }

}
