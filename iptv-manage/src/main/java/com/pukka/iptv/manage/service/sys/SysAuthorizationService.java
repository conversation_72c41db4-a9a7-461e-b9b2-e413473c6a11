package com.pukka.iptv.manage.service.sys;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.pukka.iptv.common.data.dto.*;
import com.pukka.iptv.common.data.model.sys.SysAuthorization;
import com.pukka.iptv.common.data.params.OutPassageParam;
import com.pukka.iptv.common.data.params.SysAuthorizationChannelParam;
import com.pukka.iptv.common.data.params.SysAuthorizationContentParam;
import com.pukka.iptv.common.data.params.SysAuthorizationParam;
import com.pukka.iptv.common.data.vo.req.BmsContentQueryReq;
import com.pukka.iptv.common.data.vo.req.BmsContentQueryReqBySP;

import java.util.List;
import java.util.concurrent.ExecutionException;

/**
 *
 * @author: tan
 * @date: 2021-8-26 18:34:22
 */

public interface SysAuthorizationService extends IService<SysAuthorization> {

    /**
     * 分页查询合同列表
     *
     * @param page 分页参数
     * @param entity 查询参数
     * @return list
     */
    Page<SysAuthorization> selectByPage(Page<SysAuthorization> page,SysAuthorization entity);

    /**
     * 保存授权合同
     *
     * @param sysAuthorization 实体
     * @return bool  ture:保存成功  false：保存失败
     */
    @Override
    boolean save(SysAuthorization sysAuthorization);

    /**
     * 修改授权合同
     *
     * @param sysAuthorization 实体
     * @return bool  ture:修改成功  false：修改失败
     */
    @Override
    boolean updateById(SysAuthorization sysAuthorization);

    /**
     * 通过ids批量删除授权合同
     *
     * @param ids  id集合
     * @return bool
     */
    boolean removeByIds(List<Long> ids);

    /**
     * 查询授权合同树
     *
     * @param name CP名称
     * @return list
     */
    List<SysAuthorizationTreeDto> getAuthorizationList(String name,Integer businessType);

    /**
     * 分页查询未授权的媒资
     *
     * @param page 分页参数
     * @param param 查询参数
     * @return page
     */
    Page<SysAuthorizationContentDto> getUnauthorizedContent(Page page, SysAuthorizationParam param);


    IPage<BmsContentQueryReqBySP> getUnauthorizedContentBySp(Page page, BmsContentQueryReq param);

    /**
     * 未授权+已授权+剧集+单集
     * @param page
     * @param param
     * @return
     */
    IPage<BmsContentQueryReqBySP> getUnauthorizedContentAndAuthBySp(Page page, BmsContentQueryReq param);

    /**
     * 未授权+已授权+剧集
     * @param page
     * @param param
     * @return
     */
    IPage<BmsContentQueryReqBySP> getUnauthorizedContentAndAuthBySpForSeries(Page page, BmsContentQueryReq param);

    /**
     * 未授权+已授权+单集
     * @param page
     * @param param
     * @return
     */
    IPage<BmsContentQueryReqBySP> getUnauthorizedContentAndAuthBySpForProgram(Page page, BmsContentQueryReq param);

    /**
     * 引入媒资
     *
     * @param param 媒资参数
     * @return bool
     */
    boolean authorizeContent(SysAuthorizationContentParam param);

    /**
     * 根据cp引入媒资
     * @param param
     * @return
     */
    String authorizeContentBySps(SysAuthorizationContentParam param);

    String authorizeContentBySpsForThread(SysAuthorizationContentParam param) throws ExecutionException, InterruptedException;


    public String authrizeContentBySpsForThreaDataConversion(SysAuthorizationContentParam param);
    /**
     * 移除媒资
     *
     * @param ids    媒资id集合
     * @return bool
     */
    boolean deleteAuthorizedContent(List<Long> ids);

    String deleteAuthorizedContentBySps(SysAuthorizationContentParam param);

    String deleteAuthorizedContentBySpsForThreaDataConversion(SysAuthorizationContentParam param);

    String deleteAuthorizedContentForThread (SysAuthorizationContentParam param) ;

    /**
     * 修改授权通道
     *
     * @param param 修改参数
     * @return bool
     */
    boolean updateOutPassage(OutPassageParam param);


    /**
     * 通过spId查询授权通道列表
     *
     * @param spId spid
     * @return list
     */
    List<OutPassageDto> getOutPassageList(Long spId);

    List<OutSpDto> getOutPassageListByCp(Long cpId);

    /**
     * 通过媒资id查询授权通道
     *
     * @param id 媒资id
     * @return list
     */
    List<OutPassageDto> getOutPassageListByContentId(Long id,Long spId,Integer type);

    /**
     * 分页查询未授权的频道
     *
     * @param page 分页参数
     * @param param 频道名称
     * @return list
     */
    Page<SysAuthorizationChannelDto> getUnauthorizedChannelPage(Page page, SysAuthorizationParam param);

    /**
     * 保存授权频道
     *
     * @param param 参数
     * @return bool
     */
    boolean saveAuthorizeChannel(SysAuthorizationChannelParam param);

    /**
     * 移除频道
     *
     * @param ids 频道id集合
     * @return bool
     */
    boolean deleteAuthorizedChannel(List<Long> ids);

    /**
     * 保存到缓存和数据库
     * @param sysAuthorization
     * @return
     */
    boolean saveToCacheAndDB(SysAuthorization sysAuthorization);


    boolean updateToCacheAndDB(SysAuthorization sysAuthorization);

    boolean updateToCache(SysAuthorization sysAuthorization);

    boolean deleteToCacheAndDB(Long id);

    boolean deleteToCache(Long id);

    boolean deleteToCacheAndDB(List<Long> ids);
}


