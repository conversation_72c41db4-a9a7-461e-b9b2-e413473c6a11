package com.pukka.iptv.manage.rule.handle;

import com.pukka.iptv.common.base.enums.ContentTypeEnum;
import com.pukka.iptv.common.data.model.copyright.RuleProhibit;
import com.pukka.iptv.common.data.vo.copyright.RuleProhibitVo;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * @Author: chiron
 * @Date: 2022/08/03/09:04
 * @Description: 剧集规则处理
 */
@Slf4j
@Component
public class SeriesProhibitRuleHandle extends AbstractProhibitRuleHandle {

    @Override
    public ContentTypeEnum getContentType() {
        return ContentTypeEnum.TELEPLAY;
    }

    @Override
    public Map<String, RuleProhibit> handle(List<RuleProhibit> ruleProhibitList) {
        List<RuleProhibit> productNoList = ruleProhibitList.stream().filter(
                (RuleProhibit ruleProhibit) -> ContentTypeEnum.TELEPLAY.getValue().equals
                        (ruleProhibit.getContentType())).collect(Collectors.toList());
        Map<String, RuleProhibit> longListMap = integrationCollection(productNoList);
        return longListMap;
    }

    @Override
    public Integer execute(List<RuleProhibit> ruleProhibitList, RuleProhibitVo entity) {
        return super.execute(ruleProhibitList, entity);
    }
}
