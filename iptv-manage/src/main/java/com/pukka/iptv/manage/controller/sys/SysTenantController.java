package com.pukka.iptv.manage.controller.sys;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.pukka.iptv.common.base.vo.CommonResponse;
import com.pukka.iptv.common.data.model.sys.SysTenant;
import com.pukka.iptv.common.data.vo.IdList;
import com.pukka.iptv.common.log.annotation.SysLog;
import com.pukka.iptv.common.log.enums.OperateObjectEnum;
import com.pukka.iptv.common.log.enums.OperateTypeEnum;
import com.pukka.iptv.manage.service.sys.SysTenantService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.AllArgsConstructor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.List;

/**
 *
 * @author: zhengcl
 * @date: 2021-8-18 10:37:54
 */

@RestController
@AllArgsConstructor
@RequestMapping(value = "/sysTenant", produces = MediaType.APPLICATION_JSON_VALUE)
@Api(tags="sysTenant管理")
public class SysTenantController {

    @Autowired
    private SysTenantService sysTenantService;

    @ApiOperation(value = "分页")
    @GetMapping("/page" )
    public CommonResponse<Page> page(@Valid Page<SysTenant> page, SysTenant sysTenant) {
        return  CommonResponse.success(sysTenantService.page(page, sysTenant));
    }

    @ApiOperation(value = "详情")
    @GetMapping("/getById")
    public CommonResponse<SysTenant> getById(@Valid @RequestParam(name = "id", required = true)  Long id) {
        return CommonResponse.success(sysTenantService.getById(id));
    }

    @SysLog(objectType = OperateObjectEnum.SYS_TENANT, operateType = OperateTypeEnum.SAVE, objectIds = "#sysTenant.id", objectNames = "#sysTenant.name")
    @ApiOperation(value = "新增")
    @PostMapping("/save")
    public CommonResponse<Boolean> save(@Valid @RequestBody SysTenant sysTenant) {
        return  CommonResponse.success(sysTenantService.save(sysTenant));
    }

    @SysLog(objectType = OperateObjectEnum.SYS_TENANT, operateType = OperateTypeEnum.UPDATE, objectIds = "#sysTenant.id", objectNames = "#sysTenant.name")
    @ApiOperation(value = "修改")
    @PutMapping("/update")
    public CommonResponse<Boolean> update(@Valid @RequestBody SysTenant sysTenant) {
        return CommonResponse.success(sysTenantService.update(sysTenant));
    }
    @SysLog(objectType = OperateObjectEnum.SYS_TENANT, operateType = OperateTypeEnum.UPDATE, objectIds = "#sysTenant.id", objectNames = "#sysTenant.name")
    @ApiOperation(value = "修改状态")
    @PutMapping("/updateById")
    public CommonResponse<Boolean> updateById(@Valid @RequestBody SysTenant sysTenant) {
        return CommonResponse.success(sysTenantService.updateById(sysTenant));
    }

    @SysLog(objectType = OperateObjectEnum.SYS_TENANT, operateType = OperateTypeEnum.DELETE,objectIds = "#id")
    @ApiOperation(value = "删除",hidden = true)
    @DeleteMapping("/deleteById" )
    public CommonResponse<Boolean> deleteById(@Valid @RequestParam(name = "id", required = true) Long id) {
        return  CommonResponse.success(sysTenantService.removeByIdWithValid(id));
    }

    @SysLog(objectType = OperateObjectEnum.SYS_TENANT, operateType = OperateTypeEnum.DELETE,objectIds = "#idList.ids")
    @ApiOperation(value = "批量删除")
    @DeleteMapping("/deleteByIds" )
    public CommonResponse<Boolean> deleteByIds(@Valid @RequestBody IdList idList) {
        return  CommonResponse.success(sysTenantService.removeByIdsWithValid(idList.getIds()));
    }

    @ApiOperation(value ="查询列表" )
    @GetMapping("/listByUserId")
    public CommonResponse<List<SysTenant>> listByUserId(@RequestParam(name = "userId", required = true) Long userId) {
        return CommonResponse.success(sysTenantService.listByUserId(userId));
    }


    @ApiOperation(value ="查询全部列表" )
    @GetMapping("/listAll")
    public CommonResponse<List<SysTenant>> listByUserId() {
        return CommonResponse.success(sysTenantService.listAll());
    }
}
