package com.pukka.iptv.manage.listener.mq;

import static java.util.regex.Pattern.compile;

import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.google.common.base.Joiner;
import com.pukka.iptv.common.base.constant.RedisKeyConstants;
import com.pukka.iptv.common.base.enums.AutoAuthorizeEnum;
import com.pukka.iptv.common.base.enums.ContentTypeEnum;
import com.pukka.iptv.common.base.enums.CpCheckStatusEnum;
import com.pukka.iptv.common.base.enums.DictionaryBaseEnums;
import com.pukka.iptv.common.base.enums.IsProhibitEnum;
import com.pukka.iptv.common.base.enums.MediaAssociationStatusEnum;
import com.pukka.iptv.common.base.enums.MovieStatusEnum;
import com.pukka.iptv.common.base.enums.OpCheckStatusEnum;
import com.pukka.iptv.common.base.enums.ProhibitPointEnum;
import com.pukka.iptv.common.base.enums.ProhibitSkipEnum;
import com.pukka.iptv.common.base.enums.ProhibitStatusEnum;
import com.pukka.iptv.common.base.enums.RequestResourceEnum;
import com.pukka.iptv.common.base.enums.SkipCheckEnum;
import com.pukka.iptv.common.base.enums.StatusEnum;
import com.pukka.iptv.common.base.enums.StorageStatusEnum;
import com.pukka.iptv.common.core.util.MD5;
import com.pukka.iptv.common.core.util.bean.BeanUtils;
import com.pukka.iptv.common.data.dto.TeleplayExcelDto;
import com.pukka.iptv.common.data.model.bms.BmsContent;
import com.pukka.iptv.common.data.model.cms.CmsMovie;
import com.pukka.iptv.common.data.model.cms.CmsResource;
import com.pukka.iptv.common.data.model.cms.CmsSeries;
import com.pukka.iptv.common.data.model.sys.SysAuthorization;
import com.pukka.iptv.common.data.model.sys.SysCp;
import com.pukka.iptv.common.data.model.sys.SysCpContentProvider;
import com.pukka.iptv.common.data.model.sys.SysDictionaryBase;
import com.pukka.iptv.common.data.model.sys.SysDictionaryItem;
import com.pukka.iptv.common.data.model.sys.SysSp;
import com.pukka.iptv.common.data.model.sys.SysStorage;
import com.pukka.iptv.common.data.vo.cms.CmsSeriesVO;
import com.pukka.iptv.common.redis.service.RedisService;
import com.pukka.iptv.manage.config.TeleplayMQConfig;
import com.pukka.iptv.manage.rule.IRuleProhibitHelper;
import com.pukka.iptv.manage.service.bms.BmsContentService;
import com.pukka.iptv.manage.service.cms.CmsMovieService;
import com.pukka.iptv.manage.service.cms.CmsResourceService;
import com.pukka.iptv.manage.service.cms.CmsSeriesService;
import com.pukka.iptv.manage.service.common.FtpUtil;
import com.pukka.iptv.manage.service.sys.SysAuthorizationService;
import com.pukka.iptv.manage.service.sys.SysCpContentProviderService;
import com.pukka.iptv.manage.service.sys.SysCpService;
import com.pukka.iptv.manage.service.sys.SysDictionaryBaseService;
import com.pukka.iptv.manage.service.sys.SysDictionaryItemService;
import com.pukka.iptv.manage.service.sys.SysStorageService;
import com.rabbitmq.client.Channel;
import java.io.IOException;
import java.util.ArrayList;
import java.util.Comparator;
import java.util.Date;
import java.util.List;
import java.util.TreeSet;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.springframework.amqp.core.Message;
import org.springframework.amqp.rabbit.annotation.Exchange;
import org.springframework.amqp.rabbit.annotation.Queue;
import org.springframework.amqp.rabbit.annotation.QueueBinding;
import org.springframework.amqp.rabbit.annotation.RabbitListener;
import org.springframework.amqp.support.converter.MessageConversionException;
import org.springframework.amqp.support.converter.MessageConverter;
import org.springframework.beans.BeansException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

@Component
@Slf4j
public class TeleplayMQListener {

    @Autowired
    private SysCpService sysCpService;

    @Autowired
    private SysCpContentProviderService sysCpContentProviderService;

    @Autowired
    private CmsSeriesService cmsSeriesService;

    @Autowired
    private CmsMovieService cmsMovieService;

    @Autowired
    private SysAuthorizationService sysAuthorizationService;

    @Autowired
    private BmsContentService bmsContentService;

    @Autowired
    private CmsResourceService cmsResourceService;

    @Autowired
    private RedisService redisService;

    @Autowired
    private SysDictionaryBaseService sysDictionaryBaseService;

    @Autowired
    private SysDictionaryItemService sysDictionaryItemService;

    @Autowired
    private MessageConverter jackson2JsonMessageConverter;

    @Autowired
    private SysStorageService storageService;

    @Autowired
    private IRuleProhibitHelper iRuleProhibitHelper;

    @Transactional(rollbackFor = Exception.class)
    @RabbitListener(bindings = {
            @QueueBinding(value = @Queue(value = TeleplayMQConfig.TELEPLAY_EXCEL_IMPORT_QUEUE),
                    exchange = @Exchange(value = TeleplayMQConfig.TELEPLAY_EXCEL_IMPORT_EXCHANGE),
                    key = TeleplayMQConfig.TELEPLAY_EXCEL_IMPORT_ROUTING)}, containerFactory = "rabbitListenerContainerFactory")
    public void recieved(Message messageorigin, Channel channel) {
        try {
            String excel = (String) jackson2JsonMessageConverter.fromMessage(messageorigin);
            log.info("excel剧集导入消息消费:{}", excel);
            List<TeleplayExcelDto> teleplayExcelDtos = JSONObject.parseArray(excel, TeleplayExcelDto.class);
            for (TeleplayExcelDto teleplayExcelDto : teleplayExcelDtos) {
                try {
                    CmsSeries cmsSeries = new CmsSeries();
                    org.springframework.beans.BeanUtils.copyProperties(teleplayExcelDto, cmsSeries);
                    cmsSeries.setProhibitStatus(ProhibitStatusEnum.NO.getValue());
        /*            CmsSeries series = cmsSeriesService.getOne(Wrappers.<CmsSeries>lambdaQuery()
                            .eq(CmsSeries::getCpName, cmsSeries.getCpName())
                            .eq(CmsSeries::getName, cmsSeries.getName())
                            .last("limit 1")
                    );

                    if (ObjectUtil.isNotEmpty(series)) {
                        log.info("剧头导入失败 名称：{},所属CP：{}已存在", series.getName(), series.getCpName());
                        continue;
                    }*/
                    //查询所属cp是否为跳过自审 如果是跳过自审
                    SysCp sysCp = sysCpService.getByName(cmsSeries.getCpName());
                    if (ObjectUtil.isNotEmpty(sysCp)) {
                        if (sysCp.getSkipCheck().equals(SkipCheckEnum.NoSkip.getValue())) {
                            cmsSeries.setCpCheckStatus(CpCheckStatusEnum.Await.getValue());
                            cmsSeries.setOpCheckStatus(OpCheckStatusEnum.Await.getValue());
                        }
                        if (sysCp.getSkipCheck().equals(SkipCheckEnum.SelfSkip.getValue())) {
                            cmsSeries.setCpCheckStatus(CpCheckStatusEnum.Pass.getValue());
                            cmsSeries.setCpCheckDesc("自动跳过审核");
                            cmsSeries.setCpCheckTime(new Date());
                            cmsSeries.setOpCheckStatus(OpCheckStatusEnum.Await.getValue());
                        }
                        if (sysCp.getSkipCheck().equals(SkipCheckEnum.EndSkip.getValue())) {
                            cmsSeries.setOpCheckStatus(OpCheckStatusEnum.Pass.getValue());
                            cmsSeries.setOpCheckDesc("自动跳过审核");
                            cmsSeries.setOpCheckTime(new Date());
                            cmsSeries.setCpCheckStatus(CpCheckStatusEnum.Await.getValue());
                        }

  /*                      List<SysCpContentProvider> providerList = sysCpContentProviderService.getByCpId(sysCp.getId());
                        if(providerList != null && providerList.size() > 0) {
                            providerList.forEach(p -> {
                                if (!p.getName().equals(teleplayExcelDto.getContentProvider())) {
                                    cmsSeries.setContentProvider(null);
                                }
                            });
                        }*/

                        List<SysCpContentProvider> providerList = sysCpContentProviderService.getByCpId(sysCp.getId());
                        List<String> providerName = new ArrayList<>();
                        if (ObjectUtil.isNotEmpty(providerList)) {
                            providerList.forEach(p -> providerName.add(p.getName()));
                            if (!providerName.contains(teleplayExcelDto.getContentProvider())) {
                                cmsSeries.setContentProvider(null);
                            }
                        } else {
                            cmsSeries.setContentProvider(null);
                        }
                        //如果为全部跳过则修改自审和终审状态
                        if (sysCp.getSkipCheck().equals(SkipCheckEnum.AllSkip.getValue())) {
                            cmsSeries.setCpCheckStatus(CpCheckStatusEnum.Pass.getValue());
                            cmsSeries.setOpCheckStatus(CpCheckStatusEnum.Pass.getValue());
                            cmsSeries.setOpCheckDesc("自动跳过审核");
                            cmsSeries.setOpCheckTime(new Date());
                            cmsSeries.setCpCheckDesc("自动跳过审核");
                            cmsSeries.setCpCheckTime(new Date());
                        }
                        cmsSeries.setCpId(sysCp.getId());
                        cmsSeries.setPreviewStatus(MovieStatusEnum.NotRelevancy.getValue());
                        cmsSeries.setSource(RequestResourceEnum.SYSTEM.getCode());
                        cmsSeries.setStatus(1);
                        cmsSeries.setIsProhibit(IsProhibitEnum.ING.getValue());
                        //一级分类二级分类 产地值设置
                        setItem(cmsSeries);
                        teleplayExcelDto.def(cmsSeries);
                        setPreviewCode(cmsSeries, teleplayExcelDto.getPreviewName(), sysCp);
                        cmsSeriesService.save(cmsSeries);
                        //违禁检测
                        if (!ProhibitSkipEnum.SKIP.getValue().equals(sysCp.getSkipProhibit())) {
                            ProhibitPointEnum prohibitPointEnum = null;
                            switch (ProhibitPointEnum.getByValue(sysCp.getPointProhibit())) {
                                case INSERT:
                                    prohibitPointEnum = ProhibitPointEnum.INSERT;
                                    break;
                                case EXAMINE:
                                    if (sysCp.getSkipCheck().equals(SkipCheckEnum.SelfSkip.getValue())
                                            || sysCp.getSkipCheck().equals(SkipCheckEnum.AllSkip.getValue())) {
                                        prohibitPointEnum = ProhibitPointEnum.EXAMINE;
                                    }
                                    break;
                                default:
                                    break;
                            }
                            if (ObjectUtils.isNotEmpty(prohibitPointEnum)) {
                                CmsSeriesVO cmsSeriesVO = new CmsSeriesVO(cmsSeries, prohibitPointEnum);
                                CmsSeries cmsSeries1 = iRuleProhibitHelper.checkSeriesRuleProhibit(cmsSeriesVO);
                                cmsSeries.setProhibitStatus(cmsSeries1.getProhibitStatus());
                                cmsSeries.setIsProhibit(cmsSeries1.getIsProhibit());
                                cmsSeriesService.updateById(cmsSeries);
                            }
                        } else {
                            cmsSeries.setProhibitStatus(ProhibitStatusEnum.NO.getValue());
                            cmsSeries.setIsProhibit(IsProhibitEnum.SKIP.getValue());
                            cmsSeriesService.updateById(cmsSeries);
                        }
                        //判断是否开启自动授权
                        List<SysAuthorization> sysAuthorizations = sysAuthorizationService
                                .list(Wrappers.<SysAuthorization>lambdaQuery()
                                        .eq(SysAuthorization::getCpName, cmsSeries.getCpName())
                                        .eq(SysAuthorization::getStatus, StatusEnum.COME.getCode()));
                        List<SysAuthorization> filterSysAuthorizations = new ArrayList<>();
                        if(ObjectUtils.isNotEmpty(sysAuthorizations)) {
                            filterSysAuthorizations = sysAuthorizations.stream()
                                    .filter(sysAuthorization ->
                                            ObjectUtils.isNotEmpty(redisService
                                                    .getCacheMapValue(RedisKeyConstants.SYS_SP
                                                            , String.valueOf(sysAuthorization.getSpId()))))
                                    .collect(Collectors.toList());
                        }
                        if (ProhibitStatusEnum.NO.getValue().equals(cmsSeries.getProhibitStatus()) && ObjectUtil.isNotEmpty(filterSysAuthorizations)) {
                            for (SysAuthorization sysAuthorization : filterSysAuthorizations) {
                                BmsContent bmsContent = new BmsContent();
                                //判断是否开启自动授权
                                if (sysAuthorization.getAutoAuthorize().equals(AutoAuthorizeEnum.Yes.getValue())) {
                                    //将剧集内容写入bms_content表中
                                    BeanUtils.copyProperties(cmsSeries, bmsContent, "id", "code");
                                    set(cmsSeries, sysAuthorization, bmsContent);
                                    bmsContent.setStatus(1);
                                    bmsContentService.save(bmsContent);
                                }
                            }
                        }
                    } else {
                        log.info("剧集模板导入失败:当前CP不存在");
                    }
                } catch (BeansException e) {
                    log.error("剧集导入异常:" + e.getMessage());
                    e.printStackTrace();
                }

            }
        } catch (MessageConversionException e) {
            log.error("剧集导入异常:" + e.getMessage());
            e.printStackTrace();
        } finally {
            //成功确认消息
            try {
                log.info("消息成功确认");
                channel.basicAck(messageorigin.getMessageProperties().getDeliveryTag(), false);
            } catch (IOException e) {
                log.info("basicAck ex:" + e.getMessage());
                try {
                    channel.basicAck(messageorigin.getMessageProperties().getDeliveryTag(), false);
                } catch (IOException ee) {
                    log.info("again basicAck ex:" + ee.getMessage());

                }
            }
        }
    }

    private void setItem(CmsSeries cmsSeries) {
        //一级分类id
        if (ObjectUtil.isNotEmpty(cmsSeries.getPgmCategory())) {
            SysDictionaryBase firstClass = sysDictionaryBaseService.getOne(Wrappers.<SysDictionaryBase>lambdaQuery().select(SysDictionaryBase::getId).eq(SysDictionaryBase::getCode, DictionaryBaseEnums.FIRST_CLASS.getValue()));
            SysDictionaryItem one = sysDictionaryItemService.getOne(Wrappers.<SysDictionaryItem>lambdaQuery()
                    .select(SysDictionaryItem::getId, SysDictionaryItem::getName)
                    .eq(SysDictionaryItem::getDictionaryBaseId, firstClass.getId())
                    .eq(SysDictionaryItem::getName, cmsSeries.getPgmCategory())
                    .last("limit 1")
            );
            if (ObjectUtil.isNotEmpty(one)) {
                cmsSeries.setPgmCategoryId(one.getId());
                cmsSeries.setPgmCategory(one.getName());
            } else if (ObjectUtil.isEmpty(one)) {
                cmsSeries.setPgmCategory(null);
            }
        }
        if (ObjectUtil.isNotEmpty(cmsSeries.getPgmSndClass())) {
            //将excel中的二级分类切割成数组
            String[] split = cmsSeries.getPgmSndClass().split(",");
            //查出二级分类的dictionaryBaseId
            SysDictionaryBase secondClass = sysDictionaryBaseService.getOne(Wrappers.<SysDictionaryBase>lambdaQuery().select(SysDictionaryBase::getId).eq(SysDictionaryBase::getCode, DictionaryBaseEnums.SECOND_CLASS.getValue()));
            //根据二级分类dictionaryBaseId 和 名称 查出所有二级分类 并进行名称去重
            List<SysDictionaryItem> list = sysDictionaryItemService.list(Wrappers.<SysDictionaryItem>lambdaQuery()
                    .select(SysDictionaryItem::getId, SysDictionaryItem::getName)
                    .eq(SysDictionaryItem::getDictionaryBaseId, secondClass.getId())
                    .in(SysDictionaryItem::getName, split)
            );
            //将二级分类id存入库中
            if (ObjectUtil.isNotEmpty(list)) {
                TreeSet<SysDictionaryItem> collect = list.stream().collect(Collectors.toCollection(() -> new TreeSet<>(Comparator.comparing(p -> p.getName()))));
                //去重后的ID
                List<Long> idList = new ArrayList<>();
                collect.forEach(p -> idList.add(p.getId()));
                String idJoin = Joiner.on(",").join(idList);
                cmsSeries.setPgmSndClassId(idJoin);

                List<String> nameList = new ArrayList<>();
                collect.forEach(p -> nameList.add(p.getName()));
                String nameJoin = Joiner.on(",").join(nameList);
                cmsSeries.setPgmSndClass(nameJoin);
            } else if (ObjectUtil.isEmpty(list)) {
                cmsSeries.setPgmSndClass(null);
            }
        }
        //产地
        if (ObjectUtil.isNotEmpty(cmsSeries.getOriginalCountry())) {
            SysDictionaryBase originalCountry = sysDictionaryBaseService.getOne(Wrappers.<SysDictionaryBase>lambdaQuery().select(SysDictionaryBase::getId).eq(SysDictionaryBase::getCode, DictionaryBaseEnums.ORIGINAL_COUNTRY.getValue()));
            SysDictionaryItem orig = sysDictionaryItemService.getOne(Wrappers.<SysDictionaryItem>lambdaQuery().select(SysDictionaryItem::getId)
                    .eq(SysDictionaryItem::getDictionaryBaseId, originalCountry.getId())
                    .eq(SysDictionaryItem::getName, cmsSeries.getOriginalCountry())
                    .last("limit 1"));
            if (ObjectUtil.isNotEmpty(orig)) {
                cmsSeries.setOriginalCountryId(orig.getId());
            } else if (ObjectUtil.isEmpty(orig)) {
                cmsSeries.setOriginalCountry(null);
            }
        }

    }

    private void set(CmsSeries cmsSeries, SysAuthorization sysAuthorization, BmsContent bmsContent) {
        //关联
        bmsContent.setCmsContentCode(cmsSeries.getCode());
        bmsContent.setCmsContentId(cmsSeries.getId());
        bmsContent.setContentType(ContentTypeEnum.TELEPLAY.getValue());

        bmsContent.setSpId(sysAuthorization.getSpId());
        bmsContent.setSpName(sysAuthorization.getSpName());
        bmsContent.setCpId(sysAuthorization.getCpId());
        bmsContent.setCpName(sysAuthorization.getCpName());

//        SysSp sysSp = sysSpMapper.selectById(sysAuthorization.getSpId());
        SysSp sysSp = redisService.getCacheMapValue(RedisKeyConstants.SYS_SP, String.valueOf(sysAuthorization.getSpId()));
        if (ObjectUtil.isNotEmpty(sysSp)) {
            bmsContent.setOutPassageIds(sysSp.getOutPassageIds());
            bmsContent.setOutPassageNames(sysSp.getOutPassageNames());
            bmsContent.setBmsSpChannelId(sysSp.getBmsSpChannelId());
            bmsContent.setBmsSpChannelName(sysSp.getBmsSpChannelName());
        }
    }

    /**
     * @param cmsProgram
     * @param previewName 视频名称
     * @param sysCp       cp信息
     */
    public void setPreviewCode(CmsSeries cmsProgram, String previewName, SysCp sysCp) {
        if (ObjectUtil.isNotEmpty(previewName)) {
//            CmsResource cmsResource = cmsResourceService.getOne(Wrappers.<CmsResource>lambdaQuery().eq(CmsResource::getName, previewName).eq(CmsResource::getCpName, cmsProgram.getCpName()).eq(CmsResource::getType, 2));
            String encrypt = MD5.encrypt("[" + sysCp.getId() + "]" + "[" + previewName + "]");
//            cmsProgram.setPreviewStatus(MovieStatusEnum.Relevancy.getValue());
            cmsProgram.setResourcePreviewCode(encrypt);
            log.info("子集已关联预览片");

        }
    }


    /**
     * 关联视频
     *
     * @param name
     */
    public void movRelevancy(CmsSeries cmsSeries, String name, SysCp sysCp) {
        if (ObjectUtil.isNotEmpty(name)) {
            CmsResource cmsResource = cmsResourceService.getOne(Wrappers.<CmsResource>lambdaQuery().eq(CmsResource::getName, name).eq(CmsResource::getCpName, cmsSeries.getCpName()).eq(CmsResource::getType, 2));
            //如果文件名正确且视频未关联 则关联视频
            if (cmsResource != null) {
                if (cmsResource.getContentStatus() == 1) {
                    String encrypt = MD5.encrypt("[" + sysCp.getId() + "]" + "[" + name + "]");
                    cmsSeries.setResourcePreviewCode(encrypt);
                    //视频与媒资建立关系
                    cmsSeries.setPreviewStatus(MovieStatusEnum.Relevancy.getValue());

                    cmsSeriesService.updateById(cmsSeries);
                    cmsResourceService.update(null, Wrappers.<CmsResource>lambdaUpdate()
                            .set(CmsResource::getContentId, cmsSeries.getId())
                            .set(CmsResource::getContentCode, cmsSeries.getCode())
                            .set(CmsResource::getContentName, cmsSeries.getName())
                            .set(CmsResource::getContentType, ContentTypeEnum.TELEPLAY.getValue())
                            .set(CmsResource::getContentStatus, MediaAssociationStatusEnum.Relevancy.getValue())
                            .eq(CmsResource::getName, name)
                            .eq(CmsResource::getCpName, cmsSeries.getCpName())
                    );

                    Long storageId = cmsResource.getStorageId();
                    String fileUrl = cmsResource.getFileUrl();
                    SysStorage sysStorage = null;
                    String movUrl = null;
                    if (ObjectUtil.isNotEmpty(storageId)) {
                        sysStorage = storageService.getOne(Wrappers.lambdaQuery(SysStorage.class)
                                .eq(SysStorage::getId, storageId)
                                .eq(SysStorage::getStatus, StorageStatusEnum.ALLOW.getCode())
                                .last("limit 1")
                        );
                        if (sysStorage != null && sysStorage.getMovieHttpPrefix() != null) {
                            Pattern p = compile("\\d+\\.\\d+\\.\\d+\\.\\d+\\:\\d+");
                            Matcher matcher = p.matcher(fileUrl);
                            if (matcher.find()) {
                                //PlayURL只保存相对路径
                                movUrl = FtpUtil.getPath(fileUrl);
                            }
                        }
                    }
                    //同步到cms_movie表中
                    cmsMovieService.update(null, Wrappers.<CmsMovie>lambdaUpdate()
                            .set(CmsMovie::getContentStatus, MediaAssociationStatusEnum.Relevancy.getValue())
                            .set(CmsMovie::getContentType, ContentTypeEnum.TELEPLAY.getValue())
                            .set(CmsMovie::getPlayUrl, movUrl)
                            .set(CmsMovie::getFileUrl, cmsResource.getFileUrl())
                            .set(CmsMovie::getContentId, cmsSeries.getId())
                            .set(CmsMovie::getContentCode, cmsSeries.getCode())
                            .eq(CmsMovie::getName, name)
                            .eq(CmsMovie::getCpName, cmsSeries.getCpName())
                    );
                }
            }
        }
    }

}
