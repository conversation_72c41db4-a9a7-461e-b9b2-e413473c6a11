package com.pukka.iptv.manage.controller.bms;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.pukka.iptv.common.base.constant.PublishParamTypeConstants;
import com.pukka.iptv.common.base.enums.PriorityEnums;
import com.pukka.iptv.common.base.vo.CommonResponse;
import com.pukka.iptv.common.data.model.OutParamExpand;
import com.pukka.iptv.common.data.model.bms.BmsPackage;
import com.pukka.iptv.common.data.vo.req.*;
import com.pukka.iptv.common.log.annotation.SysLog;
import com.pukka.iptv.common.log.enums.OperateObjectEnum;
import com.pukka.iptv.common.log.enums.OperateTypeEnum;
import com.pukka.iptv.manage.service.bms.BmsPackageService;
import com.pukka.iptv.manage.service.bms.BmsPublishParamApi;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.util.Assert;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 产品包
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2021-08-27 10:20:57
 */

@RestController
@RequestMapping(value = "/bmsPackage", produces = MediaType.APPLICATION_JSON_VALUE)
@Api(tags = "bmsPackage管理")
public class BmsPackageController implements BmsPublishParamApi {

    @Autowired
    private BmsPackageService bmsPackageService;


    @SysLog(objectType = OperateObjectEnum.BMS_PACKAGE, operateType = OperateTypeEnum.PACKAGE_SAVE, objectNames = "#bmsPackageSaveReq.name")
    @ApiOperation(value = "产品包新增")
    @PostMapping("/save")
    public CommonResponse<Boolean> save(@Valid @RequestBody BmsPackageSaveReq saveReq) {
        // 校验
        saveReq.validPrice();
        //saveReq.validTime();
        return CommonResponse.success(bmsPackageService.savePackage(saveReq));
    }

    @ApiOperation(value = "产品包查询")
    @GetMapping("/query")
    public CommonResponse<IPage<BmsPackage>> page(Page<BmsPackage> page, BmsPackageQueryReq bmsPackageQueryReq) {
        return CommonResponse.success(bmsPackageService.queryList(page, bmsPackageQueryReq));
    }


    @SysLog(objectType = OperateObjectEnum.BMS_PACKAGE, operateType = OperateTypeEnum.PACKAGE_UPDATE, objectIds = "#updateReq.id", objectNames = "updateReq.name")
    @ApiOperation(value = "产品包修改")
    @PutMapping("/update")
    public CommonResponse<Boolean> updateById(@Valid @RequestBody BmsPackageUpdateReq updateReq) {
        // 校验
        updateReq.validPrice();
        //updateReq.validTime();
        return CommonResponse.success(bmsPackageService.updatePackage(updateReq));
    }

    @ApiOperation(value = "产品包新增(注入新增)")
    @PostMapping("/injectionSave")
    public CommonResponse<Boolean> injectionSave(@RequestBody BmsPackageInjectionReq injectionReq) {
        return CommonResponse.success(bmsPackageService.savePackage(injectionReq));
    }

    @ApiOperation(value = "产品包修改(注入修改)")
    @PutMapping("/injectionUpdate")
    public CommonResponse<Boolean> injectionUpdate(@RequestBody BmsPackageInjectionReq injectionReq) {
        return CommonResponse.success(bmsPackageService.updatePackage(injectionReq));
    }


    @SysLog(objectType = OperateObjectEnum.BMS_PACKAGE, operateType = OperateTypeEnum.PACKAGE_DELETE, objectIds = "#idList")
    @ApiOperation(value = "产品包删除")
    @DeleteMapping("/delete")
    public CommonResponse<Boolean> deleteById(@RequestBody List<Long> idList) {
        Assert.notEmpty(idList, "parameter cannot be null");
        return CommonResponse.success(bmsPackageService.removePackageByIds(idList));
    }


    @SysLog(objectType = OperateObjectEnum.BMS_PACKAGE, operateType = OperateTypeEnum.PACKAGE_LOCK_OR_UNLOCK, objectIds = "#operationReq.idList")
    @ApiOperation(value = "产品包解锁/锁定")
    @PutMapping("/lock")
    public CommonResponse<Boolean> lock(@Valid @RequestBody BmsPackageOperationReq operationReq) {
        // 校验
        operationReq.validBatchLock();
        return CommonResponse.success(bmsPackageService.updateBatchLockByIds(operationReq));
    }


    @SysLog(objectType = OperateObjectEnum.BMS_PACKAGE, operateType = OperateTypeEnum.PACKAGE_PUBLISH, objectIds = "#idList")
    @ApiOperation(value = "产品包发布")
    @PutMapping("/publish")
    public CommonResponse<Boolean> publish(@RequestBody List<Long> idList) {
        Assert.notEmpty(idList, "parameter cannot be null");
        Map<String, OutParamExpand> paramMap = new HashMap<>();
        paramMap.put(PublishParamTypeConstants.PRIORITY, new OutParamExpand().setPriority(PriorityEnums.GENERAL.getValue()));
        return CommonResponse.success(bmsPackageService.publish(idList, paramMap));
    }


    @SysLog(objectType = OperateObjectEnum.BMS_PACKAGE, operateType = OperateTypeEnum.PACKAGE_RECYCLE, objectIds = "#idList")
    @ApiOperation(value = "产品包回收")
    @PutMapping("/recycle")
    public CommonResponse<Boolean> recycle(@RequestBody List<Long> idList) {
        Assert.notEmpty(idList, "parameter cannot be null");
        return CommonResponse.success(bmsPackageService.recycle(idList));
    }


    @SysLog(objectType = OperateObjectEnum.BMS_PACKAGE, operateType = OperateTypeEnum.PACKAGE_RESET_PUBLISH_STATUS, objectIds = "#idList")
    @ApiOperation(value = "产品包重置发布状态")
    @PutMapping("/publishStatusReset")
    public CommonResponse<Boolean> resetPublishStatus(@RequestBody List<Long> idList) {
        Assert.notEmpty(idList, "parameter cannot be null");
        return CommonResponse.success(bmsPackageService.resetPublishStatus(idList));
    }


    @SysLog(objectType = OperateObjectEnum.BMS_PACKAGE, operateType = OperateTypeEnum.PACKAGE_UPDATE_PUBLISH_STATUS, objectIds = "#operationReq.idList")
    @ApiOperation(value = "产品包修改发布状态")
    @PutMapping("/publishStatusUpdate")
    public CommonResponse<Boolean> updatePublishStatus(@Valid @RequestBody BmsPackageOperationReq operationReq) {
        // 校验
        operationReq.validPublish();
        return CommonResponse.success(bmsPackageService.updatePublishStatus(operationReq));
    }

    @SysLog(objectType = OperateObjectEnum.BMS_PACKAGE, operateType = OperateTypeEnum.PACKAGE_ACTIVE_OR_POSITIVE, objectIds = "#operationReq.idList")
    @ApiOperation(value = "产品包生效/失效")
    @PutMapping("/status")
    public CommonResponse<Boolean> status(@Valid @RequestBody BmsPackageOperationReq operationReq) {
        // 校验
        operationReq.validStatus();
        boolean status = bmsPackageService.status(operationReq);
        if (status) {
            Map<String, OutParamExpand> paramMap = new HashMap<>();
            paramMap.put(PublishParamTypeConstants.PRIORITY, new OutParamExpand().setPriority(PriorityEnums.IMMEDIATE.getValue()));
            if (operationReq.getNeedPublish()) {
                return CommonResponse.success(bmsPackageService.publish(operationReq.getIdList(), paramMap));
            }
        }
        return CommonResponse.success(status);
    }

    @ApiOperation(value = "详情")
    @GetMapping("/getById")
    public CommonResponse<BmsPackage> getById(@Valid @RequestParam(name = "id", required = true) Long id) {
        return CommonResponse.success(bmsPackageService.getById(id));
    }

}
