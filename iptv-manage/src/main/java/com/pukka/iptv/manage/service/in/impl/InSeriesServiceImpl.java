package com.pukka.iptv.manage.service.in.impl;


import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.pukka.iptv.common.data.model.in.InSeries;
import com.pukka.iptv.manage.mapper.in.InSeriesMapper;
import com.pukka.iptv.manage.service.in.InSeriesService;
import org.springframework.stereotype.Service;

/**
 *
 * @author: tan
 * @date: 2021-8-24 10:50:13
 */

@Service
public class InSeriesServiceImpl extends ServiceImpl<InSeriesMapper, InSeries> implements InSeriesService {

}


