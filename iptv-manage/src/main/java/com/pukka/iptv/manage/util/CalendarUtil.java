package com.pukka.iptv.manage.util;

import org.springframework.context.annotation.Configuration;

import java.util.Calendar;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

@Configuration
public class CalendarUtil {


    //节目单导入 不带ss的
    public static String duration(String startDate, String startTime, String endTime) {

        //开始时间
        Calendar start = Calendar.getInstance();
        String starty = startDate.substring(0, 4);
        String startM = startDate.substring(4, 6);
        String startd = startDate.substring(6);
        int startH = Integer.parseInt(startTime.substring(0, 2));//时
        int startm = Integer.parseInt(startTime.substring(2, 4));//分

        start.set(Calendar.YEAR, Integer.parseInt(starty));
        start.set(Calendar.MONTH, Integer.parseInt(startM) - 1);
        start.set(Calendar.DATE, Integer.parseInt(startd));

        start.set(Calendar.HOUR_OF_DAY, startH);
        start.set(Calendar.MINUTE, startm);

        //结束时间
        Calendar end = Calendar.getInstance();
        String endy = startDate.substring(0, 4);
        String endM = startDate.substring(4, 6);
        String endd = startDate.substring(6);

        int endH = Integer.parseInt(endTime.substring(0, 2));//时
        int endm = Integer.parseInt(endTime.substring(2, 4));//分

        end.set(Calendar.YEAR, Integer.parseInt(endy));
        end.set(Calendar.MONTH, Integer.parseInt(endM) - 1);
        end.set(Calendar.DATE, Integer.parseInt(endd));

        end.set(Calendar.HOUR_OF_DAY, endH);
        end.set(Calendar.MINUTE, endm);
        if (Integer.parseInt(endTime) < Integer.parseInt(startTime)) {
            end.set(Calendar.DATE, Integer.parseInt(endd) + 1);
        }

        long aTime = end.getTimeInMillis();
        long bTime = start.getTimeInMillis();

        long cTime = aTime - bTime;
        long sTime = cTime / 1000;//时间差，单位：秒
        long mTime = sTime / 60;
        long hTime = mTime / 60;

        String hour = String.valueOf(hTime % 24);
        String minute = String.valueOf(mTime % 60);
        if ((hTime % 24) < 10) {
            hour = 0 + String.valueOf(hTime % 24);
        }

        if ((mTime % 60) < 10) {
            minute = 0 + String.valueOf(mTime % 60);
        }
        return hour + minute + "00";
    }


    //新增 带ss的  结束时间 = 开播时间+ 节目时长
    public static String newDuration(String startTime, String endTime) {
        //开始时间
        int startH = Integer.parseInt(startTime.substring(0, 2));//时
        int startm = Integer.parseInt(startTime.substring(2, 4));//分
        int starts = Integer.parseInt(startTime.substring(4));

        //结束时间
        int durationH = Integer.parseInt(endTime.substring(0, 2));//时
        int durationm = Integer.parseInt(endTime.substring(2, 4));//分
        int durations = Integer.parseInt(endTime.substring(4, 6));

        int start = (startH * 3600000) + (startm * 60000) + (starts * 1000);

        int duration = (durationH * 3600000) + (durationm * 60000) + (durations * 1000);

        int end = 0;
        end = start + duration;

        Integer ends = end / 1000;
        Integer endm = ends / 60;
        Integer endH = endm / 60;

        String H = String.valueOf(endH % 60);
        String m = String.valueOf(endm % 60);
        String s = String.valueOf(ends % 60);

        if ((endH % 60) > 24) {
            H = String.valueOf((endH % 60) - 24);
        }
        if (Integer.parseInt(H)< 10) {
            H = "0" + H;
        }

        if ((endm % 60) < 10) {
            m = "0" + endm % 60;
        }

        if ((ends % 60) < 10) {
            s = "0" + ends % 60;
        }
        return H + m + s;
    }

    /**
     * 判断字符串是否是yyyyMMddHHmmss格式
     * @param mes 字符串
     * @return boolean 是否是日期格式
     */
    public static boolean isRqSjFormat(String mes){
        String format = "([0-9]{4})(0[1-9]|1[012])(0[1-9]|[12][0-9]|3[01])"
                + "([01][0-9]|2[0-3])[0-5][0-9][0-5][0-9]";
        Pattern pattern = Pattern.compile(format);
        Matcher matcher = pattern.matcher(mes);
        if (matcher.matches()) {
            pattern = Pattern.compile("(\\d{4})(\\d{2})(\\d{2}).*");
            matcher = pattern.matcher(mes);
            if (matcher.matches()) {
                int y = Integer.valueOf(matcher.group(1));
                int m = Integer.valueOf(matcher.group(2));
                int d = Integer.valueOf(matcher.group(3));
                if (d > 28) {
                    Calendar c = Calendar.getInstance();
                    c.set(y, m-1, 1);//每个月的最大天数
                    int lastDay = c.getActualMaximum(Calendar.DAY_OF_MONTH);
                    return (lastDay >= d);
                }
            }
            return true;
        }
        return false;

    }



}
