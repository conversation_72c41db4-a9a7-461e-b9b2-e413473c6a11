package com.pukka.iptv.manage.service.condition.rule;

import cn.hutool.core.collection.CollectionUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.pukka.iptv.common.base.enums.OpCheckStatusEnum;
import com.pukka.iptv.common.core.util.SpringUtils;
import com.pukka.iptv.common.core.util.StringUtils;

import com.pukka.iptv.manage.mapper.bms.BmsBaseMapper;
import com.pukka.iptv.manage.service.condition.AbstractRule;
import com.pukka.iptv.manage.service.condition.BaseRule;
import com.pukka.iptv.manage.service.condition.RuleResult;


import java.util.Collection;


/**
 * @author: wz
 * @date: 2021/9/7 18:57
 * @description:
 */
public class OpCheckRule<T> extends AbstractRule<T, Object> {

    protected OpCheckRule(Class<T> clazz) {
        super(clazz);
    }

    public static <T> OpCheckRule<T> init(Class<T> clazz) {
        return new OpCheckRule<>(clazz);
    }

    @Override
    public BaseRule wrapper(QueryWrapper wrapper) {
        return null;
    }

    @Override
    public RuleResult execute() {
        String tableName = getTableName();
        Collection<Long> ids = getData();

        //为空不检查
        if (CollectionUtil.isEmpty(ids)) return RuleResult.ok();
        BmsBaseMapper bean = SpringUtils.getBean(BmsBaseMapper.class);
        String name = bean.getOpCheckStatusCount(tableName, OpCheckStatusEnum.Pass.getValue(), ids);
        if (StringUtils.hasText(name)) {
            return RuleResult.fail(name + " 终审审核未通过 无法操作!", name);
        } else {
            return RuleResult.ok();
        }
    }
}
