package com.pukka.iptv.manage.controller.api;

import com.pukka.iptv.common.api.feign.dualScreen.DualScreenInteractionFeignClient;
import com.pukka.iptv.common.base.vo.CommonResponse;
import com.pukka.iptv.common.core.util.StringUtils;
import com.pukka.iptv.manage.service.api.SPHDService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;

/**
 * 双屏互动controller
 */
@RestController
@RequestMapping(value = "/", produces = MediaType.APPLICATION_JSON_VALUE)
@Api(tags = "双屏互动controller")
public class SPHDController {

    @Autowired
    SPHDService sphdService;
    @Autowired
    private DualScreenInteractionFeignClient dualScreenInteractionFeignClient;

    @ApiOperation(value = "查询对应运营商在ftp上的媒资文件地址")
    @GetMapping("/validMediaFtp")
    public CommonResponse<String> validMedia(@RequestParam String operator) {
        CommonResponse<String> response = dualScreenInteractionFeignClient.getFtpMediaUrlForOperator(operator);
        if (response != null) {
            String ftp = response.getData();
            if (StringUtils.isEmpty(ftp)) {
                return CommonResponse.fail("暂无媒资统计结果");
            } else {
                return CommonResponse.success(ftp);
            }
        }
        return CommonResponse.fail("远程调用结果为空，暂无统计结果");
    }

    @ApiOperation(value = "统计对应运营商有效媒资，定时任务调用")
    @GetMapping("/exeValidMedia")
    public CommonResponse<String> exeValidMedia(@RequestParam String operator) {
        if (!"ctcc".equals(operator) && !"cucc".equals(operator)) {
            return CommonResponse.fail("执行参数有误");
        }
        String ftp = sphdService.getValidMediaUpFtp(operator);
        if (StringUtils.isEmpty(ftp)) {
            return CommonResponse.fail("统计失败");
        } else {
            return CommonResponse.success(ftp);
        }


    }

    /**
     * 查询在给定sp列表中对应包含预约标签的媒资接口
     */
    @ApiOperation(value = "查询在给定sp列表中对应包含预约标签的媒资接口")
    @PostMapping("/validSubscriptionMedia")
    public CommonResponse<Map<String, List<?>>> validSubscriptionMedia(@RequestBody Map<String, Object> params) {
        List<String> spIds = (List<String>) params.get("spIds");
        List<String> codes = (List<String>) params.get("codes");
        Map<String, List<?>> result = sphdService.validSubscriptionMedia(spIds, codes);
        if (result == null) {
            return CommonResponse.fail("系统繁忙,请稍后再试!");
        } else {
            return CommonResponse.success(result);
        }

    }
}
