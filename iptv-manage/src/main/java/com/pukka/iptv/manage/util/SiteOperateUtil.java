package com.pukka.iptv.manage.util;

import cn.hutool.core.bean.BeanUtil;
import com.pukka.iptv.common.core.util.DateUtils;
import com.pukka.iptv.common.data.model.bms.BmsPicture;
import com.pukka.iptv.common.data.vo.bms.BmsPictureVO;

import java.io.*;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;


/**
 * <AUTHOR>
 * @description: 地址工具类
 * @create 2021-09-13 11:53
 */
public class SiteOperateUtil {
    /**
     * 获取url后缀地址
     * @param str
     * @return
     */
    public static String getSingleMatchValue(String str) {
        String values = null;
        if (str == null) {
            return values;
        }

        String[] strs = str.split("://");
        if (strs == null || strs.length < 2) {
            return str;
        }
        switch (strs[0]) {
            case "ftp":
                values = ftpSingleMatchValue(strs);
                break;
            case "http":
                values = httpSingleMatchValue(strs);
                break;
            default:
                break;
        }
        return values;
    }

    /**
     * http地址格式处理
     * @param strs
     * @return
     */
    private static String httpSingleMatchValue(String[] strs) {
        String values = null;
        String address = strs[1].replaceAll("//", "/");
        String result = "";
        Pattern p = Pattern.compile("[0-9.:]{4,25}");
        Matcher m = p.matcher(address);
        if (m.find() && m.toMatchResult().group() != null) {
            result = m.toMatchResult().group().trim();
        } else {
            result = "";
        }
        values = address.split(result)[1];
        values = values.substring(values.indexOf('/') + 1);
        return values;
    }

    /**
     * ftp地址格式处理
     * @param strs
     * @return
     */
    private static String ftpSingleMatchValue(String[] strs) {
        String values = null;
        String address = strs[1].replaceAll("//", "/");
        address = address.substring(address.indexOf(':') + 1, address.length());
        String result = "";
        Pattern p = Pattern.compile("@[0-9.:]{4,25}");
        Matcher m = p.matcher(address);
        if (m.find() && m.toMatchResult().group() != null) {
            result = m.toMatchResult().group().trim();
        } else {
            result = "";
        }
        values = address.split(result)[1];
        values = values.substring(values.indexOf('/') + 1);
        return values;
    }

}
