package com.pukka.iptv.manage.service.in.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.pukka.iptv.common.data.model.in.InSchedule;
import com.pukka.iptv.manage.mapper.in.InScheduleMapper;
import com.pukka.iptv.manage.service.in.InScheduleService;
import org.springframework.stereotype.Service;

/**
 *
 * @author: liuli
 * @date: 2021年8月31日 下午2:39:47
 */

@Service
public class InScheduleServiceImpl extends ServiceImpl<InScheduleMapper, InSchedule> implements InScheduleService {

}


