package com.pukka.iptv.manage.service.cms.impl;

import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.pukka.iptv.common.base.enums.ContentStatusEnum;
import com.pukka.iptv.common.core.util.DateUtils;
import com.pukka.iptv.common.core.util.ResponseUtil;
import com.pukka.iptv.common.data.dto.PlayerReportDto;
import com.pukka.iptv.common.data.model.cms.CmsMovie;
import com.pukka.iptv.common.data.model.cms.CmsPlayerPicture;
import com.pukka.iptv.common.data.model.cms.CmsPlayerSlice;
import com.pukka.iptv.common.data.vo.resp.PlayerReportResponse;
import com.pukka.iptv.manage.mapper.cms.CmsPlayerSliceMapper;
import com.pukka.iptv.manage.service.cms.CmsMovieService;
import com.pukka.iptv.manage.service.cms.CmsPlayerPictureService;
import com.pukka.iptv.manage.service.cms.CmsPlayerSliceService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.net.URL;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Objects;

/**
 * 视频审核标记
 *
 * @author: zhoul
 * @date: 2021-11-3 14:33:05
 */

@Service
@Slf4j
public class CmsPlayerSliceServiceImpl extends ServiceImpl<CmsPlayerSliceMapper, CmsPlayerSlice> implements CmsPlayerSliceService {

    @Autowired
    private CmsMovieService movieService;

    @Autowired
    private CmsPlayerPictureService playerPictureService;

    /**
     * 视频审核标记分页查询
     *
     * @return
     */
    @Override
    public Page<CmsPlayerSlice> listByCmsContentIdContentType(Page page, CmsMovie cmsMovie) {
        cmsMovie.valid();
        CmsMovie movie = movieService.getOne(Wrappers.lambdaQuery(CmsMovie.class)
                .eq(Objects.nonNull(cmsMovie.getContentId()), CmsMovie::getContentId, cmsMovie.getContentId())
                .eq(Objects.nonNull(cmsMovie.getContentType()), CmsMovie::getContentType, cmsMovie.getContentType())
                .eq(Objects.nonNull(cmsMovie.getType()), CmsMovie::getType, cmsMovie.getType())
                .eq(Objects.nonNull(cmsMovie.getCpId()), CmsMovie::getCpId, cmsMovie.getCpId())
                .last("limit 1")
        );
        Page pageData = new Page();
        if (movie == null) {
            pageData.setSize(0L).setCurrent(1L).setTotal(0L);
            return pageData;
        }
        page.setSize(page.getSize() == -1 ? Integer.MAX_VALUE : page.getSize());
        pageData = this.page(page, Wrappers.lambdaQuery(CmsPlayerSlice.class)
                .eq(Objects.nonNull(movie.getId()), CmsPlayerSlice::getMovieId, movie.getId())
                .eq(Objects.nonNull(cmsMovie.getType()), CmsPlayerSlice::getMovieType, cmsMovie.getType())
                .orderByDesc(CmsPlayerSlice::getId)
        );
        List<CmsPlayerSlice> cmsPlayerSlices = pageData.getRecords();
        cmsPlayerSlices.stream().forEach(cmsPlayerSlice -> {
            if(ObjectUtil.isNotEmpty(cmsPlayerSlice.getSliceUrl())){
                cmsPlayerSlice.setSliceUrl(cmsPlayerSlice.getSliceUrl().concat("?cpid=" + cmsPlayerSlice.getCpId() + "&moviecode=" + cmsPlayerSlice.getMovieCode()));
            }
        });
        return pageData;
    }

    @Override
    public void playerReport(HttpServletResponse httpServletResponse, PlayerReportDto params) {
        PlayerReportResponse playerResponse = new PlayerReportResponse();
        String code = "0";
        String message = null;
        log.info("播放器上报params = {} ", params);

        try {
            /* Slices 和 图片 Pic 同时为空 直接结束 */
            if (!params.isAvailable()) {
                message = "切片Slices 和 图片 Pic 同时为空。";
                log.error(message);
                /* 只是作为程序中断，实际return在 finally 中 */
                return;
            }

            /* 验证Movie 是不是存在 */
            CmsMovie movie = movieService.getOne(Wrappers.lambdaQuery(CmsMovie.class)
                    .eq(CmsMovie::getCode, params.getMoviecode())
                    .eq(CmsMovie::getStatus, ContentStatusEnum.EFFECTIVE.getCode()));
            if (movie == null) {
                message = "Code 为 " + params.getMoviecode() + " 的 Movie 不存在";
                log.error(message);
                /* 只是作为程序中断，实际return在finally中 */
                return;
            }

            /* 数据入库 */
            List<PlayerReportDto.Slice> slices = params.getSlices();
            boolean sliceResult = parsePlayerSlices(slices, movie, params.getFile());

            List<PlayerReportDto.Pic> pic = params.getPic();
            boolean pictureResult = parsePlayerPicture(pic, movie, params.getFile());

            /* 当切片和截图都失败的时候 */
            if (!(sliceResult && pictureResult)) {
                message = "播放器上报 保存数据库 失败";
                log.error(message);
                /* 只是作为程序中断，实际return在finally中 */
                return;
            }
            code = "1";
            message = "success";
        } catch (Exception e) {
            message = "切片Slices 和 图片 Pic 同时为空。";
            log.error("播放器上报接口错误 ： {} ", e);
        } finally {
            playerResponse.setCode(code);
            playerResponse.setMessage(message);
            log.info("上报流程结束 result = {}", playerResponse);
            try {
                ResponseUtil.out(httpServletResponse, playerResponse);
            } catch (IOException e) {
                log.error("上报结果返回异常：{}", e.fillInStackTrace());
                e.printStackTrace();
            }
        }
    }

    private  String getPicTimestamp(String urlStr){
        String result = "";
        try {
            URL url = new URL(urlStr);
            result = url.getPath();
            if(result.lastIndexOf("_") >= 0 && result.lastIndexOf(".") >= 0 && result.lastIndexOf(".") > result.lastIndexOf("_")){
                return result.substring(result.lastIndexOf("_")+1,result.lastIndexOf("."));
            }
        } catch (Exception e) {
            e.printStackTrace();
            log.error("getPicTimestamp Error url:"+urlStr,e.fillInStackTrace());
        }
        return result;
    }

    private boolean parsePlayerPicture(List<PlayerReportDto.Pic> pics, CmsMovie movie, String file) {
        if (pics.isEmpty()) {
            log.info("此次播放器上报无 截图");
            return true;
        }
        ArrayList<CmsPlayerPicture> cmsPlayerPictures = new ArrayList<>();
        pics.forEach(pic -> {
            CmsPlayerPicture playerPicture = new CmsPlayerPicture();
            playerPicture.setCpId(movie.getCpId());
            playerPicture.setCpName(movie.getCpName());
            playerPicture.setMovieCode(movie.getCode());
            playerPicture.setMovieId(movie.getId());
            playerPicture.setMovieType(movie.getType());
            playerPicture.setPlayUrl(file);
            playerPicture.setPictureCode(pic.getCode());
            playerPicture.setPictureUrl(pic.getUrl());
            playerPicture.setPictureStorageId(movie.getStorageId());
            playerPicture.setPictureTimestamp(getPicTimestamp(pic.getUrl()));
            cmsPlayerPictures.add(playerPicture);
        });
        boolean result = playerPictureService.saveBatch(cmsPlayerPictures);
        log.info("截图上报 插入数据库 成功");
        return result;
    }

    private boolean parsePlayerSlices(List<PlayerReportDto.Slice> slices, CmsMovie movie, String sliceUrl) {
        if (slices.isEmpty()) {
            log.info("此次播放器上报无切片");
            return true;
        }
        ArrayList<CmsPlayerSlice> cmsPlayerSlices = new ArrayList<>();
        slices.forEach(slice -> {
            CmsPlayerSlice playerSlice = new CmsPlayerSlice();
            playerSlice.setCpId(movie.getCpId());
            playerSlice.setCpName(movie.getCpName());
            playerSlice.setMovieCode(movie.getCode());
            playerSlice.setMovieId(movie.getId());
            playerSlice.setMovieType(movie.getType());
            playerSlice.setSliceStorageId(movie.getStorageId());
            playerSlice.setSliceUrl(sliceUrl);
            playerSlice.setSliceBeginTime(slice.getBegin());
            playerSlice.setSliceEndTime(slice.getEnd());
            playerSlice.setSliceConstantTime(getSliceConstantTime(slice.getBegin(), slice.getEnd()));
            cmsPlayerSlices.add(playerSlice);
        });
        boolean result = this.saveBatch(cmsPlayerSlices);
        log.info("切片上报 插入数据库 成功");
        return result;
    }

    private String getSliceConstantTime(String beginStr, String endStr) {
        String result = "";
        try {
            String[] beginTime = beginStr.split(":");
            String[] endTime = endStr.split(":");
            Float beginMillSeconds = Long.parseLong(beginTime[0]) * 60 * 60 * 1000
                    + Long.parseLong(beginTime[1]) * 60 * 1000
                    + Float.parseFloat(beginTime[2]) * 1000;
            Float endMillSeconds = Long.parseLong(endTime[0]) * 60 * 60 * 1000
                    + Long.parseLong(endTime[1]) * 60 * 1000
                    + Float.parseFloat(endTime[2]) * 1000;
            Float constantMillSeconds = endMillSeconds - beginMillSeconds;
            Date date = new Date(constantMillSeconds.longValue());
            return DateUtils.format(date, "HH:mm:ss.SSS");
        } catch (Exception e) {
            e.printStackTrace();
            log.error("getSliceConstantTime Error begin:" + beginStr + " end:" + endStr, e.fillInStackTrace());
        }
        return result;
    }

}


