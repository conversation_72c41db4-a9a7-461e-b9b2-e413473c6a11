package com.pukka.iptv.manage.service.cms.impl;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.pukka.iptv.common.base.constant.ObjectsTypeConstants;
import com.pukka.iptv.common.base.enums.*;
import com.pukka.iptv.common.base.exception.BizException;
import com.pukka.iptv.common.base.exception.CommonResponseException;
import com.pukka.iptv.common.base.vo.CommonResponse;
import com.pukka.iptv.common.core.util.MD5;
import com.pukka.iptv.common.core.util.SafeUtil;
import com.pukka.iptv.common.core.util.StringUtils;
import com.pukka.iptv.common.data.dto.CmsResourceDto;
import com.pukka.iptv.common.data.model.bms.BmsContent;
import com.pukka.iptv.common.data.model.bms.BmsProgram;
import com.pukka.iptv.common.data.model.cms.*;
import com.pukka.iptv.common.data.model.order.SubOrderMappingsEntity;
import com.pukka.iptv.common.data.model.sys.SysCp;
import com.pukka.iptv.common.data.model.sys.SysStorage;
import com.pukka.iptv.common.data.model.sys.SysStorageDirctory;
import com.pukka.iptv.common.data.vo.cms.CmsResourceVo;
import com.pukka.iptv.common.data.vo.req.BlobUploadReq;
import com.pukka.iptv.common.data.vo.resp.ResultBlob;
import com.pukka.iptv.common.rabbitmq.config.DelStatisticsStorageMQConfig;
import com.pukka.iptv.common.rabbitmq.config.StatisticsStorageMQConfig;
import com.pukka.iptv.manage.mapper.cms.CmsResourceMapper;
import com.pukka.iptv.manage.service.bms.BmsContentService;
import com.pukka.iptv.manage.service.bms.BmsProgramService;
import com.pukka.iptv.manage.service.cms.*;
import com.pukka.iptv.manage.service.common.FtpAnalysisUtil;
import com.pukka.iptv.manage.service.common.FtpUtil;
import com.pukka.iptv.manage.service.condition.RuleCondition;
import com.pukka.iptv.manage.service.condition.RuleResult;
import com.pukka.iptv.manage.service.condition.rule.PublishStatusRule;
import com.pukka.iptv.manage.service.condition.rule.enums.PublishCheck;
import com.pukka.iptv.manage.service.sys.SysCpService;
import com.pukka.iptv.manage.service.sys.SysStorageDirctoryService;
import com.pukka.iptv.manage.service.sys.SysStorageService;
import com.pukka.iptv.manage.util.CommonUtils;
import io.jsonwebtoken.lang.Collections;
import lombok.extern.slf4j.Slf4j;
import org.springframework.amqp.rabbit.core.RabbitTemplate;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.io.UnsupportedEncodingException;
import java.net.URLDecoder;
import java.util.*;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

import static java.util.regex.Pattern.compile;

/**
 * @author: zhengcl
 * @date: 2021-9-15 15:13:01
 */

@Service
@Slf4j
public class CmsResourceServiceImpl extends ServiceImpl<CmsResourceMapper, CmsResource> implements CmsResourceService {


    @Autowired
    private CmsResourceMapper cmsResourceMapper;

    @Autowired
    private CmsMovieService cmsMovieService;

    @Lazy
    @Autowired
    private CmsProgramService programService;

    @Lazy
    @Autowired
    private CmsSeriesService seriesService;

    @Autowired
    private SysStorageService storageService;

    @Autowired
    private SysCpService cpService;

    @Autowired
    private SysStorageDirctoryService dirctoryService;

    @Autowired
    private BmsContentService contentService;

    @Autowired
    private BmsProgramService bmsProgramService;

    @Autowired
    private BmsContentService bmsContentService;

    @Autowired
    private CmsPlayerPictureService cmsPlayerPictureService;

    @Autowired
    private CmsPlayerSliceService cmsPlayerSliceService;

    @Autowired
    private RabbitTemplate rabbitTemplate;


    @Transactional(rollbackFor = Exception.class)
    @Override
    public boolean blobUpload(CmsResource cmsResource) {
        /*
        if (cmsResource == null || cmsResource.getCpId() == null || cmsResource.getFileUrl() == null) {
            throw new CommonResponseException(CommonResponseEnum.EMPTY.code);
        }
         */
        log.info("注入视频下载上报参数 CmsResource = {}", cmsResource);
        /** 验证参数 */
        cmsResource.validBlobUpload();
        SysCp cp = cpService.getOne(Wrappers.lambdaQuery(SysCp.class)
                .eq(SysCp::getId, cmsResource.getCpId())
                .ne(SysCp::getStatus, StatusEnum.DELETE.getCode()));
        if (cp == null) {
            log.error("CP ID={} 账号不存在或被禁用", cmsResource.getCpId());
            throw new CommonResponseException(CommonResponseEnum.ACCOUNT_NOT_EXIST, "CP账号不存在或被禁用");
        }
        /*SysStorageDirctory storageDirctory = dirctoryService.getOne(Wrappers.lambdaQuery(SysStorageDirctory.class)
                .eq(SysStorageDirctory::getStorageId, cmsResource.getStorageId()).last("LIMIT 1"));*/
        String mediainfo = String.join("|",
                SafeUtil.getString(cmsResource.getVideoBitrate()),
                SafeUtil.getString(cmsResource.getFileSize()),
                SafeUtil.getString(cmsResource.getDuration()),
                SafeUtil.getString(cmsResource.getWidth()),
                SafeUtil.getString(cmsResource.getHeight()),
                SafeUtil.getString(cmsResource.getFrameRate()),
                SafeUtil.getString(ScreenFomatEnum.getByCode(cmsResource.getScreenFormat()).getDesc()));
        log.info("mediainfo = {} ", mediainfo);
        cmsResource.setMediainfo(mediainfo);
//        FtpUtil.getPath()
        /** 处理节目和视频解绑信息，并保存或更新视频  */
        dealBlob(cmsResource);
        log.info(" 注入视频上报结束");
        sendMq(cmsResource.getCpId(),cmsResource.getFileSize());
        return true;
    }

    /**
     * 解析mediaSpec字段，将值分别set到不同字段中去，方便前端展示
     *
     * @param resource
     * @return
     */
    private CmsResource resolvMediaSpec(CmsResource resource) {
        /** Envelope-BitrateType-VideoCodec-VideoBitrate-Resolution-FrameRate-AudioCodec-AudioBitrate */
        /*String mediaSpec = resource.getMediaSpec();
        String[] mediaSpecArray = mediaSpec.split("-");
        resource.setEnvelope(mediaSpecArray[0]);
        resource.setBitrateType(mediaSpecArray[1]);
        resource.setVideoCodec(mediaSpecArray[2]);
        resource.setVideoBitrate(mediaSpecArray[3]);
        resource.setResolution(mediaSpecArray[4]);

        if (StringUtils.isNotEmpty(mediaSpecArray[5])) {
            try {
                resource.setFrameRate(Float.valueOf(mediaSpecArray[5]));
            } catch (NumberFormatException e) {
                *//** mediaSpec中FrameRate不符合规范 *//*
                log.error("mediaSpec = {} 中FrameRate不符合规范 : {} ", mediaSpec, e);
                new CommonResponseException(CommonResponseEnum.DATA_FORMAT_EXCEPTION, "MediaSpec中FrameRate不符合规范 ");
            }
        }

        resource.setAudioCodec(mediaSpecArray[6]);
        *//** 当最后一位为空时，split函数无法识别，比如 b-a--v- 会被识别为四个字符串组成的数组 *//*
        if (mediaSpecArray.length >= 7) {
            resource.setAudioBitrate(mediaSpecArray[7]);
        }*/
        return resource;
    }


    /*****************视频下载上报公共处理方法，客户端上传和注入下载公用  ************/

    /**
     * 处理节目和视频解绑信息，并保存或更新视频
     */
    private void dealBlob(CmsResource resource) {
        CmsResource oldResource = getOne(Wrappers.lambdaQuery(CmsResource.class)
                .eq(CmsResource::getCode, resource.getCode()).last("LIMIT 1"));
        resource.setContentStatus(MovieStatusEnum.NotRelevancy.getValue());
        resource.setStatus(ContentStatusEnum.EFFECTIVE.getCode());
        if (oldResource != null) {
            log.info("code = {} 的resource 已存在， 更新Resource、Movie信息");
            resource.setId(oldResource.getId());
            /**解绑内容信息， 更改状态为待绑定 */
            resource.setContentStatus(MovieStatusEnum.NotRelevancy.getValue());
            /*resource.setStorageId(storageDirctory.getStorageId());
            resource.setStorageName(storageDirctory.getStorageName());*/
            updateById(resource);
            /** 生成成片 */
            CmsMovie movie = syncToMovie(resource);
            /** 更新成片信息 */
            cmsMovieService.update(movie, Wrappers.lambdaUpdate(CmsMovie.class)
                    .eq(CmsMovie::getCode, movie.getCode()));
            if (MovieStatusEnum.NotRelevancy.getValue().equals(oldResource.getContentStatus())) {
                // 认为旧视频还未绑定节目，这里就不需要操作节目了
                return;
            }
            if (isValided(oldResource)) {
                ContentTypeEnum contentTypeEnum = ContentTypeEnum.getByValue(oldResource.getContentType());
                /** 否则解绑resource和program关联信息 */
                resetCpResourceAndProgram(resource, contentTypeEnum);
                restSpContent(contentTypeEnum, oldResource);
            }
            return;
        }
        /** 新增 Resource */
        log.info("新增Resource、Movie信息");
        save(resource);
        CmsMovie movie = syncToMovie(resource);
        cmsMovieService.save(movie);
    }


    private boolean isValided(CmsResource oldResource) {
        try {
            oldResource.validReleatedResource();
        } catch (Exception e) {
            log.error("旧视频关联内容参数校验不通过", e);
            return false;
        }
        return true;
    }

    /**
     * 解除Program、Series、Resource、Movie的绑定信息，等待定时任务RelateProgramXxlJob重新绑定
     *
     * @param resource
     * @param contentType
     */
    private void resetCpResourceAndProgram(CmsResource resource, ContentTypeEnum contentType) {
        log.info("解绑Resource Code = {}, name = {} 的Content 信息，同步修改Movie的content信息", resource.getCode(), resource.getName());
        /** 视频解绑内容 */
        update(Wrappers.lambdaUpdate(CmsResource.class)
                .set(CmsResource::getContentType, null)
                .set(CmsResource::getContentId, null)
                .set(CmsResource::getContentCode, null)
                .set(CmsResource::getContentName, null)
                .eq(CmsResource::getId, resource.getId()));
        cmsMovieService.update(Wrappers.lambdaUpdate(CmsMovie.class)
                .set(CmsMovie::getContentType, null)
                .set(CmsMovie::getContentId, null)
                .set(CmsMovie::getContentCode, null)
                .eq(CmsMovie::getResourceId, resource.getId()));

        /** 内容解绑视频 */

        switch (contentType) {
            /** 单集子集 */
            case FILM:
            case SUBSET:
                if (MovieTypeEnums.RELEASE.getCode().equals(resource.getType())) {
                    log.info("修改关联 正片 Resource code = {} 的Program绑定视频信息");
                    //正片
                    programService.update(Wrappers.lambdaUpdate(CmsProgram.class)
                            .set(CmsProgram::getReleaseStatus, MovieStatusEnum.NotRelevancy.getValue())
                            .eq(CmsProgram::getResourceReleaseCode, resource.getCode())
                    );
                } else {
                    log.info("修改关联 预览片 Resource code = {} 的Program绑定视频信息");
                    // 预览片
                    programService.update(Wrappers.lambdaUpdate(CmsProgram.class)
                            .set(CmsProgram::getPreviewStatus, MovieStatusEnum.NotRelevancy.getValue())
                            .eq(CmsProgram::getResourcePreviewCode, resource.getCode())
                    );
                }
                break;
            /** series */
            case TELEPLAY:
            case EPISODES:
                log.info("修改关联 预览片 Resource code = {} 的Series绑定视频信息");
                // 只有预览片
                seriesService.update(Wrappers.lambdaUpdate(CmsSeries.class)
                        .set(CmsSeries::getPreviewStatus, MovieStatusEnum.NotRelevancy.getValue())
                        .eq(CmsSeries::getResourcePreviewCode, resource.getCode())
                );
                break;
            default:
                log.error("不存在的ContentType = {} 类型", contentType);
        }

    }


    /**
     * SP侧关联状态更改
     */
    private void restSpContent(ContentTypeEnum contentType, CmsResource oldResource) {
        log.info("重置SP侧数据 关联状态");
        switch (contentType) {
            /** bms_program */
            case SUBSET:
                // 子集
                bmsProgramService.update(Wrappers.lambdaUpdate(BmsProgram.class)
                        .set(BmsProgram::getReleaseStatus, MovieStatusEnum.NotRelevancy.getValue())
                        .eq(BmsProgram::getCpId, oldResource.getCpId())
                        .eq(BmsProgram::getCmsContentId, oldResource.getContentId())
                );
                break;
            /** content */
            case FILM:
            case TELEPLAY:
            case EPISODES:
                log.info("修改关联 预览片 Resource code = {} 的Series绑定视频信息");
                // Series只有预览片
                if (MovieTypeEnums.RELEASE.getCode().equals(oldResource.getType())) {
                    log.info("修改关联 正片 Resource code = {} 的Program绑定视频信息");
                    //正片
                    contentService.update(Wrappers.lambdaUpdate(BmsContent.class)
                            .set(BmsContent::getReleaseStatus, MovieStatusEnum.NotRelevancy.getValue())
                            .eq(BmsContent::getContentType, contentType.getValue())
                            .eq(BmsContent::getCpId, oldResource.getCpId())
                            .eq(BmsContent::getCmsContentId, oldResource.getContentId())
                    );

                } else {
                    log.info("修改关联 预览片 Resource code = {} 的Program绑定视频信息");
                    // 预览片
                    contentService.update(Wrappers.lambdaUpdate(BmsContent.class)
                            .set(BmsContent::getPreviewStatus, MovieStatusEnum.NotRelevancy.getValue())
                            .eq(BmsContent::getContentType, contentType.getValue())
                            .eq(BmsContent::getCpId, oldResource.getCpId())
                            .eq(BmsContent::getCmsContentId, oldResource.getContentId())
                    );
                }
                break;
            default:
                log.error("不存在的ContentType = {} 类型", contentType);
        }
    }

    private CmsMovie syncToMovie(CmsResource resource) {
        /** 不转码直接同步到movie */
        //保存到cmsMovie
        CmsMovie movie = new CmsMovie();
        BeanUtils.copyProperties(resource, movie);
        movie.setId(null);
        movie.setResourceId(resource.getId());
        movie.setResourceCode(resource.getCode());
        movie.setPlayUrl(FtpUtil.getPath(resource.getFileUrl()));
        return movie;
    }


    /***********************            END     ****************************************/

    /************************************旧组件上传视频上报规范接口********************************************/
    /**
     * 客户端上传
     *
     * @param req
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public ResultBlob clientBlobUpload(BlobUploadReq req) {
        log.info("客户端视频下载上传上报参数 Params = {} ", req);
        ResultBlob result = new ResultBlob();
        result.setResultcode(-1);
        try {
            SysCp cp = cpService.getOne(Wrappers.lambdaQuery(SysCp.class)
                    .eq(SysCp::getId, req.getCpid())
                    .ne(SysCp::getStatus, StatusEnum.DELETE.getCode()));
            SysStorageDirctory storageDirctory = validCPAndStorage(req, cp);

            /** MD5校验，此版本不需要 */

            /** 解析resource */
            CmsResource resource = resolvBlobUploadReqToResource(req, cp);
            resource.setStorageName(storageDirctory.getStorageName());
            resource.setStorageId(storageDirctory.getStorageId());
            dealBlob(resource);
            result.setResultcode(0);
            log.info("客户端视频上报结束");
            sendMq(resource.getCpId(),resource.getFileSize());
        } catch (CommonResponseException e) {
            result.setResultdesc(e.getMessage());
        } catch (Exception e) {
            log.error("客户端视频上报错误 {} ", e);
            result.setResultdesc("上报错误");
        }
        return result;
    }

    /**
     * 注入发送mq
     */
    private void sendMq(Long cpId,Long fileSize){
        Map<String, Long> map = new HashMap<>();
        map.put(cpId.toString(),fileSize);
        rabbitTemplate.convertAndSend(StatisticsStorageMQConfig.STATISTIC_STORAGE_EXCHANGE, StatisticsStorageMQConfig.STATISTIC_STORAGE_ROUTING, map);
    }

    /**
     * 删除介质发送mq
     */
    private void sendDelMq(Long cpId,Long fileSize){
        Map<String, Long> map = new HashMap<>();
        map.put(cpId.toString(),fileSize);
        rabbitTemplate.convertAndSend(DelStatisticsStorageMQConfig.DEL_STATISTIC_STORAGE_EXCHANGE, DelStatisticsStorageMQConfig.DEL_STATISTIC_STORAGE_ROUTING, map);
    }

    /**
     * 校验CP和存储账号是否对应，防止在上报过程中，CP更换存储配置
     *
     * @param req
     * @param cp
     * @return
     */
    private SysStorageDirctory validCPAndStorage(BlobUploadReq req, SysCp cp) {
        if (cp == null) {
            log.error("CP ID={} 账号不存在或被禁用", req.getCpid());
            throw new CommonResponseException(CommonResponseEnum.ACCOUNT_NOT_EXIST, "CP账号不存在或被禁用");
        }
        // 查找有效存储信息
        SysStorageDirctory storageDirctory = dirctoryService.getOne(Wrappers.lambdaQuery(SysStorageDirctory.class)
                .eq(SysStorageDirctory::getStorageId, cp.getStorageId())
                .eq(SysStorageDirctory::getStatus, StatusEnum.COME.getCode())
                .eq(SysStorageDirctory::getType, StorageDirctoryTypeEnum.COMPLETED_FILM.getCode())
                .eq(SysStorageDirctory::getAuthorityType, AuthorityTypeEnums.READ_WRITE.getCode())
                .last("LIMIT 1"));
        if (storageDirctory == null) {
            log.error("该CP ID={} 存储账号不存在或被禁用", req.getCpid());
            throw new CommonResponseException(CommonResponseEnum.ACCOUNT_NOT_EXIST, "CP存储账号不存在或被禁用");
        }
        /** 验证账号是不是对的上 */
        /*if (!storageDirctory.getAccount().equals(req.getBlob())) {
            throw new CommonResponseException(CommonResponseEnum.ACCOUNT_NOT_EXIST, "CP存储账号不存在");
        }*/
        return storageDirctory;
    }

    private CmsResource resolvBlobUploadReqToResource(BlobUploadReq req, SysCp cp) throws UnsupportedEncodingException {
        CmsResource resource = new CmsResource();
        String mediainfo = req.getMediainfo();
        resource = resolvMediaInfo(resource, mediainfo);

        /** 设置基础信息 */
        resource.setCpId(cp.getId());
        resource.setCpName(cp.getName());
        String name = URLDecoder.decode(req.getFilename(), "UTF-8");
        if (StringUtils.isEmpty(req.getCode())) {
            String codeMessage = "[" + cp.getId() + "][" + name + "]";
            // 兼容客户端上传无code的情况
            resource.setCode(MD5.encrypt(codeMessage));
        } else {
            resource.setCode(req.getCode());
        }
        resource.setName(name);
        resource.setWidth(SafeUtil.getInteger(req.getWidth()));
        resource.setHeight(SafeUtil.getInteger(req.getHeight()));
        resource.setFrameRate(SafeUtil.getFloat(req.getFramerate()));
        resource.setVideoBitrate(req.getBitrate());
        resource.setFileUrl(req.getFileurl());
        resource.setFileSize(SafeUtil.getLong(req.getLength(), null));
        resource.setMd5(req.getMd5());
        resource.setSource(SourceEnum.MANUALWORK.getValue());
        resource.setVid(req.getVid());
        resource.setFileid(req.getFileid());
        resource.setContentStatus(MovieStatusEnum.NotRelevancy.getValue());
        resource.setType(MovieTypeEnums.RELEASE.getCode());

        return resource;
    }

    private CmsResource resolvMediaInfo(CmsResource resource, String mediainfo) {
        if (mediainfo.equals("||||||")) {
            return resource;
        }
        String[] infoArray = mediainfo.split("\\|");

        /** 解析时长 */
        String timestamp = infoArray[2];
        long duration = 0;
        /** 时间戳 HH:mm:ss */
        if (timestamp.contains(":")) {
            String[] times = timestamp.split(":");
            duration = Long.valueOf(times[0]) * 60 * 60 + Long.valueOf(times[1]) * 60 + (long) Math.ceil(Float.valueOf(times[2]));
        } else {
            /** 秒 */
            duration = Long.valueOf(timestamp);
        }
        resource.setDuration(duration);

        /** 解析分辨率 */
        resource.setResolution(infoArray[3]);

        resource.setMediainfo(mediainfo);
        return resource;
    }


    /***************************************************************************************/


    /**
     * 源片分页查询
     *
     * @param page
     * @param cmsResource
     * @return
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Page<CmsResource> pageList(Page page, CmsResource cmsResource) {
/*        String name = null;
        if (ObjectUtil.isNotEmpty(cmsResource.getName())) {
            name = cmsResource.getName().trim();
        }
        String[] names = CmsSeriesCheckServiceImpl.DeCoder(name);*/
        Page iPage = getPage(page);
        LambdaQueryWrapper<CmsResource> wrapper = new LambdaQueryWrapper<>();
/*        if (ObjectUtil.isNotEmpty(name)&& StringUtils.isEmpty(names)) {
            wrapper.like(CmsResource::getName, CmsSeriesCheckServiceImpl.Coder(name));
        }

        if (ObjectUtil.isNotEmpty(names)) {
            wrapper.in(CmsResource::getName, names);
        }*/
        if (ObjectUtil.isNotEmpty(cmsResource.getCpId())) {
            wrapper.eq(CmsResource::getCpId, cmsResource.getCpId());
        }
        if (ObjectUtil.isNotEmpty(cmsResource.getContentStatus())) {
            wrapper.eq(CmsResource::getContentStatus, cmsResource.getContentStatus());
        }
        if (ObjectUtil.isNotEmpty(cmsResource.getStatus())) {
            wrapper.eq(CmsResource::getStatus, cmsResource.getStatus());
        }
        if (ObjectUtil.isNotEmpty(cmsResource.getType())) {
            wrapper.eq(CmsResource::getType, cmsResource.getType());
        }
        /**
         * 增加contentCode
         */
        if (ObjectUtil.isNotEmpty(cmsResource.getContentCode())) {
            wrapper.eq(CmsResource::getContentCode, cmsResource.getContentCode());
        }
//        if(ObjectUtil.isNotEmpty(cmsResource.getContentName())){
//            wrapper.eq(CmsResource::getContentName, cmsResource.getContentName());
//        }
        //未换行，模糊查询,换行后精确查询
        if (cmsResource.getName() != null) {
            if (cmsResource.getName().contains("\n")) {
                //防止name为空字符串以及只有回车的情况  进行再一次校验
                cmsResource.setName(cmsResource.getName().trim());
                if (StringUtils.isNotEmpty(cmsResource.getName())) {
                    String[] names = CommonUtils.getNames(cmsResource.getName());
                    if (names.length > 0) {
                        wrapper.in(CmsResource::getName, Arrays.asList(names));
                    }
                }
            } else {
                String[] names = CommonUtils.getNames(cmsResource.getName());
                if (names != null && names.length > 0) {
                    wrapper.like(CmsResource::getName, names[0]);
                }
            }
        }

        if (cmsResource.getContentName() != null) {
            if (cmsResource.getContentName().contains("\n")) {
                //防止name为空字符串以及只有回车的情况  进行再一次校验
                cmsResource.setContentName(cmsResource.getContentName().trim());
                if (StringUtils.isNotEmpty(cmsResource.getContentName())) {
                    String[] names = CommonUtils.getNames(cmsResource.getContentName());
                    if (names.length > 0) {
                        wrapper.in(CmsResource::getContentName, Arrays.asList(names));
                    }
                }
            } else {
                String[] names = CommonUtils.getNames(cmsResource.getContentName());
                if (names != null && names.length > 0) {
                    wrapper.like(CmsResource::getContentName, names[0]);
                }
            }
        }


        wrapper.orderByDesc(CmsResource::getId);
        iPage = cmsResourceMapper.selectPage(iPage, wrapper);
        List<CmsResource> cmsResources = iPage.getRecords();
        List<CmsResourceVo> cmsResourceVos = new ArrayList<>();
        cmsResources.stream().forEach(cmsResource1 -> {
            set(cmsResourceVos, cmsResource1);
        });
        iPage.setRecords(cmsResourceVos);
        return iPage;
    }

    public final static List<Integer> PU_LIST =
            Arrays.asList(PublishStatusEnum.PUBLISH.getCode(),
                    PublishStatusEnum.FAILUPDATE.getCode(),
                    PublishStatusEnum.WAITUPDATE.getCode(),
                    PublishStatusEnum.UPDATING.getCode(),
                    PublishStatusEnum.FAILROLLBACK.getCode(),
                    PublishStatusEnum.PUBLISHING.getCode(),
                    PublishStatusEnum.ROLLBACKING.getCode());

    /**
     * 关联分页查询检查
     *
     * @param contentId 媒资ID
     * @param type      1 单集 2子集 3剧集
     * @return
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean check(Long contentId, Integer type, Integer saveOrUpStatus) {

        //是否发布状态检查
        if (saveOrUpStatus == 2) {
            switch (type) {
                //单集的发布状态
                case 1:
                    LambdaQueryWrapper<BmsContent> bmsContentLambdaQueryWrapper = new LambdaQueryWrapper<>();
                    bmsContentLambdaQueryWrapper.eq(BmsContent::getCmsContentId, contentId)
                            .eq(BmsContent::getContentType, ContentTypeEnum.FILM.getValue())
                            .in(BmsContent::getPublishStatus, PU_LIST);

                    List<BmsContent> bmsContents = bmsContentService.list(bmsContentLambdaQueryWrapper);
                    if (ObjectUtil.isNotEmpty(bmsContents)) {
                        throw new CommonResponseException("请检查媒资的发布状态");
                    }
                    break;
                //子集的发布状态
                case 2:
                    LambdaQueryWrapper<BmsProgram> bmsProgramLambdaQueryWrapper = new LambdaQueryWrapper<>();

                    bmsProgramLambdaQueryWrapper.eq(BmsProgram::getCmsContentId, contentId)
                            .in(BmsProgram::getPublishStatus, PU_LIST);
                    List<BmsProgram> bmsPrograms = bmsProgramService.list(bmsProgramLambdaQueryWrapper);

                    if (ObjectUtil.isNotEmpty(bmsPrograms)) {
                        throw new CommonResponseException("请检查媒资的发布状态");
                    }
                    break;
                //剧集的发布状态
                case 3:
                case 4:
                    LambdaQueryWrapper<BmsContent> bmsContentLambdaQueryWrapper1 = new LambdaQueryWrapper<>();

                    bmsContentLambdaQueryWrapper1.eq(BmsContent::getCmsContentId, contentId)
                            .in(BmsContent::getContentType, ContentTypeEnum.TELEPLAY.getValue(), ContentTypeEnum.EPISODES.getValue())
                            .in(BmsContent::getPublishStatus, PU_LIST);

                    List<BmsContent> bmsContents1 = bmsContentService.list(bmsContentLambdaQueryWrapper1);
                    if (ObjectUtil.isNotEmpty(bmsContents1)) {
                        throw new CommonResponseException("请检查媒资的发布状态");
                    }
                    break;

                default:
                    throw new BizException("case值有误");
            }
/*

            List<BmsContent> bmsContents = bmsContentService.list(bmsContentLambdaQueryWrapper);
            if (ObjectUtil.isNotEmpty(bmsContents)){
                throw new CommonResponseException("请检查媒资的发布状态");
            }
            List<BmsProgram> bmsPrograms = bmsProgramService.list(bmsProgramLambdaQueryWrapper);
            if (ObjectUtil.isNotEmpty(bmsPrograms)){
                throw new CommonResponseException("请检查媒资的发布状态");
            }*/
        }
        return true;
    }

    /**
     * 1、子集数据回收成功，删除bms表中的子集数据
     * 2、视频和子集的mapping关系回收：视频和子集进行解绑操作，子集的resource_release_code值清空
     * 3、只回收视频介质：平台不做任何处理，介质不删除、关系不解绑
     * @param delMovieCodes
     * @param spIdList
     * @return
     */
    @Override
    public boolean delByCode(List<String> delMovieCodes, List<Long> spIdList) {
        // 先判断是不是所有的sp都是自动发布
        log.info("自动发布删除 movie ");
        boolean allSpRollback = true;
        List<CmsResource> cmsResources = cmsResourceMapper.selectList(Wrappers.lambdaQuery(CmsResource.class)
                .in(CmsResource::getCode, delMovieCodes));
        List<String> contentCodes = cmsResources.stream()
                .map(CmsResource::getContentCode)
                .collect(Collectors.toList());
        Long extendSpContentCount = bmsContentService.count(Wrappers.lambdaQuery(BmsContent.class)
                .notIn(BmsContent::getSpId, spIdList)
                .in(BmsContent::getCmsContentCode, contentCodes)
        );
        if (extendSpContentCount != null && extendSpContentCount > 0) {
            log.error("该Cp内容已授权给其他非自动发布SP且已经发布到运营商");
            allSpRollback = false;
//            throw new CommonResponseException(CommonResponseEnum.DELETE_ERROR);
        }
        if (allSpRollback) {
            log.info("视频关联所有sp内容回收成功，删除cp视频");
            //解绑内容上的code信息
            List<String> releaseCode = cmsResources.stream()
                    .filter(resource -> resource.getType().equals(MovieTypeEnums.RELEASE.getCode()))
                    .map(CmsResource::getContentCode)
                    .collect(Collectors.toList());
            List<String> previewCode = cmsResources.stream()
                    .filter(resource -> resource.getType().equals(MovieTypeEnums.PREVIEW.getCode()))
                    .map(CmsResource::getContentCode)
                    .collect(Collectors.toList());

//            programService.update(Wrappers.lambdaUpdate(CmsProgram.class)
//                    .set(CmsProgram::getPreviewStatus, MovieStatusEnum.NotRelevancy.getValue())
//                    .set(CmsProgram::getResourcePreviewCode, null)
//                    .in(CmsProgram::getCode, previewCode)
//            );
//            // 考虑剧集 有可能的预览片
//            seriesService.update(Wrappers.lambdaUpdate(CmsSeries.class)
//                    .set(CmsSeries::getPreviewStatus, MovieStatusEnum.NotRelevancy.getValue())
//                    .set(CmsSeries::getResourcePreviewCode, null)
//                    .in(CmsSeries::getCode, previewCode)
//            );
//            programService.update(Wrappers.lambdaUpdate(CmsProgram.class)
//                    .set(CmsProgram::getReleaseStatus, MovieStatusEnum.NotRelevancy.getValue())
//                    .set(CmsProgram::getResourceReleaseCode, null)
//                    .in(CmsProgram::getCode, releaseCode)
//            );

//            // 删除movie
//            cmsResourceMapper.delete(Wrappers.lambdaQuery(CmsResource.class).in(CmsResource::getCode, delMovieCodes));
//            //删除resource
//            cmsMovieService.remove(Wrappers.lambdaQuery(CmsMovie.class).in(CmsMovie::getResourceCode, delMovieCodes));
//            //删除FTP内容
//            cmsResources.forEach(resource -> {
//                log.info("自动发布 删除 resource code = {}， File = {}", resource.getCode(), resource.getFileUrl());
//                FtpAnalysisUtil.DelFtp(resource.getFileUrl());
//            });
        }
        return allSpRollback;
    }

    @Override
    public boolean deleteByCodeAndSp(List<SubOrderMappingsEntity> deleteMovieMappingEntities, List<Long> spIdList) {
        log.info("自动发布删除 movie mapping 关系 ");
        boolean allSpRollback = true;
        Long extendSpContentCount = bmsContentService.count(Wrappers.lambdaQuery(BmsContent.class)
                .notIn(BmsContent::getSpId, spIdList)
                .in(BmsContent::getCmsContentCode,
                        deleteMovieMappingEntities.stream().map(SubOrderMappingsEntity::getParentCode)
                                .collect(Collectors.toList()))
        );

        if (extendSpContentCount != null && extendSpContentCount > 0) {
            log.error("该Cp 视频绑定节目已授权给其他非自动发布SP且已经发布到运营商");
            allSpRollback = false;
//            throw new CommonResponseException(CommonResponseEnum.DELETE_ERROR);
        }
        //有可能是子集视频
        Long extendSpProgramCount = bmsProgramService.count(Wrappers.lambdaQuery(BmsProgram.class)
                .notIn(BmsProgram::getSpId, spIdList)
                .in(BmsProgram::getCmsContentCode,
                        deleteMovieMappingEntities.stream().map(SubOrderMappingsEntity::getParentCode)
                                .collect(Collectors.toList()))
        );

        if (extendSpProgramCount != null && extendSpProgramCount > 0) {
            log.error("该Cp视频绑定子集已授权给其他非自动发布SP");
            allSpRollback = false;
//            throw new CommonResponseException(CommonResponseEnum.DELETE_ERROR);
        }
        if (allSpRollback) {

            deleteMovieMappingEntities.forEach(orderMappingsEntity -> {
                String resourceCode = orderMappingsEntity.getElementCode();
                String contentCode = orderMappingsEntity.getParentCode();
                //仅仅解绑关联在状态
                cmsResourceMapper.update(null, Wrappers.lambdaUpdate(CmsResource.class)
                        .set(CmsResource::getContentId, null)
                        .set(CmsResource::getContentType, null)
                        .set(CmsResource::getContentCode, null)
                        .set(CmsResource::getContentStatus, MovieStatusEnum.NotRelevancy.getValue())
                        .set(CmsResource::getContentName, null)
                        .eq(CmsResource::getCode, resourceCode)
                        .eq(CmsResource::getContentCode, contentCode)
                );
                cmsMovieService.update(Wrappers.lambdaUpdate(CmsMovie.class)
                        .set(CmsMovie::getContentId, null)
                        .set(CmsMovie::getContentType, null)
                        .set(CmsMovie::getContentCode, null)
                        .set(CmsMovie::getContentStatus, MovieStatusEnum.NotRelevancy.getValue())
                        .eq(CmsMovie::getResourceCode, resourceCode)
                        .eq(CmsMovie::getContentCode, contentCode)
                );
                if (ObjectsTypeConstants.SERIES.equals(orderMappingsEntity.getParentType())) {
                    seriesService.update(Wrappers.lambdaUpdate(CmsSeries.class)
                            .set(CmsSeries::getPreviewStatus, MovieStatusEnum.NotRelevancy.getValue())
                            .set(CmsSeries::getResourcePreviewCode, null)
                            .eq(CmsSeries::getCode, contentCode)
                            .eq(CmsSeries::getResourcePreviewCode, resourceCode));
                } else {
                    // 单集子集
                    //正片
                    programService.update(Wrappers.lambdaUpdate(CmsProgram.class)
                            .set(CmsProgram::getReleaseStatus, MovieStatusEnum.NotRelevancy.getValue())
                            .set(CmsProgram::getResourceReleaseCode, null)
                            .eq(CmsProgram::getCode, contentCode)
                            .eq(CmsProgram::getResourceReleaseCode, resourceCode));
                    //预览片
                    programService.update(Wrappers.lambdaUpdate(CmsProgram.class)
                            .set(CmsProgram::getPreviewStatus, MovieStatusEnum.NotRelevancy.getValue())
                            .set(CmsProgram::getResourcePreviewCode, null)
                            .eq(CmsProgram::getCode, contentCode)
                            .eq(CmsProgram::getResourcePreviewCode, resourceCode));
                }
            });
        }
        return true;
    }

    @Override
    public List<CmsResource> listByCode(List<String> codeList) {
        if (CollectionUtil.isEmpty(codeList)) {
            return new ArrayList<>();
        }
        LambdaQueryWrapper<CmsResource> query = Wrappers.lambdaQuery();
        query.in(CmsResource::getCode, codeList);
        return list(query);
    }

    @Override
    public void deleteByIds(List<Long> resourceIdList) {
        if (CollectionUtil.isEmpty(resourceIdList)) {
            return;
        }
        LambdaQueryWrapper<CmsResource> query = Wrappers.lambdaQuery();
        query.in(CmsResource::getId, resourceIdList);
        remove(query);
    }


    /**
     * 视频播放
     *
     * @param id
     * @return
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public CommonResponse selectMov(Long id) {
        CmsResource cmsResource = cmsResourceMapper.selectById(id);
        String resourceFileUrl = cmsResource.getFileUrl();
        Long storageId = cmsResource.getStorageId();
        SysStorage sysStorage = null;
        String movieUrl = null;
        if (ObjectUtil.isNotEmpty(storageId)) {
            sysStorage = storageService.getOne(Wrappers.lambdaQuery(SysStorage.class)
                    .eq(SysStorage::getId, storageId)
                    .eq(SysStorage::getStatus, StorageStatusEnum.ALLOW.getCode())
                    .last("limit 1")
            );
            if (sysStorage != null && sysStorage.getMovieHttpPrefix() != null) {
                Pattern p = compile("\\d+\\.\\d+\\.\\d+\\.\\d+\\:\\d+");
                Matcher matcher = p.matcher(resourceFileUrl);
                if (matcher.find()) {
                    int length = resourceFileUrl.length();
                    String concat = sysStorage.getMovieHttpPrefix().concat(resourceFileUrl.substring(resourceFileUrl.indexOf(matcher.group()) + matcher.group().length() + 1, length));
                    movieUrl = concat + "?cpid=" + cmsResource.getCpId() + "&moviecode=" + cmsResource.getCode();
                }
            }
        }
        return CommonResponse.success(movieUrl);
    }


    private void set(List<CmsResourceVo> cmsResourceVos, CmsResource cmsResource1) {
        CmsResourceVo cmsResourceVo = new CmsResourceVo();
        BeanUtils.copyProperties(cmsResource1, cmsResourceVo);
        if (ObjectUtil.isNotEmpty(cmsResourceVo.getDuration()) && cmsResourceVo.getDuration() > 0) {
            cmsResourceVo.setDuration(cmsResourceVo.getDuration() / 60);
        }
        String fileUrl = cmsResource1.getFileUrl();
        Long storageId = cmsResource1.getStorageId();
        SysStorage sysStorage = null;
        if (ObjectUtil.isNotEmpty(storageId)) {
            sysStorage = storageService.getOne(Wrappers.lambdaQuery(SysStorage.class)
                    .eq(SysStorage::getId, storageId)
                    .eq(SysStorage::getStatus, StorageStatusEnum.ALLOW.getCode())
                    .last("limit 1")
            );
            if (sysStorage != null && sysStorage.getMovieHttpPrefix() != null) {
                Pattern p = compile("\\d+\\.\\d+\\.\\d+\\.\\d+\\:\\d+");
                Matcher matcher = p.matcher(fileUrl);
                if (matcher.find()) {
                    int length = fileUrl.length();
                    String concat = sysStorage.getMovieHttpPrefix().concat(fileUrl.substring(fileUrl.indexOf(matcher.group()) + matcher.group().length() + 1, length));
                    String movieUrl = concat + "?cpid=" + cmsResourceVo.getCpId() + "&moviecode=" + cmsResourceVo.getCode();
                    cmsResourceVo.setMovieUrl(movieUrl);
                }
            }
        }
        cmsResourceVos.add(cmsResourceVo);
    }

    /**
     * 设为正片/预览片
     *
     * @param ids
     * @param type
     * @return
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public CommonResponse updateStatus(String ids, String type) {
        if (ObjectUtil.isEmpty(ids) || ObjectUtil.isEmpty(type)) {
            throw new CommonResponseException("参数不能为空");
        }
        String[] idAyyay = ids.split(",");
        CommonResponse<Object> FAIL = getResponse(idAyyay);
        if (FAIL != null) return FAIL;
        for (String id : idAyyay) {
            CmsResource cmsResource = cmsResourceMapper.selectById(id);
            if (type.equals("1")) {
                //设为正片
                cmsResource.setType(1);
                cmsResourceMapper.updateById(cmsResource);
                //同步到cms_movie表中
                cmsMovieService.update(null, Wrappers.<CmsMovie>lambdaUpdate()
                        .set(CmsMovie::getType, 1)
                        .eq(CmsMovie::getResourceId, id)
                );
            }
            if (type.equals("2")) {
                cmsResource.setType(2);
                cmsResourceMapper.updateById(cmsResource);
                //同步到cms_movie表中
                cmsMovieService.update(null, Wrappers.<CmsMovie>lambdaUpdate()
                        .set(CmsMovie::getType, 2)
                        .eq(CmsMovie::getResourceId, id)
                );
            }
        }
        return CommonResponse.general(CommonResponseEnum.SUCCESS);
    }

    @Override
    public CommonResponse updateStatus(String ids) {
        if (ObjectUtil.isEmpty(ids) ) {
            throw new CommonResponseException("参数不能为空");
        }
        String[] idAyyay = ids.split(",");
        CommonResponse<Object> FAIL = getResponseNoContentStatus(idAyyay);
        StringBuilder sb = new StringBuilder();
        if (FAIL != null) return FAIL;
        boolean retFlag = false;
        for (String id : idAyyay) {
            CmsResource cmsResource = cmsResourceMapper.selectById(id);
                //设为正片
            if(cmsResource.getStatus().equals(ContentStatusEnum.DELETE.getCode()) ){
                sb.append(cmsResource.getName()).append("已删除;");
                continue;
            }
            if( cmsResource.getStatus().equals(ContentStatusEnum.DELETEING.getCode())){
                sb.append(cmsResource.getName()).append("待删除;");
                continue;
            }
            retFlag =true;
            cmsResource.setStatus(ContentStatusEnum.DELETEING.getCode());
            cmsResourceMapper.updateById(cmsResource);
            //同步到cms_movie表中
            cmsMovieService.update(null, Wrappers.<CmsMovie>lambdaUpdate()
                    .set(CmsMovie::getStatus, ContentStatusEnum.DELETEING.getCode())
                    .eq(CmsMovie::getResourceId, id)
            );

        }
        if(sb.length() ==0 ){
           return CommonResponse.general(CommonResponseEnum.SUCCESS);
        }
        if(sb.length()> 0 && retFlag == false){
            return CommonResponse.fail( sb.toString());
        }
        if(sb.length()>0 && retFlag == true ){
            return CommonResponse.general(CommonResponseEnum.RESOURCE_HINT, sb.toString());
        }
        return CommonResponse.general(CommonResponseEnum.SUCCESS, sb.toString());
    }


    /**
     * 删除源片
     *
     * @param ids
     * @return
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public CommonResponse del(String ids) {
        if (ObjectUtil.isEmpty(ids)) {
            throw new CommonResponseException("参数不能为空");
        }
        String[] idArray = ids.split(",");
        CommonResponse<Object> FAIL = getResponse(idArray);
        if (FAIL != null) return FAIL;

        for (String id : idArray) {
            CmsResource cmsResource = cmsResourceMapper.selectOne(Wrappers.<CmsResource>lambdaQuery().eq(CmsResource::getId, id));
            List<CmsMovie> cmsMovies = cmsMovieService.list(Wrappers.<CmsMovie>lambdaQuery().eq(CmsMovie::getResourceId, id).eq(CmsMovie::getCpId, cmsResource.getCpId()));

            //文件名、路径解析
            FtpAnalysisUtil.DelFtp(cmsResource.getFileUrl());
            //删除成片
            if (ObjectUtil.isNotEmpty(cmsMovies)) {
                cmsMovies.stream().forEach(p -> FtpAnalysisUtil.DelFtp(p.getFileUrl()));
            }
            cmsResourceMapper.deleteById(id);
            cmsMovieService.remove(Wrappers.<CmsMovie>lambdaQuery().eq(CmsMovie::getResourceId, id));
            sendDelMq(cmsResource.getCpId(),cmsResource.getFileSize());
        }

        return CommonResponse.general(CommonResponseEnum.SUCCESS);
    }

    @Override
    public CommonResponse delOnlyTS(String ids) {
        if (ObjectUtil.isEmpty(ids)) {
            throw new CommonResponseException("参数不能为空");
        }
        String[] idArray = ids.split(",");
        CommonResponse<Object> FAIL = getResponseNoContentStatus(idArray);
        if (FAIL != null) return FAIL;
        //修改cms_resource，cms_movie表 status 状态为 -1
        for (String id : idArray) {
            CmsResource cmsResource = cmsResourceMapper.selectOne(Wrappers.<CmsResource>lambdaQuery().eq(CmsResource::getId, id));
            List<CmsMovie> cmsMovies = cmsMovieService.list(Wrappers.<CmsMovie>lambdaQuery().eq(CmsMovie::getResourceId, id).eq(CmsMovie::getCpId, cmsResource.getCpId()));

            //文件名、路径解析
            Boolean cmsResourceDeleteFlag = FtpAnalysisUtil.DelFtp(cmsResource.getFileUrl());
            if(cmsResourceDeleteFlag){
                cmsResource.setStatus(ContentStatusEnum.DELETE.getCode());
                this.updateById(cmsResource);
            }else{
                if (!FtpAnalysisUtil.IsExistPath(cmsResource.getFileUrl())) {
                    cmsResource.setStatus(ContentStatusEnum.DELETE.getCode());
                    this.updateById(cmsResource);
                    log.info("文件路径不存在："+ cmsResource.getFileUrl());
                }else{
                   return CommonResponse.general(CommonResponseEnum.FAIL);
                }
            }
            //删除成片
            if (ObjectUtil.isNotEmpty(cmsMovies)) {
                cmsMovies.stream().forEach(p -> {
                    Boolean flag = FtpAnalysisUtil.DelFtp(p.getFileUrl());
                    if(flag) {
                        p.setStatus(ContentStatusEnum.DELETE.getCode());
                        cmsMovieService.updateById(p);
                    }else{
                        if (!FtpAnalysisUtil.IsExistPath(p.getFileUrl())) {
                            p.setStatus(ContentStatusEnum.DELETE.getCode());
                            cmsMovieService.updateById(p);
                            log.info("文件路径不存在："+ p.getFileUrl());
                        }else{
                           ;
                        }
                    }
                });
            }
        }
        return CommonResponse.general(CommonResponseEnum.SUCCESS);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public CommonResponse delOnlyTsById(String id) {

        CmsResource cmsResource = cmsResourceMapper.selectOne(Wrappers.<CmsResource>lambdaQuery().eq(CmsResource::getId, id));
        List<CmsMovie> cmsMovies = cmsMovieService.list(Wrappers.<CmsMovie>lambdaQuery().eq(CmsMovie::getResourceId, id).eq(CmsMovie::getCpId, cmsResource.getCpId()));

        //文件名、路径解析
        Boolean cmsResourceDeleteFlag = FtpAnalysisUtil.DelFtp(cmsResource.getFileUrl());
        if(cmsResourceDeleteFlag){
            cmsResource.setStatus(ContentStatusEnum.DELETE.getCode());
            this.updateById(cmsResource);
            sendDelMq(cmsResource.getCpId(),cmsResource.getFileSize());
        }else{
            cmsResource.setStatus(ContentStatusEnum.DELETE_FAIL.getCode());
            this.updateById(cmsResource);
            return CommonResponse.general(CommonResponseEnum.FAIL);

        }
        //删除成片
        if (ObjectUtil.isNotEmpty(cmsMovies)) {
            cmsMovies.stream().forEach(p -> {
                Boolean flag = FtpAnalysisUtil.DelFtp(p.getFileUrl());
                if(flag) {
                    p.setStatus(ContentStatusEnum.DELETE.getCode());
                    cmsMovieService.updateById(p);
                }else{
                    //增加删除失败状态变更
                    p.setStatus(ContentStatusEnum.DELETE_FAIL.getCode());
                    cmsMovieService.updateById(p);
                    log.info("文件删除失败"+ p.getFileUrl());

                }
            });
        }
        return CommonResponse.general(CommonResponseEnum.SUCCESS);
    }

    @Override
    public CommonResponse delOnlyTsBySelect() {
        LambdaQueryWrapper<CmsResource> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(CmsResource::getStatus, ContentStatusEnum.DELETEING.getCode()).last("limit 1");
        CmsResource cmsResource = this.getOne(wrapper);
        if(ObjectUtil.isNotEmpty(cmsResource)){
            CommonResponse commonResponse = delOnlyTsById(cmsResource.getId().toString());
            log.info("delOnlyTsBySelect:,{}, {}, {}" ,commonResponse.getMessage(),commonResponse.getData(),cmsResource.getId());
            return commonResponse;
        }
        return CommonResponse.general("null", "null");
    }
    /**
     * 关联正片预览片列表查询
     *
     * @param page
     * @param cmsResource
     * @return
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Page<CmsResource> movPageList(Page page, CmsResourceDto cmsResource) {
  /*      if (cmsResource.getSaveOrUpStatus() == 2) {
            //是否发布状态检查
            publishInspect(cmsResource);
        }*/

        Page iPage = getPage(page);
        LambdaQueryWrapper<CmsResource> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(CmsResource::getCpId, cmsResource.getCpId());
        wrapper.eq(CmsResource::getType, cmsResource.getType());
        wrapper.eq(CmsResource::getStatus,  ContentStatusEnum.EFFECTIVE.getCode());
        wrapper.notIn(CmsResource::getContentStatus, MediaAssociationStatusEnum.Relevancy.getValue());
        if (ObjectUtil.isNotEmpty(cmsResource.getName())) {
            wrapper.like(CmsResource::getName, cmsResource.getName());
        }
        Page selectPage = cmsResourceMapper.selectPage(iPage, wrapper);
        List<CmsResource> records = selectPage.getRecords();
        records.forEach(p -> {
            if (ObjectUtil.isNotEmpty(p.getDuration())) {
                p.setDuration(p.getDuration() / 60);
            }
        });
        return selectPage;
    }

    /**
     * 删除视频（解绑关联关系）
     *
     * @param cmsResource
     * @return
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean untie(CmsResource cmsResource) {
        //是否发布状态检查
        check(cmsResource.getContentId(), cmsResource.getContentType(), 2);

        //解除媒资跟视频的关系
        update(Wrappers.<CmsResource>lambdaUpdate()
                .set(CmsResource::getContentId, null)
                .set(CmsResource::getContentCode, null)
                .set(CmsResource::getContentName, null)
                .set(CmsResource::getContentType, null)
                .set(CmsResource::getContentStatus, MediaAssociationStatusEnum.AwaitRelevancy.getValue())
                .eq(CmsResource::getId, cmsResource.getId())
        );

        Integer type = cmsResource.getType();
        //正片
        if (type == 1) {
            //单集
            if (cmsResource.getContentType().equals(ContentTypeEnum.FILM.getValue())) {
                LambdaUpdateWrapper<CmsProgram> wrapper = new LambdaUpdateWrapper<>();
                LambdaUpdateWrapper<BmsContent> bmsContentLambdaQueryWrapper = new LambdaUpdateWrapper<>();
                wrapper.set(CmsProgram::getResourceReleaseCode, null);
                wrapper.set(CmsProgram::getReleaseStatus, MediaAssociationStatusEnum.AwaitRelevancy.getValue());
                bmsContentLambdaQueryWrapper.set(BmsContent::getReleaseStatus, MediaAssociationStatusEnum.AwaitRelevancy.getValue());

                wrapper.eq(CmsProgram::getId, cmsResource.getContentId());
                programService.update(wrapper);

                bmsContentLambdaQueryWrapper.eq(BmsContent::getCmsContentId, cmsResource.getContentId())
                        .eq(BmsContent::getContentType, ContentTypeEnum.FILM.getValue())
                ;
                contentService.update(bmsContentLambdaQueryWrapper);
            }

            //子集
            if (cmsResource.getContentType().equals(ContentTypeEnum.SUBSET.getValue())) {
                LambdaUpdateWrapper<CmsProgram> wrapper = new LambdaUpdateWrapper<>();
                LambdaUpdateWrapper<BmsProgram> bmsProgramLambdaUpdateWrapper = new LambdaUpdateWrapper<>();
                wrapper.set(CmsProgram::getResourceReleaseCode, null);
                wrapper.set(CmsProgram::getReleaseStatus, MediaAssociationStatusEnum.AwaitRelevancy.getValue());
                bmsProgramLambdaUpdateWrapper.set(BmsProgram::getReleaseStatus, MediaAssociationStatusEnum.AwaitRelevancy.getValue());

                wrapper.eq(CmsProgram::getId, cmsResource.getContentId());
                programService.update(wrapper);

                bmsProgramLambdaUpdateWrapper.eq(BmsProgram::getCmsContentId, cmsResource.getContentId());
                bmsProgramService.update(bmsProgramLambdaUpdateWrapper);
            }
        }
        //预览片
        if (type == 2) {
            //单集
            if (cmsResource.getContentType().equals(ContentTypeEnum.FILM.getValue())) {
                LambdaUpdateWrapper<CmsProgram> wrapper = new LambdaUpdateWrapper<>();
                LambdaUpdateWrapper<BmsContent> bmsContentLambdaQueryWrapper = new LambdaUpdateWrapper<>();
                wrapper.set(CmsProgram::getResourcePreviewCode, null);
                wrapper.set(CmsProgram::getPreviewStatus, MediaAssociationStatusEnum.AwaitRelevancy.getValue());
                bmsContentLambdaQueryWrapper.set(BmsContent::getReleaseStatus, MediaAssociationStatusEnum.AwaitRelevancy.getValue());

                wrapper.eq(CmsProgram::getId, cmsResource.getContentId());
                programService.update(wrapper);

                bmsContentLambdaQueryWrapper.eq(BmsContent::getCmsContentId, cmsResource.getContentId())
                        .eq(BmsContent::getContentType, ContentTypeEnum.FILM.getValue());
                contentService.update(bmsContentLambdaQueryWrapper);
            }

            if (cmsResource.getContentType().equals(ContentTypeEnum.TELEPLAY.getValue()) || cmsResource.getContentType().equals(ContentTypeEnum.EPISODES.getValue())) {
                LambdaUpdateWrapper<CmsSeries> cmsSeriesLambdaUpdateWrapper = new LambdaUpdateWrapper<>();
                LambdaUpdateWrapper<BmsContent> bmsContentLambdaQueryWrapper = new LambdaUpdateWrapper<>();
                cmsSeriesLambdaUpdateWrapper.set(CmsSeries::getResourcePreviewCode, null);
                cmsSeriesLambdaUpdateWrapper.set(CmsSeries::getPreviewStatus, MediaAssociationStatusEnum.AwaitRelevancy.getValue());
                cmsSeriesLambdaUpdateWrapper.eq(CmsSeries::getId, cmsResource.getContentId());
                seriesService.update(cmsSeriesLambdaUpdateWrapper);


                bmsContentLambdaQueryWrapper.set(BmsContent::getPreviewStatus, MediaAssociationStatusEnum.AwaitRelevancy.getValue());
                bmsContentLambdaQueryWrapper.eq(BmsContent::getCmsContentId, cmsResource.getContentId())
                        .in(BmsContent::getContentType, ContentTypeEnum.TELEPLAY.getValue(), ContentTypeEnum.EPISODES.getValue());
                contentService.update(bmsContentLambdaQueryWrapper);
            }
        }

        //同步到cms_movie表中
        cmsMovieService.update(Wrappers.<CmsMovie>lambdaUpdate()
                        .set(CmsMovie::getContentStatus, MediaAssociationStatusEnum.AwaitRelevancy.getValue())
                        .set(CmsMovie::getContentType, null)
                        .set(CmsMovie::getContentCode, null)
                        .set(CmsMovie::getContentId, null)
//                .set(CmsMovie::getFileUrl, null)
                        .eq(CmsMovie::getResourceId, cmsResource.getId())

        );
        List<CmsMovie> cmsMovieList = cmsMovieService.list(Wrappers.<CmsMovie>lambdaQuery().select(CmsMovie::getId).eq(CmsMovie::getResourceId, cmsResource.getId()));
        if (ObjectUtil.isNotEmpty(cmsMovieList)) {
            List<Long> idList = new ArrayList<>();
            cmsMovieList.forEach(p -> idList.add(p.getId()));
            //删除视频关键帧信息
            cmsPlayerPictureService.remove(Wrappers.<CmsPlayerPicture>lambdaUpdate().in(CmsPlayerPicture::getMovieId, idList));
            cmsPlayerSliceService.remove(Wrappers.<CmsPlayerSlice>lambdaQuery().in(CmsPlayerSlice::getMovieId, idList));
        }
        return true;
    }

    /**
     * 视频列表编辑回显
     *
     * @return
     */
    @Override
    public CommonResponse getMovList(String contentId, Integer type) {
        List<CmsResource> cmsResources = cmsResourceMapper.selectList(Wrappers.<CmsResource>lambdaQuery().eq(CmsResource::getContentId, contentId).eq(CmsResource::getContentType, type));
        List<CmsResourceVo> cmsResourceVos = new ArrayList<>();
        if (!Collections.isEmpty(cmsResources)) {
            cmsResources.stream().forEach(
                    cmsResource -> {
                        set(cmsResourceVos, cmsResource);
                    }
            );

        }
        return CommonResponse.success(cmsResourceVos);
    }


    /**
     * 发布检查
     *
     * @param cmsResource
     */
    private void publishInspect(CmsResource cmsResource) {
        Integer contentType = cmsResource.getContentType();
        List<Long> contentId = new ArrayList<>();
        contentId.add(cmsResource.getContentId());

        Class<?> tableClazz = null;

        if (contentType.equals(ContentTypeEnum.FILM.getValue()) || contentType.equals(ContentTypeEnum.TELEPLAY.getValue()) || contentType.equals(ContentTypeEnum.EPISODES.getValue())) {
            tableClazz = BmsContent.class;
        }
        if (contentType.equals(ContentTypeEnum.SUBSET.getValue())) {
            tableClazz = BmsProgram.class;
        }
        RuleResult rr = RuleCondition.create()
                .and(PublishStatusRule.init(tableClazz).data(contentId).condCol("cms_content_id").policy(PublishCheck.PU))
                .execute();
        if (!rr.isPass()) {
            throw new BizException(rr.getMsg());
        }
    }

    /**
     * 判断源片是否存在以及该源片已被媒资关联
     *
     * @param split
     * @return
     */
    private CommonResponse<Object> getResponse(String[] split) {
        for (String id : split) {
            CmsResource cmsResource = cmsResourceMapper.selectById(id);
            if (ObjectUtil.isEmpty(cmsResource)) {
                return CommonResponse.general(CommonResponseEnum.FAIL, "源片不存在,请刷新页面");
            }
            if (cmsResource.getContentStatus().equals(2)) {
                return CommonResponse.general(CommonResponseEnum.FAIL, cmsResource.getName() + "已被媒资关联，无法操作！");
            }
        }
        return null;
    }

    /**
     * 判断源片是否存在以及该源片已被媒资关联
     *
     * @param split
     * @return
     */
    private CommonResponse<Object> getResponseNoContentStatus(String[] split) {
        for (String id : split) {
            CmsResource cmsResource = cmsResourceMapper.selectById(id);
            if (ObjectUtil.isEmpty(cmsResource)) {
                return CommonResponse.general(CommonResponseEnum.FAIL, "源片不存在,请刷新页面");
            }
        }
        return null;
    }


    private Page getPage(Page page) {
        if (page.getCurrent() < 0) {
            page.setCurrent(1);
        }
        if (page.getSize() < 0 || page.getSize() > 500) {
            page.setSize(20);
        }
        Page iPage = new Page(page.getCurrent(), page.getSize());
        return iPage;
    }
}


