package com.pukka.iptv.manage.service.cms;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;

import com.pukka.iptv.common.data.dto.CheckEndDto;
import com.pukka.iptv.common.data.dto.CheckSelfDto;
import com.pukka.iptv.common.data.dto.CountDto;
import com.pukka.iptv.common.data.dto.MediaCountDto;
import com.pukka.iptv.common.data.model.cms.CmsCheckHistory;
import com.pukka.iptv.common.data.model.cms.CmsProgram;
import com.pukka.iptv.common.data.model.cms.CmsSeries;
import com.pukka.iptv.common.base.vo.CommonResponse;

import java.text.ParseException;

/**
 * 审核历史
 *
 * @author: zhoul
 * @date: 2021-8-27 11:34:28
 */

public interface CmsCheckHistoryService extends IService<CmsCheckHistory> {

    IPage<CmsCheckHistory> listById(Page page, CmsCheckHistory cmsCheckHistory);

    /**
     * 单集子集自审审核
     * @return
     */
    CommonResponse selfCheck(CheckSelfDto checkSelfDto);

    /**
     * 剧集自审审核
     * @return
     */
    CommonResponse seriesSelfCheck(CheckSelfDto checkSelfDto);

    /**
     * 单集子集终重审核
     * @return
     */

    CommonResponse endCheck(CheckEndDto checkEndDto);

    /**
     * 剧集终重审核
     * @return
     */

    CommonResponse seriesEndCheck(CheckEndDto checkEndDto);

    /**
     * 自审集数
     *
     * @return
     */
    CountDto selfCount();

    /**
     * 终审，重审集数
     *
     * @return
     */
    CountDto endCount();

    /**
     * 内容管理集数
     *
     * @return
     */
    MediaCountDto getCount();
}


