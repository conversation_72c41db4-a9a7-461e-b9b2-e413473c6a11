package com.pukka.iptv.manage.mapper.copyright.copyright;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.pukka.iptv.common.data.annotation.DataPermission;
import com.pukka.iptv.common.data.model.copyright.CopyrightData;
import com.pukka.iptv.common.data.vo.bms.BmsPictueFtpVo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

/**
 * @Author: chiron
 * @Date: 2022/07/21/09:04
 * @Description:
 */
@Mapper
@DataPermission(config = "cp_id=cpIds")
public interface CopyrightDataMapper extends BaseMapper<CopyrightData> {
    /**
     * 根据cpid获取到ftp路径
     * @param cpId
     * @return
     */
    BmsPictueFtpVo getStorageFtpUrl(@Param("cpId")Long cpId);
}
