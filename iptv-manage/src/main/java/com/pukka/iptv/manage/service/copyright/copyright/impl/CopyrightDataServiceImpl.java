package com.pukka.iptv.manage.service.copyright.copyright.impl;

import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.nacos.common.utils.StringUtils;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.pukka.iptv.common.base.constant.RedisKeyConstants;
import com.pukka.iptv.common.base.exception.BizException;
import com.pukka.iptv.common.base.exception.CommonResponseException;
import com.pukka.iptv.common.base.util.JwtTokenUtil;
import com.pukka.iptv.common.base.util.UUID;
import com.pukka.iptv.common.base.vo.SecurityUser;
import com.pukka.iptv.common.core.util.DateUtils;
import com.pukka.iptv.common.core.util.SafeUtil;
import com.pukka.iptv.common.data.model.copyright.CopyrightData;
import com.pukka.iptv.common.data.model.sys.SysCp;
import com.pukka.iptv.common.data.model.sys.SysDictionaryItem;
import com.pukka.iptv.common.data.model.sys.SysStorage;
import com.pukka.iptv.common.data.vo.bms.BmsPictueFtpVo;
import com.pukka.iptv.common.redis.service.RedisService;
import com.pukka.iptv.manage.mapper.copyright.copyright.CopyrightDataMapper;
import com.pukka.iptv.manage.service.common.FtpAnalysisUtil;
import com.pukka.iptv.manage.service.copyright.copyright.CopyrightDataService;
import com.pukka.iptv.manage.service.sys.SysDictionaryItemService;
import com.pukka.iptv.manage.util.DateTooles;
import com.pukka.iptv.manage.util.downloadUtils.FtpClientService;
import com.pukka.iptv.manage.util.downloadUtils.dto.RetrieveFileEntity;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

import static java.util.regex.Pattern.compile;

/**
 * @Author: chiron
 * @Date: 2022/07/21/09:03
 * @Description: 版权材料库
 */
@Slf4j
@Service
public class CopyrightDataServiceImpl extends ServiceImpl<CopyrightDataMapper, CopyrightData> implements CopyrightDataService {

    private static final String COPYRIGHT_DATA_PRE = "copyright/";
    private static final String FILENAME_SUFFIX = ".pdf";
    @Autowired
    private RedisService redisService;
    @Autowired
    private FtpClientService ftpClientService;
    @Autowired
    private CopyrightDataMapper copyrightDataMapper;
    @Autowired
    private SysDictionaryItemService sysDictionaryItemService;

    /**
     * 版权文件分页查询
     *
     * @param page
     * @param copyrightData
     * @return
     */
    @Override
    public IPage<CopyrightData> selectPage(Page page, CopyrightData copyrightData) {
        QueryWrapper<CopyrightData> copyrightDataQueryWrapper = new QueryWrapper<>();
        copyrightDataQueryWrapper.lambda().eq(ObjectUtils.isNotEmpty(copyrightData.getCpId()), CopyrightData::getCpId, copyrightData.getCpId())
                .eq(ObjectUtils.isNotEmpty(copyrightData.getPgmCategoryId()), CopyrightData::getPgmCategoryId, copyrightData.getPgmCategoryId())
                .like(ObjectUtils.isNotEmpty(copyrightData.getShowName()), CopyrightData::getShowName, copyrightData.getShowName())
                .orderByDesc(CopyrightData::getCreateTime);
        return copyrightDataMapper.selectPage(page, copyrightDataQueryWrapper);
    }

    /**
     * 新增版权文件
     *
     * @param copyrightData
     * @return
     */
    @Override
    public Boolean createCopyrightData(CopyrightData copyrightData, MultipartFile file) {
        int result = 0;
        SecurityUser user = JwtTokenUtil.getSecurityUser();
        //上传版权文件
        String upload = upload(file, copyrightData);
        if (StringUtils.isEmpty(upload)) {
            log.warn("新增版权文件 -----> 上传版权文件失败，版权实体:{}", JSON.toJSONString(copyrightData));
            return false;
        }
        //生成uuid
        String code = UUID.nextSnow().toString();
        SysCp sysCp = redisService.getCacheMapValue(RedisKeyConstants.SYS_CP, String.valueOf(copyrightData.getCpId()));
        if (sysCp == null) {
            throw new CommonResponseException("cpId" + copyrightData.getCpId() + "不存在");
        } else {
            copyrightData.setCpName(sysCp.getName());
        }
        String fileName = setName(copyrightData.getCpName(), copyrightData.getShowName());
        copyrightData.setCode(code).setFilePath(upload).setShowName(fileName + FILENAME_SUFFIX);

        SysDictionaryItem one = sysDictionaryItemService.getOne(Wrappers.<SysDictionaryItem>lambdaQuery().eq(SysDictionaryItem::getId, copyrightData.getPgmCategoryId()).last("limit 1"));
        if (null == one) {
            throw new CommonResponseException("媒资分类id=" + copyrightData.getPgmCategoryId() + "不存在");
        } else {
            copyrightData.setPgmCategory(one.getName());
        }
        if (ObjectUtils.isNotEmpty(user)) {
            copyrightData.setCreatorId(user.getCreatorId());
            copyrightData.setCreatorName(user.getName());
        }
        result = copyrightDataMapper.insert(copyrightData);
        return result > 0;
    }

    /**
     * 查看版权文件
     *
     * @param id
     * @return
     */
    @Override
    public List<String> getLicensingFile(Long id) {
        List<CopyrightData> copyrightData = copyrightDataMapper.selectList(
                Wrappers.lambdaQuery(CopyrightData.class)
                        .eq(ObjectUtils.isNotEmpty(id), CopyrightData::getId, id)
                        .orderBy(true, true, CopyrightData::getCreateTime));
        List<String> copyrightHttpFileList = new ArrayList<>();
        if (ObjectUtils.isEmpty(copyrightData)) {
            return copyrightHttpFileList;
        }
        if (copyrightData.size() > 0) {
            copyrightData.stream().forEach(
                    data -> {
                        String fileUrl = data.getFilePath();
                        if (ObjectUtil.isNotEmpty(data.getStorageId())) {
                            SysStorage sysStorage = redisService.getCacheMapValue(RedisKeyConstants.SYS_STORAGE_KEY, String.valueOf(data.getStorageId()));
                            if (ObjectUtils.isEmpty(sysStorage)) {
                                throw new BizException("查看版权文件 -----> 从缓存获取存储失败 ,sysStorageId " + data.getStorageId());
                            }
                            if (ObjectUtil.isNotEmpty(sysStorage.getPictureHttpPrefix())) {
                                Pattern p = compile("\\d+\\.\\d+\\.\\d+\\.\\d+\\:\\d+");
                                Matcher matcher = p.matcher(fileUrl);
                                if (matcher.find()) {
                                    int length = fileUrl.length();
                                    copyrightHttpFileList.add(sysStorage.getPictureHttpPrefix().concat(fileUrl.substring(fileUrl.indexOf(matcher.group()) + matcher.group().length() + 1, length)));
                                }
                            }
                        }
                    }
            );

        }
        return copyrightHttpFileList;
    }


    /**
     * 下载版权文件介质
     *
     * @param id
     * @param response
     * @return
     */
    @Override
    public Boolean retrieveFile(Long id, HttpServletResponse response) {
        try {
            CopyrightData copyrightData = copyrightDataMapper.selectById(id);
            RetrieveFileEntity retrieveFile = new RetrieveFileEntity().setFileName(copyrightData.getShowName())
                    .setFileUrl(copyrightData.getFilePath()).setResponse(response);
            Boolean aBoolean = ftpClientService.readFtpStream(retrieveFile);
            if (!aBoolean) {
                log.warn("下载版权文件介质 -----> 介质信息:{},检索介质文件并下载失败!", JSON.toJSONString(copyrightData));
                return false;
            }
        } catch (Exception exception) {
            log.error("下载版权文件介质 -----> 失败,错误信息:{}", exception);
            return false;
        }
        return true;
    }

    /**
     * 版权文件删除
     * @param idList
     * @return
     */
    @Override
    public Boolean remove(List<Long> idList) {
        try {
            //删除存储
            idList.forEach(id -> {
                CopyrightData copyrightData = copyrightDataMapper.selectById(id);
                String filePath = copyrightData.getFilePath();
                if (ObjectUtils.isNotEmpty(filePath)) {
                    FtpAnalysisUtil.DelFtp(filePath);
                }
                copyrightDataMapper.deleteById(id);
            });
        } catch (Exception exception) {
            log.error("版权文件删除 -----> 失败,错误信息:{}", exception);
            return false;
        }
        return true;
    }

    /**
     * 上传版权文件
     *
     * @param file
     * @param copyrightData
     * @return
     */
    public String upload(MultipartFile file, CopyrightData copyrightData) {
        String result = "";
        BmsPictueFtpVo pictueFtpUrl = copyrightDataMapper.getStorageFtpUrl(copyrightData.getCpId());
        if (ObjectUtils.isEmpty(pictueFtpUrl)) {
            log.error("上传版权文件 -----> 未查询到ftp服务器");
            return result;
        }
        copyrightData.setStorageId(pictueFtpUrl.getStorageId()).setStorageName(pictueFtpUrl.getStorageName());
        // 后缀名
        String suffixName = file.getOriginalFilename().substring(file.getOriginalFilename().lastIndexOf("."));
        //后缀名校验pdf
        if (!"pdf".equals((suffixName.substring(1)).toLowerCase())) {
            log.error("上传版权文件 -----> 该文件后缀名错误，请传输pdf文件!");
            throw new CommonResponseException("该文件后缀名错误，请传输pdf文件!");
        }
        String url = "ftp://" + pictueFtpUrl.getAccount() + ":" + pictueFtpUrl.getPassword() + "@" + pictueFtpUrl.getInnerUrl() + "/";
        String fileName = getFolder(pictueFtpUrl) + java.util.UUID.randomUUID().toString().replace("-", "") + suffixName;

        try {
            result = SafeUtil.pictureUpByFtp(file, fileName, url);
        } catch (IOException e) {
            log.error("上传版权文件 -----> 失败,错误信息:{}", e);
            return result;
        }
        if (StringUtils.isEmpty(result)) {
            log.error("上传版权文件 -----> 上传异常,返回结果为空");
            return result;
        }
        return result;
    }

    /**
     * 设置版权材料名称
     *
     * @param cpName
     * @param showName
     * @return
     */
    private String setName(String cpName, String showName) {
        String name = cpName + DateUtils.dateTimeNow() + showName;
        return name;
    }

    /**
     * 获取ftp文件夹
     *
     * @param pictueFtpUrl
     * @return
     */
    public String getFolder(BmsPictueFtpVo pictueFtpUrl) {
        Date date = new Date();
        return COPYRIGHT_DATA_PRE + pictueFtpUrl.getFolderCode() + "/" + DateTooles.getYear(date) + "/" + DateTooles.getMonth(date) + "/" + DateTooles.getDay(date) + "/";
    }
}
