package com.pukka.iptv.manage.service.feedback;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.pukka.iptv.common.api.feign.sys.OutOrderBaseFeignClient;
import com.pukka.iptv.common.base.enums.*;
import com.pukka.iptv.common.base.vo.CommonResponse;
import com.pukka.iptv.common.core.util.ResultData;
import com.pukka.iptv.common.core.util.SafeUtil;
import com.pukka.iptv.common.data.config.CommonConfig;
import com.pukka.iptv.common.data.dto.PublishParamsDto;
import com.pukka.iptv.common.data.model.bms.*;
import com.pukka.iptv.manage.service.bms.*;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.ObjectUtils;

import javax.validation.constraints.NotNull;
import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.Set;

/** 反馈处理器的基类
 * @Author: liaowj
 * @Description:
 * @CreateDate: 2021/9/18 10:51
 * @Version: 1.0
*/
@Component
@Slf4j
public abstract class FeedbackHandler {

    /* 反馈参数 */
    protected PublishParamsDto params;
    /* 内容最终需要更新的状态 */
    protected Integer status;
    /* 工单结果性质描述（发布失败、发布成功、更新失败等等） */
    protected String result;
    /* 工单类型 REGIST、UPDATE、DELETE */
    protected String xmlType;
    /** CP内容运营商状态反馈标识 */
    protected Integer cpFeedbackFlag = null;

    @Autowired
    private CommonConfig config;

    /** 需要反馈状态的cp ID */
    protected Set<Long> cpIdList = new HashSet<>();
    /**
     * 初始化
     * @param params
     * @param success
     * @param fail
     */
    public void initParams(PublishParamsDto params, PublishStatusEnum success, PublishStatusEnum fail) {
        this.params = params;
        this.xmlType = ActionEnums.getByValue(params.getAction()).getInfo();
        if (OrderBaseResultEnum.SUCCESS.getCode().equals(params.getResult())) {
            this.status = success.getCode();
            this.result = success.getMsg();
            try {
                /* 每次都主动查询下需要反馈的CP信息，以防止后期更新，实时同步 */
                JSONArray urlJsonArray = JSONObject.parseArray(config.getCpFeedbackURL());
                for (int i = 0; i < urlJsonArray.size(); i++) {
                    JSONObject urlJson = urlJsonArray.getJSONObject(i);
                    Long cpId = SafeUtil.getLong(urlJson.get("cpId"), 0L);
                    if (cpId > 0) {
                        cpIdList.add(cpId);
                    }
                }
            } catch (Exception e) {
                log.error("获取需要反馈的CP信息失败 {} ", e.getMessage());
            }
            //只要 处理成功就进入待反馈状态
            this.cpFeedbackFlag = CpFeedbackEnum.WAITING.getCode();
        } else {
            this.status = fail.getCode();
            this.result = fail.getMsg();
            this.cpFeedbackFlag = null;
        }
    }
    @Autowired
    protected BmsContentService bmsContentService;
    @Autowired
    protected BmsProgramService bmsProgramService;
    @Autowired
    protected BmsChannelService bmsChannelService;
    @Autowired
    protected BmsPhysicalChannelService bmsPhysicalChannelService;
    @Autowired
    protected BmsScheduleService bmsScheduleService;
    @Autowired
    protected BmsPictureService bmsPictureService;
    @Autowired
    protected BmsPackageService bmsPackageService;
    @Autowired
    protected BmsCategoryService bmsCategoryService;
    @Autowired
    protected BmsPackageContentService bmsPackageContentService;
    @Autowired
    protected BmsPackageChannelService bmsPackageChannelService;
    @Autowired
    protected BmsCategoryContentService bmsCategoryContentService;
    @Autowired
    protected BmsCategoryChannelService bmsCategoryChannelService;
    @Autowired
    protected OutOrderBaseFeignClient outOrderBaseFeignClient;


    /** 处理节目（单集，剧头 -> bms_content）反馈 */
    public FeedbackHandler dealContent() {
        Long contentId = params.getContentId();
        String errorDescription = params.getErrorDescription();
        setCpFeedbackFlag(params.getContentType(), contentId);
        log.info(" 《 {}工单 》 {} 反馈处理， 即将更新Content ID = {}, 状态为publish_status = {}, 描述信息为 publish_description = {} ",
                xmlType, result, contentId, status, errorDescription);
        bmsContentService.update(Wrappers.lambdaUpdate(BmsContent.class)
                .set(BmsContent::getPublishStatus, status)
                .set(cpFeedbackFlag != null, BmsContent::getCpFeedbackFlag, cpFeedbackFlag)
                .set(BmsContent::getPublishDescription, errorDescription)
                .eq(BmsContent::getId, contentId));
        log.info("修改 Content ID = {} 发布状态成功，结束", contentId);
        return this;
    }

    /** 处理子集（子集 -> bms_program）反馈 */
    public FeedbackHandler dealProgram() {
        Long programId = params.getContentId();
        /** 下游处理结果 */
        Integer result = params.getResult();
        String errorDescription = params.getErrorDescription();
        setCpFeedbackFlag(params.getContentType(), programId);
        log.info(" {} 《 {}工单》 反馈处理， 即将更新 Program  ID = {}, 状态为publish_status = {}, 描述信息为 publish_description = {} ",
                xmlType, result, programId, status, errorDescription);
        bmsProgramService.update(Wrappers.lambdaUpdate(BmsProgram.class)
                .set(BmsProgram::getPublishDescription, errorDescription)
                .set(cpFeedbackFlag != null, BmsProgram::getCpFeedbackFlag, cpFeedbackFlag)
                .set(BmsProgram::getPublishStatus, status)
                .eq(BmsProgram::getId, programId));
        log.info("修改 Program ID = {} 发布状态成功，结束", programId);

        return this;
    }

    /** 处理频道（频道 -> bms_channel）反馈 */
    public FeedbackHandler dealChannel() {
        Long channelId = params.getContentId();
        String errorDescription = params.getErrorDescription();
        log.info("Channel 《{}工单》 {}  反馈， 即将更新  ID = {}, 状态为publish_status = {}, 描述信息为 publish_description = {} ",
                xmlType, result, channelId, status, errorDescription);
        bmsChannelService.update(Wrappers.lambdaUpdate(BmsChannel.class).set(BmsChannel::getPublishStatus, status)
                .set(BmsChannel::getPublishDescription, errorDescription).eq(BmsChannel::getId, channelId));
        return this;
    }

    /** 处理节目单（ -> bms_schedule）反馈 */
    public FeedbackHandler dealSchedule() {
        CommonResponse<List<Long>> response = outOrderBaseFeignClient.getScheduleIds(params.getBaseOrderId());
        log.info("查询Schedule 列表返回 = {}", response);
        List<Long> ids = response.getData();
        if (!ObjectUtils.isEmpty(ids)) {
            String errorDescription = params.getErrorDescription();
            log.info("Schedule 《{}工单》 {} 反馈， 即将更新  ID = {}, 状态为publish_status = {}, 描述信息为 publish_description = {} ",
                    xmlType, result, ids,status, errorDescription);
            bmsScheduleService.update(Wrappers.lambdaUpdate(BmsSchedule.class)
                    .set(BmsSchedule::getPublishStatus, status)
                    .set(BmsSchedule::getPublishDescription, errorDescription)
                    .in(BmsSchedule::getId, ids));
            log.info("修改 Schedule ID = {} 状态为： {} 成功，结束", ids, status);
            return this;
        }
        log.error("查询Schedule失败，或者Schedule为空， orderBaseId = {}", params.getBaseOrderId());
        return this;
    }

    /** 处理物理频道（ -> bms_physicalChannel）反馈 */
    public FeedbackHandler dealPhysicalChannel() {
        Long physicalChannelId = params.getContentId();
        String errorDescription = params.getErrorDescription();
        if (params.getContentType().equals(ContentTypeEnum.CHANNEL_PHYSICAL.getValue())) {
            CommonResponse<List<Long>> response = outOrderBaseFeignClient.getPhysicalChannelIds(params.getBaseOrderId());
            log.info("查询Channel相关PhysicalChannel = {}", response);
            if (!ObjectUtils.isEmpty(response.getData())) {
                log.info("同步修改PhysicalChannel IDs = {}", response.getData());
                bmsPhysicalChannelService.update(Wrappers.lambdaUpdate(BmsPhysicalChannel.class)
                        .set(BmsPhysicalChannel::getPublishStatus, status)
                        .set(BmsPhysicalChannel::getPublishDescription, errorDescription)
                        .in(BmsPhysicalChannel::getId, response.getData()));
            }
            return this;
        }
        log.info("PhysicalChannel 《 {}工单 》 {} 反馈， 即将更新  ID = {}, 状态为publish_status = {}, 描述信息为 publish_description = {} ",
                xmlType, result, physicalChannelId, status, errorDescription);
        bmsPhysicalChannelService.update(Wrappers.lambdaUpdate(BmsPhysicalChannel.class)
                .set(BmsPhysicalChannel::getPublishStatus, status)
                .set(BmsPhysicalChannel::getPublishDescription, errorDescription)
                .eq(BmsPhysicalChannel::getId, physicalChannelId));
        log.info("修改 PhysicalChannel ID = {} 发布状态成功，结束", physicalChannelId);
        return this;
    }

    /** 处理栏目（ -> bms_category）反馈 */
    public FeedbackHandler dealCategory() {
        Long id = params.getContentId();
        String errorDescription = params.getErrorDescription();
        log.info("Category 《 {}工单 》{} 反馈， 即将更新  ID = {}, 状态为publish_status = {}, 描述信息为 publish_description = {} ",
                xmlType, result, id, status, errorDescription);
        bmsCategoryService.update(Wrappers.lambdaUpdate(BmsCategory.class)
                .set(BmsCategory::getPublishStatus, status)
                .set(BmsCategory::getPublishDescription, errorDescription)
                .eq(BmsCategory::getId, id));
        log.info("修改 Category ID = {} 发布状态成功，结束", id);
        return this;
    }

    /** 处理产品包（ -> bms_package）反馈 */
    public FeedbackHandler dealPackage() {
        Long id = params.getContentId();
        /** 下游处理结果 */
        String errorDescription = params.getErrorDescription();
        log.info("Package 《 {}工单 》{}  反馈， 即将更新 Package ID = {}, 状态为publish_status = {}, 描述信息为 publish_description = {} ",
                xmlType, result, id, status, errorDescription);
        bmsPackageService.update(Wrappers.lambdaUpdate(BmsPackage.class)
                .set(BmsPackage::getPublishStatus, status)
                .set(BmsPackage::getPublishDescription, errorDescription)
                .eq(BmsPackage::getId, id));
        log.info("修改 Package ID = {} 发布状态成功，结束", id);
        return this;
    }

    /** 处理单独发布的图片（ -> bms_picture）反馈 */
    public FeedbackHandler dealPicture() {
        /** 图片的单独发布  */
        Long pictureId = params.getContentId();
        log.info("Picture 《 {}工单 》{} 反馈， 即将更新  ID = {}, 状态为publish_status = {}, 描述信息为 publish_description = {} ",
                xmlType, result, pictureId, status, params.getErrorDescription());
        bmsPictureService.update(Wrappers.lambdaUpdate(BmsPicture.class)
                .set(BmsPicture::getPublishStatus, status)
                .set(BmsPicture::getPublishDescription, params.getErrorDescription())
                .eq(BmsPicture::getId, pictureId));
        log.info("修改 Picture ID = {} 发布状态成功，结束", pictureId);
        return null;
    }

    /** 处理跟随内容工单发布的图片（ -> bms_picture）反馈 */
    public FeedbackHandler dealMappingPicture() {
        Long contentId = params.getContentId();
        Integer contentType = params.getContentType();
        if (ContentTypeEnum.CHANNEL_PHYSICAL.getValue().equals(contentType)) {
            //channel和physicalchannel一起发布时
            contentType = ContentTypeEnum.CHANNEL.getValue();
        }

        /** 内容发布不带图片ID，内容更新工单既有可能是REGIST、也有可能是UPDATE 图片 */
        if (OrderBaseResultEnum.SUCCESS.getCode().equals(params.getResult())) {
            log.info("{} 《更新工单》成功反馈， 即将更新 Content ID = {} 相关的图片, 状态为publish_status = {}, 描述信息为 publish_description = {} ",
                    ContentTypeEnum.getByValue(contentType).getDesc(), contentId,
                    PublishStatusEnum.PUBLISH.getCode(), params.getErrorDescription());
            //把这个内容相关的全部发布中，更新中的图片改为发布成功
            bmsPictureService.update(Wrappers.lambdaUpdate(BmsPicture.class)
                    .set(BmsPicture::getPublishStatus, PublishStatusEnum.PUBLISH.getCode())
                    .set(BmsPicture::getPublishDescription, params.getErrorDescription())
                    .in(BmsPicture::getPublishStatus, PublishStatusEnum.PUBLISHING.getCode(),PublishStatusEnum.UPDATING.getCode())
                    .eq(BmsPicture::getContentType, contentType)
                    .in(BmsPicture::getBmsContentId, contentId)
            );
        } else {

            //把这个内容相关的全部发布中图片改为发布失败，更新中的图片改为更新失败
            bmsPictureService.update(Wrappers.lambdaUpdate(BmsPicture.class)
                    .set(BmsPicture::getPublishStatus, PublishStatusEnum.FAILPUBLISH.getCode())
                    .set(BmsPicture::getPublishDescription, params.getErrorDescription())
                    .eq(BmsPicture::getPublishStatus, PublishStatusEnum.PUBLISHING.getCode())
                    .eq(BmsPicture::getContentType, contentType)
                    .in(BmsPicture::getBmsContentId, contentId)
            );
            bmsPictureService.update(Wrappers.lambdaUpdate(BmsPicture.class)
                    .set(BmsPicture::getPublishStatus, PublishStatusEnum.FAILUPDATE.getCode())
                    .set(BmsPicture::getPublishDescription, params.getErrorDescription())
                    .eq(BmsPicture::getPublishStatus, PublishStatusEnum.UPDATING.getCode())
                    .eq(BmsPicture::getContentType, contentType)
                    .in(BmsPicture::getBmsContentId, contentId)
            );
            log.info("{} 《更新工单》失败反馈， 即将更新  ID in {}, 状态为publish_status = {}, 描述信息为 publish_description = {} ",
                    ContentTypeEnum.getByValue(contentType).getDesc(), contentId,
                    PublishStatusEnum.FAILUPDATE.getCode(), params.getErrorDescription());
        }
        log.info("修改 {} ID in {} 图片关系发布状态成功，结束", contentId);

        return this;
    }

    /** 处理内容图片关系反馈 */
   /* abstract public FeedbackHandler dealContentPicture();

    *//** 处理内容图片关系反馈 *//*
    abstract public FeedbackHandler dealProgramPicture();

    *//** 处理内容图片关系反馈 *//*
    abstract public FeedbackHandler dealPackagePicture();

    *//** 处理内容图片关系反馈 *//*
    abstract public FeedbackHandler dealCategoryPicture();

    *//** 处理频道图片关系反馈 *//*
    abstract public FeedbackHandler dealChannelPicture();*/

    /** 处理栏目内容关系反馈 */
    public FeedbackHandler dealCategoryContent() {
        Long categoryContentId = params.getContentId();
        log.info("CategoryContent 《 {}工单 》 {} 反馈， 即将更新  ID in {}, 状态为publish_status = {}, 描述信息为 publish_description = {} ",
                xmlType, result, categoryContentId, status, params.getErrorDescription());
        bmsCategoryContentService.update(Wrappers.lambdaUpdate(BmsCategoryContent.class)
                .set(BmsCategoryContent::getPublishStatus, status)
                .set(BmsCategoryContent::getPublishDescription, params.getErrorDescription())
                .eq(BmsCategoryContent::getId, categoryContentId));
        log.info("修改 CategoryContent ID in {} 发布状态成功，结束", categoryContentId);

        return this;
    }

    /** 处理栏目频道关系反馈 */
    public FeedbackHandler dealCategryChannel() {
        Long categryChannelId = params.getContentId();
        log.info("CategoryContent 《 {}工单 》 {}  反馈， 即将更新  ID in {}, 状态为publish_status = {}, 描述信息为 publish_description = {} ",
                xmlType, result, categryChannelId, status, params.getErrorDescription());
        bmsCategoryChannelService.update(Wrappers.lambdaUpdate(BmsCategoryChannel.class)
                .set(BmsCategoryChannel::getPublishStatus, status)
                .set(BmsCategoryChannel::getPublishDescription, params.getErrorDescription())
                .eq(BmsCategoryChannel::getId, categryChannelId));
        log.info("修改 CategoryContent ID in {} 发布状态成功，结束", categryChannelId);
        return this;
    }

    /** 处理产品包频道关系反馈 */
    public FeedbackHandler dealPackageChannel() {
        Long packageChannelId = params.getContentId();
        log.info("PackageChannel 《 {}工单 》  {}  反馈， 即将更新  ID = {}, 状态为publish_status = {}, 描述信息为 publish_description = {} ",
                xmlType, result, packageChannelId, status, params.getErrorDescription());
        bmsPackageChannelService.update(Wrappers.lambdaUpdate(BmsPackageChannel.class)
                .set(BmsPackageChannel::getPublishStatus, status)
                .set(BmsPackageChannel::getPublishDescription, params.getErrorDescription())
                .eq(BmsPackageChannel::getId, packageChannelId));
        log.info("修改 PackageChannel ID = {} 发布状态成功，结束", packageChannelId);
        return this;
    }

    /** 处理产品包内容关系反馈 */
    public FeedbackHandler dealPackageContent() {
        Long packageContentId = params.getContentId();
        log.info("PackageContent 《 {}工单 》 {} 反馈， 即将更新  ID = {}, 状态为publish_status = {}, 描述信息为 publish_description = {} ",
                xmlType, result, packageContentId, status, params.getErrorDescription());
        bmsPackageContentService.update(Wrappers.lambdaUpdate(BmsPackageContent.class)
                .set(BmsPackageContent::getPublishStatus, status)
                .set(BmsPackageContent::getPublishDescription, params.getErrorDescription())
                .eq(BmsPackageContent::getId, packageContentId));
        log.info("修改 PackageContent ID = {} 发布状态成功，结束", packageContentId);
        return this;
    }

    abstract public ResultData getResult();

    public abstract void excute(ContentTypeEnum contentType, PublishParamsDto params);

    protected void setCpFeedbackFlag(Integer contentType, Long contentId) {
        if (cpIdList.isEmpty()) {
            cpFeedbackFlag = null;
            return;
        }
        long count;
        /** 子集 */
        if (contentType.equals(ContentTypeEnum.SUBSET.getValue())) {
            count = bmsProgramService.count(Wrappers.lambdaQuery(BmsProgram.class)
                    .eq(BmsProgram::getId, contentId)
                    .in(BmsProgram::getCpId, cpIdList));
        } else {
            count = bmsContentService.count(Wrappers.lambdaQuery(BmsContent.class)
                    .eq(BmsContent::getId, contentId)
                    .in(BmsContent::getCpId, cpIdList));
        }
        /** 如果不是需要上报CP的内容则不修改CPFeedbackFlag */
        if (count == 0) {
            cpFeedbackFlag = null;
        }
        log.info("是否上报CP：" + cpFeedbackFlag);
    }
}
