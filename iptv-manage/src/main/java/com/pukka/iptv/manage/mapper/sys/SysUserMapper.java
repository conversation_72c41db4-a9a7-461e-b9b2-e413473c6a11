package com.pukka.iptv.manage.mapper.sys;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.pukka.iptv.common.data.model.sys.SysUser;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;


@Mapper
public interface SysUserMapper extends BaseMapper<SysUser>{


    List<SysUser> listUserRoleTenant(@Param("userIds") List<Long> userIds);
}
