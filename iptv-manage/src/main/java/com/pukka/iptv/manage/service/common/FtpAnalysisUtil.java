package com.pukka.iptv.manage.service.common;

import com.pukka.iptv.manage.util.URLUtil;
import lombok.extern.slf4j.Slf4j;

import java.util.Map;

@Slf4j
public class FtpAnalysisUtil {

    /**
     * 删除ftp视频图片
     *
     * @param fileUrl
     * @return
     */
    public static boolean DelFtp(String fileUrl) {
        boolean success = false;
        try {
//            Map<String, String> map = URLUtil.parseFtp(fileUrl);
            FtpUtil ftpUtil = new FtpUtil(fileUrl);
            //删除存储
//            success = ftpUtil.deleteFile(map.get("path"));
            success = ftpUtil.deleteFile();
        } catch (Exception e) {
            log.error("error ftp", e);
        }
        return success;
    }

    public static boolean IsExistPath(String fileUrl){
        boolean success = false;
        try {
//            Map<String, String> map = URLUtil.parseFtp(fileUrl);
            FtpUtil ftpUtil = new FtpUtil(fileUrl);
            //删除存储
//            success = ftpUtil.deleteFile(map.get("path"));
            success = ftpUtil.checkFileExists();
        } catch (Exception e) {
            log.error("error ftp", e);
        }
        return success;
    }

}
