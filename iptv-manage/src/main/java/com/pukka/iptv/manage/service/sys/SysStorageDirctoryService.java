package com.pukka.iptv.manage.service.sys;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.pukka.iptv.common.data.model.sys.SysStorageDirctory;

import java.util.List;

/**
 *
 * @author: zhengcl
 * @date: 2021-9-8 18:12:15
 */

public interface SysStorageDirctoryService extends IService<SysStorageDirctory> {

    Page<SysStorageDirctory> page(Page<SysStorageDirctory> page, SysStorageDirctory sysStorageDirctory);

    boolean removeCacheAndDB(List<Long> idList);

    boolean removeCacheAndDB(Long id);

    boolean updateCacheAndDB(SysStorageDirctory storageDirctory);

    boolean saveToCacheAndDB(SysStorageDirctory entity);
}


