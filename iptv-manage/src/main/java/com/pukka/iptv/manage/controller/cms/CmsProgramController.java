package com.pukka.iptv.manage.controller.cms;

import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.enums.CellDataTypeEnum;
import com.alibaba.excel.exception.ExcelDataConvertException;
import com.alibaba.excel.metadata.data.CellData;
import com.alibaba.excel.read.builder.ExcelReaderBuilder;
import com.pukka.iptv.common.base.enums.CommonResponseEnum;
import com.pukka.iptv.common.base.enums.MovieTypeEnums;
import com.pukka.iptv.common.base.exception.BizException;
import com.pukka.iptv.common.base.vo.CommonResponse;
import com.pukka.iptv.common.core.constant.SymbolConstant;
import com.pukka.iptv.common.data.dto.CmsProgramDto;
import com.pukka.iptv.common.data.dto.SimpleSetExcelDto;
import com.pukka.iptv.common.data.dto.SubSetExcelDto;
import com.pukka.iptv.common.data.group.GetMovieGroup;
import com.pukka.iptv.common.data.model.cms.CmsMovie;
import com.pukka.iptv.common.data.model.cms.CmsProgram;
import com.pukka.iptv.common.data.model.statistics.StatisticsIn;
import com.pukka.iptv.common.data.model.statistics.StatisticsInAgain;
import com.pukka.iptv.common.data.model.statistics.StatisticsInCheck;
import com.pukka.iptv.common.data.model.statistics.StatisticsInFinal;
import com.pukka.iptv.common.data.vo.IdVO;
import com.pukka.iptv.common.data.vo.req.StatisticsInVo;
import com.pukka.iptv.common.log.annotation.SysLog;
import com.pukka.iptv.common.log.enums.OperateObjectEnum;
import com.pukka.iptv.common.log.enums.OperateTypeEnum;
import com.pukka.iptv.manage.listener.excel.SimpleSetReadListener;
import com.pukka.iptv.manage.listener.excel.SubSetReadListener;
import com.pukka.iptv.manage.service.cms.CmsProgramService;
import feign.Request;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.util.Arrays;
import java.util.List;

/**
 * @author: luo
 * @date: 2021-8-27 9:19:00
 */
@Slf4j
@RestController
@AllArgsConstructor
@RequestMapping(value = "/cmsProgram", produces = MediaType.APPLICATION_JSON_VALUE)
@Api(tags = "cmsProgram管理")
public class CmsProgramController {

    @Autowired
    private CmsProgramService cmsProgramService;

    @Autowired
    private SimpleSetReadListener simpleSetReadListener;

    @Autowired
    private SubSetReadListener subSetReadListener;

    @SysLog(objectType = OperateObjectEnum.CMS_PROGRAM, operateType = OperateTypeEnum.SAVE, objectIds = "#cmsProgramDto.id", objectNames = "#cmsProgramDto.name")
    @ApiOperation(value = "单集新增")
    @PostMapping("/simpleset/save")
    public CommonResponse save(@RequestBody CmsProgramDto cmsProgramDto) {
        cmsProgramDto.validRequestResource();
        cmsProgramDto.validSimpleSave();
        return cmsProgramService.news(cmsProgramDto);
    }

    @SysLog(objectType = OperateObjectEnum.CMS_PROGRAM, operateType = OperateTypeEnum.UPDATE, objectIds = "#cmsProgramDto.id", objectNames = "#cmsProgramDto.name")
    @ApiOperation(value = "单集编辑")
    @PutMapping("simpleset/update")
    public CommonResponse update(@RequestBody CmsProgramDto cmsProgramDto) {
        cmsProgramDto.validRequestResource();
        cmsProgramDto.validSimpleUpdate();
        return cmsProgramService.up(cmsProgramDto);
    }

    @SysLog(objectType = OperateObjectEnum.CMS_PROGRAM, operateType = OperateTypeEnum.UPDATE, objectIds = "#cmsProgramDto.id", objectNames = "#cmsProgramDto.name")
    @ApiOperation(value = "单集编辑(不更新视频媒介信息)")
    @PutMapping("simpleset/updateWithoutMov")
    public CommonResponse updateWithoutMov(@RequestBody CmsProgramDto cmsProgramDto) {
        cmsProgramDto.validRequestResource();
        cmsProgramDto.validSimpleUpdate();
        return cmsProgramService.upWithoutMov(cmsProgramDto);
    }

    @SysLog(objectType = OperateObjectEnum.CMS_PROGRAM, operateType = OperateTypeEnum.LOCK_OR_UNLOCK, objectIds = "#cmsProgramDto.ids")
    @ApiOperation(value = "批量锁定")
    @PutMapping("batch/lock")
    public CommonResponse batchLock(@RequestBody CmsProgramDto cmsProgramDto) {
        return cmsProgramService.batchLock(cmsProgramDto.getIds(), cmsProgramDto.getLockStatus().toString());
    }

    @SysLog(objectType = OperateObjectEnum.CMS_PROGRAM, operateType = OperateTypeEnum.DELETE, objectIds = "#cmsProgramDto.ids")
    @ApiOperation(value = "批量删除")
    @DeleteMapping("/deleteByIds")
    public CommonResponse deleteByIds(@RequestBody CmsProgramDto cmsProgramDto) {
        return cmsProgramService.del(cmsProgramDto.getIds());
    }

    @SysLog(objectType = OperateObjectEnum.CMS_PROGRAM, operateType = OperateTypeEnum.DELETE, objectIds = "#cmsProgramDto.ids")
    @ApiOperation(value = "删除编目及介质")
    @DeleteMapping("/deleteMov")
    public CommonResponse deleteMov(@RequestBody CmsProgramDto cmsProgramDto) {
        return cmsProgramService.deleteMov(cmsProgramDto.getIds());
    }

    @SysLog(objectType = OperateObjectEnum.CMS_PROGRAM, operateType = OperateTypeEnum.UPDATE, objectIds = "#cmsProgram.ids")
    @ApiOperation(value = "批量元数据修改")
    @PutMapping("/updateByIds")
    public CommonResponse updateByIds(@RequestBody CmsProgramDto cmsProgram) {
        return cmsProgramService.updateAll(cmsProgram);
    }

    @SysLog(objectType = OperateObjectEnum.CMS_PROGRAM, operateType = OperateTypeEnum.SAVE)
    @ApiOperation(value = "模板导入")
    @PostMapping("/import")
    public CommonResponse importExcel(@RequestParam("file") MultipartFile file) {
        String errorMsg = null;
        try {
            ExcelReaderBuilder workBook = EasyExcel.read(file.getInputStream(), SimpleSetExcelDto.class, simpleSetReadListener);
            workBook.sheet().doRead();
            return CommonResponse.success("上传成功");
        } catch (Exception e) {
            errorMsg = e.getMessage();
            if (e.getCause() instanceof ExcelDataConvertException) {
                ExcelDataConvertException excelDataConvertException = (ExcelDataConvertException) e.getCause();
                String cellMsg = "";
                CellData cellData = excelDataConvertException.getCellData();
                //这里有一个celldatatype的枚举值,用来判断CellData的数据类型
                CellDataTypeEnum type = cellData.getType();
                if (type.equals(CellDataTypeEnum.NUMBER)) {
                    cellMsg = cellData.getNumberValue().toString();
                } else if (type.equals(CellDataTypeEnum.STRING)) {
                    cellMsg = cellData.getStringValue();
                } else if (type.equals(CellDataTypeEnum.BOOLEAN)) {
                    cellMsg = cellData.getBooleanValue().toString();
                }
                errorMsg = String.format("Excel表格:第%s行,第%s列,数据值为:%s,该数据值不符合要求,请检验后重新导入 请检查其他的记录是否有同类型的错误", excelDataConvertException.getRowIndex() + 1, excelDataConvertException.getColumnIndex(), cellMsg);
                log.error(errorMsg);
            }
            log.error("数据导入失败", e);
        }
        return CommonResponse.general(CommonResponseEnum.FAIL, errorMsg);
    }


    //========================================子集========================================================
    @SysLog(objectType = OperateObjectEnum.CMS_PROGRAM, operateType = OperateTypeEnum.SAVE, objectIds = "#cmsProgramDto.id", objectNames = "#cmsProgramDto.name")
    @ApiOperation(value = "子集新增")
    @PostMapping("/subset/save")
    public CommonResponse subsetSave(@RequestBody CmsProgramDto cmsProgramDto) {
        cmsProgramDto.validRequestResource();
        cmsProgramDto.validSubsetSave();
        return cmsProgramService.subsetSave(cmsProgramDto);
    }

    @SysLog(objectType = OperateObjectEnum.CMS_PROGRAM, operateType = OperateTypeEnum.DELETE, objectIds = "#cmsProgramDto.ids")
    @ApiOperation(value = "子集批量删除")
    @DeleteMapping("/subset/deleteByIds")
    public CommonResponse subsetDeleteByIds(@RequestBody CmsProgramDto cmsProgramDto) {
        return cmsProgramService.subsetDel(cmsProgramDto.getIds(), cmsProgramDto.getRequestResource());
    }

    @SysLog(objectType = OperateObjectEnum.CMS_PROGRAM, operateType = OperateTypeEnum.DELETE, objectIds = "#cmsProgramDto.ids")
    @ApiOperation(value = "子集删除编目及介质")
    @DeleteMapping("/subset/deleteMov")
    public CommonResponse subDeleteMov(@RequestBody CmsProgramDto cmsProgramDto) {
        return cmsProgramService.subDeleteMov(cmsProgramDto.getIds());
    }

    @SysLog(objectType = OperateObjectEnum.CMS_PROGRAM, operateType = OperateTypeEnum.UPDATE, objectIds = "#cmsProgramDto.id", objectNames = "#cmsProgramDto.name")
    @ApiOperation(value = "子集编辑")
    @PutMapping("/subset/update")
    public CommonResponse subsetUpdate(@RequestBody CmsProgramDto cmsProgramDto) {
        cmsProgramDto.validRequestResource();
        cmsProgramDto.validSubsetUpdate();
        return cmsProgramService.subsetUp(cmsProgramDto);
    }

    @SysLog(objectType = OperateObjectEnum.CMS_PROGRAM, operateType = OperateTypeEnum.UPDATE, objectIds = "#cmsProgramDto.id", objectNames = "#cmsProgramDto.name")
    @ApiOperation(value = "子集编辑(不更新视频媒介信息)")
    @PutMapping("/subset/updateWithoutMov")
    public CommonResponse subsetUpdateWithoutMov(@RequestBody CmsProgramDto cmsProgramDto) {
        cmsProgramDto.validRequestResource();
        cmsProgramDto.validSubsetUpdate();
        return cmsProgramService.subsetUpWithoutMov(cmsProgramDto);
    }

    @SysLog(objectType = OperateObjectEnum.CMS_PROGRAM, operateType = OperateTypeEnum.UPDATE, objectIds = "#cmsProgramDto.ids")
    @ApiOperation(value = "单集批量转子集")
    @PutMapping("/updateSimpleSet")
    public CommonResponse updateSimpleSet(@RequestBody CmsProgramDto cmsProgramDto) {
        return cmsProgramService.updateSimpleSet(cmsProgramDto.getIds());
    }

    @SysLog(objectType = OperateObjectEnum.CMS_PROGRAM, operateType = OperateTypeEnum.UPDATE, objectIds = "#cmsProgramDto.ids")
    @ApiOperation(value = "子集转单集")
    @PutMapping("/updateSubSet")
    public CommonResponse updateSubSet(@RequestBody CmsProgramDto cmsProgramDto) {
        return cmsProgramService.updateSubSet(cmsProgramDto.getIds());
    }

    @SysLog(objectType = OperateObjectEnum.CMS_PROGRAM, operateType = OperateTypeEnum.SAVE)
    @ApiOperation(value = "子集模板导入")
    @PostMapping("/subset/import")
    public CommonResponse importSubsetExcel(@RequestParam("file") MultipartFile file) {
        String errorMsg = null;
        try {
            ExcelReaderBuilder workBook = EasyExcel.read(file.getInputStream(), SubSetExcelDto.class, subSetReadListener);
            workBook.sheet().doRead();
            return CommonResponse.success("上传成功");
        } catch (Exception e) {
            errorMsg = e.getMessage();
            if (e.getCause() instanceof ExcelDataConvertException) {
                ExcelDataConvertException excelDataConvertException = (ExcelDataConvertException) e.getCause();
                String cellMsg = "";
                CellData cellData = excelDataConvertException.getCellData();
                //这里有一个celldatatype的枚举值,用来判断CellData的数据类型
                CellDataTypeEnum type = cellData.getType();
                if (type.equals(CellDataTypeEnum.NUMBER)) {
                    cellMsg = cellData.getNumberValue().toString();
                } else if (type.equals(CellDataTypeEnum.STRING)) {
                    cellMsg = cellData.getStringValue();
                } else if (type.equals(CellDataTypeEnum.BOOLEAN)) {
                    cellMsg = cellData.getBooleanValue().toString();
                }
                errorMsg = String.format("Excel表格:第%s行,第%s列,数据值为:%s,该数据值不符合要求,请检验后重新导入 请检查其他的记录是否有同类型的错误", excelDataConvertException.getRowIndex() + 1, excelDataConvertException.getColumnIndex(), cellMsg);
                log.error(errorMsg);
            }
            log.error("数据导入失败", e);
        }
        return CommonResponse.general(CommonResponseEnum.FAIL, errorMsg);
    }

    @ApiOperation(value = "详情")
    @GetMapping("/getById")
    public CommonResponse<CmsProgram> getById(@RequestParam(name = "id") Long id) {
        return CommonResponse.success(cmsProgramService.getById(id));
    }

    @ApiOperation(value = "搜索标识")
    @GetMapping("/getBySearchName")
    public CommonResponse<CmsProgram> getBySearchName(@RequestParam(name = "name") String name) {
        return cmsProgramService.getBySearchName(name);
    }


    @GetMapping("/relateRelease")
    public CommonResponse<Boolean> relateRelease(@RequestParam("relateCount") Integer relateCount) {
        return cmsProgramService.relateResource(relateCount, MovieTypeEnums.RELEASE);
    }

    @GetMapping("/relatePreview")
    public CommonResponse<Boolean> relatePreview(@RequestParam("relateCount") Integer relateCount) {
        return cmsProgramService.relateResource(relateCount, MovieTypeEnums.PREVIEW);
    }

    @PostMapping("/programStatistic")
    public CommonResponse<List<StatisticsIn>> programStatistic(@RequestBody StatisticsInVo statisticsInVo, Request.Options options) {
        return cmsProgramService.getProgramStatistic(statisticsInVo);
    }

    @PostMapping("/subStatistic")
    public CommonResponse<List<StatisticsIn>> subStatistic(@RequestBody StatisticsInVo statisticsInVo, Request.Options options) {
        return cmsProgramService.getSubStatistic(statisticsInVo);
    }

    @PostMapping("/subAndProgramStatistic")
    public CommonResponse<List<StatisticsIn>> subAndProgramStatistic(@RequestBody StatisticsInVo statisticsInVo, Request.Options options) {
        return cmsProgramService.getSubAndProgramStatistic(statisticsInVo);
    }

    @PostMapping("/selfProgramStatistic")
    public CommonResponse<List<StatisticsInCheck>> getSelfProgramStatistic(@RequestBody StatisticsInVo statisticsInVo, Request.Options options) {
        return cmsProgramService.getSelfProgramStatistic(statisticsInVo);
    }

    @PostMapping("/selfSubStatistic")
    public CommonResponse<List<StatisticsInCheck>> getSelfSubStatistic(@RequestBody StatisticsInVo statisticsInVo, Request.Options options) {
        return cmsProgramService.getSelfSubStatistic(statisticsInVo);
    }

    @PostMapping("/finalProgramStatistic")
    public CommonResponse<List<StatisticsInFinal>> getFinalProgramStatistic(@RequestBody StatisticsInVo statisticsInVo, Request.Options options) {
        return cmsProgramService.getFinalProgramStatistic(statisticsInVo);
    }

    @PostMapping("/finalSubStatistic")
    public CommonResponse<List<StatisticsInFinal>> getFinalSubStatistic(@RequestBody StatisticsInVo statisticsInVo, Request.Options options) {
        return cmsProgramService.getFinalSubStatistic(statisticsInVo);
    }


    @PostMapping("/againProgramStatistic")
    public CommonResponse<List<StatisticsInAgain>> againProgramStatistic(@RequestBody StatisticsInVo statisticsInVo, Request.Options options) {
        return cmsProgramService.getAgainProgramStatistic(statisticsInVo);
    }

    @PostMapping("/againSubStatistic")
    public CommonResponse<List<StatisticsInAgain>> getAgainSubStatistic(@RequestBody StatisticsInVo statisticsInVo, Request.Options options) {
        return cmsProgramService.getAgainSubStatistic(statisticsInVo);
    }


    @PostMapping("/subset/checkEpisodeIndex")
    public CommonResponse checkEpisodeIndex(@RequestBody CmsProgramDto cmsProgramDto) {
        return cmsProgramService.checkEpisodeIndex(cmsProgramDto);
    }

    /**
     * 介质地址查询
     */
    @GetMapping("/getHttpFileUrl")
    public CommonResponse<String> getHttpFileUrl(@RequestParam(name = "cmsContentId") Long cmsContentId,
                                                 @RequestParam(name = "contentType") Integer contentType,
                                                 @RequestParam(name = "cpId") Long cpId) {
        return cmsProgramService.getHttpFileUrl(cmsContentId, contentType, cpId);
    }

    /**
     * 通过HTTP POST请求获取媒资文件的URL列表。
     *
     * @param cmsContentIdList 包含媒资内容ID的列表，作为请求体发送。
     * @return 返回一个CommonResponse对象，其中包含了媒资视频信息的列表。如果获取成功，则列表中包含相应的CmsMovie对象；如果失败，则返回错误信息。
     */
    @PostMapping("/getHttpFileUrls")
    public CommonResponse<List<CmsMovie>> getHttpFileUrls(@RequestBody List<String> cmsContentIdList) {
        return CommonResponse.success(cmsProgramService.getHttpFileUrls(cmsContentIdList));
    }

    /**
     * 更新CMS节目状态
     *
     * @param cmsProgramList 包含需要更新状态的CMS节目列表
     * @return 返回操作结果的通用响应对象，如果更新成功则返回成功信息，否则返回失败信息
     */
    @PutMapping("/updateCmsProgramStatus")
    public CommonResponse<Boolean> updateCmsProgramStatus(@RequestBody List<CmsProgram> cmsProgramList) {
        boolean updateBatchById = cmsProgramService.updateCmsProgramStatus(cmsProgramList);
        if (!updateBatchById) {
            log.error("更新CMS节目状态失败，保存视频审核记录时发生错误: {}", "保存失败");
            return CommonResponse.fail("更新CMS节目状态失败");
        }
        return CommonResponse.success(true);
    }

    /**
     * 更新审核状态
     *
     * @param idVo
     * @return
     */
    @PutMapping("/updateAuditStatus")
    public CommonResponse<Boolean> updateAuditStatus(@RequestBody IdVO idVo) {
        if (ObjectUtils.isEmpty(idVo.getIds())) {
            log.error("更新审核状态失败，保存视频审核记录时发生错误: {}", "参数不能为空");
            throw new BizException("参数不能为空");
        }
        //逗号分割转换集合
        try {
            List<String> list = Arrays.asList(idVo.getIds().split(SymbolConstant.COMMA));
            return CommonResponse.success(cmsProgramService.updateAuditStatus(list));
        } catch (Exception e) {
            log.error("更新审核状态失败，保存视频审核记录时发生错误: {}", e.getMessage());
            throw new BizException("传入格式错误，更新审核状态失败");
        }
    }
}