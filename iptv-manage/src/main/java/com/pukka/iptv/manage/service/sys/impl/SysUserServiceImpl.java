package com.pukka.iptv.manage.service.sys.impl;

import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.pukka.iptv.common.base.constant.RedisKeyConstants;
import com.pukka.iptv.common.base.enums.CommonResponseEnum;
import com.pukka.iptv.common.base.enums.StatusEnum;
import com.pukka.iptv.common.base.enums.SysUserTypeEnum;
import com.pukka.iptv.common.base.enums.SystemStatusEnum;
import com.pukka.iptv.common.base.exception.CommonResponseException;
import com.pukka.iptv.common.base.vo.SecurityUser;
import com.pukka.iptv.common.core.util.CollectionUtil;
import com.pukka.iptv.common.core.util.SafeUtil;
import com.pukka.iptv.common.core.util.StringUtils;
import com.pukka.iptv.common.data.model.sys.*;
import com.pukka.iptv.common.data.params.SysUserParam;
import com.pukka.iptv.common.data.params.SysUserStateParam;
import com.pukka.iptv.common.redis.service.RedisService;
import com.pukka.iptv.manage.mapper.sys.SysRoleMapper;
import com.pukka.iptv.manage.mapper.sys.SysTenantMapper;
import com.pukka.iptv.manage.mapper.sys.SysUserMapper;
import com.pukka.iptv.manage.service.cache.CacheServiceImpl;
import com.pukka.iptv.manage.service.sys.SysUserRoleService;
import com.pukka.iptv.manage.service.sys.SysUserService;
import com.pukka.iptv.manage.service.sys.SysUserTenantService;
import io.jsonwebtoken.lang.Collections;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.crypto.bcrypt.BCryptPasswordEncoder;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.stream.Collectors;

/**
 *
 * @author: zhengcl
 * @date: 2021-8-17 16:48:35
 */

@Service
public class SysUserServiceImpl extends ServiceImpl<SysUserMapper, SysUser> implements SysUserService {

	@Autowired
	private RedisService redisService;
	@Autowired
	private CacheServiceImpl cacheService;
	@Autowired
	private SysUserMapper sysUserMapper;
	@Autowired
	private SysRoleMapper sysRoleMapper;
	@Autowired
	private SysTenantMapper sysTenantMapper;
	@Autowired
	private SysUserRoleService sysUserRoleService;
	@Autowired
	private SysUserTenantService sysUserTenantService;

	@Override
	public SecurityUser getByUsername(String username) {
		SysUser sysUser = getSysUserByUsername(username);
		if(sysUser != null){
			SecurityUser securityUser = JSON.parseObject(JSON.toJSONString(sysUser),SecurityUser.class);
			return securityUser;
		}
		return null;
	}

	private SysUser getSysUserByUsername(String username){
		QueryWrapper<SysUser> query = new QueryWrapper<>();
		query.lambda().eq(SysUser::getUsername, username).last(" limit 1");
		return this.sysUserMapper.selectOne(query);

	}

	@Override
	@Transactional(rollbackFor = Exception.class)
	public boolean save(SysUser sysUser) {
//		本层方法调用必须通过service,否则不会滚
//		((SysUserService) AopContext.currentProxy()).save(sysUser);
		sysUser.valid();

		if (StringUtils.isContainsChinese(sysUser.getUsername()) || sysUser.getUsername().contains(" ")) {
			throw new CommonResponseException(CommonResponseEnum.ACCOUNT_INVALID,"账号包含中文或空格");
		}
		//检查用户账号是否存在
		SysUser otherUser = getSysUserByUsername(sysUser.getUsername());
		if (!Objects.isNull(otherUser)){
			throw new CommonResponseException(CommonResponseEnum.FAIL,"账号已存在");
		}
		sysUser.setPassword(new BCryptPasswordEncoder().encode(sysUser.getPassword()));
		sysUser.setStatus(StatusEnum.COME.getCode());
		sysUserMapper.insert(sysUser);
		try {
			cacheService.cacheSysUser(java.util.Collections.singletonList(sysUser));
		} catch (Exception exception) {
			log.error("新增缓存用户失败,Exception:", exception);
			throw new CommonResponseException(CommonResponseEnum.CREATE_ERROR, "新增缓存用户失败,redis异常:" + exception.getMessage());
		}
		if(SysUserTypeEnum.SUPER_ADMIN.getCode().equals(sysUser.getType())){
			return true;
		}
		Set<Long> roleIdList = new HashSet<>();
		if(org.springframework.util.StringUtils.hasLength(sysUser.getRoleIds())){
			roleIdList = CollectionUtil.stringToLongSet(sysUser.getRoleIds(),",");
		}
		Set<Long> tenantIdList = new HashSet<>();
		if(org.springframework.util.StringUtils.hasLength(sysUser.getTenantIds())){
			tenantIdList = CollectionUtil.stringToLongSet(sysUser.getTenantIds(),",");
		}
		for (Long roleId : roleIdList) {
			SysRole sysRole = sysRoleMapper.selectById(roleId);
			if (Objects.isNull(sysRole)) {
				throw new CommonResponseException(CommonResponseEnum.FAIL, "角色不存在");
			}
			SysUserRole sysUserRole = new SysUserRole();
			sysUserRole.setUserId(sysUser.getId());
			sysUserRole.setRoleId(roleId);
			sysUserRoleService.save(sysUserRole);
		}
		for (Long tenantId : tenantIdList) {
			SysTenant sysTenant = sysTenantMapper.selectById(tenantId);
			if (Objects.isNull(sysTenant)) {
				throw new CommonResponseException(CommonResponseEnum.FAIL, "租户不存在");
			}
			SysUserTenant sysUserTenant = new SysUserTenant();
			sysUserTenant.setUserId(sysUser.getId());
			sysUserTenant.setTenantId(tenantId);
			sysUserTenantService.save(sysUserTenant);
		}
		return true;
	}


	@Override
	@Transactional(rollbackFor = Exception.class)
	public boolean update(SysUser sysUser) {
//		本层方法调用必须通过service,否则不会滚
//		((SysUserService) AopContext.currentProxy()).save(sysUser);
		sysUser.validUpdate();
		//检查用户账号是否存在
		if (StringUtils.isContainsChinese(sysUser.getUsername()) || sysUser.getUsername().contains(" ")) {
			throw new CommonResponseException(CommonResponseEnum.ACCOUNT_INVALID,"账号包含中文或空格");
		}

		long count = count(Wrappers.lambdaQuery(SysUser.class).eq(SysUser::getName, sysUser.getName()).ne(SysUser::getId, sysUser.getId()));
		if (count >= 1){
			throw new CommonResponseException(CommonResponseEnum.FAIL,"账号已存在");
		}
		Set<Long> roleIdList = new HashSet<>();
		if(org.springframework.util.StringUtils.hasLength(sysUser.getRoleIds())){
			roleIdList = CollectionUtil.stringToLongSet(sysUser.getRoleIds(),",");
		}
		Set<Long> tenantIdList = new HashSet<>();
		if(org.springframework.util.StringUtils.hasLength(sysUser.getTenantIds())){
			tenantIdList = CollectionUtil.stringToLongSet(sysUser.getTenantIds(),",");
		}
		SysUser sysUserInDb = sysUserMapper.selectById(sysUser.getId());
		if (Objects.isNull(sysUserInDb)) {
			throw new CommonResponseException(CommonResponseEnum.FAIL, "用户不存在");
		} else {
			Set<Long> containsRoleIds = new HashSet<>();
			Set<Long> containsTenantIds = new HashSet<>();
			Set<Long> removeUserRoleIds = new HashSet<>();
			Set<Long> removeUserTenantIds = new HashSet<>();
			List<SysUserRole> sysUserRoles = this.sysUserRoleService.listByUserId(sysUser.getId());
			if (!Collections.isEmpty(sysUserRoles)) {
				for (SysUserRole sysUserRole: sysUserRoles) {
					if (roleIdList.contains(sysUserRole.getRoleId())) {
						containsRoleIds.add(sysUserRole.getRoleId());
					}else{
						removeUserRoleIds.add(sysUserRole.getId());
					}
				}
			}
			List<SysUserTenant> sysUserTenants = this.sysUserTenantService.listByUserId(sysUser.getId());
			if (!Collections.isEmpty(sysUserTenants)) {
				for (SysUserTenant sysUserTenant: sysUserTenants) {
					if (tenantIdList.contains(sysUserTenant.getTenantId())) {
						containsTenantIds.add(sysUserTenant.getTenantId());
					}else{
						removeUserTenantIds.add(sysUserTenant.getId());
					}
				}
			}
			roleIdList.removeAll(containsRoleIds);
			tenantIdList.removeAll(containsTenantIds);
			if (!Collections.isEmpty(removeUserRoleIds)) {
				sysUserRoleService.removeByIds(removeUserRoleIds);
			}
			if (!Collections.isEmpty(removeUserTenantIds)) {
				sysUserTenantService.removeByIds(removeUserTenantIds);
			}
			sysUserMapper.updateById(sysUser);
			try {
				cacheService.cacheSysUser(java.util.Collections.singletonList(sysUser));
			} catch (Exception exception) {
				log.error("更新用户缓存失败,Exception:", exception);
				throw new CommonResponseException("redis更新用户缓存异常：" + exception.getMessage());
			}
			for (Long roleId : roleIdList) {
				SysRole sysRole = sysRoleMapper.selectById(roleId);
				if (Objects.isNull(sysRole)) {
					throw new CommonResponseException(CommonResponseEnum.FAIL, "角色不存在");
				}
				SysUserRole sysUserRole = new SysUserRole();
				sysUserRole.setUserId(sysUser.getId());
				sysUserRole.setRoleId(roleId);
				sysUserRoleService.save(sysUserRole);
			}
			for (Long tenantId : tenantIdList) {
				SysTenant sysTenant = sysTenantMapper.selectById(tenantId);
				if (Objects.isNull(sysTenant)) {
					throw new CommonResponseException(CommonResponseEnum.FAIL, "租户不存在");
				}
				SysUserTenant sysUserTenant = new SysUserTenant();
				sysUserTenant.setUserId(sysUser.getId());
				sysUserTenant.setTenantId(tenantId);
				sysUserTenantService.save(sysUserTenant);
			}
		}
		return true;
	}

	@Override
	public boolean updateStatusById(SysUser sysUser) {
		if(sysUser == null || sysUser.getStatus() == null){
			throw new CommonResponseException(CommonResponseEnum.ERROR_PARAMETER);
		}
		updateById(sysUser);
		return true;
	}

	@Override
	public Page<SysUser> pageLike(Page<SysUser> page, SysUser sysUser) {
		Page<SysUser> sysUserPage = page(page, Wrappers.lambdaQuery(SysUser.class)
				.like(!SafeUtil.getString(sysUser.getName()).isEmpty(), SysUser::getName, sysUser.getName())
				.like(!SafeUtil.getString(sysUser.getUsername()).isEmpty(), SysUser::getUsername, sysUser.getUsername())
				.orderByDesc(SysUser::getId)
		);
		List<SysUser> users = sysUserPage.getRecords();
		if (users.isEmpty()) {
			return sysUserPage;
		}
		List<Long> userIds = new ArrayList<>();
		users.forEach(user -> {
			userIds.add(user.getId());
		});
		List<SysUser> userList = sysUserMapper.listUserRoleTenant(userIds);
		userList.forEach(user -> {
			user.setPassword(null);
		});
		sysUserPage.setRecords(userList);
		return sysUserPage;
	}

	@Override
	public boolean updatePassword(SysUserParam param){
		if(param == null || param.getUserId() == null ||
				StringUtils.isEmpty(param.getOldPwd()) || StringUtils.isEmpty(param.getPwd())){
			throw new CommonResponseException(CommonResponseEnum.MISS_PARAMETER);
		}
		SysUser sysUser = sysUserMapper.selectById(param.getUserId());
		if(sysUser == null){
			throw new CommonResponseException(CommonResponseEnum.EMPTY,"操作员不存在");
		}
		//判断用户是否被禁用
		if(!StatusEnum.COME.getCode().equals(sysUser.getStatus())){
			throw new CommonResponseException(CommonResponseEnum.DISABLE_ERROR,"操作员被禁用,不允许修改");
		}
		//判断原始密码是否正确
		if(!new BCryptPasswordEncoder().matches(param.getOldPwd(),sysUser.getPassword())){
			throw new CommonResponseException(CommonResponseEnum.ERROR_PARAMETER,"原始密码不正确");
		}
		update(Wrappers.<SysUser>lambdaUpdate().set(SysUser::getPassword,new BCryptPasswordEncoder().encode(param.getPwd()))
				.eq(SysUser::getId,param.getUserId()));
		return true;
	}

	@Override
	public String resetPassword(List<Long> ids){
		if(ids == null || ids.size() == 0 ){
			throw new CommonResponseException(CommonResponseEnum.MISS_PARAMETER);
		}
		for (Long id : ids) {
			SysUser sysUser = sysUserMapper.selectById(id);
			if(sysUser == null){
				throw new CommonResponseException(CommonResponseEnum.EMPTY,"操作员不存在,ID:" + id );
			}
			//判断用户是否被禁用
			if(!StatusEnum.COME.getCode().equals(sysUser.getStatus())){
				throw new CommonResponseException(CommonResponseEnum.DISABLE_ERROR,sysUser.getName() + "被禁用,不允许修改");
			}
		}
		String pwd = "tv@_123456";
		update(Wrappers.<SysUser>lambdaUpdate().set(SysUser::getPassword,new BCryptPasswordEncoder().encode(pwd))
				.in(SysUser::getId,ids));
		return pwd;
	}

	@Override
	public boolean resetState(SysUserStateParam sysUserStateParam) {

		if(sysUserStateParam.getIds() == null || sysUserStateParam.getIds().size() == 0 ){
			throw new CommonResponseException(CommonResponseEnum.MISS_PARAMETER);
		}
		for (Long id : sysUserStateParam.getIds()) {
			SysUser sysUser = sysUserMapper.selectById(id);
			if(sysUser == null){
				throw new CommonResponseException(CommonResponseEnum.EMPTY,"操作员不存在,ID:" + id );
			}

		}
       if(sysUserStateParam.getState().equals(SystemStatusEnum.COME.getCode())){
		   update(Wrappers.<SysUser>lambdaUpdate().set(SysUser::getStatus, SystemStatusEnum.COME.getCode())
				   .in(SysUser::getId,sysUserStateParam.getIds() ));
	   }else{
		   update(Wrappers.<SysUser>lambdaUpdate().set(SysUser::getStatus, SystemStatusEnum.LOSE.getCode())
				   .in(SysUser::getId,sysUserStateParam.getIds() ));
	   }

		return true;
	}

	@Override
	@Transactional
	public boolean removieUserAndMapping(Long id) {
		sysUserTenantService.remove(Wrappers.lambdaQuery(SysUserTenant.class).eq(SysUserTenant::getUserId, id));
		sysUserRoleService.remove(Wrappers.lambdaQuery(SysUserRole.class).eq(SysUserRole::getUserId, id));
		boolean removeById = removeById(id);
		try{
			redisService.deleteByHashKey(RedisKeyConstants. SYS_USER, id.toString());
		}catch (Exception exception){
			log.error("删除指定用户缓存失败,Exception:{}", exception);
			throw new CommonResponseException("redis删除指定用户缓存异常：" + exception.getMessage());
		}
		return removeById;
	}

	@Override
	@Transactional
	public boolean removieUserAndMappings(List<Long> ids) {
		sysUserTenantService.remove(Wrappers.lambdaQuery(SysUserTenant.class).in(SysUserTenant::getUserId, ids));
		sysUserRoleService.remove(Wrappers.lambdaQuery(SysUserRole.class).in(SysUserRole::getUserId, ids));
		boolean removeByIds = removeByIds(ids);
		try {
			List<String> stringList = ids.stream().map(x -> x + "").collect(Collectors.toList());
			redisService.deleteCacheMap(RedisKeyConstants.SYS_USER, stringList);
		} catch (Exception exception) {
			log.error("批量删除指定用户失败,Exception:{}", exception);
			throw new CommonResponseException("redis批量删除指定用户异常:"+exception.getMessage());
		}
		return removeByIds;
	}

	@Override
	public boolean isExistByUserId(Long userId) {
		return count(Wrappers.<SysUser>lambdaQuery().eq(SysUser::getId, userId).eq(SysUser::getStatus, StatusEnum.COME.getCode())) > 0;
	}

	/**
	 * 是否是超级管理员
	 * @param userId
	 * @return
	 */
	@Override
	public Boolean isSuperAdmin(Long userId) {
		SysUser sysUser = sysUserMapper.selectById(userId);
		if (sysUser == null) {
			return false;
		}
		return SysUserTypeEnum.SUPER_ADMIN.getCode().equals(sysUser.getType());
	}


}


