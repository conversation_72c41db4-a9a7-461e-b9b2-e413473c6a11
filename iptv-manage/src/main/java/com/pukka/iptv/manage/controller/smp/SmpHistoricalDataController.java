package com.pukka.iptv.manage.controller.smp;


import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.pukka.iptv.common.api.feign.smp.SmpHistoricalDataFeignClient;
import com.pukka.iptv.common.base.vo.CommonResponse;
import com.pukka.iptv.common.data.model.smp.CpDto;
import com.pukka.iptv.common.data.model.smp.SmpHistoricalData;
import com.pukka.iptv.common.data.model.smp.SpDto;
import com.pukka.iptv.common.data.vo.smp.SmpHistoricalDataVo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.AllArgsConstructor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.*;



@RestController
@AllArgsConstructor
@RequestMapping(value = "/SmpHistoricalData", produces = MediaType.APPLICATION_JSON_VALUE)
@Api(tags = "smp历史数据")
public class SmpHistoricalDataController {
    @Autowired
    private SmpHistoricalDataFeignClient smpHistoricalDataFeignClient;

    /**
     * 历史数据分页查询
     *
     * @param smpHistoricalDataVo
     * @return
     */
    @ApiOperation(value = "分页")
    @PostMapping("/page")
    public CommonResponse<Page<SmpHistoricalData>> page(@RequestBody SmpHistoricalDataVo smpHistoricalDataVo
                                     ) {
        return  smpHistoricalDataFeignClient.page(smpHistoricalDataVo);
    }

    /**
     * 获取所有CP
     * @param current
     * @param size
     * @return
     */
    @ApiOperation(value = "获取CP列表")
    @GetMapping("/listCp")
    public CommonResponse<Page<CpDto>> listCp(@RequestParam(value = "current", defaultValue = "1") Integer current,
                                              @RequestParam(value = "size", defaultValue = "10") Integer size,
                                              @RequestParam(value = "name",required = false) String name) {
        return  smpHistoricalDataFeignClient.listCp(current,size,name);
    }

    /**
     * 获取详情
     * @return
     */
    @GetMapping("/getByCode")
    public CommonResponse<Page<SpDto>> getByCode(@RequestParam(value = "id",required = false) Integer id,
                                                 @RequestParam(value = "code",required = false) String code,
                                                 @RequestParam(value = "current", defaultValue = "1") Integer current,
                                                 @RequestParam(value = "size", defaultValue = "10")Integer size) {
        return smpHistoricalDataFeignClient.getByCode(id,code,current,size);
    }

}
