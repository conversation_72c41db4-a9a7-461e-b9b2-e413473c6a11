package com.pukka.iptv.manage.service.sys;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.pukka.iptv.common.data.model.sys.SysSp;

import java.util.List;

/**
 *
 * @author: chenyudong
 * @date: 2021-9-2 22:54:04
 */

public interface SysSpService extends IService<SysSp> {

    Page<SysSp> page(Page<SysSp> page, SysSp sysSp);

    /**
     * 通过id删除CP
     *
     * @param id
     * @return
     */
    boolean removeById(Long id);

    /**
     * 通过ids批量删除CP
     *
     * @param ids  id集合
     * @return bool
     */
    boolean removeByIds(List<Long> ids);

    /**
     * 删除mysql&redis数据
     * @param id
     * @return
     */
    boolean removeTwoById(Long id);

    /**
     * 删除mysql&redis数据
     * @param ids
     * @return
     */
    boolean removeTwoByIds(List<Long> ids);

    /**
     * 保存mysql&redis数据
     * @param sysSp
     * @return
     */
    boolean saveToCacheAndDB(SysSp sysSp);

    /**
     * 更新mysql&redis数据
     * @param sysSp
     * @return
     */
    boolean updateToCacheAndDB(SysSp sysSp);

    /**
     * 通过id修改状态
     *
     * @param sysSp
     * @return
     */
    boolean updateStatusById(SysSp sysSp);

    // 通过账户查询sp
    List<SysSp> findSysSpByType(Integer type, Long channelId, String name);

    List<SysSp> listByType(Integer type, Long channelId, String name);
}


