package com.pukka.iptv.manage.controller.bms;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.pukka.iptv.common.base.constant.PublishParamTypeConstants;
import com.pukka.iptv.common.base.enums.ContentTypeEnum;
import com.pukka.iptv.common.base.enums.CpFeedbackEnum;
import com.pukka.iptv.common.base.enums.PriorityEnums;
import com.pukka.iptv.common.base.vo.CommonResponse;
import com.pukka.iptv.common.data.model.OutParamExpand;
import com.pukka.iptv.common.data.model.bms.BmsContent;
import com.pukka.iptv.common.data.model.bms.BmsProgram;
import com.pukka.iptv.common.data.vo.CodeList;
import com.pukka.iptv.common.data.vo.IdList;
import com.pukka.iptv.common.data.vo.IdVO;
import com.pukka.iptv.common.data.vo.req.BmsProgramPublishReq;
import com.pukka.iptv.common.data.vo.req.BmsProgramReq;
import com.pukka.iptv.common.data.vo.req.BmsSubProgramQueryReq;
import com.pukka.iptv.common.data.vo.resp.R;
import com.pukka.iptv.common.log.annotation.SysLog;
import com.pukka.iptv.common.log.enums.OperateObjectEnum;
import com.pukka.iptv.common.log.enums.OperateTypeEnum;
import com.pukka.iptv.manage.service.bms.BmsProgramService;
import com.pukka.iptv.manage.service.bms.BmsPublishParamApi;
import com.pukka.iptv.manage.service.bms.BmsRecycleApi;
import com.pukka.iptv.manage.service.bms.dto.BmsProgramRecycleDto;
import com.pukka.iptv.manage.util.scheduletypeutils.ScheduleTypeFactory;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.HashMap;
import java.util.List;
import java.util.Map;


/**
 * 子集内容表
 *
 * <AUTHOR>
 * @date 2021-09-02 17:02:20
 */

@RestController
@RequestMapping(value = "/bmsProgram", produces = MediaType.APPLICATION_JSON_VALUE)
@Api(tags = "BMS子集管理")
public class BmsProgramController implements BmsPublishParamApi {

    @Autowired
    private BmsProgramService bmsProgramService;
    @Autowired
    @Qualifier("bmsProgramServiceImpl")
    private BmsRecycleApi recycleApi;

    @ApiOperation(value = "子集查询")
    @PostMapping("/page")
    public CommonResponse page(@Valid @RequestBody BmsSubProgramQueryReq req) {
        IPage result = bmsProgramService.pageList(req);
        return R.page(result);
    }

    @ApiOperation(value = "详情")
    @GetMapping("/getById")
    public CommonResponse<BmsProgram> getById(@Valid @RequestParam(name = "id") Long id) {
        return CommonResponse.success(bmsProgramService.getById(id));
    }

    @SysLog(objectType = OperateObjectEnum.BMS_PROGRAM, operateType = OperateTypeEnum.PROGRAM_PUBLISH, objectIds = "#req.ids")
    @ApiOperation(value = "子集发布")
    @PutMapping("/publish")
    public CommonResponse<String> publish(@Valid @RequestBody BmsProgramPublishReq req) {
        Map<String, OutParamExpand> paramMap = new HashMap<>();
        paramMap.put(PublishParamTypeConstants.PRIORITY, new OutParamExpand().setPriority(PriorityEnums.GENERAL.getValue()));
        bmsProgramService.publish(req.getIds(), req.getPid(), req.getDoSchedule() == 1, req.getScheduleTime(), paramMap);
        return R.success("内容发布成功");
    }

    @SysLog(objectType = OperateObjectEnum.BMS_PROGRAM, operateType = OperateTypeEnum.PROGRAM_RECYCLE, objectIds = "#req.ids")
    @ApiOperation(value = "子集回收")
    @PutMapping("/recycle")
    public CommonResponse<String> recycle(@Valid @RequestBody BmsProgramPublishReq req) {
        bmsProgramService.recycle(req.getIds(), req.getPid());
        return R.success("内容发布成功");
    }

    @SysLog(objectType = OperateObjectEnum.BMS_PROGRAM, operateType = OperateTypeEnum.CP_PROGRAM_RECYCLE_ALL, objectIds = "#ids")
    @ApiOperation(value = "子集一键回收(cp侧)")
    @PutMapping("/programRecycleAll")
    public CommonResponse<Boolean> programRecycled(@RequestBody List<Long> ids) {
        bmsProgramService
                //.triggerCpRecycle()
                .noDelete()//子集CP侧的回收不需要 删除授权
                .breakFlag(BmsProgramRecycleDto.class)
                .setArgs(BmsProgramRecycleDto.class, new BmsProgramRecycleDto().setBmsProgramIds(ids).setOnlyProgram(true));
        //设置一键回收标记
        return CommonResponse.success(bmsProgramService.oneKeyRecycle());
    }

    @SysLog(objectType = OperateObjectEnum.BMS_PROGRAM, operateType = OperateTypeEnum.PROGRAM_RESET_PUBLISH_STATUS, objectIds = "#req.ids")
    @ApiOperation(value = "子集重置发布状态")
    @PutMapping("/resetPublishStatus")
    public CommonResponse<Boolean> resetPublishStatus(@Valid @RequestBody BmsProgramReq req) {
        req.validPid();
        return CommonResponse.success(bmsProgramService.resetPublishStatus(req.getIds(), req.getPid()));
    }

    @SysLog(objectType = OperateObjectEnum.BMS_PROGRAM, operateType = OperateTypeEnum.PROGRAM_UPDATE_PUBLISH_STATUS, objectIds = "#req.ids")
    @ApiOperation(value = "子集修改发布状态")
    @PutMapping("/updatePublishStatus")
    public CommonResponse<Boolean> updatePublishStatus(@Valid @RequestBody BmsProgramReq req) {
        req.validUpdatePublishStatus();
        return CommonResponse.success(bmsProgramService.modifyPublishStatus(req.getIds(), req.getPid(), req.getPublishStatus()));
    }

    @SysLog(objectType = OperateObjectEnum.BMS_PROGRAM, operateType = OperateTypeEnum.PROGRAM_ACTIVE_OR_POSITIVE, objectIds = "#req.ids")
    @ApiOperation(value = "子集生效/失效")
    @PutMapping("/status")
    public CommonResponse<Boolean> updateStatus(@Valid @RequestBody BmsProgramReq req) {
        req.validStatus();
        return CommonResponse.success(bmsProgramService.updateStatus(req.getPid(), req.getIds(), req.getStatus(), req.getNeedPublish()));
    }

    @ApiOperation(value = "定时发布")
    @PostMapping("/programSchedulePublish")
    public CommonResponse<Boolean> programSchedulePublish() {
        try {
            Boolean apply = ScheduleTypeFactory.initMode(ContentTypeEnum.SUBSET).apply();
            return CommonResponse.success(apply);
        } catch (Exception e) {
            return CommonResponse.fail("子集定时发布失败");
        }
    }

    @PutMapping("/cpFeedbackFail")
    CommonResponse<Boolean> cpFeedbackFail(@RequestBody IdList idList) {
        return CommonResponse.success(bmsProgramService.updateCpFeedback(idList.getIds(), CpFeedbackEnum.FAILED.getCode()));
    }

    @PutMapping("/cpFeedbackSuccess")
    CommonResponse<Boolean> cpFeedbackSuccess(@RequestBody IdList idList) {
        return CommonResponse.success(bmsProgramService.updateCpFeedback(idList.getIds(), CpFeedbackEnum.FINISHED.getCode()));
    }

    @SysLog(objectType = OperateObjectEnum.BMS_CONTENT, operateType = OperateTypeEnum.CONTENT_CANCEL_TIMED_PUBLISH, objectIds = "#id")
    @ApiOperation(value = "取消定时发布")
    @PutMapping("/cancelTimedPublish")
    public CommonResponse<String> cancelTimedPublish(@RequestBody @Valid IdVO idVO) {
        bmsProgramService.cancelTimedPublish(Long.parseLong(idVO.getId()));
        return R.success("取消定时发布成功");
    }

    @ApiOperation(value = "根据子集id查询剧集信息")
    @GetMapping("/getSeriesBySubProgramIds")
    public CommonResponse<BmsContent> getSeriesBySubProgramIds(@RequestParam(name = "programId") Long programId){
        return CommonResponse.success(bmsProgramService.getSeriesBySubProgramIds(programId));
    }

    @ApiOperation(value = "根据条件查询子集列表")
    @PostMapping("/getByProgramId")
    public CommonResponse<List<BmsProgram>> getByProgramId(@RequestBody CodeList codeList) {
        return CommonResponse.success(bmsProgramService.getByProgramCondition(codeList));
    }
}
