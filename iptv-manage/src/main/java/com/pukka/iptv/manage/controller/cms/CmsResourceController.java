package com.pukka.iptv.manage.controller.cms;

import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.pukka.iptv.common.base.vo.CommonResponse;
import com.pukka.iptv.common.data.dto.CmsResourceDO;
import com.pukka.iptv.common.data.dto.CmsResourceDto;
import com.pukka.iptv.common.data.model.cms.CmsResource;
import com.pukka.iptv.common.data.vo.req.BlobUploadReq;
import com.pukka.iptv.common.data.vo.resp.ResultBlob;
import com.pukka.iptv.common.log.annotation.SysLog;
import com.pukka.iptv.common.log.enums.OperateObjectEnum;
import com.pukka.iptv.common.log.enums.OperateTypeEnum;
import com.pukka.iptv.manage.service.cms.CmsResourceService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.AllArgsConstructor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;

/**
 * @author: zhengcl
 * @date: 2021-9-15 15:13:01
 */

@RestController
@AllArgsConstructor
@RequestMapping(value = "/cmsResource", produces = MediaType.APPLICATION_JSON_VALUE)
@Api(tags = "cmsResource管理")
public class CmsResourceController {

    @Autowired
    private CmsResourceService cmsResourceService;

    @ApiOperation(value = "分页")
    @PostMapping("/page")
    public CommonResponse<Page> page(@RequestBody CmsResourceDO cmsResourceDO) {
        Page page = new Page();
        page.setCurrent(cmsResourceDO.getCurrent());
        page.setSize(cmsResourceDO.getSize());
        return CommonResponse.success(cmsResourceService.pageList(page, cmsResourceDO));
    }

    @ApiOperation(value = "详情")
    @GetMapping("/getById")
    public CommonResponse<CmsResource> getById(@Valid @RequestParam(name = "id", required = true) Long id) {
        return CommonResponse.success(cmsResourceService.getById(id));
    }

    @SysLog(objectType = OperateObjectEnum.CMS_RESOURCE, operateType = OperateTypeEnum.SAVE)
    @ApiOperation(value = "新增")
    @PostMapping("/save")
    public CommonResponse<Boolean> save(@Valid @RequestBody CmsResource cmsResource) {
        return CommonResponse.success(cmsResourceService.save(cmsResource));
    }

    @SysLog(objectType = OperateObjectEnum.CMS_RESOURCE, operateType = OperateTypeEnum.UPDATE)
    @ApiOperation(value = "修改")
    @PutMapping("/update")
    public CommonResponse<Boolean> updateById(@Valid @RequestBody CmsResource cmsResource) {
        return CommonResponse.success(cmsResourceService.updateById(cmsResource));
    }

    @SysLog(objectType = OperateObjectEnum.CMS_RESOURCE, operateType = OperateTypeEnum.DELETE)
    @ApiOperation(value = "删除")
    @DeleteMapping("/deleteById")
    public CommonResponse<Boolean> deleteById(@RequestBody CmsResource cmsResource) {
        return CommonResponse.success(cmsResourceService.removeById(cmsResource.getId()));
    }

    @SysLog(objectType = OperateObjectEnum.CMS_RESOURCE, operateType = OperateTypeEnum.DELETE)
    @ApiOperation(value = "批量删除")
    @DeleteMapping("/deleteByIds")
    public CommonResponse<Boolean> deleteByIds(@RequestBody CmsResourceDto cmsResourceDto) {
        return cmsResourceService.del(cmsResourceDto.getIds());
    }

    @SysLog(objectType = OperateObjectEnum.CMS_RESOURCE, operateType = OperateTypeEnum.DELETE)
    @ApiOperation(value = "批量删除只删除介质")
    @DeleteMapping("/deleteByIdsOnlyTS")
    public CommonResponse<Boolean> deleteByIdsOnlyTS(@RequestBody CmsResourceDto cmsResourceDto) {
        return cmsResourceService.updateStatus(cmsResourceDto.getIds());
//        return cmsResourceService.delOnlyTS(cmsResourceDto.getIds());
    }

    @SysLog(objectType = OperateObjectEnum.CMS_RESOURCE, operateType = OperateTypeEnum.DELETE)
    @ApiOperation(value = "批量删除介质任务")
    @GetMapping("/deleteByIdsOnlyTSBySelect")
    public CommonResponse<Boolean> deleteByIdsOnlyTSBySelect() {
        return cmsResourceService.delOnlyTsBySelect();
    }

    @SysLog(objectType = OperateObjectEnum.CMS_RESOURCE, operateType = OperateTypeEnum.UPDATE, objectIds = "#cmsResourceDto.ids")
    @ApiOperation(value = "设为正片/预览片")
    @PutMapping("/updateStatus")
    public CommonResponse<Boolean> updateStatus(@RequestBody CmsResourceDto cmsResourceDto) {
        return cmsResourceService.updateStatus(cmsResourceDto.getIds(), cmsResourceDto.getType().toString());
    }

    @ApiOperation(value = "关联正片/预览片查询")
    @GetMapping("/movPage")
    public CommonResponse<Page> movPage(@Valid Page page, CmsResourceDto cmsResource) {
        cmsResource.validResourceSave();
        return CommonResponse.success(cmsResourceService.movPageList(page, cmsResource));
    }

    @ApiOperation(value = "查询检查")
    @GetMapping("/check")
    public CommonResponse<Boolean> check(@RequestParam("contentId") Long contentId, @RequestParam("type") Integer type,@RequestParam("saveOrUpStatus") Integer saveOrUpStatus) {
        return CommonResponse.success(cmsResourceService.check(contentId, type,saveOrUpStatus));
    }

    @SysLog(objectType = OperateObjectEnum.CMS_RESOURCE, operateType = OperateTypeEnum.DELETE, objectIds = "#cmsResource.id")
    @ApiOperation(value = "删除(解绑关联关系)")
    @DeleteMapping("/untie")
    public CommonResponse untie(@RequestBody CmsResource cmsResource) {
        cmsResource.validResourceUpdate();
        return CommonResponse.success(cmsResourceService.untie(cmsResource));
    }

    @ApiOperation(value = "视频列表编辑回显")
    @GetMapping("/getMovList")
    public CommonResponse getMovList(@RequestParam("contentId") String contentId, @RequestParam("type") Integer type) {
        return cmsResourceService.getMovList(contentId, type);
    }

    @ApiOperation(value = "视频播放接口")
    @GetMapping("/selectMov")
    public CommonResponse selectMov(@RequestParam("id") Long id) {
        return cmsResourceService.selectMov(id);
    }

    @ApiOperation(value = "客户端视频下载上报接口")
    @PostMapping("/api/clientBlobUpload")
    public ResultBlob blobUpload(@Valid @RequestBody BlobUploadReq blobUploadReq) {
        return cmsResourceService.clientBlobUpload(blobUploadReq);
    }


    @ApiOperation(value = "视频下载上报接口")
    @PostMapping("/blobUpload")
    public CommonResponse<Boolean> blobUpload(@Valid @RequestBody CmsResource cmsResource) {
        return CommonResponse.success(cmsResourceService.blobUpload(cmsResource));
    }
}
