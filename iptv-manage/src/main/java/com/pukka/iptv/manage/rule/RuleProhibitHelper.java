package com.pukka.iptv.manage.rule;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.pukka.iptv.common.base.constant.RedisKeyConstants;
import com.pukka.iptv.common.base.enums.*;
import com.pukka.iptv.common.data.model.bms.BmsContent;
import com.pukka.iptv.common.data.model.cms.CmsProgram;
import com.pukka.iptv.common.data.model.cms.CmsSeries;
import com.pukka.iptv.common.data.model.copyright.RuleProhibit;
import com.pukka.iptv.common.data.model.copyright.RuleProhibitResponse;
import com.pukka.iptv.common.data.model.sys.SysCp;
import com.pukka.iptv.common.data.vo.cms.CmsProgramVO;
import com.pukka.iptv.common.data.vo.cms.CmsSeriesVO;
import com.pukka.iptv.common.data.vo.copyright.CmsProhibitListVo;
import com.pukka.iptv.common.data.vo.copyright.ContentIdAndTypeVo;
import com.pukka.iptv.common.data.vo.copyright.RuleProhibitVo;
import com.pukka.iptv.common.redis.service.RedisService;
import com.pukka.iptv.manage.config.RuleProhibitConfig;
import com.pukka.iptv.manage.rule.cache.RuleCacheImpl;
import com.pukka.iptv.manage.rule.strategy.IRuleStrategyHandler;
import com.pukka.iptv.manage.rule.strategy.RuleStrategyFactory;
import com.pukka.iptv.manage.rule.util.RuleCopyPropertiesUtil;
import com.pukka.iptv.manage.service.bms.BmsContentService;
import com.pukka.iptv.manage.service.cms.CmsProgramService;
import com.pukka.iptv.manage.service.copyright.prohibit.CmsProhibitService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;

/**
 * @Author: chiron
 * @Date: 2022/08/03/17:12
 * @Description: 违禁检测
 */
@Slf4j
@Component
public class RuleProhibitHelper implements IRuleProhibitHelper {


    @Autowired
    private RuleCacheImpl ruleCache;
    @Autowired
    private RedisService redisService;
    @Autowired
    private BmsContentService bmsContentService;
    @Autowired
    private CmsProgramService cmsProgramService;
    @Autowired
    private RuleProhibitConfig ruleProhibitConfig;
    @Autowired
    private CmsProhibitService cmsProhibitService;
    @Autowired
    private RuleCopyPropertiesUtil copyProperties;
    @Autowired
    private RuleStrategyFactory ruleStrategyFactory;

    /**
     * 校验cp违禁触发点是否合法
     *
     * @param ruleProhibit
     * @param prohibitPointEnum
     * @return
     */
    private RuleProhibitResponse illegalExamine(RuleProhibitVo ruleProhibit, ProhibitPointEnum prohibitPointEnum) {
        RuleProhibitResponse response = new RuleProhibitResponse();
        if (ObjectUtils.isNotEmpty(ruleProhibit) && ObjectUtils.isNotEmpty(ruleProhibit.getCpId())) {
            SysCp sysCp = redisService.getCacheMapValue(RedisKeyConstants.SYS_CP, String.valueOf(ruleProhibit.getCpId()));
            //校验cp是否跳过违禁检测
            if (ObjectUtils.isNotEmpty(sysCp) && !ProhibitSkipEnum.SKIP.getValue().equals(sysCp.getSkipProhibit())) {
                //校验cp监测点是否符合
                if (prohibitPointEnum.getValue().equals(sysCp.getPointProhibit())) {
                    return response.setResult(true);
                } else if (prohibitPointEnum.getValue().equals(ProhibitPointEnum.OTHER.getValue())
                        || prohibitPointEnum.getValue().equals(ProhibitPointEnum.UPDATE.getValue())) {
                    log.info("违禁检测 -----> 当前媒资违禁监测点信息为:{}，进行违禁检测.媒资信息:{}.", prohibitPointEnum.toString(), ruleProhibit);
                    return response.setResult(true);
                } else {
                    log.info("违禁检测 -----> 当前媒资违禁监测点信息为:{}，不符合cp违禁监测点:{}.媒资信息:{}.", prohibitPointEnum.toString(), sysCp.getPointProhibit(), ruleProhibit);
                    return response.setResult(false).setIsProhibit(IsProhibitEnum.ING.getValue()).setIsEntry(false);
                }
            } else {
                log.info("违禁检测 -----> 当前cp设置不进行违禁检测.媒资信息:{}.", ruleProhibit);
                return response.setResult(false).setIsProhibit(IsProhibitEnum.SKIP.getValue()).setIsEntry(false);
            }
        }
        log.warn("违禁检测 -----> 媒资关键信息不能为空，无法校验当前媒资.媒资信息:{}.", ruleProhibit);
        return response.setResult(false).setIsProhibit(IsProhibitEnum.ING.getValue()).setIsEntry(false);
    }

    /**
     * 违禁片判断
     *
     * @param ruleProhibit
     */
    public RuleProhibitResponse ruleProhibitCheck(RuleProhibitVo ruleProhibit) {
        Boolean result = false;
        ProhibitPointEnum prohibitPointEnum = ruleProhibit.getProhibitPointEnum();
        RuleProhibitResponse response = illegalExamine(ruleProhibit, prohibitPointEnum);
        if (!response.getResult()) {
            RuleProhibitResponse request = voluationRequest(response.getResult(), response.getIsEntry(), response.getIsProhibit(), 0, ruleProhibit);
            return request;
        }
        SysCp sysCp = redisService.getCacheMapValue(RedisKeyConstants.SYS_CP, String.valueOf(ruleProhibit.getCpId()));
        Boolean entryValue = getEntryValue(sysCp);
        ContentTypeEnum contentEnum = ContentTypeEnum.getByValue(Integer.valueOf(ruleProhibit.getType()));
        List<RuleProhibit> allProhibitRules = ruleCache.getAllProhibitRules();
        IRuleStrategyHandler<Serializable> handler = ruleStrategyFactory.getHandler(contentEnum);
        Integer ratio = handler.execute(allProhibitRules, ruleProhibit);
        if (ObjectUtils.isNotEmpty(ratio) && ruleProhibitConfig.getProportion() < ratio) {
            log.info("违禁检测 -----> 当前媒资名称:{},匹配违禁规则名:{}, 最终权重为:{},违禁片检测完毕,符合违禁片规则验证，验证结果为违禁片,msg:{}"
                    , ruleProhibit.getContentName(), ruleProhibit.getShowName(), ratio, ruleProhibit);
            result = true;
        }
        RuleProhibitResponse ruleProhibitResponse = voluationRequest(result, result ? entryValue : false, result ? IsProhibitEnum.YES.getValue() : IsProhibitEnum.NO.getValue(), ratio, ruleProhibit);
        return ruleProhibitResponse;
    }

    /**
     * 是否入库
     *
     * @param sysCp
     * @return
     */
    private Boolean getEntryValue(SysCp sysCp) {
        Boolean result = false;
        ProhibitSkipEnum prohibitSkipEnum = ProhibitSkipEnum.getByValue(sysCp.getSkipProhibit());
        switch (prohibitSkipEnum) {
            case AUTO:
                result = true;
                break;
            case MANUAL:
                result = false;
                break;
            default:
                break;
        }
        return result;
    }

    /**
     * 返回信息赋值
     *
     * @param result       返回结果
     * @param IsEntry      是否入库
     * @param isProhibit   0:"否",1:"是",2:"待检测",3:"跳过检测"
     * @param ratio        占比
     * @param ruleProhibit 违禁检测实体
     * @return
     */
    private RuleProhibitResponse voluationRequest(Boolean result, Boolean IsEntry, Integer isProhibit, Integer ratio, RuleProhibitVo ruleProhibit) {
        RuleProhibitResponse ruleProhibitResponse = new RuleProhibitResponse();
        ruleProhibitResponse.setResult(result)
                .setIsProhibit(isProhibit)
                .setRuleCode(ruleProhibit.getCode())
                .setRuleName(ruleProhibit.getShowName())
                .setRatio(ratio)
                .setIsEntry(IsEntry)
                .setContentType(ruleProhibit.getType())
                .setContentCode(ruleProhibit.getContentCode());
        return ruleProhibitResponse;
    }

    /**
     * 单集检测
     *
     * @param cmsProgram
     * @return
     */
    @Override
    public CmsProgram checkProgramRuleProhibit(CmsProgramVO cmsProgram) {
        try {
            //判断违禁片库中是否存在
            Boolean exist = cmsProhibitService.isExist(cmsProgram.getCode(), ContentTypeEnum.FILM.getValue());
            if (exist) {
                //为违禁片
                cmsProgram.setIsProhibit(IsProhibitEnum.YES.getValue());
                cmsProgram.setProhibitStatus(ProhibitStatusEnum.YES.getValue());
                return cmsProgram;
            }
            RuleProhibitVo ruleProhibitVo = copyProperties.copyCmsProgramProperties(cmsProgram);
            //添加违禁检测
            RuleProhibitResponse ruleProhibitResponse = ruleProhibitCheck(ruleProhibitVo);
            if (ObjectUtils.isNotEmpty(ruleProhibitResponse)) {
                if (ruleProhibitResponse.getResult()) {
                    //为违禁片
                    cmsProgram.setIsProhibit(IsProhibitEnum.YES.getValue());
                    if (ruleProhibitResponse.getIsEntry()) {
                        //判断是否授权sp侧
                        List<BmsContent> bmsContentList = bmsContentService.list(Wrappers.<BmsContent>lambdaQuery().eq(BmsContent::getCmsContentId, cmsProgram.getId()).eq(BmsContent::getContentType, ContentTypeEnum.FILM.getValue()));
                        if (!CollectionUtils.isEmpty(bmsContentList)) {
                            cmsProgram.setProhibitStatus(ProhibitStatusEnum.NO.getValue());
                        } else {
                            cmsProgram.setProhibitStatus(ProhibitStatusEnum.YES.getValue());
                            //插入违禁片库
                            CmsProhibitListVo cmsProhibitListVo = new CmsProhibitListVo();
                            ContentIdAndTypeVo contentIdAndTypeVo = new ContentIdAndTypeVo();
                            contentIdAndTypeVo.setContentId(cmsProgram.getId());
                            contentIdAndTypeVo.setType(ContentTypeEnum.FILM.getValue());
                            contentIdAndTypeVo.setRuleCode(ruleProhibitResponse.getRuleCode());
                            cmsProhibitListVo.setList(new ArrayList<ContentIdAndTypeVo>() {{
                                add(contentIdAndTypeVo);
                            }});
                            int add = cmsProhibitService.add(cmsProhibitListVo);
                            log.info("注入单集违禁检测完成,共插入违禁片:{}条", add);
                        }
                    } else {
                        cmsProgram.setProhibitStatus(ProhibitStatusEnum.NO.getValue());
                    }
                } else {
                    cmsProgram.setIsProhibit(ruleProhibitResponse.getIsProhibit());
                    cmsProgram.setProhibitStatus(ProhibitStatusEnum.NO.getValue());
                    log.info("注入单集违禁检测返回结果为非违禁片，单集信息:{}", cmsProgram);
                }
            } else {
                cmsProgram.setIsProhibit(IsProhibitEnum.ING.getValue());
                cmsProgram.setProhibitStatus(ProhibitStatusEnum.NO.getValue());
                log.warn("注入单集违禁检测返回结果为空,修改检测状态为待检测,单集信息:{}", cmsProgram);
            }

        } catch (Exception exception) {
            log.error("注入单集违禁检测错误，单集信息:{}, 错误信息:{}", cmsProgram, exception);
        }
        return cmsProgram;
    }

    /**
     * 剧集检测
     *
     * @param cmsSeries
     * @return
     */
    @Override
    public CmsSeries checkSeriesRuleProhibit(CmsSeriesVO cmsSeries) {
        try {
            //判断违禁片库中是否存在
            Boolean exist = cmsProhibitService.isExist(cmsSeries.getCode(), ContentTypeEnum.TELEPLAY.getValue());
            if (exist) {
                //为违禁片
                cmsSeries.setIsProhibit(IsProhibitEnum.YES.getValue());
                cmsSeries.setProhibitStatus(ProhibitStatusEnum.YES.getValue());
                return cmsSeries;
            }
            RuleProhibitVo ruleProhibitVo = copyProperties.copyCmsSeriesProperties(cmsSeries);
            //添加违禁检测
            RuleProhibitResponse ruleProhibitResponse = ruleProhibitCheck(ruleProhibitVo);
            if (ObjectUtils.isNotEmpty(ruleProhibitResponse)) {
                if (ruleProhibitResponse.getResult()) {
                    //为违禁片
                    cmsSeries.setIsProhibit(IsProhibitEnum.YES.getValue());
                    //子集默认加入违禁片库状态
                    Integer prohibitstatus = ProhibitStatusEnum.NO.getValue();
                    if (ruleProhibitResponse.getIsEntry()) {
                        //判断是否授权sp侧
                        List<BmsContent> bmsContentList = bmsContentService.list(Wrappers.<BmsContent>lambdaQuery().eq(BmsContent::getCmsContentId, cmsSeries.getId()).ne(BmsContent::getContentType, ContentTypeEnum.FILM.getValue()));
                        if (!CollectionUtils.isEmpty(bmsContentList)) {
                            cmsSeries.setProhibitStatus(ProhibitStatusEnum.NO.getValue());
                        } else {
                            cmsSeries.setProhibitStatus(ProhibitStatusEnum.YES.getValue());
                            prohibitstatus = ProhibitStatusEnum.YES.getValue();
                            //插入违禁片库
                            CmsProhibitListVo cmsProhibitListVo = new CmsProhibitListVo();
                            ContentIdAndTypeVo contentIdAndTypeVo = new ContentIdAndTypeVo();
                            contentIdAndTypeVo.setContentId(cmsSeries.getId());
                            contentIdAndTypeVo.setType(ContentTypeEnum.TELEPLAY.getValue());
                            contentIdAndTypeVo.setRuleCode(ruleProhibitResponse.getRuleCode());
                            cmsProhibitListVo.setList(new ArrayList<ContentIdAndTypeVo>() {{
                                add(contentIdAndTypeVo);
                            }});
                            int add = cmsProhibitService.add(cmsProhibitListVo);
                            log.info("注入剧集违禁检测完成,共插入违禁片:{}条", add);
                        }
                    } else {
                        cmsSeries.setProhibitStatus(ProhibitStatusEnum.NO.getValue());
                    }
                    List<CmsProgram> bySeriesCode = cmsProgramService.getBySeriesCode(cmsSeries.getCode());
                    Integer finalProhibitstatus = prohibitstatus;
                    bySeriesCode.forEach(cmsProgram -> {
                        cmsProgram.setIsProhibit(IsProhibitEnum.YES.getValue());
                        //子集不加入违禁片库
                        cmsProgram.setProhibitStatus(finalProhibitstatus);
                    });
                    //批量更新子集违禁状态
                    boolean b = cmsProgramService.updateBatchById(bySeriesCode);
                } else {
                    cmsSeries.setIsProhibit(ruleProhibitResponse.getIsProhibit());
                    cmsSeries.setProhibitStatus(ProhibitStatusEnum.NO.getValue());
                    log.info("注入剧集违禁检测返回结果为非违禁片，剧集信息:{}", cmsSeries);
                }
            } else {
                cmsSeries.setIsProhibit(IsProhibitEnum.ING.getValue());
                cmsSeries.setProhibitStatus(ProhibitStatusEnum.NO.getValue());
                log.warn("注入剧集违禁检测返回结果为空,修改检测状态为待检测,剧集信息:{}", cmsSeries);
            }

        } catch (Exception exception) {
            log.error("注入剧集违禁检测错误，剧集信息:{}, 错误信息:{}", cmsSeries, exception);
        }
        return cmsSeries;
    }
}
