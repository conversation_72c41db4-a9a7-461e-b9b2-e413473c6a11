package com.pukka.iptv.manage.controller.sys;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.pukka.iptv.common.base.util.Menu;
import com.pukka.iptv.common.base.vo.CommonResponse;
import com.pukka.iptv.common.data.model.sys.SysMenu;
import com.pukka.iptv.common.data.vo.IdList;
import com.pukka.iptv.common.log.annotation.SysLog;
import com.pukka.iptv.common.log.enums.OperateObjectEnum;
import com.pukka.iptv.common.log.enums.OperateTypeEnum;
import com.pukka.iptv.manage.service.sys.SysMenuService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.AllArgsConstructor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import javax.validation.constraints.NotNull;
import java.util.List;
import java.util.Set;

/**
 *
 * @author: zhengcl
 * @date: 2021-8-18 10:37:52
 */

@RestController
@AllArgsConstructor
@RequestMapping(value = "/sysMenu", produces = MediaType.APPLICATION_JSON_VALUE)
@Api(tags="sysMenu管理")
public class SysMenuController {

    @Autowired
    @NotNull
    private SysMenuService sysMenuService;

    @ApiOperation(value = "分页",hidden = true)
    @GetMapping("/page" )
    public CommonResponse<Page> page(Page page, SysMenu sysMenu) {
        return  CommonResponse.success(sysMenuService.page(page, Wrappers.query(sysMenu)));
    }

    @ApiOperation(value = "详情")
    @GetMapping("/getById")
    public CommonResponse<SysMenu> getById(@Valid @RequestParam(name = "id", required = true)  Long id) {
        return CommonResponse.success(sysMenuService.getById(id));
    }

    @SysLog(objectType = OperateObjectEnum.SYS_MENU, operateType = OperateTypeEnum.SAVE, objectIds = "#sysMenu.id", objectNames = "#sysMenu.name")
    @ApiOperation(value = "新增",hidden = true)
    @PostMapping("/save")
    public CommonResponse<Boolean> save(@Valid @RequestBody SysMenu sysMenu) {
        return  CommonResponse.success(sysMenuService.save(sysMenu));
    }

    @SysLog(objectType = OperateObjectEnum.SYS_MENU, operateType = OperateTypeEnum.UPDATE, objectIds = "#sysMenu.id", objectNames = "#sysMenu.name")
    @ApiOperation(value = "修改",hidden = true)
    @PutMapping("/update")
    public CommonResponse<Boolean> updateById(@Valid @RequestBody SysMenu sysMenu) {
        return CommonResponse.success(sysMenuService.update(sysMenu));
    }

    @SysLog(objectType = OperateObjectEnum.SYS_MENU, operateType = OperateTypeEnum.DELETE, objectIds = "#id")
    @ApiOperation(value = "删除",hidden = true)
    @DeleteMapping("/deleteById" )
    public CommonResponse<Boolean> deleteById(@Valid @RequestParam(name = "id", required = true) Long id) {
        return  CommonResponse.success(sysMenuService.removeById(id));
    }

    @SysLog(objectType = OperateObjectEnum.SYS_MENU, operateType = OperateTypeEnum.DELETE, objectIds = "#idList.ids")
    @ApiOperation(value = "批量删除",hidden = true)
    @DeleteMapping("/deleteByIds" )
    public CommonResponse<Boolean> deleteByIds(@Valid @RequestBody IdList idList) {
        return  CommonResponse.success(sysMenuService.removeByIds(idList.getIds()));
    }

    @ApiOperation(value ="查询所有菜单" )
    @GetMapping("/menuList")
    public CommonResponse<Object> menuList(@Valid @RequestParam(name = "userId", required = true) Long userId) {
        return CommonResponse.success(sysMenuService.menuList(userId));
    }

    @ApiOperation(value ="查询标签" )
    @GetMapping("/tabList")
    public CommonResponse<Object> tabList(@Valid @RequestParam(name = "userId", required = true) Long userId, @Valid @RequestParam(name = "parentId", required = true) Long parentId, @Valid @RequestParam(name = "group", required = true) String group) {
        return CommonResponse.success(sysMenuService.tabList(userId, parentId, group));
    }

    @ApiOperation(value ="查询分组" )
    @GetMapping("/groupList")
    public CommonResponse<Object> groupList(@Valid @RequestParam(name = "userId", required = true) Long userId, @Valid @RequestParam(name = "parentId", required = true) Long parentId, @Valid @RequestParam(name = "group", required = true) String group) {
        return CommonResponse.success(sysMenuService.groupList(userId, parentId,group));
    }

    @ApiOperation(value ="查询页面工具栏按钮" )
    @GetMapping("/toolButtonList")
    public CommonResponse<Object> toolButtonList(@Valid @RequestParam(name = "userId", required = true) Long userId, @Valid @RequestParam(name = "parentId", required = true) Long parentId, @Valid @RequestParam(name = "group", required = true) String group) {
        return CommonResponse.success(sysMenuService.toolButtonList(userId, parentId,group));
    }

    @ApiOperation(value ="查询页面表格列按钮" )
    @GetMapping("/columnButtonList")
    public CommonResponse<Object> columnButtonList(@Valid @RequestParam(name = "userId", required = true) Long userId, @Valid @RequestParam(name = "parentId", required = true) Long parentId, @Valid @RequestParam(name = "group", required = true) String group) {
        return CommonResponse.success(sysMenuService.columnButtonList(userId, parentId,group));
    }

    @ApiOperation(value ="查询列表" )
    @GetMapping("/listByRoleIds")
    public CommonResponse<List<Menu>> listByRoleIds(@RequestParam(name = "roleIds", required = false) Set<Long> roleIds) {
        return CommonResponse.success(sysMenuService.listByRoleIds(roleIds));
    }

    @ApiOperation(value ="查询所有" )
    @GetMapping("/listTreeAll")
    public CommonResponse<List<Menu>> listAll(@RequestParam(name = "roleId", required = true) Long roleId) {
        return CommonResponse.success(sysMenuService.listTreeAll(roleId));
    }
    
}
