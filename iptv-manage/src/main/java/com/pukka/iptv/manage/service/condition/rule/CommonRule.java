package com.pukka.iptv.manage.service.condition.rule;

import cn.hutool.core.collection.CollectionUtil;
import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.pukka.iptv.common.base.enums.LockStatusEnum;
import com.pukka.iptv.common.core.util.SpringUtils;
import com.pukka.iptv.common.core.util.StringUtils;

import com.pukka.iptv.common.data.model.bms.BmsContent;
import com.pukka.iptv.manage.mapper.bms.BmsBaseMapper;
import com.pukka.iptv.manage.mapper.bms.BmsContentMapper;
import com.pukka.iptv.manage.service.condition.AbstractRule;
import com.pukka.iptv.manage.service.condition.BaseRule;
import com.pukka.iptv.manage.service.condition.RuleResult;
import org.springframework.util.CollectionUtils;

import java.util.Collection;

/**
 * @author: wz
 * @date: 2021/9/9 17:34
 * @description:
 */
public class CommonRule<T> extends AbstractRule<T, Object> {

    protected CommonRule(Class<T> clazz) {
        super(clazz);
    }

    public static <T> CommonRule<T> init(Class<T> clazz) {
        return new CommonRule<>(clazz);
    }

    private static RuleResult fail(String name) {
        return RuleResult.fail(name + "无法操作!", name);
    }


    @Override
    public BaseRule wrapper(QueryWrapper wrapper) {
        return this;
    }

    @Override
    public RuleResult execute() {
        String table = getTableName();
        Collection<Long> ids = getData();
        //为空不检查
        if (CollectionUtil.isEmpty(ids)) return RuleResult.ok();

        BmsBaseMapper bean = SpringUtils.getBean(BmsBaseMapper.class);
        String name = bean.getLockCountByTable(table, ids, LockStatusEnum.LOCK.getCode());
        if (StringUtils.hasText(name)) {
            return fail(name);
        }
        return RuleResult.ok();
    }
}
