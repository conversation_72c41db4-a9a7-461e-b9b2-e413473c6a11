package com.pukka.iptv.manage.service.condition.rule;

import cn.hutool.core.collection.CollectionUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.pukka.iptv.common.base.enums.OpCheckStatusEnum;
import com.pukka.iptv.common.base.enums.StatusEnum;
import com.pukka.iptv.common.core.constant.SymbolConstant;
import com.pukka.iptv.common.core.util.SpringUtils;
import com.pukka.iptv.manage.mapper.bms.BmsBaseMapper;
import com.pukka.iptv.manage.service.condition.AbstractRule;
import com.pukka.iptv.manage.service.condition.BaseRule;
import com.pukka.iptv.manage.service.condition.RuleResult;
import org.apache.commons.lang3.StringUtils;
import org.apache.poi.util.StringUtil;

import java.util.Collection;
import java.util.List;


/**
 * @author: chiron
 * @date: 2023/6/12 18:57
 * @description:
 */
public class SpCheckRule<T> extends AbstractRule<T, Object> {

    protected SpCheckRule(Class<T> clazz) {
        super(clazz);
    }

    public static <T> SpCheckRule<T> init(Class<T> clazz) {
        return new SpCheckRule<>(clazz);
    }

    @Override
    public BaseRule wrapper(QueryWrapper wrapper) {
        return null;
    }

    @Override
    public RuleResult execute() {
        String tableName = getTableName();
        Collection<Long> ids = getData();

        //为空不检查
        if (CollectionUtil.isEmpty(ids)) return RuleResult.ok();
        BmsBaseMapper bean = SpringUtils.getBean(BmsBaseMapper.class);
        List<String> spCheckStatusList = bean.getSpCheckStatusList(tableName, StatusEnum.COME.getCode(), ids);
        //list集合以逗号分割，转string字符串
        String spCheckStatus = StringUtils.join(spCheckStatusList, SymbolConstant.COMMA);
        if (CollectionUtil.isNotEmpty(spCheckStatusList)) {
            return RuleResult.fail(" SP已被禁用 无法操作!", spCheckStatus);
        } else {
            return RuleResult.ok();
        }
    }
}
