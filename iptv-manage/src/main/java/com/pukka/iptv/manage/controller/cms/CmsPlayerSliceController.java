package com.pukka.iptv.manage.controller.cms;

import com.pukka.iptv.common.base.vo.CommonResponse;
import com.pukka.iptv.common.data.model.cms.CmsMovie;
import javax.validation.Valid;

import com.pukka.iptv.common.data.model.cms.CmsPlayerPicture;
import com.pukka.iptv.common.data.model.cms.CmsPlayerSlice;
import com.pukka.iptv.common.log.annotation.SysLog;
import com.pukka.iptv.common.log.enums.OperateObjectEnum;
import com.pukka.iptv.common.log.enums.OperateTypeEnum;
import com.pukka.iptv.manage.service.cms.CmsPlayerSliceService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.*;
import org.springframework.beans.factory.annotation.Autowired;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.AllArgsConstructor;


/**
 * 视频审核标记
 *
 * @author: zhoul
 * @date: 2021-11-3 14:33:05
 */

@RestController
@AllArgsConstructor
@RequestMapping(value = "/cmsPlayerSlice", produces = MediaType.APPLICATION_JSON_VALUE)
@Api(tags="cmsPlayerSlice管理")
public class CmsPlayerSliceController {

    @Autowired
    private CmsPlayerSliceService cmsPlayerSliceService;

    @ApiOperation(value = "分页")
    @GetMapping("/page" )
    public CommonResponse<Page> listByCmsContentIdContentType(@Valid Page page, CmsMovie cmsMovie) {
        return  CommonResponse.success(cmsPlayerSliceService.listByCmsContentIdContentType(page, cmsMovie));
    }

    @ApiOperation(value = "修改")
    @PutMapping("/update")
    public CommonResponse<Boolean> updateById(@Valid @RequestBody CmsPlayerSlice cmsPlayerSlice) {
        return CommonResponse.success(cmsPlayerSliceService.updateById(cmsPlayerSlice));
    }

    @ApiOperation(value = "删除")
    @DeleteMapping("/deleteById" )
    public CommonResponse<Boolean> deleteById(@Valid @RequestBody CmsPlayerSlice cmsPlayerSlice) {
        return  CommonResponse.success(cmsPlayerSliceService.removeById(cmsPlayerSlice));

    }

}
