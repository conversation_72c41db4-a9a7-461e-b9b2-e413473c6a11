package com.pukka.iptv.manage.service.sys.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.pukka.iptv.common.data.model.sys.SysUserTenant;
import com.pukka.iptv.manage.mapper.sys.SysUserTenantMapper;
import com.pukka.iptv.manage.service.sys.SysUserTenantService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 *
 * @author: zhengcl
 * @date: 2021-8-17 16:48:36
 */

@Service
public class SysUserTenantServiceImpl extends ServiceImpl<SysUserTenantMapper, SysUserTenant> implements SysUserTenantService {

    @Autowired
    private SysUserTenantMapper sysUserTenantMapper;

    @Override
    public List<SysUserTenant> listByUserId(Long userId) {
        QueryWrapper<SysUserTenant> query = new QueryWrapper<>();
        query.lambda().eq(SysUserTenant::getUserId, userId);
        return this.sysUserTenantMapper.selectList(query);
    }
}


