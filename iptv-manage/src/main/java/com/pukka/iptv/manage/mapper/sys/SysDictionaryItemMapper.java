package com.pukka.iptv.manage.mapper.sys;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.pukka.iptv.common.data.annotation.DataPermission;
import com.pukka.iptv.common.data.model.sys.SysDictionaryItem;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

/**
 * @author: zhengcl
 * @date: 2021-9-13 17:59:15
 */

@Mapper
public interface SysDictionaryItemMapper extends BaseMapper<SysDictionaryItem> {
    SysDictionaryItem selectPlatformIdentification();

    SysDictionaryItem isPlatformIdentification(@Param("id") Long id);

    String selectPgmCategory(@Param("pgmCategoryId") Long pgmCategoryId);

    String selectPgmSndClass(@Param("pgmSndClassId") Long pgmSndClassId);
}
