package com.pukka.iptv.manage.service.in.impl;


import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.pukka.iptv.common.data.model.in.InPackage;
import com.pukka.iptv.manage.mapper.in.InPackageMapper;
import com.pukka.iptv.manage.service.in.InPackageService;
import org.springframework.stereotype.Service;


/**
 *
 * @author: tan
 * @date: 2021-8-24 10:47:07
 */

@Service
public class InPackageServiceImpl extends ServiceImpl<InPackageMapper, InPackage> implements InPackageService {

}


