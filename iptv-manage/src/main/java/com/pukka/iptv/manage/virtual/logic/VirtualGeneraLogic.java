package com.pukka.iptv.manage.virtual.logic;

import com.alibaba.fastjson.JSON;
import com.pukka.iptv.common.base.constant.VirtualStrategyConstans;
import com.pukka.iptv.common.base.enums.ActionEnums;
import com.pukka.iptv.common.base.enums.ContentTypeEnum;
import com.pukka.iptv.common.base.enums.ItemResultEnum;
import com.pukka.iptv.common.core.util.SafeUtil;
import com.pukka.iptv.common.data.dto.PublishParamsDto;
import com.pukka.iptv.manage.virtual.IVirtualLogic;
import com.pukka.iptv.manage.virtual.VirtualStrategyFactory;
import com.pukka.iptv.manage.virtual.IVirtualStrategyHandler;
import com.pukka.iptv.manage.virtual.util.DataCache;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.io.Serializable;

@Slf4j
@Component(VirtualStrategyConstans.VIRTUAL_BEAN_PRE + VirtualStrategyConstans.LOGIC_BEAN_PRE + VirtualStrategyConstans.GENERA_PRE)
public class VirtualGeneraLogic implements IVirtualLogic {
    @Autowired
    private DataCache dataCache;
    @Autowired
    private VirtualStrategyFactory virtualStrategyFactory;

    @Override
    public Boolean dealContent(PublishParamsDto params) throws Exception {
        try {
            log.info("虚拟频道消息处理 -----> 普通发布 接收消息:{}", (ObjectUtils.isEmpty(params) ? "暂无内容" : JSON.toJSONString(params)));
            //判断消息类型，处理单集、子集、剧集
            if (ObjectUtils.isEmpty(params.getResult()) || !params.getResult().equals(ItemResultEnum.Success.getValue())) {
                log.info("虚拟频道消息处理 -----> 普通发布 接收消息:{} 当前内容执行失败 暂不处理!", (ObjectUtils.isEmpty(params) ? "暂无内容" : JSON.toJSONString(params)));
                return true;
            }
            if (!dataCache.isExistSpChannel(SafeUtil.getString(params.getSpId()))) {
                log.info("虚拟频道消息处理 -----> 普通发布 接收消息:{} 当前内容分发域:{} 暂不处理!", (ObjectUtils.isEmpty(params) ? "暂无内容" : JSON.toJSONString(params)), params.getSpId());
                return true;
            }
            ContentTypeEnum contentType = ContentTypeEnum.getByValue(params.getContentType());
            ActionEnums codeByPublishStatus = ActionEnums.getByValue(params.getAction());
            IVirtualStrategyHandler<Serializable> handler = virtualStrategyFactory.getHandler(codeByPublishStatus);
            handler.execute(contentType, params.getContentId());
        } catch (Exception exception) {
            log.error("虚拟频道消息处理 -----> 普通发布 RabbitMQ监听异常,消息内容:{},异常原因:{}",
                    (ObjectUtils.isEmpty(params) ? "暂无内容" : JSON.toJSONString(params)), exception);
            return false;
        }
        return true;
    }
}
