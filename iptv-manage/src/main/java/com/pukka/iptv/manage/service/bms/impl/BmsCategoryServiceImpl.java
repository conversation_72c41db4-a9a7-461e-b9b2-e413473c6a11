package com.pukka.iptv.manage.service.bms.impl;

import static com.pukka.iptv.common.base.enums.PublishStatusEnum.FAILPUBLISH;
import static com.pukka.iptv.common.base.enums.PublishStatusEnum.FAILROLLBACK;
import static com.pukka.iptv.common.base.enums.PublishStatusEnum.FAILUPDATE;
import static com.pukka.iptv.common.base.enums.PublishStatusEnum.PUBLISH;
import static com.pukka.iptv.common.base.enums.PublishStatusEnum.ROLLBACK;
import static com.pukka.iptv.common.base.enums.PublishStatusEnum.WAITPUBLISH;
import static com.pukka.iptv.common.base.enums.PublishStatusEnum.WAITUPDATE;
import static com.pukka.iptv.manage.service.condition.rule.enums.PublishCheck.CAN_DELETE;
import static com.pukka.iptv.manage.service.condition.rule.enums.PublishCheck.ING;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.ObjectUtils;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.pukka.iptv.common.base.constant.PublishParamTypeConstants;
import com.pukka.iptv.common.base.constant.RedisKeyConstants;
import com.pukka.iptv.common.base.enums.ActionEnums;
import com.pukka.iptv.common.base.enums.CategoryMarkEnum;
import com.pukka.iptv.common.base.enums.ContentTypeEnum;
import com.pukka.iptv.common.base.enums.InPassageTypeEnum;
import com.pukka.iptv.common.base.enums.PriorityEnums;
import com.pukka.iptv.common.base.enums.PublishStatusEnum;
import com.pukka.iptv.common.base.enums.StatisticsMediaMQEnum;
import com.pukka.iptv.common.base.enums.StatusEnum;
import com.pukka.iptv.common.base.exception.BizException;
import com.pukka.iptv.common.base.util.UUID;
import com.pukka.iptv.common.base.vo.CommonResponse;
import com.pukka.iptv.common.core.constant.SymbolConstant;
import com.pukka.iptv.common.data.model.OutParamExpand;
import com.pukka.iptv.common.data.model.bms.BmsCategory;
import com.pukka.iptv.common.data.model.bms.BmsCategoryChannel;
import com.pukka.iptv.common.data.model.bms.BmsCategoryContent;
import com.pukka.iptv.common.data.model.bms.BmsPicture;
import com.pukka.iptv.common.data.model.cms.CmsPicture;
import com.pukka.iptv.common.data.model.order.SubOrderObjectsEntity;
import com.pukka.iptv.common.data.model.sys.SysInPassage;
import com.pukka.iptv.common.data.model.sys.SysSp;
import com.pukka.iptv.common.data.util.Tuple2;
import com.pukka.iptv.common.data.vo.bms.BmsBelongCategorySpVO;
import com.pukka.iptv.common.data.vo.bms.BmsBelongCategoryVO;
import com.pukka.iptv.common.data.vo.bms.BmsCategoryVO;
import com.pukka.iptv.common.data.vo.bms.BmsPictureVO;
import com.pukka.iptv.common.data.vo.req.BmsCategoryCommonReq;
import com.pukka.iptv.common.data.vo.req.BmsCategoryOperationReq;
import com.pukka.iptv.common.data.vo.req.BmsCategoryQueryReq;
import com.pukka.iptv.common.rabbitmq.config.CmsContentRenameMQConfig;
import com.pukka.iptv.common.redis.service.RedisService;
import com.pukka.iptv.manage.mapper.bms.BmsCategoryMapper;
import com.pukka.iptv.manage.service.bms.BmsCategoryChannelService;
import com.pukka.iptv.manage.service.bms.BmsCategoryContentService;
import com.pukka.iptv.manage.service.bms.BmsCategoryService;
import com.pukka.iptv.manage.service.bms.BmsPictureService;
import com.pukka.iptv.manage.service.bms.common.WorkOrderOperation;
import com.pukka.iptv.manage.service.cms.CmsPictureService;
import com.pukka.iptv.manage.service.condition.RuleCondition;
import com.pukka.iptv.manage.service.condition.RuleResult;
import com.pukka.iptv.manage.service.condition.rule.LockStatusRule;
import com.pukka.iptv.manage.service.condition.rule.PublishStatusRule;
import com.pukka.iptv.manage.service.condition.rule.RecycleStatusRule;
import com.pukka.iptv.manage.service.condition.rule.enums.PublishCheck;
import com.pukka.iptv.manage.service.condition.rule.enums.RecycleCheck;
import com.pukka.iptv.manage.service.sys.impl.SysInPassageServiceImpl;
import com.pukka.iptv.manage.util.RuleUtil;
import java.util.ArrayList;
import java.util.Collection;
import java.util.Collections;
import java.util.Comparator;
import java.util.Date;
import java.util.HashMap;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;
import lombok.extern.slf4j.Slf4j;
import org.springframework.amqp.rabbit.core.RabbitTemplate;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;


/**
 * 栏目
 *
 * <AUTHOR>
 * @date 2021-08-27 10:20:57
 */

@Service
@Slf4j
public class BmsCategoryServiceImpl extends ServiceImpl<BmsCategoryMapper, BmsCategory> implements BmsCategoryService {

    @Autowired
    private BmsCategoryMapper bmsCategoryMapper;

    @Autowired
    private BmsPictureService bmsPictureService;

    @Autowired
    private CmsPictureService cmsPictureService;

    @Autowired
    private BmsCategoryContentService bmsCategoryContentService;

    @Autowired
    private BmsCategoryChannelService bmsCategoryChannelService;

    @Autowired
    private RedisService redisService;

    @Autowired
    private WorkOrderOperation workOrderOperation;

    @Autowired
    private SysInPassageServiceImpl sysInPassageService;//已缓存

    @Autowired
    private RabbitTemplate rabbitTemplate;

    // 栏目分页查询 已完成
    @Override
    public IPage<BmsCategory> listByParentId(Page<BmsCategory> page, BmsCategoryQueryReq queryReq) {
        //size等于-1时不分页
        page.setSize(page.getSize() == -1 ? Integer.MAX_VALUE : page.getSize());
        return this.page(page, Wrappers.lambdaQuery(BmsCategory.class)
                .eq(Objects.nonNull(queryReq.getSpId()), BmsCategory::getSpId, queryReq.getSpId())
                .eq(Objects.nonNull(queryReq.getParentId()), BmsCategory::getParentId, queryReq.getParentId())
                .eq(Objects.nonNull(queryReq.getStatus()), BmsCategory::getStatus, queryReq.getStatus())
                .eq(Objects.nonNull(queryReq.getPublishStatus()), BmsCategory::getPublishStatus, queryReq.getPublishStatus())
                .like(StringUtils.hasText(queryReq.getName()), BmsCategory::getName, queryReq.getName())
                .orderByAsc(BmsCategory::getSequence)
                .orderByDesc(BmsCategory::getId))
                ;
    }

    // 栏目多选排序 已完成
    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = Exception.class)
    public boolean sort(List<BmsCategoryVO> vo) {
        List<Long> ids = vo.stream().map(BmsCategory::getId).collect(Collectors.toList());
        RuleResult rr = RuleCondition.create().and(LockStatusRule.init(BmsCategory.class).data(ids))
                .and(PublishStatusRule.init(BmsCategory.class).policy(ING).data(ids))
                .execute();
        if (!rr.isPass()) {
            throw new BizException(rr.getMsg());
        }
        // 按id升序排列
        List<BmsCategory> list = this.list(Wrappers.lambdaQuery(BmsCategory.class)
                .select(BmsCategory::getId, BmsCategory::getPublishStatus)
                .in(BmsCategory::getId, ids)
                .orderByAsc(BmsCategory::getId));
        // 入参id和查询结果都按升序排列，传入的id数量，查询结果总数必须一致
        if (ids.size() != list.size()) {
            return false;
        }
        List<BmsCategoryVO> afterSorting = vo.stream().sorted(Comparator.comparing(BmsCategoryVO::getId)).collect(Collectors.toList());
        List<BmsCategory> collect = new ArrayList<>();
        for (int i = 0; i < afterSorting.size(); i++) {
            //校验
            afterSorting.get(i).valid();
            BmsCategory bmsCategory = new BmsCategory();
            bmsCategory.setId(afterSorting.get(i).getId());
            bmsCategory.setSequence(afterSorting.get(i).getSequence());
            Integer publishStatus = list.get(i).getPublishStatus();
            // 确认即将要修改的发布状态
            Integer newStatus = RuleUtil.confirmPublishStatus(publishStatus);
            bmsCategory.setPublishStatus(newStatus);
            collect.add(bmsCategory);
        }
        if (collect.isEmpty()) {
            return false;
        }
        return this.updateBatchById(collect);
    }

    // 栏目批量解锁锁定 已完成
    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = Exception.class)
    public boolean lock(BmsCategoryOperationReq operationReq) {
        // id集合
        List<Long> idList = operationReq.getIdList();
        return this.update(Wrappers.lambdaUpdate(BmsCategory.class)
                .set(BmsCategory::getLockStatus, operationReq.getLockStatus())
                .in(BmsCategory::getId, idList));
    }

    // 栏目新增
    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = Exception.class)
    public boolean saveCategory(BmsCategoryCommonReq commonReq) {
        Long parentId = commonReq.getParentId();
        // 栏目是否存在 存在则异常中断
        isExistCategory(false, null, commonReq.getSpId(), parentId, commonReq.getName());
        // 当前父栏目不能有绑定内容
        existContent(Collections.singletonList(parentId), false);
        // 将父栏目标记为非叶子节点
        if (parentId != 0) {
            boolean update = this.update(Wrappers.lambdaUpdate(BmsCategory.class)
                    .set(BmsCategory::getLeafNode, CategoryMarkEnum.NOT_LEAF_NODE.getCode())
                    .eq(BmsCategory::getId, parentId));
            if (!update)
                throw new BizException("更新父栏目节点标记失败");
        }
        SysSp cacheSp = redisService.getCacheMapValue(RedisKeyConstants.SYS_SP, String.valueOf(commonReq.getSpId()));
        SysSp sp = Optional.ofNullable(cacheSp).orElseThrow(() -> new BizException("sp查询异常，spId：" + commonReq.getSpId()));
        // 根据通道属性设置发布状态
        SysInPassage sysInPassage = sysInPassageService.sysInPassage(commonReq.getCspId());
        if (sysInPassage != null && InPassageTypeEnum.TOW.getCode().equals(sysInPassage.getSpPublishType())) {
            // 只有明确的配置了 默认为发布成功 才设置
            commonReq.setPublishStatus(PublishStatusEnum.PUBLISH.getCode())
                    .setPublishTime(new Date())
                    .setPublishDescription("系统配置,自动发布");
        }
        String code = UUID.generalPlatformUUID();
        commonReq.setBmsSpChannelId(sp.getBmsSpChannelId())
                .setBmsSpChannelName(sp.getBmsSpChannelName())
                .setSpName(sp.getName())
                .setOutPassageIds(sp.getOutPassageIds())
                .setOutPassageNames(sp.getOutPassageNames())
                .setSource(commonReq.getSource())
                .setExtraCode(commonReq.getCode() != null ? commonReq.getCode() : code)
                .setCode(commonReq.getCode() != null ? commonReq.getCode() : code)
                .setLeafNode(CategoryMarkEnum.LEAF_NODE.getCode());
        //保存栏目信息
        if (!this.save(commonReq)) {
            throw new BizException("栏目保存失败");
        }
        BmsCategory one = this.getOne(Wrappers.lambdaQuery(BmsCategory.class).select(BmsCategory::getCode).eq(BmsCategory::getId, commonReq.getId()));
        if (Objects.nonNull(one)) {
            commonReq.setCode(one.getCode());
        }
        //保存图片信息
        this.savePicture(commonReq, commonReq.getId());
        return true;
    }

    // 栏目是否已经存在
    private void isExistCategory(Boolean isUpdate, Long id, Long spId, Long parentId, String name) {
        long isExist = this.count(Wrappers.lambdaQuery(BmsCategory.class)
                .eq(BmsCategory::getSpId, spId)
                .eq(BmsCategory::getName, name)
                .eq(BmsCategory::getParentId, parentId)
                // 更新时启用此条件，传入id
                .ne(isUpdate, BmsCategory::getId, id));
        if (isExist != 0) {
            throw new BizException("栏目名称重复");
        }
    }

    /**
     * 保存图片信息
     *
     * @param commonReq
     * @param contentId
     */
    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = Exception.class)
    public void savePicture(BmsCategoryCommonReq commonReq, Long contentId) {
        List<BmsPictureVO> picArray = commonReq.getPicArray();
        if (Objects.nonNull(picArray)) {
            //过滤掉所有id不为null的图片信息 id为null则为新增的图片信息
            for (BmsPictureVO picture : picArray) {
                if (Objects.isNull(picture.getId())) {
                    picture.setBmsContentId(contentId)
                            .setContentCode(commonReq.getCode())
                            .setContentType(ContentTypeEnum.CATEGORY.getValue())
                            .setOutPassageIds(commonReq.getOutPassageIds())
                            .setOutPassageNames(commonReq.getOutPassageNames())
                            .setSpId(commonReq.getSpId())
                            .setSpName(commonReq.getSpName());
                    //20230315 添加cms插入图片信息操作
                    CmsPicture cmsPicture = new CmsPicture();
                    BeanUtils.copyProperties(picture, cmsPicture);
                    cmsPicture.setContentId(picture.getBmsContentId());
                    boolean save = cmsPictureService.save(cmsPicture);
                    if (save) {
                        picture.setCmsPictureId(cmsPicture.getId())
                                .setCmsPictureCode(cmsPicture.getCode());
                        if (!bmsPictureService.save(picture)) {
                            throw new BizException("保存内容图片信息失败");
                        }
                    } else {
                        throw new BizException("保存编排图片信息失败");
                    }
                }
            }
        }
    }

    // 栏目修改
    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = Exception.class)
    public boolean updateCategory(BmsCategoryCommonReq commonReq) {
        RuleResult rr = RuleCondition.create()
                //锁定判断
                .and(LockStatusRule.init(BmsCategory.class).data(commonReq.getId()))
                //发布状态
                .and(PublishStatusRule.init(BmsCategory.class).policy(ING).data(commonReq.getId()))
                .execute();
        if (!rr.isPass()) {
            throw new BizException(rr.getMsg());
        }
        BmsCategory category = Optional.ofNullable(this.getById(commonReq.getId())).orElseThrow(() -> new BizException("栏目不存在"));
        // 栏目是否存在 存在则异常中断
        isExistCategory(true, category.getId(), category.getSpId(), category.getParentId(), commonReq.getName());
        // 更新
        boolean success = this.update(Wrappers.lambdaUpdate(BmsCategory.class)
                .set(StringUtils.hasText(commonReq.getName()), BmsCategory::getName, commonReq.getName())
                .set(Objects.nonNull(commonReq.getSequence()), BmsCategory::getSequence, commonReq.getSequence())
                .set(BmsCategory::getStatus, commonReq.getStatus())
                .set(BmsCategory::getPublishStatus, RuleUtil.confirmPublishStatus(category.getPublishStatus()))
                .set(BmsCategory::getDescription, commonReq.getDescription())
                .eq(BmsCategory::getId, category.getId()));
        // 图片只有新增，删除独立调用图片删除接口
        // 图片有一条不成功则异常
        if (!CollectionUtils.isEmpty(commonReq.getPicArray())) {
            commonReq.setSpId(category.getSpId());
            commonReq.setSpName(category.getSpName());
            commonReq.setOutPassageIds(category.getOutPassageIds());
            commonReq.setOutPassageNames(category.getOutPassageNames());
            commonReq.setCode(category.getCode());
            this.savePicture(commonReq, category.getId());
        }
        // 更新栏目关系表名称
        if (success) {
            bmsCategoryContentService.update(Wrappers.lambdaUpdate(BmsCategoryContent.class)
                    .set(BmsCategoryContent::getCategoryName, commonReq.getName())
                    .ne(BmsCategoryContent::getCategoryName, commonReq.getName())
                    .eq(BmsCategoryContent::getCategoryId, commonReq.getId()));
            sendMQ(category, commonReq);
        }
        return success;
    }

    private void sendMQ(BmsCategory category, BmsCategoryCommonReq commonReq) {
        try {
            HashMap<String, String> map = new HashMap<>();
            map.put("categoryCode", category.getCode());
            if (com.pukka.iptv.common.core.util.StringUtils.isNotEmpty(commonReq.getName())
                    && !category.getName().equals(commonReq.getName())) {
                map.put("categoryName", commonReq.getName());
            } else {
                return;
            }
            HashMap<String, Object> hashMap = new HashMap<>();
            BmsCategoryContent bmsCategoryContent = JSON.parseObject(JSON.toJSONString(map), BmsCategoryContent.class);
            hashMap.put(StatisticsMediaMQEnum.BMS_CATEGORY_CONTENT.getValue(), bmsCategoryContent);
            log.info("category.sendMQ.hashMap={}", JSON.toJSONString(hashMap));
            this.rabbitTemplate.convertAndSend(CmsContentRenameMQConfig.CMS_CONTENT_RENAME_EXCHANGE,
                    CmsContentRenameMQConfig.CMS_CONTENT_RENAME_ROUTING, hashMap);
        } catch (Exception e) {
            log.error("BmsCategoryServiceImpl.sendMQ.category={},commonReq={},Exception={]",
                    JSON.toJSONString(category), JSON.toJSONString(commonReq), e);
        }
    }

    // 栏目删除
    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = Exception.class)
    public boolean delete(List<Long> idList) {
        RuleResult rr = RuleCondition.create()
                //锁定判断
                .and(LockStatusRule.init(BmsCategory.class).data(idList))
                //发布状态
                .and(PublishStatusRule.init(BmsCategory.class).policy(CAN_DELETE).data(idList).type("删除"))
                .execute();
        if (!rr.isPass()) {
            throw new BizException(rr.getMsg());
        }
        // 查询父栏目信息，修改父栏目标记，如果子栏目全部删除完，父栏目节点标记标记将修改为叶子节点
        BmsCategory bmsCategory = this.getById(idList.get(0));
        if (Objects.nonNull(bmsCategory)) {
            long count = this.count(Wrappers.lambdaQuery(BmsCategory.class).eq(BmsCategory::getParentId, bmsCategory.getParentId()));
            // 如果本次删除的栏目数量是一个父栏目下的所有，则更新该父栏目为叶子节点，所有操作完成后事务将一并提取或回滚
            if (count <= idList.size()) {
                boolean update = this.update(Wrappers.lambdaUpdate(BmsCategory.class)
                        .set(BmsCategory::getLeafNode, CategoryMarkEnum.LEAF_NODE.getCode())
                        .eq(BmsCategory::getId, bmsCategory.getParentId()));
                if (!update) {
                    throw new BizException("更新父栏目节点标记失败");
                }
            }
        }
        // 是否存在子栏目
        existChildCategory(idList, false);
        // 是否存在绑定的内容
        existContent(idList, false);
        // 图片是否可删除 存在不符合删除条件的图片,此方法会异常
        // 2023年3月2日由false改为true，解决禅道BUG编号1526
        bmsPictureService.removePicture(idList, ContentTypeEnum.CATEGORY, true);
        if (!this.removeByIds(idList)) {
            throw new BizException("栏目删除失败");
        }
        return true;
    }

    // true则没有子栏目
    private void existChildCategory(List<Long> ids, boolean recycleCondition) {
        BmsCategory one = this.getOne(
                Wrappers.lambdaQuery(BmsCategory.class)
                        .select(BmsCategory::getName)
                        .in(BmsCategory::getParentId, ids)
                        // true则启用该条件 是否存在没有回收成功的子栏目
                        .notIn(recycleCondition, BmsCategory::getPublishStatus, PublishStatusRule.UN_PUBLISHED_LIST)
                        .last("limit 1"));
        if (Objects.nonNull(one))
            throw new BizException(recycleCondition ? " 存在未回收成功的子栏目" : " 存在子栏目");
    }

    // 判断是否存在关系
    // recycleCondition为true查绑定 false查回收
    private void existContent(List<Long> id, boolean recycleCondition) {
        // 栏目频道内容
        BmsCategoryChannel categoryChannel = bmsCategoryChannelService.getOne(Wrappers.lambdaQuery(BmsCategoryChannel.class)
                .select(BmsCategoryChannel::getCategoryName)
                .in(BmsCategoryChannel::getCategoryId, id)
                // true则启用该条件 是否存在没有回收成功的栏目频道
                .notIn(recycleCondition, BmsCategoryChannel::getPublishStatus, PublishStatusRule.UN_PUBLISHED_LIST)
                .last("limit 1"));
        if (Objects.nonNull(categoryChannel))
            throw new BizException(categoryChannel.getCategoryName() + (recycleCondition ? " 存在未回收成功的栏目频道关系" : " 存在绑定的频道"));
        // 栏目媒资内容
        BmsCategoryContent bmsCategoryContent = bmsCategoryContentService.getOne(Wrappers.lambdaQuery(BmsCategoryContent.class)
                .select(BmsCategoryContent::getCategoryName)
                .in(BmsCategoryContent::getCategoryId, id)
                // true则启用该条件 是否存在没有回收成功的栏目内容
                .notIn(recycleCondition, BmsCategoryContent::getPublishStatus, PublishStatusRule.UN_PUBLISHED_LIST)
                .last("limit 1"));
        if (Objects.nonNull(bmsCategoryContent))
            throw new BizException(bmsCategoryContent.getCategoryName() + (recycleCondition ? " 存在未回收成功的栏目媒资关系" : " 存在绑定的媒资"));
    }

    // 栏目发布
    @Override
    public boolean publish(List<Long> idList, Map<String, OutParamExpand> paramMap) {
        BmsCategory category = this.getOne(Wrappers.lambdaQuery(BmsCategory.class)
                .select(BmsCategory::getParentId)
                .eq(BmsCategory::getId, idList.get(0)));
        if (category != null) {
            // 父栏目是否已发布
            RuleResult ruleResult = RuleCondition.create()
                    .and(PublishStatusRule.init(BmsCategory.class).policy(PublishCheck.PUBLISH_SUCCESS).data(category.getParentId()))
                    .execute();
            if (!ruleResult.isPass()) {
                throw new BizException("发布失败,该栏目的父栏目未发布");
            }
        }
        //栏目 锁定/发布状态 检查
        RuleCondition.create()
                .and(LockStatusRule.init(BmsCategory.class).data(idList))
                .and(PublishStatusRule.init(BmsCategory.class).policy(PublishCheck.CAN_PUBLISH).data(idList))
                .execute().check();
        List<BmsCategory> categories = getBmsCategoryList(idList);
        if (CollectionUtils.isEmpty(categories)) {
            return false;
        }
        BmsCategory obj = categories.get(0);
        // 更新工单的栏目
        List<Long> updateIds = new ArrayList<>();
        // 发布工单的栏目
        List<Long> publishIds = categories.stream().filter(bmsCategory -> {
            if (!RuleUtil.isWaitPublish(bmsCategory.getPublishStatus())) {
                updateIds.add(bmsCategory.getId());
                return false;
            }
            return true;
        }).map(BmsCategory::getId).collect(Collectors.toList());
        // 发布工单
        if (!publishIds.isEmpty()) {
            if (!this.sendCategoryOrder(ActionEnums.REGIST, publishIds, obj.getSpId(), obj.getSpName(), paramMap)) {
                throw new BizException("发布工单下发失败");
            }
        }
        // 更新工单
        if (!updateIds.isEmpty()) {
            if (!this.sendCategoryOrder(ActionEnums.UPDATE, updateIds, obj.getSpId(), obj.getSpName(), paramMap)) {
                throw new BizException("更新工单下发失败");
            }
        }
        return true;
    }

    //栏目回收
    @Override
    public boolean recycle(List<Long> idList) {
        //栏目 锁定/发布状态 检查
        RuleResult ruleResult = RuleCondition.create()
                .and(LockStatusRule.init(BmsCategory.class).data(idList))
                .and(RecycleStatusRule.init(BmsCategory.class).policy(RecycleCheck.CAN_RECYCLE).data(idList))
                .execute();
        if (!ruleResult.isPass()) {
            throw new BizException(ruleResult.getMsg());
        }
        // 是否存在未回收成功的子栏目
        existChildCategory(idList, true);
        // 是否存在未回收成功的内容
        existContent(idList, true);
        List<BmsCategory> categories = this.list(Wrappers.lambdaQuery(BmsCategory.class)
                .notIn(BmsCategory::getPublishStatus, RecycleStatusRule.NOT_RECYCLE_LIST)
                .in(BmsCategory::getId, idList));
        if (CollectionUtils.isEmpty(categories)) {
            return false;
        }
        BmsCategory obj = categories.get(0);
        if (!CollectionUtils.isEmpty(categories)) {
            List<Long> recycleIds = categories.stream().map(BmsCategory::getId).collect(Collectors.toList());
            Map<String, OutParamExpand> paramMap = new HashMap<>();
            paramMap.put(PublishParamTypeConstants.PRIORITY, new OutParamExpand().setPriority(PriorityEnums.GENERAL.getValue()));
            // 回收工单
            if (!this.sendCategoryOrder(ActionEnums.DELETE, recycleIds, obj.getSpId(), obj.getSpName(), paramMap)) {
                throw new BizException("回收工单下发失败");
            }
        }
        return true;
    }

    // 栏目设置生效/失效 已完成
    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = Exception.class)
    public boolean status(BmsCategoryOperationReq operationReq) {
        List<Long> idList = operationReq.getIdList();
        //是否可修改 判断锁定 发布中判断
        RuleResult ruleResult = RuleUtil.canModify(BmsCategory.class, idList);
        if (!ruleResult.isPass()) {
            throw new BizException(ruleResult.getMsg());
        }
        //设置的 set status=x
        Integer status = operationReq.getStatus();
        //条件 where status =!x
        int st = status.equals(StatusEnum.COME.getCode()) ? StatusEnum.LOSE.getCode() : StatusEnum.COME.getCode();
        // 发布成功 更新失败 回收失败的修改为待更新
        this.update(Wrappers.lambdaUpdate(BmsCategory.class)
                .set(BmsCategory::getStatus, status)
                .set(BmsCategory::getPublishStatus, WAITUPDATE.getCode())
                .in(BmsCategory::getPublishStatus, PUBLISH.getCode(), FAILUPDATE.getCode(), FAILROLLBACK.getCode())
                .in(BmsCategory::getId, idList)
                .eq(BmsCategory::getStatus, st));
        // 其它只修改 生效/失效 状态
        this.update(Wrappers.lambdaUpdate(BmsCategory.class)
                .set(BmsCategory::getStatus, status)
                .in(BmsCategory::getId, idList)
                .eq(BmsCategory::getStatus, st));
        this.update(Wrappers.lambdaUpdate(BmsCategory.class)
                .set(BmsCategory::getPublishStatus, WAITPUBLISH.getCode())
                .in(BmsCategory::getId, idList)
                .eq(BmsCategory::getPublishStatus, FAILPUBLISH.getCode()));
        return true;
    }

    // 栏目重置发布状态
    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = Exception.class)
    public boolean resetPublishStatus(List<Long> idList) {
        //栏目 锁定/发布状态 检查
        RuleCondition.create()
                .and(LockStatusRule.init(BmsCategory.class).data(idList))
                .and(PublishStatusRule.init(BmsCategory.class)
                        .policy(PublishCheck.MUST_ING).data(idList).type("存在非下发进行中的数据不可重置"))
                .execute().check();
        // 批量查询
        List<BmsCategory> bmsCategories = this.list(Wrappers.lambdaQuery(BmsCategory.class)
                .select(BmsCategory::getId, BmsCategory::getPublishStatus)
                .in(BmsCategory::getId, idList));
        bmsCategories.forEach(category -> {
            // 确认即将要修改的发布状态
            Integer newStatus = RuleUtil.confirmModifyPublishIngStatus(category.getPublishStatus());
            category.setPublishStatus(newStatus);
        });
        return this.updateBatchById(bmsCategories);
    }

    // 栏目修改发布状态 已完成
    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = Exception.class)
    public boolean updatePublishStatus(BmsCategoryOperationReq operationReq) {
        List<Long> idList = operationReq.getIdList();
        // 判断锁定
        RuleResult ruleResult = RuleUtil.checkLockStatus(BmsCategory.class, idList);
        if (!ruleResult.isPass()) {
            throw new BizException(ruleResult.getMsg());
        }
        if (!idList.isEmpty()) {
            this.update(Wrappers.lambdaUpdate(BmsCategory.class)
                    .set(BmsCategory::getPublishStatus, operationReq.getPublishStatus())
                    .set(BmsCategory::getPublishDescription, "")
                    .in(BmsCategory::getId, idList));
        }
        return true;
    }

    /**
     * @param categoryIds 不为null 则通过叶子节点向上查
     * @param spIds       不为null 则通过根节点向下查
     * <AUTHOR>
     * @Date 2021-11-15 18:01:54
     * @Description 查询栏目树根据所有叶子节点, 返回直达根节点的整个树结构(多根节点树)
     */
    @Override
    public Collection<BmsBelongCategorySpVO> getBelongCategory(List<Long> categoryIds, List<Long> spIds) {
        // 所有的树节点(根节点所有叶子节点 )
        Map<Long, BmsBelongCategoryVO> map;
        // 通过叶子节点向上查
        if (!CollectionUtils.isEmpty(categoryIds)) {
            map = findParentCategory(categoryIds);
        }
        // 通过根节点向下查
        else {
            map = getBmsBelongCategoryVOMap(spIds);
            if (map == null) return null;
        }
        // 构建栏目节点树
        Map<Long, BmsBelongCategorySpVO> data = createTree(map);
        return data.values();
    }

    @Override
    public Collection<BmsBelongCategorySpVO> getCategoryTree(List<Long> spIds) {
        // 所有的树节点(根节点所有叶子节点 )
        Map<Long, BmsBelongCategoryVO> map;
        // 通过根节点向下查
        map = getBmsBelongCategoryVOMap(spIds);
        if (map == null) return null;
        // 构建栏目节点树
        Map<Long, BmsBelongCategorySpVO> data = createTree(map);
        Map<Long, BmsBelongCategorySpVO> resultMap = new LinkedHashMap<>();
        if (ObjectUtil.isNotEmpty(data.values())) {
            for (Long spId : spIds) {
                if (ObjectUtil.isNotEmpty(data.get(spId))) {
                    resultMap.put(spId, data.get(spId));
                } else {
                    //若sp未查询到下属栏目仍然需要查询sp信息放入栏目树中
                    SysSp cacheSp = redisService.getCacheMapValue(RedisKeyConstants.SYS_SP, String.valueOf(spId));
                    SysSp sp = Optional.ofNullable(cacheSp).orElseThrow(() -> new BizException("sp查询异常，spId：" + spId));
                    BmsBelongCategorySpVO bmsBelongCategorySpVO = new BmsBelongCategorySpVO();
                    bmsBelongCategorySpVO.setId(spId).setName(sp.getName()).setChildren(new ArrayList<>());
                    resultMap.put(spId, bmsBelongCategorySpVO);
                }
            }
        }
        return resultMap.values();
    }

    /**
     * 创建栏目树
     *
     * @param map
     * @return
     */
    private Map<Long, BmsBelongCategorySpVO> createTree(Map<Long, BmsBelongCategoryVO> map) {
        List<BmsBelongCategoryVO> categoryVOS = buildCategoryTree(map);
        Map<Long, BmsBelongCategorySpVO> data = new LinkedHashMap<>();
        categoryVOS.forEach(categorys -> {
            BmsBelongCategorySpVO tmp = data.get(categorys.getSpId());
            if (tmp == null) {
                BmsBelongCategorySpVO belongSp = new BmsBelongCategorySpVO();
                belongSp.setId(categorys.getSpId()).setName(categorys.getSpName());
                List<BmsBelongCategoryVO> list = new ArrayList<>();
                list.add(categorys);
                belongSp.setChildren(list);
                data.put(categorys.getSpId(), belongSp);
            } else {
                List<BmsBelongCategoryVO> list = tmp.getChildren();
                list.add(categorys);
            }
        });
        return data;
    }

    private Map<Long, BmsBelongCategoryVO> getBmsBelongCategoryVOMap(List<Long> spIds) {
        Map<Long, BmsBelongCategoryVO> map;
        List<BmsCategory> rootParents = this.list(Wrappers.lambdaQuery(BmsCategory.class)
                .select(BmsCategory::getId)
                .eq(BmsCategory::getParentId, 0)
                .in(BmsCategory::getSpId, spIds)
                .orderByAsc(BmsCategory::getSequence)
                .orderByDesc(BmsCategory::getId));
        if (CollectionUtils.isEmpty(rootParents)) {
            return null;
        }
        List<Long> rootParentIds = rootParents.stream().map(BmsCategory::getId).collect(Collectors.toList());
        map = findChildCategoryAll(rootParentIds);
        return map;
    }

    /**
     * 自动发布反馈-栏目删除操作
     *
     * @param codeList
     * @param spIdList
     * @return
     */
    @Override
    public boolean autoFeedBackCategoryDel(List<String> codeList, List<Long> spIdList) {
        //参数校验
        if (CollectionUtils.isEmpty(codeList) || CollectionUtils.isEmpty(spIdList)) {
            log.warn("自动发布反馈-栏目删除操作,参数codes:{},sps:{} 不全", codeList, spIdList);
            return false;
        }
        //更新栏目状态
        bmsCategoryMapper.update(null, Wrappers.lambdaUpdate(BmsCategory.class)
                .set(BmsCategory::getPublishDescription, SymbolConstant.SPACE)
                .set(BmsCategory::getPublishStatus, ROLLBACK.getCode())
                .in(BmsCategory::getCode, codeList)
                .in(BmsCategory::getSpId, spIdList)
        );
        return true;
    }

    @Override
    public CommonResponse checkCategoryName(Long categoryId, String name, Long spId, Long parentId) {
        List<BmsCategory> bmsCategoryList = new ArrayList<>();
        if (ObjectUtils.isNotEmpty(categoryId)){
            bmsCategoryList = bmsCategoryMapper.selectList(Wrappers.<BmsCategory>lambdaQuery()
            .eq(BmsCategory::getName,name)
            .eq(BmsCategory::getSpId,spId)
            .eq(BmsCategory::getParentId,parentId)
            .ne(BmsCategory::getId,categoryId));
        }else {
            bmsCategoryList = bmsCategoryMapper.selectList(Wrappers.<BmsCategory>lambdaQuery()
                    .eq(BmsCategory::getName,name)
                    .eq(BmsCategory::getSpId,spId)
                    .eq(BmsCategory::getParentId,parentId));
        }
        if (CollectionUtils.isEmpty(bmsCategoryList)) {
            return CommonResponse.success("true");
        }
        return CommonResponse.success("false");
    }

    /**
     * ToDo:20230214 弃用方法
     *
     * @param delCategoryCodes
     * @param spIdList
     * @param isRollbak
     * @return
     */
    @Override
    public boolean deleteByCodeAndSp(List<String> delCategoryCodes, List<Long> spIdList, boolean isRollbak) {
        log.info("自动发布 删除 category=====");
        /*boolean allSpRollback = true;
        Long extendSpContentCount = bmsCategoryMapper.selectCount(Wrappers.lambdaQuery(BmsCategory.class)
                .notIn(BmsCategory::getSpId, spIdList)
                .in(BmsCategory::getCode, delCategoryCodes)
                .notIn(BmsCategory::getPublishStatus,
                        PublishStatusEnum.WAITPUBLISH.getCode(),
                        PublishStatusEnum.ROLLBACK.getCode(),
                        PublishStatusEnum.FAILPUBLISH.getCode()
                )
        );
        if (extendSpContentCount != null && extendSpContentCount > 0) {
            log.error("该Cp Category 已授权给其他非自动发布SP且已经发布到运营商");
            allSpRollback = false;
//            throw new CommonResponseException(CommonResponseEnum.DELETE_ERROR);
        }*/
        if (isRollbak) {
            bmsCategoryMapper.update(null, Wrappers.lambdaUpdate(BmsCategory.class)
                    .set(BmsCategory::getPublishStatus, ROLLBACK.getCode())
                    .in(BmsCategory::getCode, delCategoryCodes)
                    .in(BmsCategory::getSpId, spIdList)
            );

            bmsCategoryContentService.update(Wrappers.lambdaUpdate(BmsCategoryContent.class)
                    .set(BmsCategoryContent::getPublishStatus, ROLLBACK.getCode())
                    .in(BmsCategoryContent::getCategoryCode, delCategoryCodes)
                    .in(BmsCategoryContent::getSpId, spIdList)
            );
            bmsCategoryChannelService.update(Wrappers.lambdaUpdate(BmsCategoryChannel.class)
                    .set(BmsCategoryChannel::getPublishStatus, ROLLBACK.getCode())
                    .in(BmsCategoryChannel::getCategoryCode, delCategoryCodes)
                    .in(BmsCategoryChannel::getSpId, spIdList)
            );
            return true;
        }
        bmsCategoryMapper.delete(Wrappers.lambdaQuery(BmsCategory.class)
                .in(BmsCategory::getCode, delCategoryCodes)
                .in(BmsCategory::getSpId, spIdList)
        );

        List<BmsCategoryContent> contents = bmsCategoryContentService.list(Wrappers.lambdaQuery(BmsCategoryContent.class)
                .in(BmsCategoryContent::getCategoryCode, delCategoryCodes)
                .in(BmsCategoryContent::getSpId, spIdList)
        );

        List<Long> contentIds = contents.stream().map(BmsCategoryContent::getBmsContentId).collect(Collectors.toList());

        bmsCategoryContentService.remove(Wrappers.lambdaQuery(BmsCategoryContent.class)
                .in(BmsCategoryContent::getCategoryCode, delCategoryCodes)
                .in(BmsCategoryContent::getSpId, spIdList)
        );
        bmsCategoryChannelService.remove(Wrappers.lambdaQuery(BmsCategoryChannel.class)
                .in(BmsCategoryChannel::getCategoryCode, delCategoryCodes)
                .in(BmsCategoryChannel::getSpId, spIdList)
        );

        List<BmsCategoryContent> list = bmsCategoryContentService.list(Wrappers.lambdaQuery(BmsCategoryContent.class)
                .in(BmsCategoryContent::getBmsContentId, contentIds));
        bmsCategoryContentService.syncMsgToBmsContent(contentIds, list);
        return true;
    }

    /**
     * <AUTHOR>
     * @Date 2021-11-15 15:03:02
     * @Description 构建栏目树
     */
    private List<BmsBelongCategoryVO> buildCategoryTree(Map<Long, BmsBelongCategoryVO> map) {
        List<BmsBelongCategoryVO> rootNode = new ArrayList<>();
        // 多叉树的问题
        map.forEach((id, value) -> {
            Long parentId = value.getParentId();
            if (parentId == null || parentId == 0) {
                rootNode.add(value);
            } else {
                BmsBelongCategoryVO parent = map.get(parentId);
                if (parent != null) {
                    List<BmsBelongCategoryVO> children = parent.getChildren();
                    if (children == null) {
                        children = new ArrayList<>();
                        parent.setChildren(children);
                    }
                    children.add(value);
                }
            }
        });
        return rootNode;
    }

    /**
     * <AUTHOR>
     * @Date 2021-10-23 15:12:23
     * @Description 通过根节点  返回所有子节点，此场景不适合使用递归会导致频繁创建大量的集合并进行集合拷贝
     */
    private Map<Long, BmsBelongCategoryVO> findChildCategoryAll(List<Long> rootIds) {
        // 存储容器 所有子节点
        //List<BmsCategory> resultContainer = new ArrayList<>();
        Map<Long, BmsBelongCategoryVO> resultContainer = new LinkedHashMap<>();
        // 结果容器存入根栏目
        List<BmsCategory> rootParent = this.list(Wrappers.lambdaQuery(BmsCategory.class)
                .select(BmsCategory::getId, BmsCategory::getCode, BmsCategory::getName, BmsCategory::getParentId, BmsCategory::getParentCode,
                        BmsCategory::getLeafNode, BmsCategory::getSpId, BmsCategory::getSpName)
                .in(BmsCategory::getId, rootIds)
                .orderByAsc(BmsCategory::getSequence)
                .orderByDesc(BmsCategory::getId));
        rootParent.forEach(r -> resultContainer.put(r.getId(), buildBelongCategory(r)));
        // 临时子id容器
        // 存入第一批id(根节点)
        List<Long> childIdContainer = new ArrayList<>(rootIds);
        // 如果id容器里 始终存在开始的根节点(或者上次已经查询过的节点id)，那么每一次查询都将不可能为空，所以下面缓存到查询结果后会清空掉id容器
        for (; ; ) {
            // 查询的返回结果
            List<BmsCategory> result = this.list(Wrappers.lambdaQuery(BmsCategory.class)
                    .select(BmsCategory::getId, BmsCategory::getCode, BmsCategory::getName, BmsCategory::getParentId, BmsCategory::getParentCode,
                            BmsCategory::getLeafNode,
                            BmsCategory::getSpId, BmsCategory::getSpName)
                    .in(BmsCategory::getParentId, childIdContainer)
                    .orderByAsc(BmsCategory::getSequence)
                    .orderByDesc(BmsCategory::getId));
            childIdContainer.clear();
            if (result.isEmpty()) {
                break;
            }
            // 查询结果处理
            for (BmsCategory category : result) {
                if (category.getId().equals(category.getParentId())) {
                    throw new BizException("栏目数据存在异常,父栏目不能为自身 - 错误栏目：" + category);
                }
                // 查询条件容器：存储每一批最新的id列
                childIdContainer.add(category.getId());
                // 子栏目容器：存储每一批最新的栏目
                //resultContainer.add(category);
                BmsBelongCategoryVO belongCategoryVO = buildBelongCategory(category);
                resultContainer.put(category.getId(), belongCategoryVO);
            }
        }
        return resultContainer;
    }

    /**
     * <AUTHOR>
     * @Date 2021-11-15 15:03:02
     * @Description 通过叶子节点 查询所有的间接父栏目
     */
    private Map<Long, BmsBelongCategoryVO> findParentCategory(List<Long> ids) {
        // 存储容器 所有子节点
        Map<Long, BmsBelongCategoryVO> resultContainer = new HashMap<>();
        // 存入第一批id(叶子节点)
        List<Long> parentIdContainer = new ArrayList<>(ids);
        for (; ; ) {
            // 查询的返回结果
            List<BmsCategory> result = this.list(Wrappers.lambdaQuery(BmsCategory.class)
                    .select(BmsCategory::getId, BmsCategory::getCode,
                            BmsCategory::getParentId, BmsCategory::getName, BmsCategory::getSpId,
                            BmsCategory::getSpName, BmsCategory::getLeafNode)
                    .in(BmsCategory::getId, parentIdContainer));
            parentIdContainer.clear();
            if (result.isEmpty()) {
                break;
            }
            // 查询结果处理
            for (BmsCategory category : result) {
                if (category.getId().equals(category.getParentId())) {
                    throw new BizException("栏目数据存在异常,父栏目不能为自身 - 错误栏目：" + category);
                }
                // 查询条件容器：存储每一批最新的id列
                parentIdContainer.add(category.getParentId());
                // 栏目容器：存储每一批最新的栏目
                BmsBelongCategoryVO belongCategoryVO = buildBelongCategory(category);
                resultContainer.put(category.getId(), belongCategoryVO);
            }
        }
        return resultContainer;
    }

    /**
     * <AUTHOR>
     * @Date 2021-11-15 15:03:02
     * @Description 构建栏目节点树需要的字段
     */
    private BmsBelongCategoryVO buildBelongCategory(BmsCategory category) {
        BmsBelongCategoryVO belongCategoryVO = new BmsBelongCategoryVO();
        belongCategoryVO.setId(category.getId())
                .setName(category.getName())
                .setCode(category.getCode())
                .setParentId(category.getParentId())
                .setSpId(category.getSpId())
                .setSpName(category.getSpName())
                .setLeafNode(category.getLeafNode())
                .setParentCode(category.getParentCode());
        return belongCategoryVO;
    }

    @Override
    public BmsCategoryVO findCategoryById(Long id) {
        BmsCategory bmsCategory = this.getById(id);
        if (bmsCategory == null) {
            return null;
        }
        List<BmsPicture> list = bmsPictureService.list(Wrappers.lambdaQuery(BmsPicture.class)
                .eq(BmsPicture::getBmsContentId, bmsCategory.getId())
                .eq(BmsPicture::getContentType, ContentTypeEnum.CATEGORY_PICTURE.getValue())
        );
        BmsCategoryVO bmsCategoryVO = BeanUtil.copyProperties(bmsCategory, BmsCategoryVO.class);
        bmsCategoryVO.setPicArray(list);
        return bmsCategoryVO;
    }

    @Override
    public Tuple2<Boolean, List<BmsCategory>> getLockStatus(String[] ids) {
        List<BmsCategory> result = new ArrayList<>();
        List<BmsCategory> error = new ArrayList<>();
        for (String id : ids) {
            if (!StringUtils.isEmpty(id)) {
                BmsCategory bmsCategory = this.getById(id);
                if (bmsCategory != null) {
                    if (bmsCategory.getLockStatus() != null && bmsCategory.getLockStatus() == 1) {
                        result.add(bmsCategory);
                    } else {
                        error.add(bmsCategory);
                        return new Tuple2<>(false, error);
                    }
                }
            }
        }
        return new Tuple2<>(true, result);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean modifyPublishStatus(List<Long> ids, Integer publishStatus) {
        if (CollectionUtils.isEmpty(ids)) return true;
        RuleResult rr = RuleCondition.create()
                //判断栏目的发布状态
                .and(LockStatusRule.init(BmsCategory.class).data(ids))
                //检查是否有 发布中 的内容
                .and(PublishStatusRule.init(BmsCategory.class).data(ids).policy(ING))
                .execute();
        if (!rr.isPass()) {
            throw new BizException(rr.getMsg());
        }

        //step1:修改媒资本身的发布状态
        LambdaUpdateWrapper<BmsCategory> updateWrapper = Wrappers.lambdaUpdate(BmsCategory.class)
                .in(BmsCategory::getId, ids)
                .set(BmsCategory::getPublishStatus, publishStatus);
        this.update(updateWrapper);

        //如果是修改为发布成功的，只修改当前媒资本身即可,结束执行
        if (PUBLISH.getCode().equals(publishStatus)) return true;

        //step2:设置栏目图片的 发布状态
        //step2.1:查找栏目对应的图片
        List<Long> picIds =
                bmsPictureService.getPicByContentIds(ids, ContentTypeEnum.CATEGORY.getValue()).stream().map(BmsPicture::getId).collect(Collectors.toList());
        //step2.2:设置图片的发布状态
        bmsPictureService.modifyPublishStatus(picIds, publishStatus);

        //step3:查询栏目下的 栏目内容关系 并设置发布状态
        //step3.1:查询栏目下的所有栏目内容关系
        List<Long> categoryContentIds = bmsCategoryContentService.getByCategoryIds(ids).stream().map(BmsCategoryContent::getId).collect(Collectors.toList());
        //step3.2:设置发布状态
        bmsCategoryContentService.modifyPublishStatus(categoryContentIds, publishStatus);

        //step4:查询栏目下的子栏目 并设置发布状态
        for (Long item : ids) {
            //step4.1:找到其中一个栏目的所有子栏目id
            List<Long> itemSubIds = getSubCategory(item).stream().map(BmsCategory::getId).collect(Collectors.toList());
            //step4.2:对子栏目进行递归 设置发布状态
            this.modifyPublishStatus(itemSubIds, publishStatus);
        }
        return true;
    }

    //根据pid查找子节点
    private List<BmsCategory> getSubCategory(Long pId) {
        LambdaUpdateWrapper<BmsCategory> wrapper = Wrappers.lambdaUpdate(BmsCategory.class).eq(BmsCategory::getParentId, pId);
        return this.list(wrapper);
    }

    /**
     * <AUTHOR>
     * @Date 2021-11-06 15:12:23
     * @Description 下发栏目
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean sendCategoryOrder(ActionEnums actionEnums, List<Long> categories, Long spId, String spName, Map<String, OutParamExpand> paramMap) {
        if (CollectionUtils.isEmpty(categories)) return true;
        Map<String, String> picMap;
        // 如果是回收
        if (ActionEnums.DELETE.getCode().equals(actionEnums.getCode())) {
            // 查询所有可回收的图片
            List<Long> recycleIds = bmsPictureService.pictureCanBeRecycled(categories, ContentTypeEnum.CATEGORY);
            picMap = bmsPictureService.buildRecycleWorkOrderMap(recycleIds);
        } else {
            // 查询可跟随本次 发布|更新 的图片
            picMap = bmsPictureService.findCanPublishPicture(categories, ContentTypeEnum.CATEGORY);
        }
        List<Long> publishPicIds = new ArrayList<>();
        List<Long> updatePicIds = new ArrayList<>();
        List<Long> recyclePicIds = new ArrayList<>();
        if (!CollectionUtils.isEmpty(picMap)) {
            picMap.forEach((id, action) -> {
                // 更新工单的图片id
                if (action.equals(ActionEnums.REGIST.getCode().toString())) {
                    publishPicIds.add(Long.valueOf(id));
                } else if (action.equals(ActionEnums.UPDATE.getCode().toString())) {
                    updatePicIds.add(Long.valueOf(id));
                } else if (action.equals(ActionEnums.DELETE.getCode().toString())) {
                    recyclePicIds.add(Long.valueOf(id));
                }
            });
        }
        return workOrderOperation.send(actionEnums, ContentTypeEnum.CATEGORY, categories, picMap, spId, spName,
                (success, publishStatus, description) -> {
                    // 更新发布状态以及发布描述
                    this.update(Wrappers.lambdaUpdate(BmsCategory.class)
                            .set(BmsCategory::getPublishTime, new Date())
                            .set(BmsCategory::getPublishStatus, publishStatus)
                            .set(!success, BmsCategory::getPublishDescription, description)
                            .in(BmsCategory::getId, categories));
                    // 更新图片发布状态以及发布描述
                    bmsPictureService.pictureIssuedProcess(success, publishPicIds, updatePicIds, recyclePicIds, description);
                }, paramMap);
    }

    private List<BmsCategory> getBmsCategoryList(List<Long> ids) {
        return this.list(Wrappers.lambdaQuery(BmsCategory.class)
                .select(BmsCategory::getId, BmsCategory::getSpId, BmsCategory::getSpName, BmsCategory::getPublishStatus)
                .in(BmsCategory::getId, ids));
    }

    @Override
    public boolean updatePublishStatus(List<SubOrderObjectsEntity> orderObjectsEntities, List<Long> spIdList) {
        int result = bmsCategoryMapper.updatePublishStatus(orderObjectsEntities, spIdList);
        return result >= 1;
    }

    @Override
    public BmsCategory getExtracodeById(Long id) {
        if (id.equals(0)) {
            return null;
        }
        BmsCategory bmsCategory = this.getById(id);
        return bmsCategory;
    }

    @Override
    public CommonResponse<Boolean> priortyPublish(List<Long> ids, Map<String, OutParamExpand> paramMap) {
        boolean result = this.publish(ids, paramMap);
        return result ? CommonResponse.success(result) : CommonResponse.commonfail("发布失败");
    }

    @Override
    public List<BmsCategory> getByCode(String code, Long spId, Boolean isExtraCode){
        //检验是否是父栏目查询
        if("0".equals(code)){
            return new ArrayList<>();
        }
        LambdaQueryWrapper<BmsCategory> lambdaQueryWrapper = Wrappers.lambdaQuery(BmsCategory.class)
               .eq(BmsCategory::getSpId,spId);
        if(isExtraCode){
            lambdaQueryWrapper.eq(BmsCategory::getExtraCode,code);
        }else{
            lambdaQueryWrapper.eq(BmsCategory::getCode,code);
        }
        return bmsCategoryMapper.selectList(lambdaQueryWrapper);
    }
}
