package com.pukka.iptv.manage.service.bms;


import java.util.HashMap;
import java.util.Map;

public interface BmsSequenceApi
{
    //临时数据（方法入参）
    public final static ThreadLocal<Map<Long, Integer>> TMP_DATA = new ThreadLocal<>();


    default void setSequence(Long key, Integer value) {
        Map<Long, Integer> store = TMP_DATA.get();
        if (store == null) {
            store = new HashMap<>();
            TMP_DATA.set(store);
        }
        store.put(key, value);
        TMP_DATA.set(store);
    }

    default Boolean getArgs(Long key){
        Map<Long, Integer> store = TMP_DATA.get();
        if (store==null ||store.get(key) == null) {
            return false;
        }
        return true;
    }
    default Integer getSequence(Long key) {
        Map<Long, Integer> store = TMP_DATA.get();
        if (store == null) return null;
        Integer sequence = store.get(key);
        if (sequence == null) {
            sequence = 999;
        }else if (sequence > 1){
            sequence--;
        }
        store.put(key,sequence);
        TMP_DATA.set(store);
        return sequence;
    }
    default void clearArgs() {
        TMP_DATA.remove();
    }
}
