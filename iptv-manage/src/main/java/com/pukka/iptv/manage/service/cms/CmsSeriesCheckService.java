package com.pukka.iptv.manage.service.cms;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.pukka.iptv.common.data.dto.CmsSeriesDO;
import com.pukka.iptv.common.data.dto.CmsSeriesEndDto;
import com.pukka.iptv.common.data.model.cms.CmsSeries;
import com.pukka.iptv.common.data.vo.cms.CmsSeriesDetailVO;
import com.pukka.iptv.common.data.vo.cms.CmsSeriesVO;

public interface CmsSeriesCheckService extends IService<CmsSeries> {
    /**
     * 剧集自审列表
     * @param page
     * @param cmsSeries
     * @return
     */
    Page<CmsSeries> seriesSelfList(Page page, CmsSeriesVO cmsSeries, String startTime, String endTime);

    IPage<CmsSeries> seriesSelfList(Page page, CmsSeriesDO cmsSeriesDO, String startTime, String endTime, boolean flag);

    /**
     * 剧集详情
     * @param id
     * @return
     */
    CmsSeriesDetailVO seriesById(Long id);

    /**
     * 剧集终重列表
     * @param page
     * @param cmsSeriesEndDto
     * @return
     */
    Page<CmsSeries> seriesEndList(Page page, CmsSeriesEndDto cmsSeriesEndDto);

    /**
     * 剧集导出
     * @param response
     * @param cmsSeries
     */
     Object export( CmsSeriesVO cmsSeries, String startTime, String endTime) throws InterruptedException;
}
