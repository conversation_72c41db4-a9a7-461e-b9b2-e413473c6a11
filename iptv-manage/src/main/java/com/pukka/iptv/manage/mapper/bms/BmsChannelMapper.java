package com.pukka.iptv.manage.mapper.bms;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.pukka.iptv.common.data.annotation.DataPermission;
import com.pukka.iptv.common.data.dto.SysAuthorizationChannelDto;
import com.pukka.iptv.common.data.model.bms.BmsChannel;
import com.pukka.iptv.common.data.model.order.SubOrderObjectsEntity;
import com.pukka.iptv.common.data.params.SysAuthorizationParam;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Set;

/**
 * 频道
 *
 * <AUTHOR>
 * @email
 * @date 2021-08-27 14:45:33
 */

@Mapper
@DataPermission(config = "cp_id=cpIds")
public interface BmsChannelMapper extends BaseMapper<BmsChannel>
{

    /**
     * 频道分页查询
     * @param page
     * @param bmsChannel
     * @param categoryId
     * @param status
     * @param cpIdSet
     * @return
     */
    IPage<BmsChannel> getChannelsPage(@Param("page") Page<BmsChannel> page, @Param("bmsChannel") BmsChannel bmsChannel, @Param("categoryId") String categoryId, @Param("status") Integer status, @Param("names") String[] names, @Param("cpIdSet") Set<Long> cpIdSet);


    /**
     * 查询未被授权的频道列表
     *
     * @param page  分页参数
     * @return page
     */
    Page<SysAuthorizationChannelDto> getUnauthorizedChannelPage(Page page, @Param("param") SysAuthorizationParam param);


    IPage<BmsChannel> getNotBindChannelsPage(Page<BmsChannel> page,@Param("categoryId") Long categoryId,@Param("spId")Long spId,@Param("channelName")String channelName,@Param("cpId")Long cpId);

    String checkRollback(@Param("ids")List<Long> ids);

    /**
     *授权频道-查询授权频道列表
     *
     * @param page 分页参数
     * @param param 查询参数
     * @param spIdSet
     * @return
     */
    Page<SysAuthorizationChannelDto> getAuthorizationChannelList(@Param("page") Page page, @Param("param") SysAuthorizationParam param, @Param("spIdSet") Set<Long> spIdSet);

    int updatePublishStatus(@Param("entityList") List<SubOrderObjectsEntity> orderObjectsEntities, @Param("spIdList") List<Long> spIdList);

    @DataPermission(config = "a.cp_id=cpIds")
    long getChannelsTotal(@Param("bmsChannel") BmsChannel bmsChannel, @Param("categoryId") String categoryId, @Param("status") Integer status, @Param("names") String[] channelNames);
}
