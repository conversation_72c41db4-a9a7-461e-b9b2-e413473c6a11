package com.pukka.iptv.manage.service.cms;

import com.baomidou.mybatisplus.extension.service.IService;
import com.pukka.iptv.common.base.vo.CommonResponse;
import com.pukka.iptv.common.data.model.cms.CmsMovie;

import java.util.List;

/**
 * 视频
 */
public interface CmsMovieService extends IService<CmsMovie> {

    /**
     * 视频列表查询
     * @param cmsContentId
     * @param contentType
     * @return
     */
    CommonResponse listByCmsContentIdAndType(Long cmsContentId, Integer contentType, Long cpId,Integer type);

    List<CmsMovie> getByContentIdContentType(Long contentId, Integer contentType,Long cpId);

    CmsMovie getByCode(String code);

    List<CmsMovie> listByResourceId(List<Long> resourceIdList);

    void deleteByIds(List<Long> cmsMovieIds);
}


