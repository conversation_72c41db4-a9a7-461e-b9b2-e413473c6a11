package com.pukka.iptv.manage.service.bms.common.service.Impl;

import com.pukka.iptv.common.base.enums.ActionEnums;
import com.pukka.iptv.common.data.model.bms.UpdatePublishStatusInterface;
import com.pukka.iptv.manage.service.bms.common.service.GenerateSendApi;
import com.pukka.iptv.manage.service.condition.rule.PublishStatusRule;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * @Author: wangbo
 * @Date: 2022/4/1 15:12
 */
public class GenerateSendImpl<T extends UpdatePublishStatusInterface> implements GenerateSendApi<T> {

    @Override
    public Map<String, String> generateSendMap(List<T> list, boolean isDelete) {
        Map<String, String> sendMap = new HashMap<>();
        list.forEach(t -> {
            if (isDelete) {
                sendMap.put(t.getId() + "", ActionEnums.DELETE.getCode() + "");
            } else {
                if (PublishStatusRule.CAN_UPDATE_LIST.contains(t.getPublishStatus())) {
                    sendMap.put(t.getId() + "", ActionEnums.UPDATE.getCode() + "");
                } else if (PublishStatusRule.PUBLISH_LIST.contains(t.getPublishStatus())) {
                    sendMap.put(t.getId() + "", ActionEnums.REGIST.getCode() + "");
                }
            }
        });
        return sendMap;
    }
}
