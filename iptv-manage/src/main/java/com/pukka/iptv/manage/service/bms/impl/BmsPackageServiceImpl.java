package com.pukka.iptv.manage.service.bms.impl;

import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.pukka.iptv.common.base.constant.PublishParamTypeConstants;
import com.pukka.iptv.common.base.constant.RedisKeyConstants;
import com.pukka.iptv.common.base.enums.*;
import com.pukka.iptv.common.base.exception.BizException;
import com.pukka.iptv.common.base.util.UUID;
import com.pukka.iptv.common.base.vo.CommonResponse;
import com.pukka.iptv.common.core.constant.SymbolConstant;
import com.pukka.iptv.common.data.model.OutParamExpand;
import com.pukka.iptv.common.data.model.bms.*;
import com.pukka.iptv.common.data.model.order.SubOrderObjectsEntity;
import com.pukka.iptv.common.data.model.sys.SysInPassage;
import com.pukka.iptv.common.data.model.sys.SysSp;
import com.pukka.iptv.common.data.vo.req.BmsCategoryCommonReq;
import com.pukka.iptv.common.data.vo.req.BmsPackageInjectionReq;
import com.pukka.iptv.common.data.vo.req.BmsPackageOperationReq;
import com.pukka.iptv.common.data.vo.req.BmsPackageQueryReq;
import com.pukka.iptv.common.rabbitmq.config.CmsContentRenameMQConfig;
import com.pukka.iptv.common.redis.service.RedisService;
import com.pukka.iptv.manage.mapper.bms.BmsPackageMapper;
import com.pukka.iptv.manage.service.bms.BmsContentService;
import com.pukka.iptv.manage.service.bms.BmsPackageContentService;
import com.pukka.iptv.manage.service.bms.BmsPackageService;
import com.pukka.iptv.manage.service.bms.common.WorkOrderOperation;
import com.pukka.iptv.manage.service.condition.RuleCondition;
import com.pukka.iptv.manage.service.condition.RuleResult;
import com.pukka.iptv.manage.service.condition.rule.LockStatusRule;
import com.pukka.iptv.manage.service.condition.rule.PublishStatusRule;
import com.pukka.iptv.manage.service.condition.rule.RecycleStatusRule;
import com.pukka.iptv.manage.service.condition.rule.enums.RecycleCheck;
import com.pukka.iptv.manage.service.sys.impl.SysInPassageServiceImpl;
import com.pukka.iptv.manage.util.CommonUtils;
import com.pukka.iptv.manage.util.RuleUtil;
import com.pukka.iptv.manage.util.StrUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.amqp.rabbit.core.RabbitTemplate;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.util.*;
import java.util.stream.Collectors;

import static com.pukka.iptv.common.base.enums.PublishStatusEnum.*;
import static com.pukka.iptv.common.core.util.StringUtils.rmTarWithSplit;
import static com.pukka.iptv.manage.service.condition.rule.enums.PublishCheck.*;

/**
 * 产品包
 *
 * <AUTHOR>
 * @date 2021-08-27 10:20:57
 */

@Service
@Slf4j
public class BmsPackageServiceImpl extends ServiceImpl<BmsPackageMapper, BmsPackage> implements BmsPackageService {

    @Autowired
    private BmsContentService bmsContentService;
    @Autowired
    private BmsPackageContentService bmsPackageContentService;
    @Autowired
    private RedisService redisService;
    @Autowired
    private WorkOrderOperation workOrderOperation;
    @Autowired
    private SysInPassageServiceImpl sysInPassageService;//已缓存
    @Autowired
    private BmsPackageMapper bmsPackageMapper;
    @Autowired
    private RabbitTemplate rabbitTemplate;

    public static String TIME_SUFFIX = "000000";

    // 新增产品包
    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = Exception.class)
    public boolean savePackage(BmsPackage saveReq) {
        // 查询是否有重复的产品包名称 存在则异常中断
        isExistPackage(false, null, saveReq.getSpId(), saveReq.getName());
        SysSp cacheSp = redisService.getCacheMapValue(RedisKeyConstants.SYS_SP, String.valueOf(saveReq.getSpId()));
        SysSp sp = Optional.ofNullable(cacheSp).orElseThrow(() -> new BizException("sp查询不到"));
        String code = UUID.generalPlatformUUID();
        //时间组件格式化
        saveReq.setSpName(sp.getName())
                .setOutPassageIds(sp.getOutPassageIds())
                .setOutPassageNames(sp.getOutPassageNames())
                .setBmsSpChannelId(sp.getBmsSpChannelId())
                .setCode(code)
                .setExtraCode(code)
                .setSequence(saveReq.getSequence())
                //兼容前端时间格式化
                .setLicensingWindowStart(org.apache.commons.lang3.StringUtils.substring(saveReq.getLicensingWindowStart(), 0, 8) + TIME_SUFFIX)
                .setLicensingWindowEnd(org.apache.commons.lang3.StringUtils.substring(saveReq.getLicensingWindowEnd(), 0, 8) + TIME_SUFFIX)
                .setBmsSpChannelName(sp.getBmsSpChannelName());
        return this.save(saveReq);
    }

    // 新增产品包
    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = Exception.class)
    public boolean savePackage(BmsPackageInjectionReq saveReq) {
        // 查询是否有重复的产品包名称 存在则异常中断
        isExistPackage(false, null, saveReq.getSpId(), saveReq.getName());
        SysSp cacheSp = redisService.getCacheMapValue(RedisKeyConstants.SYS_SP, String.valueOf(saveReq.getSpId()));
        SysSp sp = Optional.ofNullable(cacheSp).orElseThrow(() -> new BizException("sp查询不到"));
        saveReq.setSpName(sp.getName())
                .setOutPassageIds(sp.getOutPassageIds())
                .setOutPassageNames(sp.getOutPassageNames())
                .setBmsSpChannelId(sp.getBmsSpChannelId())
                .setBmsSpChannelName(sp.getBmsSpChannelName());
        // 根据通道属性设置发布状态
        SysInPassage sysInPassage = sysInPassageService.sysInPassage(saveReq.getCspId());
        if (sysInPassage != null && InPassageTypeEnum.TOW.getCode().equals(sysInPassage.getSpPublishType())) {
            // 只有明确的配置了 默认为发布成功 才设置
            saveReq.setPublishStatus(PublishStatusEnum.PUBLISH.getCode())
                    .setPublishTime(new Date())
                    .setPublishDescription("系统配置,自动发布");
        }
        return this.save(saveReq);
    }

    // 产品包是否存在
    private void isExistPackage(boolean isUpdate, Long id, Long spId, String name) {
        long count = this.count(
                Wrappers.lambdaQuery(BmsPackage.class)
                        .eq(BmsPackage::getSpId, spId)
                        .eq(BmsPackage::getName, name)
                        // 是否是更新操作 更新操作排除掉自身再查重
                        .ne(isUpdate, BmsPackage::getId, id));
        if (count != 0) {
            throw new BizException("产品包名称重复");
        }
    }


    // 产品包分页查询
    @Override
    public IPage<BmsPackage> queryList(Page<BmsPackage> page, BmsPackageQueryReq queryReq) {
        LambdaQueryWrapper<BmsPackage> wrapper = Wrappers.lambdaQuery(BmsPackage.class)
                .eq(Objects.nonNull(queryReq.getBmsSpChannelId()), BmsPackage::getBmsSpChannelId, queryReq.getBmsSpChannelId())
                .eq(Objects.nonNull(queryReq.getPriceType()), BmsPackage::getPriceType, queryReq.getPriceType())
                .eq(Objects.nonNull(queryReq.getPublishStatus()), BmsPackage::getPublishStatus, queryReq.getPublishStatus())
                .eq(Objects.nonNull(queryReq.getSpId()), BmsPackage::getSpId, queryReq.getSpId())
                .orderByAsc(BmsPackage::getSequence)
                .orderByDesc(BmsPackage::getId);
        //未换行，模糊查询,换行后精确查询
        //判断name是否含有回车 若含有回车则进行精准多条查询
        if (queryReq.getName() != null) {
            if (queryReq.getName().contains("\n")) {
                //防止name为空字符串以及只有回车的情况  进行再一次校验
                queryReq.setName(queryReq.getName().trim());
                if (com.baomidou.mybatisplus.core.toolkit.StringUtils.isNotEmpty(queryReq.getName())) {
                    String[] names = CommonUtils.getNames(queryReq.getName());
                    if (names != null) {
                        wrapper.in(BmsPackage::getName, Arrays.asList(names));
                    }
                }
            } else {
                String[] names = CommonUtils.getNames(queryReq.getName());
                if (names != null && names.length > 0) {
                    wrapper.like(BmsPackage::getName, names[0]);
                }
            }
        }
        return bmsPackageMapper.selectPage(page, wrapper);

    }

    // 产品包修改
    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = Exception.class)
    public boolean updatePackage(BmsPackage updateReq) {
        RuleResult rr = RuleCondition.create()
                //锁定判断
                .and(LockStatusRule.init(BmsPackage.class).data(updateReq.getId()))
                //发布状态
                .and(PublishStatusRule.init(BmsPackage.class).policy(ING).data(updateReq.getId()))
                .execute();
        if (!rr.isPass()) {
            throw new BizException(rr.getMsg());
        }
        BmsPackage bmsPackage = Optional.ofNullable(this.getById(updateReq.getId())).orElseThrow(() -> new BizException("产品包不存在"));
        // 判断是否重复
        isExistPackage(true, bmsPackage.getId(), bmsPackage.getSpId(), updateReq.getName());
        // 发布成功 更新失败 回收失败 修改为待更新 发布失败修改为待发布
        Integer newStatus = RuleUtil.confirmPublishStatus(bmsPackage.getPublishStatus());
        updateReq.setPublishStatus(newStatus)
                //兼容前端时间格式传值
                .setLicensingWindowStart(org.apache.commons.lang3.StringUtils.substring(updateReq.getLicensingWindowStart(), 0, 8) + TIME_SUFFIX)
                .setLicensingWindowEnd(org.apache.commons.lang3.StringUtils.substring(updateReq.getLicensingWindowEnd(), 0, 8) + TIME_SUFFIX);
        boolean success = this.update(
                Wrappers.lambdaUpdate(BmsPackage.class)
                        .set(StringUtils.hasText(updateReq.getName()), BmsPackage::getName, updateReq.getName())
                        .set(Objects.nonNull(updateReq.getType()), BmsPackage::getType, updateReq.getType())
                        .set(Objects.nonNull(updateReq.getPriceType()), BmsPackage::getPriceType, updateReq.getPriceType())
                        .set(Objects.nonNull(updateReq.getPrice()), BmsPackage::getPrice, updateReq.getPrice())
                        .set(StringUtils.hasText(updateReq.getLicensingWindowStart()), BmsPackage::getLicensingWindowStart, updateReq.getLicensingWindowStart())
                        .set(StringUtils.hasText(updateReq.getLicensingWindowEnd()), BmsPackage::getLicensingWindowEnd, updateReq.getLicensingWindowEnd())
                        .set(StringUtils.hasText(updateReq.getDescription()), BmsPackage::getDescription, updateReq.getDescription())
                        .set(Objects.nonNull(updateReq.getStatus()), BmsPackage::getStatus, updateReq.getStatus())
                        .set(Objects.nonNull(updateReq.getPublishStatus()), BmsPackage::getPublishStatus, updateReq.getPublishStatus())
                        .set(Objects.nonNull(updateReq.getSequence()),BmsPackage::getSequence,updateReq.getSequence())
                        .eq(BmsPackage::getId, updateReq.getId()));
        if (success) {
            // 修改关系表名称
            bmsPackageContentService.update(Wrappers.lambdaUpdate(BmsPackageContent.class)
                    .set(BmsPackageContent::getPackageName, updateReq.getName())
                    .ne(BmsPackageContent::getPackageName, updateReq.getName())
                    .eq(BmsPackageContent::getPackageId, updateReq.getId()));
            sendMQ(bmsPackage,updateReq);
        }
        return success;
    }

    private void sendMQ(BmsPackage bmsPackage, BmsPackage updateReq) {
        try {
            HashMap<String, String> map = new HashMap<>();
            map.put("packageCode", bmsPackage.getCode());
            if (com.pukka.iptv.common.core.util.StringUtils.isNotEmpty(updateReq.getName())
                    && !bmsPackage.getName().equals(updateReq.getName())) {
                map.put("packageName", updateReq.getName());
            }else {
                return;
            }
            HashMap<String, Object> hashMap = new HashMap<>();
            BmsPackageContent bmsPackageContent = JSON.parseObject(JSON.toJSONString(map), BmsPackageContent.class);
            hashMap.put(StatisticsMediaMQEnum.BMS_PACKAGE_CONTENT.getValue(),bmsPackageContent);
            log.info("package.sendMQ.hashMap={}",JSON.toJSONString(hashMap));
            this.rabbitTemplate.convertAndSend(CmsContentRenameMQConfig.CMS_CONTENT_RENAME_EXCHANGE,
                    CmsContentRenameMQConfig.CMS_CONTENT_RENAME_ROUTING, hashMap);
        }catch (Exception e){
            log.error("BmsPackageServiceImpl.sendMQ.bmsPackage={},updateReq={}", JSON.toJSONString(bmsPackage), JSON.toJSONString(updateReq));
        }
    }

    // 产品包删除 
    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = Exception.class)
    public boolean removePackageByIds(List<Long> idList) {
        // 判断锁定 发布状态检查
        RuleCondition.create()
                .and(LockStatusRule.init(BmsPackage.class).data(idList))
                .and(PublishStatusRule.init(BmsPackage.class).policy(UN_PUBLISHED).data(idList).type("删除"))
                .execute().check();
        // 是否还存在绑定内容
        existContent(idList);
        if (!this.removeByIds(idList)) {
            throw new BizException("删除产品包失败");
        }
        return true;
    }

    // true存在绑定内容
    private void existContent(List<Long> ids) {
        // 产品包内容
        BmsPackageContent one = bmsPackageContentService.getOne(
                Wrappers.lambdaQuery(BmsPackageContent.class)
                        .select(BmsPackageContent::getContentName, BmsPackageContent::getPackageName)
                        .in(BmsPackageContent::getPackageId, ids)
                        .last("limit 1"));
        if (one != null) {
            throw new BizException(one.getPackageName() + " 存在绑定内容,无法删除(内容名称: " + one.getContentName() + ")");
        }
    }

    // 产品包批量解锁/锁定 
    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = Exception.class)
    public boolean updateBatchLockByIds(BmsPackageOperationReq operationReq) {
        List<Long> idList = operationReq.getIdList();
        return this.update(
                Wrappers.lambdaUpdate(BmsPackage.class)
                        .set(BmsPackage::getLockStatus, operationReq.getLockStatus())
                        .in(BmsPackage::getId, idList));
    }

    // 产品包发布
    @Override
    public boolean publish(List<Long> idList, Map<String, OutParamExpand> paramMap) {
        // 锁定判断 发布检查
        RuleCondition.create()
                .and(LockStatusRule.init(BmsPackage.class).data(idList))
                .and(PublishStatusRule.init(BmsPackage.class).policy(CAN_PUBLISH).data(idList).type(WorkOrderTypeEnum.PUBLISH.getMsg()))
                .execute().check();

        List<BmsPackage> packages = getBmsPackageList(idList);
        if (CollectionUtils.isEmpty(packages)) {
            return false;
        }
        HashMap<String, List<Long>> updateMap = new HashMap<>();
        Map<String, List<Long>> registMap = packages.stream()
                .filter(bmsPackage -> {
                    if (!RuleUtil.isWaitPublish(bmsPackage.getPublishStatus())) {
                        if (updateMap.containsKey(bmsPackage.getSpId() + ":" + bmsPackage.getSpName())) {
                            List<Long> bmsPackage1 = updateMap.get(bmsPackage.getSpId() + ":" + bmsPackage.getSpName());
                            bmsPackage1.add(bmsPackage.getId());
                            updateMap.put(bmsPackage.getSpId() + ":" + bmsPackage.getSpName(), bmsPackage1);
                        } else {
                            ArrayList<Long> bmsPackages = new ArrayList<>();
                            bmsPackages.add(bmsPackage.getId());
                            updateMap.put(bmsPackage.getSpId() + ":" + bmsPackage.getSpName(), bmsPackages);
                        }
                        return false;
                    }
                    return true;
                }).collect(Collectors.groupingBy(b -> b.getSpId() + ":" + b.getSpName(), Collectors.mapping(BmsPackage::getId, Collectors.toList())));
        // 发布工单
        if (!registMap.isEmpty()) {
            for (String spIdName : registMap.keySet()) {
                List<Long> publishIds = registMap.get(spIdName);
                String[] split = spIdName.split(":");
                if (!this.sendPackageOrder(ActionEnums.REGIST, publishIds, Long.valueOf(split[0]), split[1], paramMap)) {
                    throw new BizException("发布工单下发失败");
                }
            }
        }
        // 更新工单
        if (!updateMap.isEmpty()) {
            for (String spIdName : updateMap.keySet()) {
                List<Long> updateIds = updateMap.get(spIdName);
                String[] split = spIdName.split(":");
                if (!this.sendPackageOrder(ActionEnums.UPDATE, updateIds, Long.valueOf(split[0]), split[1], paramMap)) {
                    throw new BizException("更新工单下发失败");
                }
            }
        }
        return true;
    }

    // 产品包回收
    @Override
    public boolean recycle(List<Long> idList) {
        //产品包 锁定/发布状态 检查
        RuleCondition.create()
                .and(LockStatusRule.init(BmsPackage.class).data(idList))
                .and(RecycleStatusRule.init(BmsPackage.class).policy(RecycleCheck.CAN_RECYCLE).data(idList))
                .execute().check();
        BmsPackageContent bmsPackageContent = bmsPackageContentService.getOne(
                Wrappers.lambdaQuery(BmsPackageContent.class)
                        .select(BmsPackageContent::getPackageName, BmsPackageContent::getContentName)
                        .in(BmsPackageContent::getPackageId, idList)
                        .notIn(BmsPackageContent::getPublishStatus, PublishStatusRule.UN_PUBLISHED_LIST)
                        .last("limit 1"));
        if (bmsPackageContent != null) {
            throw new BizException(bmsPackageContent.getPackageName() + " 与 " + bmsPackageContent.getContentName() + " 的关系未回收,此产品包无法回收");
        }
        List<BmsPackage> packages = this.list(Wrappers.lambdaQuery(BmsPackage.class)
                .notIn(BmsPackage::getPublishStatus, RecycleStatusRule.NOT_RECYCLE_LIST)
                .in(BmsPackage::getId, idList));
        if (CollectionUtils.isEmpty(packages)) {
            return false;
        }
        BmsPackage obj = packages.get(0);
        List<Long> recycleIds = packages.stream().map(BmsPackage::getId)
                .filter(Objects::nonNull).collect(Collectors.toList());
        // 回收工单
        Map<String, OutParamExpand> paramMap = new HashMap<>();
        paramMap.put(PublishParamTypeConstants.PRIORITY, new OutParamExpand().setPriority(PriorityEnums.GENERAL.getValue()));
        if (!this.sendPackageOrder(ActionEnums.DELETE, recycleIds, obj.getSpId(), obj.getSpName(), paramMap)) {
            throw new BizException("回收工单下发失败");
        }
        return true;
    }

    // 产品包重置发布状态
    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = Exception.class)
    public boolean resetPublishStatus(List<Long> idList) {
        //产品包 锁定/发布状态 检查
        RuleCondition.create()
                .and(LockStatusRule.init(BmsPackage.class).data(idList))
                .and(PublishStatusRule.init(BmsPackage.class).policy(MUST_ING).data(idList).type("存在非下发进行中的数据不可重置"))
                .execute().check();
        List<BmsPackage> bmsPackages = this.list(Wrappers.lambdaQuery(BmsPackage.class)
                .select(BmsPackage::getId, BmsPackage::getName, BmsPackage::getPublishStatus)
                .in(BmsPackage::getId, idList));
        bmsPackages.forEach(bmsPackage -> {
            // 确认即将要修改的发布状态
            Integer newStatus = RuleUtil.confirmModifyPublishIngStatus(bmsPackage.getPublishStatus());
            bmsPackage.setPublishStatus(newStatus);
        });
        return this.updateBatchById(bmsPackages);
    }

    // 产品包修改发布状态 
    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = Exception.class)
    public boolean updatePublishStatus(BmsPackageOperationReq operationReq) {
        Integer publishStatus = operationReq.getPublishStatus();
        // 取出所有id
        List<Long> idList = operationReq.getIdList();
        // 锁定检查 产品包发布状态检查 关系发布状态检查
        RuleResult execute = RuleCondition.create().and(LockStatusRule.init(BmsPackage.class).data(idList)).execute();
        if (!execute.isPass()) {
            throw new BizException(execute.getMsg());
        }
        return this.update(Wrappers.lambdaUpdate(BmsPackage.class)
                .set(BmsPackage::getPublishStatus, publishStatus)
                .set(BmsPackage::getPublishDescription, "")
                .in(BmsPackage::getId, idList));
    }

    // 产品包生效/失效 
    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = Exception.class)
    public boolean status(BmsPackageOperationReq operationReq) {
        List<Long> idList = operationReq.getIdList();
        //是否可修改 判断锁定 发布中判断
        RuleResult ruleResult = RuleUtil.canModify(BmsPackage.class, idList);
        if (!ruleResult.isPass()) {
            throw new BizException(ruleResult.getMsg());
        }
        //设置的 set status=x
        Integer status = operationReq.getStatus();
        //条件 where status =!x
        int st = status.equals(StatusEnum.COME.getCode()) ? StatusEnum.LOSE.getCode() : StatusEnum.COME.getCode();
        // 发布成功的修改为待更新
        this.update(Wrappers.lambdaUpdate(BmsPackage.class)
                .set(BmsPackage::getStatus, status)
                .set(BmsPackage::getPublishStatus, WAITUPDATE.getCode())
                .in(BmsPackage::getPublishStatus, PUBLISH.getCode(), FAILUPDATE.getCode(), FAILROLLBACK.getCode())
                .in(BmsPackage::getId, idList)
                .eq(BmsPackage::getStatus, st));
        // 其它只修改 生效/失效状态
        this.update(Wrappers.lambdaUpdate(BmsPackage.class)
                .set(BmsPackage::getStatus, status)
                .in(BmsPackage::getId, idList)
                .eq(BmsPackage::getStatus, st));
        this.update(Wrappers.lambdaUpdate(BmsPackage.class)
                .set(BmsPackage::getPublishStatus, WAITPUBLISH.getCode())
                .in(BmsPackage::getId, idList)
                .eq(BmsPackage::getPublishStatus, FAILPUBLISH.getCode()));
        return true;
    }

    /**
     * <AUTHOR>
     * @Date 2021-11-06 15:12:23
     * @Description 下发产品包
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean sendPackageOrder(ActionEnums actionEnums, List<Long> packages, Long spId, String spName, Map<String, OutParamExpand> paramMap) {
        if (CollectionUtils.isEmpty(packages)) return true;
        return workOrderOperation.send(actionEnums, ContentTypeEnum.PACKAGE, packages, null, spId, spName,
                (success, publishStatus, description) -> {
                    // 更新发布状态以及发布描述
                    this.update(Wrappers.lambdaUpdate(BmsPackage.class)
                            .set(BmsPackage::getPublishStatus, publishStatus)
                            .set(!success, BmsPackage::getPublishDescription, description)
                            .set(BmsPackage::getPublishTime, new Date())
                            .in(BmsPackage::getId, packages));
                }, paramMap);
    }

    @Override
    public boolean deleteByCodeAndSp(List<String> delPackageCodes, List<Long> spIdList, boolean isRollback) {
        log.info("自动发布 删除 package=====");
//        boolean allSpRollback = true;
//        Long extendSpContentCount = bmsPackageMapper.selectCount(Wrappers.lambdaQuery(BmsPackage.class)
//                .notIn(BmsPackage::getSpId, spIdList)
//                .in(BmsPackage::getCode, delPackageCodes)
//
//        );
//
//        if (extendSpContentCount != null && extendSpContentCount > 0) {
//            log.error("该Cp Package 已授权给其他非自动发布SP且已经发布到运营商");
//            allSpRollback = false;
////            throw new CommonResponseException(CommonResponseEnum.DELETE_ERROR);
//        }

        if (isRollback) {
            bmsPackageMapper.update(null, Wrappers.lambdaUpdate(BmsPackage.class)
                    .set(BmsPackage::getPublishStatus, ROLLBACK.getCode())
                    .in(BmsPackage::getCode, delPackageCodes)
                    .in(BmsPackage::getSpId, spIdList)
            );

            bmsPackageContentService.update(Wrappers.lambdaUpdate(BmsPackageContent.class)
                    .set(BmsPackageContent::getPublishStatus, ROLLBACK.getCode())
                    .in(BmsPackageContent::getPackageCode, delPackageCodes)
                    .in(BmsPackageContent::getSpId, spIdList)
            );
            return true;
        }
        bmsPackageMapper.delete(Wrappers.lambdaQuery(BmsPackage.class)
                .in(BmsPackage::getCode, delPackageCodes)
                .in(BmsPackage::getSpId, spIdList)
        );

        List<Long> contentIds = bmsPackageContentService.list(Wrappers.lambdaQuery(BmsPackageContent.class)
                        .in(BmsPackageContent::getPackageCode, delPackageCodes)
                        .in(BmsPackageContent::getSpId, spIdList))
                .stream().map(BmsPackageContent::getBmsContentId).collect(Collectors.toList());
        bmsPackageContentService.remove(Wrappers.lambdaQuery(BmsPackageContent.class)
                .in(BmsPackageContent::getPackageCode, delPackageCodes)
                .in(BmsPackageContent::getSpId, spIdList)
        );


        List<BmsPackageContent> packageContentList = bmsPackageContentService.list(Wrappers.lambdaQuery(BmsPackageContent.class)
                .in(BmsPackageContent::getBmsContentId, contentIds));

        syncMsgToBmsContent(contentIds, packageContentList);
        return true;
    }

    @Override
    public void copyPackageContent(List<BmsPackage> sourcePackage, List<BmsPackage> targetPackageList) {
        try {

            Set<Long> collect = targetPackageList.stream().map(BmsPackage::getSpId).collect(Collectors.toSet());
            Set<Long> sourceSpid = sourcePackage.stream().map(BmsPackage::getSpId).collect(Collectors.toSet());
            if (collect.size() != 1 || sourceSpid.size() != 1 || !sourcePackage.get(0).getSpId().equals(targetPackageList.get(0).getSpId())) {
                log.info("产品包spid不一致,无法复制");
                return;
            }
            List<Long> sourceCodes = sourcePackage.stream().map(BmsPackage::getId).collect(Collectors.toList());
            List<BmsPackageContent> sourcePackageContent = bmsPackageContentService.listByPackageIds(sourceCodes);

            Set<Long> bmsContentId = sourcePackageContent.stream().map(BmsPackageContent::getBmsContentId).collect(Collectors.toSet());
            List<BmsContent> bmsContentList = bmsContentService.listByIds(bmsContentId);
            // 取到目标 ids
            Set<String> targetPackageIds = targetPackageList.stream().map(o -> String.valueOf(o.getId())).collect(Collectors.toSet());
            // 取到目标 names
            Set<String> targetPackageNames = targetPackageList.stream().map(BmsPackage::getName).collect(Collectors.toSet());

            for (BmsContent bmsContent : bmsContentList) {
                bmsContent.setPackageIds(StrUtils.mergeSet(bmsContent.getPackageIds(), targetPackageIds));
                bmsContent.setPackageNames(StrUtils.mergeSet(bmsContent.getPackageNames(), targetPackageNames));
            }

            List<BmsPackageContent> target = new ArrayList<>();
            for (BmsPackage bmsPackage : targetPackageList) {
                for (BmsContent bmsContent : bmsContentList) {
                    BmsPackageContent bmsPackageContent = new BmsPackageContent();
                    target.add(bmsPackageContent);
                    bmsPackageContent.setBmsContentId(bmsContent.getId());
                    bmsPackageContent.setCmsContentCode(bmsContent.getCmsContentCode());
                    bmsPackageContent.setContentName(bmsContent.getName());
                    bmsPackageContent.setContentType(bmsContent.getContentType());
                    bmsPackageContent.setOutPassageIds(bmsContent.getOutPassageIds());
                    bmsPackageContent.setOutPassageNames(bmsContent.getOutPassageNames());
                    bmsPackageContent.setSpId(bmsContent.getSpId());
                    bmsPackageContent.setSpName(bmsContent.getSpName());
                    bmsPackageContent.setPackageCode(bmsPackage.getCode());
                    bmsPackageContent.setPackageId(bmsPackage.getId());
                    bmsPackageContent.setPackageName(bmsPackage.getName());
                    bmsPackageContent.setStatus(1);
                    bmsPackageContent.setPublishStatus(1);
                    bmsPackageContent.setSource(1);
                    bmsPackageContent.setPublishDescription("产品包内容复制数据");
                }
                log.info("packageId:{} packageName{}, 内容数量:{}", bmsPackage.getId(), bmsPackage.getName(), target.size());
            }
            // 写数据库
            bmsPackageContentService.saveBatch(target);
            bmsContentService.updateBatchById(bmsContentList);
        } catch (Exception e) {
            log.error("产品包复制错误..", e);
        }
    }

    private void syncMsgToBmsContent(List<Long> contentIds, List<BmsPackageContent> list) {
        List<BmsContent> bmsContents = bmsContentService.getBaseMapper().selectBatchIds(contentIds);
        List<BmsContent> updateList = new ArrayList<>(bmsContents.size());
        for (BmsPackageContent item : list) {
            Long bmsContentId = item.getBmsContentId();
            BmsContent tar = bmsContents.stream().filter(c -> c.getId().equals(bmsContentId)).findFirst().get();
            rmCaPackageFromBmsContent(tar, item.getPackageId(), item.getPackageName());
            //生成指定字段的po
            updateList.add(transPo(tar));
        }
        bmsContentService.updateBatchById(updateList);
    }

    private static BmsContent transPo(BmsContent tar) {
        if (tar == null) return null;
        BmsContent tmp = new BmsContent();
        tmp.setId(tar.getId());
        tmp.setPackageIds(tar.getPackageIds());
        tmp.setPackageNames(tar.getPackageNames());
        tmp.setUpdateTime(new Date());
        return tmp;
    }

    private static void rmCaPackageFromBmsContent(BmsContent c, Long pkId, String pkName) {
        if (c == null) return;
        String packageIds = c.getPackageIds();
        if (org.apache.commons.lang3.StringUtils.isNotEmpty(packageIds)) {
            //剔除指定的产品包id
            String ids = rmTarWithSplit(packageIds, ",", pkId.toString());
            c.setPackageIds(ids);
        }
        String packageNames = c.getPackageNames();
        if (org.apache.commons.lang3.StringUtils.isNotEmpty(packageNames)) {
            //剔除指定的产品包名称
            String names = rmTarWithSplit(packageNames, ",", pkName);
            c.setPackageNames(names);
        }
    }

    private List<BmsPackage> getBmsPackageList(List<Long> ids) {
        return this.list(Wrappers.lambdaQuery(BmsPackage.class)
                .select(BmsPackage::getId, BmsPackage::getSpId, BmsPackage::getSpName, BmsPackage::getPublishStatus)
                .in(BmsPackage::getId, ids));
    }

    @Override
    public boolean updatePublishStatus(List<SubOrderObjectsEntity> orderObjectsEntities, List<Long> spIdList) {
        int result = bmsPackageMapper.updatePublishStatus(orderObjectsEntities, spIdList);
        return result >= 1;
    }

    @Override
    public CommonResponse<Boolean> priortyPublish(List<Long> ids, Map<String, OutParamExpand> paramMap) {
        boolean result = this.publish(ids, paramMap);
        return result ? CommonResponse.success(result) : CommonResponse.commonfail("发布失败");
    }

    /**
     * 自动发布反馈-产品包删除操作
     * @param codeList
     * @param spIdList
     * @return
     */
    @Override
    public boolean autoFeedBackPackageDel(List<String> codeList, List<Long> spIdList){
    //参数校验
        if (CollectionUtils.isEmpty(codeList) || CollectionUtils.isEmpty(spIdList)) {
            log.warn("自动发布反馈-栏目删除操作,参数codes:{},sps:{} 不全", codeList, spIdList);
            return false;
        }
        //更新栏目状态
        bmsPackageMapper.update(null, Wrappers.lambdaUpdate(BmsPackage.class)
                .set(BmsPackage::getPublishDescription, SymbolConstant.SPACE)
                .set(BmsPackage::getPublishStatus, ROLLBACK.getCode())
                .in(BmsPackage::getCode, codeList)
                .in(BmsPackage::getSpId, spIdList)
        );
        return true;
    }
}

