package com.pukka.iptv.manage.service.cms.impl;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.pukka.iptv.common.base.constant.ObjectsTypeConstants;
import com.pukka.iptv.common.base.constant.RedisKeyConstants;
import com.pukka.iptv.common.base.enums.ContentTypeEnum;
import com.pukka.iptv.common.base.enums.PublishStatusEnum;
import com.pukka.iptv.common.base.exception.BizException;
import com.pukka.iptv.common.base.vo.CommonResponse;
import com.pukka.iptv.common.core.constant.SymbolConstant;
import com.pukka.iptv.common.data.model.bms.BmsPicture;
import com.pukka.iptv.common.data.model.cms.CmsPicture;
import com.pukka.iptv.common.data.model.order.SubOrderMappingsEntity;
import com.pukka.iptv.common.data.model.sys.SysStorage;
import com.pukka.iptv.common.data.vo.cms.CmsPictureVO;
import com.pukka.iptv.common.redis.service.RedisService;
import com.pukka.iptv.manage.mapper.bms.BmsPictureMapper;
import com.pukka.iptv.manage.mapper.cms.CmsPictureMapper;
import com.pukka.iptv.manage.service.bms.BmsPictureService;
import com.pukka.iptv.manage.service.cms.CmsPictureService;
import com.pukka.iptv.manage.service.common.FtpAnalysisUtil;
import com.pukka.iptv.manage.service.condition.RuleCondition;
import com.pukka.iptv.manage.service.condition.RuleResult;
import com.pukka.iptv.manage.service.condition.rule.PublishStatusRule;
import com.pukka.iptv.manage.service.condition.rule.enums.PublishCheck;
import io.jsonwebtoken.lang.Collections;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.util.*;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

import static com.pukka.iptv.common.base.enums.PublishStatusEnum.ROLLBACK;
import static java.util.regex.Pattern.compile;

/**
 * @author: zhoul
 * @date: 2021-8-30 10:58:58
 */

@Service
@Slf4j
public class CmsPictureServiceImpl extends ServiceImpl<CmsPictureMapper, CmsPicture> implements CmsPictureService {
    @Autowired
    private CmsPictureMapper cmsPictureMapper;

    @Autowired
    private BmsPictureMapper bmsPictureMapper;

    @Autowired
    private RedisService redisService;

    @Autowired
    private BmsPictureService bmsPictureService;

    /**
     * 自动发布反馈-图片删除操作
     *
     * @param codeList
     * @param spIdList
     * @return
     */
    @Override
    public boolean autoFeedBackPicDel(List<String> codeList, List<Long> spIdList) {
        //参数校验
        if (CollectionUtils.isEmpty(codeList) || CollectionUtils.isEmpty(spIdList)) {
            log.warn("自动发布反馈-图片删除操作,参数codes:{},sps:{} 不全", codeList, spIdList);
            return false;
        }
        //校验图片所属媒资类型
        List<BmsPicture> list = bmsPictureService.list(Wrappers.lambdaQuery(BmsPicture.class)
                .in(BmsPicture::getCmsPictureCode, codeList)
                .in(BmsPicture::getSpId, spIdList));
        list.forEach(bmsPicture -> {
            if (bmsPicture.getContentType().equals(ContentTypeEnum.CATEGORY.getValue())) {
                //栏目图片只修改图片发布状态
                bmsPictureService.update(Wrappers.lambdaUpdate(BmsPicture.class)
                        .set(BmsPicture::getPublishDescription, SymbolConstant.SPACE)
                        .set(BmsPicture::getPublishStatus, ROLLBACK.getCode())
                        .in(BmsPicture::getCmsPictureCode, codeList)
                        .in(BmsPicture::getSpId, spIdList));
            } else {
                //媒资图片删除操作
                bmsPictureService.remove(Wrappers.lambdaQuery(BmsPicture.class)
                        .in(BmsPicture::getCmsPictureCode, codeList)
                        .in(BmsPicture::getSpId, spIdList));
            }
        });
        return true;
    }

    /**
     * ToDo:20230214 弃用方法
     *
     * @param codeList
     * @param spIdList
     * @return
     */
    @Override
    public boolean delPicByCodeAndSp(List<String> codeList, List<Long> spIdList) {
        boolean allSpRollback = true;
        Long extendSpContentCount = bmsPictureMapper.selectCount(Wrappers.lambdaQuery(BmsPicture.class)
                .notIn(BmsPicture::getSpId, spIdList)
                .in(BmsPicture::getCode, codeList)
        );
        if (extendSpContentCount != null && extendSpContentCount > 0) {
            log.error("该Cp Picture 已授权给其他非自动发布SP且已经发布到运营商");
            allSpRollback = false;
//            throw new CommonResponseException(CommonResponseEnum.DELETE_ERROR);
        }

        if (allSpRollback) {
            log.info("图片全部sp回收成功，删除cp图片");
            List<CmsPicture> pictures = list(Wrappers.lambdaQuery(CmsPicture.class)
                    .in(CmsPicture::getCode, codeList));
            bmsPictureService.remove(Wrappers.lambdaQuery(BmsPicture.class)
                    .in(BmsPicture::getCode, codeList));
            remove(Wrappers.lambdaQuery(CmsPicture.class)
                    .in(CmsPicture::getCode, codeList));

            pictures.forEach(picture -> {
                log.info("删除Picture code={}， File = {}", picture.getCode(), picture.getFileUrl());
                FtpAnalysisUtil.DelFtp(picture.getFileUrl());
            });
        }
        return true;
    }

    /**
     * 自动发布反馈-图片关系删除操作
     *
     * @param deletePictureMappingEntities
     * @param spIdList
     * @return
     */
    @Override
    public boolean autoFeedBackPicMappingDel(List<SubOrderMappingsEntity> deletePictureMappingEntities, List<Long> spIdList) {
        //参数校验
        if (CollectionUtils.isEmpty(deletePictureMappingEntities) || CollectionUtils.isEmpty(spIdList)) {
            log.warn("自动发布反馈-图片关系删除操作,参数实体:{},sps:{} 不全", deletePictureMappingEntities, spIdList);
            return false;
        }
        //校验图片关联媒资类型
        deletePictureMappingEntities.forEach(orderMappingsEntity -> {
            switch (orderMappingsEntity.getElementType()) {
                case ObjectsTypeConstants.CATEGORY:
                case ObjectsTypeConstants.PROGRAM:
                case ObjectsTypeConstants.SERIES:
                    bmsPictureService.update(Wrappers.lambdaUpdate(BmsPicture.class)
                            .set(BmsPicture::getPublishDescription, SymbolConstant.SPACE)
                            .set(BmsPicture::getPublishStatus, ROLLBACK.getCode())
                            .eq(BmsPicture::getCmsPictureCode, orderMappingsEntity.getParentCode())
                            .eq(BmsPicture::getContentCode, orderMappingsEntity.getElementCode())
                            .in(BmsPicture::getSpId, spIdList));
                    break;
                default:
                    log.info("不支持的Mapping类型 ParentType = {}， ElementType = {} ", orderMappingsEntity.getParentType(), orderMappingsEntity.getElementType());
                    break;
            }

        });
        return true;
    }

    /**
     * ToDo:20230214 弃用方法
     *
     * @param deletePictureMappingEntities
     * @param spIdList
     * @return
     */
    @Override
    public boolean deletePictureMapping(List<SubOrderMappingsEntity> deletePictureMappingEntities, List<Long> spIdList) {
        // 查询mapping关系的其他非自动发布sp情况
        // sql:
        //      sp_id not in ? and publish_status not in ? and ((content_code=? and code=?) or (content_code=? and code=?))
        LambdaQueryWrapper<BmsPicture> wrapper = Wrappers.lambdaQuery(BmsPicture.class)
                .notIn(BmsPicture::getSpId, spIdList)
                .and(queryWrapper ->
                        deletePictureMappingEntities.forEach(orderMappingsEntity ->
                                queryWrapper.or(child -> child
                                        .eq(BmsPicture::getContentCode, orderMappingsEntity.getElementCode())
                                        .eq(BmsPicture::getCode, orderMappingsEntity.getParentCode())
                                )
                        )
                );
        Long extendSpContentCount = bmsPictureMapper.selectCount(wrapper);
        boolean allSpRollback = true;
        if (extendSpContentCount != null && extendSpContentCount > 0) {
            log.error("该Cp 图片mapping已授权给其他非自动发布SP且已经发布到运营商");
            allSpRollback = false;
//            throw new CommonResponseException(CommonResponseEnum.DELETE_ERROR);
        }
        boolean finalAllSpRollback = allSpRollback;


        deletePictureMappingEntities.forEach(orderMappingsEntity -> {
            // 只有全部sp的图片都回收了才去解绑cp侧图片内容关系
            if (finalAllSpRollback) {
                bmsPictureService.update(Wrappers.lambdaUpdate(BmsPicture.class)
                        .set(BmsPicture::getBmsContentId, null)
                        .set(BmsPicture::getContentCode, null)
                        .set(BmsPicture::getContentType, null)
                        .eq(BmsPicture::getCmsPictureCode, orderMappingsEntity.getParentCode())
                        .eq(BmsPicture::getContentCode, orderMappingsEntity.getElementCode())
                );

                update(Wrappers.lambdaUpdate(CmsPicture.class)
                        .set(CmsPicture::getContentId, null)
                        .set(CmsPicture::getContentCode, null)
                        .set(CmsPicture::getContentType, null)
                        .eq(CmsPicture::getCode, orderMappingsEntity.getParentCode())
                        .eq(CmsPicture::getContentCode, orderMappingsEntity.getElementCode())
                );
            }
        });
        return true;
    }

    /**
     * 根据id和type查询cms图片详情
     *
     * @param contentId
     * @param contentType
     * @return
     */
    @Override
    public List<CmsPicture> getByContentIdContentType(Long contentId, Integer contentType) {
        QueryWrapper<CmsPicture> wrapper = new QueryWrapper<>();
        wrapper.lambda().eq(CmsPicture::getContentId, contentId).eq(CmsPicture::getContentType, contentType);
        return this.list(wrapper);
    }

    @Override
    public CmsPicture getByCode(String code) {
        CmsPicture cmsPicture = cmsPictureMapper.selectOne(
                Wrappers.lambdaQuery(CmsPicture.class)
                        .eq(CmsPicture::getCode, code)
                        .last("limit 1")

        );
        return cmsPicture;
    }

    @Override
    public CommonResponse<String> up(CmsPicture cmsPicture) {
        List<BmsPicture> bmsPictureList = bmsPictureService.listByCmsPictureId(cmsPicture.getId());
        for (BmsPicture bmsPicture : bmsPictureList) {
            if (PublishStatusEnum.checkUpdateStatus(bmsPicture.getPublishStatus())) {
                return CommonResponse.fail("sp侧图片发布状态不不满足修改条件:bmsPictureId" + bmsPicture.getId());
            }
        }
        CmsPicture pictureModel = new CmsPicture();
        pictureModel.setSequence(cmsPicture.getSequence());
        pictureModel.setType(cmsPicture.getType());
        pictureModel.setId(cmsPicture.getId());
        updateById(pictureModel);

        for (BmsPicture bmsPicture : bmsPictureList) {
            bmsPicture.setSequence(cmsPicture.getSequence());
            bmsPicture.setType(cmsPicture.getType());
            bmsPicture.setPublishStatus(PublishStatusEnum.getUpdatePublishStatus(bmsPicture.getPublishStatus()));
        }
        bmsPictureService.updateBatchById(bmsPictureList);
        return CommonResponse.success(null);
    }

    @Override
    public List<CmsPicture> listBySeriesId(List<Long> seriesIdList) {
        if (CollectionUtil.isEmpty(seriesIdList)) {
            return new ArrayList<>();
        }
        LambdaQueryWrapper<CmsPicture> query = Wrappers.lambdaQuery();
        query.in(CmsPicture::getContentId, seriesIdList)
                .and(q -> q.eq(CmsPicture::getContentType, ContentTypeEnum.EPISODES.getValue()).or().eq(CmsPicture::getContentType, ContentTypeEnum.TELEPLAY.getValue()));
        return list(query);
    }

    @Override
    public List<CmsPicture> listByProgramId(List<Long> programIdList) {
        if (CollectionUtil.isEmpty(programIdList)) {
            return new ArrayList<>();
        }
        LambdaQueryWrapper<CmsPicture> query = Wrappers.lambdaQuery();
        query.in(CmsPicture::getContentId, programIdList)
                .and(q -> q.eq(CmsPicture::getContentType, ContentTypeEnum.SUBSET.getValue()));
        return list(query);
    }

    @Override
    public void deleteByIds(Set<Long> seriesPictureIds) {
        if (CollectionUtil.isEmpty(seriesPictureIds)) {
            return;
        }
        LambdaQueryWrapper<CmsPicture> query = Wrappers.lambdaQuery();
        query.in(CmsPicture::getId, seriesPictureIds);
        remove(query);
    }

    @Override
    public Set<String> countByCode(Set<String> pictureCodes) {
        if (CollectionUtil.isEmpty(pictureCodes)) {
            return new HashSet<>();
        }
        LambdaQueryWrapper<CmsPicture> query = Wrappers.lambdaQuery();
        query.select(CmsPicture::getCode);
        query.in(CmsPicture::getCode, pictureCodes).groupBy(CmsPicture::getCode);
        List<CmsPicture> list = list(query);
        return list.stream().map(CmsPicture::getCode).collect(Collectors.toSet());
    }

    @Override
    public Map<String, CmsPicture> listByContentCode(Collection<String> cmsContentCode) {
        LambdaQueryWrapper<CmsPicture> query = Wrappers.lambdaQuery();
        query.in(CmsPicture::getContentCode, cmsContentCode);
        List<CmsPicture> list = list(query);
        // 保留分辨率为长图的图片 序号最小的图片
        Map<String, CmsPicture> picMap = list.stream().filter(pic -> {
            //判断 分辨率不是竖图的不要
            try {
                String ratio = pic.getRatio();
                String[] split = ratio.split("[\\*\\|x]");
                if (Integer.parseInt(split[0]) > Integer.parseInt(split[1])) {
                    return false;
                }
            } catch (Exception e) {
                log.error("分辨率解析失败：" + pic.getRatio());
                return false;
            }
            // 图片类型
            Integer type = pic.getType();
            // 只要类型为海报图片
            return type != null && type.equals(1);
        }).collect(
                // 保留序号小的图片
                Collectors.toMap(CmsPicture::getContentCode, v -> v, (v1, v2) -> {
                    Integer sequence1 = v1.getSequence();
                    Integer sequence2 = v2.getSequence();
                    if (sequence1 != null && sequence2 != null) {
                        if (sequence1 > sequence2) {
                            return v2;
                        } else {
                            return v1;
                        }
                    } else {
                        return v1;
                    }
                })
        );
        return picMap;
    }

    /**
     * 图片列表查询getByContentIdContentType
     *
     * @param cmsPicture
     * @return
     */
    @Override
    public List<CmsPictureVO> listById(CmsPicture cmsPicture) {
        List<CmsPicture> cmsPictures = cmsPictureMapper.selectList(
                Wrappers.lambdaQuery(CmsPicture.class)
                        .eq(Objects.nonNull(cmsPicture.getContentId()), CmsPicture::getContentId, cmsPicture.getContentId())
                        .eq(Objects.nonNull(cmsPicture.getCpId()), CmsPicture::getCpId, cmsPicture.getCpId())
                        .eq(Objects.nonNull(cmsPicture.getContentType()), CmsPicture::getContentType, cmsPicture.getContentType())
                        .orderBy(true, true, CmsPicture::getSequence)
                        .orderBy(true, false, CmsPicture::getId));
        List<CmsPictureVO> cmsPictureVOs = new ArrayList<>();
        if (cmsPictures == null) {
            return cmsPictureVOs;
        }
        if (!Collections.isEmpty(cmsPictures)) {
            cmsPictures.stream().forEach(
                    picture -> {
                        String fileUrl = picture.getFileUrl();
                        CmsPictureVO cmsPictureVO = new CmsPictureVO();
                        BeanUtils.copyProperties(picture, cmsPictureVO);
                        if (ObjectUtil.isNotEmpty(picture.getStorageId())) {
                            SysStorage sysStorage = redisService.getCacheMapValue(RedisKeyConstants.SYS_STORAGE_KEY, String.valueOf(picture.getStorageId()));
                            if (sysStorage == null) {
                                throw new BizException("从缓存获取存储失败 ,sysStorageId " + picture.getStorageId());
                            }
                            if (ObjectUtil.isNotEmpty(sysStorage.getPictureHttpPrefix())) {
                                Pattern p = compile("\\d+\\.\\d+\\.\\d+\\.\\d+\\:\\d+");
                                Matcher matcher = p.matcher(fileUrl);
                                if (matcher.find()) {
                                    int length = fileUrl.length();
                                    cmsPictureVO.setHttpUrl(sysStorage.getPictureHttpPrefix().concat(fileUrl.substring(fileUrl.indexOf(matcher.group()) + matcher.group().length() + 1, length)));
                                }
                            }
                        }
                        cmsPictureVOs.add(cmsPictureVO);
                    }
            );

        }
        return cmsPictureVOs;
    }


    /**
     * 删除图片
     *
     * @param cmsPicture
     * @return
     */
    @Override
    public void delPicture(CmsPicture cmsPicture) {
        String fileUrl = cmsPicture.getFileUrl();

        cmsPictureMapper.delete(Wrappers.<CmsPicture>lambdaQuery().eq(CmsPicture::getId, cmsPicture.getId()));
        bmsPictureMapper.delete(Wrappers.<BmsPicture>lambdaQuery().eq(BmsPicture::getCmsPictureId, cmsPicture.getId()));

        List<CmsPicture> cmsPictures = cmsPictureMapper.selectList(Wrappers.<CmsPicture>lambdaQuery().eq(CmsPicture::getCode, cmsPicture.getCode()));
        if (ObjectUtil.isEmpty(cmsPictures) && StringUtils.isNotEmpty(fileUrl)) {
            //删除存储
            FtpAnalysisUtil.DelFtp(fileUrl);
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public CommonResponse delPictureCheckStatus(CmsPicture cmsPicture) {
        CmsPicture picture = cmsPictureMapper.selectById(cmsPicture.getId());
        if (picture == null) {
            //throw new CommonResponseException("图片不存在");
            //ToDo: 2023/8/16 图片不存在,默认删除成功
            return CommonResponse.success("删除成功");
        }
        Long id = picture.getId();

        //已发布到下游检查
        RuleResult rr = RuleCondition.create()
                .and(PublishStatusRule.init(BmsPicture.class).data(id).col(BmsPicture::getCmsPictureId).condCol(BmsPicture::getCmsPictureId).policy(PublishCheck.PU))
                .execute();
        if (!rr.isPass()) {
            throw new BizException("图片已发布到下游,无法删除");
        }
        cmsPictureMapper.delete(Wrappers.<CmsPicture>lambdaQuery().eq(CmsPicture::getId, id));
        bmsPictureMapper.delete(Wrappers.<BmsPicture>lambdaQuery().eq(BmsPicture::getCmsPictureId, id));

        // 1.执行完删除后若还存在code相同的数据,则说明该图片关联了其他媒资,此时不删除图片介质
        // 2.执行完删除后若不存在code相同的数据,若此时图片路径不为空则应该删除图片介质;
        List<CmsPicture> cmsPictures = cmsPictureMapper.selectList(Wrappers.<CmsPicture>lambdaQuery().eq(CmsPicture::getCode, picture.getCode()));
        if (ObjectUtil.isEmpty(cmsPictures) && StringUtils.isNotEmpty(picture.getFileUrl())) {
            //删除存储
            FtpAnalysisUtil.DelFtp(picture.getFileUrl());
        }
        return CommonResponse.success("删除成功");
    }
}




