package com.pukka.iptv.manage.virtual.util;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.ObjectUtils;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.pukka.iptv.common.core.constant.SymbolConstant;
import com.pukka.iptv.common.data.model.sys.SysSp;
import com.pukka.iptv.manage.config.VirtualChannelConfig;
import com.pukka.iptv.manage.mapper.sys.SysSpMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 获取指定sp集合信息
 */
@Slf4j
@Component
public class DataCache {
    @Autowired
    private SysSpMapper sysSpMapper;
    @Autowired
    private VirtualChannelConfig virtualChannelConfig;

    public List<String> sysSpArray = new ArrayList<>();

    public List<String> getSpIds() {
        if (ObjectUtils.isNotEmpty(sysSpArray) && sysSpArray.size() > 0) {
            return sysSpArray;
        }
        try {
            if (ObjectUtils.isNotEmpty(virtualChannelConfig.getIds())) {
                LambdaQueryWrapper<SysSp> sysSpLambdaQueryWrapper =
                        Wrappers.lambdaQuery(SysSp.class)
                                .in(SysSp::getBmsSpChannelId, virtualChannelConfig.getIds());
                List<SysSp> sysSpList = sysSpMapper.selectList(sysSpLambdaQueryWrapper);
                sysSpArray = sysSpList.stream().map(sp -> sp.getId().toString()).collect(Collectors.toList());
            }
        } catch (Exception exception) {
            log.error("获取指定渠道sp信息 -----> 失败,错误信息:{}", exception);
        }
        return sysSpArray;
    }

    /**
     * 是否存在
     *
     * @param spIds
     * @return
     */
    public boolean isExistSpChannel(String spIds) {
        if (ObjectUtils.isEmpty(spIds)) {
            log.warn("获取指定渠道sp信息 -----> 传入sp参数为空!");
            return false;
        }
        //spchannel的sp集合
        List<String> sysSpList = getSpIds();
        //分发工单的sp信息
        List<String> spPublishList = Arrays.asList(spIds.split(SymbolConstant.COMMA));
        List<String> existList = spPublishList.stream().filter(sp -> sysSpList.contains(sp)).collect(Collectors.toList());
        if (ObjectUtils.isNotEmpty(existList) && existList.size() > 0) {
            return true;
        }
        return false;
    }
}
