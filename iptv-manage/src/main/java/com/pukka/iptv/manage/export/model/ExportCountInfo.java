package com.pukka.iptv.manage.export.model;

import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

/**
 * @Author: wangbo
 * @Date: 2022/4/20 16:53
 */
@Getter
@Setter
@Accessors(chain = true)
public class ExportCountInfo {
    /**
     * 总条数
     */
    private Long count;
    /**
     * 最大或最小的id
     */
    private Long MostId;
    /**
     * true  正序
     * FALSE  倒叙
     */
    private Boolean sort;
    /**
     * 单次查询最大数量
     */
    private int batchSize;
}
