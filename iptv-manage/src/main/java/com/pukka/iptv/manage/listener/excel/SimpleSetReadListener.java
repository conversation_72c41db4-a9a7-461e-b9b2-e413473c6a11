package com.pukka.iptv.manage.listener.excel;

import cn.hutool.core.util.ObjectUtil;
import com.alibaba.excel.context.AnalysisContext;
import com.alibaba.excel.event.AnalysisEventListener;
import com.alibaba.fastjson.JSON;
import com.pukka.iptv.common.base.util.JwtTokenUtil;
import com.pukka.iptv.common.base.vo.SecurityUser;
import com.pukka.iptv.common.core.util.SpringUtils;
import com.pukka.iptv.common.core.util.StringUtils;
import com.pukka.iptv.common.data.dto.SimpleSetExcelDto;
import com.pukka.iptv.common.data.model.sys.SysCp;
import com.pukka.iptv.manage.config.SimpleSetMQConfig;
import com.pukka.iptv.manage.service.sys.SysCpService;
import com.pukka.iptv.manage.util.CalendarUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.springframework.amqp.rabbit.connection.CorrelationData;
import org.springframework.amqp.rabbit.core.RabbitTemplate;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Component;

import java.util.*;

/**
 * 单集模板导入
 */
@Slf4j
@Component
@Scope("prototype")
public class SimpleSetReadListener extends AnalysisEventListener<SimpleSetExcelDto> {

    private RabbitTemplate rabbitTemplate = SpringUtils.getBean("rabbitTemplate");

    private SysCpService sysCpService = SpringUtils.getBean(SysCpService.class);

    List<SimpleSetExcelDto> excel = new ArrayList<>();

    Map<String, Integer> movieNameMap = new HashMap<>();

    Map<String, Integer> previewNameMap = new HashMap<>();

    //这里设置多少条消息为一组推送到mq
    private static final int BATCH_COUNT = 1000;

    //每读一次会调用一次invoke
    @Override
    public void invoke(SimpleSetExcelDto cmsprogramExcel, AnalysisContext analysisContext) {
        //判断记录是否是正常数据
        if (StringUtils.isEmpty(cmsprogramExcel.getName())
                && StringUtils.isEmpty(cmsprogramExcel.getCpName())
                && StringUtils.isEmpty(cmsprogramExcel.getContentProvider())
                && StringUtils.isEmpty(cmsprogramExcel.getLicensingWindowStart())
                && StringUtils.isEmpty(cmsprogramExcel.getLicensingWindowEnd())
                && StringUtils.isEmpty(cmsprogramExcel.getDescription())
                && StringUtils.isEmpty(cmsprogramExcel.getDefinitionFlag())
                && StringUtils.isEmpty(cmsprogramExcel.getPgmSndClass())
                && StringUtils.isEmpty(cmsprogramExcel.getPgmCategory())) {
            return;
        }

        if (StringUtils.isEmpty(cmsprogramExcel.getName())
                || StringUtils.isEmpty(cmsprogramExcel.getCpName())
                || StringUtils.isEmpty(cmsprogramExcel.getContentProvider())
                || StringUtils.isEmpty(cmsprogramExcel.getLicensingWindowStart())
                || StringUtils.isEmpty(cmsprogramExcel.getLicensingWindowEnd())
                || StringUtils.isEmpty(cmsprogramExcel.getDescription())
                || StringUtils.isEmpty(cmsprogramExcel.getDefinitionFlag())
                || StringUtils.isEmpty(cmsprogramExcel.getPgmSndClass())
                || StringUtils.isEmpty(cmsprogramExcel.getPgmCategory())) {
            throw new RuntimeException("标红的关键字段不能为空！");
        }
        if(StringUtils.isNotEmpty(cmsprogramExcel.getApproval())){
            if(cmsprogramExcel.getApproval().length() >32){
                throw new RuntimeException("内容字号长度超过32字符！");
            }
        }
        SecurityUser securityUser = JwtTokenUtil.getSecurityUser();
        if (Objects.nonNull(securityUser)) {
            cmsprogramExcel.setCreatorName(securityUser.getName());
            cmsprogramExcel.setCreatorId(securityUser.getId());
        }
        // 若不为空则记录
        if (StringUtils.isNotEmpty(cmsprogramExcel.getPreviewName())) {
            if (previewNameMap.containsKey(cmsprogramExcel.getPreviewName())) {
                // 添加失败则说明有重复数据 报错
                String msg = "第" + analysisContext.readRowHolder().getRowIndex() + "行数据,预览片名称与第" + previewNameMap.get(cmsprogramExcel.getPreviewName()) + "行数据重复";
                clear();
                throw new RuntimeException(msg);
            }
            previewNameMap.put(cmsprogramExcel.getPreviewName(), analysisContext.readRowHolder().getRowIndex());
        }

        if (StringUtils.isNotEmpty(cmsprogramExcel.getMovName())) {
            if (movieNameMap.containsKey(cmsprogramExcel.getMovName())) {
                // 添加失败则说明有重复数据 报错
                String msg = "第" + analysisContext.readRowHolder().getRowIndex() + "行数据,正片名称与第" + movieNameMap.get(cmsprogramExcel.getMovName()) + "行数据重复";
                clear();
                throw new RuntimeException(msg);
            }
            movieNameMap.put(cmsprogramExcel.getMovName(), analysisContext.readRowHolder().getRowIndex());
        }

        //20220704 添加注入vspcode空值校验
        if (ObjectUtils.isNotEmpty(cmsprogramExcel.getCpName())) {
            SysCp cp = sysCpService.getByName(cmsprogramExcel.getCpName());
            if (ObjectUtils.isNotEmpty(cp)) {
                cmsprogramExcel.setVspCode(cp.getCode());
                cmsprogramExcel.setCpId(cp.getId());
            }
        }

        if (StringUtils.isEmpty(cmsprogramExcel.getLicensingWindowStart()) ||
                StringUtils.isEmpty(cmsprogramExcel.getLicensingWindowEnd())) {

        } else {
            if (!CalendarUtil.isRqSjFormat(cmsprogramExcel.getLicensingWindowStart())) {
                throw new RuntimeException("授权开始时间格式不正确");
            }
            if (!CalendarUtil.isRqSjFormat(cmsprogramExcel.getLicensingWindowEnd())) {
                throw new RuntimeException("授权结束时间格式不正确");
            }
            //每调用一次往集合里添加一个
            excel.add(cmsprogramExcel);
        }

    }

    @Override
    public void doAfterAllAnalysed(AnalysisContext analysisContext) {
        //发送
        if (ObjectUtil.isNotEmpty(excel)) {
            send();
        }
        clear();
    }

    /**
     * 向mq发送消息
     */
    private void send() {
        //全局唯一 不然ReturnCallback 无效
        CorrelationData correlationData = new CorrelationData(java.util.UUID.randomUUID().toString());
        String excel = JSON.toJSONString(this.excel);
        //发送消息
        this.rabbitTemplate.convertAndSend(SimpleSetMQConfig.EXCEL_IMPORT_EXCHANGE,
                SimpleSetMQConfig.EXCEL_IMPORT_ROUTING,
                excel, correlationData
        );
    }

    void clear() {
        previewNameMap.clear();
        movieNameMap.clear();
        excel.clear();
    }
}
