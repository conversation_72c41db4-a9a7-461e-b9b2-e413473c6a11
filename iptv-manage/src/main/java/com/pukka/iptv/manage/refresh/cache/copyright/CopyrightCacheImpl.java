package com.pukka.iptv.manage.refresh.cache.copyright;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.ObjectUtils;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.pukka.iptv.common.base.constant.RedisKeyConstants;
import com.pukka.iptv.common.base.enums.CopyrightTypeEnum;
import com.pukka.iptv.common.base.enums.DictionaryBaseEnums;
import com.pukka.iptv.common.core.util.SafeUtil;
import com.pukka.iptv.common.data.model.copyright.CopyrightInfo;
import com.pukka.iptv.common.data.model.sys.SysCp;
import com.pukka.iptv.common.data.model.sys.SysDictionaryBase;
import com.pukka.iptv.common.data.model.sys.SysDictionaryItem;
import com.pukka.iptv.common.data.vo.req.CopyrightInfoReq;
import com.pukka.iptv.common.data.vo.req.CopyrightInfoSizeReq;
import com.pukka.iptv.common.redis.service.RedisService;
import com.pukka.iptv.downloader.util.SpringUtils;
import com.pukka.iptv.manage.service.copyright.copyright.CopyrightInfoService;
import com.pukka.iptv.manage.service.sys.SysDictionaryBaseService;
import com.pukka.iptv.manage.service.sys.SysDictionaryItemService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.CompletableFuture;

/**
 * @Author: chiron
 * @Date: 2022/08/30/18:10
 * @Description: 版权信息缓存
 */
@Slf4j
@Component
public class CopyrightCacheImpl implements CopyrightCache {
    //到期时间
    private static Integer dateline = 10;
    //分页条数
    private final static Integer PAGE_SIZE = 9;
    //分页页码
    private final static Integer PAGE_CURRENT = 1;

    private RedisService redisService = SpringUtils.getBean(RedisService.class);
    private CopyrightInfoService copyrightInfoService = SpringUtils.getBean(CopyrightInfoService.class);
    private SysDictionaryBaseService sysDictionaryBaseService = SpringUtils.getBean(SysDictionaryBaseService.class);
    private SysDictionaryItemService sysDictionaryItemService = SpringUtils.getBean(SysDictionaryItemService.class);

    public CopyrightCacheImpl() {
        getDateline();
        setCopyrightInfoCache();
        setCopyrightSizeCache();
    }

    /**
     * 获取版权到期时间
     *
     * @return
     */
    private Integer getDateline() {
        try {
            SysDictionaryBase sysDictionaryBase = sysDictionaryBaseService.getOne(Wrappers.<SysDictionaryBase>lambdaQuery().select(SysDictionaryBase::getId).eq(SysDictionaryBase::getCode, DictionaryBaseEnums.AUTHORIZATION_EXPIRATION_TIME.getValue()));
            if (ObjectUtils.isNotEmpty(sysDictionaryBase)) {
                SysDictionaryItem sysDictionaryItem = sysDictionaryItemService.getOne(Wrappers.<SysDictionaryItem>lambdaQuery()
                        .select(SysDictionaryItem::getId, SysDictionaryItem::getName)
                        .eq(SysDictionaryItem::getDictionaryBaseId, sysDictionaryBase.getId())
                        .last("limit 1")
                );
                if (ObjectUtils.isNotEmpty(sysDictionaryItem)) {
                    return dateline = Integer.valueOf(sysDictionaryItem.getName());
                }
            }
        } catch (Exception exception) {
            log.error("获取版权到期时间失败,错误信息:{}", exception);
        }
        return dateline;
    }

    @Scheduled(initialDelay = 1000, fixedRateString = "600000")
    private void setCopyrightInfoCache() {
        log.info("开始刷新版权信息到期过期数据缓存");
        CompletableFuture.runAsync(() -> {
            try {
                Page<CopyrightInfo> pageParam = getPageParam();
                List<CopyrightInfoReq> copyrightInfoReqList = new ArrayList<CopyrightInfoReq>() {{
                    add(new CopyrightInfoReq().setContentType(CopyrightTypeEnum.ALL.getValue()).setCpId(0L).setLastCopyrightDay(dateline));
                }};

                Map<String, SysCp> sysCpMap = redisService.getCacheMap(RedisKeyConstants.SYS_CP);
                List<String> keys = new ArrayList<String>(sysCpMap.keySet());
                keys.forEach(key -> {
                    CopyrightInfoReq copyrightInfoReq = new CopyrightInfoReq();
                    copyrightInfoReq.setCpId(Long.valueOf(key));
                    copyrightInfoReq.setContentType(CopyrightTypeEnum.ALL.getValue());
                    copyrightInfoReq.setLastCopyrightDay(dateline);
                    copyrightInfoReqList.add(copyrightInfoReq);
                });
                Map<String, List<CopyrightInfo>> collect = new HashMap<>();
                copyrightInfoReqList.forEach(copyrightInfoReq -> {
                    IPage<CopyrightInfo> copyrightInfoList = copyrightInfoService.getCopyrightInfoList(pageParam, copyrightInfoReq,false);
                    collect.put(SafeUtil.getString(copyrightInfoReq.getCpId()),
                            org.apache.commons.lang3.ObjectUtils.isNotEmpty(copyrightInfoList.getRecords()) ? copyrightInfoList.getRecords() : new ArrayList<>());

                });
                redisService.setCacheMap(RedisKeyConstants.AUTHORIZATION_EXPIRATION_TIME, collect);
                log.info("刷新版权信息到期过期数据缓存,成功");
            } catch (Exception exception) {
                log.error("刷新版权信息到期过期数据缓存,失败.error:{}", exception);
            }
        });
    }

    @Scheduled(initialDelay = 1000, fixedRateString = "600000")
    private void setCopyrightSizeCache() {
        log.info("开始刷新版权信息到期过期数量缓存");
        CompletableFuture.runAsync(() -> {
            try {
                Map<String, SysCp> sysCpMap = redisService.getCacheMap(RedisKeyConstants.SYS_CP);
                List<String> keys = new ArrayList<String>(sysCpMap.keySet());
                keys.add("0");
                Map<String, CopyrightInfoSizeReq> map = new HashMap<>();
                keys.forEach(key -> {
                    CopyrightInfoSizeReq copyrightInfoSize = copyrightInfoService.getCopyrightInfoSize(dateline, Integer.parseInt(key));
                    map.put(key, copyrightInfoSize);
                });
                redisService.setCacheMap(RedisKeyConstants.AUTHORIZATION_EXPIRATION_SIZE, map);
                log.info("刷新版权信息到期过期数量缓存,成功");
            } catch (Exception exception) {
                log.error("刷新版权信息到期过期数量缓存,失败.error:{}", exception);
            }
        });
    }

    @Override
    public List<CopyrightInfo> getCopyrightInfoCache(long cpId) {
        Map<String, List<CopyrightInfo>> cacheMap = redisService.getCacheMap(RedisKeyConstants.AUTHORIZATION_EXPIRATION_TIME);
        List<CopyrightInfo> copyrightInfoList = cacheMap.get(SafeUtil.getString(cpId));
        return copyrightInfoList;
    }

    @Override
    public CopyrightInfoSizeReq getCopyrightInfoSize(long cpId) {
        Map<String, CopyrightInfoSizeReq> cacheMap = redisService.getCacheMap(RedisKeyConstants.AUTHORIZATION_EXPIRATION_SIZE);
        CopyrightInfoSizeReq copyrightInfoSizeReq = cacheMap.get(SafeUtil.getString(cpId));
        return copyrightInfoSizeReq;
    }

    /**
     * 获取分页实体
     *
     * @return
     */
    private Page<CopyrightInfo> getPageParam() {
        Page<CopyrightInfo> page = new Page<>();
        page.setSize(PAGE_SIZE);
        page.setCurrent(PAGE_CURRENT);
        return page;
    }
}
