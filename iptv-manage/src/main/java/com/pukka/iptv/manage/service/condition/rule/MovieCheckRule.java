package com.pukka.iptv.manage.service.condition.rule;


import cn.hutool.core.collection.CollectionUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.pukka.iptv.common.base.enums.ContentTypeEnum;
import com.pukka.iptv.common.base.enums.MovieStatusEnum;
import com.pukka.iptv.common.core.util.SpringUtils;
import com.pukka.iptv.common.data.model.bms.BmsContent;
import com.pukka.iptv.common.data.model.bms.BmsProgram;
import com.pukka.iptv.manage.mapper.bms.BmsContentMapper;
import com.pukka.iptv.manage.mapper.bms.BmsProgramMapper;
import com.pukka.iptv.manage.service.condition.AbstractRule;
import com.pukka.iptv.manage.service.condition.BaseRule;
import com.pukka.iptv.manage.service.condition.RuleResult;
import com.pukka.iptv.manage.service.condition.rule.enums.MovieCheck;
import lombok.Setter;

import java.util.Arrays;
import java.util.Collection;

import static com.pukka.iptv.manage.service.condition.RuleResult.fail;


/**
 * @author: wz
 * @date: 2021/9/7 18:57
 * @description: 视频是否关联检查
 */
@Setter
public class MovieCheckRule<T> extends AbstractRule<T, MovieCheck> {

    protected MovieCheckRule(Class<T> clazz) {
        super(clazz);
    }

    public static <T> MovieCheckRule<T> init(Class<T> clazz) {
        return new MovieCheckRule<>(clazz);
    }


    //查找是否有没有关联视频的记录
    @Override
    public RuleResult execute() {
        //获取要查询的数据
        Collection<Long> data = getData();
        Class<T> clazz = getTableClass();
        //为空不检查
        if (CollectionUtil.isEmpty(data)) return RuleResult.ok();
        //子集
        if (BmsProgram.class.equals(clazz)) {
            BmsProgramMapper programMapper = SpringUtils.getBean(BmsProgramMapper.class);
            BmsProgram bmsProgram = programMapper.selectOne(Wrappers.lambdaQuery(BmsProgram.class)
                    .select(BmsProgram::getName)
                    .eq(BmsProgram::getReleaseStatus, MovieStatusEnum.NotRelevancy.getValue())
                    .in(BmsProgram::getId, data)
                    .last(" limit 1 "));
            if (bmsProgram != null) {
                return fail(bmsProgram.getName() + "没有关联正片不能进行发布", "");
            }
            //剧集和单集
        } else if (BmsContent.class.equals(clazz)) {
            BmsContentMapper mapper = SpringUtils.getBean(BmsContentMapper.class);
            BmsContent bmsContent = mapper.selectOne(Wrappers.lambdaQuery(BmsContent.class)
                    .select(BmsContent::getName)
                    .eq(BmsContent::getReleaseStatus, MovieStatusEnum.NotRelevancy.getValue())
                    .eq(BmsContent::getPreviewStatus, MovieStatusEnum.NotRelevancy.getValue())
                    //排除剧头
                    .notIn(BmsContent::getContentType, Arrays.asList(ContentTypeEnum.TELEPLAY.getValue(), ContentTypeEnum.TELEPLAY.getValue()))
                    .in(BmsContent::getId, data)
                    .last(" limit 1 "));
            if (bmsContent != null) {
                return fail(bmsContent.getName() + "没有关联视频不能进行发布", "");
            }
        }
        return RuleResult.ok();
    }

    @Override
    public BaseRule wrapper(QueryWrapper wrapper) {
        return this;
    }


}
