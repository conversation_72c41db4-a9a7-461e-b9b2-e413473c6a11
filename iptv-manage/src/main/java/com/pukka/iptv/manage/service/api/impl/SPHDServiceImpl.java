package com.pukka.iptv.manage.service.api.impl;


import cn.hutool.core.collection.CollectionUtil;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.pukka.iptv.common.base.constant.RedisKeyConstants;
import com.pukka.iptv.common.core.util.DateUtils;
import com.pukka.iptv.common.core.util.SafeUtil;
import com.pukka.iptv.common.data.model.bms.BmsCategoryContent;
import com.pukka.iptv.common.data.model.bms.BmsPackageContent;
import com.pukka.iptv.common.data.model.bms.BmsProgram;
import com.pukka.iptv.common.data.model.cms.CmsPicture;
import com.pukka.iptv.common.data.model.cms.CmsProgram;
import com.pukka.iptv.common.data.model.cms.CmsSeries;
import com.pukka.iptv.common.data.model.cms.CmsSeriesDTO;
import com.pukka.iptv.common.data.model.sys.SysSp;
import com.pukka.iptv.common.data.model.sys.SysStorage;
import com.pukka.iptv.common.redis.service.RedisService;
import com.pukka.iptv.manage.mapper.bms.BmsCategoryContentMapper;
import com.pukka.iptv.manage.mapper.bms.BmsPackageContentMapper;
import com.pukka.iptv.manage.mapper.bms.BmsProgramMapper;
import com.pukka.iptv.manage.mapper.cms.CmsProgramMapper;
import com.pukka.iptv.manage.mapper.cms.CmsSeriesMapper;
import com.pukka.iptv.manage.service.api.SPHDService;
import com.pukka.iptv.manage.service.cms.CmsPictureService;
import com.pukka.iptv.manage.service.cms.CmsProgramService;
import com.pukka.iptv.manage.service.cms.CmsSeriesService;
import com.pukka.iptv.manage.util.OutOperateUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

@Service
@Slf4j
public class SPHDServiceImpl implements SPHDService {
    @Autowired
    private StringRedisTemplate redisTemplate;

    @Autowired
    private RedisService redisService;

    @Resource
    private BmsPackageContentMapper bmsPackageContentMapper;

    @Resource
    private BmsCategoryContentMapper bmsCategoryContentMapper;

    @Resource
    private BmsProgramMapper bmsProgramMapper;
    @Autowired
    private CmsSeriesMapper cmsSeriesMapper;
    @Autowired
    private CmsProgramService cmsProgramService;

    @Autowired
    private CmsSeriesService cmsSeriesService;

    @Autowired
    private CmsPictureService cmsPictureService;

    @Autowired
    OutOperateUtil outOperateUtil;

    final static String SPHD_PATH = "SPHD-Media/";

    @Override
    public String getValidMediaUpFtp(String operator) {
        String ftpUrl = "";
        HashMap<String, List<?>> result = new HashMap<>();
        String spIds = "ctcc".equals(operator) ? "69,67" : "57";
        List<String> spList = Arrays.asList(spIds.split(","));
        try {
            long start = System.currentTimeMillis();
            // 查询在sp列表都存在栏目关系生效的媒资列表
            List<BmsCategoryContent> bmsCategoryContentList = bmsCategoryContentMapper.listValidMediaCodeByDate(spList, spList.size());
            log.info("categoryMediaCodeListSize：{} 耗时:{}", bmsCategoryContentList.size(), System.currentTimeMillis() - start);
            // 根据code的类型分组 3、4是剧集  1是单集
            Map<Integer, List<String>> categoryMediaCodeGroup = bmsCategoryContentList.stream().
                    collect(Collectors.groupingBy(BmsCategoryContent::getContentType, Collectors.mapping(BmsCategoryContent::getCmsContentCode, Collectors.toList())));

            // 查询在sp列表都存在包关系生效的媒资列表
            long start2 = System.currentTimeMillis();
            List<BmsPackageContent> bmsPackageContentList = bmsPackageContentMapper.listValidMediaCodeByDate(spList, spList.size());
            log.info("packageMediaCodeListSize：{} 耗时:{}", bmsPackageContentList.size(), System.currentTimeMillis() - start2);
            // 根据code的类型分组 3、4是剧集  1是单集
            Map<Integer, List<String>> packageMediaCodeGroup = bmsPackageContentList.stream().
                    collect(Collectors.groupingBy(BmsPackageContent::getContentType, Collectors.mapping(BmsPackageContent::getCmsContentCode, Collectors.toList())));

            Collection<String> series3Codes = CollectionUtil.intersection(categoryMediaCodeGroup.get(3), packageMediaCodeGroup.get(3));
            Collection<String> series4Codes = CollectionUtil.intersection(categoryMediaCodeGroup.get(4), packageMediaCodeGroup.get(4));
            Collection<String> seriesCodes = CollectionUtil.union(series3Codes, series4Codes);
            Collection<String> programCodes = CollectionUtil.intersection(categoryMediaCodeGroup.get(1), packageMediaCodeGroup.get(1));
            Collection<String> contentCodes = CollectionUtil.union(seriesCodes, programCodes);

            // 根据spId和剧集code统计子集集数
            Map<String, Map<String, Long>> updateNumMap =  bmsProgramMapper.countUpdateNumBySpId(seriesCodes, spList.get(0));

            // 查媒资
            long start3 = System.currentTimeMillis();
            List<CmsProgram> programList = cmsProgramService.listByCode(programCodes);
            log.info("单集数量:{}, 耗时:{}", programList.size(), System.currentTimeMillis() - start3);

            long start4 = System.currentTimeMillis();
            List<CmsSeries> seriesList = cmsSeriesService.listByCode(seriesCodes);
            // 修改剧集的更新集数
            seriesList.forEach(series -> {
                long updateNum = updateNumMap.get(series.getCode()) == null ? 0 : updateNumMap.get(series.getCode()).get("num");
                series.setVolumnUpdate(updateNum);
            });
            log.info("剧集数量:{}, 耗时:{}", seriesList.size(), System.currentTimeMillis() - start4);

            // 查图片
            long start5 = System.currentTimeMillis();
            Map<String , CmsPicture> pictureMap = cmsPictureService.listByContentCode(contentCodes);
            List<CmsPicture> pictureList = new ArrayList<>(pictureMap.values());
            log.info("图片数量:{}  耗时:{}", pictureMap.size(), System.currentTimeMillis() - start5);

            pictureList.forEach(o -> {
                o.setFileUrl(outOperateUtil.getMediaPrefix(o.getStorageId() == null ? "" : o.getStorageId().toString(), o.getFileUrl(), OutOperateUtil.OUT_PICTURE_FTP_PREFIX));
            });

            // 过滤掉无海报媒资
            programList = programList.stream().filter(program -> {
                if (pictureMap.get(program.getCode()) == null) {
                    return false;
                } else {
                    return true;
                }
            }).collect(Collectors.toList());
            seriesList = seriesList.stream().filter(series -> {
                if (pictureMap.get(series.getCode()) == null) {
                    return false;
                } else {
                    return true;
                }
            }).collect(Collectors.toList());

            // 组装数据包
            result.put("programList", programList);
            result.put("seriesList", seriesList);
            result.put("pictureList", pictureList);
            log.info("耗时:{}", System.currentTimeMillis() - start);
        } catch (Exception e) {
            log.error("getValidMedia error", e);
        }
        // 将结果写入ftp
        if (!result.isEmpty()) {
            // 通过sp获取存储id
            SysSp sp = redisService.getCacheMapValue(RedisKeyConstants.SYS_SP, spList.get(0));
            // 通过存储id获取存储对象
            SysStorage storage = redisService.getCacheMapValue(RedisKeyConstants.SYS_STORAGE_KEY,
                    String.valueOf(sp.getStorageId()));
            // 生成上传fpt前缀
            String uploadFtp = outOperateUtil.getUploadFtp(storage);
            // 生成文件名
            String fileName = DateUtils.dateTimeNow(DateUtils.YYYYMMDDHHMMSSS);
            // 上传文件
            ftpUrl = SafeUtil.upByFtp(JSONObject.toJSONString(result),
                    SPHD_PATH + operator + "/" + fileName + ".json", uploadFtp);
            if (StringUtils.isNotEmpty(ftpUrl)) {
                log.info("双屏媒资统计完成 ftp:{}", ftpUrl);
                // 若上传成功 删除目录下除当前文件外的其他文件
                SafeUtil.delFtpByPathExcludeCurrent(uploadFtp, SPHD_PATH + operator, fileName + ".json");
            }
        }
        return ftpUrl;
    }

    void task(){
        getValidMediaUpFtp("cucc");
        getValidMediaUpFtp("ctcc");
    }

    @Override
    public String getMediaFtp(String operator) {
        String result;
        String spIds = "ctcc".equals(operator) ? "69,67" : "57";
        List<String> spList = Arrays.asList(spIds.split(","));
        // 通过sp获取存储id
        SysSp sp = redisService.getCacheMapValue(RedisKeyConstants.SYS_SP, spList.get(0));
        SysStorage storage = redisService.getCacheMapValue(RedisKeyConstants.SYS_STORAGE_KEY,
                String.valueOf(sp.getStorageId()));
        // 生成上传fpt前缀
        String uploadFtp = outOperateUtil.getUploadFtp(storage);
        // 获取目标路径下最新的一个文件路径
        String sphdMadiaFile = SafeUtil.getNewFile(uploadFtp, SPHD_PATH + operator);
        if (StringUtils.isEmpty(sphdMadiaFile)) {
            result = "";
        } else {
            // 替换前置机地址
            result = outOperateUtil.getMediaPrefix(String.valueOf(storage.getId()), sphdMadiaFile, OutOperateUtil.OUT_PICTURE_FTP_PREFIX);
        }
        return result;
    }

    @Override
    public Map<String, List<?>> validSubscriptionMedia(List<String> spIds, List<String> codes) {
        if(codes == null){
            codes= new ArrayList<>();
        }
        HashMap<String, List<?>> result = new HashMap<>();
        List<CmsSeries> resultSeriesList = new ArrayList<>();
        List<CmsSeriesDTO> seriesList = new ArrayList<>();
        List<CmsPicture> pictureList = new ArrayList<>();

        try {
            if (redisTemplate.hasKey(RedisKeyConstants.VALID_SUBSCRIPTION_MEDIA_LOCK)) {
                return null;
            }
            redisTemplate.opsForValue().set(RedisKeyConstants.VALID_SUBSCRIPTION_MEDIA_LOCK, "1", 600, TimeUnit.SECONDS);
            long start = System.currentTimeMillis();
            //先查剧头
            //查询对应二级标签的剧集剧头
            long start1 = System.currentTimeMillis();
            List<CmsSeriesDTO>  dBSeriesList = cmsSeriesMapper.selectByBmsVolumnUpdate();
            log.info("剧集数量:{}, 耗时:{}", dBSeriesList.size(), System.currentTimeMillis() - start1);
            //转换为map 如果出现key冲突, 取对象CmsProgramVolumnUpdate最小的那个
            Map<String, CmsSeriesDTO> dBSeriesMap = dBSeriesList.stream().collect(Collectors.toMap(
                    CmsSeriesDTO::getCode,
                    o -> o,
                    (existing, replacement) -> existing.getCmsProgramVolumnUpdate() < replacement.getCmsProgramVolumnUpdate() ? existing : replacement
            ));
            Set<String> seriesCodeSet = dBSeriesMap.keySet();

            //取出需要的参数
            for (String code : seriesCodeSet) {
                CmsSeriesDTO cmsSeriesDTO = dBSeriesMap.get(code);
                if (cmsSeriesDTO.getCmsProgramVolumnUpdate()<cmsSeriesDTO.getVolumnCount() || codes.contains(code)){
                    seriesList.add(cmsSeriesDTO);
                }
            }
            //包含集数的剧集数据
            Map<String, CmsSeriesDTO> seriesMap = seriesList.stream().collect(Collectors.toMap(CmsSeriesDTO::getCode, o -> o, (k1, k2) -> k2));
            Set<String> filterCode = seriesMap.keySet();
            log.info("循环处理完耗时:{}",  System.currentTimeMillis() - start1);

            //发生修改时执行
            if (!codes.isEmpty()){
                //剔除为待发布的 媒资状态 栏目以及包关系
                long start2 = System.currentTimeMillis();
                List<BmsCategoryContent> removeSubscriptionMediaCodeBySp = bmsCategoryContentMapper.filtrationListSubscriptionMediaCodeBySp(spIds,Arrays.asList("5"),filterCode);
                Map<String, BmsCategoryContent> removeSeries = removeSubscriptionMediaCodeBySp.stream().collect(Collectors.toMap(BmsCategoryContent::getCmsContentCode, o -> o, (k1, k2) -> k2));
                Set<String> removeCodes = removeSeries.keySet();
                filterCode.removeAll(removeCodes);
                log.info("剔除为待发布的栏目以及包关系效验耗时:{}",  System.currentTimeMillis() - start2);
            }

            //查询包与栏目的绑定是否有效
            long start3 = System.currentTimeMillis();
            List<BmsCategoryContent> subscriptionMediaCodeBySp = bmsCategoryContentMapper.listSubscriptionMediaCodeBySp(spIds,Arrays.asList("3","10"),filterCode);
            result.put("subscriptionMediaCodeBySp", subscriptionMediaCodeBySp);
            Map<String, BmsCategoryContent> spSeries = subscriptionMediaCodeBySp.stream().collect(Collectors.toMap(BmsCategoryContent::getCmsContentCode, o -> o, (k1, k2) -> k2));
            log.info("栏目以及包关系效验耗时:{}",  System.currentTimeMillis() - start3);
            Set<String> seriesCodes = spSeries.keySet();

            //结果组装
            if (seriesCodes.size()>0){
                //查询cmsSeries 编目信息
                LambdaQueryWrapper<CmsSeries> cmsSeriesLambdaQueryWrapper = Wrappers.lambdaQuery();
                cmsSeriesLambdaQueryWrapper.in( CmsSeries::getCode, seriesCodes);
                List<CmsSeries> cmsSeries = cmsSeriesMapper.selectList(cmsSeriesLambdaQueryWrapper);
                Map<String, CmsSeries> cmsSeriesMap = cmsSeries.stream().collect(Collectors.toMap(CmsSeries::getCode, o -> o, (k1, k2) -> k2));
                for (String key : seriesCodes) {
                    CmsSeriesDTO cmsSeriesDTO = seriesMap.get(key);
                    CmsSeries cmsSeriesResult = cmsSeriesMap.get(key);
                    cmsSeriesResult.setVolumnUpdate(cmsSeriesDTO.getCmsProgramVolumnUpdate());
                    resultSeriesList.add(cmsSeriesResult);
                }
                // 查图片  剧集
                long start4 = System.currentTimeMillis();
                List<String> seriesCodesSubscription = resultSeriesList.stream().map(CmsSeries::getCode).collect(Collectors.toList());
                pictureList.addAll(cmsPictureService.listByContentCode(seriesCodesSubscription).values());
                log.info("图片数量:{}  耗时:{}", pictureList.size(), System.currentTimeMillis() - start4);
                //取照片序号最小的那条数据
                Map<String, CmsPicture> uniquePictures = pictureList.stream()
                        .collect(Collectors.toMap(
                                CmsPicture::getContentCode,
                                picture -> picture,
                                (existing, replacement) -> existing.getSequence() < replacement.getSequence() ? existing : replacement
                        ));

                pictureList = new ArrayList<>(uniquePictures.values());

                pictureList.forEach(o -> {
                    o.setFileUrl(outOperateUtil.getMediaPrefix(o.getStorageId() == null ? "" : o.getStorageId().toString(), o.getFileUrl(), OutOperateUtil.OUT_PICTURE_FTP_PREFIX));
                });
            }
            log.info("耗时:{}", System.currentTimeMillis() - start);

        } catch (Exception e) {
            log.error("getValidMedia error", e);
        } finally {
            redisTemplate.delete(RedisKeyConstants.VALID_SUBSCRIPTION_MEDIA_LOCK);
        }
        // 子集列表 seriesList 剧头列表 pictureList 图片列表
        result.put("seriesList", resultSeriesList);
        result.put("pictureList", pictureList);
        return result;
    }
}
