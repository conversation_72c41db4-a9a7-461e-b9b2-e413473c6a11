package com.pukka.iptv.manage.service.cms;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.pukka.iptv.common.data.dto.CmsProgramDO;
import com.pukka.iptv.common.data.dto.CmsProgramEndDto;
import com.pukka.iptv.common.data.model.cms.CmsProgram;
import com.pukka.iptv.common.data.vo.cms.CmsProgramSimplesetDetailVO;
import com.pukka.iptv.common.data.vo.cms.CmsProgramVO;


public interface CmsProgramCheckService extends IService<CmsProgram> {
    /**
     * 单集自审列表
     * @param page
     * @param cmsProgram
     * @return
     */
    Page<CmsProgram> selfList(Page page, CmsProgramVO cmsProgram, String startTime, String endTime);

    IPage<CmsProgram> selfList(Page page, CmsProgramDO cmsProgramDO, String startTime, String endTime, Boolean flag);
    /**
     * 子集自审列表
     * @param page
     * @param cmsProgram
     * @return
     */
    Page<CmsProgram> subsetSelfList(Page page, CmsProgram cmsProgram, String startTime,String endTime);

    IPage<CmsProgram> subsetSelfList(Page page, CmsProgramDO cmsProgramDO, String startTime,String endTime, Boolean flag);

    /**
     * 单集详情
     * @param id
     * @return
     */
    CmsProgramSimplesetDetailVO simplesetById(Long id);

    /**
     * 子集详情
     * @param id
     * @return
     */
    CmsProgram subsetById(Long id);

    /**
     * 终重单集列表
     * @param page
     * @return
     */
    Page<CmsProgram> simplesetEndList(Page page, CmsProgramEndDto cmsProgramEndDto);
    /**
     * 终重子集列表
     * @param page
     * @return
     */
    Page<CmsProgram> subsetEndList(Page page, CmsProgramEndDto cmsProgramEndDto);
    /**
     * 子集管理列表
     */
    Page<CmsProgram> manageList(Page page,Long id,Integer status);

    /**
     * 单集导出
     * @param response
     * @param cmsProgramVO
     * @param startTime
     * @param endTime
     */
    Object export( CmsProgramVO cmsProgramVO, String startTime, String endTime) throws Exception;

}
