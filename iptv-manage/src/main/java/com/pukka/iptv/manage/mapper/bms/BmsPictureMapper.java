package com.pukka.iptv.manage.mapper.bms;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.pukka.iptv.common.data.model.bms.BmsChannel;
import com.pukka.iptv.common.data.model.bms.BmsPicture;
import com.pukka.iptv.common.data.model.order.SubOrderMappingsEntity;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * @author: luo
 * @date: 2021-8-27 11:43:07
 */

@Mapper
public interface BmsPictureMapper extends BaseMapper<BmsPicture> {
    int updatePublishStatus(@Param("entityList") List<SubOrderMappingsEntity> orderMappingsEntities, @Param("spIdList") List<Long> spIdList);

    /**
     * (bmsContentId,cpId, spId)
     * 批量 in 删除
     * @param bmsChannels
     */
    int deleteByBmsContentIdAndCpIdAndSpId(@Param("bmsChannels") List<BmsChannel> bmsChannels);
}
