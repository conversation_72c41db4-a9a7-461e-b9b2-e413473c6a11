package com.pukka.iptv.manage.virtual.handler;

import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.pukka.iptv.common.base.enums.ActionEnums;
import com.pukka.iptv.common.base.enums.ContentTypeEnum;
import com.pukka.iptv.common.data.model.virtual.VirtualProgram;
import com.pukka.iptv.common.data.model.virtual.VirtualSeries;
import com.pukka.iptv.manage.mapper.api.VirtualProgramMapper;
import com.pukka.iptv.manage.mapper.api.VirtualSeriesMapper;
import com.pukka.iptv.manage.service.api.VirtualSeriesService;
import com.pukka.iptv.manage.virtual.IVirtualStrategyHandler;
import com.pukka.iptv.manage.virtual.util.DataCache;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Optional;

@Slf4j
@Component
public class UpdateContentHandler implements IVirtualStrategyHandler {
    @Autowired
    private DataCache dataCache;
    @Autowired
    private VirtualProgramMapper virtualProgramMapper;
    @Autowired
    private VirtualSeriesMapper virtualSeriesMapper;
    @Autowired
    private VirtualSeriesService virtualSeriesService;

    @Override
    public ActionEnums getContentType() {
        return ActionEnums.UPDATE;
    }

    @Override
    public void execute(ContentTypeEnum contentType, Long bmsContentId) {
        switch (contentType) {
            // 单集
            case FILM:
                List<VirtualProgram> contentInfo = virtualProgramMapper.getContentInfo(bmsContentId, dataCache.getSpIds());
                if (ObjectUtils.isEmpty(contentInfo) || contentInfo.size() < 1) {
                    log.info("虚拟频道 处理发布数据信息 -----> 当前处理类型:{} 当前数据 bmsContentId:{}.不符合虚拟频道规则，暂不处理", getContentType().getInfo(), bmsContentId);
                    return;
                }
                Optional<VirtualProgram> first = contentInfo.stream().findFirst();
                if (first.isPresent()) {
                    VirtualProgram virtualProgram = first.get();
                    int result = virtualProgramMapper.update(virtualProgram, Wrappers.lambdaUpdate(VirtualProgram.class)
                            .set(VirtualProgram::getContentType, virtualProgram.getContentType())
                            .set(VirtualProgram::getType, virtualProgram.getType())
                            .set(VirtualProgram::getName, virtualProgram.getName())
                            .set(VirtualProgram::getVspname, virtualProgram.getVspname())
                            .set(VirtualProgram::getDuration, virtualProgram.getDuration())
                            .set(VirtualProgram::getPrice, virtualProgram.getPrice())
                            .set(VirtualProgram::getCreateTime, virtualProgram.getCreateTime())
                            .eq(VirtualProgram::getCode, virtualProgram.getCode()));
                    log.info("虚拟频道 处理发布数据信息 -----> 当前处理类型:{} 当前数据:{}.入库结果:{}", getContentType().getInfo(), JSON.toJSONString(virtualProgram), result > 0 ? "成功" : "失败");
                } else {
                    log.info("虚拟频道 处理发布数据信息 -----> 当前处理类型:{} 当前数据 bmsContentId:{} 查询为空，暂不处理", getContentType().getInfo(), bmsContentId);
                    return;
                }
                break;
            // 子集
            case SUBSET:
                List<VirtualProgram> programInfo = virtualProgramMapper.getProgramInfo(bmsContentId, dataCache.getSpIds());
                if (ObjectUtils.isEmpty(programInfo) || programInfo.size() < 1) {
                    log.info("虚拟频道 处理发布数据信息 -----> 当前处理类型:{} 当前数据 bmsContentId:{}.不符合虚拟频道规则，暂不处理", getContentType().getInfo(), bmsContentId);
                    return;
                }
                Optional<VirtualProgram> program = programInfo.stream().findFirst();
                if (program.isPresent()) {
                    VirtualProgram virtualProgram = program.get();
                    int result = virtualProgramMapper.update(virtualProgram, Wrappers.lambdaUpdate(VirtualProgram.class)
                            .set(VirtualProgram::getSeriesCode, virtualProgram.getSeriesCode())
                            .set(VirtualProgram::getContentType, virtualProgram.getContentType())
                            .set(VirtualProgram::getType, virtualProgram.getType())
                            .set(VirtualProgram::getName, virtualProgram.getName())
                            .set(VirtualProgram::getVspname, virtualProgram.getVspname())
                            .set(VirtualProgram::getEpisodeindex, virtualProgram.getEpisodeindex())
                            .set(VirtualProgram::getDuration, virtualProgram.getDuration())
                            .set(VirtualProgram::getPrice, virtualProgram.getPrice())
                            .set(VirtualProgram::getCreateTime, virtualProgram.getCreateTime())
                            .eq(VirtualProgram::getCode, virtualProgram.getCode()));
                    log.info("虚拟频道 处理发布数据信息 -----> 当前处理类型:{} 当前数据:{}.入库结果:{}", getContentType().getInfo(), JSON.toJSONString(virtualProgram), result > 0 ? "成功" : "失败");
                } else {
                    log.info("虚拟频道 处理发布数据信息 -----> 当前处理类型:{} 当前数据 bmsContentId:{} 查询为空，暂不处理", getContentType().getInfo(), bmsContentId);
                    return;
                }
                break;
            //连续剧,序列片
            case TELEPLAY:
            case EPISODES:
                List<VirtualSeries> seriesInfo = virtualSeriesMapper.getSeriesInfo(bmsContentId, dataCache.getSpIds());
                if (ObjectUtils.isEmpty(seriesInfo) || seriesInfo.size() < 1) {
                    log.info("虚拟频道 处理发布数据信息 -----> 当前处理类型:{} 当前数据 bmsContentId:{}.不符合虚拟频道规则，暂不处理", getContentType().getInfo(), bmsContentId);
                    return;
                }
                Optional<VirtualSeries> seriesOptional = seriesInfo.stream().findFirst();
                if (seriesOptional.isPresent()) {
                    VirtualSeries virtualSeries = seriesOptional.get();
                    boolean result = virtualSeriesService.updateSeriesAndSubset(virtualSeries);
                    log.info("虚拟频道 处理发布数据信息 -----> 当前处理类型:{} 当前数据:{}.入库结果:{}", getContentType().getInfo(), JSON.toJSONString(virtualSeries), result ? "成功" : "失败");
                } else {
                    log.info("虚拟频道 处理发布数据信息 -----> 当前处理类型:{} 当前数据 bmsContentId:{} 查询为空，暂不处理", getContentType().getInfo(), bmsContentId);
                    return;
                }
                break;
            default:
                log.warn("虚拟频道 处理发布数据信息 -----> 当前数据类型 ContentType:{},该类型暂不处理", contentType);
                break;

        }
    }
}
