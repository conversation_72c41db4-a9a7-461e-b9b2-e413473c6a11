package com.pukka.iptv.manage.service.bms.impl;

import static com.pukka.iptv.common.base.enums.ContentTypeEnum.CATEGORY_PROGRAM;
import static com.pukka.iptv.common.base.enums.ContentTypeEnum.PACKAGE_PROGRAM;
import static com.pukka.iptv.common.core.util.StringUtils.rmTarWithSplit;
import static com.pukka.iptv.manage.service.condition.rule.RecycleStatusRule.NOT_RECYCLE_LIST;
import static com.pukka.iptv.manage.service.condition.rule.enums.PublishCheck.CAN_PUBLISH;
import static com.pukka.iptv.manage.service.condition.rule.enums.PublishCheck.ING;
import static com.pukka.iptv.manage.service.condition.rule.enums.PublishCheck.MUST_ING;
import static com.pukka.iptv.manage.service.condition.rule.enums.PublishCheck.PUBLISH_SUCCESS;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.metadata.TableInfo;
import com.baomidou.mybatisplus.core.metadata.TableInfoHelper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.pukka.iptv.common.base.constant.PublishParamTypeConstants;
import com.pukka.iptv.common.base.constant.RedisKeyConstants;
import com.pukka.iptv.common.base.enums.ActionEnums;
import com.pukka.iptv.common.base.enums.ContentTypeEnum;
import com.pukka.iptv.common.base.enums.IsTimedEnums;
import com.pukka.iptv.common.base.enums.PriorityEnums;
import com.pukka.iptv.common.base.enums.PublishStatusEnum;
import com.pukka.iptv.common.base.enums.StatisticsMediaMQEnum;
import com.pukka.iptv.common.base.enums.StatusEnum;
import com.pukka.iptv.common.base.exception.BizException;
import com.pukka.iptv.common.base.util.JwtTokenUtil;
import com.pukka.iptv.common.base.vo.CommonResponse;
import com.pukka.iptv.common.base.vo.SecurityUser;
import com.pukka.iptv.common.data.model.OutParamExpand;
import com.pukka.iptv.common.data.model.OutScheduledTask;
import com.pukka.iptv.common.data.model.bms.BmsCategory;
import com.pukka.iptv.common.data.model.bms.BmsCategoryContent;
import com.pukka.iptv.common.data.model.bms.BmsContent;
import com.pukka.iptv.common.data.model.bms.BmsPackageContent;
import com.pukka.iptv.common.data.model.order.SubOrderMappingsEntity;
import com.pukka.iptv.common.data.vo.bms.BmsCategoryBindContentVO;
import com.pukka.iptv.common.data.vo.bms.BmsCategoryBindContentVOExtend;
import com.pukka.iptv.common.data.vo.bms.BmsContentNotBindCategoryVO;
import com.pukka.iptv.common.data.vo.req.BmsCategoryContentQueryReq;
import com.pukka.iptv.common.data.vo.req.BmsCategoryContentQueryReqExtent;
import com.pukka.iptv.common.data.vo.req.BmsCategoryContentSortReq;
import com.pukka.iptv.common.data.vo.req.BmsCategoryNotBindContentQueryReq;
import com.pukka.iptv.common.data.vo.resp.Rt;
import com.pukka.iptv.common.rabbitmq.config.StatisticsOnlineMQConfig;
import com.pukka.iptv.manage.export.model.ExcelTaskInfo;
import com.pukka.iptv.manage.export.model.ExportInfo;
import com.pukka.iptv.manage.export.task.ExportTask;
import com.pukka.iptv.manage.mapper.bms.BmsBaseMapper;
import com.pukka.iptv.manage.mapper.bms.BmsCategoryContentMapper;
import com.pukka.iptv.manage.service.bms.BmsCategoryContentService;
import com.pukka.iptv.manage.service.bms.BmsCategoryService;
import com.pukka.iptv.manage.service.bms.BmsContentService;
import com.pukka.iptv.manage.service.bms.BmsSchedulePublishApi;
import com.pukka.iptv.manage.service.bms.common.LambdaTrans;
import com.pukka.iptv.manage.service.bms.common.WorkOrderOperation;
import com.pukka.iptv.manage.service.bms.dto.BmsCategoryContentRecycleDto;
import com.pukka.iptv.manage.service.common.ScheduledTasksUtil;
import com.pukka.iptv.manage.service.condition.RuleCondition;
import com.pukka.iptv.manage.service.condition.rule.LockStatusRule;
import com.pukka.iptv.manage.service.condition.rule.PublishStatusRule;
import com.pukka.iptv.manage.service.condition.rule.RecycleStatusRule;
import com.pukka.iptv.manage.service.condition.rule.TimePublishCheckRule;
import com.pukka.iptv.manage.service.condition.rule.enums.RecycleCheck;
import com.pukka.iptv.manage.service.sys.OutScheduledTasksService;
import com.pukka.iptv.manage.util.CommonUtils;
import com.pukka.iptv.manage.util.DateUtils;
import com.pukka.iptv.manage.util.RuleUtil;
import java.io.ByteArrayOutputStream;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Comparator;
import java.util.Date;
import java.util.HashMap;
import java.util.HashSet;
import java.util.Iterator;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.concurrent.CountDownLatch;
import java.util.stream.Collectors;
import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.amqp.rabbit.core.RabbitTemplate;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;

/**
 * 栏目和内容关系
 *
 * <AUTHOR>
 * @date 2021-08-27 10:20:57
 */

@Slf4j
@Service
public class BmsCategoryContentServiceImpl extends ServiceImpl<BmsCategoryContentMapper, BmsCategoryContent>
        implements BmsCategoryContentService, BmsSchedulePublishApi {
    @Autowired
    private BmsCategoryContentMapper bmsCategoryContentMapper;
    @Autowired
    private BmsContentService bmsContentService;
    @Autowired
    private BmsCategoryService bmsCategoryService;
    @Autowired
    private WorkOrderOperation workOrderOperation;
    @Autowired
    private RabbitTemplate rabbitTemplate;
    @Autowired
    private ExportTask<BmsCategoryContent> exportTask;
    @Autowired
    private OutScheduledTasksService outScheduledTasksService;
    @Autowired
    private ScheduledTasksUtil scheduledTasksUtil;

    //执行器线程池
    @Resource(name = "schedulePublishThreadPool")
    private ThreadPoolTaskExecutor threadPoolTaskExecutor;

    @Override
    public boolean updatePublishStatus(List<SubOrderMappingsEntity> orderMappingsEntities, List<Long> spIdList) {
        int result = bmsCategoryContentMapper.updatePublishStatus(orderMappingsEntities, spIdList);
        return result >= 1;
    }

    @Override
    public IPage<BmsCategoryContent> pageList(BmsCategoryContentQueryReq req) {
        return null;
    }

    @Override
    public List<BmsCategoryContent> getByCategoryContentId(Long id) {
        LambdaQueryWrapper<BmsCategoryContent> eq = Wrappers.lambdaQuery(BmsCategoryContent.class).eq(BmsCategoryContent::getId, id);
        return bmsCategoryContentMapper.selectList(eq);
    }

    @Override
    public IPage<BmsCategoryBindContentVO> queryCategoryBindContentList(BmsCategoryContentQueryReq queryReq) {
        CommonUtils.checkNames(queryReq);
        return bmsCategoryContentMapper.queryCategoryBindContentList(queryReq);
    }

    @Override
    public Object export(BmsCategoryContentQueryReqExtent req) throws Exception {
        try {

            List<BmsCategoryBindContentVO> resultList =
                    bmsCategoryContentMapper.queryCategoryBindContentListForExport(req);
            //导出设置
            List<BmsCategoryContent>   bmsCategoryContentList = new ArrayList<>();
            if( resultList.size() >= 0) {
                resultList.forEach(e -> {
                    BmsCategoryBindContentVOExtend bmsCategoryBindContentVOExtend = new BmsCategoryBindContentVOExtend();
                    BmsCategoryContent bmsCategoryContent = new BmsCategoryContent();
                    bmsCategoryContent.setSequence(e.getBccSequence());
                    bmsCategoryBindContentVOExtend.setContentType(Integer.valueOf(e.getContentType()));
                    long dateDiff = com.pukka.iptv.common.core.util.DateUtils.getDateDiff(e.getLicensingWindowEnd());
                    if (dateDiff < 0){
                        bmsCategoryBindContentVOExtend.setCopyRightDays("已过期");
                    }else{
                        bmsCategoryBindContentVOExtend.setCopyRightDays( String.valueOf(dateDiff));
                    }

                    BeanUtils.copyProperties(e, bmsCategoryBindContentVOExtend);
                    bmsCategoryContentList.add(bmsCategoryBindContentVOExtend);
                } );
            }
            long ms = System.currentTimeMillis();
            ExportInfo<BmsCategoryContent> exportInfo = new ExportInfo<>();
            exportInfo.setOut(new ByteArrayOutputStream())
                    .setSheetName("媒资栏目关系")
                    .setQueryWrapper(null)
                    .setPojoClass(BmsCategoryBindContentVOExtend.class)
                    .setCacheKey(RedisKeyConstants.CATEGORY_CONTENT_EXPORT)
                    .setBaseMapper(bmsCategoryContentMapper)
                    .setResultList(bmsCategoryContentList);
            ExcelTaskInfo excelTaskInfo = exportTask.startProcess(exportInfo).dowork();
            log.info("总导出时长为 {} ms", (System.currentTimeMillis() - ms));
            return JSON.toJSON(excelTaskInfo);
        } catch (Exception e) {
            log.error("栏目导出 -----> 失败，错误信息:{}", e);
        }
        return null;
    }

    /**
     * @description:查询 没有绑定指定栏目的内容列表
     * @param: [req]
     * @return: java.util.List<com.pukka.iptv.common.data.model.bms.BmsContent>
     * @author: wz
     * @date: 2021/9/26 17:48
     */
    @Override
    public IPage<BmsContentNotBindCategoryVO> queryContentsWhichNotBindThatCategory(BmsCategoryNotBindContentQueryReq req) {
        CommonUtils.checkNames(req);
        return bmsCategoryContentMapper.queryContentsWhichNotBindThatCategory(req);
    }

    /**
     * @description: 内容绑定栏目 需要将关系写入关系表，然后需要将栏目的ids同步到content表
     * @param: [categoryIds, contentIds]
     * @return: boolean
     * @author: wz
     * @date: 2021/9/26 15:39
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Rt<List<Long>> bind(List<Long> categoryIds, List<Long> contentIds, Integer source) {
        RuleCondition.create()
                //栏目锁定状态判断
                .and(LockStatusRule.init(BmsCategory.class).data(categoryIds))
                //内容锁定状态判断
                .and(LockStatusRule.init(BmsContent.class).data(contentIds))
                .execute().check();

        //step1:查询要绑定的内容
        List<BmsContent> cList =
                bmsContentService.list(Wrappers.lambdaQuery(BmsContent.class).in(BmsContent::getId, contentIds));
        //step2:查询要绑定的栏目
        List<BmsCategory> pList =
                bmsCategoryService.list(Wrappers.lambdaQuery(BmsCategory.class).in(BmsCategory::getId, categoryIds));
        //step3:查询 栏目和内容关系 进行多对多匹配
        LambdaUpdateWrapper<BmsCategoryContent> w = Wrappers.lambdaUpdate(BmsCategoryContent.class);
        Date now = new Date();
        List<BmsCategoryContent> patchList = new ArrayList<>(cList.size() * pList.size());
        //需要进行更新的content集合
        List<BmsContent> needUpdateCList = new ArrayList<>();
        //小集合带大集合,减少sql执行次数
        if (cList.size() < pList.size()) {
            List<Long> pIds = pList.stream().map(BmsCategory::getId).collect(Collectors.toList());
            for (BmsContent c : cList) {
                //查询已经存在的
                List<BmsCategoryContent> list = this.list(w.eq(BmsCategoryContent::getBmsContentId, c.getId()).in(BmsCategoryContent::getCategoryId, pIds));
                w.clear();
                //转化栏目id
                List<Long> exist = list.stream().map(BmsCategoryContent::getCategoryId).collect(Collectors.toList());
                //过滤已经存在的
                List<BmsCategoryContent> patch =
                        pList.stream().filter(p ->
                                !exist.contains(p.getId())).map(p -> tranBatch(c, p, now, source, needUpdateCList))
                                .collect(Collectors.toList());
                patchList.addAll(patch);
            }
        } else {
            List<Long> cIds = cList.stream().map(BmsContent::getId).collect(Collectors.toList());
            for (BmsCategory p : pList) {
                //查询已经存在的
                List<BmsCategoryContent> list = this.list(w.eq(BmsCategoryContent::getCategoryId, p.getId()).in(BmsCategoryContent::getBmsContentId, cIds));
                w.clear();
                //转化内容id
                List<Long> exist = list.stream().map(BmsCategoryContent::getBmsContentId).collect(Collectors.toList());
                //过滤已经存在的
                List<BmsContent> notExistList = cList.stream().filter(c -> !exist.contains(c.getId())).collect(Collectors.toList());
                List<BmsCategoryContent> patch = notExistList.stream().map(c -> tranBatch(c, p, now, source, needUpdateCList)).collect(Collectors.toList());
                patchList.addAll(patch);
            }
        }
        if (CollectionUtils.isEmpty(patchList)) {
            throw new BizException("媒资和栏目已绑定过！");
        }
        //批量新增
        boolean ok = this.saveBatch(patchList);
        if (ok) {
            //批量同步content表 category_ids 和 category_names
            bmsContentService.updateBatchById(needUpdateCList);
        }
        clearArgs();
        return Rt.ok(patchList.stream().map(BmsCategoryContent::getId).collect(Collectors.toList()));
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean unbind(List<Long> ids) {
        //检查内容和栏目绑定 发布状态 只能移除下游不存在的 所属状态
        //根据主键id查找出锁关联的内容和栏目ids
        List<BmsCategoryContent> list = this.list(Wrappers.lambdaQuery(BmsCategoryContent.class).in(BmsCategoryContent::getId, ids));
        list.forEach(c -> {
                    if (!PublishStatusRule.CAN_DELETE_LIST.contains(c.getPublishStatus())) {
                        throw new BizException("请检查关系发布状态，只有待发布、回收成功可移除");
                    }
                }
        );
        List<Long> contentIds = list.stream().map(BmsCategoryContent::getBmsContentId).distinct().collect(Collectors.toList());
        List<Long> categoryIds = list.stream().map(BmsCategoryContent::getCategoryId).distinct().collect(Collectors.toList());
        //分别检查内容和栏目的锁定状态
        RuleCondition.create()
                .and(LockStatusRule.init(BmsContent.class).data(contentIds))
                .and(LockStatusRule.init(BmsCategory.class).data(categoryIds))
                .execute().check();
        //直接删除
        boolean ok = this.removeByIds(ids);
        if (ok) {
            syncMsgToBmsContent(contentIds, list);
        }
        return ok;
    }

    public void syncMsgToBmsContent(List<Long> contentIds, List<BmsCategoryContent> list) {
        //下面开始更新 bmscontent 冗余的category 的 id和name
        List<BmsContent> updateList = new ArrayList<>();
        List<BmsContent> bmsContents = bmsContentService.getBaseMapper().selectBatchIds(contentIds);
        for (BmsCategoryContent item : list) {
            Long bmsContentId = item.getBmsContentId();
            BmsContent tar = bmsContents.stream().filter(c -> c.getId().equals(bmsContentId)).findFirst().get();
            rmCategoryFromBmsContent(tar, item.getCategoryId(), item.getCategoryName());
            //生成指定字段的po
            updateList.add(transPo(tar));
        }
        bmsContentService.updateBatchById(updateList);
    }

    /**
     * @description: 栏目内容关系发布
     * @param: [ids]
     * @return: boolean
     * @author: wz
     * @date: 2021/9/26 15:41
     */
    @Override
    public boolean publish(List<Long> ids, Date timing, Map<String, OutParamExpand> paramMap) {
        //是否是定时发布的标识
        boolean isSchedule = isSchedule();
        //step1: 查询 栏目内容关系
        List<BmsCategoryContent> list = this.getBaseMapper().selectBatchIds(ids);
        //找出栏目和内容id
        List<Long> categoryIds = list.stream().map(BmsCategoryContent::getCategoryId).collect(Collectors.toList());
        List<Long> contentIds = list.stream().map(BmsCategoryContent::getBmsContentId).collect(Collectors.toList());

        //step2: 状态检查
        //如果是媒资发布的
        if (!isCheck()) {
            RuleCondition.create()
                    //检查栏目和内容是否有锁定状态
                    .and(LockStatusRule.init(BmsContent.class).data(contentIds))
                    .and(LockStatusRule.init(BmsCategory.class).data(categoryIds))
                    //检查栏目的发布状态 是否是发布成功
                    .and(PublishStatusRule.init(BmsCategory.class).data(categoryIds).policy(PUBLISH_SUCCESS))
                    //检查内容的发布状态 是否是发布成功
                    .and(PublishStatusRule.init(BmsContent.class).data(contentIds).policy(PUBLISH_SUCCESS))
                    //检查关系是否都是 待发布 检查
                    .and(PublishStatusRule.init(BmsCategoryContent.class).data(ids).col(BmsCategoryContent::getCategoryName).policy(CAN_PUBLISH))
                    //不是定时发布才进行 检测是否有定时发布
                    .and(!isSchedule, TimePublishCheckRule.init(BmsCategoryContent.class).data(ids))
                    .execute().check();
        }

        //step3:调用发布接口
        if (timing != null) {
            //添加到定时发布任务表
            boolean scheduledTasks = outScheduledTasksService.createScheduledTasks(ids, CATEGORY_PROGRAM.getValue(),
                    DateUtils.getFormatDate(timing,DateUtils.YYYYMMDDHHMMSS), PriorityEnums.GENERAL.getValue());
            if(!scheduledTasks){
                log.error("栏目内容关系发布添加到定时发布任务表outScheduledTasks失败，ids:{}",ids);
                throw new BizException("定时发布任务添加失败");
            }
            LambdaUpdateWrapper<BmsCategoryContent> in = Wrappers.lambdaUpdate(BmsCategoryContent.class)
                    .set(BmsCategoryContent::getTimedPublish, timing)
                    .set(BmsCategoryContent::getTimedPublishStatus, IsTimedEnums.IS_TIMED.getCode())
                    .in(BmsCategoryContent::getId, ids);
            this.update(in);
        } else {
            List<BmsCategoryContent> categoryContents = getBmsCategoryContentList(ids);
            if (CollectionUtils.isEmpty(categoryContents)) {
                return false;
            }
            BmsCategoryContent obj = categoryContents.get(0);
            // 单集
            List<BmsCategoryContent> film = categoryContents.stream().filter(c -> ContentTypeEnum.FILM.getValue().equals(c.getContentType())).collect(Collectors.toList());
            // 剧集
            List<BmsCategoryContent> teleplay = categoryContents.stream().filter(c -> ContentTypeEnum.TELEPLAY.getValue().equals(c.getContentType())).collect(Collectors.toList());
            //系列片
            List<BmsCategoryContent> escList =
                    categoryContents.stream().filter(c -> ContentTypeEnum.EPISODES.getValue().equals(c.getContentType())).collect(Collectors.toList());

            // 单集
            if (!film.isEmpty()) {
                List<Long> filmUpdateIds = new ArrayList<>();
                List<Long> filmPublishIds = film.stream().filter(categoryContent -> {
                    if (!RuleUtil.isWaitPublish(categoryContent.getPublishStatus())) {
                        filmUpdateIds.add(categoryContent.getId());
                        return false;
                    }
                    return true;
                }).map(BmsCategoryContent::getId).collect(Collectors.toList());
                // TODO 单集关系更新
                if (!filmUpdateIds.isEmpty()) {
                    if (!this.sendCategoryContentOrder(ActionEnums.UPDATE, ContentTypeEnum.CATEGORY_PROGRAM, filmUpdateIds, obj.getSpId(), obj.getSpName(), paramMap)) {
                        throw new BizException("栏目单集关系 更新工单下发失败");
                    }
                }
                // TODO 单集关系发布
                if (!filmPublishIds.isEmpty()) {
                    if (!this.sendCategoryContentOrder(ActionEnums.REGIST, ContentTypeEnum.CATEGORY_PROGRAM, filmPublishIds, obj.getSpId(), obj.getSpName(), paramMap)) {
                        throw new BizException("栏目单集关系 发布工单下发失败");
                    }
                }
            }
            // 剧集
            if (!teleplay.isEmpty()) {
                List<Long> teleplayUpdateIds = new ArrayList<>();
                List<Long> teleplayPublishIds = teleplay.stream().filter(categoryContent -> {
                    if (!RuleUtil.isWaitPublish(categoryContent.getPublishStatus())) {
                        teleplayUpdateIds.add(categoryContent.getId());
                        return false;
                    }
                    return true;
                }).map(BmsCategoryContent::getId).collect(Collectors.toList());
                // TODO 剧集关系更新
                if (!teleplayUpdateIds.isEmpty()) {
                    if (!this.sendCategoryContentOrder(ActionEnums.UPDATE, ContentTypeEnum.CATEGORY_SERIES, teleplayUpdateIds, obj.getSpId(), obj.getSpName(), paramMap)) {
                        throw new BizException("栏目剧集关系 更新工单下发失败");
                    }
                }
                // TODO 剧集关系发布
                if (!teleplayPublishIds.isEmpty()) {
                    if (!this.sendCategoryContentOrder(ActionEnums.REGIST, ContentTypeEnum.CATEGORY_SERIES, teleplayPublishIds, obj.getSpId(), obj.getSpName(), paramMap)) {
                        throw new BizException("栏目剧集关系 发布工单下发失败");
                    }
                }
            }
            //系列片
            if (!escList.isEmpty()) {
                List<Long> escUpdateIds = new ArrayList<>();
                List<Long> escPublishIds = escList.stream().filter(categoryContent -> {
                    if (!RuleUtil.isWaitPublish(categoryContent.getPublishStatus())) {
                        escUpdateIds.add(categoryContent.getId());
                        return false;
                    }
                    return true;
                }).map(BmsCategoryContent::getId).collect(Collectors.toList());
                // TODO 剧集关系更新
                if (!escUpdateIds.isEmpty()) {
                    if (!this.sendCategoryContentOrder(ActionEnums.UPDATE, ContentTypeEnum.CATEGORY_SERIES, escUpdateIds, obj.getSpId(), obj.getSpName(), paramMap)) {
                        throw new BizException("栏目剧集关系 更新工单下发失败");
                    }
                }
                // TODO 剧集关系发布
                if (!escPublishIds.isEmpty()) {
                    if (!this.sendCategoryContentOrder(ActionEnums.REGIST, ContentTypeEnum.CATEGORY_SERIES, escPublishIds, obj.getSpId(), obj.getSpName(), paramMap)) {
                        throw new BizException("栏目剧集关系 发布工单下发失败");
                    }
                }
            }
        }
        return true;
    }

    @Override
    public boolean schedulePublish() {
        List<OutScheduledTask> scheduledTasks = outScheduledTasksService.getScheduledTasks(
                CATEGORY_PROGRAM.getValue());
        if (ObjectUtil.isEmpty(scheduledTasks)) {
            return true;
        }
        List<Long> collect = scheduledTasks.stream().map(OutScheduledTask::getContentId)
                .collect(Collectors.toList());
        List<BmsCategoryContent> list = this.list(Wrappers.lambdaQuery(BmsCategoryContent.class)
                .select(BmsCategoryContent::getId, BmsCategoryContent::getSpId, BmsCategoryContent::getTimedPublish)
                .in(BmsCategoryContent::getId,collect));
        if (ObjectUtil.isEmpty(list)) {
            //更新定时发布状态
            outScheduledTasksService.finishScheduledTasks(scheduledTasks.stream().map(OutScheduledTask::getContentId).collect(Collectors.toList()), CATEGORY_PROGRAM.getValue());
            return true;
        }
        //添加媒资库不存在定时任务校验
        List<Long> ids = list.stream().map(BmsCategoryContent::getId).collect(Collectors.toList());
        List<Long> filter = scheduledTasksUtil.filterScheduledTask(
                scheduledTasks, ids);
        if (CollectionUtil.isNotEmpty(filter)) {
            //更新状态
            outScheduledTasksService.finishScheduledTasks(filter,
                    ContentTypeEnum.CATEGORY_PROGRAM.getValue());
        }
        Exception exc = null;
        try {
            //添加定时发布任务状态双重校验
            Iterator<Long> iterator = collect.iterator();
            while (iterator.hasNext()) {
                Long item = iterator.next();
                try {
                    relaCheck(Collections.singletonList(item));
                } catch (Exception e) {
                    log.error("定时发布任务状态双重校验失败", e);
                    //更新媒资状态
                    outScheduledTasksService.finishScheduledTasks(Collections.singletonList(item), CATEGORY_PROGRAM.getValue());
                    this.update(Wrappers.lambdaUpdate(BmsCategoryContent.class)
                            .set(BmsCategoryContent::getTimedPublishDescription, "定时发布状态校验失败")
                            .set(BmsCategoryContent::getTimedPublishStatus, IsTimedEnums.NO_TIMED.getCode())
                            .eq(BmsCategoryContent::getId, item));
                    iterator.remove();
                }
            }
            if (CollectionUtils.isEmpty(collect)) {
                log.info("定时发布任务状态双重校验结束，无可发布内容");
                return true;
            }
            Integer priority = scheduledTasks.get(0).getPriority();
            Map<String, OutParamExpand> paramMap = new HashMap<>();
            paramMap.put(PublishParamTypeConstants.PRIORITY,
                    new OutParamExpand().setPriority(
                            priority < 0 ? PriorityEnums.GENERAL.getValue() : priority));
            //设置定时发布的标识
            setScheduleFlag();
            this.publish(collect, null, paramMap);
        } catch (Exception e) {
            exc = e;
            log.error(e.getMessage(), e);
        } finally {
            if (org.apache.commons.lang3.ObjectUtils.isNotEmpty(exc)) {
                for (OutScheduledTask scheduledTask : scheduledTasks) {
                    scheduledTask.setTimedPublishDescription(exc.getMessage());
                }
            }
            //更新定时发布状态
            outScheduledTasksService.finishScheduledTasks(collect, CATEGORY_PROGRAM.getValue());
            this.update(Wrappers.lambdaUpdate(BmsCategoryContent.class)
                    .set(BmsCategoryContent::getTimedPublishDescription, exc != null ? exc.getMessage() : "")
                    .set(BmsCategoryContent::getTimedPublishStatus, IsTimedEnums.NO_TIMED.getCode())
                    .in(BmsCategoryContent::getId,collect));
        }
        return true;
    }

    @Override
    public boolean cancelTimedPublish(Long id) {
        //取消定时发布
        boolean b = outScheduledTasksService.deleteScheduledTask(id,
                ContentTypeEnum.CATEGORY_PROGRAM.getValue());
        if (!b) {
            log.error("栏目内容关系取消定时发布失败，id:{}", id);
            throw new BizException("取消定时发布失败");
        }
        LambdaUpdateWrapper<BmsCategoryContent> wrapper =
                Wrappers.lambdaUpdate(BmsCategoryContent.class)
                        .set(BmsCategoryContent::getTimedPublish, null)
                        .set(BmsCategoryContent::getTimedPublishStatus, IsTimedEnums.NO_TIMED.getCode())
                        .eq(BmsCategoryContent::getId, id);
        return this.update(wrapper);
    }

    /**
     * @description: 栏目和内容关系的回收
     * @param: [ids]
     * @return: boolean
     * @author: wz
     * @date: 2021/9/28 10:02
     */
    @Override
    public boolean recycle(List<Long> ids) {
        //检查 如果有不可回收的数据就报错
        RuleCondition.create().and(RecycleStatusRule.init(BmsCategoryContent.class).col(BmsCategoryContent::getId)
                .data(ids).policy(RecycleCheck.CAN_NOT_RECYCLE))
                .execute().check();
        //详情集合
        List<BmsCategoryContent> list = findRecycleList(ids);
        if (ObjectUtil.isEmpty(list)) {
            log.info("集合为空");
            return true;
        }
        //找出栏目和内容id
        List<Long> categoryIds = list.stream()
                .map(BmsCategoryContent::getCategoryId).collect(Collectors.toList());
        List<Long> contentIds = list.stream()
                .map(BmsCategoryContent::getBmsContentId).collect(Collectors.toList());
        //step2: 状态检查
        //检查栏目和内容是否有锁定状态
        RuleCondition.create().and(LockStatusRule.init(BmsContent.class).data(contentIds))
                .and(LockStatusRule.init(BmsCategory.class).data(categoryIds))
                .execute().check();
        return recycleHandler(list);
    }

    /**
     * @param ids
     * @return boolean
     * @Description 栏目内容关系的一键回收
     * <AUTHOR>
     * @date 2021/11/23 16:08
     */
    @Override
    public boolean recycleAll(List<Long> ids) {
        List<BmsCategoryContent> list = findRecycleList(ids);
        if (ObjectUtil.isEmpty(list)) {
            log.info("集合为空");
            return true;
        }
        return recycleHandler(list);
    }

    private List<BmsCategoryContent> findRecycleList(List<Long> ids) {
        //step1: 查询 栏目内容关系
        LambdaQueryWrapper<BmsCategoryContent> query = Wrappers.lambdaQuery(BmsCategoryContent.class)
                .select(BmsCategoryContent::getId, BmsCategoryContent::getCategoryId, BmsCategoryContent::getBmsContentId,
                        BmsCategoryContent::getSpId, BmsCategoryContent::getSpName, BmsCategoryContent::getContentType)
                // 跳过不需要回收的 发布失败|回收成功|待发布的|回收中
                .notIn(BmsCategoryContent::getPublishStatus, NOT_RECYCLE_LIST)
                .in(BmsCategoryContent::getId, ids);
        return this.list(query);
    }

    private boolean recycleHandler(List<BmsCategoryContent> list) {
        log.info("调用栏目内容回收接口");
        if (ObjectUtil.isEmpty(list)) {
            return true;
        }
        // TODO 回收工单
        BmsCategoryContent sp = list.get(0);
        // 单集
        List<Long> film =
                list.stream().filter(c -> ContentTypeEnum.FILM.getValue().equals(c.getContentType()))
                        .map(BmsCategoryContent::getId).filter(Objects::nonNull)
                        .collect(Collectors.toList());
        // 剧集
        List<Long> series =
                list.stream().filter(c -> ContentTypeEnum.TELEPLAY.getValue().equals(c.getContentType()))
                        .map(BmsCategoryContent::getId).filter(Objects::nonNull).collect(Collectors.toList());

        // 系列片
        List<Long> escList =
                list.stream().filter(c -> ContentTypeEnum.EPISODES.getValue().equals(c.getContentType()))
                        .map(BmsCategoryContent::getId).filter(Objects::nonNull).collect(Collectors.toList());
        Map<String, OutParamExpand> paramMap = new HashMap<>();
        paramMap.put(PublishParamTypeConstants.PRIORITY, new OutParamExpand().setPriority(PriorityEnums.GENERAL.getValue()));
        // 单集
        if (!film.isEmpty()) {
            if (!this.sendCategoryContentOrder(ActionEnums.DELETE, ContentTypeEnum.CATEGORY_PROGRAM, film, sp.getSpId(), sp.getSpName(), paramMap)) {
                throw new BizException("栏目单集关系 回收工单下发失败");
            }
        }
        // 剧集
        if (!series.isEmpty()) {
            if (!this.sendCategoryContentOrder(ActionEnums.DELETE, ContentTypeEnum.CATEGORY_SERIES, series, sp.getSpId(), sp.getSpName(), paramMap)) {
                throw new BizException("栏目剧集关系 回收工单下发失败");
            }
        }
        // 系列片
        if (!escList.isEmpty()) {
            if (!this.sendCategoryContentOrder(ActionEnums.DELETE, ContentTypeEnum.CATEGORY_SERIES, escList, sp.getSpId(), sp.getSpName(), paramMap)) {
                throw new BizException("栏目系列片关系 回收工单下发失败");
            }
        }
        return true;
    }

    // 栏目内容关系 多选排序
    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean sort(List<BmsCategoryContentSortReq> reqList) {
        if (CollectionUtils.isEmpty(reqList)) {
            return false;
        }
        List<Long> ids = reqList.stream().map(BmsCategoryContentSortReq::getId).collect(Collectors.toList());
        //step1: 查询 栏目内容关系
        LambdaQueryWrapper<BmsCategoryContent> wrapper = Wrappers.lambdaQuery(BmsCategoryContent.class)
                .select(BmsCategoryContent::getId, BmsCategoryContent::getBmsContentId, BmsCategoryContent::getCategoryId)
                .in(BmsCategoryContent::getId, ids);
        List<Map<String, Object>> list = this.listMaps(wrapper);
        if (CollectionUtils.isEmpty(list)) return true;
        //找出内容id
        Set<Long> contentIds = new HashSet<>(list.size());
        LambdaTrans<BmsCategoryContent> lt = new LambdaTrans<>();
        list.forEach(item -> {
            contentIds.add(lt.trans(item, BmsCategoryContent::getBmsContentId, Long.class));
        });
        //step2: 状态检查
        RuleCondition.create()
                //检查是否 包含 是发布中 的关系 （发布超时，在DB中的体现就是 中间态 状态）
                .and(PublishStatusRule.init(BmsCategoryContent.class).col(BmsCategoryContent::getCategoryName).data(ids).policy(ING))
                //检查栏目和内容是否有锁定状态
                .and(LockStatusRule.init(BmsContent.class).data(contentIds))
                .execute().check();
        // 查询按id升序
        List<BmsCategoryContent> findResult = this.list(Wrappers.lambdaQuery(BmsCategoryContent.class)
                .select(BmsCategoryContent::getId, BmsCategoryContent::getPublishStatus)
                .in(BmsCategoryContent::getId, ids)
                .orderByAsc(BmsCategoryContent::getId));
        // 入参id和查询结果都按升序排列，传入的id数量，查询结果总数必须一致
        if (findResult.size() != reqList.size()) {
            return false;
        }
        // 升序排列
        List<BmsCategoryContentSortReq> afterSorting = reqList.stream().sorted(Comparator.comparing(BmsCategoryContentSortReq::getId)).collect(Collectors.toList());
        List<BmsCategoryContent> collect = new ArrayList<>();
        for (int i = 0; i < afterSorting.size(); i++) {
            BmsCategoryContent po = new BmsCategoryContent();
            po.setId(afterSorting.get(i).getId());
            po.setSequence(afterSorting.get(i).getSequence());
            // 确认即将要修改的发布状态
            Integer newStatus = RuleUtil.confirmPublishStatus(findResult.get(i).getPublishStatus());
            po.setPublishStatus(newStatus);
            collect.add(po);
        }
        if (!CollectionUtils.isEmpty(collect)) {
            this.updateBatchById(collect);
        }
        return true;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean resetPublishStatus(List<Long> ids) {
        //step1: 查询 栏目内容关系
        LambdaQueryWrapper<BmsCategoryContent> wrapper = Wrappers.lambdaQuery(BmsCategoryContent.class)
                .select(BmsCategoryContent::getId, BmsCategoryContent::getPublishStatus, BmsCategoryContent::getBmsContentId, BmsCategoryContent::getCategoryId)
                .in(BmsCategoryContent::getId, ids);
        List<Map<String, Object>> list = this.listMaps(wrapper);
        if (CollectionUtils.isEmpty(list)) return true;
        //找出栏目和内容id
        Set<Long> categoryIds = new HashSet<>(list.size());
        Set<Long> contentIds = new HashSet<>(list.size());
        LambdaTrans<BmsCategoryContent> lt = new LambdaTrans<>();
        list.forEach(item -> {
            categoryIds.add(lt.trans(item, BmsCategoryContent::getCategoryId, Long.class));
            contentIds.add(lt.trans(item, BmsCategoryContent::getBmsContentId, Long.class));
        });
        //step2: 状态检查
        RuleCondition.create()
                //检查是否 包含 不是发布中 的关系 （发布超时，在DB中的体现就是 中间态 状态）
                .and(PublishStatusRule.init(BmsCategoryContent.class)
                        .col(BmsCategoryContent::getCategoryName).data(ids).policy(MUST_ING).type("重置发布状态"))
                //检查栏目和内容是否有锁定状态
                .and(LockStatusRule.init(BmsContent.class).data(contentIds))
                .and(LockStatusRule.init(BmsCategory.class).data(categoryIds))
                .execute().check();
        //step2.1查询对应发布状态
        List<BmsCategoryContent> updateList = list.stream().map(item -> {
            Long id = lt.trans(item, BmsCategoryContent::getId, Long.class);
            Integer status = lt.trans(item, BmsCategoryContent::getPublishStatus, Integer.class);
            return updatePublishTrans(id, status);
        }).collect(Collectors.toList());
        //step2.2更新对应的发布状态
        this.updateBatchById(updateList);
        return true;
    }

    private BmsCategoryContent updatePublishTrans(Long id, Integer publishStatus) {
        BmsCategoryContent po = new BmsCategoryContent();
        po.setId(id);
        po.setPublishDescription("");
        po.setPublishStatus(CommonUtils.resetPublishStatus(publishStatus));
        //取消定时发布
        po.setTimedPublishStatus(IsTimedEnums.NO_TIMED.getCode());
        po.setTimedPublishDescription("");
        return po;
    }

    //栏目内容关系的 修改发布状态
    @Override
    public boolean modifyPublishStatus(List<Long> ids, Integer publishStatus) {
        if (CollectionUtils.isEmpty(ids)) return true;
        //step1:检查栏目和内容关系是否是发布中状态

        //step2:根据 查询所属栏目，并判断栏目的锁定状态,如果有栏目是锁定的状态，则抛出异常
        //step2.1:查询所属栏目的id集合
        LambdaQueryWrapper<BmsCategoryContent> query = Wrappers.lambdaQuery(BmsCategoryContent.class)
                .select(BmsCategoryContent::getCategoryId, BmsCategoryContent::getBmsContentId)
                .isNotNull(BmsCategoryContent::getCategoryId)
                .in(BmsCategoryContent::getId, ids);
        List<BmsCategoryContent> list = this.list(query);
        List<Long> categoryIds = list.stream().map(BmsCategoryContent::getCategoryId).distinct()
                .collect(Collectors.toList());
        List<Long> contentIds = list.stream().map(BmsCategoryContent::getBmsContentId).distinct().collect(Collectors.toList());
        //step2.2:检查所属栏目和内容的锁定状态
        RuleCondition.create()
                .and(LockStatusRule.init(BmsContent.class).data(contentIds))
                .and(LockStatusRule.init(BmsCategory.class).data(categoryIds))
                .execute().check();
        List<BmsCategoryContent> bmsCategoryContents = bmsCategoryContentMapper.selectBatchIds(ids);
        //step3:修改栏目内容关系的发布状态
        LambdaUpdateWrapper<BmsCategoryContent> updateWrapper =
                Wrappers.lambdaUpdate(BmsCategoryContent.class)
                        .set(BmsCategoryContent::getPublishTime, null)
                        .set(BmsCategoryContent::getPublishDescription, "")
                        .set(BmsCategoryContent::getPublishStatus, publishStatus)
                        .set(BmsCategoryContent::getTimedPublishDescription, "")
                        .set(BmsCategoryContent::getTimedPublishStatus, IsTimedEnums.NO_TIMED.getCode())
                        //where
                        .in(BmsCategoryContent::getId, ids);
        this.update(updateWrapper);
        //更新定时发布状态
        outScheduledTasksService.finishScheduledTasks(ids, CATEGORY_PROGRAM.getValue());
        //向统计报表发送消息
        sendMQ(bmsCategoryContents, publishStatus);
        return true;
    }

    private void sendMQ(List<BmsCategoryContent> bmsCategoryContents, Integer publishStatus) {
        String type;
        if (PublishStatusEnum.PUBLISH.getCode().equals(publishStatus)) {
            type = StatisticsMediaMQEnum.BMS_CATEGORY_CONTENT_SUCCESS.getValue();
        } else if (PublishStatusEnum.WAITPUBLISH.getCode().equals(publishStatus)) {
            type = StatisticsMediaMQEnum.BMS_CATEGORY_CONTENT_WAITPUBLISH.getValue();
        } else {
            return;
        }
        for (BmsCategoryContent bmsCategoryContent : bmsCategoryContents) {
            Integer publishStatusOld = bmsCategoryContent.getPublishStatus();
            if (Boolean.FALSE.equals(verifyPublishStatus(publishStatusOld,publishStatus))) {
                continue;
            }
            bmsCategoryContent.setPublishStatus(publishStatus);
            bmsCategoryContent.setPublishTime(DateUtils.getNowString());
            Map<String, Object> map = new HashMap<>();
            map.put(type, bmsCategoryContent);
            log.info("BmsCategoryContentController.modifyPublishStatus.sendMQ.map={}", JSON.toJSONString(map));
            this.rabbitTemplate.convertAndSend(StatisticsOnlineMQConfig.STATISTIC_ONLINE_EXCHANGE, StatisticsOnlineMQConfig.STATISTIC_ONLINE_ROUTING, map);
        }
    }

    private Boolean verifyPublishStatus(Integer publishStatusOld, Integer publishStatusNew) {
        if (publishStatusOld.equals(publishStatusNew)) {
            return false;
        }
        if (PublishStatusEnum.ROLLBACKING.getCode().equals(publishStatusOld)) {
            return false;
        }
        if (PublishStatusEnum.ROLLBACK.getCode().equals(publishStatusOld)
                && PublishStatusEnum.WAITPUBLISH.getCode().equals(publishStatusNew)) {
            return false;
        }
        if (PublishStatusEnum.FAILROLLBACK.getCode().equals(publishStatusOld)
                && PublishStatusEnum.PUBLISH.getCode().equals(publishStatusNew)) {
            return false;
        }
        if (PublishStatusEnum.WAITUPDATE.getCode().equals(publishStatusOld)
                &&  PublishStatusEnum.WAITPUBLISH.getCode().equals(publishStatusNew)) {
            return false;
        }
        if (PublishStatusEnum.PUBLISHING.getCode().equals(publishStatusOld)
                && PublishStatusEnum.WAITPUBLISH.getCode().equals(publishStatusNew)) {
            return false;
        }
        return true;
    }

    @Override
    public List<BmsCategoryContent> getByCategoryIds(List<Long> categoryIds) {
        LambdaUpdateWrapper<BmsCategoryContent> eq =
                Wrappers.lambdaUpdate(BmsCategoryContent.class)
                        .eq(BmsCategoryContent::getCategoryId, categoryIds);
        return this.list(eq);
    }

    @Override
    public List<BmsCategoryContent> getByContentIds(List<Long> contentIds) {
        if (ObjectUtils.isEmpty(contentIds)) {
            return null;
        }
        LambdaUpdateWrapper<BmsCategoryContent> eq =
                Wrappers.lambdaUpdate(BmsCategoryContent.class)
                        .in(BmsCategoryContent::getBmsContentId, contentIds);
        return this.list(eq);
    }

    //po转换
    private BmsCategoryContent tranBatch(BmsContent c, BmsCategory p, Date now, Integer source, List<BmsContent> list) {
        Integer sequence;
        if (!getArgs(p.getId())) {
            sequence = getSequence(p.getId(), BmsCategoryContent.class);
            setSequence(p.getId(), sequence);
        } else {
            sequence = getSequence(p.getId());
        }
        BmsCategoryContent tmp = new BmsCategoryContent();
        //content
        tmp.setBmsContentId(c.getId());
        tmp.setContentName(c.getName());
        tmp.setContentType(c.getContentType());
        tmp.setCmsContentCode(c.getCmsContentCode());
        //下发通道
        tmp.setOutPassageIds(c.getOutPassageIds());
        tmp.setOutPassageNames(c.getOutPassageNames());
        //sp
        tmp.setSpId(c.getSpId());
        tmp.setSpName(c.getSpName());
        //category
        tmp.setCategoryId(p.getId());
        tmp.setCategoryCode(p.getCode());
        tmp.setCategoryName(p.getName());
        //status
        tmp.setStatus(1);
        tmp.setCreateTime(now);
        tmp.setSource(source);
        tmp.setSequence(sequence);

        SecurityUser securityUser = JwtTokenUtil.getSecurityUser();
        if (securityUser != null) {
            tmp.setCreatorId(securityUser.getId());
            tmp.setCreatorName(securityUser.getName());
        }
        //收集需要更新的content
        list.add(transCategoryIdToBmsContent(c, p));
        return tmp;
    }

    @Autowired
    private BmsBaseMapper bmsBaseMapper;

    public Integer getSequence(Long categoryId, Class<?> table) {
        TableInfo tableInfo = TableInfoHelper.getTableInfo(table);
        Integer sequence = bmsBaseMapper.getSequence(tableInfo.getTableName(), categoryId);
        if (sequence == null) {
            sequence = 999;
        } else if (sequence > 1) {
            sequence--;
        }
        return sequence;
    }

    private static BmsContent transPo(BmsContent tar) {
        if (tar == null) return null;
        BmsContent tmp = new BmsContent();
        tmp.setId(tar.getId());
        tmp.setCategoryIds(tar.getCategoryIds());
        tmp.setCategoryNames(tar.getCategoryNames());
        tmp.setUpdateTime(new Date());
        return tmp;
    }

    /**
     * @description: 将栏目的id和name添加给content表
     * @author: wz
     * @date: 2021/9/26 10:42
     */
    private static BmsContent transCategoryIdToBmsContent(BmsContent c, BmsCategory p) {
        c.setCategoryIds(StringUtils.isEmpty(c.getCategoryIds()) ? p.getId().toString() : (c.getCategoryIds() + "," + p.getId()));
        c.setCategoryNames(StringUtils.isEmpty(c.getCategoryNames()) ? p.getName() : (c.getCategoryNames() + "," + p.getName()));
        return c;
    }

    /**
     * @param c
     * @param categoryId
     * @param categoryName
     * @return void
     * @description: 为content删除指定的category 的id和name
     * <AUTHOR>
     * @date 2021/9/26 11:43
     */
    private static void rmCategoryFromBmsContent(BmsContent c, Long categoryId, String categoryName) {
        if (c == null) return;
        String categoryIds = c.getCategoryIds();
        if (StringUtils.isNotEmpty(categoryIds)) {
            String ids = rmTarWithSplit(categoryIds, ",", categoryId.toString());
            c.setCategoryIds(ids);
        }
        String categoryNames = c.getCategoryNames();
        if (StringUtils.isNotEmpty(categoryNames)) {
            String names = rmTarWithSplit(categoryNames, ",", categoryName);
            c.setCategoryNames(names);
        }
    }

    /**
     * @Description 可回收的栏目内容关系
     * <AUTHOR>
     * @Date 2021-09-24 15:09:35
     * @Return 返回 可回收的单集/剧集id 不包含子集
     */
    @Override
    public List<Long> categoryContentCanBeRecycled(List<Long> contentIds) {
        Set<Long> categoryIds = new HashSet<>();
        // 发布中/更新中/回收中 的栏目内容关系
        // 查关系表 单集和剧集
        List<Long> operable = this.list(
                Wrappers.lambdaQuery(BmsCategoryContent.class).in(BmsCategoryContent::getBmsContentId, contentIds)
        ).stream().filter(categoryContent -> {
            Integer publishStatus = categoryContent.getPublishStatus();
            if (RuleUtil.canRecycle(publishStatus)) {
                categoryIds.add(categoryContent.getCategoryId());
                return true;
            }
            return false;
        }).map(BmsCategoryContent::getId).collect(Collectors.toList());
        // 对应的栏目是否锁定
        if (!categoryIds.isEmpty()) {
            RuleUtil.checkLockStatus(BmsCategory.class, categoryIds).check();
        }
        return operable;
    }

    @Override
    public List<Long> getCategoryIdsByContentIds(List<Long> contentId) {
        List<BmsCategoryContent> list = this.list(Wrappers.lambdaQuery(BmsCategoryContent.class)
                .select(BmsCategoryContent::getCategoryId)
                .in(BmsCategoryContent::getBmsContentId, contentId));
        return list.stream().map(BmsCategoryContent::getCategoryId).distinct().collect(Collectors.toList());
    }

    /**
     * <AUTHOR>
     * @Date 2021-11-06 15:12:23
     * @Description 下发栏目内容
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean sendCategoryContentOrder(ActionEnums actionEnums, ContentTypeEnum contentTypeEnum, List<Long> contents, Long spId, String spName, Map<String, OutParamExpand> paramMap) {
        if (CollectionUtils.isEmpty(contents)) return true;
        return workOrderOperation.send(actionEnums, contentTypeEnum, contents, null, spId, spName,
                (success, publishStatus, description) -> {
                    // 更新发布状态以及发布描述
                    this.update(Wrappers.lambdaUpdate(BmsCategoryContent.class)
                            .set(BmsCategoryContent::getPublishStatus, publishStatus)
                            .set(BmsCategoryContent::getPublishDescription, description)
                            .set(BmsCategoryContent::getPublishTime, new Date())
                            .set(BmsCategoryContent::getTimedPublishDescription, "")
                            .in(BmsCategoryContent::getId, contents));
                }, paramMap);
    }

    @Override
    public boolean deleteByCodeAndSp(List<SubOrderMappingsEntity> deleteCategoryContentEntities, List<Long> spIdList) {
        log.info("CP自动发布删除栏目内容关系");

       /* boolean allSpRollback = true;
        LambdaQueryWrapper<BmsCategoryContent> wrapper = Wrappers.lambdaQuery(BmsCategoryContent.class)
                .notIn(BmsCategoryContent::getSpId, spIdList)
                .notIn(BmsCategoryContent::getPublishStatus,
                        WAITPUBLISH.getCode(),
                        ROLLBACK.getCode(),
                        FAILPUBLISH.getCode()
                ).and(queryWrapper ->
                        deleteCategoryContentEntities.forEach(orderMappingsEntity ->
                                queryWrapper.or(child -> child
                                        .eq(BmsCategoryContent::getCmsContentCode, orderMappingsEntity.getElementCode())
                                        .eq(BmsCategoryContent::getCategoryCode, orderMappingsEntity.getParentCode())
                                )
                        )
                );
        Long extendSpContentCount = bmsCategoryContentMapper.selectCount(wrapper);
        if (extendSpContentCount != null && extendSpContentCount > 0) {
            log.error("该Cp Category mapping已授权给其他非自动发布SP且已经发布到运营商");
            allSpRollback = false;
//            throw new CommonResponseException(CommonResponseEnum.DELETE_ERROR);
        }
        boolean finalAllSpRollback = allSpRollback;*/
        deleteCategoryContentEntities.forEach(orderMappingsEntity -> {
            bmsCategoryContentMapper.delete(Wrappers.lambdaQuery(BmsCategoryContent.class)
                    .eq(BmsCategoryContent::getCategoryCode, orderMappingsEntity.getParentCode())
                    .eq(BmsCategoryContent::getCmsContentCode, orderMappingsEntity.getElementCode())
                    .in(BmsCategoryContent::getSpId, spIdList)
            );
            // 更新bmscontent上的categoryids names字段
            List<Long> contentIds = bmsContentService.list(Wrappers.lambdaQuery(BmsContent.class)
                    .eq(BmsContent::getCmsContentCode, orderMappingsEntity.getElementCode())
                    .in(BmsContent::getSpId, spIdList))
                    .stream().map(BmsContent::getId).collect(Collectors.toList());
            List<BmsCategoryContent> categoryContents = bmsCategoryContentMapper.selectList(Wrappers.lambdaQuery(BmsCategoryContent.class)
                    .eq(BmsCategoryContent::getCmsContentCode, orderMappingsEntity.getElementCode())
                    .in(BmsCategoryContent::getSpId, spIdList));
            syncMsgToBmsContent(contentIds, categoryContents);
        });
        return true;
    }


    private List<BmsCategoryContent> getBmsCategoryContentList(List<Long> ids) {
        return this.list(Wrappers.lambdaQuery(BmsCategoryContent.class)
                .select(BmsCategoryContent::getId, BmsCategoryContent::getSpId, BmsCategoryContent::getSpName,
                        BmsCategoryContent::getPublishStatus, BmsCategoryContent::getContentType)
                .in(BmsCategoryContent::getId, ids));
    }

    @Override
    public boolean preCheck() {
        BmsCategoryContentRecycleDto args = getArgs(BmsCategoryContentRecycleDto.class);
        List<Long> ids = args.getIds();
        List<Long> categoryIds = args.getCategoryIds();
        if (ObjectUtil.isEmpty(ids) && ObjectUtil.isEmpty(categoryIds)) {
            log.warn("BmsCategoryContentRecycleDto为空");
            return false;
        }
        //如果检查已经通过则不进行检查
        if (args.isPreCheckPass()) {
            return true;
        }
        log.info("检查栏目是否有锁定状态");
        if (ObjectUtil.isNotEmpty(categoryIds)) {
            RuleCondition.create().and(LockStatusRule.init(BmsCategory.class).data(categoryIds)).execute().check();
        } else if (ObjectUtil.isNotEmpty(ids)) {
            List<BmsCategoryContent> list = this.list(Wrappers.lambdaQuery(BmsCategoryContent.class)
                    .select(BmsCategoryContent::getId, BmsCategoryContent::getCategoryId)
                    .in(BmsCategoryContent::getId, ids));
            //找出栏目id
            categoryIds = list.stream()
                    .map(BmsCategoryContent::getCategoryId).collect(Collectors.toList());
            RuleCondition.create().and(LockStatusRule.init(BmsCategory.class).data(categoryIds)).execute().check();
        }
        args.setPreCheckPass(true);
        return true;
    }

    @Override
    public List<BmsCategoryContent> findCanRecycleList() {
        BmsCategoryContentRecycleDto args = getArgs(BmsCategoryContentRecycleDto.class);
        List<Long> ids = args.getIds();
        if (ObjectUtil.isEmpty(ids)) {
            return null;
        }
        return this.list(Wrappers.lambdaQuery(BmsCategoryContent.class)
                .select(BmsCategoryContent::getId, BmsCategoryContent::getContentType,
                        BmsCategoryContent::getSpId, BmsCategoryContent::getSpName,
                        BmsCategoryContent::getBmsContentId,
                        BmsCategoryContent::getPublishStatus)
                // 跳过不需要回收的 发布失败|回收成功|待发布的
                .notIn(BmsCategoryContent::getPublishStatus, RecycleStatusRule.NOT_RECYCLE_LIST)
                .in(BmsCategoryContent::getId, ids));
    }

    @Override
    public boolean deleteRemain() {
        BmsCategoryContentRecycleDto args = getArgs(BmsCategoryContentRecycleDto.class);
        List<Long> ids = args.getIds();
        if (ObjectUtil.isEmpty(ids)) {
            log.warn("BmsCategoryContentRecycleDto为空");
            return true;
        }
        log.info("删除未发布的栏目内容关系");
        return this.remove(Wrappers.lambdaQuery(BmsCategoryContent.class)
                .in(BmsCategoryContent::getPublishStatus, RecycleStatusRule.CAN_DELETE_LIST)
                .in(BmsCategoryContent::getId, ids));
    }

    @Override
    public List<Long> markDelete() {
        try {
            BmsCategoryContentRecycleDto args = getArgs(BmsCategoryContentRecycleDto.class);
            List<Long> ids = args.getIds();
            if (ObjectUtil.isEmpty(ids)) {
                log.warn("BmsCategoryContentRecycleDto为空");
                return null;
            }
            //更新
            LambdaUpdateWrapper<BmsCategoryContent> updateWrapper = Wrappers.lambdaUpdate(BmsCategoryContent.class)
                    .set(BmsCategoryContent::getStatus, StatusEnum.DELETE.getCode())
                    //where 将回收中的 但状态还没有被标记的进行标记删除
                    .in(BmsCategoryContent::getPublishStatus, RecycleStatusRule.CAN_MARK_DELETE_LIST)
                    .ne(BmsCategoryContent::getStatus, StatusEnum.DELETE.getCode())
                    .in(BmsCategoryContent::getId, ids);
            log.info("将栏目内容关系标记为待删除");
            this.update(updateWrapper);
        } catch (Exception e) {
            log.error(e.getMessage(), e);
        }
        return null;
    }


    @Override
    public boolean oneKeyRecycle() {
        //前置检查
        if (!preCheck()) {
            return false;
        }
        if (needDelete()) {
            log.info("删除栏目内容关系不需要回收的");
            deleteRemain();
        }
        //找到需要回收的
        List<BmsCategoryContent> list = findCanRecycleList();
        //根据sp分组
        try {
            Map<Long, List<BmsCategoryContent>> map = list.stream().collect(Collectors.groupingBy(BmsCategoryContent::getSpId));
            for (Map.Entry<Long, List<BmsCategoryContent>> item : map.entrySet()) {
                List<BmsCategoryContent> value = item.getValue();
                //调用回收接口
                recycleHandler(value);
            }
        } catch (RuntimeException e) {
            log.error(e.getMessage(), e);
            throw e;
        } finally {
            if (needDelete()) {
                //标记删除
                markDelete();
            }
            clearArgs(BmsCategoryContentRecycleDto.class);
            if (isBreak(BmsCategoryContentRecycleDto.class)) {
                //如果只回收自己则进行清除防止内存残留
                closeRecycle();
            }
        }
        return true;
    }

    @Override
    public CommonResponse<Boolean> priortyPublish(List<Long> ids, Map<String, OutParamExpand> paramMap) {

        boolean result = this.publish(ids, null, paramMap);
        return result ? CommonResponse.success(result) : CommonResponse.commonfail("发布失败");
    }

    /**
     * 关系校验
     * @param ids
     */
    private void relaCheck(List<Long> ids){
        //step1: 查询 栏目内容关系
        List<BmsCategoryContent> list = this.getBaseMapper().selectBatchIds(ids);
        //找出栏目和内容id
        List<Long> categoryIds = list.stream().map(BmsCategoryContent::getCategoryId).collect(Collectors.toList());
        List<Long> contentIds = list.stream().map(BmsCategoryContent::getBmsContentId).collect(Collectors.toList());
        RuleCondition.create()
                //检查栏目和内容是否有锁定状态
                .and(LockStatusRule.init(BmsContent.class).data(contentIds))
                .and(LockStatusRule.init(BmsCategory.class).data(categoryIds))
                //检查栏目的发布状态 是否是发布成功
                .and(PublishStatusRule.init(BmsCategory.class).data(categoryIds).policy(PUBLISH_SUCCESS))
                //检查内容的发布状态 是否是发布成功
                .and(PublishStatusRule.init(BmsContent.class).data(contentIds).policy(PUBLISH_SUCCESS))
                //检查关系是否都是 待发布 检查
                .and(PublishStatusRule.init(BmsCategoryContent.class).data(ids).col(BmsCategoryContent::getCategoryName).policy(CAN_PUBLISH))
                //不是定时发布才进行 检测是否有定时发布
                //.and(!isSchedule, TimePublishCheckRule.init(BmsCategoryContent.class).data(ids))
                .execute().check();
    }
}
