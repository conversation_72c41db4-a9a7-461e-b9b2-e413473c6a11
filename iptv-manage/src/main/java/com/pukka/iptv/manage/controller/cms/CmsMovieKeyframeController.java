package com.pukka.iptv.manage.controller.cms;

import com.pukka.iptv.common.base.vo.CommonResponse;
import com.pukka.iptv.common.data.group.FrameGroup;
import com.pukka.iptv.common.data.vo.*;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.pukka.iptv.common.data.model.cms.CmsMovie;
import com.pukka.iptv.common.data.model.cms.CmsMovieKeyframe;

import javax.validation.Valid;

import com.pukka.iptv.manage.service.cms.CmsMovieKeyframeService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.http.MediaType;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.beans.factory.annotation.Autowired;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.AllArgsConstructor;
import org.springframework.web.multipart.MultipartFile;

/**
 * 视频关键帧
 *
 * @author: zhoul
 * @date: 2021-8-30 10:31:39
 */

@RestController
@AllArgsConstructor
@RequestMapping(value = "/cmsMovieKeyframe", produces = MediaType.APPLICATION_JSON_VALUE)
@Api(tags="cmsMovieKeyframe管理")
public class CmsMovieKeyframeController {

    @Autowired
    private CmsMovieKeyframeService cmsMovieKeyframeService;

    @ApiOperation(value = "分页")
    @GetMapping("/page" )
    public CommonResponse<Page> page(@Valid Page page, CmsMovieKeyframe cmsMovieKeyframe) {
        return  CommonResponse.success(cmsMovieKeyframeService.page(page, Wrappers.query(cmsMovieKeyframe)));
    }
    @ApiOperation(value = "视频关键帧分页查询")
    @GetMapping("/listById" )
    public CommonResponse<IPage<CmsMovieKeyframe>> listById(@Valid Page page, @Validated(FrameGroup.class) CmsMovie cmsMovie) {
        return  CommonResponse.success(cmsMovieKeyframeService.listById(page, cmsMovie));
    }

    @ApiOperation(value = "详情",hidden = true)
    @GetMapping("/getByMovieId")
    public CommonResponse<CmsMovieKeyframe> getByMovied(@Valid @RequestParam(name = "id", required = true)  Long id) {
        return CommonResponse.success(cmsMovieKeyframeService.getById(id));
    }
    @ApiOperation(value = "播放器截图上报接口")
    @PostMapping("/save")
    public CommonResponse<Boolean> save(@Valid @RequestBody CmsMovieKeyframe cmsMovieKeyframe) {
        return  CommonResponse.success(cmsMovieKeyframeService.save(cmsMovieKeyframe));
    }

    @ApiOperation(value = "修改",hidden = true)
    @PutMapping("/update")
    public CommonResponse<Boolean> updateById(@Valid @RequestBody CmsMovieKeyframe cmsMovieKeyframe) {
        return CommonResponse.success(cmsMovieKeyframeService.updateById(cmsMovieKeyframe));
    }

    @ApiOperation(value = "删除",hidden = true)
    @DeleteMapping("/deleteById" )
    public CommonResponse<Boolean> deleteById(@Valid @RequestParam(name = "id", required = true) Long id) {
        return  CommonResponse.success(cmsMovieKeyframeService.removeById(id));
    }

    @ApiOperation(value = "批量删除",hidden = true)
    @DeleteMapping("/deleteByIds" )
    public CommonResponse<Boolean> deleteByIds(@Valid @RequestBody IdList idList) {
        return  CommonResponse.success(cmsMovieKeyframeService.removeByIds(idList.getIds()));
    }

    @ApiOperation(value = "上次播放器截图")
    @PostMapping("/uploadImg" )
    public CommonResponse uploadImg(@Valid @RequestParam("file") MultipartFile file,@Valid @RequestParam("cpId") Long cpId ) {
        return  CommonResponse.success(cmsMovieKeyframeService.uploadImg(file,cpId));
    }

}
