package com.pukka.iptv.manage.util.copyInfoUtils;

import cn.hutool.core.date.DateTime;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.pukka.iptv.common.base.enums.ContentTypeEnum;
import com.pukka.iptv.common.base.enums.ProhibitStatusEnum;
import com.pukka.iptv.common.base.enums.SerachNameEnums;
import com.pukka.iptv.common.base.enums.SeriesFlagEnum;
import com.pukka.iptv.common.core.util.DateUtils;
import com.pukka.iptv.common.data.model.cms.CmsProgram;
import com.pukka.iptv.common.data.model.cms.CmsSeries;
import com.pukka.iptv.common.data.model.copyright.CopyrightInfo;
import com.pukka.iptv.common.data.model.copyright.CopyrightWrapperResponse;
import com.pukka.iptv.common.data.vo.req.CopyrightInfoReq;
import com.pukka.iptv.manage.mapper.cms.CmsProgramMapper;
import com.pukka.iptv.manage.mapper.cms.CmsSeriesMapper;
import com.pukka.iptv.manage.mapper.copyright.copyright.CopyrightInfoMapper;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Set;

/**
 * @Author: chiron
 * @Date: 2022/08/22/09:03
 * @Description: wrapper构建类
 */
@Slf4j
@Component
public class StructureUtil {

    @Autowired
    private CmsSeriesMapper cmsSeriesMapper;
    @Autowired
    private CmsProgramMapper cmsProgramMapper;
    @Autowired
    private CopyrightInfoMapper copyrightInfoMapper;

    /**
     * 单集分页查询
     *
     * @param req
     * @param status
     * @param nameArray
     * @param current
     * @param size
     * @param cpIdSet
     * @return
     */
    public CopyrightWrapperResponse getCopyrightProgramInfoList(CopyrightInfoReq req, Integer status, List<String> nameArray, long current, long size, Set<Long> cpIdSet) {
        CopyrightWrapperResponse copyrightWrapperResponse = new CopyrightWrapperResponse();
        try {
            LambdaQueryWrapper<CmsProgram> wrapper = Wrappers.lambdaQuery(CmsProgram.class)
                    .eq(CmsProgram::getSeriesFlag, SeriesFlagEnum.SimpleSet.getValue())
                    .eq(CmsProgram::getProhibitStatus, ProhibitStatusEnum.NO.getValue())
                    .eq(ObjectUtils.isNotEmpty(req.getPgmCategoryId()), CmsProgram::getPgmCategoryId, req.getPgmCategoryId())
                    .eq(ObjectUtils.isNotEmpty(req.getReleaseYear()), CmsProgram::getReleaseYear, req.getReleaseYear())
                    .eq(ObjectUtils.isNotEmpty(req.getApproval()), CmsProgram::getApproval, req.getApproval())
                    .eq(ObjectUtils.isNotEmpty(req.getCpId()) && 0L != req.getCpId(), CmsProgram::getCpId, req.getCpId())
                    .eq(ObjectUtils.isNotEmpty(req.getOriginalCountryId()), CmsProgram::getOriginalCountryId, req.getOriginalCountryId())
                    .in(ObjectUtils.isNotEmpty(cpIdSet),CmsProgram::getCpId,cpIdSet)
                    .orderByAsc(CmsProgram::getLicensingWindowEnd);
            //填充名称
            if (CollectionUtils.isNotEmpty(nameArray) && SerachNameEnums.ExactMatch.getValue().equals(status)) {
                wrapper.in(CmsProgram::getName, nameArray);
            } else if (CollectionUtils.isNotEmpty(nameArray) && SerachNameEnums.FuzzyMatch.getValue().equals(status)) {
                wrapper.like(CmsProgram::getName, nameArray.get(0));
            }
            if (ObjectUtils.isNotEmpty(req.getActorName())) {
                List<Long> ids = copyrightInfoMapper.getProgramIdsByActorName(req.getActorName());
                wrapper.in(CmsProgram::getId, ids);
            }
            if (ObjectUtils.isNotEmpty(req.getLastCopyrightDay())) {
                if (req.getLastCopyrightDay() > 0) {
                    wrapper.apply("datediff(licensing_window_end,now()) between 0 and {0}", req.getLastCopyrightDay());
                } else {
                    wrapper.apply("datediff(licensing_window_end,now()) < {0}", req.getLastCopyrightDay());
                }
            }
            if (ObjectUtils.isNotEmpty(req.getAuthorizationEndTime())) {
                wrapper.apply("DATE_FORMAT(licensing_window_end, '%Y-%m-%d') = {0}", req.getAuthorizationEndTime());
            }
            long total = cmsProgramMapper.selectCount(wrapper);
            wrapper.select(CmsProgram::getId, CmsProgram::getCode, CmsProgram::getName, CmsProgram::getCpId,
                            CmsProgram::getCpName, CmsProgram::getContentProvider, CmsProgram::getDirector,
                            CmsProgram::getKpeople, CmsProgram::getApproval, CmsProgram::getCopyRight,
                            CmsProgram::getPublisher, CmsProgram::getLicensingWindowStart, CmsProgram::getLicensingWindowEnd,
                            CmsProgram::getPgmCategoryId, CmsProgram::getPgmCategory, CmsProgram::getReleaseYear,
                            CmsProgram::getOriginalCountryId, CmsProgram::getOriginalCountry, CmsProgram::getCreateTime, CmsProgram::getUpdateTime)
                    .last(String.format("limit %s,%s", current, size));
            List<CmsProgram> cmsProgramList = cmsProgramMapper.selectList(wrapper);
            List<CopyrightInfo> copyrightInfoList = transProgram2CopyrightInfo(cmsProgramList);
            copyrightWrapperResponse.setCopyrightInfoList(copyrightInfoList).setTotal(total);
        } catch (Exception exception) {
            log.error("单集分页查询 -----> 失败,错误信息:{}", exception);
        }
        return copyrightWrapperResponse;
    }

    /**
     * 剧集分页查询
     *
     * @param req
     * @param status
     * @param nameArray
     * @param current
     * @param size
     * @param cpIdSet
     * @return
     */
    public CopyrightWrapperResponse getCopyrightSeriesInfoList(CopyrightInfoReq req, Integer status, List<String> nameArray, long current, long size, Set<Long> cpIdSet) {
        CopyrightWrapperResponse copyrightWrapperResponse = new CopyrightWrapperResponse();
        try {
            LambdaQueryWrapper<CmsSeries> wrapper = Wrappers.lambdaQuery(CmsSeries.class)
                    .eq(CmsSeries::getProhibitStatus, ProhibitStatusEnum.NO.getValue())
                    .eq(ObjectUtils.isNotEmpty(req.getPgmCategoryId()), CmsSeries::getPgmCategoryId, req.getPgmCategoryId())
                    .eq(ObjectUtils.isNotEmpty(req.getReleaseYear()), CmsSeries::getReleaseYear, req.getReleaseYear())
                    .eq(ObjectUtils.isNotEmpty(req.getApproval()), CmsSeries::getApproval, req.getApproval())
                    .eq(ObjectUtils.isNotEmpty(req.getCpId()) && 0L != req.getCpId(), CmsSeries::getCpId, req.getCpId())
                    .eq(ObjectUtils.isNotEmpty(req.getOriginalCountryId()), CmsSeries::getOriginalCountryId, req.getOriginalCountryId())
                    .in(ObjectUtils.isNotEmpty(cpIdSet),CmsSeries::getCpId,cpIdSet)
                    .orderByAsc(CmsSeries::getLicensingWindowEnd);
            //填充名称
            if (CollectionUtils.isNotEmpty(nameArray) && SerachNameEnums.ExactMatch.getValue().equals(status)) {
                wrapper.in(CmsSeries::getName, nameArray);
            } else if (CollectionUtils.isNotEmpty(nameArray) && SerachNameEnums.FuzzyMatch.getValue().equals(status)) {
                wrapper.like(CmsSeries::getName, nameArray.get(0));
            }
            if (ObjectUtils.isNotEmpty(req.getActorName())) {
                List<Long> ids = copyrightInfoMapper.getSeriesIdsByActorName(req.getActorName());
                wrapper.in(CmsSeries::getId, ids);
            }
            if (ObjectUtils.isNotEmpty(req.getLastCopyrightDay())) {
                if (req.getLastCopyrightDay() > 0) {
                    wrapper.apply("datediff(licensing_window_end,now()) between 0 and {0}", req.getLastCopyrightDay());
                } else {
                    wrapper.apply("datediff(licensing_window_end,now()) < {0}", req.getLastCopyrightDay());
                }
            }
            if (ObjectUtils.isNotEmpty(req.getAuthorizationEndTime())) {
                wrapper.apply("DATE_FORMAT(licensing_window_end, '%Y-%m-%d') = {0}", req.getAuthorizationEndTime());
            }
            long total = cmsSeriesMapper.selectCount(wrapper);
            wrapper.select(CmsSeries::getId, CmsSeries::getCode, CmsSeries::getName, CmsSeries::getCpId,
                            CmsSeries::getCpName, CmsSeries::getContentProvider, CmsSeries::getDirector,
                            CmsSeries::getKpeople, CmsSeries::getApproval, CmsSeries::getCopyRight,
                            CmsSeries::getPublisher, CmsSeries::getLicensingWindowStart, CmsSeries::getLicensingWindowEnd,
                            CmsSeries::getPgmCategoryId, CmsSeries::getPgmCategory, CmsSeries::getReleaseYear,CmsSeries::getVolumnCount,
                            CmsSeries::getOriginalCountryId, CmsSeries::getOriginalCountry, CmsSeries::getCreateTime, CmsSeries::getUpdateTime)
                    .last(String.format("limit %s,%s", current, size));
            List<CmsSeries> cmsSeriesList = cmsSeriesMapper.selectList(wrapper);
            List<CopyrightInfo> copyrightInfoList = transSeries2CopyrightInfo(cmsSeriesList);
            copyrightWrapperResponse.setCopyrightInfoList(copyrightInfoList).setTotal(total);
        } catch (Exception exception) {
            log.error("剧集分页查询 -----> 失败,错误信息:{}", exception);
        }
        return copyrightWrapperResponse;
    }

    /**
     * 转换单集
     *
     * @return
     */
    public List<CopyrightInfo> transProgram2CopyrightInfo(List<CmsProgram> cmsProgramList) {
        List<CopyrightInfo> copyrightInfoList = new ArrayList<>();
        cmsProgramList.forEach(cmsProgram -> {
            CopyrightInfo copyrightInfo = new CopyrightInfo();
            BeanUtils.copyProperties(cmsProgram, copyrightInfo);
            copyrightInfo.setContentType(ContentTypeEnum.FILM.getValue())
                    .setDisplayAsLastChance(getDateDiff((cmsProgram.getLicensingWindowEnd())));
            copyrightInfoList.add(copyrightInfo);
        });
        return copyrightInfoList;
    }

    /**
     * 转换剧集
     *
     * @return
     */
    public List<CopyrightInfo> transSeries2CopyrightInfo(List<CmsSeries> cmsSeriesList) {
        List<CopyrightInfo> copyrightInfoList = new ArrayList<>();
        cmsSeriesList.forEach(cmsSeries -> {
            CopyrightInfo copyrightInfo = new CopyrightInfo();
            BeanUtils.copyProperties(cmsSeries, copyrightInfo);
            copyrightInfo.setContentType(ContentTypeEnum.TELEPLAY.getValue())
                    .setDisplayAsLastChance(getDateDiff((cmsSeries.getLicensingWindowEnd())));
            copyrightInfoList.add(copyrightInfo);
        });
        return copyrightInfoList;
    }

    /**
     * 计算两个时间差(天)
     */
    public Long getDateDiff(String dateStr) {
        Long day = 0L;
        if (ObjectUtils.isEmpty(dateStr)) {
            return null;
        }
        try {
            Date date = new SimpleDateFormat(DateUtils.YYYYMMDDHHMMSS).parse(dateStr);
            SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd");
            long startDateTime = dateFormat.parse(dateFormat.format(DateTime.now())).getTime();
            long endDateTime = dateFormat.parse(dateFormat.format(date)).getTime();
            day = ((endDateTime - startDateTime) / (1000 * 3600 * 24));
        } catch (Exception exception) {
            log.error("授权信息导出 -----> 到期时间计算，计算时间差:{} 错误，错误信息:{}", dateStr, exception);
        }
        return day;
    }
}
