package com.pukka.iptv.manage.service.sys;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.pukka.iptv.common.data.model.cms.CmsProgram;
import com.pukka.iptv.common.data.model.cms.CmsSeries;
import com.pukka.iptv.common.data.model.sys.SysDictionaryItem;

import java.util.Date;
import java.util.List;

/**
 *
 * @author: zhengcl
 * @date: 2021-9-13 17:59:15
 */

public interface SysDictionaryItemService extends IService<SysDictionaryItem> {

    Page<SysDictionaryItem> page(Page<SysDictionaryItem> page, SysDictionaryItem sysDictionaryItem);

    /**
     * 通过字典code查询子项
     *
     * @param code
     * @return
     */
    List<SysDictionaryItem> getByCode(String code);

    /**
     * 通过id集合删除
     *
     * @param ids
     * @return
     */
    boolean removeByIds(List<Long> ids,String code);

    /**
     * 查询平台标识
     */
    SysDictionaryItem getPlatformIdentification();

    boolean removeCacheAndDB(Long id);

    boolean saveCacheAndDB(SysDictionaryItem sysDictionaryItem);

    boolean updateCacheAndDB(SysDictionaryItem sysDictionaryItem);

    void setDictionaryId(CmsProgram cmsProgram);

    void setDictionaryId(CmsSeries cmsSeries);

    boolean isItTimeout(Date publishTime);
}


