package com.pukka.iptv.manage.service.cache;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.pukka.iptv.common.api.feign.in.InOrderFeignClient;
import com.pukka.iptv.common.base.constant.RedisKeyConstants;
import com.pukka.iptv.common.base.enums.ContentStatusEnum;
import com.pukka.iptv.common.base.enums.StatusEnum;
import com.pukka.iptv.common.base.vo.CommonResponse;
import com.pukka.iptv.common.core.constant.SymbolConstant;
import com.pukka.iptv.common.data.model.OutOrderBase;
import com.pukka.iptv.common.data.model.cms.CmsDownload;
import com.pukka.iptv.common.data.model.copyright.RuleProhibit;
import com.pukka.iptv.common.data.model.sys.*;
import com.pukka.iptv.common.data.vo.sys.SysOutpassageStorageRelaVO;
import com.pukka.iptv.common.redis.service.RedisService;
import com.pukka.iptv.manage.service.cms.CmsDownloadService;
import com.pukka.iptv.manage.service.sys.OutOrderBaseService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Map;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @Date 2021/10/15 11:24
 * @Description
 */
@Slf4j
@Component
public class CacheServiceImpl {

    @Autowired
    private RedisService redisService;

    public boolean cacheOutPassage(List<SysOutPassage> sysOutPassageList) {
        try {
            Map<String, SysOutPassage> outPassageMap = sysOutPassageList.stream()
                    .collect(Collectors.toMap(outPassage -> String.valueOf(outPassage.getId()), outPassage -> outPassage, (val1, val2) -> val2));
            redisService.setCacheMap(RedisKeyConstants.SYS_OUT_PASSAGE, outPassageMap);
            return true;
        } catch (Exception exception) {
            log.error("redis插入SysOutPassage失败，", exception);
            return false;
        }
    }

    public boolean cacheSp(List<SysSp> sysSpList) {
        try {
            Map<String, SysSp> sysSpMap = sysSpList.stream()
                    .collect(Collectors.toMap(sysSp -> String.valueOf(sysSp.getId()), sysSp -> sysSp, (val1, val2) -> val2));
            redisService.setCacheMap(RedisKeyConstants.SYS_SP, sysSpMap);
            return true;
        } catch (Exception exception) {
            log.error("redis插入SysSp失败，", exception);
            return false;
        }
    }

    public boolean cacheAuthorization(List<SysAuthorization> authorizationList) {
        //获取数据库表对象
        try {
            Map<String, List<SysAuthorization>> authorizationMap = authorizationList.stream()
                    .filter(sa -> ((StatusEnum.COME.getCode().equals(sa.getStatus()))
                            && ((redisService.getCacheMapValue(RedisKeyConstants.SYS_SP, String.valueOf(sa.getSpId())) != null)))
                    )
                    .collect(Collectors.groupingBy(authorization -> String.valueOf(authorization.getCpId())));
            redisService.setCacheMap(RedisKeyConstants.ALL_SYS_AUTHORIZATION, authorizationMap);
            return true;
        } catch (Exception exception) {
            log.error("redis插入SysAuthorization失败，", exception);
            return false;
        }
    }

    public boolean cacheCp(List<SysCp> cpList) {
        try {
            Map<String, SysCp> cpMap = cpList.stream()
                    .collect(Collectors.toMap(cp -> String.valueOf(cp.getId()), cp -> cp, (val1, val2) -> val2));
            Map<String, SysCp> cpMapByCode = cpList.stream()
                    .collect(Collectors.toMap(cp -> String.valueOf(cp.getCode()), cp -> cp, (val1, val2) -> val2));
            redisService.setCacheMap(RedisKeyConstants.SYS_CP, cpMap);
            redisService.setCacheMap(RedisKeyConstants.SYS_CP_CODE, cpMapByCode);
            return true;
        } catch (Exception exception) {
            log.error("redis插入SysCp失败，", exception);
            return false;
        }
    }

    public boolean cacheInPassage(List<SysInPassage> sysInPassageList) {
        try {
            Map<String, SysInPassage> inPassageMap = sysInPassageList.stream()
                    .collect(Collectors.toMap(SysInPassage::getCspId, sysInPassage -> sysInPassage, (val1, val2) -> val2));
            redisService.setCacheMap(RedisKeyConstants.SYS_IN_PASSAGE, inPassageMap);
            return true;
        } catch (Exception exception) {
            log.error("redis插入SysInPassage失败，", exception);
            return false;
        }
    }

    public boolean cacheStorage(List<SysStorage> sysStorageList) {
        try {
            Map<String, SysStorage> storageMap = sysStorageList.stream()
                    .collect(Collectors.toMap(sysStorage -> String.valueOf(sysStorage.getId()), sysStorage -> sysStorage, (val1, val2) -> val2));
            redisService.setCacheMap(RedisKeyConstants.SYS_STORAGE_KEY, storageMap);
            return true;
        } catch (Exception exception) {
            log.error("redis插入SysStorage失败，", exception);
            return false;
        }
    }

    public boolean cacheStorageDirctory(List<SysStorageDirctory> sysStorageDirctoryList) {
        try {
            Map<String, SysStorageDirctory> storageDirctoryMap = sysStorageDirctoryList.stream()
                    .collect(Collectors.toMap(sysStorageDirctory ->
                                    StringUtils.joinWith(":", sysStorageDirctory.getStorageId(), sysStorageDirctory.getType(), sysStorageDirctory.getAuthorityType()),
                            sysStorage -> sysStorage, (val1, val2) -> val2));
            redisService.setCacheMap(RedisKeyConstants.SYS_STORAGE_DIRCTORY_KEY, storageDirctoryMap);
            return true;
        } catch (Exception exception) {
            log.error("redis插入SysStorageDirctory失败，", exception);
            return false;
        }
    }

    public boolean cacheDictionaryBase(List<SysDictionaryBase> sysDictionaryBaseList) {
        try {
            Map<String, SysDictionaryBase> dictionaryBaseMap = sysDictionaryBaseList.stream()
                    .collect(Collectors.toMap(SysDictionaryBase::getName, sysDictionaryBase -> sysDictionaryBase, (val1, val2) -> val2));
            redisService.setCacheMap(RedisKeyConstants.SYS_DICTIONARY_BASE_KEY, dictionaryBaseMap);
            return true;
        } catch (Exception exception) {
            log.error("redis插入SysDictionaryBase失败，", exception);
            return false;
        }
    }

    public boolean cacheDictionaryItem(List<SysDictionaryItem> sysDictionaryItemList) {
        try {
            Map<String, SysDictionaryItem> dictionaryItemMap = sysDictionaryItemList.stream()
                    .collect(Collectors.toMap(sysDictionaryItem ->
                                    StringUtils.joinWith(":", sysDictionaryItem.getDictionaryBaseId(), sysDictionaryItem.getName()),
                            sysDictionaryBase -> sysDictionaryBase, (val1, val2) -> val2));
            redisService.setCacheMap(RedisKeyConstants.SYS_DICTIONARY_ITEM_KEY, dictionaryItemMap);
            return true;
        } catch (Exception exception) {
            log.error("redis插入SysDictionaryItem失败，", exception);
            return false;
        }
    }

    public boolean cachePlatformIdentification(SysDictionaryItem sysDictionaryItem) {
        try {
            redisService.setCacheObject(RedisKeyConstants.SYS_DICTIONARY_ITEM_PLATFORM_IDENTIFICATION_KEY, sysDictionaryItem);
            return true;
        } catch (Exception exception) {
            log.error("redis插入SysDictionaryItem失败，", exception);
            return false;
        }
    }

    public boolean setSysNat(List<SysNat> sysNatList) {
        try {
            Map<String, SysNat> dictionaryBaseMap = sysNatList.stream().filter(sysNat -> sysNat.getStatus().equals(ContentStatusEnum.EFFECTIVE.getCode())).collect(Collectors.toMap(sysNat -> sysNat.getNatSource(), sysNat -> sysNat, (a, b) -> b));
            redisService.setCacheMap(RedisKeyConstants.SYS_NAT_KEY, dictionaryBaseMap);
            return true;
        } catch (Exception exception) {
            log.error("redis插入SysNat失败，", exception);
            return false;
        }
    }

    public boolean cacheRuleCache(List<RuleProhibit> ruleProhibitList) {
        try {
            Map<String, RuleProhibit> ruleProhibitMap = ruleProhibitList.stream()
                    .collect(Collectors.toMap(ruleProhibit -> String.valueOf(ruleProhibit.getCode()), ruleProhibit -> ruleProhibit, (val1, val2) -> val2));
            redisService.setCacheMap(RedisKeyConstants.RULE_CACHE, ruleProhibitMap);
            return true;
        } catch (Exception exception) {
            log.error("redis插入ruleProhibit失败，", exception);
            return false;
        }
    }

    public boolean setSysOutpassageStorageRela(List<SysOutpassageStorageRelaVO> sysOutpassageStorageRelaVOList) {
        try {
            Map<String, SysOutpassageStorageRelaVO> storageRelaVOMap = sysOutpassageStorageRelaVOList.stream()
                    .collect(Collectors.toMap(sysOutpassageStorageRelaVO ->
                                    sysOutpassageStorageRelaVO.getOutPassageId()
                                            + SymbolConstant.UNDER_SCORE
                                            + sysOutpassageStorageRelaVO.getStorageId(),
                            sysOutpassageStorageRelaVO -> sysOutpassageStorageRelaVO, (val1, val2) -> val2));
            redisService.setCacheMap(RedisKeyConstants.Sys_Outpassage_Storage_Rela_VO_CACHE, storageRelaVOMap);
            return true;
        } catch (Exception exception) {
            log.error("redis插入sysOutpassageStorageRela失败，", exception);
            return false;
        }
    }

    public boolean cacheSysUser(List<SysUser> sysUserList) {
        try{
            Map<String, SysUser> sysUserMap = sysUserList.stream()
                    .collect(Collectors.toMap(sysUser -> String.valueOf(sysUser.getId()), sysUser -> sysUser, (val1, val2) -> val2));
            redisService.setCacheMap(RedisKeyConstants.SYS_USER, sysUserMap);
            return true;
        }catch (Exception exception){
            log.error("redis插入SysUser失败，", exception);
            return false;
        }
    }
}
