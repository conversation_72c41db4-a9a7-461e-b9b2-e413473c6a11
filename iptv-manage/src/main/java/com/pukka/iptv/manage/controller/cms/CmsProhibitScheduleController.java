package com.pukka.iptv.manage.controller.cms;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.pukka.iptv.common.base.vo.CommonResponse;
import com.pukka.iptv.common.data.model.cms.CmsProhibitSchedule;
import com.pukka.iptv.common.data.model.cms.CmsProhibitScheduleDTO;
import com.pukka.iptv.common.data.vo.IdList;
import com.pukka.iptv.manage.service.cms.CmsProhibitScheduleService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.util.Assert;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;
import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;

/**
 * <AUTHOR>
 * @Title: CmsProhibitScheduleController
 * @ProjectName iptv-cloud
 * @date 16/11/2023 3:27 pm
 */

@Slf4j
@RestController
@RequestMapping("/cmsProhibitSchedule")
public class CmsProhibitScheduleController {

    /**
     * 查询列表
     *
     * @param page
     * @param cmsProhibitScheduleDTO
     * @return
     */
    @GetMapping
    public CommonResponse<IPage<CmsProhibitSchedule>> list(Page page, CmsProhibitScheduleDTO cmsProhibitScheduleDTO) {
        return CommonResponse.success(cmsProhibitScheduleService.selectList(page, cmsProhibitScheduleDTO));
    }

    /**
     * 导出
     *
     * @param list
     * @param response
     * @return
     */
    @PostMapping("/export")
    public CommonResponse export(@RequestBody IdList list, HttpServletResponse response) {
        Assert.notNull(list.getIds(),"ids不可为空");
        return CommonResponse.success(cmsProhibitScheduleService.exportList(list, response));
    }

    /**
     * 删除
     *
     * @param list
     * @return
     */
    @DeleteMapping
    public CommonResponse delete(@RequestBody IdList list) {
        cmsProhibitScheduleService.delete(list);
        return CommonResponse.success(true);
    }

    /**
     * 新增
     *
     * @param cmsProhibitSchedules
     * @return
     */
    @PostMapping("/save")
    public CommonResponse save(@RequestBody List<CmsProhibitSchedule> cmsProhibitSchedules) {
        cmsProhibitScheduleService.save(cmsProhibitSchedules);
        return CommonResponse.success(true);
    }

    @Resource
    private CmsProhibitScheduleService cmsProhibitScheduleService;
}
