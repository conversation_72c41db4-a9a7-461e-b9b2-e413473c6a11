package com.pukka.iptv.manage.service.sys;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.pukka.iptv.common.data.model.OutOrderItem;
import com.pukka.iptv.common.data.vo.sys.OutOrderItemRepublish;
import com.pukka.iptv.common.data.vo.sys.OutOrderItemVo;

import java.util.List;

/**
 * <AUTHOR>
 * @Date 2021/9/9 10:19 上午
 * @description: 子工单
 * @Version 1.0
 */
public interface OutOrderItemService extends IService<OutOrderItem> {
    /**
     * 子工单重新发布
     * @param outOrderItemRepublish
     * @return
     */
    boolean orderRepublish(OutOrderItemRepublish outOrderItemRepublish);

    /**
     * 更新重新发布失败状态
     * @param outOrderItemVo
     * @return
     */
    Boolean getReportEntity(OutOrderItemVo outOrderItemVo);

    /**
     * 子工单信息查询
     * @param page
     * @param outOrderItemVo
     * @param startTime
     * @param endTime
     * @return
     */
    IPage<OutOrderItemVo> listByOutOrderItemProperty(IPage<OutOrderItem> page, OutOrderItemVo outOrderItemVo, String startTime, String endTime);

    /**
     * 通过id，type查询子工单的contentId,contentCode
     * @param contentIds
     * @param contentType
     * @return
     */
    List<OutOrderItem> getByIdType(String contentIds, Integer contentType);
    /**
     * 获取id,code
     * @param contentId
     * @return
     */
    OutOrderItemVo getBmsContentCodeAndName(String contentId, Integer contentType);

    /**
     * 更新子工单
     * @param outOrderItemVo
     * @return
     */
    Boolean updateResult(OutOrderItemVo outOrderItemVo);

    /**
     * 查询主工单结果
     * @param outOrderItem
     * @return
     */
    Boolean selectBaseResult(OutOrderItemVo outOrderItem);
}
