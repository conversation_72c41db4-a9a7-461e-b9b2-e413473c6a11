package com.pukka.iptv.manage.service.bms.impl;

import static com.pukka.iptv.manage.service.condition.rule.enums.PublishCheck.CAN_PUBLISH;
import static com.pukka.iptv.manage.service.condition.rule.enums.PublishCheck.ING;
import static com.pukka.iptv.manage.service.condition.rule.enums.PublishCheck.MUST_ING;
import static com.pukka.iptv.manage.service.condition.rule.enums.PublishCheck.PUBLISH_SUCCESS;
import static com.pukka.iptv.manage.service.condition.rule.enums.RecycleCheck.CAN_NOT_RECYCLE;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.metadata.TableInfo;
import com.baomidou.mybatisplus.core.metadata.TableInfoHelper;
import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.pukka.iptv.common.base.constant.PublishParamTypeConstants;
import com.pukka.iptv.common.base.constant.RedisKeyConstants;
import com.pukka.iptv.common.base.enums.ActionEnums;
import com.pukka.iptv.common.base.enums.ContentTypeEnum;
import com.pukka.iptv.common.base.enums.ContentTypeItemEnum;
import com.pukka.iptv.common.base.enums.IsTimedEnums;
import com.pukka.iptv.common.base.enums.LonelyContentEnum;
import com.pukka.iptv.common.base.enums.OpCheckStatusEnum;
import com.pukka.iptv.common.base.enums.PriorityEnums;
import com.pukka.iptv.common.base.enums.PublishStatusEnum;
import com.pukka.iptv.common.base.enums.StatisticsMediaMQEnum;
import com.pukka.iptv.common.base.enums.StatisticsTypeEnum;
import com.pukka.iptv.common.base.enums.StatusEnum;
import com.pukka.iptv.common.base.enums.SysUserTypeEnum;
import com.pukka.iptv.common.base.exception.BizException;
import com.pukka.iptv.common.base.exception.CommonResponseException;
import com.pukka.iptv.common.base.util.JwtTokenUtil;
import com.pukka.iptv.common.base.vo.CommonResponse;
import com.pukka.iptv.common.base.vo.SecurityUser;
import com.pukka.iptv.common.core.constant.SymbolConstant;
import com.pukka.iptv.common.data.config.CommonConfig;
import com.pukka.iptv.common.data.dto.DateFormatCompletionDto;
import com.pukka.iptv.common.data.dto.OutSpDto;
import com.pukka.iptv.common.data.model.OutParamExpand;
import com.pukka.iptv.common.data.model.OutScheduledTask;
import com.pukka.iptv.common.data.model.bms.BmsCategory;
import com.pukka.iptv.common.data.model.bms.BmsCategoryContent;
import com.pukka.iptv.common.data.model.bms.BmsContent;
import com.pukka.iptv.common.data.model.bms.BmsPackage;
import com.pukka.iptv.common.data.model.bms.BmsPackageContent;
import com.pukka.iptv.common.data.model.bms.BmsPicture;
import com.pukka.iptv.common.data.model.bms.BmsProgram;
import com.pukka.iptv.common.data.model.cms.CmsMovie;
import com.pukka.iptv.common.data.model.cms.CmsPicture;
import com.pukka.iptv.common.data.model.cms.CmsProgram;
import com.pukka.iptv.common.data.model.cms.CmsResource;
import com.pukka.iptv.common.data.model.order.SubOrderObjectsEntity;
import com.pukka.iptv.common.data.model.statistics.StatisticsInDelete;
import com.pukka.iptv.common.data.model.sys.SysAuthorization;
import com.pukka.iptv.common.data.model.sys.SysSp;
import com.pukka.iptv.common.data.model.sys.SysTenant;
import com.pukka.iptv.common.data.vo.bms.CpFeedbackContentVO;
import com.pukka.iptv.common.data.vo.req.BmsContentQueryReq;
import com.pukka.iptv.common.data.vo.req.BmsContentQueryReqBySP;
import com.pukka.iptv.common.rabbitmq.config.StatisticsOnlineMQConfig;
import com.pukka.iptv.common.redis.service.RedisService;
import com.pukka.iptv.manage.config.BmsContentNacosConfig;
import com.pukka.iptv.manage.export.model.ExcelTaskInfo;
import com.pukka.iptv.manage.export.model.ExportInfo;
import com.pukka.iptv.manage.export.task.ExportTask;
import com.pukka.iptv.manage.mapper.bms.BmsBaseMapper;
import com.pukka.iptv.manage.mapper.bms.BmsContentMapper;
import com.pukka.iptv.manage.mapper.bms.BmsProgramMapper;
import com.pukka.iptv.manage.service.api.StatisticsInDeleteService;
import com.pukka.iptv.manage.service.bms.BmsCategoryContentService;
import com.pukka.iptv.manage.service.bms.BmsContentService;
import com.pukka.iptv.manage.service.bms.BmsPackageContentService;
import com.pukka.iptv.manage.service.bms.BmsPictureService;
import com.pukka.iptv.manage.service.bms.BmsProgramService;
import com.pukka.iptv.manage.service.bms.BmsSchedulePublishApi;
import com.pukka.iptv.manage.service.bms.common.LambdaTrans;
import com.pukka.iptv.manage.service.bms.common.WorkOrderOperation;
import com.pukka.iptv.manage.service.bms.dto.BmsCategoryContentRecycleDto;
import com.pukka.iptv.manage.service.bms.dto.BmsContentRecycleDto;
import com.pukka.iptv.manage.service.bms.dto.BmsPackageContentRecycleDto;
import com.pukka.iptv.manage.service.bms.dto.BmsPicRecycleDto;
import com.pukka.iptv.manage.service.bms.dto.BmsProgramRecycleDto;
import com.pukka.iptv.manage.service.cms.CmsMovieService;
import com.pukka.iptv.manage.service.cms.CmsPictureService;
import com.pukka.iptv.manage.service.cms.CmsProgramService;
import com.pukka.iptv.manage.service.cms.CmsResourceService;
import com.pukka.iptv.manage.service.common.FtpAnalysisUtil;
import com.pukka.iptv.manage.service.common.ScheduledTasksUtil;
import com.pukka.iptv.manage.service.condition.RuleCondition;
import com.pukka.iptv.manage.service.condition.RuleResult;
import com.pukka.iptv.manage.service.condition.rule.LockStatusRule;
import com.pukka.iptv.manage.service.condition.rule.MovieCheckRule;
import com.pukka.iptv.manage.service.condition.rule.OpCheckRule;
import com.pukka.iptv.manage.service.condition.rule.PublishStatusRule;
import com.pukka.iptv.manage.service.condition.rule.RecycleStatusRule;
import com.pukka.iptv.manage.service.condition.rule.SpCheckRule;
import com.pukka.iptv.manage.service.condition.rule.TimePublishCheckRule;
import com.pukka.iptv.manage.service.condition.rule.enums.PublishCheck;
import com.pukka.iptv.manage.service.sys.OutScheduledTasksService;
import com.pukka.iptv.manage.service.sys.SysAuthorizationService;
import com.pukka.iptv.manage.service.sys.SysTenantService;
import com.pukka.iptv.manage.util.CommonUtils;
import com.pukka.iptv.manage.util.DateUtils;
import com.pukka.iptv.manage.util.RuleUtil;
import com.pukka.iptv.manage.util.TimeResolutionUtil;
import java.io.ByteArrayOutputStream;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.Date;
import java.util.HashMap;
import java.util.HashSet;
import java.util.Iterator;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.concurrent.CountDownLatch;
import java.util.stream.Collectors;
import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.springframework.amqp.rabbit.core.RabbitTemplate;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;


/**
 * 内容表，不包含子集
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2021-08-27 10:20:57
 */

@Slf4j
@Service
public class BmsContentServiceImpl extends ServiceImpl<BmsContentMapper, BmsContent>
        implements BmsContentService, BmsSchedulePublishApi {

    @Autowired
    private SysTenantService sysTenantService;
    @Autowired
    private BmsCategoryContentService bmsCategoryContentService;


    @Autowired
    private CmsPictureService cmsPictureService;

    @Autowired
    private CmsResourceService cmsResourceService;

    @Autowired
    private CmsMovieService cmsMovieService;

    @Autowired
    private CmsProgramService cmsProgramService;

    @Autowired
    private BmsPackageContentService bmsPackageContentService;

    @Autowired
    private BmsProgramService bmsProgramService;

    @Autowired
    private BmsPictureService bmsPictureService;

    @Resource
    private BmsProgramMapper bmsProgramMapper;

    @Autowired
    private ExportTask<BmsContent> exportTask;
    @Autowired
    private WorkOrderOperation workOrderOperation;
    //执行器线程池
    @Resource(name = "schedulePublishThreadPool")
    private ThreadPoolTaskExecutor threadPoolTaskExecutor;

    @Autowired
    private CommonConfig config;

    @Autowired
    private BmsContentMapper bmsContentMapper;

    @Autowired
    private BmsContentNacosConfig nacosConfig;

    @Autowired
    private SysAuthorizationService sysAuthorizationService;


    @Autowired
    private StatisticsInDeleteService statisticsInDeleteService;

    @Autowired
    private RabbitTemplate rabbitTemplate;

    @Autowired
    private RedisService redisService;

    @Autowired
    private ScheduledTasksUtil scheduledTasksUtil;

    @Autowired
    private OutScheduledTasksService outScheduledTasksService;

    @Override
    public IPage pageList(BmsContentQueryReq req) {
        return bmsContentMapper.selectPage(req, initWrapper(req));
    }

    @Override
    public IPage pageListForSp(BmsContentQueryReq req) {
        BmsContent po = BeanUtil.copyProperties(req, BmsContent.class);
        //获取授权合同对应的sp
        LambdaQueryWrapper<SysAuthorization> wrapperSysAuthorization = Wrappers.lambdaQuery();
        wrapperSysAuthorization.eq(SysAuthorization::getCpId, po.getCpId());
        wrapperSysAuthorization.eq(SysAuthorization::getStatus, 1);
        List<SysAuthorization> sysAuthorizationList = sysAuthorizationService.list(
                wrapperSysAuthorization);
        List<Long> spId = sysAuthorizationList.stream().map(SysAuthorization::getSpId)
                .collect(Collectors.toList());
        po.setOutPassageIds(req.getOutPassageId());
        //开始、结束时间格式化
        DateFormatCompletionDto dateFormatCompletionDto = new DateFormatCompletionDto().setStartTime(
                req.getStartUpdateTime()).setEndTime(req.getEndUpdateTime());
        Boolean aBoolean = TimeResolutionUtil.dateTimeCompare(dateFormatCompletionDto);
        QueryWrapper<BmsContent> wrapperQuery = new QueryWrapper<BmsContent>();
        wrapperQuery.select(
                " id, code, name, licensing_window_start, licensing_window_end, pgm_category_id, pgm_category,"
                        +
                        "  pgm_snd_class_id, pgm_snd_class, status, content_provider, cp_id, cp_name, create_time, update_time, GROUP_CONCAT(sp_id  SEPARATOR ',') as sp_name,"
                        +
                        "  content_type, cp_check_status, cp_check_desc, cp_check_time,op_check_status, op_check_desc, op_check_time,"
                        +
                        "  source, lock_status, out_passage_ids, out_passage_names,publish_status, cms_content_code, cms_content_id,bms_sp_channel_name,"
                        +
                        "  bms_sp_channel_id, release_status, preview_status  ");

        wrapperQuery.lambda().notExists(
                        LonelyContentEnum.LONELY_CONTENT.getCode().equals(req.getLonelyContent()),
                        "select 1 from bms_category_content b where bms_content.id=b.bms_content_id")
                .notExists(
                        LonelyContentEnum.LONELY_CONTENT.getCode().equals(req.getLonelyContent()),
                        "select 1 from bms_package_content c where bms_content.id=c.bms_content_id");
        wrapperQuery.lambda().in(Objects.nonNull(spId), BmsContent::getSpId, spId);
        wrapperQuery.lambda().eq(Objects.nonNull(po.getCpId()), BmsContent::getCpId, po.getCpId());
        wrapperQuery.lambda()
                .eq(Objects.nonNull(po.getStatus()), BmsContent::getStatus, po.getStatus());
        wrapperQuery.lambda()
                .eq(Objects.nonNull(po.getPublishStatus()), BmsContent::getPublishStatus,
                        po.getPublishStatus());
        wrapperQuery.lambda()
                .eq(Objects.nonNull(po.getOpCheckStatus()), BmsContent::getOpCheckStatus,
                        po.getOpCheckStatus());
        wrapperQuery.lambda()
                .eq(StringUtils.isNotBlank(req.getCmsContentCode()), BmsContent::getCmsContentCode,
                        po.getCmsContentCode());
        wrapperQuery.lambda().ge(ObjectUtils.isNotEmpty(dateFormatCompletionDto.getStartTime()),
                BmsContent::getUpdateTime, dateFormatCompletionDto.getStartTime());
        wrapperQuery.lambda().le(ObjectUtils.isNotEmpty(dateFormatCompletionDto.getEndTime()),
                BmsContent::getUpdateTime, dateFormatCompletionDto.getEndTime());
        wrapperQuery.lambda().orderByDesc(BmsContent::getId);
        // 是否按别名查询
        if (org.springframework.util.StringUtils.hasText(req.getOriginalName())) {
            wrapperQuery.lambda().like(BmsContent::getOriginalName, req.getOriginalName());
        } else {
            //未换行，模糊查询,换行后精确查询
            //判断name是否含有回车 若含有回车则进行精准多条查询
            if (req.getName() != null) {
                if (req.getName().contains("\n")) {
                    //防止name为空字符串以及只有回车的情况  进行再一次校验
                    req.setName(req.getName().trim());
                    if (StringUtils.isNotEmpty(req.getName())) {
                        String[] names = CommonUtils.getNames(req.getName());
                        if (names != null) {
                            wrapperQuery.lambda().in(BmsContent::getName, Arrays.asList(names));
                        }
                    }
                } else {
                    String[] names = CommonUtils.getNames(req.getName());
                    if (names != null && names.length > 0) {
                        wrapperQuery.lambda().like(BmsContent::getName, names[0]);
                    }
                }
            }
        }
        // 是否缺集
        Integer missedInfo = req.getMissingEpisodes();
        if (Objects.nonNull(missedInfo)) {
            // 不缺集查询
            if (missedInfo == 2) {
                wrapperQuery.lambda().isNull(BmsContent::getMissedInfo);
            } else {
                // 缺集查询
                wrapperQuery.lambda().isNotNull(BmsContent::getMissedInfo);
            }
        }
        if (Objects.nonNull(po.getContentType())) {
            List<Integer> typeList = new ArrayList<>();
            if (ContentTypeItemEnum.PROGRAM.getContentType().equals(po.getContentType())) {
                typeList.add(ContentTypeItemEnum.PROGRAM.getContentType());
                typeList.add(ContentTypeItemEnum.FLOWER.getContentType());
                wrapperQuery.lambda().in(BmsContent::getContentType, typeList);
            }
            if (ContentTypeItemEnum.SERIES3.getContentType().equals(po.getContentType())) {
                typeList.add(ContentTypeItemEnum.SERIES3.getContentType());
                typeList.add(ContentTypeItemEnum.SERIES4.getContentType());
                wrapperQuery.lambda().in(BmsContent::getContentType, typeList);
            }
        }
        wrapperQuery.lambda().groupBy(BmsContent::getCmsContentCode);
        return this.page(req, wrapperQuery);
    }

    @Override
    public Page<BmsContentQueryReqBySP> getAuthorizedContentBySp(Page page,
            BmsContentQueryReq param) {
        //获取授权合同对应的sp
        LambdaQueryWrapper<SysAuthorization> wrapperSysAuthorization = Wrappers.lambdaQuery();
        wrapperSysAuthorization.eq(SysAuthorization::getCpId, param.getCpId());
        wrapperSysAuthorization.eq(SysAuthorization::getStatus, 1);
        List<SysAuthorization> sysAuthorizationList = sysAuthorizationService.list(
                wrapperSysAuthorization);
        List<Long> spId = sysAuthorizationList.stream().map(SysAuthorization::getSpId)
                .collect(Collectors.toList());
        param.setSpIdList(spId);

        if (param.getSeriesFlag() == null) {
            param.setSeriesFlag(2);
        }
        if (param.getName() != null) {
            if (param.getName().contains("\n")) {
                //防止name为空字符串以及只有回车的情况  进行再一次校验
                param.setName(param.getName().trim());
                if (StringUtils.isNotEmpty(param.getName())) {
                    String[] names = CommonUtils.getNames(param.getName());
                    if (names != null) {
                        param.setNameList(names);
                    }
                }
            } else {
                String[] names = CommonUtils.getNames(param.getName());
                if (names != null && names.length > 0) {
                    param.setName(names[0]);
                }
            }
        }

        Page<BmsContentQueryReqBySP> bmsContentQueryReqBySPPage = bmsContentMapper.getAuthorizedContentBySp(
                param);
        List<BmsContentQueryReqBySP> bmsContentQueryReqBySPList = bmsContentQueryReqBySPPage.getRecords();
        //获取cpid授权合同
        List<OutSpDto> outSpDtoList = sysAuthorizationService.getOutPassageListByCp(
                param.getCpId());

        for (BmsContentQueryReqBySP bmsContent : bmsContentQueryReqBySPList) {
            List<OutSpDto> outSpDtoListTemp = new ArrayList<>();

            if (null == bmsContent.getSpName()) {
                for (OutSpDto outSpDto : outSpDtoList) {
                    OutSpDto outSpDtoTemp = new OutSpDto();
                    outSpDto.setIsSelect(false);
                    BeanUtils.copyProperties(outSpDto, outSpDtoTemp);
                    outSpDtoListTemp.add(outSpDtoTemp);
                }
            } else {
                String[] spNameStr = bmsContent.getSpName().split(",");
                if (spNameStr == null) {
                    for (OutSpDto outSpDto : outSpDtoList) {
                        OutSpDto outSpDtoTemp = new OutSpDto();
                        outSpDto.setIsSelect(false);
                        BeanUtils.copyProperties(outSpDto, outSpDtoTemp);
                        outSpDtoListTemp.add(outSpDtoTemp);
                    }
                } else {
                    List<String> spNameLiST = Arrays.asList(spNameStr);
                    for (OutSpDto outSpDto : outSpDtoList) {
                        OutSpDto outSpDtoTemp = new OutSpDto();
                        outSpDto.setIsSelect(false);
                        BeanUtils.copyProperties(outSpDto, outSpDtoTemp);
                        for (String s : spNameLiST) {
                            if (s.equals(outSpDtoTemp.getId().toString())) {
                                outSpDtoTemp.setIsSelect(true);
                            }
                        }
                        outSpDtoListTemp.add(outSpDtoTemp);
                    }
                }
            }

            bmsContent.setSpNameList(outSpDtoListTemp);
            //   bmsContent.setAuthorize(param.getAuthorize());
        }

        return bmsContentQueryReqBySPPage;
    }

    @Override
    public boolean modifyLockStatus(List<Long> ids, Integer lockStatus) {
        List<BmsContent> poList = ids.stream().map(id -> {
            BmsContent po = new BmsContent();
            po.setId(id);
            po.setLockStatus(lockStatus);
            return po;
        }).collect(Collectors.toList());
        return updateBatchById(poList);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean modifyPublishStatus(List<Long> ids, Integer publishStatus) {
        if (CollectionUtils.isEmpty(ids)) {
            return false;
        }
        RuleCondition.create()
                //检查是否有锁定的状态
                .and(LockStatusRule.init(BmsContent.class).data(ids))
                .execute().check();
        List<BmsContent> bmsContents = bmsContentMapper.selectBatchIds(ids);
        //step1:修改媒资本身的发布状态
        LambdaUpdateWrapper<BmsContent> updateWrapper = Wrappers.lambdaUpdate(BmsContent.class)
                .set(BmsContent::getPublishStatus, publishStatus)
                .set(BmsContent::getPublishTime, null)
                .set(BmsContent::getTimedPublishStatus, IsTimedEnums.NO_TIMED.getCode())
                .set(BmsContent::getTimedPublishDescription, "")
                .set(BmsContent::getPublishTime, null)
                .set(BmsContent::getPublishDescription, "")
                //where
                .in(BmsContent::getId, ids);
        this.update(updateWrapper);
        //更新定时发布状态
        outScheduledTasksService.finishScheduledTasks(ids, ContentTypeEnum.FILM.getValue());
        //向统计报表发送消息
        sendMQ(bmsContents, publishStatus);
        return true;
    }

    private void sendMQ(List<BmsContent> bmsContents, Integer publishStatus) {
        String type;
        if (PublishStatusEnum.PUBLISH.getCode().equals(publishStatus)) {
            type = StatisticsMediaMQEnum.BMS_CONTENT.getValue();
        } else if (PublishStatusEnum.WAITPUBLISH.getCode().equals(publishStatus)) {
            type = StatisticsMediaMQEnum.BMS_CONTENT_WAITPUBLISH.getValue();
        } else {
            return;
        }
        for (BmsContent bmsContent : bmsContents) {
            Integer publishStatusOld = bmsContent.getPublishStatus();
            if (Boolean.FALSE.equals(verifyPublishStatus(publishStatusOld, publishStatus))) {
                continue;
            }
            bmsContent.setPublishStatus(publishStatus);
            bmsContent.setPublishTime(new Date());
            Map<String, Object> map = new HashMap<>();
            map.put(type, bmsContent);
            log.info("BmsContentController.modifyPublishStatus.sendMQ.map={}",
                    JSON.toJSONString(map));
            this.rabbitTemplate.convertAndSend(StatisticsOnlineMQConfig.STATISTIC_ONLINE_EXCHANGE,
                    StatisticsOnlineMQConfig.STATISTIC_ONLINE_ROUTING, map);
        }
    }

    private Boolean verifyPublishStatus(Integer publishStatusOld, Integer publishStatusNew) {
        if (publishStatusOld.equals(publishStatusNew)) {
            return false;
        }
        if (PublishStatusEnum.ROLLBACKING.getCode().equals(publishStatusOld)) {
            return false;
        }
        if (PublishStatusEnum.ROLLBACK.getCode().equals(publishStatusOld)
                && PublishStatusEnum.WAITPUBLISH.getCode().equals(publishStatusNew)) {
            return false;
        }
        if (PublishStatusEnum.FAILROLLBACK.getCode().equals(publishStatusOld)
                && PublishStatusEnum.PUBLISH.getCode().equals(publishStatusNew)) {
            return false;
        }
        if (PublishStatusEnum.WAITUPDATE.getCode().equals(publishStatusOld)
                && PublishStatusEnum.WAITPUBLISH.getCode().equals(publishStatusNew)) {
            return false;
        }
        if (PublishStatusEnum.PUBLISHING.getCode().equals(publishStatusOld)
                && PublishStatusEnum.WAITPUBLISH.getCode().equals(publishStatusNew)) {
            return false;
        }
        return true;
    }

    /**
     * @Description: 重置发布状态
     * @param: [ids]
     * @return: boolean
     * @Author: wz
     * @date: 2021/10/7 15:02
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean resetPublishStatus(List<Long> ids) {
        //step1:检查内容的锁定状态
        if (CollectionUtils.isEmpty(ids)) {
            return true;
        }
        RuleCondition.create()
                //检查是否有锁定的状态
                .and(LockStatusRule.init(BmsContent.class).data(ids))
                //检查是否 包含 不是发布中 的内容 （发布超时，在DB中的体现就是发布中）
                .and(PublishStatusRule.init(BmsContent.class).data(ids).policy(MUST_ING)
                        .type("重置发布状态"))
                .execute().check();

        //step2.1查询对应发布状态
        LambdaQueryWrapper<BmsContent> wrapper =
                Wrappers.lambdaQuery(BmsContent.class)
                        .select(BmsContent::getId, BmsContent::getPublishStatus,
                                BmsContent::getContentType, BmsContent::getCmsContentId,
                                BmsContent::getSpId).in(BmsContent::getId, ids);
        //DB po集合
        List<Map<String, Object>> list = this.listMaps(wrapper);
        if (CollectionUtils.isEmpty(list)) {
            return true;
        }
        //转换成需要更新的po
        LambdaTrans<BmsContent> lt = LambdaTrans.instance(BmsContent.class);

        List<BmsContent> updateList = list.stream().map(item -> {
            Long id = lt.trans(item, BmsContent::getId, Long.class);
            //批量修改的PO集合
            Integer status = lt.trans(item, BmsContent::getPublishStatus, Integer.class);
            return updatePublishTrans(id, status);
        }).collect(Collectors.toList());

        //step2.2更新对应内容的发布状态
        this.updateBatchById(updateList);
//不对子集和图片进行重置
//        //step3.1 查询相关的图片id
//        List<Long> picIds = getPicIds(seriesIds, ContentTypeEnum.TELEPLAY);
//        picIds.addAll(getPicIds(seriesIds, ContentTypeEnum.FILM));
//        //step3.2重置 剧集的图片的发布状态
//        if (ObjectUtil.isNotEmpty(picIds)) {
//            bmsPictureService.resetPublishStatus(picIds);
//        }
//        //step4.1 过滤出发布中的剧头
//        List<BmsContent> seriesList = getSeriesInPublishIng(list, lt);
//        if (!CollectionUtils.isEmpty(seriesList)) {
//            seriesList.forEach(series -> {
//                //step4.2 获取剧头下的子集
//                List<Long> programIds = bmsProgramService.getProgramIds(series.getCmsContentId(), series.getSpId());
//                //step4.3 重置子集的发布状态
//                bmsProgramService.resetPublishStatus(programIds, series.getId());
//            });
//        }
        return true;
    }

    //过滤出发布中的剧头
    private List<BmsContent> getSeriesInPublishIng(List<Map<String, Object>> list,
            LambdaTrans<BmsContent> lt) {
        return list.stream().map(item -> {
            Integer type = lt.trans(item, BmsContent::getContentType, Integer.class);
            Integer publishStatus = lt.trans(item, BmsContent::getPublishStatus, Integer.class);
            //如果是剧集 则需要更新子集的状态
            if (ContentTypeEnum.TELEPLAY.getValue().equals(type)) {
                //如果是发布中的状态 子集的状态需要和剧头同步
                if (PublishStatusEnum.PUBLISHING.getCode().equals(publishStatus)) {
                    Long id = lt.trans(item, BmsContent::getId, Long.class);
                    Long cmsContentId = lt.trans(item, BmsContent::getCmsContentId, Long.class);
                    Long spId = lt.trans(item, BmsContent::getSpId, Long.class);
                    BmsContent series = new BmsContent();
                    series.setCmsContentId(cmsContentId);
                    series.setSpId(spId);
                    series.setId(id);
                    return series;
                }
            }
            return null;
        }).filter(Objects::nonNull).collect(Collectors.toList());
    }

    /**
     * @Description: 转换只更新 发布状态 的po
     * @param: [po, publishStatus]
     * @return: com.pukka.iptv.common.data.model.bms.BmsContent
     * @Author: wz
     * @date: 2021/10/7 15:32
     */
    private BmsContent updatePublishTrans(Long id, Integer publishStatus) {
        BmsContent bmsContent = new BmsContent();
        bmsContent.setId(id);
        bmsContent.setPublishStatus(CommonUtils.resetPublishStatus(publishStatus));
        //取消定时发布
        bmsContent.setTimedPublishStatus(IsTimedEnums.NO_TIMED.getCode());
        bmsContent.setTimedPublishDescription("");
        return bmsContent;
    }

    //剧集的修改发布状态
    private boolean seriesModifyPublishStatus(List<BmsContent> seriesList, Integer publishStatus) {
        //step2.1:获取所有子集的ids
        List<Long> programIds = bmsProgramService.getProgramIds(seriesList);
        //step:2.2:设置所有子集的发布状态
        bmsProgramService.modifyPublishStatus(programIds, null, publishStatus);
        //step3:由于剧集和单集除了子集部分不一样，其余都是一样的操作，故下面可以把剧头 当作单集处理
        //step3.1:修改剧头的发布状态
        signalFilmModifyPublishStatus(seriesList, publishStatus, ContentTypeEnum.TELEPLAY);
        //todo  ContentTypeEnum.EPISODES
        return true;
    }

    //单集的修改发布状态
    private boolean signalFilmModifyPublishStatus(List<BmsContent> signalList,
            Integer publishStatus, ContentTypeEnum contentType) {
        //单集的ids
        List<Long> contentIds = signalList.stream().map(BmsContent::getId)
                .collect(Collectors.toList());

        //step1:检查单集自己是否 是发布中的状态
        //自己在上一步调用处已经检查过，此处不做

        //step2:检查单集的图片是否 是发布中的状态
        //step2.1查找图片的id
        List<Long> picIds = getPicIds(contentIds, contentType);
        //step2.2修改图片的发布状态
        bmsPictureService.modifyPublishStatus(picIds, publishStatus);

        //step3:检查单集绑定的栏目内容关系 是否是发布中的状态
        //step3.1:查询单集绑定的栏目内容关系
        List<Long> categoryContentIds = getCategoryContentIds(contentIds);
        //step3.2: 设置栏目和内容关系的 发布状态
        bmsCategoryContentService.modifyPublishStatus(categoryContentIds, publishStatus);

        //step4:检查单集绑定的产品包内容关系 是否是发布中的状态
        //step4.1:获取产品包和内容关系id集合
        List<Long> packageContentIds = getPackageContentIds(contentIds);
        //step4.2:设置产品包和内容关系的 发布状态
        bmsPackageContentService.modifyPublishStatus(packageContentIds, publishStatus);
        return true;
    }

    //根据内容id集合查询产品包内容关系ids
    private List<Long> getPackageContentIds(List<Long> contentIds) {
        return bmsPackageContentService.getByContentIds(contentIds).stream()
                .map(BmsPackageContent::getId).collect(Collectors.toList());
    }

    //根据内容id集合查询栏目内容关系ids
    private List<Long> getCategoryContentIds(List<Long> contentIds) {
        return bmsCategoryContentService.getByContentIds(contentIds).stream()
                .map(BmsCategoryContent::getId).collect(Collectors.toList());
    }

    //根据内容的ids查找图片的ids
    private List<Long> getPicIds(List<Long> contentIds, ContentTypeEnum contentType) {
        return bmsPictureService.getPicIdByContentIds(contentIds, contentType.getValue());
    }

    //剧集+系列片发布
    private boolean seriesPublish(List<BmsContent> seriesList,
            Map<String, OutParamExpand> paramMap) {
        boolean isProgram = isProgram();
        if (ObjectUtil.isEmpty(seriesList)) {
            return true;
        }
        BmsContent tmp = seriesList.get(0);
        log.info("剧集发布");
        //需要发布的集合
        List<BmsContent> updateList = seriesList.stream().filter(
                        (BmsContent s) -> PublishStatusRule.CAN_UPDATE_LIST.contains(s.getPublishStatus()))
                .collect(Collectors.toList());
        List<BmsContent> publishList = seriesList.stream().filter(
                        (BmsContent s) -> PublishStatusRule.PUBLISH_LIST.contains(s.getPublishStatus()))
                .collect(Collectors.toList());
        //剧集的发布工单
        for (BmsContent seriesItem : publishList) {
            publishContentByaction(seriesItem, ActionEnums.REGIST, isProgram, paramMap);
        }
        //剧集的更新工单
        for (BmsContent seriesItem : updateList) {
            publishContentByaction(seriesItem, ActionEnums.UPDATE, isProgram, paramMap);
        }

       /* boolean flag = true;
        Map<Integer, List<BmsContent>> updateGroup = updateList.stream().collect(Collectors.groupingBy(BmsContent::getContentType));
        for (Map.Entry<Integer, List<BmsContent>> item : updateGroup.entrySet()) {
            List<Long> updateListOneType = item.getValue().stream().map(BmsContent::getId).collect(Collectors.toList());
            ContentTypeEnum type = ContentTypeEnum.getByValue(item.getKey());
            //剧集的更新工单
            log.info("调用{}的发布更新接口", type.getDesc());
            if (!this.sendContentOrder(ActionEnums.UPDATE, type, updateListOneType, tmp.getSpId(), tmp.getSpName(), paramMap)) {
                flag = false;
            }
        }
        if (!flag) throw new BizException("剧集更新工单下发失败");*/
        return true;
    }

    private void publishContentByaction(BmsContent seriesItem, ActionEnums actionEnums,
            boolean isProgram, Map<String, OutParamExpand> paramMap) {
        //发布剧头
        log.info("发布剧头");
        if (!this.sendContentOrder(actionEnums,
                ContentTypeEnum.getByValue(seriesItem.getContentType()),
                Collections.singletonList(seriesItem.getId()),
                seriesItem.getSpId(), seriesItem.getSpName(), paramMap)) {
            throw new BizException("发布工单下发失败");
        }
        if (!isProgram) {
            //查询剧头下的子集
            log.info("发布剧头下的子集");
            LambdaQueryWrapper<BmsProgram> wrapper = Wrappers.lambdaQuery(BmsProgram.class)
                    .select(BmsProgram::getId, BmsProgram::getPublishStatus)
                    //审核通过
                    .eq(BmsProgram::getOpCheckStatus, OpCheckStatusEnum.Pass.getValue())
                    .eq(BmsProgram::getCmsSeriesId, seriesItem.getCmsContentId())
                    .in(BmsProgram::getPublishStatus, PublishStatusRule.CAN_PUBLISH_LIST)
                    .eq(BmsProgram::getSpId, seriesItem.getSpId());
            List<BmsProgram> bmsPrograms = bmsProgramMapper.selectList(wrapper);
            if (ObjectUtil.isNotEmpty(bmsPrograms)) {
                List<Long> programIds = bmsPrograms.stream().map(BmsProgram::getId)
                        .collect(Collectors.toList());
                //检查子集是否有缺少视频的
                RuleCondition.create()
                        //查询是否有媒资 没有关联视频
                        .and(MovieCheckRule.init(BmsProgram.class).data(programIds))
                        .execute().check();

                List<Long> updateIds = bmsPrograms.stream().filter(
                                (BmsProgram p) -> PublishStatusRule.CAN_UPDATE_LIST.contains(
                                        p.getPublishStatus())).map(BmsProgram::getId)
                        .collect(Collectors.toList());
                List<Long> publishIds = bmsPrograms.stream().filter(
                                (BmsProgram p) -> PublishStatusRule.PUBLISH_LIST.contains(
                                        p.getPublishStatus())).map(BmsProgram::getId)
                        .collect(Collectors.toList());
                //发布子集
                if (!bmsProgramService.sendProgramOrder(ActionEnums.REGIST, publishIds,
                        seriesItem.getSpId(), seriesItem.getSpName(), paramMap)) {
                    throw new BizException("发布工单下发失败");
                }
                if (!bmsProgramService.sendProgramOrder(ActionEnums.UPDATE, updateIds,
                        seriesItem.getSpId(), seriesItem.getSpName(), paramMap)) {
                    throw new BizException("更新工单下发失败");
                }
            }
        }
    }

    //单集发布
    private boolean filmPublish(List<BmsContent> filmList, Map<String, OutParamExpand> paramMap) {
        if (ObjectUtil.isEmpty(filmList)) {
            return true;
        }
        log.info("单集发布");
        BmsContent tmp = filmList.get(0);
        //需要发布的集合
        List<Long> publishList = new ArrayList<>(filmList.size() / 2);
        //需要更新的集合
        List<Long> updateList = new ArrayList<>(filmList.size() / 2);
        for (BmsContent bmsContent : filmList) {
            Long id = bmsContent.getId();
            Integer publishStatus = bmsContent.getPublishStatus();
            // 发布工单 (待发布和回收成功)
            if (RuleUtil.isWaitPublish(publishStatus)) {
                publishList.add(id);
            }
            // 更新工单
            else {
                updateList.add(id);
            }
        }
        //调用发布接口
        log.info("单集调用发布接口");
        if (!this.sendContentOrder(ActionEnums.REGIST, ContentTypeEnum.FILM, publishList,
                tmp.getSpId(), tmp.getSpName(), paramMap)) {
            throw new BizException("单集发布工单下发失败");
        }

        //调用更新接口
        log.info("单集调用发布更新接口");
        if (!this.sendContentOrder(ActionEnums.UPDATE, ContentTypeEnum.FILM, updateList,
                tmp.getSpId(), tmp.getSpName(), paramMap)) {
            throw new BizException("单集更新工单下发失败");
        }
        return true;
    }

    private Boolean checkCpName(List<String> cpNameList, List<String> filterCpNameList) {
        List<String> inputCp = cpNameList.stream().distinct().collect(Collectors.toList());
        int oldSize = inputCp.size();
        List<String> staticCp = filterCpNameList.stream().distinct().collect(Collectors.toList());
        inputCp.removeIf(staticCp::contains);
        return inputCp.size() > 0 && inputCp.size() != oldSize;
    }

    /**
     * @Description: 单集/剧集发布接口(支持定时发布) 前置条件: 1.内容不能锁定 2.
     * @param: [ids, doSchedule, scheduleTime]
     * @return: boolean
     * @author: wz
     * @date: 2021/11/6 14:18
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean publish(List<Long> ids, boolean doSchedule, String scheduleTime,
            Map<String, OutParamExpand> paramMap) {
        checkContent(ids);
        //如果需要定时发布，将定时发布时间存入数据库
        if (doSchedule) {
            //添加到定时发布任务表
            boolean scheduledTasks = outScheduledTasksService.createScheduledTasks(ids,
                    ContentTypeEnum.FILM.getValue(), scheduleTime,
                    PriorityEnums.GENERAL.getValue());
            if (!scheduledTasks) {
                log.error("媒资内容添加到定时发布任务表outScheduledTasks失败，ids:{}", ids);
                throw new BizException("定时发布任务添加失败");
            }
            LambdaUpdateWrapper<BmsContent> in = Wrappers.lambdaUpdate(BmsContent.class)
                    .set(BmsContent::getTimedPublish, scheduleTime)
                    .set(BmsContent::getTimedPublishStatus, IsTimedEnums.IS_TIMED.getCode())
                    .in(BmsContent::getId, ids);
            this.update(in);
        } else {
            List<BmsContent> bmsContents = this.list(Wrappers.lambdaQuery(BmsContent.class)
                    .select(BmsContent::getId, BmsContent::getSpId, BmsContent::getSpName,
                            BmsContent::getContentType, BmsContent::getPublishStatus,
                            BmsContent::getCmsContentId)
                    .in(BmsContent::getId, ids));
            //剧集集合
            List<BmsContent> seriesList = new ArrayList<>(bmsContents.size() / 2);
            //对内容进行单集/剧集分组
            Iterator<BmsContent> iterator = bmsContents.iterator();
            while (iterator.hasNext()) {
                BmsContent next = iterator.next();
                //剧集＋系列片
                if (ContentTypeEnum.TELEPLAY.getValue().equals(next.getContentType())
                        || ContentTypeEnum.EPISODES.getValue().equals(next.getContentType())) {
                    seriesList.add(next);
                    iterator.remove();
                }
            }
            //单集发布
            filmPublish(bmsContents, paramMap);
            //剧集发布
            seriesPublish(seriesList, paramMap);
        }
        return true;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean seriesPublish(List<Long> ids, boolean doSchedule, String scheduleTime) {
        List<BmsContent> bmsContents = bmsContentMapper.selectBatchIds(ids);
        bmsContents.parallelStream().forEach(bmsContent -> {
            if (ContentTypeItemEnum.PROGRAM.getContentType().equals(bmsContent.getContentType())
                    || ContentTypeItemEnum.FLOWER.getContentType()
                    .equals(bmsContent.getContentType())) {
                throw new BizException("请选择剧集进行发布");
            }
        });
        Map<String, OutParamExpand> paramMap = new HashMap<>();
        paramMap.put(PublishParamTypeConstants.PRIORITY,
                new OutParamExpand().setPriority(PriorityEnums.GENERAL.getValue()));
        setProgramFlag();
        boolean publish = this.publish(ids, doSchedule, scheduleTime, paramMap);
        return publish;
    }

    /**
     * @Description: 取消定时发布
     * @param: [id]
     * @return: boolean
     * @author: wz
     * @date: 2021/11/6 14:17
     */
    @Override
    public boolean cancelTimedPublish(Long id) {
        List<Long> ids = Collections.singletonList(id);
        RuleCondition.create()
                //锁定判断
                .and(LockStatusRule.init(BmsContent.class).data(ids))
                .execute().check();
        //取消定时发布
        boolean b = outScheduledTasksService.deleteScheduledTask(id,
                ContentTypeEnum.FILM.getValue());
        if (!b) {
            log.error("内容取消定时发布失败，id:{}", id);
            throw new BizException("取消定时发布失败");
        }
        LambdaUpdateWrapper<BmsContent> wrapper =
                Wrappers.lambdaUpdate(BmsContent.class)
                        .set(BmsContent::getTimedPublish, null)
                        .set(BmsContent::getTimedPublishStatus, IsTimedEnums.NO_TIMED.getCode())
                        .eq(BmsContent::getId, id);
        return this.update(wrapper);
    }

    /**
     * <AUTHOR>
     * @Date 2021-11-10 10:02:10
     * @Description 媒资下发
     */
    @Override
    @Transactional(rollbackFor = {Exception.class})
    public boolean sendContentOrder(ActionEnums actionEnums, ContentTypeEnum typeEnum,
            List<Long> contents, Long spId, String spName, Map<String, OutParamExpand> paramMap) {
        if (CollectionUtils.isEmpty(contents)) {
            return true;
        }
        Map<String, String> picMap;
        // 如果是回收
        if (ActionEnums.DELETE.equals(actionEnums)) {
            // 查询所有可以回收的图片
            List<Long> recycleIds = bmsPictureService.pictureCanBeRecycled(contents, typeEnum);
            picMap = bmsPictureService.buildRecycleWorkOrderMap(recycleIds);
        } else {
            // 查询可跟随本次 发布|更新 的图片
            picMap = bmsPictureService.findCanPublishPicture(contents, typeEnum);
        }
        List<Long> publishPicIds = new ArrayList<>();
        List<Long> updatePicIds = new ArrayList<>();
        List<Long> recyclePicIds = new ArrayList<>();
        if (!CollectionUtils.isEmpty(picMap)) {
            picMap.forEach((k, v) -> {
                // 更新工单的图片id
                if (v.equals(ActionEnums.REGIST.getCode().toString())) {
                    publishPicIds.add(Long.valueOf(k));
                } else if (v.equals(ActionEnums.UPDATE.getCode().toString())) {
                    updatePicIds.add(Long.valueOf(k));
                } else if (v.equals(ActionEnums.DELETE.getCode().toString())) {
                    recyclePicIds.add(Long.valueOf(k));
                }
            });
        }
        return workOrderOperation.send(actionEnums, typeEnum, contents, picMap, spId, spName,
                (success, publishStatus, description) -> {
                    //首次下发时间
                    if (ActionEnums.REGIST.equals(actionEnums)) {
                        bmsContentMapper.setFirstPublishTime(contents);
                    }
                    // 更新发布状态以及发布描述
                    this.update(Wrappers.lambdaUpdate(BmsContent.class)
                            .set(BmsContent::getPublishStatus, publishStatus)
                            .set(BmsContent::getPublishDescription, description)
                            .set(BmsContent::getPublishTime, new Date())
                            .set(BmsContent::getTimedPublishDescription, "")
                            .in(BmsContent::getId, contents));
                    // 处理调用下发失败后的图片状态以及描述
                    bmsPictureService.pictureIssuedProcess(success, publishPicIds, updatePicIds,
                            recyclePicIds, description);
                }, paramMap);
    }

    @Override
    public boolean status(List<Long> ids, StatusEnum statusEnum) {
        RuleCondition.create()
                //锁定判断
                .and(LockStatusRule.init(BmsContent.class).data(ids))
                //发布状态为 发布中的内容不可修改
                .and(PublishStatusRule.init(BmsContent.class).data(ids).policy(ING))
                .execute()
                .check();
        return setStatus(BmsContent.class, ids, statusEnum) > 0;
    }

    @Autowired
    private BmsBaseMapper bmsBaseMapper;

    public int setStatus(Class<?> table, List<Long> ids, StatusEnum status) {
        TableInfo tableInfo = TableInfoHelper.getTableInfo(table);
        return bmsBaseMapper.setStatus(tableInfo.getTableName(), ids, status);
    }

    // 定时发布
    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean schedulePublish() {
        List<OutScheduledTask> scheduledTasks = outScheduledTasksService.getScheduledTasks(
                ContentTypeEnum.FILM.getValue());
        if (ObjectUtil.isEmpty(scheduledTasks)) {
            return true;
        }
        LambdaQueryWrapper<BmsContent> wrapper = Wrappers.lambdaQuery(BmsContent.class)
                .select(BmsContent::getId, BmsContent::getTimedPublish)
                .in(BmsContent::getId, scheduledTasks.stream().map(OutScheduledTask::getContentId)
                        .collect(Collectors.toList()));
        List<BmsContent> list = this.list(wrapper);
        if (CollectionUtils.isEmpty(list)) {
            //更新定时发布状态
            outScheduledTasksService.finishScheduledTasks(
                    scheduledTasks.stream().map(OutScheduledTask::getContentId)
                            .collect(Collectors.toList()), ContentTypeEnum.FILM.getValue());
            return true;
        }
        List<Long> ids = list.stream().map(BmsContent::getId).collect(Collectors.toList());
        //添加媒资库不存在定时任务校验
        List<Long> filter = scheduledTasksUtil.filterScheduledTask(
                scheduledTasks, ids);
        if (CollectionUtil.isNotEmpty(filter)) {
            //更新状态
            outScheduledTasksService.finishScheduledTasks(filter,
                    ContentTypeEnum.FILM.getValue());
        }
        //添加定时发布任务状态双重校验
        Iterator<Long> iterator = ids.iterator();
        while (iterator.hasNext()) {
            Long item = iterator.next();
            try {
                checkDoubleContent(Collections.singletonList(item));
            } catch (Exception e) {
                log.error("定时发布任务状态双重校验失败", e);
                //更新状态
                outScheduledTasksService.finishScheduledTasks(Collections.singletonList(item),
                        ContentTypeEnum.FILM.getValue());
                this.update(Wrappers.lambdaUpdate(BmsContent.class)
                        .set(BmsContent::getTimedPublishDescription, "定时发布校验失败")
                        .set(BmsContent::getTimedPublishStatus, IsTimedEnums.NO_TIMED.getCode())
                        .eq(BmsContent::getId, item));
                iterator.remove();
            }
        }
        if (CollectionUtils.isEmpty(ids)) {
            log.info("定时发布任务状态双重校验结束，无可发布内容");
            return true;
        }
        Integer priority = scheduledTasks.get(0).getPriority();
        Map<String, OutParamExpand> paramMap = new HashMap<>();
        paramMap.put(PublishParamTypeConstants.PRIORITY,
                new OutParamExpand().setPriority(
                        priority < 0 ? PriorityEnums.GENERAL.getValue() : priority));
        Exception exc = null;
        try {
            setScheduleFlag();
            this.publish(ids, false, null,
                    paramMap);
        } catch (Exception e) {
            exc = e;
            log.error("单集剧集定时发布错误:" + e.getMessage(), e);
        } finally {
            if (ObjectUtils.isNotEmpty(exc)) {
                for (OutScheduledTask scheduledTask : scheduledTasks) {
                    scheduledTask.setTimedPublishDescription(exc.getMessage());
                }
            }
            //更新定时发布状态
            outScheduledTasksService.finishScheduledTasks(ids, ContentTypeEnum.FILM.getValue());
            this.update(Wrappers.lambdaUpdate(BmsContent.class)
                    .set(BmsContent::getTimedPublishDescription,
                            exc != null ? exc.getMessage() : "")
                    .set(BmsContent::getTimedPublishStatus, IsTimedEnums.NO_TIMED.getCode())
                    .in(BmsContent::getId, ids));
        }
        return true;
    }

    @Override
    public List<CpFeedbackContentVO> getCpFeedbackContents() {
        String cpFeedbackURL = config.getCpFeedbackURL();
        log.info("cpFeedbackURL:{}", cpFeedbackURL);
        if (com.pukka.iptv.common.core.util.StringUtils.isEmpty(cpFeedbackURL)) {
            log.info("CP运营商内容状态反馈地址配置为空，请前往nacos上配置common.cpFeedback.url");
            return null;
        }
        ArrayList<String> cpIdList = new ArrayList<>();
        JSONArray urlJsonArray = JSONObject.parseArray(cpFeedbackURL);
        for (int i = 0; i < urlJsonArray.size(); i++) {
            JSONObject urlJson = urlJsonArray.getJSONObject(i);
            cpIdList.add(urlJson.getString("cpId"));
        }
        return bmsContentMapper.getCpFeedbackContents(config.getCpFeedbackCount(), cpIdList);
    }

    @Override
    public boolean updateCpFeedback(List<Long> idList, int result) {
        log.info("CP运营商内容状态反馈 Content Id = {}", idList);
        if (idList.isEmpty()) {
            return true;
        }
        return this.update(Wrappers.lambdaUpdate(BmsContent.class)
                .set(BmsContent::getCpFeedbackFlag, result)
                .in(BmsContent::getId, idList)
        );
    }

    @Override
    public List<Long> getcontentIdsByProgramIds(List<Long> programIds) {
        LambdaQueryWrapper<BmsContent> lambdaQueryWrapper = new LambdaQueryWrapper();
        LambdaQueryWrapper<BmsProgram> bmsProgramLambdaQueryWrapper = new LambdaQueryWrapper();
        bmsProgramLambdaQueryWrapper.select(BmsProgram::getSpId, BmsProgram::getCmsSeriesId)
                .in(BmsProgram::getId, programIds);
        List<BmsProgram> bmsPrograms = bmsProgramMapper.selectList(bmsProgramLambdaQueryWrapper);
        List<Long> seriesIds = bmsPrograms.stream().map(BmsProgram::getCmsSeriesId)
                .collect(Collectors.toList()).stream().distinct().collect(Collectors.toList());
        List<Long> spIds = bmsPrograms.stream().map(BmsProgram::getSpId)
                .collect(Collectors.toList()).stream().distinct().collect(Collectors.toList());
        lambdaQueryWrapper.in(BmsContent::getCmsContentId, seriesIds)
                .in(BmsContent::getSpId, spIds);
        List<BmsContent> bmsContents = this.list(lambdaQueryWrapper);
        return bmsContents.stream().map(BmsContent::getId).distinct().collect(Collectors.toList());
    }

    @Override
    public boolean batchRecycle(List<Long> ids, Integer type) {
        //子集调用子集一键回收
        if (ContentTypeEnum.SUBSET.getValue().equals(type)) {
            return bmsProgramService.batchRecycle(ids);
        }
        LambdaQueryWrapper<BmsContent> wrapper = new LambdaQueryWrapper<>();
        wrapper.select(BmsContent::getId).in(BmsContent::getCmsContentId, ids)
                .in(type.equals(3), BmsContent::getContentType, 3, 4)
                .in(type.equals(1), BmsContent::getContentType, 1);
        List<BmsContent> list = this.list(wrapper);
        if (CollectionUtils.isEmpty(list)) {
            return false;
        }
        List<Long> bmsContenIds = list.stream().map(BmsContent::getId).collect(Collectors.toList());
        //20230612 添加sp状态校验
        RuleResult execute = RuleCondition.create()
                .and(SpCheckRule.init(BmsContent.class).data(bmsContenIds))
                .execute();
        if (!execute.isPass()) {
            //20230612 更新媒资发布状态为禁用
            Arrays.stream(execute.getNameVal().split(SymbolConstant.COMMA))
                    .collect(Collectors.toList())
                    .forEach(id -> {
                        this.update(Wrappers.lambdaUpdate(BmsContent.class)
                                .set(BmsContent::getPublishStatus,
                                        PublishStatusEnum.DISABLE.getCode())
                                .eq(BmsContent::getId, id));
                    });
            //20230612 更新媒资集合
            bmsContenIds.removeAll(Arrays.stream(execute.getNameVal().split(SymbolConstant.COMMA))
                    .map(Long::parseLong).collect(Collectors.toList()));
        }
        this.triggerCpRecycle()
                .breakFlag(BmsContentRecycleDto.class)
                .setArgs(BmsContentRecycleDto.class,
                        new BmsContentRecycleDto().setBmsContentIds(bmsContenIds));
        this.oneKeyRecycle();

        //重新统计锚点
        StatisticsInDelete statisticsInDelete = new StatisticsInDelete();
        if (ContentTypeEnum.FILM.getValue().equals(type)) {
            statisticsInDelete.setType(StatisticsTypeEnum.SimpleSet.getValue());
        } else if (ContentTypeEnum.TELEPLAY.getValue().equals(type)) {
            statisticsInDelete.setType(StatisticsTypeEnum.Series.getValue());
        } else {

        }

        statisticsInDelete.setStaticticDate(DateUtils.getNowString());
        statisticsInDeleteService.sendMessage(statisticsInDelete);
        return true;
    }

    @Override
    public boolean deleteByCodeAndSp(List<String> codeList, List<Long> spIdList,
            ContentTypeEnum contentType, boolean isRollback) {
        log.info("CP自动发布删除媒资");

        if (contentType.equals(ContentTypeEnum.TELEPLAY)) {
            deleteSeries(codeList, spIdList, isRollback);
        } else {
            deleteProgram(codeList, spIdList, isRollback);
        }
        return true;
    }


    private void deleteSeries(List<String> codeList, List<Long> spIdList, boolean isRollback) {
        log.info("删除 授权 剧集");
        if (isRollback) {
            //删除 授权 子集
            bmsProgramService.update(Wrappers.lambdaUpdate(BmsProgram.class)
                    .set(BmsProgram::getPublishStatus, PublishStatusEnum.ROLLBACK.getCode())
                    .in(BmsProgram::getCmsSeriesCode, codeList)
                    .in(BmsProgram::getSpId, spIdList)
            );

            // 删除授权剧头
            bmsContentMapper.update(null, Wrappers.lambdaUpdate(BmsContent.class)
                    .set(BmsContent::getPublishStatus, PublishStatusEnum.ROLLBACK.getCode())
                    .in(BmsContent::getCmsContentCode, codeList)
                    .in(BmsContent::getContentType,
                            ContentTypeEnum.TELEPLAY.getValue(),
                            ContentTypeEnum.EPISODES.getValue()
                    )
                    .in(BmsContent::getSpId, spIdList)
            );
        } else {
            //删除 授权 子集
            bmsProgramService.remove(Wrappers.lambdaQuery(BmsProgram.class)
                    .in(BmsProgram::getCmsSeriesCode, codeList)
                    .in(BmsProgram::getSpId, spIdList)
            );

            // 删除授权剧头
            bmsContentMapper.delete(Wrappers.lambdaQuery(BmsContent.class)
                    .in(BmsContent::getCmsContentCode, codeList)
                    .in(BmsContent::getContentType,
                            ContentTypeEnum.TELEPLAY.getValue(),
                            ContentTypeEnum.EPISODES.getValue()
                    )
                    .in(BmsContent::getSpId, spIdList)
            );
        }
        deleteContentMapping(codeList, spIdList, isRollback, ContentTypeEnum.TELEPLAY.getValue(),
                ContentTypeEnum.EPISODES.getValue());
        //只有所有sp数据都回收了才会删除cp侧的数据

    }

    private void deleteContentMapping(List<String> codeList, List<Long> spIdList,
            boolean isRollback, Integer... contentTypes) {
        log.info("删除内容的 mapping 关系");
        if (isRollback) {
            log.info("回收内容的 mapping 关系");
            bmsCategoryContentService.update(Wrappers.lambdaUpdate(BmsCategoryContent.class)
                    .set(BmsCategoryContent::getPublishStatus, PublishStatusEnum.ROLLBACK.getCode())
                    .in(BmsCategoryContent::getCmsContentCode, codeList)
                    .in(BmsCategoryContent::getContentType, contentTypes)
                    .in(BmsCategoryContent::getSpId, spIdList)
            );
            bmsPackageContentService.update(Wrappers.lambdaUpdate(BmsPackageContent.class)
                    .set(BmsPackageContent::getPublishStatus, PublishStatusEnum.ROLLBACK.getCode())
                    .in(BmsPackageContent::getCmsContentCode, codeList)
                    .in(BmsPackageContent::getContentType, contentTypes)
                    .in(BmsPackageContent::getSpId, spIdList)
            );
            bmsPictureService.update(Wrappers.lambdaUpdate(BmsPicture.class)
                    .set(BmsPicture::getPublishStatus, PublishStatusEnum.ROLLBACK.getCode())
                    .in(BmsPicture::getContentCode, codeList)
                    .in(BmsPicture::getContentType, contentTypes)
                    .in(BmsPicture::getSpId, spIdList)
            );
            return;
        }
        // 删除 栏目、产品包、图片关系
        bmsCategoryContentService.remove(Wrappers.lambdaQuery(BmsCategoryContent.class)
                .in(BmsCategoryContent::getCmsContentCode, codeList)
                .in(BmsCategoryContent::getContentType, contentTypes)
                .in(BmsCategoryContent::getSpId, spIdList)
        );
        bmsPackageContentService.remove(Wrappers.lambdaQuery(BmsPackageContent.class)
                .in(BmsPackageContent::getCmsContentCode, codeList)
                .in(BmsPackageContent::getContentType, contentTypes)
                .in(BmsPackageContent::getSpId, spIdList)
        );
        bmsPictureService.remove(Wrappers.lambdaQuery(BmsPicture.class)
                .in(BmsPicture::getContentCode, codeList)
                .in(BmsPicture::getContentType, contentTypes)
                .in(BmsPicture::getSpId, spIdList)
        );
    }

    private void deleteProgram(List<String> codeList, List<Long> spIdList, boolean isRollback) {
        log.info("删除单集");
        deleteContentMapping(codeList, spIdList, isRollback, ContentTypeEnum.FILM.getValue());

        if (isRollback) {
            /* Program */
            log.info("回收成功单集");
            bmsContentMapper.update(null, Wrappers.lambdaUpdate(BmsContent.class)
                    .set(BmsContent::getPublishStatus, PublishStatusEnum.ROLLBACK.getCode())
                    .in(BmsContent::getCmsContentCode, codeList)
                    .eq(BmsContent::getContentType, ContentTypeEnum.FILM.getValue())
                    .in(BmsContent::getSpId, spIdList)
            );
        } else {
            /* Program */
            bmsContentMapper.delete(Wrappers.lambdaQuery(BmsContent.class)
                    .in(BmsContent::getCmsContentCode, codeList)
                    .eq(BmsContent::getContentType, ContentTypeEnum.FILM.getValue())
                    .in(BmsContent::getSpId, spIdList)
            );
        }
    }


    /**
     * 删除视频图片记录及其ftp文件
     *
     * @param codeList
     * @param contentTypes
     */
    @Override
    public void deletePicAndMov(List<String> codeList, Integer... contentTypes) {
        List<CmsPicture> pictures = cmsPictureService.list(Wrappers.lambdaQuery(CmsPicture.class)
                .in(CmsPicture::getContentCode, codeList)
                .in(CmsPicture::getContentType, contentTypes)
        );

        //删除SP数据
        bmsPictureService.remove(Wrappers.lambdaQuery(BmsPicture.class)
                .in(BmsPicture::getCmsPictureId,
                        pictures.stream().map(CmsPicture::getId).collect(Collectors.toList())));

        log.info("删除内容相关图片");
        cmsPictureService.removeByIds(
                pictures.stream().map(CmsPicture::getId).collect(Collectors.toList()));

        pictures.forEach(cmsPicture -> {
            log.info("自动发布，删除图片 code = {}， file = {}", cmsPicture.getCode(),
                    cmsPicture.getFileUrl());
            FtpAnalysisUtil.DelFtp(cmsPicture.getFileUrl());
        });

        List<CmsResource> resources = cmsResourceService.list(
                Wrappers.lambdaQuery(CmsResource.class)
                        .in(CmsResource::getContentCode, codeList)
                        .in(CmsResource::getContentType, contentTypes)
        );

        log.info("删除内容相关视频");
        cmsResourceService.remove(Wrappers.lambdaQuery(CmsResource.class)
                .in(CmsResource::getContentCode, codeList)
                .in(CmsResource::getContentType, contentTypes));
        cmsMovieService.remove(Wrappers.lambdaQuery(CmsMovie.class)
                .in(CmsMovie::getContentCode, codeList)
                .in(CmsMovie::getContentType, contentTypes));

        resources.forEach(resource -> {
            log.info("自动发布，删除视频 code = {}， file = {}", resource.getCode(),
                    resource.getFileUrl());
            FtpAnalysisUtil.DelFtp(resource.getFileUrl());
        });
//Arrays.stream(contentTypes).anyMatch(e -> e.equals( ContentTypeEnum.TELEPLAY.getValue()))

        if (contentTypes.length == 2) {
            //删除子集图片
            List<CmsProgram> cmsProgramList = cmsProgramService.list(
                    Wrappers.lambdaQuery(CmsProgram.class)
                            .in(CmsProgram::getSeriesCode, codeList)
            );
            List<String> cmsProgramCodeList = cmsProgramList.stream().map(CmsProgram::getCode)
                    .collect(Collectors.toList());
            List<CmsPicture> cmsPictures = cmsPictureService.list(
                    Wrappers.lambdaQuery(CmsPicture.class)
                            .in(CmsPicture::getContentCode, cmsProgramCodeList)
                            .in(CmsPicture::getContentType, ContentTypeEnum.SUBSET.getValue())
            );

            //删除SP数据
            bmsPictureService.remove(Wrappers.lambdaQuery(BmsPicture.class)
                    .in(BmsPicture::getCmsPictureId,
                            pictures.stream().map(CmsPicture::getId).collect(Collectors.toList())));

            log.info("删除子集内容相关图片");
            cmsPictureService.removeByIds(
                    pictures.stream().map(CmsPicture::getId).collect(Collectors.toList()));

            pictures.forEach(cmsPicture -> {
                log.info("自动发布，删除子集图片 code = {}， file = {}", cmsPicture.getCode(),
                        cmsPicture.getFileUrl());
                FtpAnalysisUtil.DelFtp(cmsPicture.getFileUrl());
            });
        }

    }

    @Override
    public List<BmsContent> listBySeriesIds(List<Long> idList) {
        LambdaQueryWrapper<BmsContent> query = Wrappers.lambdaQuery();
        query.in(BmsContent::getCmsContentId, idList)
                .and(qw -> qw.eq(BmsContent::getContentType, ContentTypeEnum.TELEPLAY.getValue())
                        .or().eq(BmsContent::getContentType, ContentTypeEnum.EPISODES.getValue()));
        return list(query);
    }

    private LambdaQueryWrapper<BmsContent> initWrapper(BmsContentQueryReq req) {
        BmsContent po = BeanUtil.copyProperties(req, BmsContent.class);
        po.setOutPassageIds(req.getOutPassageId());
        //开始、结束时间格式化
        DateFormatCompletionDto dateFormatCompletionDto = new DateFormatCompletionDto().setStartTime(
                req.getStartUpdateTime()).setEndTime(req.getEndUpdateTime());
        Boolean aBoolean = TimeResolutionUtil.dateTimeCompare(dateFormatCompletionDto);
        LambdaQueryWrapper<BmsContent> wrapper = Wrappers.lambdaQuery(BmsContent.class)
                .notExists(
                        LonelyContentEnum.LONELY_CONTENT.getCode().equals(req.getLonelyContent()),
                        "select 1 from bms_category_content b where bms_content.id=b.bms_content_id")
                .notExists(
                        LonelyContentEnum.LONELY_CONTENT.getCode().equals(req.getLonelyContent()),
                        "select 1 from bms_package_content c where bms_content.id=c.bms_content_id")
                .eq(ObjectUtils.isNotEmpty(po.getSpId()), BmsContent::getSpId, po.getSpId())
                .eq(ObjectUtils.isNotEmpty(po.getCpId()), BmsContent::getCpId, po.getCpId())
                .eq(ObjectUtils.isNotEmpty(po.getPgmCategoryId()), BmsContent::getPgmCategoryId,
                        po.getPgmCategoryId())
                .eq(ObjectUtils.isNotEmpty(po.getLockStatus()), BmsContent::getLockStatus,
                        po.getLockStatus())
                .eq(ObjectUtils.isNotEmpty(po.getDefinitionFlag()), BmsContent::getDefinitionFlag,
                        po.getDefinitionFlag())
                .like(ObjectUtils.isNotEmpty(po.getOutPassageIds()), BmsContent::getOutPassageIds,
                        po.getOutPassageIds())
                .eq(ObjectUtils.isNotEmpty(po.getStatus()), BmsContent::getStatus, po.getStatus())
                .eq(ObjectUtils.isNotEmpty(po.getPublishStatus()), BmsContent::getPublishStatus,
                        po.getPublishStatus())
                .eq(ObjectUtils.isNotEmpty(po.getOpCheckStatus()), BmsContent::getOpCheckStatus,
                        po.getOpCheckStatus())
                .eq(StringUtils.isNotBlank(req.getCmsContentCode()), BmsContent::getCmsContentCode,
                        po.getCmsContentCode())
                .ge(ObjectUtils.isNotEmpty(dateFormatCompletionDto.getStartTime()),
                        BmsContent::getUpdateTime, dateFormatCompletionDto.getStartTime())
                .le(ObjectUtils.isNotEmpty(dateFormatCompletionDto.getEndTime()),
                        BmsContent::getUpdateTime, dateFormatCompletionDto.getEndTime())
                .orderByDesc(BmsContent::getId);
        // 是否按别名查询
        if (org.springframework.util.StringUtils.hasText(req.getOriginalName())) {
            wrapper.like(BmsContent::getOriginalName, req.getOriginalName());
        } else {
            //未换行，模糊查询,换行后精确查询
            //判断name是否含有回车 若含有回车则进行精准多条查询
            if (req.getName() != null) {
                if (req.getName().contains("\n")) {
                    //防止name为空字符串以及只有回车的情况  进行再一次校验
                    req.setName(req.getName().trim());
                    if (StringUtils.isNotEmpty(req.getName())) {
                        String[] names = CommonUtils.getNames(req.getName());
                        if (names != null) {
                            wrapper.in(BmsContent::getName, Arrays.asList(names));
                        }
                    }
                } else {
                    String[] names = CommonUtils.getNames(req.getName());
                    if (names != null && names.length > 0) {
                        wrapper.like(BmsContent::getName, names[0]);
                    }
                }
            }
        }
        // 是否缺集
        Integer missedInfo = req.getMissingEpisodes();
        if (Objects.nonNull(missedInfo)) {
            // 不缺集查询
            if (missedInfo == 2) {
                wrapper.isNull(BmsContent::getMissedInfo);
            } else {
                // 缺集查询
                wrapper.isNotNull(BmsContent::getMissedInfo);
            }
        }
        if (Objects.nonNull(po.getContentType())) {
            List<Integer> typeList = new ArrayList<>();
            if (ContentTypeItemEnum.PROGRAM.getContentType().equals(po.getContentType())) {
                typeList.add(ContentTypeItemEnum.PROGRAM.getContentType());
                typeList.add(ContentTypeItemEnum.FLOWER.getContentType());
                wrapper.in(BmsContent::getContentType, typeList);
            }
            if (ContentTypeItemEnum.SERIES3.getContentType().equals(po.getContentType())) {
                typeList.add(ContentTypeItemEnum.SERIES3.getContentType());
                typeList.add(ContentTypeItemEnum.SERIES4.getContentType());
                wrapper.in(BmsContent::getContentType, typeList);
            }
        }
        // 租户权限过滤
        SecurityUser securityUser = JwtTokenUtil.getSecurityUser();
        List<SysTenant> sysTenants = new ArrayList<>();
        if (SysUserTypeEnum.TENANT.getCode().equals(securityUser.getType())) {
            sysTenants = sysTenantService.listByUserId(securityUser.getId());
        }
        if (ObjectUtils.isNotEmpty(sysTenants)) {
            List<SysTenant> spLimitList = sysTenants.stream()
                    .filter(sysTenant -> {
                        List<Long> spIdList = Arrays.stream(sysTenant.getSpIds()
                                        .split(SymbolConstant.COMMA))
                                .map(Long::parseLong).collect(Collectors.toList());
                        return spIdList.contains(req.getSpId());
                    }).collect(Collectors.toList());
            if (ObjectUtils.isNotEmpty(spLimitList)) {
                Set<Long> cpIdSet = new HashSet<>();
                for (SysTenant sysTenant : spLimitList) {
                    List<Long> cpIdList = Arrays.stream(sysTenant.getCpIds()
                                    .split(SymbolConstant.COMMA))
                            .map(Long::parseLong).collect(Collectors.toList());
                    cpIdSet.addAll(cpIdList);
                }
                if (ObjectUtils.isNotEmpty(cpIdSet)) {
                    wrapper.in(BmsContent::getCpId, cpIdSet);
                }
            }
        }
        return wrapper;
    }


    @Override
    public Object export(BmsContentQueryReq req) {

        long ms = System.currentTimeMillis();
        LambdaQueryWrapper<BmsContent> wrapper = initWrapper(req);
        ExportInfo<BmsContent> exportInfo = new ExportInfo<>();
        exportInfo.setOut(new ByteArrayOutputStream())
                .setSheetName(req.getSpName())
                .setQueryWrapper(wrapper)
                .setPojoClass(BmsContent.class)
                .setCacheKey(req.getSpId() + "_" + RedisKeyConstants.CONTENT_SELECT_EXPORT)
                .setBaseMapper(bmsContentMapper);
        ExcelTaskInfo excelTaskInfo = exportTask.startProcess(exportInfo).dowork();
        log.info("总导出时长为 {} ms", (System.currentTimeMillis() - ms));
        // result.toByteArray().toString();
        return JSON.toJSON(excelTaskInfo);
    }


    @Override//前置检查
    public boolean preCheck() {
        BmsContentRecycleDto args = getArgs(BmsContentRecycleDto.class);
        List<Long> contentIds = args.getBmsContentIds();
        if (ObjectUtil.isEmpty(contentIds)) {
            log.warn("contentIds 为空!");
            throw new BizException("内容id为空");
        }
        if (ObjectUtil.isNotEmpty(contentIds)) {
            List<Long> disableAssetIds = contentIds.stream()
                    .map(bmsContentMapper::selectById)
                    .filter(bmsContent -> bmsContent != null && bmsContent.getSpId() != null)
                    .filter(bmsContent -> {
                        SysSp cacheSp = redisService.getCacheMapValue(RedisKeyConstants.SYS_SP,
                                String.valueOf(bmsContent.getSpId()));
                        if (cacheSp == null || !StatusEnum.COME.getCode()
                                .equals(cacheSp.getStatus())) {
                            log.warn("当前域 {} 已禁用!", bmsContent.getSpName());
                            return true;
                        }
                        return false;
                    })
                    .map(BmsContent::getId)
                    .collect(Collectors.toList());
            if (!disableAssetIds.isEmpty()) {
                try {
                    this.update(Wrappers.<BmsContent>lambdaUpdate()
                            .set(BmsContent::getPublishStatus, PublishStatusEnum.DISABLE.getCode())
                            .in(BmsContent::getId, disableAssetIds));
                } catch (Exception e) {
                    log.error("更新内容:{} 发布状态失败.错误信息:{}", disableAssetIds,
                            e.getMessage());
                    throw new BizException("更新内容发布状态失败");
                }
                throw new BizException("当前域已禁用");
            }
        }
        RuleCondition condition = RuleCondition.create();
        //如果是SP侧的回收，需要判断所勾选的内容是否可以回收
        if (isSpRecycle()) {
            //是否能回收检查
            condition.and(RecycleStatusRule.init(BmsContent.class).data(contentIds)
                    .policy(CAN_NOT_RECYCLE));
        }
        log.info("单集剧集锁定状态");
        condition.and(LockStatusRule.init(BmsContent.class).data(contentIds))
                .execute().check();
        return true;
    }

    @Override
    public List<BmsContent> findCanRecycleList() {
        BmsContentRecycleDto args = getArgs(BmsContentRecycleDto.class);
        List<Long> ids = args.getBmsContentIds();
        return this.list(Wrappers.lambdaQuery(BmsContent.class)
                .select(BmsContent::getId, BmsContent::getContentType,
                        BmsContent::getSpId, BmsContent::getSpName,
                        BmsContent::getCmsContentId, BmsContent::getPublishStatus)
                // 跳过不需要回收的 发布失败|回收成功|待发布的
                .notIn(BmsContent::getPublishStatus, RecycleStatusRule.NOT_RECYCLE_LIST)
                .in(BmsContent::getId, ids));
    }

    @Override
    public boolean deleteRemain() {
        BmsContentRecycleDto args = getArgs(BmsContentRecycleDto.class);
        List<Long> ids = args.getBmsContentIds();
        if (ObjectUtil.isEmpty(ids)) {
            return false;
        }
        LambdaQueryWrapper<BmsContent> query = Wrappers.lambdaQuery(BmsContent.class)
                .select(BmsContent::getId, BmsContent::getContentType, BmsContent::getCmsContentId,
                        BmsContent::getSpId)
                //可以直接删除的状态
                .in(BmsContent::getPublishStatus, RecycleStatusRule.CAN_DELETE_LIST)
                .in(BmsContent::getId, ids);
        List<BmsContent> list = this.list(query);
        log.info("将单集和剧头下的图片都删除");
        {
            Map<Integer, List<BmsContent>> map = list.stream()
                    .collect(Collectors.groupingBy(BmsContent::getContentType));
            for (Map.Entry<Integer, List<BmsContent>> item : map.entrySet()) {
                Integer type = item.getKey();
                List<BmsContent> value = item.getValue();
                List<Long> tmp = value.stream().map(BmsContent::getId).collect(Collectors.toList());
                log.info("删除图片{}", tmp);
                this.setArgs(BmsPicRecycleDto.class, new BmsPicRecycleDto().setContentIds(tmp)
                        .setType(ContentTypeEnum.getByValue(type)));
                bmsPictureService.deleteRemain();
            }
        }
        log.info("找到剧头和系列片，并删除其下的子集");
        {
            Map<Long, List<BmsContent>> tmpMap =
                    list.stream().filter(i ->
                                    ContentTypeEnum.EPISODES.getValue().equals(i.getContentType()) ||
                                            ContentTypeEnum.TELEPLAY.getValue().equals(i.getContentType()))
                            .collect(Collectors.groupingBy(BmsContent::getCmsContentId));
            for (Map.Entry<Long, List<BmsContent>> item : tmpMap.entrySet()) {
                Long cmsContentId = item.getKey();
                List<BmsContent> value = item.getValue();
                List<Long> spIds = value.stream().map(BmsContent::getSpId)
                        .collect(Collectors.toList());
                this.setArgs(BmsProgramRecycleDto.class,
                        new BmsProgramRecycleDto().setCmsSeriesId(cmsContentId).setSpId(spIds));
                bmsProgramService.deleteRemain();
            }
        }
        //删除
        log.info("单集剧集删除");
        return this.remove(query);
    }

    @Override
    public List<Long> markDelete() {
        try {
            BmsContentRecycleDto args = getArgs(BmsContentRecycleDto.class);
            List<Long> ids = args.getBmsContentIds();
            if (ObjectUtil.isEmpty(ids)) {
                return null;
            }
            //更新
            LambdaUpdateWrapper<BmsContent> updateWrapper = Wrappers.lambdaUpdate(BmsContent.class)
                    .set(BmsContent::getStatus, StatusEnum.DELETE.getCode())
                    //where 将回收中的 但状态还没有被标记的进行标记删除
                    .in(BmsContent::getPublishStatus, RecycleStatusRule.CAN_MARK_DELETE_LIST)
                    .ne(BmsContent::getStatus, StatusEnum.DELETE.getCode())
                    .in(BmsContent::getId, ids);
            log.info("剧集标记为删除");
            this.update(updateWrapper);
        } catch (Exception e) {
            log.error(e.getMessage(), e);
        }
        return null;
    }

    @Override
    public boolean oneKeyRecycle() {
        try {
            //step1:前置检查
            boolean pass = this.preCheck();
            if (!pass) {
                return false;
            }

            log.info("判断栏目和产品包是否锁定");
            {
                BmsContentRecycleDto args = getArgs(BmsContentRecycleDto.class);
                List<Long> contentIds = args.getBmsContentIds();
                List<BmsCategoryContent> bccList = bmsCategoryContentService.getByContentIds(
                        contentIds);
                List<Long> categoryIds = bccList.stream().map(BmsCategoryContent::getCategoryId)
                        .collect(Collectors.toList());
                List<Long> bccIds = bccList.stream().map(BmsCategoryContent::getId)
                        .collect(Collectors.toList());
                setArgs(BmsCategoryContentRecycleDto.class,
                        new BmsCategoryContentRecycleDto().setIds(bccIds)
                                .setCategoryIds(categoryIds));
                log.info("CategoryContent 进行lock检查");
                bmsCategoryContentService.preCheck();
                // 判断产品包是否锁定
                List<BmsPackageContent> bpcList = bmsPackageContentService.getByContentIds(
                        contentIds);
                List<Long> packageIds = bpcList.stream().map(BmsPackageContent::getPackageId)
                        .collect(Collectors.toList());
                List<Long> bpcIds = bpcList.stream().map(BmsPackageContent::getId)
                        .collect(Collectors.toList());
                setArgs(BmsPackageContentRecycleDto.class,
                        new BmsPackageContentRecycleDto().setIds(bpcIds).setPackageIds(packageIds));
                log.info("PackageContent 进行lock检查");
                bmsPackageContentService.preCheck();
            }
            log.info("启动  栏目和内容关系 产品包和内容关系 的一键回收");
            {
                //启动  栏目和内容关系  的一键回收
                bmsCategoryContentService.oneKeyRecycle();
                //启动  产品包和内容关系  的一键回收
                bmsPackageContentService.oneKeyRecycle();
            }

            //step2:如果是CP侧的回收，需要删除所有不需要回收的剧头
            if (needDelete()) {
                log.info("CP侧的回收，需要删除所有不需要回收的剧头");
                this.deleteRemain();
            }
            //step3:找到可以回收的内容并下发回收工单
            {
                List<BmsContent> list = this.findCanRecycleList();
                log.info("单集剧集执行回收动作");
                oneKeyRecycleAction(list);
            }
        } catch (RuntimeException e) {
            log.error(e.getMessage(), e);
            throw e;
        } finally {
            //step5:标记回收中
            if (needDelete()) {
                this.markDelete();
            }
            if (isBreak(BmsContentRecycleDto.class)) {
                closeRecycle();
            }
        }
        return true;
    }

    private boolean oneKeyRecycleAction(List<BmsContent> list) {
        if (ObjectUtil.isEmpty(list)) {
            return true;
        }
        //单集集合
        List<BmsContent> filmList = list.stream()
                .filter(i -> ContentTypeEnum.FILM.getValue().equals(i.getContentType()))
                .collect(Collectors.toList());
        //剧集集合
        List<BmsContent> seriesList = list.stream()
                .filter(i -> ContentTypeEnum.TELEPLAY.getValue().equals(i.getContentType()))
                .collect(Collectors.toList());
        //系列片
        List<BmsContent> escList =
                list.stream()
                        .filter(i -> ContentTypeEnum.EPISODES.getValue().equals(i.getContentType()))
                        .collect(Collectors.toList());
        //找到所有的
        //List<Long> seriesIds = seriesList.stream().map(BmsContent::getCmsContentId).distinct().collect(Collectors.toList());
        if (ObjectUtil.isNotEmpty(seriesList)) {
            log.info("启动子集回收");
            oneKeyRecycleProgramAction(seriesList);
        }

        if (ObjectUtil.isNotEmpty(escList)) {
            log.info("启动子集回收");
            oneKeyRecycleProgramAction(escList);
        }
        Map<String, OutParamExpand> paramMap = new HashMap<>();
        paramMap.put(PublishParamTypeConstants.PRIORITY,
                new OutParamExpand().setPriority(PriorityEnums.GENERAL.getValue()));
        //删除关系
        log.info("发布单集的回收工单");
        sendRecycleOrder(filmList, ContentTypeEnum.FILM, paramMap);
        log.info("发布剧集的回收工单");
        sendRecycleOrder(seriesList, ContentTypeEnum.TELEPLAY, paramMap);
        log.info("系列片的回收工单");
        sendRecycleOrder(escList, ContentTypeEnum.EPISODES, paramMap);
        return true;
    }

    private boolean sendRecycleOrder(List<BmsContent> list, ContentTypeEnum type,
            Map<String, OutParamExpand> paramMap) {
        if (ObjectUtil.isEmpty(list)) {
            return true;
        }
        //按照sp进行分组
        Map<Long, List<BmsContent>> spMap = list.stream()
                .collect(Collectors.groupingBy(BmsContent::getSpId));
        for (Map.Entry<Long, List<BmsContent>> item : spMap.entrySet()) {
            Long spId = item.getKey();
            List<BmsContent> value = item.getValue();
            if (ObjectUtil.isNotEmpty(value)) {
                String spName = value.get(0).getSpName();
                List<Long> tmp = value.stream().map(BmsContent::getId).collect(Collectors.toList());
                this.sendContentOrder(ActionEnums.DELETE, type, tmp, spId, spName, paramMap);
            }
        }
        return true;
    }

    //剧集一键回收
    private boolean oneKeyRecycleProgramAction(List<BmsContent> seriesList) {
        if (ObjectUtil.isEmpty(seriesList)) {
            return true;
        }
        //根据cmsSeriesId进行分组
        Map<Long, List<BmsContent>> map = seriesList.stream()
                .collect(Collectors.groupingBy(BmsContent::getCmsContentId));
        for (Map.Entry<Long, List<BmsContent>> item : map.entrySet()) {
            List<Long> spIds = item.getValue().stream().map(BmsContent::getSpId).distinct()
                    .collect(Collectors.toList());
            bmsProgramService.setArgs(BmsProgramRecycleDto.class, new BmsProgramRecycleDto()
                            .setSpId(spIds).setCmsSeriesId(item.getKey()).setPreCheckPass(true))
                    .oneKeyRecycle();
        }
        return true;
    }

    @Override
    public boolean updatePublishStatus(List<SubOrderObjectsEntity> orderObjectsEntities,
            List<Long> spIdList) {
        Integer result = bmsContentMapper.updatePublishStatus(orderObjectsEntities, spIdList);
        return result >= 1;
    }

    @Override
    public CommonResponse<Boolean> priortyPublish(List<Long> ids,
            Map<String, OutParamExpand> paramMap) {
        boolean result = this.publish(ids, false, null, paramMap);
        return result ? CommonResponse.success(result) : CommonResponse.commonfail("发布失败");
    }

    private void checkContent(List<Long> ids) {
        boolean schedule = isSchedule();
        Map<String, String> contentFilterCpMap = nacosConfig.getMapping();
        List<String> filterCpNameList = new ArrayList();

        List<BmsContent> filterList = this.list(Wrappers.lambdaQuery(BmsContent.class)
                .select(BmsContent::getCpName)
                .in(BmsContent::getId, ids));
        List<String> cpNameList = filterList.stream().map(BmsContent::getCpName).distinct()
                .collect(Collectors.toList());
        if (contentFilterCpMap != null || contentFilterCpMap.size() != 0) {
            filterCpNameList = new ArrayList<String>(contentFilterCpMap.values());
        }
        if (checkCpName(cpNameList, filterCpNameList)) {
            throw new CommonResponseException(
                    "所属cp为：" + com.alibaba.nacos.common.utils.StringUtils.join(filterCpNameList,
                            ",") + " 的媒资不可与其他所属cp的媒资同时进行发布");
        }
        //检测所有的媒资状态是否是可发布的
        RuleCondition.create()
                //锁定判断
                .and(LockStatusRule.init(BmsContent.class).data(ids))
                //待发布检查
                .and(PublishStatusRule.init(BmsContent.class).data(ids).policy(CAN_PUBLISH))
                //op审核状态
                .and(OpCheckRule.init(BmsContent.class).data(ids))
                //查询是否有媒资 没有关联视频
                .and(!cpNameList.contains(contentFilterCpMap.get("MovieCheckRule")),
                        MovieCheckRule.init(BmsContent.class).data(ids))
                //检测是否有定时发布
                .and(!schedule, TimePublishCheckRule.init(BmsContent.class).data(ids))
                .execute().check();
    }

    @Override
    public boolean publishRelation(List<Long> ids, Map<String, OutParamExpand> paramMap) {
        //查询出所有可以发布的栏目 包 关系
        List<BmsCategoryContent> categoryContents = bmsCategoryContentService.getByContentIds(ids);
        List<Long> categoryContentsIds = new ArrayList<>();
        List<Long> packageContentsIds = new ArrayList<>();
        List<Long> categoryIds = new ArrayList<>();
        List<Long> packageIds = new ArrayList<>();
        if (categoryContents.size() > 0) {
            categoryContentsIds = categoryContents.parallelStream().filter(bmsCategoryContent ->
                            PublishStatusRule.CAN_PUBLISH_LIST.contains(
                                    bmsCategoryContent.getPublishStatus())).map(BmsCategoryContent::getId)
                    .collect(Collectors.toList());
            if (categoryContentsIds.size() > 0) {
                categoryIds = categoryContents.parallelStream().filter(bmsCategoryContent ->
                                PublishStatusRule.CAN_PUBLISH_LIST.contains(
                                        bmsCategoryContent.getPublishStatus()))
                        .map(BmsCategoryContent::getCategoryId).collect(Collectors.toList());
                RuleCondition.create()
                        //检查栏目和内容是否有锁定状态
                        .and(LockStatusRule.init(BmsContent.class).data(ids))
                        .and(LockStatusRule.init(BmsCategory.class).data(categoryIds))
                        //检查栏目的发布状态 是否是发布成功
                        .and(PublishStatusRule.init(BmsCategory.class).data(categoryIds)
                                .policy(PUBLISH_SUCCESS))
                        //检查内容的发布状态 是否是发布成功
                        .and(PublishStatusRule.init(BmsContent.class).data(ids)
                                .policy(PUBLISH_SUCCESS))
                        //检查关系是否都是 待发布 检查
                        .and(PublishStatusRule.init(BmsCategoryContent.class)
                                .data(categoryContentsIds).col(BmsCategoryContent::getCategoryName)
                                .policy(CAN_PUBLISH))
                        //不是定时发布才进行 检测是否有定时发布
                        .and(true, TimePublishCheckRule.init(BmsCategoryContent.class)
                                .data(categoryContentsIds))
                        .execute().check();
            }
        }
        List<BmsPackageContent> packageContents = bmsPackageContentService.getByContentIds(ids);
        if (packageContents.size() > 0) {
            packageContentsIds = packageContents.parallelStream().filter(bmsPackageContent ->
                            PublishStatusRule.CAN_PUBLISH_LIST.contains(
                                    bmsPackageContent.getPublishStatus())).map(BmsPackageContent::getId)
                    .collect(Collectors.toList());
            if (packageContentsIds.size() > 0) {
                packageIds = packageContents.parallelStream().filter(bmsPackageContent ->
                                PublishStatusRule.CAN_PUBLISH_LIST.contains(
                                        bmsPackageContent.getPublishStatus()))
                        .map(BmsPackageContent::getPackageId).collect(Collectors.toList());
                RuleCondition.create()
                        //产品包是否锁定
                        .and(LockStatusRule.init(BmsPackage.class).data(packageIds))
                        //内容是否锁定
                        //.and(LockStatusRule.init(BmsContent.class).data(contentIdList))
                        //检测是否有定时发布
                        .and(true, TimePublishCheckRule.init(BmsPackageContent.class)
                                .data(packageContentsIds))
                        //检查产品包内容关系是否 符合发布条件
                        .and(PublishStatusRule.init(BmsPackageContent.class)
                                .col(BmsPackageContent::getContentName)
                                .policy(PublishCheck.CAN_PUBLISH).data(packageContentsIds))
                        //检查内容是否发布成功
                        .and(PublishStatusRule.init(BmsContent.class)
                                .policy(PublishCheck.PUBLISH_SUCCESS).data(ids))
                        //检查产品包是否发布成功
                        .and(PublishStatusRule.init(BmsPackage.class)
                                .policy(PublishCheck.PUBLISH_SUCCESS).data(packageIds))
                        .execute().check();
            }
        }
        if (categoryIds.size() > 0) {
            setCheckFlag();
            bmsCategoryContentService.publish(categoryContentsIds, null, paramMap);
        }
        if (packageIds.size() > 0) {
            setCheckFlag();
            bmsPackageContentService.relationPublish(packageContentsIds, packageIds, ids, null,
                    paramMap);
        }
        return true;
    }

    /**
     * 根据状态和类型查询内容
     * @param publishStatusList
     * @param contentTypeList
     * @param pageNo
     * @param pageSize
     * @return
     */
    @Override
    public Page<BmsContent> listContentByStatusAndType(List<Integer> publishStatusList, List<Integer> contentTypeList, Integer pageNo, Integer pageSize) {
        Page<BmsContent> page = new Page<>(pageNo, pageSize);

        // 构建查询条件
        LambdaQueryWrapper<BmsContent> queryWrapper = Wrappers.<BmsContent>lambdaQuery()
                .in(CollectionUtil.isNotEmpty(publishStatusList),
                        BmsContent::getPublishStatus, publishStatusList)
                .in(CollectionUtil.isNotEmpty(contentTypeList),
                        BmsContent::getContentType, contentTypeList);

        // 执行查询并返回分页结果
        return this.page(page, queryWrapper);
    }

    /**
     * 根据spId和seriesCode查询内容
     * @param spId
     * @param seriesCode
     * @return
     */
    @Override
    public BmsContent getByCode(Long spId, String seriesCode) {
        try {
            BmsContent bmsContent = this.getOne(Wrappers.lambdaQuery(BmsContent.class)
                    .eq(BmsContent::getSpId, spId)
                    .eq(BmsContent::getCmsContentCode, seriesCode));
            if (bmsContent == null) {
                log.warn("未找到对应的内容，spId: {}, seriesCode: {}", spId, seriesCode);
                throw new BizException("未找到对应的内容");
            }
            return bmsContent;
        } catch (Exception e) {
            log.error("根据spId和seriesCode查询内容时发生异常，spId: {}, seriesCode: {}, 异常信息: {}", spId, seriesCode, e.getMessage(), e);
            throw new BizException("查询内容失败，请稍后重试");
        }
    }

    private void checkDoubleContent(List<Long> ids) {
        Map<String, String> contentFilterCpMap = nacosConfig.getMapping();
        List<String> filterCpNameList = new ArrayList();
        List<BmsContent> filterList = this.list(Wrappers.lambdaQuery(BmsContent.class)
                .select(BmsContent::getCpName)
                .in(BmsContent::getId, ids));
        List<String> cpNameList = filterList.stream().map(BmsContent::getCpName).distinct()
                .collect(Collectors.toList());
        if (contentFilterCpMap != null || contentFilterCpMap.size() != 0) {
            filterCpNameList = new ArrayList<String>(contentFilterCpMap.values());
        }
        if (checkCpName(cpNameList, filterCpNameList)) {
            throw new CommonResponseException(
                    "所属cp为：" + com.alibaba.nacos.common.utils.StringUtils.join(filterCpNameList,
                            ",") + " 的媒资不可与其他所属cp的媒资同时进行发布");
        }
        //检测所有的媒资状态是否是可发布的
        RuleCondition.create()
                //锁定判断
                .and(LockStatusRule.init(BmsContent.class).data(ids))
                //待发布检查
                .and(PublishStatusRule.init(BmsContent.class).data(ids).policy(CAN_PUBLISH))
                //op审核状态
                .and(OpCheckRule.init(BmsContent.class).data(ids))
                //查询是否有媒资 没有关联视频
                .and(!cpNameList.contains(contentFilterCpMap.get("MovieCheckRule")),
                        MovieCheckRule.init(BmsContent.class).data(ids))
                .execute().check();
    }
}
