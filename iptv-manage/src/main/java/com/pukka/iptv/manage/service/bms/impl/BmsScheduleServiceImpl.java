package com.pukka.iptv.manage.service.bms.impl;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.metadata.TableInfo;
import com.baomidou.mybatisplus.core.metadata.TableInfoHelper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.google.common.collect.Lists;
import com.pukka.iptv.common.base.constant.PublishParamTypeConstants;
import com.pukka.iptv.common.base.enums.*;
import com.pukka.iptv.common.base.exception.BizException;
import com.pukka.iptv.common.base.exception.CommonResponseException;
import com.pukka.iptv.common.base.vo.CommonResponse;
import com.pukka.iptv.common.core.constant.SymbolConstant;
import com.pukka.iptv.common.data.model.OutParamExpand;
import com.pukka.iptv.common.data.model.OutScheduledTask;
import com.pukka.iptv.common.data.model.bms.BmsChannel;
import com.pukka.iptv.common.data.model.bms.BmsSchedule;
import com.pukka.iptv.common.data.model.order.SubOrderObjectsEntity;
import com.pukka.iptv.common.data.util.Tuple2;
import com.pukka.iptv.common.data.vo.CodeList;
import com.pukka.iptv.common.data.vo.bms.BmsSchedulePageVO;
import com.pukka.iptv.common.data.vo.req.BmsSchedulePageReq;
import com.pukka.iptv.manage.mapper.bms.BmsBaseMapper;
import com.pukka.iptv.manage.mapper.bms.BmsScheduleMapper;
import com.pukka.iptv.manage.service.bms.BmsChannelService;
import com.pukka.iptv.manage.service.bms.BmsSchedulePublishApi;
import com.pukka.iptv.manage.service.bms.BmsScheduleService;
import com.pukka.iptv.manage.service.bms.common.WorkOrderOperation;
import com.pukka.iptv.manage.service.common.ScheduledTasksUtil;
import com.pukka.iptv.manage.service.condition.RuleCondition;
import com.pukka.iptv.manage.service.condition.rule.PublishStatusRule;
import com.pukka.iptv.manage.service.condition.rule.RecycleStatusRule;
import com.pukka.iptv.manage.service.condition.rule.TimePublishCheckRule;
import com.pukka.iptv.manage.service.condition.rule.enums.PublishCheck;
import com.pukka.iptv.manage.service.condition.rule.enums.RecycleCheck;
import com.pukka.iptv.manage.service.sys.OutScheduledTasksService;
import com.pukka.iptv.manage.util.CommonUtils;
import com.pukka.iptv.manage.util.DateTooles;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.text.ParseException;
import java.util.*;
import java.util.stream.Collectors;

import static com.pukka.iptv.common.base.enums.ContentTypeEnum.SCHEDULE;
import static com.pukka.iptv.common.base.enums.PublishStatusEnum.PUBLISH;
import static com.pukka.iptv.common.base.enums.PublishStatusEnum.PUBLISHING;
import static com.pukka.iptv.manage.service.condition.rule.enums.PublishCheck.*;

/**
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2021/9/9
 */
@Service
@Slf4j
public class BmsScheduleServiceImpl extends ServiceImpl<BmsScheduleMapper, BmsSchedule> implements
        BmsScheduleService, BmsSchedulePublishApi {

    @Autowired
    private BmsScheduleMapper bmsScheduleMapper;

    @Autowired
    private BmsChannelService bmsChannelService;

    @Autowired
    private WorkOrderOperation workOrderOperation;

    @Autowired
    private ScheduledTasksUtil scheduledTasksUtil;

    @Autowired
    private OutScheduledTasksService outScheduledTasksService;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public CommonResponse<Boolean> schedulePublish(List<Long> bmsScheduleIds, Integer doSchedule,
                                                   String scheduleTime) throws ParseException {
        //根据节目单ids获取到逻辑频道id，判断逻辑频道是否已发布
        //若逻辑频道为已发布状态则检测状态是否为可发布的状态
        List<BmsSchedule> schedules = bmsScheduleMapper.selectBatchIds(bmsScheduleIds);
        List<Long> channelsIds = schedules.stream().map(BmsSchedule::getCmsChannelId)
                .collect(Collectors.toList()).stream().distinct().collect(Collectors.toList());
        List<Long> spIds = schedules.stream().map(BmsSchedule::getSpId)
                .collect(Collectors.toList()).stream().distinct().collect(Collectors.toList());

        List<Long> channelByCmsContentId = bmsChannelService.getChannelByCmsContentId(channelsIds,
                spIds);
        if (channelByCmsContentId.size() == 0) {
            throw new BizException("未查询到节目单所绑定的频道数据");
        }
        boolean schedule = isSchedule();
        RuleCondition.create()
                .and(PublishStatusRule.init(BmsChannel.class).policy(PUBLISH_SUCCESS)
                        .data(channelByCmsContentId))
                .and(PublishStatusRule.init(BmsSchedule.class).col(BmsSchedule::getProgramName)
                        .policy(CAN_PUBLISH).data(bmsScheduleIds))
                .and(!schedule, TimePublishCheckRule.init(BmsSchedule.class).data(bmsScheduleIds))
                .execute().check();
        //当为可发布的状态则进行发布
        if (IsTimedEnums.IS_TIMED.getCode().equals(doSchedule)) {
            //添加到定时发布任务表
            boolean scheduledTasks = outScheduledTasksService.createScheduledTasks(bmsScheduleIds,
                    SCHEDULE.getValue(), scheduleTime, PriorityEnums.GENERAL.getValue());
            if (!scheduledTasks) {
                log.error("节目单添加到定时发布任务表outScheduledTasks失败，ids:{}", bmsScheduleIds);
                throw new BizException("定时发布任务添加失败");
            }
            // 当为定时发布时加入定时器中进行定时发布
            schedules.forEach(p -> {
                        try {
                            p.setTimedPublish(DateTooles.ConvertFormatToTime(DateTooles.YYYY_MM_DD_HH_mm_ss,
                                    scheduleTime));
                            p.setTimedStatus(IsTimedEnums.IS_TIMED.getCode());
                        } catch (ParseException e) {
                            e.printStackTrace();
                        }
                    }
            );
            this.updateBatchById(schedules);
            log.info("新增定时发布节目单名：{}，定时时间：{}", StringUtils.join(
                            schedules.stream().map(BmsSchedule::getProgramName)
                                    .collect(Collectors.toList()), ","),
                    DateTooles.ConvertFormatToTime(DateTooles.YYYY_MM_DD_HH_mm_ss, scheduleTime));
            return CommonResponse.success(true);
        }
        boolean result = schedulePublishAndRollback(schedules, ActionEnums.REGIST,
                channelByCmsContentId);
        return CommonResponse.success(result);
    }

    /**
     * @param schedules
     * @param isPublish
     * @return
     */
    private Map<String, String> createSendMap(List<BmsSchedule> schedules, Boolean isPublish) {
        Map<String, String> sendMap = new HashMap<>();
        schedules.forEach(s -> {
            if (isPublish) {
                if (PublishStatusRule.CAN_UPDATE_LIST.contains(s.getPublishStatus())) {
                    sendMap.put(s.getId() + "", ActionEnums.UPDATE.getCode() + "");
                }
                if (PublishStatusRule.PUBLISH_LIST.contains(s.getPublishStatus())) {
                    sendMap.put(s.getId() + "", ActionEnums.REGIST.getCode() + "");
                }
            } else {
                sendMap.put(s.getId() + "", ActionEnums.DELETE.getCode() + "");
            }
        });
        return sendMap;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public CommonResponse<Boolean> scheduleRollback(List<Long> bmsScheduleIds) {
        RuleCondition.create()
                .and(RecycleStatusRule.init(BmsSchedule.class).data(bmsScheduleIds)
                        .col(BmsSchedule::getProgramName).policy(RecycleCheck.CAN_NOT_RECYCLE))
                .execute().check();
        List<BmsSchedule> bmsSchedules = bmsScheduleMapper.selectBatchIds(bmsScheduleIds);
        List<Long> channelsIds = bmsSchedules.stream().map(BmsSchedule::getCmsChannelId)
                .collect(Collectors.toList()).stream().distinct().collect(Collectors.toList());
        List<Long> spIds = bmsSchedules.stream().map(BmsSchedule::getSpId)
                .collect(Collectors.toList()).stream().distinct().collect(Collectors.toList());

        List<Long> channelByCmsContentId = bmsChannelService.getChannelByCmsContentId(channelsIds,
                spIds);
        //回收接口
        schedulePublishAndRollback(bmsSchedules, ActionEnums.DELETE, channelByCmsContentId);
        return CommonResponse.success(true);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public Boolean resetPublishStatus(List<Long> idList) {
        RuleCondition.create()
                .and(PublishStatusRule.init(BmsSchedule.class).col(BmsSchedule::getProgramName)
                        .policy(PublishCheck.MUST_ING).data(idList))
                .execute().check();
        List<BmsSchedule> bmsSchedules = bmsScheduleMapper.selectBatchIds(idList);
        if (bmsSchedules.size() == 0) {
            return false;
        }
        bmsSchedules.forEach(c -> {
                    if (PUBLISHING.getCode().equals(c.getPublishStatus())) {
                        c.setPublishDescription("");
                    }
                    c.setPublishStatus(CommonUtils.resetPublishStatus(c.getPublishStatus()));

                }
        );
        this.updateBatchById(bmsSchedules);
        return true;
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public Boolean updatePublishStatus(List<Long> idList, Integer updatePublishStatus) {
        List<BmsSchedule> bmsSchedules = bmsScheduleMapper.selectBatchIds(idList);
        if (bmsSchedules.size() == 0) {
            return false;
        }
        LambdaUpdateWrapper<BmsSchedule> lambdaUpdateWrapper = new LambdaUpdateWrapper<>();
        lambdaUpdateWrapper.in(BmsSchedule::getId, idList)
                .set(BmsSchedule::getPublishDescription, "")
                .set(BmsSchedule::getTimedPublish, null)
                .set(BmsSchedule::getTimeDescription, "")
                .set(BmsSchedule::getPublishStatus, updatePublishStatus)
                .set(BmsSchedule::getTimedStatus, IsTimedEnums.NO_TIMED.getCode());
        this.update(lambdaUpdateWrapper);
        //更新定时发布状态
        outScheduledTasksService.finishScheduledTasks(idList,
                SCHEDULE.getValue());
        return true;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean setStatus(List<Long> ids, StatusEnum status) {
        RuleCondition.create()
                //发布状态为 发布中的节目单不可修改
                .and(PublishStatusRule.init(BmsSchedule.class).col(BmsSchedule::getProgramName)
                        .data(ids).policy(ING)).execute().check();
        int i = setStatus(BmsSchedule.class, ids, status);
        return i != 0;

    }

    @Autowired
    private BmsBaseMapper bmsBaseMapper;

    public int setStatus(Class<?> table, List<Long> ids, StatusEnum status) {
        TableInfo tableInfo = TableInfoHelper.getTableInfo(table);
        return bmsBaseMapper.setStatus(tableInfo.getTableName(), ids, status);
    }

    @Override
    public IPage<BmsSchedulePageVO> getChannelsPage(BmsSchedulePageReq bmsSchedulePageReq) {
        return bmsScheduleMapper.getSchedulesPage(bmsSchedulePageReq);
    }

    private boolean schedulePublishAndRollback(List<BmsSchedule> schedules, ActionEnums actionEnums,
                                               List<Long> channelIds) {
        Map<String, String> sendMap = createSendMap(schedules,
                !ActionEnums.DELETE.equals(actionEnums));
        Map<String, OutParamExpand> paramMap = getParamMap();
        paramMap.put(PublishParamTypeConstants.ORDER_CHANNELSCHEDULE,
                new OutParamExpand().setSpareMap(sendMap));
        return workOrderOperation.send(actionEnums, SCHEDULE, channelIds, null,
                schedules.get(0).getSpId(), schedules.get(0).getSpName(),
                (success, publishStatus, description) -> {
                    //根据传过来的动作以及调用接口返回的状态进行发布状态的转换
                    schedules.forEach(s -> {
                        Long publishId = s.getId();
                        sendMap.forEach((id, action) -> {
                            if (publishId.equals(Long.parseLong(id))) {
                                ActionEnums value = ActionEnums.getByValue(
                                        Integer.parseInt(action));
                                s.setPublishStatus(CommonUtils.getEnumByAction(value, success));
                                s.setPublishTime(new Date());
                                s.setTimeDescription("");
                                s.setPublishDescription(success ? "" : description);
                            }
                        });
                    });
                    this.updateBatchById(schedules);
                }, paramMap);
    }

    @Override
    public List<BmsSchedule> getTimingPublish() {
        LambdaQueryWrapper<BmsSchedule> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(BmsSchedule::getTimedStatus, IsTimedEnums.IS_TIMED.getCode())
                .le(BmsSchedule::getTimedPublish, new Date()).last("limit 100");
        return bmsScheduleMapper.selectList(wrapper);
    }

    @Override
    public boolean timedPublish() {
        List<OutScheduledTask> scheduledTasks = outScheduledTasksService.getScheduledTasks(
                SCHEDULE.getValue());
        if (ObjectUtil.isEmpty(scheduledTasks)) {
            return true;
        }
        LambdaQueryWrapper<BmsSchedule> wrapper = new LambdaQueryWrapper<>(BmsSchedule.class)
                .in(BmsSchedule::getId, scheduledTasks.stream().map(OutScheduledTask::getContentId)
                        .collect(Collectors.toList()));
        List<BmsSchedule> timingPublish = this.list(wrapper);
        if (CollectionUtil.isNotEmpty(timingPublish)) {
            //添加媒资库不存在定时任务校验
            List<Long> ids = timingPublish.stream().map(BmsSchedule::getId).collect(Collectors.toList());
            List<Long> filter = scheduledTasksUtil.filterScheduledTask(
                    scheduledTasks, ids);
            if (CollectionUtil.isNotEmpty(filter)) {
                //更新状态
                outScheduledTasksService.finishScheduledTasks(filter,
                        ContentTypeEnum.SCHEDULE.getValue());
            }
            setParam(PublishParamTypeConstants.PRIORITY, new OutParamExpand().setPriority(0));
            //根据sp为一级维度，频道为二级维度对子集进行分类
            Map<Long, Map<Long, List<BmsSchedule>>> publishMap = timingPublish.stream()
                    .collect(Collectors.groupingBy(BmsSchedule::getSpId,
                            Collectors.groupingBy(BmsSchedule::getCmsChannelId)));
            publishMap.forEach((spId, cmsChannelMap) -> {
                cmsChannelMap.forEach((k, v) -> {
                    List<Long> bmsScheduleIds = v.stream().map(BmsSchedule::getId)
                            .collect(Collectors.toList());
                    //添加定时发布任务状态双重校验
                    Iterator<Long> iterator = bmsScheduleIds.iterator();
                    while (iterator.hasNext()) {
                        Long item = iterator.next();
                        try {
                            scheduleCheck(Collections.singletonList(item));
                        } catch (Exception e) {
                            log.error("定时发布任务状态双重校验失败", e);
                            //更新状态
                            outScheduledTasksService.finishScheduledTasks(Collections.singletonList(item),
                                    SCHEDULE.getValue());
                            this.update(new LambdaUpdateWrapper<BmsSchedule>()
                                    .eq(BmsSchedule::getId, item)
                                    .set(BmsSchedule::getTimeDescription, "定时发布状态校验失败")
                                    .set(BmsSchedule::getTimedStatus,
                                            IsTimedEnums.NO_TIMED.getCode()));
                            iterator.remove();
                        }
                    }
                    if (CollectionUtils.isEmpty(bmsScheduleIds)) {
                        log.info("定时发布任务状态双重校验结束，无可发布内容");
                        return;
                    }
                    setScheduleFlag();
                    LambdaUpdateWrapper<BmsSchedule> scheduleWrapper = new LambdaUpdateWrapper();
                    scheduleWrapper.in(BmsSchedule::getId, bmsScheduleIds);
                    scheduleWrapper.set(BmsSchedule::getTimedPublish, null);
                    scheduleWrapper.set(BmsSchedule::getTimedStatus,
                            IsTimedEnums.NO_TIMED.getCode());
                    try {
                        CommonResponse<Boolean> response = schedulePublish(bmsScheduleIds,
                                IsTimedEnums.NO_TIMED.getCode(), null);
                        if (!response.getData()) {
                            scheduleWrapper.set(BmsSchedule::getTimeDescription,
                                    response.getMessage());
                        }
                    } catch (BizException bizException) {
                        scheduleWrapper.set(BmsSchedule::getTimeDescription,
                                bizException.getMessage());
                    } catch (Exception e) {
                        scheduleWrapper.set(BmsSchedule::getTimeDescription,
                                "定时发布失败，程序异常");
                    } finally {
                        //更新定时发布状态
                        outScheduledTasksService.finishScheduledTasks(bmsScheduleIds,
                                SCHEDULE.getValue());
                        this.update(scheduleWrapper);
                    }

                });
            });
            clearParm();
        } else {
            //更新定时任务表
            outScheduledTasksService.finishScheduledTasks(scheduledTasks.stream().map(OutScheduledTask::getContentId).collect(Collectors.toList()),
                    SCHEDULE.getValue());
        }
        return true;
    }

    @Override
    public Tuple2<Boolean, String> deleteTiming(Long id) {
        BmsSchedule schedule = bmsScheduleMapper.selectById(id);
        if (PUBLISH.getCode().equals(schedule.getPublishStatus())) {
            return new Tuple2<>(false, "关系已发布无法取消");
        }
        //取消定时发布
        boolean b = outScheduledTasksService.deleteScheduledTask(id,
                SCHEDULE.getValue());
        if (!b) {
            log.error("节目单取消定时发布失败，id:{}", id);
            throw new BizException("取消定时发布失败");
        }
        if (schedule.getTimedPublish() != null) {
            schedule.setTimedPublish(null);
            schedule.setTimedStatus(IsTimedEnums.NO_TIMED.getCode());
            bmsScheduleMapper.updateById(schedule);
            return new Tuple2<>(true, "取消成功");
        }
        return new Tuple2<>(false, "取消失败");
    }

    @Override
    public List<BmsSchedule> checkTimed(List<Long> ids) {
        List<BmsSchedule> schedules = bmsScheduleMapper.selectBatchIds(ids);

        schedules.forEach(s -> {
                    if (IsTimedEnums.IS_TIMED.getCode().equals(s.getTimedStatus())) {
                        throw new BizException("请选择不含定时发布的节目单进行发布");
                    }
                }
        );
        return schedules;
    }

    @Override
    public boolean deleteByCodeAndSp(List<String> codeList, List<Long> spIdList,
                                     boolean isRollback) {
        if (isRollback) {
            bmsScheduleMapper.update(null, Wrappers.lambdaUpdate(BmsSchedule.class)
                    .set(BmsSchedule::getPublishStatus, PublishStatusEnum.ROLLBACK.getCode())
                    .in(BmsSchedule::getCmsScheduleCode, codeList)
                    .in(BmsSchedule::getSpId, spIdList)
            );
            return true;
        }
        bmsScheduleMapper.delete(Wrappers.lambdaQuery(BmsSchedule.class)
                .in(BmsSchedule::getCmsScheduleCode, codeList)
                .in(BmsSchedule::getSpId, spIdList)
        );
        return true;
    }

    /**
     * 回看节目单-一键回收
     *
     * @param bmsScheduleIds
     * @return
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public CommonResponse<Boolean> scheduleManageRollback(List<Long> bmsScheduleIds) {
        List<BmsSchedule> bmsSchedules = bmsScheduleMapper.selectBatchIds(bmsScheduleIds);
        //节目单位于发布中、回收中、更新中、待发布不能回收
        List<BmsSchedule> wrongList = bmsSchedules.stream().filter(bmsSchedule ->
                        bmsSchedule.getPublishStatus().equals(PublishStatusEnum.WAITPUBLISH.getCode())
                                || bmsSchedule.getPublishStatus().equals(PUBLISHING.getCode())
                                || bmsSchedule.getPublishStatus()
                                .equals(PublishStatusEnum.UPDATING.getCode())
                                || bmsSchedule.getPublishStatus()
                                .equals(PublishStatusEnum.ROLLBACKING.getCode()))
                .collect(Collectors.toList());
        List<BmsSchedulePageVO> transform = Lists.transform(wrongList, bmsSchedule -> {
            BmsSchedulePageVO bmsSchedulePageVO = new BmsSchedulePageVO();
            bmsSchedulePageVO.setProgramName(bmsSchedule.getProgramName());
            bmsSchedulePageVO.setSpName(bmsSchedule.getSpName());
            List<BmsChannel> channel = bmsChannelService.getChannelById(
                    bmsSchedule.getCmsChannelId(), bmsSchedule.getSpId());
            if (CollectionUtil.isNotEmpty(channel)) {
                bmsSchedulePageVO.setChannelName(channel.get(0).getName());
            }
            return bmsSchedulePageVO;
        });
        if (CollectionUtil.isNotEmpty(transform)) {
            List<String> wrongSchedules = transform.stream()
                    .map(bmsSchedulePageVO -> bmsSchedulePageVO.getProgramName()
                            + SymbolConstant.COMMA + bmsSchedulePageVO.getChannelName()
                            + SymbolConstant.COMMA + bmsSchedulePageVO.getSpName()
                            + SymbolConstant.COMMA + "发布状态不满足回收条件，请检查！")
                    .collect(Collectors.toList());
            String wrongMessage = String.join("<br />", wrongSchedules);
            throw new CommonResponseException(wrongMessage);
        }
        //把节目单进行分组，根据频道id，创建map对象，key为频道id，value为节目单集合
        //对map对象进行分组，根据spid，创建map对象，key为spid，value为map对象
        Map<Long, Map<Long, List<BmsSchedule>>> groupMap = bmsSchedules.stream().collect(
                Collectors.groupingBy(BmsSchedule::getCmsChannelId,
                        Collectors.groupingBy(BmsSchedule::getSpId)));
        //对map对象进行遍历，将value存入sendMap中
        Set<Long> cmsChannelIds = groupMap.keySet();
        try {
            for (Long cmsChannelId : cmsChannelIds) {
                Map<Long, List<BmsSchedule>> firstFloorMap = groupMap.get(cmsChannelId);
                Set<Long> spids = firstFloorMap.keySet();
                for (Long spid : spids) {
                    List<BmsSchedule> bmsScheduleList = firstFloorMap.get(spid);
                    String spName = bmsScheduleList.stream().findFirst().get().getSpName();
                    //获取频道信息
                    List<BmsChannel> channelById = bmsChannelService.getChannelById(cmsChannelId,
                            spid);
                    if (CollectionUtil.isEmpty(channelById)) {
                        throw new CommonResponseException(
                                String.format("未查询到节目单在 %s 域所绑定的频道数据", spName));
                    }
                    Long channelId = channelById.stream().findFirst().get().getId();
                    //组建sendMap
                    Map<String, String> sendMap = createSendMap(bmsScheduleList, false);
                    Map<String, OutParamExpand> paramMap = getParamMap();
                    paramMap.put(PublishParamTypeConstants.ORDER_CHANNELSCHEDULE,
                            new OutParamExpand().setSpareMap(sendMap));
                    //回调
                    workOrderOperation.send(ActionEnums.DELETE, SCHEDULE, new ArrayList<Long>() {{
                                add(channelId);
                            }}, null, spid, spName,
                            (success, publishStatus, description) -> {
                                List<Long> publishIds = bmsScheduleList.stream()
                                        .map(bmsSchedule -> bmsSchedule.getId())
                                        .collect(Collectors.toList());
                                sendMap.forEach((id, action) -> {
                                    if (publishIds.contains(Long.parseLong(id))) {
                                        ActionEnums value = ActionEnums.getByValue(
                                                Integer.parseInt(action));
                                        bmsScheduleList.forEach(
                                                bmsSchedule -> bmsSchedule.setPublishStatus(
                                                        CommonUtils.getEnumByAction(value,
                                                                success)));
                                        bmsScheduleList.forEach(
                                                bmsSchedule -> bmsSchedule.setPublishTime(
                                                        new Date()));
                                        bmsScheduleList.forEach(
                                                bmsSchedule -> bmsSchedule.setTimeDescription(""));
                                        bmsScheduleList.forEach(
                                                bmsSchedule -> bmsSchedule.setPublishDescription(
                                                        success ? "" : description));
                                    }
                                });
                                this.updateBatchById(bmsScheduleList);
                            }, paramMap);
                }
            }
            return CommonResponse.success(true);
        } catch (Exception exception) {
            log.error("回看节目单-一键回收操作失败，错误描述:{}", exception);
            throw new CommonResponseException(exception.getMessage());
        }
    }

    @Override
    public List<BmsSchedule> selectByChannelCodeAndStartDate(String channelCode, String startDate) {
        LambdaQueryWrapper<BmsSchedule> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.eq(BmsSchedule::getCmsChannelCode, channelCode)
                .eq(BmsSchedule::getStartDate, startDate);
        return this.list(queryWrapper);
    }

    @Override
    public List<BmsSchedule> getByCodeList(CodeList codeList) {
        return Optional.ofNullable(codeList.getCodes())
                .filter(codes -> !codes.isEmpty())
                .map(codes -> Wrappers.<BmsSchedule>lambdaQuery()
                        .in(BmsSchedule::getCmsScheduleCode, codes)
                        .eq(BmsSchedule::getSpId, codeList.getSpId()))
                .map(this::list)
                .orElse(Collections.emptyList());
    }

    @Override
    public boolean updatePublishStatus(List<SubOrderObjectsEntity> orderObjectsEntities,
                                       List<Long> spIdList) {
        int result = bmsScheduleMapper.updatePublishStatus(orderObjectsEntities, spIdList);
        return result >= 1;
    }

    /**
     * 节目单校验
     *
     * @param bmsScheduleIds
     */
    private void scheduleCheck(List<Long> bmsScheduleIds) {
        List<BmsSchedule> schedules = bmsScheduleMapper.selectBatchIds(bmsScheduleIds);
        List<Long> channelsIds = schedules.stream().map(BmsSchedule::getCmsChannelId)
                .collect(Collectors.toList()).stream().distinct().collect(Collectors.toList());
        List<Long> spIds = schedules.stream().map(BmsSchedule::getSpId)
                .collect(Collectors.toList()).stream().distinct().collect(Collectors.toList());
        List<Long> channelByCmsContentId = bmsChannelService.getChannelByCmsContentId(channelsIds,
                spIds);
        if (channelByCmsContentId.isEmpty()) {
            throw new BizException("未查询到节目单所绑定的频道数据");
        }
        RuleCondition.create()
                .and(PublishStatusRule.init(BmsChannel.class).policy(PUBLISH_SUCCESS)
                        .data(channelByCmsContentId))
                .and(PublishStatusRule.init(BmsSchedule.class).col(BmsSchedule::getProgramName)
                        .policy(CAN_PUBLISH).data(bmsScheduleIds))
                .execute().check();
    }

    /**
     * 不校验状态进行节目单回收
     * @param bmsScheduleIds
     * @return
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public CommonResponse<Boolean> unCheckScheduleManageRollback(List<Long> bmsScheduleIds) {
        //不进行状态校验，全部回收
        List<BmsSchedule> bmsSchedules = bmsScheduleMapper.selectBatchIds(bmsScheduleIds);
        //把节目单进行分组，根据频道id，创建map对象，key为频道id，value为节目单集合
        //对map对象进行分组，根据spid，创建map对象，key为spid，value为map对象
        Map<Long, Map<Long, List<BmsSchedule>>> groupMap = bmsSchedules.stream().collect(
                Collectors.groupingBy(BmsSchedule::getCmsChannelId,
                        Collectors.groupingBy(BmsSchedule::getSpId)));
        //对map对象进行遍历，将value存入sendMap中
        Set<Long> cmsChannelIds = groupMap.keySet();
        try {
            for (Long cmsChannelId : cmsChannelIds) {
                Map<Long, List<BmsSchedule>> firstFloorMap = groupMap.get(cmsChannelId);
                Set<Long> spids = firstFloorMap.keySet();
                for (Long spid : spids) {
                    List<BmsSchedule> bmsScheduleList = firstFloorMap.get(spid);
                    String spName = bmsScheduleList.stream().findFirst().get().getSpName();
                    //获取频道信息
                    List<BmsChannel> channelById = bmsChannelService.getChannelById(cmsChannelId,
                            spid);
                    if (CollectionUtil.isEmpty(channelById)) {
                        throw new CommonResponseException(
                                String.format("未查询到节目单在 %s 域所绑定的频道数据", spName));
                    }
                    Long channelId = channelById.stream().findFirst().get().getId();
                    //组建sendMap
                    Map<String, String> sendMap = createSendMap(bmsScheduleList, false);
                    Map<String, OutParamExpand> paramMap = getParamMap();
                    paramMap.put(PublishParamTypeConstants.ORDER_CHANNELSCHEDULE,
                            new OutParamExpand().setSpareMap(sendMap));
                    //回调
                    workOrderOperation.send(ActionEnums.DELETE, SCHEDULE, new ArrayList<Long>() {{
                                add(channelId);
                            }}, null, spid, spName,
                            (success, publishStatus, description) -> {
                                List<Long> publishIds = bmsScheduleList.stream()
                                        .map(bmsSchedule -> bmsSchedule.getId())
                                        .collect(Collectors.toList());
                                sendMap.forEach((id, action) -> {
                                    if (publishIds.contains(Long.parseLong(id))) {
                                        ActionEnums value = ActionEnums.getByValue(
                                                Integer.parseInt(action));
                                        bmsScheduleList.forEach(
                                                bmsSchedule -> bmsSchedule.setPublishStatus(
                                                        CommonUtils.getEnumByAction(value,
                                                                success)));
                                        bmsScheduleList.forEach(
                                                bmsSchedule -> bmsSchedule.setPublishTime(
                                                        new Date()));
                                        bmsScheduleList.forEach(
                                                bmsSchedule -> bmsSchedule.setTimeDescription(""));
                                        bmsScheduleList.forEach(
                                                bmsSchedule -> bmsSchedule.setPublishDescription(
                                                        success ? "" : description));
                                    }
                                });
                                this.updateBatchById(bmsScheduleList);
                            }, paramMap);
                }
            }
            return CommonResponse.success(true);
        } catch (Exception exception) {
            log.error("自动发布节目单-一键回收操作失败，错误描述", exception);
            throw new CommonResponseException(exception.getMessage());
        }
    }
}
