package com.pukka.iptv.manage.mapper.sys;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.pukka.iptv.common.data.annotation.DataPermission;
import com.pukka.iptv.common.data.model.sys.SysMenu;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Set;

/**
 *
 * @author: zhengcl
 * @date: 2021-8-17 16:48:31
 */

@Mapper
public interface SysMenuMapper extends BaseMapper<SysMenu>{
	
	List<SysMenu> listByRoleIds(@Param("roleIds") Set roleIds);

	List<SysMenu> listAll(Long roleId);

}
