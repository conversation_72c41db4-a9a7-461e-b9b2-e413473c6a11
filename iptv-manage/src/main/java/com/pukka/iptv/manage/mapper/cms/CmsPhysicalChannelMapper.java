package com.pukka.iptv.manage.mapper.cms;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.pukka.iptv.common.data.annotation.DataPermission;
import com.pukka.iptv.common.data.model.cms.CmsPhysicalChannel;
import org.apache.ibatis.annotations.Mapper;

/**
 *
 * @author: tan
 * @date: 2021-9-7 11:54:46
 */

@Mapper
@DataPermission(config = "cp_id=cpIds")
public interface CmsPhysicalChannelMapper extends BaseMapper<CmsPhysicalChannel>{

}
