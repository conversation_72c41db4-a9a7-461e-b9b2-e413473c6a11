package com.pukka.iptv.manage.mapper.cms;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.pukka.iptv.common.data.annotation.DataPermission;
import com.pukka.iptv.common.data.model.cms.CmsChannel;
import org.apache.ibatis.annotations.Mapper;
import com.pukka.iptv.common.data.model.*;

/**
 *
 * @author: luo
 * @date: 2021-9-15 11:33:34
 */

@Mapper
@DataPermission(config = "cp_id=cpIds")
public interface CmsChannelMapper extends BaseMapper<CmsChannel>{

}
