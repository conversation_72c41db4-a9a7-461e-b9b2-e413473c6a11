package com.pukka.iptv.manage.util.downloadUtils;

import com.pukka.iptv.common.core.constant.SymbolConstant;
import com.pukka.iptv.common.core.util.DateUtils;
import com.pukka.iptv.common.core.util.FtpUtil;
import com.pukka.iptv.common.core.util.SafeUtil;
import com.pukka.iptv.manage.util.downloadUtils.dto.RetrieveFileEntity;
import com.pukka.iptv.manage.util.downloadUtils.factory.FtpBlockFactory;
import com.pukka.iptv.manage.util.downloadUtils.factory.FtpPoolFactory;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.net.ftp.FTPClient;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.servlet.http.HttpServletResponse;
import java.io.FileOutputStream;
import java.io.IOException;
import java.io.OutputStream;
import java.net.URLEncoder;
import java.nio.file.Path;

/**
 * @author: chiron Date: 2022/5/12 14:16 Description: 实现文件上传下载
 */
@Slf4j
@Component
public class FtpClientService {

    @Autowired
    private FtpPoolFactory ftpPoolFactory;
    @Autowired
    private FtpBlockFactory ftpBlockFactory;

    /**
     * 检索视频介质文件并下载
     *
     * @param retrieveFileEntity
     * @return
     */
    public Boolean retrieveFile(RetrieveFileEntity retrieveFileEntity) {
        String remotePath = retrieveFileEntity.getRemotePath();
        HttpServletResponse response = retrieveFileEntity.getResponse();
        if (!remotePath.startsWith("/")) {
            remotePath = "/" + remotePath;
        }
        FTPClient ftpClient = null;
        Boolean aBoolean = false;
        try {
            response.reset();
            // 设置文件头 最后一个参数是设置下载的文件名并编码为UTF-8
            response.setHeader(
                    "Content-Disposition",
                    "attachment;filename*=UTF-8''"
                            + URLEncoder.encode(getString(retrieveFileEntity.getFileName()), "UTF-8"));
            OutputStream out = response.getOutputStream();
            // 检查ftp链接占用情况
            boolean isContinue = ftpPoolFactory.checkFTPClientObject();
            if (!isContinue) {
                FTPClient ftpBlock = null;
                try {
                    ftpBlock = ftpBlockFactory.borrowObject();
                    aBoolean = ftpBlockFactory.streamTrans(ftpBlock, remotePath, out, isContinue);
                } catch (Exception exception) {
                    log.error("下载视频介质 -----> 写入文件流至浏览器端失败, 错误信息:{}", exception);
                } finally {
                    if (ftpBlock != null) {
                        try {
                            ftpBlock.completePendingCommand();
                            ftpBlockFactory.returnObject(ftpBlock);
                        } catch (Exception e) {
                            log.error("下载视频介质 -----> 还池失败,错误信息:{}", e);
                            ftpBlockFactory.invalidateObject(ftpBlock);
                            try {
                                ftpBlockFactory.addObject();
                            } catch (Exception ex) {
                                log.error("下载视频介质 -----> 还池失败,创建ftp链接失败!错误信息:{}", ex);
                            }
                        }
                    }
                }
            }
            ftpClient = ftpPoolFactory.borrowObject();
            if (isContinue) {
                aBoolean = ftpPoolFactory.streamTrans(ftpClient, remotePath, out);
            } else {
                aBoolean = ftpBlockFactory.streamTrans(ftpClient, remotePath, out, true);
            }
        } catch (Exception e) {
            log.error("下载视频介质 -----> 检索视频介质文件并下载失败, 错误信息:{}", e);
            return aBoolean;
        } finally {
            if (ftpClient != null) {
                try {
                    ftpClient.completePendingCommand();
                    ftpPoolFactory.returnObject(ftpClient);
                } catch (Exception e) {
                    log.error("下载视频介质 -----> 还池失败,错误信息:{}", e);
                    ftpPoolFactory.invalidateObject(ftpClient);
                    try {
                        ftpPoolFactory.addObject();
                    } catch (Exception ex) {
                        log.error("下载视频介质 -----> 还池失败,创建ftp链接失败!错误信息:{}", e);
                    }
                }
            }
        }
        return aBoolean;
    }

    /**
     * 打开单ftp连接读取下载介质
     *
     * @param retrieveFile
     * @return
     */
    public Boolean readFtpStream(RetrieveFileEntity retrieveFile) {
        // 是否删除成功
        String destination = "";
        Boolean aBoolean = false;
        String ftpPath = retrieveFile.getFileUrl();
        try {
            HttpServletResponse response = retrieveFile.getResponse();
            response.reset();
            // 设置文件头 最后一个参数是设置下载的文件名并编码为UTF-8
            response.setHeader(
                    "Content-Disposition",
                    "attachment;filename*=UTF-8''"
                            + URLEncoder.encode(getString(retrieveFile.getFileName()), "UTF-8"));
            OutputStream out = response.getOutputStream();
            FtpUtil ftpUtil = null;
            try {
                String[] values = FtpUtil.getSingleMatchValue(ftpPath);
                destination = values[4];
                ftpUtil = new FtpUtil(values[2], values[3], values[0], values[1]);
                ftpUtil.login();
                aBoolean = ftpUtil.streamTrans(destination, out);
            } catch (Exception exception) {
                log.error("打开单ftp连接读取下载介质 -----> 数据流传输失败,Exception:{}.", exception);
                return aBoolean;
            } finally {
                try {
                    if (ftpUtil != null) {
                        ftpUtil.logout();
                    }
                } catch (Exception e) {
                    log.error("打开单ftp连接读取下载介质 -----> 关闭ftp连接失败.exception:{}", e);
                }
            }
        } catch (Exception exception) {
            log.error("打开单ftp连接读取下载介质 -----> 检索视频介质文件并下载失败, 错误信息:{}", exception);
        }
        log.info(
                "打开单ftp连接读取下载介质 -----> url:{},dirPath:{}" + SafeUtil.getString(ftpPath),
                SafeUtil.getString(destination));
        return aBoolean;
    }

    /**
     * 编辑介质名称
     *
     * @param mediaName
     * @return
     */
    private String getString(String mediaName) {
        String name = "";
        if (mediaName.contains(SymbolConstant.PERIOD)) {
            String firstCharacters = mediaName.substring(0, mediaName.lastIndexOf(SymbolConstant.PERIOD));
            String lastCharacters = mediaName.substring((firstCharacters.length()));
            name = firstCharacters + SymbolConstant.PERIOD + DateUtils.dateTimeNow() + lastCharacters;
        } else {
            name = mediaName + SymbolConstant.PERIOD + DateUtils.dateTimeNow();
        }
        return name;
    }

    /**
     * 下载文件
     * @param retrieveFile
     * @param localFilePath
     * @return
     * @throws IOException
     */
    public Boolean downloadFtpToLocal(RetrieveFileEntity retrieveFile, Path localFilePath) throws IOException {
        String ftpPath = retrieveFile.getFileUrl();
        FtpUtil ftpUtil = null;
        try (OutputStream out = new FileOutputStream(localFilePath.toFile())) {
            String[] values = FtpUtil.getSingleMatchValue(ftpPath);
            ftpUtil = new FtpUtil(values[2], values[3], values[0], values[1]);
            ftpUtil.login();
            return ftpUtil.streamTrans(values[4], out);
        } catch (Exception exception) {
            log.error("FTP 文件下载失败, Exception:{}", exception);
            return false;
        } finally {
            if (ftpUtil != null) {
                ftpUtil.logout();
            }
        }
    }

}
