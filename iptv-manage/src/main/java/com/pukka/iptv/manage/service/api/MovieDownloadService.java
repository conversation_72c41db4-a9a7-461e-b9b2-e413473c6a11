package com.pukka.iptv.manage.service.api;

import com.pukka.iptv.common.data.model.cms.CmsMovie;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.List;

/**
 * @author: chiron
 * Date: 2022/5/12 14:16
 * Description:视频介质下载操作接口
 */
public interface MovieDownloadService {

    /**
     * 校验视频介质
     * @param cmsContentId
     * @param contentType
     * @param cpId
     * @return cmsmovie表id
     */
    String checkMovieInfo(Long cmsContentId, Integer contentType,Long cpId);
    /**
     * 获取视频信息
     *
     * @param cmsContentId
     * @param contentType
     * @return
     */
    List<CmsMovie> obtainMovieInfo(Long cmsContentId, Integer contentType,Long cpId);

    /**
     * 下载视频介质
     *
     * @param cmsMovie
     * @return
     */
    Boolean retrieveMovieFile(List<CmsMovie> cmsMovie,String showName, HttpServletResponse response);

    /**
     * 下载图片介质
     *
     * @param ids
     * @return
     */
    Boolean retrieveImageFile(String ids, HttpServletResponse response);

    /**
     * 下载图片介质并压缩
     * @param ids
     * @param response
     * @return
     */
    Boolean retrieveImageFilesAndCompress(String ids, HttpServletResponse response) throws IOException;
}
