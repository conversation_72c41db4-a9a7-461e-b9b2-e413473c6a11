package com.pukka.iptv.manage.service.api.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.pukka.iptv.common.data.model.virtual.VirtualProgram;
import com.pukka.iptv.common.data.model.virtual.VirtualSeries;
import com.pukka.iptv.common.data.model.virtual.VirtualSubset;
import com.pukka.iptv.common.data.vo.req.VirtualChannelSearchReq;
import com.pukka.iptv.common.data.vo.virtual.VirtualSeriesVo;
import com.pukka.iptv.manage.mapper.api.VirtualSeriesMapper;
import com.pukka.iptv.manage.service.api.VirtualProgramService;
import com.pukka.iptv.manage.service.api.VirtualSeriesService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Comparator;
import java.util.List;
import java.util.stream.Collectors;

@Slf4j
@Service
public class VirtualSeriesServiceImpl extends ServiceImpl<VirtualSeriesMapper, VirtualSeries>
        implements VirtualSeriesService {
    @Autowired
    private VirtualSeriesMapper virtualSeriesMapper;
    @Autowired
    private VirtualProgramService virtualProgramService;

    /**
     * 获取剧集信息
     *
     * @param req
     * @return
     */
    @Override
    public IPage<VirtualSeriesVo> getVirtualSeriesByPage(
            Page<VirtualProgram> page, VirtualChannelSearchReq req) {
        if (ObjectUtils.isNotEmpty(req) && ObjectUtils.isNotEmpty(req.getCode())) {
            req.setCode(req.getCode().trim());
        }
        // size等于-1时不分页
        page.setSize(page.getSize() == -1 ? Integer.MAX_VALUE : page.getSize());
        page.setCurrent(page.getCurrent() == -1 ? 1 : page.getCurrent());
        Page<VirtualSeriesVo> virtualSeriesByPage = new Page<>();
        List<VirtualSeriesVo> records =
                virtualSeriesMapper.getVirtualSeriesByPage(
                        req, (page.getCurrent() - 1) * page.getSize(), page.getSize());
        if (ObjectUtils.isNotEmpty(records)) {
            List<String> codeList =
                    records.stream().map(VirtualSeriesVo::getCode).collect(Collectors.toList());
            List<VirtualSubset> virtualSubsets = virtualSeriesMapper.getVirtualSubsetByCode(codeList);
            records.forEach(
                    virtualSeriesVo -> {
                        try {
                            List<VirtualSubset> virtualSubsetList =
                                    virtualSubsets.stream()
                                            .filter(
                                                    virtualSubset ->
                                                            virtualSubset.getSeriesCode().equals(virtualSeriesVo.getCode()))
                                            .sorted(Comparator.comparing(VirtualSubset::getIndex))
                                            .collect(Collectors.toList());
                            virtualSeriesVo.setProgramList(virtualSubsetList);
                        } catch (Exception exception) {
                            log.error("虚拟频道 -----> 获取剧集信息 查询子工单code信息失败，错误信息:{}", exception);
                        }
                    });
        }
        virtualSeriesByPage.setRecords(records);
        virtualSeriesByPage.setSize(page.getSize());
        virtualSeriesByPage.setCurrent(page.getCurrent());
        virtualSeriesByPage.setTotal(virtualSeriesMapper.getVirtualSeriesSize(req));
        return virtualSeriesByPage;
    }

    @Override
    public Boolean insertOrUpdate(VirtualSeries virtualSeries) {
        int result = 0;
        LambdaQueryWrapper<VirtualSeries> virtualProgramLLambdaQueryWrapper = Wrappers.lambdaQuery(VirtualSeries.class)
                .eq(ObjectUtils.isNotEmpty(virtualSeries.getCode()), VirtualSeries::getCode, virtualSeries.getCode())
                .eq(ObjectUtils.isNotEmpty(virtualSeries.getContentType()), VirtualSeries::getContentType, virtualSeries.getContentType());
        List<VirtualSeries> virtualSeriesList = virtualSeriesMapper.selectList(virtualProgramLLambdaQueryWrapper);
        if (ObjectUtils.isEmpty(virtualSeriesList) || virtualSeriesList.size() <= 0) {
            result = virtualSeriesMapper.insert(virtualSeries);
        } else {
            Boolean aBoolean = updateSeriesAndSubset(virtualSeries);
            result = aBoolean ? 1 : 0;
        }
        return result > 0 ? true : false;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean updateSeriesAndSubset(VirtualSeries virtualSeries) {
        try {
            virtualSeriesMapper.update(virtualSeries, Wrappers.lambdaUpdate(VirtualSeries.class)
                    .set(VirtualSeries::getContentType, virtualSeries.getContentType())
                    .set(VirtualSeries::getType, virtualSeries.getType())
                    .set(VirtualSeries::getName, virtualSeries.getName())
                    .set(VirtualSeries::getVspname, virtualSeries.getVspname())
                    .set(VirtualSeries::getEpisodenum, virtualSeries.getEpisodenum())
                    .set(VirtualSeries::getPrice, virtualSeries.getPrice())
                    .set(VirtualSeries::getCreateTime, virtualSeries.getCreateTime())
                    .eq(VirtualSeries::getCode, virtualSeries.getCode()));
            virtualProgramService.update(Wrappers.lambdaUpdate(VirtualProgram.class)
                    .set(VirtualProgram::getVspname, virtualSeries.getVspname())
                    .set(VirtualProgram::getPrice, virtualSeries.getPrice())
                    .eq(VirtualProgram::getSeriesCode, virtualSeries.getCode()));
        } catch (Exception exception) {
            log.error("虚拟频道 -----> 更新剧集子集信息失败，错误信息:{}", exception);
            return false;
        }
        return true;
    }
}
