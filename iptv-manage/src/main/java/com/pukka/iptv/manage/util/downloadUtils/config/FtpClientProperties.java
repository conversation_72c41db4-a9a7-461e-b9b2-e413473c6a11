package com.pukka.iptv.manage.util.downloadUtils.config;

import lombok.Data;
import org.apache.commons.net.ftp.FTP;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

/**
 * @author: chiron
 * Date: 2022/5/12 14:16
 * Description: ftp客服端连接配置
 */
@Data
@Component
@ConfigurationProperties(ignoreUnknownFields = false, prefix = "ftppool.config")
public class FtpClientProperties {

    /**
     * ftp地址
     */
    private String host="**************";

    /**
     * 端口号
     */
    private Integer port = 6069;

    /**
     * 登录用户
     */
    private String username="vstore";

    /**
     * 登录密码
     */
    private String password="iptv!#$vs";

    /**
     * 被动模式
     */
    private boolean passiveMode = true;

    /**
     * 编码
     */
    private String encoding = "UTF-8";

    /**
     * 连接超时时间(秒)
     */
    private Integer connectTimeout = 6000;

    /**
     * 传输超时时间(秒)
     */
    private Integer dataTimeout = 60000;

    /**
     * 缓冲大小
     */
    private Integer bufferSize = 1024;

    /**
     * 设置keepAlive
     * 单位:秒  0禁用
     */
    private Integer keepAliveTimeout = 30;

    /**
     * 传输文件类型
     */
    private Integer transferFileType = FTP.BINARY_FILE_TYPE;

    /**
     * 连接池连接数
     */
    private Integer defaultPoolSize = 5;

    /**
     *  临时连接池数
     */
    private Integer tempPoolSize = 1;
}
