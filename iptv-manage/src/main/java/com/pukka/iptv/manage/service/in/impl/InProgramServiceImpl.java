package com.pukka.iptv.manage.service.in.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.pukka.iptv.common.data.model.in.InProgram;
import com.pukka.iptv.manage.mapper.in.InProgramMapper;
import com.pukka.iptv.manage.service.in.InProgramService;
import org.springframework.stereotype.Service;

/**
 *
 * @author: liuli
 * @date: 2021年8月31日 下午2:43:04
 */

@Service
public class InProgramServiceImpl extends ServiceImpl<InProgramMapper, InProgram> implements InProgramService {

}


