package com.pukka.iptv.manage.service.sys.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.pukka.iptv.common.base.constant.RedisKeyConstants;
import com.pukka.iptv.common.base.enums.CommonResponseEnum;
import com.pukka.iptv.common.base.enums.StatusEnum;
import com.pukka.iptv.common.base.exception.CommonResponseException;
import com.pukka.iptv.common.data.model.sys.SysCp;
import com.pukka.iptv.common.data.model.sys.SysSp;
import com.pukka.iptv.common.data.model.sys.SysStorageDirctory;
import com.pukka.iptv.common.redis.service.RedisService;
import com.pukka.iptv.manage.mapper.sys.SysCpMapper;
import com.pukka.iptv.manage.mapper.sys.SysSpMapper;
import com.pukka.iptv.manage.mapper.sys.SysStorageDirctoryMapper;
import com.pukka.iptv.manage.service.cache.CacheServiceImpl;
import com.pukka.iptv.manage.service.sys.SysStorageDirctoryService;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 *
 * @author: zhengcl
 * @date: 2021-9-8 18:12:15
 */

@Service
public class SysStorageDirctoryServiceImpl extends ServiceImpl<SysStorageDirctoryMapper, SysStorageDirctory> implements SysStorageDirctoryService {

    @Autowired
    private RedisService redisService;

    @Autowired
    private CacheServiceImpl cacheService;

    @Autowired
    private SysCpMapper sysCpMapper;
    @Autowired
    private SysSpMapper sysSpMapper;

    @Override
    public Page<SysStorageDirctory> page(Page<SysStorageDirctory> page, SysStorageDirctory sysStorageDirctory){
        QueryWrapper<SysStorageDirctory> queryWrapper = Wrappers.<SysStorageDirctory>query().eq("storage_id",sysStorageDirctory.getStorageId());
        if (sysStorageDirctory.getStatus() != null) {
            queryWrapper.and(qw -> qw.eq("status", sysStorageDirctory.getStatus()));
        } else {
            queryWrapper.ne("status", StatusEnum.DELETE.getCode());
        }
        if (sysStorageDirctory.getType() != null) {
            queryWrapper.and(qw -> qw.eq("type", sysStorageDirctory.getType()));
        }
        if (sysStorageDirctory.getAuthorityType() != null) {
            queryWrapper.and(qw -> qw.eq("authority_type", sysStorageDirctory.getAuthorityType()));
        }
        queryWrapper.orderByDesc("id");
        return sysStorageDirctory.selectPage(page, queryWrapper);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public boolean removeCacheAndDB(List<Long> idList) {
        if(idList == null || idList.size() == 0){
            throw new CommonResponseException(CommonResponseEnum.ERROR_PARAMETER);
        }
        SysStorageDirctory entity = getById(idList.get(0));
        if(entity == null){
            throw new CommonResponseException(CommonResponseEnum.EMPTY);
        }
        SysCp sysCp = sysCpMapper.selectOne(Wrappers.<SysCp>query()
                .eq("storage_id",entity.getStorageId())
                .ne("status",StatusEnum.DELETE.getCode())
                .last("limit 1"));
        if(sysCp != null){
            throw new CommonResponseException("已关联CP,无法删除");
        }
        SysSp sysSp = sysSpMapper.selectOne(Wrappers.<SysSp>query()
                .eq("storage_id",entity.getStorageId())
                .ne("status",StatusEnum.DELETE.getCode())
                .last("limit 1"));
        if(sysSp != null){
            throw new CommonResponseException("已关联SP,无法删除");
        }
        boolean result = update(Wrappers.lambdaUpdate(SysStorageDirctory.class)
                .set(SysStorageDirctory::getStatus, StatusEnum.DELETE.getCode())
                .in(SysStorageDirctory::getId, idList));
        if (result) {
            Map<String, SysStorageDirctory> collect = listByIds(idList).stream()
                    .collect(Collectors.toMap(storageDirctory ->
                            StringUtils.joinWith(":", storageDirctory.getStorageId(), storageDirctory.getType(), storageDirctory.getAuthorityType())
                            , storageDirctory -> storageDirctory,(val1, val2) -> val2));
            redisService.deleteCacheMap(RedisKeyConstants.SYS_STORAGE_DIRCTORY_KEY, collect.keySet());
        }
        return result;
    }

    @Override
    public boolean removeCacheAndDB(Long id) {
        boolean result = update(Wrappers.lambdaUpdate(SysStorageDirctory.class)
                .set(SysStorageDirctory::getStatus, StatusEnum.DELETE.getCode())
                .eq(SysStorageDirctory::getId, id));
        if (result) {
            SysStorageDirctory storageDirctory = getById(id);
            if (storageDirctory != null) {
                String hKey = StringUtils.joinWith(":", storageDirctory.getStorageId(), storageDirctory.getType(), storageDirctory.getAuthorityType());
                redisService.deleteByHashKey(RedisKeyConstants.SYS_STORAGE_DIRCTORY_KEY, hKey);
            }
        }
        return result;
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public boolean updateCacheAndDB(SysStorageDirctory storageDirctory) {
/*        SysStorageDirctory temp = getOne(Wrappers.<SysStorageDirctory>query()
                .eq("storage_id",storageDirctory.getStorageId())
                .eq("type",storageDirctory.getType())
                .ne("id",storageDirctory.getId())
                .ne("status", StatusEnum.DELETE.getCode()).last("limit 1"));
        if(temp != null){
            throw new CommonResponseException("此目录类型已存在");
        }*/
        boolean result = storageDirctory.updateById();
        if (result) {
            SysStorageDirctory entity = getById(storageDirctory.getId());
            if (entity != null) {
                try {
                    String hKey = StringUtils.joinWith(":", entity.getStorageId(), entity.getType(), entity.getAuthorityType());
                    redisService.setCacheMapValue(RedisKeyConstants.SYS_STORAGE_DIRCTORY_KEY, hKey, entity);
                }catch (Exception e){
                    throw new CommonResponseException("redis异常:"+e.getMessage());
                }
            }
        }
        return result;
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public boolean saveToCacheAndDB(SysStorageDirctory entity) {
/*        SysStorageDirctory temp = getOne(Wrappers.<SysStorageDirctory>query()
                .eq("storage_id",entity.getStorageId())
                .eq("type",entity.getType())
                .ne("status", StatusEnum.DELETE.getCode()).last("limit 1"));
        if(temp != null){
            throw new CommonResponseException("此目录类型已存在");
        }*/
        if (save(entity)) {
            try {
                cacheService.cacheStorageDirctory(Collections.singletonList(entity));
                return true;
            }catch (Exception e){
                throw new CommonResponseException("redis异常:"+e.getMessage());
            }
        }
        return false;
    }
}


