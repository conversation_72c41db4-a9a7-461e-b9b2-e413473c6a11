package com.pukka.iptv.manage.util;


import com.pukka.iptv.manage.service.condition.RuleCondition;
import com.pukka.iptv.manage.service.condition.RuleResult;
import com.pukka.iptv.manage.service.condition.rule.LockStatusRule;
import com.pukka.iptv.manage.service.condition.rule.PublishStatusRule;
import com.pukka.iptv.manage.service.condition.rule.enums.PublishCheck;

import java.util.Collection;

import static com.pukka.iptv.common.base.enums.PublishStatusEnum.*;
import static com.pukka.iptv.manage.service.condition.rule.RecycleStatusRule.NOT_RECYCLE_LIST;

/**
 * <AUTHOR>
 * @create 2021-09-08 17:13
 */

public class RuleUtil {

    /**
     * 判断是否是锁定
     */
    public static <T> RuleResult checkLockStatus(Class<T> clazz, Collection<Long> ids) {
        return RuleCondition.create()
                //判断内容的锁定状态是否符合
                .and(LockStatusRule.init(clazz).data(ids))
                //执行
                .execute();
    }

    /**
     * 判断图片所属的内容是否锁定
     */
    public static RuleResult checkLockPictureBelonging(Integer type, Long id) {
        Class<?> typeClass = RuleCondition.getTypeClass(type);
        if (typeClass != null) {
            RuleCondition.create().and(LockStatusRule.init(typeClass).data(id));
            return RuleCondition.create()
                    //判断内容的锁定状态是否符合
                    .and(LockStatusRule.init(typeClass).data(id))
                    //执行
                    .execute();
        }
        throw new RuntimeException("该类型尚未在匹配规则中定义,请前往定义此类型(不存在的内容类型枚举值：" + type + ")");
    }

    public static <T> RuleResult canModify(Class<T> clazz, Collection<Long> ids) {
        return RuleCondition.create()
                .and(LockStatusRule.init(clazz).data(ids))
                .and(PublishStatusRule.init(clazz).policy(PublishCheck.ING).data(ids))
                .execute();
    }

    // 是否需要修改为待更新
    @Deprecated
    public static boolean needModifyWaitUpdate(Integer publishStatus) {
        if (PUBLISH.getCode().equals(publishStatus) ||
                FAILUPDATE.getCode().equals(publishStatus) ||
                FAILROLLBACK.getCode().equals(publishStatus)) {
            return true;
        }
        return false;
    }

    // 修改操作,需要更新的发布状态
    public static Integer confirmPublishStatus(Integer publishStatus) {
        if (PUBLISH.getCode().equals(publishStatus) ||
                FAILUPDATE.getCode().equals(publishStatus) ||
                FAILROLLBACK.getCode().equals(publishStatus)
        ) {
            return WAITUPDATE.getCode();
        } else if (FAILPUBLISH.getCode().equals(publishStatus)) {
            return WAITPUBLISH.getCode();
        } else {
            return publishStatus;
        }
    }

    // 查询可以发布 注入工单的状态
    public static boolean isWaitPublish(Integer publishStatus) {
        for (Integer item : PublishStatusRule.UN_PUBLISHED_LIST) {
            if (item.equals(publishStatus)) {
                return true;
            }
        }
        return false;
    }

    //查询 可以发布更新工单的状态
    public static boolean isWaitUpdate(Integer publishStatus) {
        for (Integer item : PublishStatusRule.PUBLISH_SUCCESS_LIST) {
            if (item.equals(publishStatus)) {
                return true;
            }
        }
        return false;
    }

    // 是否可回收
    public static boolean canRecycle(Integer publishStatus) {
        for (Integer item : NOT_RECYCLE_LIST) {
            if (item.equals(publishStatus)) return false;
        }
        return true;
    }


    public static Integer confirmModifyPublishIngStatus(Integer publishStatus) {
        if (PUBLISHING.getCode().equals(publishStatus)) {
            return WAITPUBLISH.getCode();
        } else if (UPDATING.getCode().equals(publishStatus)) {
            return WAITUPDATE.getCode();
        } else if (ROLLBACKING.getCode().equals(publishStatus)) {
            return PUBLISH.getCode();
        }
        return null;
    }

}
