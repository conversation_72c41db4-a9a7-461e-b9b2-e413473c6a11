package com.pukka.iptv.manage.rule.match;

import com.pukka.iptv.manage.rule.match.base.BaseSearchEx;
import com.pukka.iptv.manage.rule.match.base.IllegalWordsSearchResult;
import com.pukka.iptv.manage.rule.match.internals.NumHelper;
import org.springframework.stereotype.Component;

import java.io.FileOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.Set;
import java.util.function.Function;

/**
 * <AUTHOR>
 * @Description:违禁关键词检索
 */
@Component
public class IllegalWordsSearch extends BaseSearchEx {
    public class SkipWordFilterHandler {
        public char c;
        public String text;
        public int index;

        public SkipWordFilterHandler(final char c, final String text, final int index) {
            this.c = c;
            this.text = text;
            this.index = index;
        }
    }

    public class CharTranslateHandler {
        public char c;
        public String text;
        public int index;

        public CharTranslateHandler(final char c, final String text, final int index) {
            this.c = c;
            this.text = text;
            this.index = index;
        }
    }

    public class StringMatchHandler {
        public String text;
        public int start;
        public int end;
        public String keyword;
        public int keywordIndex;
        public String matchKeyword;
        public int blacklistIndex;

        public StringMatchHandler(final String text, final int start, final int end, final String keyword,
                                  final int keywordIndex, final String matchKeyword, final int blacklistIndex) {
            this.text = text;
            this.start = start;
            this.end = end;
            this.keyword = keyword;
            this.keywordIndex = keywordIndex;
            this.matchKeyword = matchKeyword;
            this.blacklistIndex = blacklistIndex;
        }
    }

    /**
     * 使用跳词过滤器，默认使用
     */
    public boolean useSkipWordFilter = true;
    private final String _skipList = " \t\r\n\b~!@#$￥%^&*()_+-=【】、[]{}|;·/"
            + "':\"，。、《》？αβγδεζηθικλμνξοπρστυφχψωΑΒΓΔΕΖΗΘΙΚΛΜΝΞΟΠΡΣΤΥΦΧΨΩ。，、；：？！…—·ˉ¨‘’“”々～‖∶＂＇｀｜〃〔〕〈〉《》「」『』．〖〗【】（）［］｛｝ⅠⅡⅢⅣⅤⅥⅦⅧⅨⅩⅪⅫ⒈⒉⒊⒋⒌⒍⒎⒏⒐⒑⒒⒓⒔⒕⒖⒗⒘⒙⒚⒛㈠㈡㈢㈣㈤㈥㈦㈧㈨㈩①②③④⑤⑥⑦⑧⑨⑩⑴⑵⑶⑷⑸⑹⑺⑻⑼⑽⑾⑿⒀⒁⒂⒃⒄⒅⒆⒇≈≡≠＝≤≥＜＞≮≯∷±＋－×÷／∫∮∝∞∧∨∑∏∪∩∈∵∴⊥∥∠⌒⊙≌∽√§№☆★○●◎◇◆□℃‰€■△▲※→←↑↓〓¤°＃＆＠＼︿＿￣―♂♀┌┍┎┐┑┒┓─┄┈├┝┞┟┠┡┢┣│┆┊┬┭┮┯┰┱┲┳┼┽┾┿╀╁╂╃└┕┖┗┘┙┚┛━┅┉┤┥┦┧┨┩┪┫┃┇┋┴┵┶┷┸┹┺┻╋╊╉╈╇╆╅╄";
    private boolean[] _skipBitArray;

    /**
     * 过滤跳词
     */
    public Function<SkipWordFilterHandler, Boolean> skipWordFilter;
    /**
     * 字符转化，可以设置繁简转化、忽略大小写，启用后UseIgnoreCase开启无效
     * 若想使用CharTranslateHandler，请先添加事件CharTranslateHandler, 再用SetKeywords设置关键字
     */
    public Function<CharTranslateHandler, Character> charTranslate;

    /**
     * 自定义字符串匹配
     */
    public Function<StringMatchHandler, Boolean> stringMatch;

    /**
     * 使用重复词过滤器
     */
    public boolean useDuplicateWordFilter = true;
    /**
     * 使用黑名单过滤器
     */
    private int[] _blacklist = new int[0];
    /**
     * 使用半角转化器
     */
    public boolean useDBCcaseConverter = true;
    /**
     * 使用忽略大小写
     */
    public boolean useIgnoreCase = true;


    public IllegalWordsSearch() {
        _skipBitArray = new boolean[Character.MAX_VALUE + 1];
        for (int i = 0; i < _skipList.length(); i++) {
            _skipBitArray[_skipList.charAt(i)] = true;
        }
        skipWordFilter = null;
        charTranslate = null;
        stringMatch = null;
    }

    /**
     * 设置跳词
     *
     * @param skipList
     */
    public void setSkipWords(final String skipList) {

        _skipBitArray = new boolean[Character.MAX_VALUE + 1];
        if (skipList != null) {
            for (int i = 0; i < skipList.length(); i++) {
                _skipBitArray[skipList.charAt(i)] = true;
            }
        }
    }

    /**
     * 设置关键字 如果想使用CharTranslateHandler，请先添加事件CharTranslateHandler,
     * 再用SetKeywords设置关键字 使用CharTranslateHandler后，UseIgnoreCase配置无效
     * 如果不使用忽略大小写，请先UseIgnoreCase设置为false,再用SetKeywords设置关键字
     *
     * @param keywords
     */
    @Override
    public void setKeywords(final List<String> keywords) {
        if (charTranslate != null) {
            final Set<String> kws = new HashSet<String>(keywords);
            final List<String> list = new ArrayList<String>();
            for (final String item : kws) {
                final StringBuilder sb = new StringBuilder();
                for (int i = 0; i < item.length(); i++) {
                    final char c = charTranslate.apply(new CharTranslateHandler(item.charAt(i), item, i));
                    sb.append(c);
                }
                list.add(sb.toString());
            }
            super.setKeywords(list);
        } else if (useDBCcaseConverter || useIgnoreCase) {
            final Set<String> kws = new HashSet<String>(keywords);
            final List<String> list = new ArrayList<String>();
            for (final String item : kws) {
                list.add(toSenseWord(item));
            }
            super.setKeywords(list);
        } else {
            super.setKeywords(keywords);
        }
    }

    @Override
    protected void save(final FileOutputStream bw) throws IOException {
        super.save(bw);

        bw.write(useSkipWordFilter ? 1 : 0);
        bw.write(NumHelper.serialize(_skipBitArray.length));
        for (final boolean item : _skipBitArray) {
            bw.write(item ? 1 : 0);
        }

        bw.write(useDuplicateWordFilter ? 1 : 0);
        bw.write(NumHelper.serialize(_blacklist.length));
        for (final int item : _blacklist) {
            bw.write(NumHelper.serialize(item));
        }

        bw.write(useDBCcaseConverter ? 1 : 0);
        bw.write(useIgnoreCase ? 1 : 0);
    }

    @Override
    public void load(final InputStream br) throws IOException {
        super.load(br);

        useSkipWordFilter = br.read() > 0;
        int length = NumHelper.read(br);
        _skipBitArray = new boolean[length];
        for (int i = 0; i < length; i++) {
            _skipBitArray[i] = br.read() > 0;
        }

        useDuplicateWordFilter = br.read() > 0;
        length = NumHelper.read(br);
        _blacklist = new int[length];
        for (int i = 0; i < length; i++) {
            _blacklist[i] = NumHelper.read(br);
        }

        useDBCcaseConverter = br.read() > 0;
        useIgnoreCase = br.read() > 0;
    }

    /**
     * 在文本中查找所有的关键字
     *
     * @param text 文本
     * @return
     */
    public List<IllegalWordsSearchResult> findAll(final String text) {
        final List<IllegalWordsSearchResult> results = new ArrayList<IllegalWordsSearchResult>();
        int p = 0;
        char pChar = (char) 0;

        for (int i = 0; i < text.length(); i++) {
            char t1 = text.charAt(i);
            if (useSkipWordFilter) {
                if (skipWordFilter != null) {// 跳词跳过
                    if (skipWordFilter.apply(new SkipWordFilterHandler(t1, text, i))) {
                        continue;
                    }
                } else if (_skipBitArray[t1]) {
                    continue;
                }
            }

            if (charTranslate != null) { // 字符串转换
                t1 = charTranslate.apply(new CharTranslateHandler(t1, text, i));
            } else if (useDBCcaseConverter || useIgnoreCase) {
                t1 = toSenseWord(t1);
            }
            final int t = _dict[t1];
            if (t == 0) {
                pChar = t1;
                p = 0;
                continue;
            }
            int next;
            if (p == 0 /*|| t < _min[p] || t > _max[p]*/) {
                next = _first[t];
            } else {
                final int index = _nextIndex[p].IndexOf(t);
                if (index > -1) {
                    next = _nextIndex[p].GetValue(index);
                } else if (useDuplicateWordFilter && pChar == t1) {
                    next = p;
                } else {
                    next = _first[t];
                }
            }

            if (next != 0) {
                if (_end[next] < _end[next + 1] && checkNextChar(text, t1, i)) {
                    for (int j = _end[next]; j < _end[next + 1]; j++) {
                        final int index = _resultIndex[j];
                        final IllegalWordsSearchResult r = getGetIllegalResult(text, i, index);
                        if (r != null) {
                            results.add(r);
                        }
                    }
                }
            }
            p = next;
            pChar = t1;
        }
        return results;
    }

    /**
     * 在文本中查找第一个关键字
     *
     * @param text 文本
     * @return
     */
    public IllegalWordsSearchResult findFirst(final String text) {
        int p = 0;
        char pChar = (char) 0;

        for (int i = 0; i < text.length(); i++) {
            char t1 = text.charAt(i);
            if (useSkipWordFilter) {
                // 跳词跳过
                if (skipWordFilter != null) {
                    if (skipWordFilter.apply(new SkipWordFilterHandler(t1, text, i))) {
                        continue;
                    }
                } else if (_skipBitArray[t1]) {
                    continue;
                }
            }
            // 字符串转换
            if (charTranslate != null) {
                t1 = charTranslate.apply(new CharTranslateHandler(t1, text, i));
            } else if (useDBCcaseConverter || useIgnoreCase) {
                t1 = toSenseWord(t1);
            }
            final int t = _dict[t1];
            if (t == 0) {
                pChar = t1;
                p = 0;
                continue;
            }
            int next;
            if (p == 0) {
                next = _first[t];
            } else {
                final int index = _nextIndex[p].IndexOf(t);
                if (index > -1) {
                    next = _nextIndex[p].GetValue(index);
                } else if (useDuplicateWordFilter && pChar == t1) {
                    next = p;
                } else {
                    next = _first[t];
                }
            }

            if (next != 0) {
                if (_end[next] < _end[next + 1] && checkNextChar(text, t1, i)) {
                    for (int j = _end[next]; j < _end[next + 1]; j++) {
                        final int index = _resultIndex[j];
                        final IllegalWordsSearchResult r = getGetIllegalResult(text, i, index);
                        if (r != null) {
                            return r;
                        }
                    }
                }
            }
            p = next;
            pChar = t1;
        }
        return null;
    }

    /**
     * 判断文本是否包含关键字
     *
     * @param text 文本
     * @return
     */
    public boolean containsAny(final String text) {
        int p = 0;
        char pChar = (char) 0;

        for (int i = 0; i < text.length(); i++) {
            char t1 = text.charAt(i);
            if (useSkipWordFilter) {
                // 跳词跳过
                if (skipWordFilter != null) {
                    if (skipWordFilter.apply(new SkipWordFilterHandler(t1, text, i))) {
                        continue;
                    }
                } else if (_skipBitArray[t1]) {
                    continue;
                }
            }
            // 字符串转换
            if (charTranslate != null) {
                t1 = charTranslate.apply(new CharTranslateHandler(t1, text, i));
            } else if (useDBCcaseConverter || useIgnoreCase) {
                t1 = toSenseWord(t1);
            }
            final int t = _dict[t1];
            if (t == 0) {
                pChar = t1;
                p = 0;
                continue;
            }
            int next;
            if (p == 0) {
                next = _first[t];
            } else {
                final int index = _nextIndex[p].IndexOf(t);
                if (index > -1) {
                    next = _nextIndex[p].GetValue(index);
                } else if (useDuplicateWordFilter && pChar == t1) {
                    next = p;
                } else {
                    next = _first[t];
                }
            }

            if (next != 0) {
                if (_end[next] < _end[next + 1] && checkNextChar(text, t1, i)) {
                    for (int j = _end[next]; j < _end[next + 1]; j++) {
                        final int index = _resultIndex[j];
                        final IllegalWordsSearchResult r = getGetIllegalResult(text, i, index);
                        if (r != null) {
                            return true;
                        }
                    }
                }
            }
            p = next;
            pChar = t1;
        }
        return false;
    }

    /**
     * 在文本中替换所有的关键字
     *
     * @param text 文本
     * @return
     */
    public String replace(final String text) {
        return replace(text, '*');
    }

    /**
     * 在文本中替换所有的关键字
     *
     * @param text        文本
     * @param replaceChar 文本
     * @return
     */
    public String replace(final String text, final char replaceChar) {
        final StringBuilder result = new StringBuilder(text);

        int p = 0;
        char pChar = (char) 0;

        for (int i = 0; i < text.length(); i++) {
            char t1 = text.charAt(i);
            if (useSkipWordFilter) {
                // 跳词跳过
                if (skipWordFilter != null) {
                    if (skipWordFilter.apply(new SkipWordFilterHandler(t1, text, i))) {
                        continue;
                    }
                } else if (_skipBitArray[t1]) {
                    continue;
                }
            }
            // 字符串转换
            if (charTranslate != null) {
                t1 = charTranslate.apply(new CharTranslateHandler(t1, text, i));
            } else if (useDBCcaseConverter || useIgnoreCase) {
                t1 = toSenseWord(t1);
            }
            final int t = _dict[t1];
            if (t == 0) {
                pChar = t1;
                p = 0;
                continue;
            }
            int next;
            if (p == 0) {
                next = _first[t];
            } else {
                final int index = _nextIndex[p].IndexOf(t);
                if (index > -1) {
                    next = _nextIndex[p].GetValue(index);
                } else if (useDuplicateWordFilter && pChar == t1) {
                    next = p;
                } else {
                    next = _first[t];
                }
            }

            if (next != 0) {
                if (_end[next] < _end[next + 1] && checkNextChar(text, t1, i)) {
                    for (int j = _end[next]; j < _end[next + 1]; j++) {
                        final int index = _resultIndex[j];
                        final IllegalWordsSearchResult r = getGetIllegalResult(text, i, index);
                        if (r != null) {
                            for (int k = r.Start; k <= r.End; k++) {
                                result.setCharAt(k, replaceChar);
                            }
                            break;
                        }
                    }
                }
            }
            p = next;
            pChar = t1;
        }
        return result.toString();
    }

    private boolean checkNextChar(final String text, final char c, final int end) {
        if (isEnglishOrNumber(c) == false) {
            return true;
        }
        if (end + 1 < text.length()) {
            char e1 = text.charAt(end + 1);
            if (useSkipWordFilter) {
                // 跳词跳过
                if (skipWordFilter != null) {
                    if (skipWordFilter.apply(new SkipWordFilterHandler(e1, text, end + 1))) {
                        return true;
                    }
                } else if (_skipBitArray[e1]) {
                    return true;
                }
            }
            // 字符串转换
            if (charTranslate != null) {
                e1 = charTranslate.apply(new CharTranslateHandler(e1, text, end + 1));
            } else if (useDBCcaseConverter || useIgnoreCase) {
                e1 = toSenseWord(e1);
            }
            if (isEnglishOrNumber(e1)) {
                return false;
            }
        }
        return true;
    }

    private IllegalWordsSearchResult getGetIllegalResult(String text, int end, int index) {
        String key = _keywords[index];

        int keyIndex = key.length() - 1;
        int start = end;
        for (int i = end; i >= 0; i--) {
            char s2 = text.charAt(i);
            if (useSkipWordFilter) {
                if (skipWordFilter != null) {
                    if (skipWordFilter.apply(new SkipWordFilterHandler(s2, text, i))) {
                        continue;
                    }
                } else if (_skipBitArray[s2]) {
                    continue;
                }
            }
            // 字符串转换
            if (charTranslate != null) {
                s2 = charTranslate.apply(new CharTranslateHandler(s2, text, i));
            } else if (useDBCcaseConverter || useIgnoreCase) {
                s2 = toSenseWord(s2);
            }
            if (s2 == key.charAt(keyIndex)) {
                keyIndex--;
                if (keyIndex == -1) {
                    start = i;
                    break;
                }
            }
        }
        for (int i = start; i >= 0; i--) {
            char s2 = text.charAt(i);
            // 字符串转换
            if (charTranslate != null) {
                s2 = charTranslate.apply(new CharTranslateHandler(s2, text, i));
            } else if (useDBCcaseConverter || useIgnoreCase) {
                s2 = toSenseWord(s2);
            }
            if (s2 != key.charAt(0)) {
                break;
            }
            start = i;
        }
        return getGetIllegalResult(text, key, start, end, index);
    }

    private IllegalWordsSearchResult getGetIllegalResult(String text, String key, int start, int end, int index) {
        if (start > 0) {
            char s1 = text.charAt(start);
            // 字符串转换
            if (charTranslate != null) {
                s1 = charTranslate.apply(new CharTranslateHandler(s1, text, start));
            }
            if (isEnglishOrNumber(s1)) {
                char s2 = text.charAt(start - 1);
                // 字符串转换
                if (charTranslate != null) {
                    s2 = charTranslate.apply(new CharTranslateHandler(s2, text, start - 1));
                } else if (useDBCcaseConverter || useIgnoreCase) {
                    s2 = toSenseWord(s2);
                }
                if (isEnglishOrNumber(s2)) {
                    return null;
                }
            }
        }

        final String keyword = text.substring(start, end + 1);
        final int bl = _blacklist.length > index ? _blacklist[index] : 0;
        if (stringMatch != null) {
            if (stringMatch.apply(new StringMatchHandler(text, start, end, keyword, index, key, _blacklist[index]))) {
                return new IllegalWordsSearchResult(keyword, start, end, index, key, bl);
            }
            return null;
        }
        return new IllegalWordsSearchResult(keyword, start, end, index, key, bl);
    }

    /**
     * 设置黑名单
     *
     * @param blacklist
     * @throws IllegalArgumentException
     */
    public void setBlacklist(final int[] blacklist) throws IllegalArgumentException {
        if (_keywords == null) {
            throw new IllegalArgumentException("请先使用SetKeywords方法设置关键字！");
        }
        if (blacklist.length != _keywords.length) {
            throw new IllegalArgumentException("请关键字与黑名单列表的长度要一样长！");
        }
        _blacklist = blacklist;
    }

    private Boolean isEnglishOrNumber(final char c) {
        if (c < 128) {
            if ((c >= '0' && c <= '9') || (c >= 'a' && c <= 'z') || (c >= 'A' && c <= 'Z')) {
                return true;
            }
        }
        return false;
    }

    private String toSenseWord(final String text) {
        final StringBuilder stringBuilder = new StringBuilder(text.length());
        for (int i = 0; i < text.length(); i++) {
            stringBuilder.append(toSenseWord(text.charAt(i)));
        }
        return stringBuilder.toString();
    }

    private Character toSenseWord(final Character c) {

        if (useIgnoreCase) {
            if (c >= 'A' && c <= 'Z') {
                return (char) (c | 0x20);
            }
        }
        if (useDBCcaseConverter) {
            if (c == 12288) {
                return ' ';
            }
            if (c >= 65280 && c < 65375) {
                Character k = (char) (c - 65248);
                if (useIgnoreCase) {
                    if ('A' <= k && k <= 'Z') {
                        k = (char) (k | 0x20);
                    }
                }
                return (char) k;
            }
        }
        return c;
    }

}