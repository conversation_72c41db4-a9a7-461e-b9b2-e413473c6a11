package com.pukka.iptv.manage.service.sys.impl;

import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.pukka.iptv.common.base.constant.RedisKeyConstants;
import com.pukka.iptv.common.base.enums.CommonResponseEnum;
import com.pukka.iptv.common.base.enums.DeleteStatusEnum;
import com.pukka.iptv.common.base.enums.StatusEnum;
import com.pukka.iptv.common.base.exception.CommonResponseException;
import com.pukka.iptv.common.core.constant.SymbolConstant;
import com.pukka.iptv.common.data.model.sys.SysCp;
import com.pukka.iptv.common.data.model.sys.SysInPassage;
import com.pukka.iptv.common.data.model.sys.SysOutPassage;
import com.pukka.iptv.common.data.model.sys.SysSp;
import com.pukka.iptv.common.redis.service.RedisService;
import com.pukka.iptv.manage.mapper.sys.SysCpMapper;
import com.pukka.iptv.manage.mapper.sys.SysInPassageMapper;
import com.pukka.iptv.manage.service.cache.CacheServiceImpl;
import com.pukka.iptv.manage.service.sys.SysInPassageService;
import com.pukka.iptv.manage.service.sys.SysOutPassageService;
import com.pukka.iptv.manage.service.sys.SysSpService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.stream.Collectors;


/**
 * @author: chenyudong
 * @date: 2021-9-13 16:58:09
 */

@Service
@Slf4j
public class SysInPassageServiceImpl extends ServiceImpl<SysInPassageMapper, SysInPassage> implements SysInPassageService {

    @Autowired
    private RedisService redisService;

    @Autowired
    private CacheServiceImpl cacheService;

    @Autowired
    private SysCpMapper sysCpMapper;

    @Autowired
    SysSpService sysSpService;

    @Autowired
    SysOutPassageService sysOutPassageService;

    @Override
    public Page<SysInPassage> page(Page<SysInPassage> page, SysInPassage sysInPassage) {
        QueryWrapper<SysInPassage> queryWrapper = Wrappers.query();
        if (sysInPassage.getStatus() != null) {
            queryWrapper.and(qw -> qw.eq("status", sysInPassage.getStatus()));
        } else {
            queryWrapper.ne("status", StatusEnum.DELETE.getCode());
        }
        if (!StringUtils.isBlank(sysInPassage.getName())) {
            queryWrapper.and(qw -> qw.like("name", sysInPassage.getName()));
        }
        queryWrapper.orderByDesc("id");
        return sysInPassage.selectPage(page, queryWrapper);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean removeDBAndCache(List<Long> idList) {
        if (CollectionUtils.isEmpty(idList)) {
            throw new CommonResponseException(CommonResponseEnum.PARAMETER_ERROR);
        }

        /** 逻辑删除注入通道 */
        boolean result = update(Wrappers.lambdaUpdate(SysInPassage.class)
                .set(SysInPassage::getStatus, StatusEnum.DELETE.getCode())
                .in(SysInPassage::getId, idList)
        );
        try {
            /** 移除redis数据 */
            if (result) {
                /** 由于redis数据结构为 RedisKeyConstants.SYS_IN_PASSAGE cspid:SysInPassage，需查找CSPID删除对应的SysInPassage */
                List<String> cspIds = new ArrayList<>();
                List<SysInPassage> sysInPassageList = listByIds(idList);
                if (sysInPassageList == null || sysInPassageList.size() == 0) {
                    throw new CommonResponseException(CommonResponseEnum.DATA_EMPTY, "数据错误");
                }
                sysInPassageList.forEach(sysInPassage -> cspIds.add(sysInPassage.getCspId()));
                redisService.delCacheMapKey(RedisKeyConstants.SYS_IN_PASSAGE, cspIds.toArray(new String[cspIds.size()]));
            }
        } catch (Exception e) {
            throw new CommonResponseException(CommonResponseEnum.ERROR, "redis异常：" + e.getMessage());
        }

        return result;
    }

    /**
     * 逻辑删除
     *
     * @param id
     * @return
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean removeDBAndCache(Long id) {
        boolean result = update(Wrappers.lambdaUpdate(SysInPassage.class)
                .set(SysInPassage::getStatus, StatusEnum.DELETE.getCode())
                .eq(SysInPassage::getId, id));
        try {
            /** 移除redis数据 */
            if (result) {
                /** 由于redis数据结构为 RedisKeyConstants.SYS_IN_PASSAGE cspid:SysInPassage，需查找CSPID删除对应的SysInPassage */
                SysInPassage sysInPassage = getById(id);
                redisService.delCacheMapKey(RedisKeyConstants.SYS_IN_PASSAGE, sysInPassage.getCspId());
            }
        } catch (Exception e) {
            throw new CommonResponseException(CommonResponseEnum.ERROR, "redis异常：" + e.getMessage());
        }
        return result;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean updateCacheAndDB(SysInPassage sysInPassage) {
        if (!StringUtils.isBlank(sysInPassage.getName())) {
            //判断名称是否重复
            SysInPassage temp = getOne(Wrappers.lambdaQuery(SysInPassage.class)
                    .ne(SysInPassage::getStatus, StatusEnum.DELETE.getCode())
                    .ne(SysInPassage::getId, sysInPassage.getId())
                    .eq(SysInPassage::getName, sysInPassage.getName())
                    .last("limit 1"));
            if (temp != null) {
                log.error("注入通道名称 已存在", temp.getName());
                throw new CommonResponseException(CommonResponseEnum.CREATE_ERROR, "注入通道名称已存在");
            }
        }
        if (sysInPassage.getCpId() != null) {
            //判断关联CP是否被禁用
            SysCp sysCp = sysCpMapper.selectOne(Wrappers.<SysCp>query()
                    .eq("id", sysInPassage.getCpId()).ne("status", DeleteStatusEnum.DELETE));
            if (sysCp == null) {
                throw new CommonResponseException(CommonResponseEnum.CREATE_ERROR, "关联CP不存在,请重新选择");
            }
            if (!StatusEnum.COME.getCode().equals(sysCp.getStatus())) {
                throw new CommonResponseException(CommonResponseEnum.CREATE_ERROR, "关联CP已被禁用,请重新选择");
            }
            sysInPassage.setCpName(sysCp.getName());
        }
        setSpAndOutPassage(sysInPassage);
        boolean result = updateById(sysInPassage);
        if (!result) {
            log.warn("数据库更新指定注入通道失败，redis操作终止!");
            throw new CommonResponseException(CommonResponseEnum.UPDATE_ERROR, "数据库更新指定注入通道失败，redis操作终止!");
        }
        try {
            SysInPassage inPassage = getById(sysInPassage.getId());
            cacheService.cacheInPassage(Collections.singletonList(inPassage));

        } catch (Exception e) {
            log.error("更新指定注入通道 失败,Exception: {}", e);
            throw new CommonResponseException(CommonResponseEnum.ERROR, "redis异常：" + e.getMessage());
        }
        return result;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean saveToCacheAndDB(SysInPassage entity) {
        /** 参数校验 */
        if (StringUtils.isBlank(entity.getName()) || StringUtils.isBlank(entity.getCspId())
                || StringUtils.isBlank(entity.getReportUrl()) || entity.getCpId() == null) {
            throw new CommonResponseException(CommonResponseEnum.ERROR_PARAMETER, "参数不能为空");
        }
        //判断名称是否重复
        SysInPassage temp = getOne(Wrappers.lambdaQuery(SysInPassage.class)
                .ne(SysInPassage::getStatus, StatusEnum.DELETE.getCode())
                .eq(SysInPassage::getName, entity.getName())
                .last("limit 1"));
        if (temp != null) {
            log.error("注入通道名称 已存在", temp.getName());
            throw new CommonResponseException(CommonResponseEnum.CREATE_ERROR, "注入通道名称已存在");
        }
        //判断cspid是否存在
        SysInPassage inPassage = getOne(Wrappers.lambdaQuery(SysInPassage.class)
                .ne(SysInPassage::getStatus, StatusEnum.DELETE.getCode())
                .eq(SysInPassage::getCspId, entity.getCspId())
                .last("limit 1"));
        if (inPassage != null) {
            log.error("CSPID = 已存在", inPassage.getCspId());
            throw new CommonResponseException(CommonResponseEnum.CREATE_ERROR, "CSPID已存在");
        }
        //判断关联CP是否被禁用
        SysCp sysCp = sysCpMapper.selectOne(Wrappers.<SysCp>query()
                .eq("id", entity.getCpId()).ne("status", DeleteStatusEnum.DELETE));
        if (sysCp == null) {
            throw new CommonResponseException(CommonResponseEnum.CREATE_ERROR, "关联CP不存在,请重新选择");
        }
        if (!StatusEnum.COME.getCode().equals(sysCp.getStatus())) {
            throw new CommonResponseException(CommonResponseEnum.CREATE_ERROR, "关联CP已被禁用,请重新选择");
        }
        entity.setCpName(sysCp.getName());
        setSpAndOutPassage(entity);
        if (!save(entity)) {
            log.warn("数据库新增指定注入通道失败，redis操作终止!");
            throw new CommonResponseException(CommonResponseEnum.CREATE_ERROR, "数据库新增指定注入通道失败，redis操作终止!");
        }
        try {
            cacheService.cacheInPassage(Collections.singletonList(entity));
        } catch (Exception exception) {
            log.error("新增指定注入通道失败,Exception:", exception);
            throw new CommonResponseException(CommonResponseEnum.ERROR, "redis异常：" + exception.getMessage());
        }
        return false;
    }

    public SysInPassage sysInPassage(String cspId) {
        SysInPassage sysInPassage = null;
        try {
            if (ObjectUtil.isNotEmpty(cspId)) {
                sysInPassage = redisService.getCacheMapValue(RedisKeyConstants.SYS_IN_PASSAGE, cspId);
            }
        } catch (Exception e) {
            throw new CommonResponseException(CommonResponseEnum.ERROR, "redis异常：" + e.getMessage());
        }
        return sysInPassage;
    }

    public void setSpAndOutPassage(SysInPassage sysInPassage) {
        if (StringUtils.isNotEmpty(sysInPassage.getRealTimeSpIds())) {
            List<Long> outSpIds = Arrays.stream(sysInPassage.getRealTimeSpIds().split(SymbolConstant.COMMA))
                    .map(Long::parseLong)
                    .collect(Collectors.toList());
            Map<Long, String> spIdNameMap = sysSpService.listByIds(outSpIds).stream()
                    .collect(Collectors.toMap(SysSp::getId, SysSp::getName));
            List<String> spNames = new ArrayList<>(spIdNameMap.values());
            String spIds = StringUtils.join(spIdNameMap.keySet(), SymbolConstant.COMMA);
            sysInPassage.setRealTimeSpNames(StringUtils.join(spNames, SymbolConstant.COMMA));
            sysInPassage.setRealTimeSpIds(spIds);
        }
        if (StringUtils.isNotEmpty(sysInPassage.getDelayTimeSpIds())) {
            List<Long> outSpIds = Arrays.stream(sysInPassage.getDelayTimeSpIds().split(SymbolConstant.COMMA))
                    .map(Long::parseLong)
                    .collect(Collectors.toList());
            Map<Long, String> spIdNameMap = sysSpService.listByIds(outSpIds).stream()
                    .collect(Collectors.toMap(SysSp::getId, SysSp::getName));
            List<String> spNames = new ArrayList<>(spIdNameMap.values());
            String spIds = StringUtils.join(spIdNameMap.keySet(), SymbolConstant.COMMA);
            sysInPassage.setDelayTimeSpNames(StringUtils.join(spNames, SymbolConstant.COMMA));
            sysInPassage.setDelayTimeSpIds(spIds);
        }
        if (StringUtils.isNotEmpty(sysInPassage.getOutPassageIds())) {
            List<Long> outPassageIds = Arrays.stream(sysInPassage.getOutPassageIds().split(SymbolConstant.COMMA)).map(Long::parseLong).collect(Collectors.toList());
            Map<Long, String> outPassageMap = sysOutPassageService.listByIds(outPassageIds).stream()
                    .collect(Collectors.toMap(SysOutPassage::getId, SysOutPassage::getName));;
            List<String> nameList = new ArrayList<>(outPassageMap.values());
            String ids = StringUtils.join(outPassageMap.keySet(), SymbolConstant.COMMA);
            sysInPassage.setOutPassageNames(StringUtils.join(nameList, SymbolConstant.COMMA));
            sysInPassage.setOutPassageIds(ids);
        }
    }
}


