package com.pukka.iptv.manage.service.condition.rule;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.pukka.iptv.common.base.enums.PublishStatusEnum;
import com.pukka.iptv.common.core.util.SpringUtils;
import com.pukka.iptv.manage.mapper.bms.BmsBaseMapper;
import com.pukka.iptv.manage.service.condition.AbstractRule;
import com.pukka.iptv.manage.service.condition.BaseRule;
import com.pukka.iptv.manage.service.condition.RuleResult;
import com.pukka.iptv.manage.service.condition.rule.enums.RecycleCheck;

import java.util.Arrays;
import java.util.Collection;
import java.util.List;
import java.util.Map;

import static com.pukka.iptv.manage.service.condition.RuleResult.fail;

/**
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2021/9/9
 */
public class RecycleStatusRule<T> extends AbstractRule<T, RecycleCheck> {
    /**
     * 是否可回收
     */
    public static final List<Integer> NOT_RECYCLE_LIST =
            Arrays.asList(PublishStatusEnum.WAITPUBLISH.getCode(),
                    PublishStatusEnum.ROLLBACK.getCode(),
                    PublishStatusEnum.ROLLBACKING.getCode());
    //可以直接在SP侧  回收删除的集合
    public static final List<Integer> CAN_DELETE_LIST =
            Arrays.asList(PublishStatusEnum.WAITPUBLISH.getCode(),
                    PublishStatusEnum.ROLLBACK.getCode());
    //可以标记删除的集合
    public static final List<Integer> CAN_MARK_DELETE_LIST =
            Arrays.asList(PublishStatusEnum.ROLLBACKING.getCode());

    protected RecycleStatusRule(Class<T> clazz) {
        super(clazz);
    }

    public static <T> RecycleStatusRule<T> init(Class<T> clazz) {
        return new RecycleStatusRule<>(clazz);
    }

    @Override
    public BaseRule wrapper(QueryWrapper wrapper) {
        return null;
    }

    @Override
    public RuleResult execute() {
        String table = getTableName();
        Collection<Long> data = getData();
        //为空不检查
        if (CollectionUtil.isEmpty(data)) return RuleResult.ok();
        RecycleCheck action = getPolicy();
        String col = getCol();
        String colAlias = getColAlias();
        // true: condCol in(data) ; false: conCol not in(data)
        boolean ins = false;
        Collection<Integer> status = null;
        String condCol = getCondCol();
        BmsBaseMapper bean = SpringUtils.getBean(BmsBaseMapper.class);
        switch (action) {
            //是否可以回收检查
            case CAN_RECYCLE:
                status = NOT_RECYCLE_LIST;
                ins = true;
                break;
            case CAN_NOT_RECYCLE:
                status = NOT_RECYCLE_LIST;
                ins = true;
                break;
            case CAH_DELETE:
                status = CAN_DELETE_LIST;
                break;
            default:
                break;
        }
        Map<String, Object> result = bean.getColByPublishStatus(table, col, colAlias, condCol, data, status, ins);
        if (ObjectUtil.isNotEmpty(result)) {
            StringBuilder builder = new StringBuilder();
            if (status != null) {
                for (Integer item : status) {
                    builder.append(PublishStatusEnum.getByValue(item).getMsg()).append(" ");
                }
            }
            Object colData = result.get(col);
            Object psData = result.get("publish_status");
            psData = psData == null ? -1 : psData;
        return fail((NAME.equals(col) ? colData : (col + " = " + colData + " 的数据")) + " 是 " + PublishStatusEnum.getByValue((int) psData).getMsg() + " 的状态,不能进行回收", "");
            // return fail(" 回收检查 " + col + " = " + result + "的数据 不是 " + builder.toString() + " 状态", result);
        }
        return RuleResult.ok();
    }

}
