package com.pukka.iptv.manage.rule.strategy;

import com.pukka.iptv.common.base.enums.ContentTypeEnum;
import org.springframework.beans.BeansException;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.context.ApplicationContext;
import org.springframework.context.ApplicationContextAware;
import org.springframework.stereotype.Component;

import java.io.Serializable;
import java.util.EnumMap;
import java.util.Map;

/**
 * <AUTHOR>
 * @description: 发布策略处理接口
 * @create 2021-08-30
 */
@Component
public class RuleStrategyFactory implements InitializingBean, ApplicationContextAware {

    private static final Map<ContentTypeEnum, IRuleStrategyHandler<Serializable>> STRATEGY_HANDLER_ENUM_MAP = new EnumMap<>(ContentTypeEnum.class);

    private ApplicationContext appContext;


    public IRuleStrategyHandler<Serializable> getHandler(ContentTypeEnum contentTypeEnum) {
        return STRATEGY_HANDLER_ENUM_MAP.get(contentTypeEnum);
    }


    @Override
    public void afterPropertiesSet() throws Exception {
        appContext.getBeansOfType(IRuleStrategyHandler.class)
                .values()
                .forEach(ruleStrategyHandler -> STRATEGY_HANDLER_ENUM_MAP.put(ruleStrategyHandler.getContentType(), ruleStrategyHandler));
    }

    @Override
    public void setApplicationContext(ApplicationContext applicationContext) throws BeansException {
        appContext = applicationContext;
    }
}
