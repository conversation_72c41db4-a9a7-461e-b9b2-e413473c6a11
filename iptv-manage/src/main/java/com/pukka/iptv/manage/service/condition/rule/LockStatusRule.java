package com.pukka.iptv.manage.service.condition.rule;

import cn.hutool.core.collection.CollectionUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.pukka.iptv.common.base.enums.LockStatusEnum;
import com.pukka.iptv.common.core.util.SpringUtils;
import com.pukka.iptv.common.core.util.StringUtils;
import com.pukka.iptv.manage.mapper.bms.BmsBaseMapper;
import com.pukka.iptv.manage.service.condition.AbstractRule;
import com.pukka.iptv.manage.service.condition.BaseRule;
import com.pukka.iptv.manage.service.condition.RuleResult;

import java.util.Collection;


/**
 * @author: wz
 * @date: 2021/9/7 18:57
 * @description:
 */
public class LockStatusRule<T> extends AbstractRule<T, Object> {


    protected LockStatusRule(Class<T> clazz) {
        super(clazz);
    }

    public static <T> LockStatusRule<T> init(Class<T> clazz) {
        return new LockStatusRule<>(clazz);
    }

    private static RuleResult fail(String name) {
        return RuleResult.fail(name + " 为锁定状态无法操作!", name);
    }


    @Override
    public BaseRule wrapper(QueryWrapper<?> wrapper) {
        return this;
    }

    @Override
    public RuleResult execute() {
        String tableName = getTableName();
        Collection<Long> ids = getData();
        //为空不检查
        if (CollectionUtil.isEmpty(ids)) return RuleResult.ok();

        BmsBaseMapper bean = SpringUtils.getBean(BmsBaseMapper.class);
        String name = bean.getLockCountByTable(tableName, ids, LockStatusEnum.LOCK.getCode());
        if (StringUtils.hasText(name)) {
            return fail(name);
        }
        return RuleResult.ok();
    }
}
