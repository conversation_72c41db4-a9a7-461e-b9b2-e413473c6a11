package com.pukka.iptv.manage.service.bms.publish;

import com.pukka.iptv.common.base.vo.CommonResponse;
import com.pukka.iptv.manage.service.bms.dto.PublishParam;

/**
 * @Author: wz
 * @Date: 2021/12/3 16:30
 * @Description:
 */
public interface PublishApi<T> {
    //prepared param
    PublishApi<T> buildParam(PublishParam param);

    //do action
    PublishApi<T> publish();

    //回调
    PublishApi<T> callback(PublishCallback func);

}
