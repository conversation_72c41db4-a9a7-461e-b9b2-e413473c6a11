package com.pukka.iptv.manage.mapper.bms;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.pukka.iptv.common.data.annotation.DataPermission;
import com.pukka.iptv.common.data.model.bms.BmsPackage;
import com.pukka.iptv.common.data.model.order.SubOrderObjectsEntity;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 产品包
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2021-08-27 10:20:57
 */

@Mapper
@DataPermission(config = "sp_id=spIds")
public interface BmsPackageMapper extends BaseMapper<BmsPackage>{

    int updatePublishStatus(@Param("entityList") List<SubOrderObjectsEntity> orderObjectsEntities, @Param("spIdList") List<Long> spIdList);
}
