package com.pukka.iptv.manage.controller.api;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.pukka.iptv.common.base.vo.CommonResponse;
import com.pukka.iptv.common.data.vo.req.VirtualChannelSearchReq;
import com.pukka.iptv.common.data.vo.resp.R;
import com.pukka.iptv.manage.service.api.VirtualProgramService;
import com.pukka.iptv.manage.service.api.VirtualSeriesService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.Valid;

@Slf4j
@RestController
@AllArgsConstructor
@RequestMapping(value = "/virtualChannelController", produces = MediaType.APPLICATION_JSON_VALUE)
@Api(tags = "虚拟频道操作类")
public class VirtualChannelController {

    @Autowired
    private VirtualProgramService virtualProgramService;
    @Autowired
    private VirtualSeriesService virtualSeriesService;

    @ApiOperation(value = "单集子集查询")
    @GetMapping("/getVirtualProgramByPage")
    public CommonResponse getVirtualProgramByPage(@Valid Page page, VirtualChannelSearchReq req) {
        return CommonResponse.success(virtualProgramService.getVirtualProgramByPage(page, req));
    }

    @ApiOperation(value = "剧集查询")
    @GetMapping("/getVirtualSeriesByPage")
    public CommonResponse getVirtualSeriesByPage(@Valid Page page, VirtualChannelSearchReq req) {
        IPage result = virtualSeriesService.getVirtualSeriesByPage(page, req);
        return R.page(result);
    }

}
