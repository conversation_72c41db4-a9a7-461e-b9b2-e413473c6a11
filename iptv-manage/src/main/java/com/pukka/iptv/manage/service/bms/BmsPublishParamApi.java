package com.pukka.iptv.manage.service.bms;

import com.pukka.iptv.common.data.model.OutParamExpand;

import java.util.HashMap;
import java.util.Map;

/**
 * @Author: wangbo
 * @Date: 2022/3/16 16:17
 */
public interface BmsPublishParamApi
{
    public final static ThreadLocal<Map<String, OutParamExpand>> TMP_DATA = new ThreadLocal<>();

    default void setParam(String key, OutParamExpand value) {
        Map<String, OutParamExpand> data = TMP_DATA.get();
        if (data == null) {
            data = new HashMap<>();
            TMP_DATA.set(data);
        }
        data.put(key, value);
        TMP_DATA.set(data);
    }
    default OutParamExpand getParam(String key) {
        Map<String, OutParamExpand> data = TMP_DATA.get();
        if (data == null) return null;
        OutParamExpand outParamExpand = data.get(key);
        return outParamExpand;
    }
    default Map<String, OutParamExpand> getParamMap() {
        Map<String, OutParamExpand> data = TMP_DATA.get();
        if (data == null) {
            return new HashMap<>();
        }
        return data;
    }
    default void clearParm() {
        TMP_DATA.remove();
    }


}
