package com.pukka.iptv.manage.service.sys.impl;

import com.pukka.iptv.common.base.constant.RedisKeyConstants;
import com.pukka.iptv.manage.refresh.handle.GlobalRefreshHandle;
import com.pukka.iptv.manage.service.sys.GlobalRefreshService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.Set;

/**
 * <AUTHOR>
 * @Date 2021/11/6 10:13 上午
 * @description:
 * @Version 1.0
 */
@Slf4j
@Service
public class GlobalRefreshServiceImpl implements GlobalRefreshService {
    @Autowired
    private StringRedisTemplate stringRedisTemplate;
    @Autowired
    private GlobalRefreshHandle globalRefreshHandle;

    @Override
    public Boolean startGlobalRefresh() {
        try {
            log.info("开始清理Redis 全局配置信息");
            Set<String> keys = stringRedisTemplate.keys(RedisKeyConstants.SYS_CONFIG_FIX + "*");
            if (!CollectionUtils.isEmpty(keys)) {
                stringRedisTemplate.delete(keys);
            }
            log.info("清理Redis 全局配置信息完成");
            log.info("开始初始化全局配置信息到REDIS");
            globalRefreshHandle.refresh();
            log.info("初始化全局配置信息到REDIS完成");
        } catch (Exception exception) {
            log.error("全局配置信息初始化失败");
            return false;
        }
        return true;
    }
}
