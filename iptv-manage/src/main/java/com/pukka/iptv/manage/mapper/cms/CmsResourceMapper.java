package com.pukka.iptv.manage.mapper.cms;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.pukka.iptv.common.data.annotation.DataPermission;
import com.pukka.iptv.common.data.dto.ContentResourceDto;
import com.pukka.iptv.common.data.model.cms.CmsResource;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 *
 * @author: zhengcl
 * @date: 2021-9-15 15:13:01
 */

@Mapper
@DataPermission(config = "cp_id=cpIds")
public interface CmsResourceMapper extends BaseMapper<CmsResource>{

    int updateBatchById(@Param("resourceList") List<ContentResourceDto> resourceList);
}
