package com.pukka.iptv.manage.service.api;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.pukka.iptv.common.data.model.virtual.VirtualProgram;
import com.pukka.iptv.common.data.vo.req.VirtualChannelSearchReq;

public interface VirtualProgramService extends IService<VirtualProgram> {
    /**
     * 获取子集单集信息
     * @param req
     * @return
     */
    IPage<VirtualProgram> getVirtualProgramByPage(Page<VirtualProgram> page,VirtualChannelSearchReq req);

    /**
     * 新增或更新
     * @param virtualProgram
     * @return
     */
    Boolean insertOrUpdate(VirtualProgram virtualProgram);
}
