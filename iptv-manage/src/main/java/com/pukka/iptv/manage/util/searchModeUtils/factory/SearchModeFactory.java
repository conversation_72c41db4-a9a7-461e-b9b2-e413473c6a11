package com.pukka.iptv.manage.util.searchModeUtils.factory;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.pukka.iptv.common.base.enums.FtsSearchModeEnum;
import com.pukka.iptv.common.data.model.OutOrderBase;
import com.pukka.iptv.common.data.vo.sys.OutOrderBaseVo;
import com.pukka.iptv.manage.util.searchModeUtils.SearchMode;
import lombok.extern.slf4j.Slf4j;

/**
 * @Author: chiron
 * @Date: 2023/06/14/17:19
 * @Description:
 */

@Slf4j
public class SearchModeFactory {
    /**
     * 初始化搜索模式
     * @param ftsSearchModeEnum
     * @param page
     * @param outOrderBaseVo
     * @return
     */
    public static SearchMode initMode(FtsSearchModeEnum ftsSearchModeEnum, Page<OutOrderBase> page, OutOrderBaseVo outOrderBaseVo) {
        SearchMode searchMode = new SearchMode();
        searchMode.setFtsSearchModeEnum(ftsSearchModeEnum);
        searchMode.setPage(page);
        searchMode.setOutOrderBaseVo(outOrderBaseVo);
        return searchMode;
    }
}
