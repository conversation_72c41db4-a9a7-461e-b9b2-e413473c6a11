package com.pukka.iptv.manage.rule;

import com.pukka.iptv.common.data.model.cms.CmsProgram;
import com.pukka.iptv.common.data.model.cms.CmsSeries;
import com.pukka.iptv.common.data.vo.cms.CmsProgramVO;
import com.pukka.iptv.common.data.vo.cms.CmsSeriesVO;

/**
 * @Author: chiron
 * @Date: 2022/08/04/09:41
 * @Description: 违禁规则检测接口
 */

public interface IRuleProhibitHelper {

    /**
     * 检测单集
     * @param cmsProgram
     */
    CmsProgram checkProgramRuleProhibit(CmsProgramVO cmsProgram);

    /**
     * 检测剧集
     * @param cmsSeries
     */
    CmsSeries checkSeriesRuleProhibit(CmsSeriesVO cmsSeries);

}
