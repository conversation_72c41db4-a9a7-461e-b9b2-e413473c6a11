package com.pukka.iptv.manage.util;

import com.pukka.iptv.common.base.exception.BizException;
import com.pukka.iptv.common.base.exception.CommonResponseException;
import com.pukka.iptv.common.core.constant.SymbolConstant;
import com.pukka.iptv.common.core.util.DateUtils;
import com.pukka.iptv.common.data.dto.DateFormatCompletionDto;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.springframework.util.Assert;

/**
 * <AUTHOR> @Date 2021-11-23 17:14 @Description 时间解析工具
 */
@Slf4j
public class TimeResolutionUtil {
    // 时间分隔符
    private static final String SPECK = "\\.";

    /**
     * <AUTHOR> @Description 为数据字典项超时时间为字符串 支持格式 1 | 1.35 | 1.05 小时单位 转换为毫秒值
     */
    public static long resolutionTime(String time) {
        Assert.hasText(time, "parameter cannot be null");
        try {
            int length = time.length();
            // 长度规范限制
            if (!time.contains(".")) {
                long h = Long.parseLong(time);
                // 小时
                return h * 3600000L;
            } else {
                String[] split = time.split(SPECK);
                // 只能有一个分隔符 所分割的长度必须为2
                Assert.state(split.length == 2, "分发超时时间-格式错误(" + time + ")");
                String hour = split[0];
                String minute = split[1];
                Assert.state(minute.length() <= 2, "分发超时时间-格式错误(" + time + ")");
                // 小时部分
                long h = Long.parseLong(hour) * 3600000L;
                // 分钟部分 个位数补0
                if (minute.length() == 1) {
                    minute = minute + "0";
                }
                // 1分钟 = 36000毫秒
                long m = Long.parseLong(minute) * 36000L;
                return h + m;
            }
        } catch (Exception e) {
            throw new BizException("分发超时时间-格式错误(" + time + ")");
        }
    }

    /**
     * 比较开始时间 结束时间格式
     *
     * @param dateFormatCompletionDto
     */
    public static Boolean dateTimeCompare(DateFormatCompletionDto dateFormatCompletionDto) {
        try {
            if (ObjectUtils.isEmpty(dateFormatCompletionDto)) {
                log.warn(
                        "比较开始时间,结束时间格式结束,参数不全,开始时间:{},结束时间:{}.",
                        dateFormatCompletionDto.getStartTime(),
                        dateFormatCompletionDto.getEndTime());
                return false;
            }
            if (ObjectUtils.isNotEmpty(dateFormatCompletionDto.getStartTime())) {
                dateFormatCompletionDto.setStartTime(
                        dateFormatCompletionDto.getStartTime()
                                + SymbolConstant.SPACE
                                + DateUtils.STARTTIME_SUFFIX);
            }
            if (ObjectUtils.isNotEmpty(dateFormatCompletionDto.getEndTime())) {
                dateFormatCompletionDto.setEndTime(
                        dateFormatCompletionDto.getEndTime() + SymbolConstant.SPACE + DateUtils.ENDTIME_SUFFIX);
            }
        } catch (Exception exception) {
            log.error(
                    "比较开始时间,结束时间格式失败,开始时间:{},结束时间:{}.错误信息:{}",
                    dateFormatCompletionDto.getStartTime(),
                    dateFormatCompletionDto.getEndTime(),
                    exception);
            throw new CommonResponseException("开始时间,结束时间格式失败");
        }
        return true;
    }
}
