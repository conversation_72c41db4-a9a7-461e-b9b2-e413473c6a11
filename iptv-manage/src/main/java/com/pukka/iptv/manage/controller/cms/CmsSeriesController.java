package com.pukka.iptv.manage.controller.cms;

import cn.hutool.core.util.ObjectUtil;
import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.enums.CellDataTypeEnum;
import com.alibaba.excel.exception.ExcelDataConvertException;
import com.alibaba.excel.metadata.data.CellData;
import com.alibaba.excel.read.builder.ExcelReaderBuilder;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.pukka.iptv.common.base.enums.CommonResponseEnum;
import com.pukka.iptv.common.base.vo.CommonResponse;
import com.pukka.iptv.common.data.dto.CmsProgramDO;
import com.pukka.iptv.common.data.dto.CmsProgramDto;
import com.pukka.iptv.common.data.dto.CmsSeriesDto;
import com.pukka.iptv.common.data.dto.TeleplayExcelDto;
import com.pukka.iptv.common.data.model.cms.CmsSeries;
import com.pukka.iptv.common.data.model.statistics.StatisticsIn;
import com.pukka.iptv.common.data.model.statistics.StatisticsInAgain;
import com.pukka.iptv.common.data.model.statistics.StatisticsInCheck;
import com.pukka.iptv.common.data.model.statistics.StatisticsInFinal;
import com.pukka.iptv.common.data.vo.req.StatisticsInVo;
import com.pukka.iptv.common.log.annotation.SysLog;
import com.pukka.iptv.common.log.enums.OperateObjectEnum;
import com.pukka.iptv.common.log.enums.OperateTypeEnum;
import com.pukka.iptv.manage.listener.excel.TeleplayReadListener;
import com.pukka.iptv.manage.service.cms.CmsSeriesService;
import feign.Request;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.validation.Valid;
import java.util.List;
import java.util.Map;

/**
 * 剧集
 *
 * @author: zhoul
 * @date: 2021-8-31 16:36:16
 */
@Slf4j
@RestController
@AllArgsConstructor
@RequestMapping(value = "/cmsSeries", produces = MediaType.APPLICATION_JSON_VALUE)
@Api(tags = "cmsSeries管理")
public class CmsSeriesController {

    @Autowired
    private CmsSeriesService cmsSeriesService;

    @Autowired
    private TeleplayReadListener teleplayReadListener;

    @ApiOperation(value = "分页")
    @GetMapping("/page")
    public CommonResponse<Page> page(@Valid Page page, CmsSeries cmsSeries) {
        return CommonResponse.success(cmsSeriesService.page(page, Wrappers.query(cmsSeries)));
    }

    @SysLog(objectType = OperateObjectEnum.CMS_SERIES, operateType = OperateTypeEnum.SAVE, objectIds = "#cmsSeriesDto.id", objectNames = "#cmsSeriesDto.name")
    @ApiOperation(value = "新增")
    @PostMapping("/save")
    public CommonResponse save(@RequestBody CmsSeriesDto cmsSeriesDto) {
        cmsSeriesDto.validRequestResource();
        cmsSeriesDto.validSeriesSave();
        return cmsSeriesService.news(cmsSeriesDto);
    }

    @SysLog(objectType = OperateObjectEnum.CMS_SERIES, operateType = OperateTypeEnum.DELETE, objectIds = "#cmsSeriesDto.ids")
    @ApiOperation(value = "删除")
    @DeleteMapping("/delete")
    public CommonResponse deleteById(@RequestBody CmsSeriesDto cmsSeriesDto) {
        return cmsSeriesService.del(cmsSeriesDto.getIds());
    }

    @SysLog(objectType = OperateObjectEnum.CMS_SERIES, operateType = OperateTypeEnum.DELETE, objectIds = "#cmsSeriesDto.ids")
    @ApiOperation(value = "删除编目+子集+介质")
    @DeleteMapping("/deleteMov")
    public CommonResponse deleteMov(@RequestBody CmsSeriesDto cmsSeriesDto) {
        return cmsSeriesService.deleteMov(cmsSeriesDto.getIds());
    }

    @SysLog(objectType = OperateObjectEnum.CMS_SERIES, operateType = OperateTypeEnum.UPDATE, objectIds = "#cmsSeriesDto.ids")
    @ApiOperation(value = "批量锁定")
    @PutMapping("/batch/lock")
    public CommonResponse batchLock(@Valid @RequestBody CmsSeriesDto cmsSeriesDto) {
        return cmsSeriesService.batchLock(cmsSeriesDto.getIds(), cmsSeriesDto.getLockStatus().toString());
    }


    @SysLog(objectType = OperateObjectEnum.CMS_SERIES, operateType = OperateTypeEnum.UPDATE, objectIds = "#cmsSeriesDto.ids")
    @ApiOperation(value = "批量元数据修改")
    @PutMapping("/updateByIds")
    public CommonResponse updateByIds(@Valid @RequestBody CmsSeriesDto cmsSeriesDto) {
        return cmsSeriesService.updateAll(cmsSeriesDto);
    }

    @SysLog(objectType = OperateObjectEnum.CMS_SERIES, operateType = OperateTypeEnum.SAVE)
    @ApiOperation(value = "模板导入")
    @PostMapping("/import")
    public CommonResponse importExcel(@Valid @RequestParam("file") MultipartFile file) {
        String errorMsg = null;
        try {
            ExcelReaderBuilder workBook = EasyExcel.read(file.getInputStream(), TeleplayExcelDto.class, teleplayReadListener);
            workBook.sheet().doRead();
            return CommonResponse.success("上传成功");
        } catch (Exception e) {
            errorMsg = e.getMessage();
            if (e.getCause() instanceof ExcelDataConvertException) {
                ExcelDataConvertException excelDataConvertException = (ExcelDataConvertException) e.getCause();
                String cellMsg = "";
                CellData cellData = excelDataConvertException.getCellData();
                //这里有一个celldatatype的枚举值,用来判断CellData的数据类型
                CellDataTypeEnum type = cellData.getType();
                if (type.equals(CellDataTypeEnum.NUMBER)) {
                    cellMsg = cellData.getNumberValue().toString();
                } else if (type.equals(CellDataTypeEnum.STRING)) {
                    cellMsg = cellData.getStringValue();
                } else if (type.equals(CellDataTypeEnum.BOOLEAN)) {
                    cellMsg = cellData.getBooleanValue().toString();
                }
                errorMsg = String.format("Excel表格:第%s行,第%s列,数据值为:%s,该数据值不符合要求,请检验后重新导入 请检查其他的记录是否有同类型的错误", excelDataConvertException.getRowIndex() + 1, excelDataConvertException.getColumnIndex(), cellMsg);
                log.error(errorMsg);
            }
            log.error("数据导入失败,", e);
        }
        return CommonResponse.general(CommonResponseEnum.FAIL, errorMsg);
    }

    @SysLog(objectType = OperateObjectEnum.CMS_SERIES, operateType = OperateTypeEnum.UPDATE, objectIds = "#cmsSeriesDto.id", objectNames = "#cmsSeriesDto.name")
    @ApiOperation(value = "修改")
    @PutMapping("/update")
    public CommonResponse updateById(@RequestBody CmsSeriesDto cmsSeriesDto) {
        cmsSeriesDto.validRequestResource();
        cmsSeriesDto.validSeriesUpdate();
        return cmsSeriesService.up(cmsSeriesDto);
    }

    @SysLog(objectType = OperateObjectEnum.CMS_SERIES, operateType = OperateTypeEnum.UPDATE, objectIds = "#cmsSeriesDto.id", objectNames = "#cmsSeriesDto.name")
    @ApiOperation(value = "修改(不更视频媒介信息)")
    @PutMapping("/updateWithoutMov")
    public CommonResponse updateByIdWithoutMov(@RequestBody CmsSeriesDto cmsSeriesDto) {
        cmsSeriesDto.validRequestResource();
        cmsSeriesDto.validSeriesUpdate();
        return cmsSeriesService.upWithoutMov(cmsSeriesDto);
    }

    @ApiOperation(value = "子集管理列表")
    @GetMapping("/subset/page")
    public CommonResponse<Page> getByProgramList(@Valid @RequestParam(name = "id", required = true) String id, Page page) {
        return CommonResponse.success(cmsSeriesService.selectByPage(page, id));
    }

    @ApiOperation(value = "子集管理列表")
    @PostMapping("/subset/getProgramListBySeries")
    public CommonResponse<Page> getProgramListBySeries(@RequestBody CmsProgramDO cmsProgramDO) {
        Page page = new Page();
        page.setCurrent(cmsProgramDO.getCurrent());
        page.setSize(cmsProgramDO.getSize());
        return CommonResponse.success(cmsSeriesService.getProgramListBySeries(cmsProgramDO));
    }

    @ApiOperation(value = "绑定子集列表")
    @GetMapping("bindProgram")
    public CommonResponse<Page> getBybindProgramList(@Valid @RequestParam Map<String, String> param) {
        String cpId = param.get("cpId");
        String name = param.get("name");
        Integer releaseStatus = null;
        String status = param.get("releaseStatus");
        if (ObjectUtil.isNotEmpty(status)) {
            releaseStatus = Integer.parseInt(status);
        }
        long current = Long.parseLong(param.get("current"));
        long size = Long.parseLong(param.get("size"));
        Page page = new Page();
        page.setSize(size);
        page.setCurrent(current);
        return CommonResponse.success(cmsSeriesService.selectBybindProgramPage(page, cpId, name, releaseStatus));
    }

    @SysLog(objectType = OperateObjectEnum.CMS_SERIES, operateType = OperateTypeEnum.SAVE, objectIds = "#cmsProgramDto.seriesId")
    @ApiOperation(value = "绑定子集")
    @PutMapping("/subset/bind")
    public CommonResponse bindProgram(@RequestBody CmsProgramDto cmsProgramDto) {
        return cmsSeriesService.bindProgram(cmsProgramDto.getSeriesId().toString(), cmsProgramDto.getIds());
    }

    @SysLog(objectType = OperateObjectEnum.CMS_SERIES, operateType = OperateTypeEnum.DELETE,objectIds =  "#cmsProgramDto.seriesId")
    @ApiOperation(value = "移除子集")
    @DeleteMapping("/deleteBySubset")
    public CommonResponse deleteBySubset(@RequestBody CmsProgramDto cmsProgramDto) {
        return cmsSeriesService.deleteById(cmsProgramDto.getSeriesId().toString(), cmsProgramDto.getIds());
    }

    @ApiOperation(value = "缺集信息")
    @GetMapping("/lack")
    public CommonResponse lack(@RequestParam("ids") String ids) {
        return cmsSeriesService.lack(ids);

    }

    @ApiOperation(value = "详情")
    @GetMapping("/getById")
    public CommonResponse<CmsSeries> getById(@Valid @RequestParam(name = "id", required = true) Long id) {
        return CommonResponse.success(cmsSeriesService.getById(id));
    }


    @ApiOperation(value = "缺集检测测试")
    @GetMapping("/test")
    public void test() {
        cmsSeriesService.test();
    }


    @ApiOperation(value = "剧集绑定集数校验")
    @GetMapping("/check")
    public CommonResponse<Boolean> check(@RequestParam("id") Long id, @RequestParam("episodeIndexs") List<Integer> episodeIndexs) {
        return CommonResponse.success(cmsSeriesService.check(id, episodeIndexs));
    }


    @GetMapping("/relatePreview")
    public CommonResponse<Boolean> relatePreview(@RequestParam("relateCount") Integer relateCount) {
        return cmsSeriesService.relateRelease(relateCount);
    }

    @PostMapping("/seriesStatistic")
    public CommonResponse<List<StatisticsIn>> seriesStatistic(@RequestBody StatisticsInVo statisticsInVo, Request.Options options) {
        return cmsSeriesService.getSeriesStatistic(statisticsInVo);
    }

    @PostMapping("/selfSeriesStatistic")
    public CommonResponse<List<StatisticsInCheck>> selfSeriesStatistic(@RequestBody StatisticsInVo statisticsInVo, Request.Options options) {
        return cmsSeriesService.getSelfSeriesStatistic(statisticsInVo);
    }

    @PostMapping("/finalSeriesStatistic")
    public CommonResponse<List<StatisticsInFinal>> finalSeriesStatistic(@RequestBody StatisticsInVo statisticsInVo, Request.Options options) {
        return cmsSeriesService.getFinalSeriesStatistic(statisticsInVo);
    }

    @PostMapping("/againSeriesStatistic")
    public CommonResponse<List<StatisticsInAgain>> againSeriesStatistic(@RequestBody StatisticsInVo statisticsInVo, Request.Options options) {
        return cmsSeriesService.getAgainSeriesStatistic(statisticsInVo);
    }


    @PostMapping("/checkVolumnCount")
    public CommonResponse checkVolumnCount(@RequestBody CmsSeriesDto cmsSeriesDto) {
        return cmsSeriesService.checkVolumnCount(cmsSeriesDto);
    }
}
