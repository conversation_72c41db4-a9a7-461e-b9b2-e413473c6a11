package com.pukka.iptv.manage.service.cms.impl;

import cn.hutool.core.net.URLDecoder;
import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.pukka.iptv.common.base.constant.RedisKeyConstants;
import com.pukka.iptv.common.base.enums.CpCheckStatusEnum;
import com.pukka.iptv.common.base.enums.ProhibitStatusEnum;
import com.pukka.iptv.common.base.enums.VerifyStatusEnum;
import com.pukka.iptv.common.base.exception.CommonResponseException;
import com.pukka.iptv.common.core.constant.SymbolConstant;
import com.pukka.iptv.common.core.util.StringUtils;
import com.pukka.iptv.common.data.dto.CmsSeriesDO;
import com.pukka.iptv.common.data.dto.CmsSeriesEndDto;
import com.pukka.iptv.common.data.dto.DateFormatCompletionDto;
import com.pukka.iptv.common.data.model.cms.CmsSeries;
import com.pukka.iptv.common.data.vo.cms.CmsSeriesDetailVO;
import com.pukka.iptv.common.data.vo.cms.CmsSeriesVO;
import com.pukka.iptv.manage.export.model.ExcelTaskInfo;
import com.pukka.iptv.manage.export.model.ExportInfo;
import com.pukka.iptv.manage.export.task.ExportTask;
import com.pukka.iptv.manage.mapper.cms.CmsSeriesMapper;
import com.pukka.iptv.manage.service.cms.CmsSeriesCheckService;
import com.pukka.iptv.manage.service.cms.CmsSeriesService;
import com.pukka.iptv.manage.util.CommonUtils;
import com.pukka.iptv.manage.util.TimeResolutionUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.io.ByteArrayOutputStream;
import java.nio.charset.StandardCharsets;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Objects;

@Service
@Slf4j
public class CmsSeriesCheckServiceImpl extends ServiceImpl<CmsSeriesMapper, CmsSeries> implements CmsSeriesCheckService {
    @Autowired
    private CmsSeriesService cmsSeriesService;
    @Autowired
    private CmsSeriesMapper cmsSeriesMapper;

    /**
     * 剧集自审列表
     *
     * @param page
     * @param cmsSeries
     * @return
     */
    @Override
    public Page<CmsSeries> seriesSelfList(Page page, CmsSeriesVO cmsSeries, String startTime, String endTime) {
        LambdaQueryWrapper<CmsSeries> wrapper = initWrapper(cmsSeries, startTime, endTime);
        Page pageData = cmsSeriesService.page(page, wrapper);
        return pageData;
    }

    @Override
    public IPage<CmsSeries> seriesSelfList(Page page, CmsSeriesDO cmsSeriesDO, String startTime, String endTime, boolean flag) {

        if (cmsSeriesDO.getName() != null) {
            if (cmsSeriesDO.getName().contains("\n")) {
                //防止name为空字符串以及只有回车的情况  进行再一次校验
                cmsSeriesDO.setName(cmsSeriesDO.getName().trim());
                if (StringUtils.isNotEmpty(cmsSeriesDO.getName())) {
                    String[] names = CommonUtils.getNames(cmsSeriesDO.getName());
                    if (names.length > 0) {
                        cmsSeriesDO.setNameList(names);
                        cmsSeriesDO.setLikeOrinFlag(2);
                    }
                }
            } else {
                String[] names = CommonUtils.getNames(cmsSeriesDO.getName());
                if (names != null && names.length > 0) {
                    cmsSeriesDO.setNameList(names);
                    cmsSeriesDO.setNameLike(names[0]);
                    cmsSeriesDO.setLikeOrinFlag(1);
                }
            }
        }
        //开始、结束时间格式化
        DateFormatCompletionDto dateFormatCompletionDto = new DateFormatCompletionDto().setStartTime(startTime).setEndTime(endTime);
        Boolean aBoolean = TimeResolutionUtil.dateTimeCompare(dateFormatCompletionDto);
        cmsSeriesDO.setEndTime(dateFormatCompletionDto.getEndTime());
        cmsSeriesDO.setStartTime(dateFormatCompletionDto.getStartTime());
        IPage<CmsSeries> cmsProgramPage = cmsSeriesMapper.getUnauthorizedContentByCmsSeries(page, cmsSeriesDO);

        return cmsProgramPage;
    }


    /**
     * 剧集详情
     *
     * @param id
     * @return
     */
    @Override
    public CmsSeriesDetailVO seriesById(Long id) {
        CmsSeriesDetailVO vo = new CmsSeriesDetailVO();
        CmsSeries cmsSeries = cmsSeriesMapper.selectOne(
                Wrappers.lambdaQuery(CmsSeries.class)
                        .eq(CmsSeries::getId, id)
        );
        if (Objects.isNull(cmsSeries)) {
            throw new CommonResponseException("媒资不存在");
        }
        BeanUtils.copyProperties(cmsSeries, vo);
        List<String> ctccPrice = new ArrayList<>();
        List<String> cuccPrice = new ArrayList<>();
        List<String> cmccPrice = new ArrayList<>();
        //价格以顿号分开
        String cuccPriceStr = cmsSeries.getCuccPrice();
        if (StringUtils.isNotEmpty(cuccPriceStr)) {
            String[] cucc = cuccPriceStr.split(SymbolConstant.SEMICOLON, -1);
            for (String price : cucc) {
                cuccPrice.add(price);
            }
        }
        String ctccPriceStr = cmsSeries.getCtccPrice();
        if (StringUtils.isNotEmpty(ctccPriceStr)) {
            String[] ctcc = ctccPriceStr.split(SymbolConstant.SEMICOLON, -1);
            for (String price : ctcc) {
                ctccPrice.add(price);
            }
        }
        String cmccPriceStr = cmsSeries.getCmccPrice();
        if (StringUtils.isNotEmpty(cmccPriceStr)) {
            String[] cmcc = cmccPriceStr.split(SymbolConstant.SEMICOLON, -1);
            for (String price : cmcc) {
                cmccPrice.add(price);
            }
        }
        vo.setCucc(cuccPrice);
        vo.setCtcc(ctccPrice);
        vo.setCmcc(cmccPrice);
        return vo;
    }

    /**
     * 终重剧集列表
     *
     * @param page
     * @return
     */
    @Override
    public Page<CmsSeries> seriesEndList(Page page, CmsSeriesEndDto cmsSeries) {
        LambdaQueryWrapper<CmsSeries> wrapper = Wrappers.lambdaQuery(CmsSeries.class);
        wrapper.eq(CmsSeries::getCpCheckStatus, CpCheckStatusEnum.Pass.getValue())
                .eq(CmsSeries::getProhibitStatus, ProhibitStatusEnum.NO.getValue())
                .eq(Objects.nonNull(cmsSeries.getOpCheckStatus()), CmsSeries::getOpCheckStatus, cmsSeries.getOpCheckStatus())
                .eq(Objects.nonNull(cmsSeries.getMissedStatus()), CmsSeries::getMissedStatus, cmsSeries.getMissedStatus())
                .eq(Objects.nonNull(cmsSeries.getCpId()), CmsSeries::getCpId, cmsSeries.getCpId())
                .eq(Objects.nonNull(cmsSeries.getPgmCategoryId()), CmsSeries::getPgmCategoryId, cmsSeries.getPgmCategoryId())
                .eq(StringUtils.isNotEmpty(cmsSeries.getCpName()), CmsSeries::getCpName, cmsSeries.getCpName())
                .eq(StringUtils.isNotEmpty(cmsSeries.getPgmCategory()), CmsSeries::getPgmCategory, cmsSeries.getPgmCategory())
                .like(StringUtils.isNotEmpty(cmsSeries.getContentProvider()), CmsSeries::getContentProvider, cmsSeries.getContentProvider())
                .orderByDesc(CmsSeries::getId);
        //未换行，模糊查询,换行后精确查询
        if (cmsSeries.getName() != null) {
            if (cmsSeries.getName().contains("\n")) {
                //防止name为空字符串以及只有回车的情况  进行再一次校验
                cmsSeries.setName(cmsSeries.getName().trim());
                if (StringUtils.isNotEmpty(cmsSeries.getName())) {
                    String[] names = CommonUtils.getNames(cmsSeries.getName());
                    if (names.length > 0) {
                        wrapper.in(CmsSeries::getName, Arrays.asList(names));
                    }
                }
            } else {
                String[] names = CommonUtils.getNames(cmsSeries.getName());
                if (names != null && names.length > 0) {
                    wrapper.like(CmsSeries::getName, names[0]);
                }
            }
        }
        //重审只展示待审核状态且审核时间不为空
        if (cmsSeries.getVerifyStatus().equals(VerifyStatusEnum.RepeatVerify.getValue())) {
            wrapper.isNotNull(CmsSeries::getOpCheckTime).eq(CmsSeries::getOpCheckStatus, CpCheckStatusEnum.Await.getValue());
        } else {
            wrapper.notInSql(CmsSeries::getId, "SELECT id FROM `cms_series` where cp_check_status = 3 and op_check_time is not null and op_check_status = 1 ");
        }
        Page pageData = cmsSeriesService.page(page, wrapper);
        return pageData;
    }


    public static String[] DeCoder(String name) {
        if (ObjectUtil.isNotEmpty(name)) {
            String[] names = URLDecoder.decode(name, StandardCharsets.UTF_8).split("\n");
            for (int i = 0; i < names.length; i++) {
                names[i] = names[i].trim();
            }
            if (names.length <= 1) {
                return null;
            }
            return names;
        }
        return null;
    }

    public static String Coder(String name) {
        String[] names = URLDecoder.decode(name, StandardCharsets.UTF_8).split("\n");
        return names[0].trim();
    }

    private LambdaQueryWrapper<CmsSeries> initWrapper(CmsSeriesVO cmsSeries, String startTime, String endTime) {
        LambdaQueryWrapper<CmsSeries> wrapper = Wrappers.lambdaQuery(CmsSeries.class);
        wrapper.notExists(Objects.nonNull(cmsSeries.getGrant()) && cmsSeries.getGrant() == 0, "select 1 from bms_content bms where bms.cms_content_id=cms_series.id and bms.content_type in (3,4)")
                .exists(Objects.nonNull(cmsSeries.getGrant()) && cmsSeries.getGrant() == 1, "select 1 from bms_content bms where bms.cms_content_id=cms_series.id  and bms.content_type in (3,4)")
                .eq(Objects.nonNull(cmsSeries.getCpCheckStatus()), CmsSeries::getCpCheckStatus, cmsSeries.getCpCheckStatus())
                .eq(Objects.nonNull(cmsSeries.getOpCheckStatus()), CmsSeries::getOpCheckStatus, cmsSeries.getOpCheckStatus())
                .eq(Objects.nonNull(cmsSeries.getMissedStatus()), CmsSeries::getMissedStatus, cmsSeries.getMissedStatus())
                .eq(Objects.nonNull(cmsSeries.getCpId()), CmsSeries::getCpId, cmsSeries.getCpId())
                .eq(Objects.nonNull(cmsSeries.getPgmCategoryId()), CmsSeries::getPgmCategoryId, cmsSeries.getPgmCategoryId())
                .like(ObjectUtil.isNotEmpty(cmsSeries.getOriginalName()), CmsSeries::getOriginalName, cmsSeries.getOriginalName())
                .eq(StringUtils.isNotEmpty(cmsSeries.getPgmCategory()), CmsSeries::getPgmCategory, cmsSeries.getPgmCategory())
                .eq(StringUtils.isNotEmpty(cmsSeries.getCpName()), CmsSeries::getCpName, cmsSeries.getCpName())
                .like(StringUtils.isNotEmpty(cmsSeries.getContentProvider()), CmsSeries::getContentProvider, cmsSeries.getContentProvider())
                .orderByDesc(CmsSeries::getId);
        if (StringUtils.isNotEmpty(endTime)) {
            wrapper.apply("create_time <= {0}", endTime);
        }
        if (StringUtils.isNotEmpty(startTime)) {
            wrapper.apply("create_time >= {0}", startTime);
        }
        //未换行，模糊查询,换行后精确查询
        if (cmsSeries.getName() != null) {
            if (cmsSeries.getName().contains("\n")) {
                //防止name为空字符串以及只有回车的情况  进行再一次校验
                cmsSeries.setName(cmsSeries.getName().trim());
                if (StringUtils.isNotEmpty(cmsSeries.getName())) {
                    String[] names = CommonUtils.getNames(cmsSeries.getName());
                    if (names.length > 0) {
                        wrapper.in(CmsSeries::getName, Arrays.asList(names));
                    }
                }
            } else {
                String[] names = CommonUtils.getNames(cmsSeries.getName());
                if (names != null && names.length > 0) {
                    wrapper.like(CmsSeries::getName, names[0]);
                }
            }
        }
        return wrapper;
    }

    @Autowired
    private ExportTask<CmsSeries> exportTask;
    @Override
    public Object export( CmsSeriesVO cmsSeries, String startTime, String endTime) {
        long ms = System.currentTimeMillis();
        LambdaQueryWrapper<CmsSeries> wrapper =  initWrapper(cmsSeries, startTime, endTime);
        ExportInfo<CmsSeries>  exportInfo = new ExportInfo<>();
        exportInfo.setOut(new ByteArrayOutputStream())
                .setSheetName("剧集")
                .setQueryWrapper(wrapper)
                .setCacheKey(RedisKeyConstants.SERIES_SELECT_EXPORT)
                .setPojoClass(CmsSeries.class)
                .setBaseMapper(cmsSeriesMapper);
        ExcelTaskInfo excelTaskInfo = exportTask.startProcess(exportInfo).dowork();
        log.info("总导出时长为 {} ms", (System.currentTimeMillis() - ms));
        return JSON.toJSON(excelTaskInfo);

    }
}
