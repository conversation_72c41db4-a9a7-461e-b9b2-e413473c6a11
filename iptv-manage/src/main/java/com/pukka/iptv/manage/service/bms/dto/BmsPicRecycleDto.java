package com.pukka.iptv.manage.service.bms.dto;

import com.pukka.iptv.common.base.enums.ContentTypeEnum;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;
import org.checkerframework.checker.units.qual.A;

import java.util.List;

/**
 * @Author: wz
 * @Date: 2021/11/30 20:18
 * @Description:
 */
@Getter
@Setter
@Accessors(chain = true)
public class BmsPicRecycleDto {
    private boolean preCheckPass = false;
    private List<Long> contentIds;
    private ContentTypeEnum type;
}
