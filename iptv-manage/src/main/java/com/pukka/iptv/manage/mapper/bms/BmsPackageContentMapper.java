package com.pukka.iptv.manage.mapper.bms;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.pukka.iptv.common.data.model.bms.BmsContent;
import com.pukka.iptv.common.data.model.bms.BmsPackageContent;
import com.pukka.iptv.common.data.model.order.SubOrderMappingsEntity;
import com.pukka.iptv.common.data.vo.bms.BmsPackageBindContentVO;
import com.pukka.iptv.common.data.vo.req.BmsPackageBindContentQueryReq;
import com.pukka.iptv.common.data.vo.req.BmsPackageNotBindContentQueryReq;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Set;

/**
 * 包内容
 *
 * <AUTHOR>
 * @date 2021-08-27 10:20:56
 */

@Mapper
public interface BmsPackageContentMapper extends BaseMapper<BmsPackageContent>{
    // 产品包查询绑定的内容
    IPage<BmsPackageBindContentVO> queryBindContent(Page<BmsPackageBindContentVO> page, @Param("queryReq") BmsPackageBindContentQueryReq queryReq);

    // 产品包查询 未绑定并发布成功 的内容
    IPage<BmsContent> queryNotBindContent(Page<BmsContent> page, @Param("queryReq") BmsPackageNotBindContentQueryReq queryReq);

    int updatePublishStatus(@Param("entityList") List<SubOrderMappingsEntity> orderMappingsEntities, @Param("spIdList") List<Long> spIdList);

    List<BmsPackageContent> listValidMediaCodeByDate(@Param("spIdList") List<String> spIdList, @Param("spNum") int spNum);

}
