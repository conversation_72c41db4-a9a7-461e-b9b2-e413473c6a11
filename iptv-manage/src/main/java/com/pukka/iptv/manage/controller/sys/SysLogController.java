package com.pukka.iptv.manage.controller.sys;

import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.pukka.iptv.common.base.vo.CommonResponse;
import com.pukka.iptv.common.data.model.sys.SysLog;
import com.pukka.iptv.common.data.util.TimeUtil;
import com.pukka.iptv.common.data.vo.IdList;
import com.pukka.iptv.common.log.enums.OperateObjectEnum;
import com.pukka.iptv.common.log.enums.OperateTypeEnum;
import com.pukka.iptv.manage.service.sys.SysLogService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.AllArgsConstructor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;

/**
 *
 * @author: chenyudong
 * @date: 2021-09-29 14:50:58
 */

@RestController
@AllArgsConstructor
@RequestMapping(value = "/sysLog", produces = MediaType.APPLICATION_JSON_VALUE)
@Api(tags="sysLog管理")
public class SysLogController {

    @Autowired
    private SysLogService sysLogService;

    @ApiOperation(value = "分页获取列表")
    @GetMapping("/page" )
    public CommonResponse<Page> page(@Valid Page<SysLog> page,SysLog sysLog) {
        return  CommonResponse.success(sysLogService.page(page,Wrappers.<SysLog>lambdaQuery()
                .like(StringUtils.isNotEmpty(sysLog.getCreatorUsername()),SysLog::getCreatorUsername,sysLog.getCreatorUsername())
                .eq(StringUtils.isNotEmpty(sysLog.getOperateType()),SysLog::getOperateType,sysLog.getOperateType())
                .like(StringUtils.isNotEmpty(sysLog.getObjectNames()),SysLog::getObjectNames,sysLog.getObjectNames())
                .eq(StringUtils.isNotEmpty(sysLog.getObjectType()),SysLog::getObjectType,sysLog.getObjectType())
                .eq(StringUtils.isNotEmpty(sysLog.getOperateResult()),SysLog::getOperateResult,sysLog.getOperateResult())
                .ge(StringUtils.isNotEmpty(sysLog.getStartTime()),SysLog::getCreateTime,sysLog.getStartTime()+ " 00:00:00")
                .le(StringUtils.isNotEmpty(sysLog.getEndTime()),SysLog::getCreateTime,sysLog.getEndTime()+ " 23:59:59")
                .isNotNull(SysLog::getCreateTime)
                .orderByDesc(SysLog::getId)));
    }

    @ApiOperation(value = "详情")
    @GetMapping("/getById")
    public CommonResponse<SysLog> getById(@Valid @RequestParam(name = "id", required = true)  Long id) {
        return CommonResponse.success(sysLogService.getById(id));
    }

    @ApiOperation(value = "新增",hidden = true)
    @PostMapping("/save")
    public CommonResponse<Boolean> save(@Valid @RequestBody SysLog sysLog) {
        return  CommonResponse.success(sysLogService.save(sysLog));
    }

    @ApiOperation(value = "修改",hidden = true)
    @PutMapping("/update")
    public CommonResponse<Boolean> updateById(@Valid @RequestBody SysLog sysLog) {
        return CommonResponse.success(sysLogService.updateById(sysLog));
    }

    @com.pukka.iptv.common.log.annotation.SysLog(objectType = OperateObjectEnum.SYS_LOG, operateType = OperateTypeEnum.DELETE, objectIds = "#id")
    @ApiOperation(value = "删除",hidden = true)
    @DeleteMapping("/deleteById" )
    public CommonResponse<Boolean> deleteById(@Valid @RequestParam(name = "id", required = true) Long id) {
        return  CommonResponse.success(sysLogService.removeById(id));
    }

    @com.pukka.iptv.common.log.annotation.SysLog(objectType = OperateObjectEnum.SYS_LOG, operateType = OperateTypeEnum.DELETE, objectIds = "#idList.ids")
    @ApiOperation(value = "批量删除")
    @DeleteMapping("/deleteByIds" )
    public CommonResponse<Boolean> deleteByIds(@Valid @RequestBody IdList idList) {
        return  CommonResponse.success(sysLogService.removeByIds(idList.getIds()));
    }

}
