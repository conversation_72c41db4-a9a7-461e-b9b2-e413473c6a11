package com.pukka.iptv.manage.service.sys.impl;

import com.pukka.iptv.common.base.constant.ObjectsTypeConstants;
import com.pukka.iptv.common.base.enums.ActionEnums;
import com.pukka.iptv.common.base.enums.AutoPublishResultEmum;
import com.pukka.iptv.common.base.enums.OrderBaseResultEnum;
import com.pukka.iptv.common.base.enums.PublishStatusEnum;
import com.pukka.iptv.common.core.util.SafeUtil;
import com.pukka.iptv.common.data.model.order.SubOrderMappingsEntity;
import com.pukka.iptv.common.data.model.sys.AutoPublishMappingList;
import com.pukka.iptv.manage.service.bms.*;
import com.pukka.iptv.manage.service.cms.CmsPictureService;
import com.pukka.iptv.manage.service.cms.CmsProgramService;
import com.pukka.iptv.manage.service.cms.CmsResourceService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.util.List;
import java.util.stream.Collectors;

/**
 * 处理自动发布中 在本平台的Mapping 关系，SP
 */
@Service
@Slf4j
public class AutoPublishMappingService {

    @Autowired
    CmsPictureService cmsPictureService;
    @Autowired
    BmsPictureService bmsPictureService;
    @Autowired
    BmsCategoryContentService bmsCategoryContentService;
    @Autowired
    BmsPackageContentService bmsPackageContentService;
    @Autowired
    BmsCategoryChannelService bmsCategoryChannelService;
    @Autowired
    CmsResourceService cmsResourceService;
    @Autowired
    BmsProgramService bmsProgramService;
    @Autowired
    CmsProgramService cmsProgramService;


    /**
     * 将对象和mapping关系分类
     *
     * @param subOrderMappingsEntities
     * @param result
     */
    private void parseEntity(List<SubOrderMappingsEntity> subOrderMappingsEntities, Integer result, AutoPublishMappingList mappingList) {

        log.info("解析Mapping关系");
        subOrderMappingsEntities.forEach(subOrderMappingsEntity -> {
            /* 重构mappingEntity，将发布状态设置为最终需要的状态，且将满足条件的添加到删除队列 */
            rebuildSubOrderMappingsEntity(subOrderMappingsEntity, result);
            String elementType = subOrderMappingsEntity.getElementType();
            String parentType = subOrderMappingsEntity.getParentType();
            switch (parentType) {
                case ObjectsTypeConstants.PICTURE:
                    addToList(subOrderMappingsEntity,
                            mappingList.getPictureMapping(),
                            mappingList.getDeletePictureMapping());
                    break;
                case ObjectsTypeConstants.PACKAGE:
                    if (ObjectsTypeConstants.PHYSICAL_CHANNEL.equals(elementType)) {
                        log.info("不支持的Mapping类型 ParentType = {}， ElementType = {} ", parentType, elementType);
                        break;
                    }
                    addToList(subOrderMappingsEntity,
                            mappingList.getPackageContent(),
                            mappingList.getDeletePackageContent());
                    break;
                case ObjectsTypeConstants.CATEGORY:
                    if (ObjectsTypeConstants.CHANNEL.equals(elementType)) {
                        addToList(subOrderMappingsEntity,
                                mappingList.getCategoryChannel(),
                                mappingList.getDeleteCategoryChannel());
                        break;
                    }
                    if (ObjectsTypeConstants.SERIES.equals(elementType) || ObjectsTypeConstants.PROGRAM.equals(elementType)) {
                        addToList(subOrderMappingsEntity,
                                mappingList.getCategoryContent(),
                                mappingList.getDeleteCategoryContent());
                        break;
                    }
                    log.info("不支持的Mapping类型 ParentType = {}， ElementType = {} ", parentType, elementType);
                    break;
                case ObjectsTypeConstants.SERIES:
                    if (ObjectsTypeConstants.MOVIE.equals(elementType)) {
                        //视频关系
                        addToList(subOrderMappingsEntity,
                                mappingList.getMovieMapping(),
                                mappingList.getDeleteMovieMapping());
                        break;
                    }
                    if (ObjectsTypeConstants.HTMLCONTENT.equals(elementType)) {
                        log.error("不支持的Mapping类型 ParentType = {}， ElementType = {} ", parentType, elementType);
                        break;
                    }
                    // 剧头子集关系
                    addToList(subOrderMappingsEntity,
                            mappingList.getSeriesProgram(),
                            mappingList.getDeleteSeriesProgram());
                    break;
                case ObjectsTypeConstants.PROGRAM:
                    //视频关系
                    addToList(subOrderMappingsEntity,
                            mappingList.getMovieMapping(),
                            mappingList.getDeleteMovieMapping());
                    break;
                default:
                    log.info("不支持的Mapping类型 ParentType = {}， ElementType = {} ", parentType, elementType);
                    break;
            }
        });
    }

    /**
     * 将元素添加到对应d的操作队列
     *
     * @param orderMappingsEntity
     * @param entityList
     * @param delList
     */
    private void addToList(SubOrderMappingsEntity orderMappingsEntity, List<SubOrderMappingsEntity> entityList, List<SubOrderMappingsEntity> delList) {
        log.info("自动发布 添加Object 到对应队列 {}", orderMappingsEntity);
        if (orderMappingsEntity.getPublishStatus().equals(PublishStatusEnum.ROLLBACK.getCode().toString())) {
            delList.add(orderMappingsEntity);
            return;
        }
        entityList.add(orderMappingsEntity);
    }

    private boolean changePubishStatus(List<Long> spIdList, AutoPublishMappingList mappingList) {
        log.info("执行自动发布mapping关系的发布状态更改");
        boolean totalResult = true;
        if (!CollectionUtils.isEmpty(mappingList.getCategoryContent())) {
            totalResult = judgeResult(bmsCategoryContentService.updatePublishStatus(mappingList.getCategoryContent(), spIdList), totalResult);
        }
        if (!CollectionUtils.isEmpty(mappingList.getCategoryChannel())) {
            totalResult = judgeResult(bmsCategoryChannelService.updatePublishStatus(mappingList.getCategoryChannel(), spIdList), totalResult);
        }
        if (!CollectionUtils.isEmpty(mappingList.getPackageContent())) {
            totalResult = judgeResult(bmsPackageContentService.updatePublishStatus(mappingList.getPackageContent(), spIdList), totalResult);
        }
        if (!CollectionUtils.isEmpty(mappingList.getPictureMapping())) {
            totalResult = judgeResult(bmsPictureService.updatePublishStatus(mappingList.getPictureMapping(), spIdList), totalResult);
        }
        if (!CollectionUtils.isEmpty(mappingList.getDeleteCategoryContent())) {
            totalResult = judgeResult(bmsCategoryContentService.updatePublishStatus(mappingList.getDeleteCategoryContent(), spIdList), totalResult);
        }
        if (!CollectionUtils.isEmpty(mappingList.getDeleteCategoryChannel())) {
            totalResult = judgeResult(bmsCategoryChannelService.updatePublishStatus(mappingList.getDeleteCategoryChannel(), spIdList), totalResult);
        }
        if (!CollectionUtils.isEmpty(mappingList.getDeletePackageContent())) {
            totalResult = judgeResult(bmsPackageContentService.updatePublishStatus(mappingList.getDeletePackageContent(), spIdList), totalResult);
        }
        totalResult = deleteCpMapping(spIdList, mappingList);
        return totalResult;
    }

    /**
     * cp关系发布删除操作
     * @param spIdList
     * @param mappingList
     * @return
     */
    private boolean deleteCpMapping(List<Long> spIdList, AutoPublishMappingList mappingList) {
        log.info("CP执行自动发布删除mapping关系的发布状态更改");
        boolean totalResult = true;
        //图片关系解绑
        if (!CollectionUtils.isEmpty(mappingList.getDeletePictureMapping())) {
            totalResult = judgeResult(cmsPictureService.autoFeedBackPicMappingDel(mappingList.getDeletePictureMapping(), spIdList), totalResult);
        }
        //媒资视频介质关系解绑
        if (!CollectionUtils.isEmpty(mappingList.getDeleteMovieMapping())) {
            totalResult = judgeResult(cmsResourceService.deleteByCodeAndSp(mappingList.getDeleteMovieMapping(), spIdList), totalResult);
        }
        //子集剧集关系解绑
        if (!CollectionUtils.isEmpty(mappingList.getDeleteSeriesProgram())) {
            totalResult = judgeResult(cmsProgramService.deleteCpMapping(mappingList.getDeleteSeriesProgram(), spIdList), totalResult);
        }
        return totalResult;
    }

    private boolean judgeResult(boolean proccessResult, boolean totalResult) {
        if (totalResult) {
            totalResult = proccessResult;
        }
        return totalResult;
    }

    /**
     * 执行工单Mapping 发布状态更改
     *
     * @param subOrderMappingsEntities
     * @param spIdList
     * @param result
     */
    @Transactional(rollbackFor = Exception.class)
    public boolean excute(List<SubOrderMappingsEntity> subOrderMappingsEntities, List<Long> spIdList, Integer result, boolean isFinish) {
        try {
            AutoPublishMappingList mappingList = new AutoPublishMappingList();
            //如果是所有sp处理结束则判断删除工单内容是否需要delete
            if (isFinish) {
                /* 对象关系分类 */
                List<SubOrderMappingsEntity> collect = subOrderMappingsEntities.stream()
                        .filter(orderMappingsEntity -> orderMappingsEntity.getAction().equals(ActionEnums.DELETE.getInfo()))
                        .collect(Collectors.toList());
                parseEntity(collect, result, mappingList);
                return deleteCpMapping(spIdList, mappingList);
            }
            parseEntity(subOrderMappingsEntities, result, mappingList);
            /* 更改SP状态 */
            return changePubishStatus(spIdList, mappingList);
        } catch (Exception e) {
            e.printStackTrace();
            log.error("自动发布修改Mapping状态失败 {}", e.fillInStackTrace());
            return false;
        }
    }

    /**
     * 重新设置Entity的发布状态，当为Delete且为运营商处理成功的，添加到Delete队列
     *
     * @param orderMappingsEntity
     * @param result
     * @retuen false: 非删除
     * true：已添加删除队列
     */
    private void rebuildSubOrderMappingsEntity(SubOrderMappingsEntity orderMappingsEntity, Integer result) {
        log.info("自动发布重新设定Mapping发布状态 {}", orderMappingsEntity);
        String action = SafeUtil.getString(orderMappingsEntity.getAction());

        if (ActionEnums.REGIST.getInfo().equals(action)) {
            if (AutoPublishResultEmum.FAILED.getCode().equals(result)) {
                orderMappingsEntity.setPublishStatus(PublishStatusEnum.FAILPUBLISH.getCode().toString());
                return;
            }
            if (AutoPublishResultEmum.SUCCESS.getCode().equals(result)) {
                orderMappingsEntity.setPublishStatus(PublishStatusEnum.PUBLISHING.getCode().toString());
                return;
            }
            if (OrderBaseResultEnum.FAILED.getCode().equals(result)) {
                orderMappingsEntity.setPublishStatus(PublishStatusEnum.FAILPUBLISH.getCode().toString());
                return;
            }
            if (OrderBaseResultEnum.SUCCESS.getCode().equals(result)) {
                orderMappingsEntity.setPublishStatus(PublishStatusEnum.PUBLISH.getCode().toString());
                return;
            }
        }
        if (ActionEnums.UPDATE.getInfo().equals(action)) {
            if (AutoPublishResultEmum.FAILED.getCode().equals(result)) {
                orderMappingsEntity.setPublishStatus(PublishStatusEnum.FAILUPDATE.getCode().toString());
                return;
            }
            if (AutoPublishResultEmum.SUCCESS.getCode().equals(result)) {
                orderMappingsEntity.setPublishStatus(PublishStatusEnum.UPDATING.getCode().toString());
                return;
            }
            if (OrderBaseResultEnum.FAILED.getCode().equals(result)) {
                orderMappingsEntity.setPublishStatus(PublishStatusEnum.FAILUPDATE.getCode().toString());
                return;
            }
            if (OrderBaseResultEnum.SUCCESS.getCode().equals(result)) {
                orderMappingsEntity.setPublishStatus(PublishStatusEnum.PUBLISH.getCode().toString());
                return;
            }
        }
        if (ActionEnums.DELETE.getInfo().equals(action)) {
            if (AutoPublishResultEmum.FAILED.getCode().equals(result)) {
                orderMappingsEntity.setPublishStatus(PublishStatusEnum.FAILROLLBACK.getCode().toString());
                return;
            }
            if (AutoPublishResultEmum.SUCCESS.getCode().equals(result)) {
                orderMappingsEntity.setPublishStatus(PublishStatusEnum.ROLLBACKING.getCode().toString());
                return;
            }
            if (OrderBaseResultEnum.FAILED.getCode().equals(result)) {
                orderMappingsEntity.setPublishStatus(PublishStatusEnum.FAILROLLBACK.getCode().toString());
                return;
            }
            if (OrderBaseResultEnum.SUCCESS.getCode().equals(result)) {
                orderMappingsEntity.setPublishStatus(PublishStatusEnum.ROLLBACK.getCode().toString());
                //所有的删除放在一个队列里
                return;
            }
        }
        log.error("不支持的Action类型 action= {}", action);
    }

    private void rebuildDeleteMappingsEntity(SubOrderMappingsEntity orderMappingsEntity, Integer result) {
        log.info("自动发布重新设定Mapping发布状态 {}", orderMappingsEntity);
        String action = SafeUtil.getString(orderMappingsEntity.getAction());
        if (ActionEnums.DELETE.getInfo().equals(action) && OrderBaseResultEnum.SUCCESS.getCode().equals(result)) {
            orderMappingsEntity.setPublishStatus(PublishStatusEnum.ROLLBACK.getCode().toString());
            //所有的删除放在一个队列里
        }
        log.error("不支持的Action类型 action= {}", action);
    }
}
