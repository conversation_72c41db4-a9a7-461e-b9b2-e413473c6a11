package com.pukka.iptv.manage.service.sys.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.pukka.iptv.common.base.constant.RedisKeyConstants;
import com.pukka.iptv.common.base.enums.*;
import com.pukka.iptv.common.base.exception.CommonResponseException;
import com.pukka.iptv.common.base.util.JwtTokenUtil;
import com.pukka.iptv.common.base.util.UUID;
import com.pukka.iptv.common.base.vo.SecurityUser;
import com.pukka.iptv.common.data.model.cms.CmsChannel;
import com.pukka.iptv.common.data.model.cms.CmsProgram;
import com.pukka.iptv.common.data.model.cms.CmsSeries;
import com.pukka.iptv.common.data.model.sys.*;
import com.pukka.iptv.common.redis.service.RedisService;
import com.pukka.iptv.manage.mapper.cms.CmsChannelMapper;
import com.pukka.iptv.manage.mapper.cms.CmsProgramMapper;
import com.pukka.iptv.manage.mapper.cms.CmsSeriesMapper;
import com.pukka.iptv.manage.mapper.sys.SysAuthorizationMapper;
import com.pukka.iptv.manage.mapper.sys.SysCpContentProviderMapper;
import com.pukka.iptv.manage.mapper.sys.SysCpMapper;
import com.pukka.iptv.manage.mapper.sys.SysStorageMapper;
import com.pukka.iptv.manage.service.cache.CacheServiceImpl;
import com.pukka.iptv.manage.service.sys.SysAuthorizationService;
import com.pukka.iptv.manage.service.sys.SysCpService;
import com.pukka.iptv.manage.service.sys.SysInPassageService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;

/**
 * @author: luo
 * @date: 2021-9-9 8:36:53
 */

@Service
public class SysCpServiceImpl extends ServiceImpl<SysCpMapper, SysCp> implements SysCpService {

    @Autowired
    private SysCpMapper sysCpMapper;
    @Autowired
    private SysStorageMapper sysStorageMapper;
    @Autowired
    private CmsSeriesMapper cmsSeriesMapper;
    @Autowired
    private CmsProgramMapper cmsProgramMapper;
    @Autowired
    private CmsChannelMapper cmsChannelMapper;
    @Autowired
    private SysAuthorizationMapper sysAuthorizationMapper;
    @Autowired
    private SysCpContentProviderMapper sysCpContentProviderMapper;
    @Autowired
    CacheServiceImpl cacheService;
    @Autowired
    RedisService redisService;


    @Autowired
    private SysInPassageService inPassageService;
    @Autowired
    private SysAuthorizationService sysAuthorizationService;

    @Override
    public Page<SysCp> page(Page<SysCp> page, SysCp sysCp) {
        QueryWrapper<SysCp> queryWrapper = Wrappers.query();
        if (sysCp.getStatus() != null) {
            queryWrapper.and(qw -> qw.eq("status", sysCp.getStatus()));
        } else {
            queryWrapper.ne("status", StatusEnum.DELETE.getCode());
        }
        if (!StringUtils.isBlank(sysCp.getName())) {
            queryWrapper.and(qw -> qw.like("name", sysCp.getName()));
        }
        if (sysCp.getBusinessType() != null) {
            queryWrapper.in("business_type", sysCp.getBusinessType(), BusinessTypeEnum.ALL.getCode());
        }
        queryWrapper.orderByDesc("id");
        return sysCp.selectPage(page, queryWrapper);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public boolean removeById(Long id) {
        SysCp entity = sysCpMapper.selectById(id);
        if (entity == null) {
            throw new CommonResponseException(CommonResponseEnum.DATA_EMPTY);
        }
        //1.判断是否绑定剧集
        CmsSeries cmsSeries = cmsSeriesMapper.selectOne(Wrappers.<CmsSeries>query().eq("cp_id", entity.getId()).last("limit 1"));
        if (cmsSeries != null) {
            throw new CommonResponseException("已绑定剧集,不能删除");
        }
        //2.判断是否绑定单集
        CmsProgram cmsProgram = cmsProgramMapper.selectOne(Wrappers.<CmsProgram>query().eq("cp_id", entity.getId()).last("limit 1"));
        if (cmsProgram != null) {
            throw new CommonResponseException("已绑定单集,不能删除");
        }
        //3.判断是否绑定频道
        CmsChannel cmsChannel = cmsChannelMapper.selectOne(Wrappers.<CmsChannel>query().eq("cp_id", entity.getId()).last("limit 1"));
        if (cmsChannel != null) {
            throw new CommonResponseException("已绑定频道,不能删除");
        }
        //删除
        SysCp temp = new SysCp();
        temp.setId(entity.getId());
        temp.setStatus(DeleteStatusEnum.DELETE.getCode());
        temp.updateById();
        deleteFromCache(entity);
        return true;
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public boolean removeByIds(List<Long> ids) {
        //判断参数
        if (ids == null || ids.size() == 0) {
            return false;
        }
        // 绑定注入通道失败      绑定内容失败
        ArrayList<String> contentFail = new ArrayList<>();
        ArrayList<String> channelFail = new ArrayList<>();
        ArrayList<String> inPassageFail = new ArrayList<>();
        ArrayList<String> authorizationFail = new ArrayList<>();
        for (Long id : ids) {
            SysCp entity = sysCpMapper.selectById(id);
            if (entity == null) {
                continue;
            }
            if (!StatusEnum.COME.getCode().equals(entity.getStatus())) {
                throw new CommonResponseException(CommonResponseEnum.DELETE_ERROR, "不能删除锁定的CP");
            }
            if (BusinessTypeEnum.MEDIA.getCode() == entity.getBusinessType()) {
                //1.判断是否绑定剧集
                CmsSeries cmsSeries = cmsSeriesMapper.selectOne(Wrappers.<CmsSeries>query().eq("cp_id", entity.getId()).last("limit 1"));
                if (cmsSeries != null) {
                    contentFail.add(entity.getName());
                    continue;
                }
                //2.判断是否绑定子集
                CmsProgram cmsProgram = cmsProgramMapper.selectOne(Wrappers.<CmsProgram>query().eq("cp_id", entity.getId()).last("limit 1"));
                if (cmsProgram != null) {
                    contentFail.add(entity.getName());
                    continue;
                }
            }
            if (BusinessTypeEnum.LIVE.getCode() == entity.getBusinessType()) {

                //3.判断是否绑定频道
                CmsChannel cmsChannel = cmsChannelMapper.selectOne(Wrappers.<CmsChannel>query().eq("cp_id", entity.getId()).last("limit 1"));
                if (cmsChannel != null) {
                    channelFail.add(entity.getName());
                    continue;
                }
            }
            // 判读是否关联注入通道
            SysInPassage inPassage = inPassageService.getOne(Wrappers.lambdaQuery(SysInPassage.class)
                    .ne(SysInPassage::getStatus, StatusEnum.DELETE.getCode())
                    .eq(SysInPassage::getCpId, id).last("limit 1"));
            if (inPassage != null) {
                inPassageFail.add(entity.getName());
                continue;
            }

            //判断是否关联授权合同
            SysAuthorization sysAuthorization = sysAuthorizationService.getOne(Wrappers.lambdaQuery(SysAuthorization.class)
                    .ne(SysAuthorization::getStatus, StatusEnum.DELETE.getCode())
                    .eq(SysAuthorization::getCpId, id).last("limit 1"));
            if (sysAuthorization != null) {
                authorizationFail.add(entity.getName());
                continue;
            }
            //删除
            SysCp temp = new SysCp();
            temp.setId(entity.getId());
            temp.setStatus(DeleteStatusEnum.DELETE.getCode());
            temp.updateById();
            deleteFromCache(temp);
        }

        String failMessage = "";
        if (contentFail.size() > 0) {
            failMessage += org.apache.commons.lang3.StringUtils.join(contentFail, ",") + " 已关联媒资无法删除";
        }
        if (channelFail.size() > 0) {
            failMessage += org.apache.commons.lang3.StringUtils.join(channelFail, ",") + " 已关联频道无法删除";
        }
        if (inPassageFail.size() > 0) {
            failMessage += org.apache.commons.lang3.StringUtils.join(contentFail, ",") + " 已绑定注入通道无法删除";
        }
        if (authorizationFail.size() > 0) {
            failMessage += org.apache.commons.lang3.StringUtils.join(authorizationFail, ",") + " 有相关的授权合同无法删除";
        }
        if (org.apache.commons.lang3.StringUtils.isNotBlank(failMessage)) {
            throw new CommonResponseException(CommonResponseEnum.DELETE_ERROR, failMessage);
        }
        return true;
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public boolean saveToCacheAndDB(SysCp sysCp) {
        if (sysCp == null || StringUtils.isBlank(sysCp.getName())
                || sysCp.getStorageId() == null || sysCp.getBusinessType() == null) {
            throw new CommonResponseException(CommonResponseEnum.ERROR_PARAMETER);
        }
        //判断cp名称是否重复
        Long nameNum = sysCpMapper.selectCount(Wrappers.<SysCp>query()
                .eq("name", sysCp.getName())
                .ne("status", DeleteStatusEnum.DELETE.getCode())
                .last("limit 1"));
        if (nameNum > 0) {
            throw new CommonResponseException(CommonResponseEnum.FAIL, "CP名称已存在，请重新填写");
        }
        //判读存储是否被禁用
        SysStorage sysStorage = sysStorageMapper.selectOne(Wrappers.<SysStorage>query()
                .eq("id", sysCp.getStorageId()).ne("status", DeleteStatusEnum.DELETE.getCode()));
        if (sysStorage == null) {
            throw new CommonResponseException(CommonResponseEnum.FAIL, "关联存储不存在,请重新选择");
        }
        if (!StatusEnum.COME.getCode().equals(sysStorage.getStatus())) {
            throw new CommonResponseException(CommonResponseEnum.FAIL, "关联存储已被禁用,请重新选择");
        }
        if (StringUtils.isBlank(sysCp.getCode())) {
            sysCp.setCode(UUID.randomUUID().toString().replace("-", ""));
        }
        Long num = sysCpMapper.selectCount(Wrappers.<SysCp>query()
                .eq("code", sysCp.getCode())
                .ne("status", DeleteStatusEnum.DELETE.getCode())
                .last("limit 1"));
        if (num > 0) {
            throw new CommonResponseException(CommonResponseEnum.FAIL, "唯一编码已存在，请重新填写");
        }
        //判断存储分配是否溢出
        Long totalSpaceStorage = null == sysStorage.getTotalSpace() ? 0L : sysStorage.getTotalSpace();
        Long totalSpaceOldCps = sysCpMapper.selectTotalSpaceByStorage(sysStorage.getId());
        if (sysCp.getTotalSpace() * 1024 * 1024 + totalSpaceOldCps >= totalSpaceStorage) {
            throw new CommonResponseException(CommonResponseEnum.FAIL, "存储分配超过当前存储剩余可分配空间");
        }
        Double spaceThreshold = sysCp.getSpaceThreshold();
        if (Double.doubleToLongBits(spaceThreshold) > Double.doubleToLongBits(1)
                || Double.doubleToLongBits(spaceThreshold) < Double.doubleToLongBits(0)) {
            throw new CommonResponseException(CommonResponseEnum.FAIL, "存储预警阈值需介于0-1之间（包含）");
        }
        sysCp.setTotalSpace(sysCp.getTotalSpace() * 1024 * 1024);
        boolean result = save(sysCp);
        //保存内容提供商
        if (sysCp.getContentProviders() != null && sysCp.getContentProviders().size() > 0) {
            saveContentProvider(sysCp.getContentProviders(), sysCp.getId(), sysCp.getName());
        }
        if (result) {
            try {
                cacheService.cacheCp(Collections.singletonList(sysCp));
                return true;
            } catch (Exception e) {
                throw new CommonResponseException("redis异常：" + e.getMessage());
            }
        } else {
            return false;
        }
    }

    //添加内容提供商
    private void saveContentProvider(List<SysCpContentProvider> provider, Long cpId, String cpName) {
        for (SysCpContentProvider entity : provider) {
            entity.setCpId(cpId);
            entity.setCpName(cpName);
            if (entity.getId() != null) {
                entity.updateById();
            } else {
                entity.setCode(UUID.randomUUID().toString().replace("-", ""));
                entity.insert();
            }
        }
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public boolean updateToCacheAndDB(SysCp sysCp) {
        if (sysCp == null || StringUtils.isBlank(sysCp.getName())
                || sysCp.getStorageId() == null || sysCp.getId() == null) {
            throw new CommonResponseException(CommonResponseEnum.ERROR_PARAMETER);
        }
        //判断cp名称是否重复
        Long nameNum = sysCpMapper.selectCount(Wrappers.<SysCp>query()
                .eq("name", sysCp.getName())
                .ne("status", DeleteStatusEnum.DELETE.getCode())
                .ne("id", sysCp.getId())
                .last("limit 1"));
        if (nameNum > 0) {
            throw new CommonResponseException(CommonResponseEnum.FAIL, "CP名称已存在，请重新填写");
        }
        //判读存储是否被禁用
        SysStorage sysStorage = sysStorageMapper.selectOne(Wrappers.<SysStorage>query()
                .eq("id", sysCp.getStorageId()).ne("status", DeleteStatusEnum.DELETE.getCode()));
        if (sysStorage == null) {
            throw new CommonResponseException(CommonResponseEnum.FAIL, "关联存储不存在,请重新选择");
        }
        if (!StatusEnum.COME.getCode().equals(sysStorage.getStatus())) {
            throw new CommonResponseException(CommonResponseEnum.FAIL, "关联存储已被禁用,请重新选择");
        }
        //判断存储分配是否溢出
        Long totalSpaceStorage = sysStorage.getTotalSpace();
        Long totalSpaceOldCps = sysCpMapper.selectTotalSpaceByStorageNoSelf(sysStorage.getId(), sysCp.getId());
        if (sysCp.getTotalSpace() * 1024 * 1024 + totalSpaceOldCps >= totalSpaceStorage) {
            throw new CommonResponseException(CommonResponseEnum.FAIL, "存储分配超过当前存储剩余可分配空间");
        }
        Double spaceThreshold = sysCp.getSpaceThreshold();
        if (Double.doubleToLongBits(spaceThreshold) > Double.doubleToLongBits(1)
                || Double.doubleToLongBits(spaceThreshold) < Double.doubleToLongBits(0)) {
            throw new CommonResponseException(CommonResponseEnum.FAIL, "存储预警阈值需介于0-1之间（包含）");
        }
        sysCp.setTotalSpace(sysCp.getTotalSpace() * 1024 * 1024);
        sysCp.setUpdateTime(new Date());
        updateById(sysCp);
        //先删除保存内容提供商
        //保存内容提供商
        if (sysCp.getContentProviders() != null && sysCp.getContentProviders().size() > 0) {
            saveContentProvider(sysCp.getContentProviders(), sysCp.getId(), sysCp.getName());
        }

        SysCp data = getById(sysCp.getId());
        try {
            cacheService.cacheCp(Collections.singletonList(data));
        } catch (Exception e) {
            throw new CommonResponseException("redis异常：" + e.getMessage());
        }
        return true;
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public boolean updateStatusById(SysCp sysCp) {
        if (sysCp == null || sysCp.getId() == null || sysCp.getStatus() == null) {
            throw new CommonResponseException(CommonResponseEnum.ERROR_PARAMETER);
        }
        SysCp temp = sysCpMapper.selectById(sysCp.getId());
        if (temp == null) {
            throw new CommonResponseException(CommonResponseEnum.EMPTY);
        }
        sysCp.updateById();
        temp.setStatus(sysCp.getStatus());
        if (sysCp.getStatus() == 1) {
            try {
                cacheService.cacheCp(Collections.singletonList(temp));
            } catch (Exception e) {
                throw new CommonResponseException("redis异常：" + e.getMessage());
            }
        } else {
            deleteFromCache(temp);
        }
        return true;
    }

    @Override
    public boolean deleteFromCache(SysCp sysCp) {
        try {
            redisService.deleteByHashKey(RedisKeyConstants.SYS_CP, String.valueOf(sysCp.getId()));
            redisService.deleteByHashKey(RedisKeyConstants.SYS_CP_CODE, String.valueOf(sysCp.getCode()));
        } catch (Exception e) {
            throw new CommonResponseException("redis异常：" + e.getMessage());
        }
        return true;
    }

    @Override
    public List<SysCp> findSysCpByType(Integer type, String name) {
        SecurityUser sysUser = JwtTokenUtil.getSecurityUser();
        if (SysUserTypeEnum.SUPER_ADMIN.getCode().equals(sysUser.getType())) {
            if (type != null && BusinessTypeEnum.getEnum(type) != null) {
                return this.list(Wrappers.lambdaQuery(SysCp.class).eq(SysCp::getStatus, StatusEnum.COME.getCode())
                        .like(org.springframework.util.StringUtils.hasText(name), SysCp::getName, name)
                        .in(SysCp::getBusinessType, type, BusinessTypeEnum.ALL.getCode()).orderByDesc(SysCp::getId));
            }
            //无type且为管理员时，直接展示全部CP
            return this.list(Wrappers.lambdaQuery(SysCp.class).eq(SysCp::getStatus, StatusEnum.COME.getCode())
                    .like(org.springframework.util.StringUtils.hasText(name), SysCp::getName, name).orderByDesc(SysCp::getId));
        }
        return sysCpMapper.findCpByType(sysUser.getId(), type, name, StatusEnum.COME.getCode());
    }

    @Override
    public SysCp getByName(String cpName) {
        LambdaQueryWrapper<SysCp> wrapper = Wrappers.<SysCp>lambdaQuery().eq(SysCp::getName, cpName).eq(SysCp::getStatus, 1).last("LIMIT 1");
        return getOne(wrapper);
    }

    @Override
    public Page pageStatistics(Page<SysCp> page, Long spId) {
        if (null == spId) {
            return sysCpMapper.selectPage(page, new LambdaQueryWrapper<SysCp>().ne(SysCp::getStatus, StatusEnum.DELETE.getCode()));
        }
        Integer count = sysCpMapper.searchCount(spId);
        if (count <= (page.getCurrent() - 1) * page.getSize()) {
            return page;
        }
        List<SysCp> sysCps = sysCpMapper.searchPage(spId, (page.getCurrent() - 1) * page.getSize(), page.getSize());
        page.setTotal(count);
        page.setRecords(sysCps);
        return page;
    }

    @Override
    public Page pageByStorage(Page<SysCp> page, Long storageId) {
        if (null == storageId) {
            throw new CommonResponseException("存储id不能为空");
        }
        return sysCpMapper.selectPage(page, new LambdaQueryWrapper<SysCp>().eq(SysCp::getStorageId, storageId));
    }

    public SysCp cpName(Long cpId) {
//        SysCp sysCp = sysCpMapper.selectById(cpId);
        return redisService.getCacheMapValue(RedisKeyConstants.SYS_CP, String.valueOf(cpId));
    }
}