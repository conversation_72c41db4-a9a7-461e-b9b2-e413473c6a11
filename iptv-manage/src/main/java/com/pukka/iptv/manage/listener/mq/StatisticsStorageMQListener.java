package com.pukka.iptv.manage.listener.mq;

import com.pukka.iptv.common.base.constant.RedisKeyConstants;
import com.pukka.iptv.common.rabbitmq.config.StatisticsStorageMQConfig;
import com.pukka.iptv.common.redis.service.RedisService;
import com.rabbitmq.client.Channel;
import lombok.extern.slf4j.Slf4j;
import org.springframework.amqp.rabbit.annotation.Exchange;
import org.springframework.amqp.rabbit.annotation.Queue;
import org.springframework.amqp.rabbit.annotation.QueueBinding;
import org.springframework.amqp.rabbit.annotation.RabbitListener;
import org.springframework.amqp.support.AmqpHeaders;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.messaging.handler.annotation.Header;
import org.springframework.stereotype.Component;

import java.util.Map;


/**
 * @Author: liqq
 * @CreateDate: 2022/9/17 13:02
 */
@Component
@Slf4j
public class StatisticsStorageMQListener {

    @Autowired
    private RedisService redisService;

    @RabbitListener(bindings = {@QueueBinding(
            value = @Queue(value = StatisticsStorageMQConfig.STATISTIC_STORAGE_QUEUE),
            exchange = @Exchange(value = StatisticsStorageMQConfig.STATISTIC_STORAGE_EXCHANGE),
            key = StatisticsStorageMQConfig.STATISTIC_STORAGE_ROUTING)}
    )
    public void recieved(Map<String, Long> map, Channel channel, @Header(AmqpHeaders.DELIVERY_TAG) Long deliveryTag) throws Exception {
        log.info("客户端视频下载上传文件大小 接收到参数：{}", map);
        try {
            // 将文件大小存入redis
            //byte[] body = mqMessage.getBody();
            //Map<String, Long> map = JSON.parseObject(JSON.toJSONString(JSON.parse(body)), Map.class);
            for (String s : map.keySet()) {
                Boolean aBoolean = redisService.hasHashKey(RedisKeyConstants.STATISTIC_STORAGE, s);
                if (!aBoolean) {
                    // 不存在，设置value为0
                    redisService.setCacheMapValue(RedisKeyConstants.STATISTIC_STORAGE, s, 0L);
                }
                Long value = redisService.getCacheMapValue(RedisKeyConstants.STATISTIC_STORAGE, s);
                redisService.setCacheMapValue(RedisKeyConstants.STATISTIC_STORAGE, s, value + map.get(s));
            }
        } catch (Exception e) {
            log.error("客户端视频下载上传文件大小 接收到参数：{}, 错误原因：{}", map,e.getMessage());
        } finally {
            channel.basicAck(deliveryTag, false);
        }
    }
}
