package com.pukka.iptv.manage.util.downloadUtils.dto;

import lombok.Data;
import lombok.experimental.Accessors;

import javax.servlet.http.HttpServletResponse;

/**
 * @author: chiron
 * Date: 2022/5/18 10:18
 * Description:
 */
@Data
@Accessors(chain = true)
public class RetrieveFileEntity {
    /**
     * ftp远端地址
     * ftp连接池模型
     * 针对单ftp存储使用，目前弃用
     */
    private String remotePath;
    /**
     * 介质名称
     */
    private String fileName;

    /**
     * 下载介质对象地址
     */
    private String fileUrl;
    /**
     * 浏览器对象
     */
    private HttpServletResponse response;
}