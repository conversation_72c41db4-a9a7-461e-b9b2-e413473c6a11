package com.pukka.iptv.manage.util;

import com.pukka.iptv.common.base.constant.RedisKeyConstants;
import com.pukka.iptv.common.base.exception.CommonResponseException;
import com.pukka.iptv.common.data.model.sys.SysStorage;
import com.pukka.iptv.common.data.model.sys.SysStorageDirctory;
import com.pukka.iptv.common.redis.service.RedisService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;


/**
 * <AUTHOR>
 * @Date 2021/10/25 11:25 上午
 * @description: 分发工具类
 * @Version 1.0
 */
@Slf4j
@Component
public class OutOperateUtil {
    @Autowired
    private RedisService redisService;

    public static final String OUT_PICTURE_FTP_PREFIX = "out_picture_ftp_prefix";
    public static final String OUT_MOVIE_FTP_PREFIX = "out_movie_ftp_prefix";
    /**
     * 替换媒资前缀地址
     *
     * @param storageId
     * @param fileUrl
     * @return
     */
    public String getMediaPrefix(String storageId, String fileUrl, String prefixType) {
        String result = "";
        try {
            if (StringUtils.isEmpty(storageId) || StringUtils.isEmpty(fileUrl)) {
                log.error("替换媒资前缀地址 ->>>>>> 传入参数storageId/fileUrl错误!");
                throw new CommonResponseException("替换媒资前缀地址失败,参数错误!");
            }
            //地址转换
            String values = SiteOperateUtil.getSingleMatchValue(fileUrl);
            //获取存储信息
            SysStorage storage = redisService.getCacheMapValue(RedisKeyConstants.SYS_STORAGE_KEY, storageId);
            switch (prefixType) {
                case OUT_MOVIE_FTP_PREFIX:
                    if (storage == null || StringUtils.isEmpty(storage.getOutMovieFtpPrefix())) {
                        log.warn("替换媒资前缀地址 ->>>>>> 替换视频前缀地址失败,分发源片存储不存在,使用源地址:{}!", fileUrl);
                        return fileUrl;
                    }
                    /** 视频地址又两种ftp，http 当为http地址时，使用http地址前缀 */
                    if (fileUrl.startsWith("ftp://") && StringUtils.isNotEmpty(storage.getOutMovieFtpPrefix())) {
                        result = storage.getOutMovieFtpPrefix() + values;
                        return result;
                    }
                    /** 特殊场景，将http转为ftp地址 */
                    if (fileUrl.startsWith("http://") && StringUtils.isNotEmpty(storage.getOutMovieHttpPrefix())) {
                        result = fileUrl.replace(storage.getOutMovieHttpPrefix(), storage.getOutMovieFtpPrefix());
                        return result;
                    }
                    log.warn("非法文件地址: {}", fileUrl);
                    break;
                case OUT_PICTURE_FTP_PREFIX:
                    if (storage == null || StringUtils.isEmpty(storage.getOutPictureFtpPrefix())) {
                        log.error("替换媒资前缀地址 ->>>>>> 替换图片前缀地址失败,分发图片存储地址前缀为空,使用源地址:{}!", fileUrl);
                        return fileUrl;
                    }
                    if (fileUrl.startsWith("ftp") && StringUtils.isNotEmpty(storage.getOutPictureFtpPrefix())) {
                        result = storage.getOutPictureFtpPrefix() + values;
                        return result;
                    }
                    /** 特殊场景，将http转为ftp地址 */
                    if (fileUrl.startsWith("http://") && StringUtils.isNotEmpty(storage.getOutPictureHttpPrefix())) {
                        //result = storage.getOutPictureFtpPrefix() + values;
                        result = fileUrl.replace(storage.getOutPictureHttpPrefix(), storage.getOutPictureFtpPrefix());
                        return result;
                    }
                    log.warn("非法文件地址: {}", fileUrl);
                    break;
                default:
                    break;
            }
        } catch (Exception exception) {
            log.error("替换媒资前缀地址 ->>>>>> 方法getMediaPrefix执行失败! errorMessage:", exception);
            throw new CommonResponseException("替换媒资前缀地址失败!");
        }
        return result;
    }

    /**
     * 组装ftp上传地址
     *
     * @param storage
     * @return
     */
    public String getUploadFtp(SysStorage storage) {
        StringBuffer result = new StringBuffer();
        try {
            if (storage == null) {
                return result.toString();
            }
            result.append("ftp://");
            String innerUrl = storage.getInnerUrl();
            SysStorageDirctory sysStorageDirctory = redisService.getCacheMapValue(
                    RedisKeyConstants.SYS_STORAGE_DIRCTORY_KEY,
                    StringUtils.joinWith(":", storage.getId(), 3, 2));
            if (sysStorageDirctory == null) {
                log.warn("组装ftp上传地址 ->>>>>> 获取sysStorageDirctory失败，sysStorageDirctory对象为空!");
                return null;
            }
            result.append(sysStorageDirctory.getAccount());
            result.append(":");
            result.append(sysStorageDirctory.getPassword() + "@" + innerUrl + "/");
        } catch (Exception exception) {
            log.error("组装ftp上传地址 ->>>>>> getUploadFtp执行失败！errorMessage:{}", exception);
            throw new CommonResponseException("组装ftp上传地址失败!");
        }
        return result.toString();
    }
}
