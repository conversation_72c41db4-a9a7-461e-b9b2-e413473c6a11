package com.pukka.iptv.manage.service.bms.dto;

import com.pukka.iptv.common.base.enums.ActionEnums;
import com.pukka.iptv.common.base.enums.ContentTypeEnum;
import lombok.*;
import lombok.experimental.Accessors;
import lombok.extern.slf4j.Slf4j;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @description:
 * @create 2021-08-30 11:18
 */
@Slf4j
@NoArgsConstructor
@AllArgsConstructor
@Setter
@Getter
@Accessors(fluent = true)
public class PublishParam {

    /**
     * 类型：1：单集   2：子集   3：电视剧   4：系列片   5：片花6：直播   7：物理频道   8：栏目   9：产品包   10：节目单  （11：图片12：视频介质   ）
     * 13：节目图片   14：剧集图片   15：产品包图片   16：栏目图片   17：频道图片18：栏目节目   19：栏目剧集   20：栏目频道   21：产品包节目   22：产品包剧集
     */
    @NotNull(message = "类型不能为空")
    private ContentTypeEnum contentTypeEnum;

    /**
     * spid
     */
    @NotBlank(message = "spid不能为空")
    private String spId;
    /**
     * spname
     */
    private String spName;

    /**
     * bms表的主键ids，以逗号分隔，批量操作
     */
    @NotBlank(message = "内容ID不能为空")
    private List<Long> contentIds;


    /**
     * key:bms图片变主键ids，发布媒资时携带图片的id
     * value:图片action，ActionEnums枚举类 info字段(REGIST,UPDATE)
     */
    private Map<String, String> picMap;

    /**
     * 显示名称
     */
    private String showName;

    /**
     * 动作1：REGIST，2：UPDATE，3：DELETE
     */
    @NotNull(message = "操作类型不能为空")
    private ActionEnums action;

}
