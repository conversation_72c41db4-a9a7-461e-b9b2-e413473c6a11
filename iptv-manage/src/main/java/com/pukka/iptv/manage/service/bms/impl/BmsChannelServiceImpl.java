package com.pukka.iptv.manage.service.bms.impl;

import cn.hutool.core.util.ObjectUtil;
import com.alibaba.nacos.common.utils.StringUtils;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.metadata.TableInfo;
import com.baomidou.mybatisplus.core.metadata.TableInfoHelper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.pukka.iptv.common.base.constant.PublishParamTypeConstants;
import com.pukka.iptv.common.base.enums.*;
import com.pukka.iptv.common.base.exception.BizException;
import com.pukka.iptv.common.base.exception.CommonResponseException;
import com.pukka.iptv.common.base.util.JwtTokenUtil;
import com.pukka.iptv.common.base.vo.SecurityUser;
import com.pukka.iptv.common.core.constant.SymbolConstant;
import com.pukka.iptv.common.data.model.OutParamExpand;
import com.pukka.iptv.common.data.model.bms.*;
import com.pukka.iptv.common.data.model.order.SubOrderObjectsEntity;
import com.pukka.iptv.common.data.model.sys.SysTenant;
import com.pukka.iptv.common.data.params.SysAuthorizationParam;
import com.pukka.iptv.common.data.util.Tuple2;
import com.pukka.iptv.manage.mapper.bms.BmsBaseMapper;
import com.pukka.iptv.manage.mapper.bms.BmsChannelMapper;
import com.pukka.iptv.manage.service.bms.*;
import com.pukka.iptv.manage.service.bms.common.LambdaTrans;
import com.pukka.iptv.manage.service.bms.common.WorkOrderOperation;
import com.pukka.iptv.manage.service.condition.RuleCondition;
import com.pukka.iptv.manage.service.condition.RuleResult;
import com.pukka.iptv.manage.service.condition.rule.LockStatusRule;
import com.pukka.iptv.manage.service.condition.rule.PublishStatusRule;
import com.pukka.iptv.manage.service.condition.rule.RecycleStatusRule;
import com.pukka.iptv.manage.service.condition.rule.enums.PublishCheck;
import com.pukka.iptv.manage.service.condition.rule.enums.RecycleCheck;
import com.pukka.iptv.manage.service.sys.SysTenantService;
import com.pukka.iptv.manage.util.CommonUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;

import java.util.*;
import java.util.stream.Collectors;

import static com.pukka.iptv.manage.service.condition.rule.enums.PublishCheck.CAN_PUBLISH;
import static com.pukka.iptv.manage.service.condition.rule.enums.PublishCheck.ING;


@Service
@Transactional
@Slf4j
public class BmsChannelServiceImpl extends ServiceImpl<BmsChannelMapper, BmsChannel> implements BmsChannelService {

    @Autowired
    private SysTenantService sysTenantService;
    @Autowired
    private BmsChannelMapper bmsChannelMapper;
    @Autowired
    private BmsPhysicalChannelService bmsPhysicalChannelService;
    @Autowired
    private BmsScheduleService bmsScheduleService;

    @Autowired
    private BmsPictureService pictureService;

    @Autowired
    private BmsCategoryChannelService categoryChannelService;

    @Autowired
    private BmsPhysicalChannelService physicalChannelService;

    @Autowired
    private WorkOrderOperation workOrderOperation;


    @Override
    public List<BmsChannel> getStatusByIds(List<Long> ids) {

        List<BmsChannel> bmsChannels = bmsChannelMapper.selectBatchIds(ids);
        return bmsChannels;
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public boolean channelPublish(List<BmsChannel> bmsChannels, Integer doSchedule, String scheduleTime, String widthPhysicalChannel) {
        //根据频道id查询是否都是可以发布的图片
        List<Long> channelIds = bmsChannels.stream().map(BmsChannel::getId).collect(Collectors.toList());
        RuleResult rr = RuleCondition.create()
                .and(PublishStatusRule.init(BmsChannel.class).policy(CAN_PUBLISH).data(channelIds))
                .execute();
        if (!rr.isPass()) {
            throw new BizException(rr.getMsg());
        }
        //获取到所有的图片按照逗号分隔

        //调用发布接口发布逻辑频道
        List<BmsChannel> waitUpdateList = bmsChannels.stream().filter(
                        (BmsChannel s) -> PublishStatusRule.CAN_UPDATE_LIST.contains(s.getPublishStatus()))
                .collect(Collectors.toList());
        List<BmsChannel> publishList = bmsChannels.stream().filter(
                        (BmsChannel s) -> PublishStatusRule.PUBLISH_LIST.contains(s.getPublishStatus()))
                .collect(Collectors.toList());
        Boolean result = false;
        if (waitUpdateList.size() > 0) {
            Tuple2<List<Long>, Map<String, String>> waitUpdatepictures = pictureService.getAllpictures(waitUpdateList, true);

            result = this.logicChannelPublish(waitUpdateList, waitUpdatepictures, ActionEnums.UPDATE, WidthPhysicalChannelEnum.getContentByCode(Integer.valueOf(widthPhysicalChannel)));
        }
        if (publishList.size() > 0) {
            Tuple2<List<Long>, Map<String, String>> publishpictures = pictureService.getAllpictures(publishList, true);
            result = this.logicChannelPublish(publishList, publishpictures, ActionEnums.REGIST, WidthPhysicalChannelEnum.getContentByCode(Integer.valueOf(widthPhysicalChannel)));
        }
        return result;

    }

    /**
     * 逻辑频道发布
     *
     * @param bmsChannels
     * @param picturess
     * @param actionEnums
     * @return
     */
    @Override
    public Boolean logicChannelPublish(List<BmsChannel> bmsChannels, Tuple2<List<Long>, Map<String, String>> picturess, ActionEnums actionEnums, Integer contentCode) {
        Map<String, String> picturesMap = new LinkedHashMap<>();
        List<Long> picturesIds = new ArrayList<>();
        Map<String, String> physicalChannelMap = new LinkedHashMap<>();
        Map<ActionEnums, List<BmsPhysicalChannel>> allphysicalChannelMap = new LinkedHashMap<>();
        if (picturess.getB().isPresent() && picturess.getA().isPresent()) {
            picturesMap.putAll(picturess.getB().get());
            picturesIds.addAll(picturess.getA().get());
        }
        List<Long> channelIds = bmsChannels.stream().map(BmsChannel::getId).collect(Collectors.toList());
        List<Long> cmsChannelIds = bmsChannels.stream().map(BmsChannel::getCmsChannelId).distinct().collect(Collectors.toList());
        //回收逻辑频道物理频道单独调用回收接口
        if (!ActionEnums.DELETE.equals(actionEnums) && ContentTypeEnum.CHANNEL_PHYSICAL.getValue().equals(contentCode)) {
            allphysicalChannelMap = physicalChannelService.getAllPhysicalChannel(cmsChannelIds, bmsChannels.get(0).getSpId(), !ActionEnums.DELETE.equals(actionEnums));
            if (ObjectUtil.isNotEmpty(allphysicalChannelMap)) {
                allphysicalChannelMap.forEach((action, bpcList) -> {
                    for (BmsPhysicalChannel physicalChannel : bpcList) {
                        physicalChannelMap.put(physicalChannel.getId() + "", action.getCode() + "");
                    }
                });
            }
        }
        //获取到扩展参数中需要发送的参数并清除
        Map<String, OutParamExpand> paramMap = getParamMap();
        paramMap.put(PublishParamTypeConstants.ORDER_PHYSICALANDCHANNEL, new OutParamExpand().setSpareMap(physicalChannelMap));
        paramMap.put(PublishParamTypeConstants.ORDER_PICTUREACTION, new OutParamExpand().setSpareMap(picturesMap));
        //调用发布接口
        Map<ActionEnums, List<BmsPhysicalChannel>> finalAllphysicalChannelMap = allphysicalChannelMap;
        return workOrderOperation.send(actionEnums, ContentTypeEnum.getByValue(contentCode), channelIds, picturesMap, bmsChannels.get(0).getSpId(), bmsChannels.get(0).getSpName(),
                (success, publishStatus, description) -> {
                    //根据返回的结果进行回调
                    this.update(new LambdaUpdateWrapper<BmsChannel>()
                            .in(BmsChannel::getId, channelIds)
                            .set(BmsChannel::getPublishStatus, CommonUtils.getEnumByAction(actionEnums, success))
                            .set(BmsChannel::getPublishTime, new Date())
                            .set(BmsChannel::getPublishDescription, success ? "" : description));
                    //当同时发布物理频道时进行物理频道回调
                    if (!ActionEnums.DELETE.equals(actionEnums) && ContentTypeEnum.CHANNEL_PHYSICAL.getValue().equals(contentCode)) {
                        bmsPhysicalChannelService.physicalChannelCallback(finalAllphysicalChannelMap, bmsChannels.get(0).getSpId(), actionEnums, success, description);
                    }
                    if (!ObjectUtils.isEmpty(picturesIds)) {
                        pictureService.pictureCallback(picturess, success);
                    }
                }, paramMap);

    }


    @Override
    @Transactional(rollbackFor = Exception.class)
    public String channelRollback(List<BmsChannel> bmsChannels) {
        List<Long> idList = bmsChannels.stream().map(BmsChannel::getId).collect(Collectors.toList());
        List<Long> categoryChannels = categoryChannelService.getCategoryChannels(bmsChannels);
        if (categoryChannels != null) {
            throw new BizException("请将频道与栏目关系全部回收再进行频道回收");
        }
        RuleCondition.create()
                .and(RecycleStatusRule.init(BmsChannel.class).data(idList).policy(RecycleCheck.CAN_NOT_RECYCLE))
                .execute().check();
        //当频道状态为发布成功时，才能进行回收物理频道
        rollbackPhysicalChannel(bmsChannels);
        Tuple2<List<Long>, Map<String, String>> allpictures = pictureService.getAllpictures(bmsChannels, false);
        this.logicChannelPublish(bmsChannels, allpictures, ActionEnums.DELETE, ContentTypeEnum.CHANNEL.getValue());
        return "回收接口调用成功";
    }


    @Override
    @Transactional(rollbackFor = Exception.class)
    public String channelRollbackAll(List<BmsChannel> bmsChannels) {
        List<Long> idList = bmsChannels.stream().map(BmsChannel::getId).collect(Collectors.toList());
        RuleCondition.create()
                .and(RecycleStatusRule.init(BmsChannel.class).data(idList).policy(RecycleCheck.CAN_NOT_RECYCLE))
                .execute().check();
        List<Long> categoryChannels = getCategoryChannels(bmsChannels, ActionEnums.DELETE);

        //回收物理频道
        rollbackPhysicalChannel(bmsChannels);
        //获取该频道对应的所有可回收的栏目关系
        //若物理频道以及栏目与频道关系全部回收成功则回收频道
        if (!CollectionUtils.isEmpty(categoryChannels)) {
            categoryChannelService.categoryChannelsPublishAndRollback(bmsChannels.get(0).getSpId(), bmsChannels.get(0).getSpName(), categoryChannels, ActionEnums.DELETE);
        }
        //获取到所有的图片
        Tuple2<List<Long>, Map<String, String>> allpictures = pictureService.getAllpictures(bmsChannels, false);
        logicChannelPublish(bmsChannels, allpictures, ActionEnums.DELETE, ContentTypeEnum.CHANNEL.getValue());
        return "一键回收成功";
    }

    private void rollbackPhysicalChannel(List<BmsChannel> bmsChannels) {
        List<Long> ids = bmsChannels.stream().map(BmsChannel::getCmsChannelId).collect(Collectors.toList());
        Map<ActionEnums, List<BmsPhysicalChannel>> deletePhysicalChannelMap = physicalChannelService.getAllPhysicalChannel(ids, bmsChannels.get(0).getSpId(), false);
        if (!CollectionUtils.isEmpty(deletePhysicalChannelMap.get(ActionEnums.DELETE))) {
            physicalChannelService.physicalChannelPublishAndRollback(deletePhysicalChannelMap.get(ActionEnums.DELETE), deletePhysicalChannelMap.get(ActionEnums.DELETE).get(0).getSpId(), ActionEnums.DELETE);
        }
    }

    /**
     * 根据
     *
     * @param channels
     * @param actionEnums
     * @return
     */
    public List<Long> getCategoryChannels(List<BmsChannel> channels, ActionEnums actionEnums) {
        List<Long> ids = channels.stream().map(BmsChannel::getId).collect(Collectors.toList());
        LambdaQueryWrapper<BmsCategoryChannel> wrapper = Wrappers.lambdaQuery(BmsCategoryChannel.class)
                .select(BmsCategoryChannel::getId, BmsCategoryChannel::getCategoryId)
                .in(BmsCategoryChannel::getBmsChannelId, ids);
        if (ActionEnums.DELETE.equals(actionEnums)) {
            wrapper.notIn(BmsCategoryChannel::getPublishStatus, RecycleStatusRule.NOT_RECYCLE_LIST);
        }
        List<Map<String, Object>> list = categoryChannelService.listMaps(wrapper);
        if (CollectionUtils.isEmpty(list)) {
            return null;
        }
        //找出栏目和内容id
        List<Long> categoryChannelIds = new ArrayList<>(list.size());
        List<Long> categoryIds = new ArrayList<>(list.size());
        LambdaTrans<BmsCategoryChannel> lt = new LambdaTrans<>();
        list.forEach(item ->
        {
            categoryChannelIds.add(lt.trans(item, BmsCategoryChannel::getId, Long.class));
            categoryIds.add(lt.trans(item, BmsCategoryChannel::getCategoryId, Long.class));
        });
        RuleCondition.create()
                .and(LockStatusRule.init(BmsCategory.class).data(categoryIds))
                .execute().check();
        return categoryChannelIds;
    }

    @Override
    public IPage<BmsChannel> getChannelsPage(Page<BmsChannel> page, BmsChannel bmsChannel, String categoryId, Integer status) {
        String name = bmsChannel.getName();
        String[] channelNames = null;
        if (name != null) {
            //判断name是否含有回车 若含有回车则进行精准多条查询
            if (name.contains("\n")) {
                //防止name为空字符串以及只有回车的情况  进行再一次校验
                bmsChannel.setName(bmsChannel.getName().trim());
                if (StringUtils.isNotEmpty(name)) {
                    channelNames = CommonUtils.getNames(name);
                }
                bmsChannel.setName(null);
            }
        }
        // 租户权限过滤
        SecurityUser securityUser = JwtTokenUtil.getSecurityUser();
        List<SysTenant> sysTenants = new ArrayList<>();
        if (SysUserTypeEnum.TENANT.getCode().equals(securityUser.getType())){
            sysTenants = sysTenantService.listByUserId(securityUser.getId());
        }
        Set<Long> cpIdSet = new HashSet<>();
        if (org.apache.commons.lang3.ObjectUtils.isNotEmpty(sysTenants)){
            List<SysTenant> spLimitList = sysTenants.stream()
                    .filter(sysTenant -> {
                        List<Long> spIdList = Arrays.stream(sysTenant.getSpIds()
                                .split(SymbolConstant.COMMA))
                                .map(Long::parseLong).collect(Collectors.toList());
                        return spIdList.contains(bmsChannel.getSpId());
                    }).collect(Collectors.toList());
            if (org.apache.commons.lang3.ObjectUtils.isNotEmpty(spLimitList)){
                for (SysTenant sysTenant : spLimitList) {
                    List<Long> cpIdList = Arrays.stream(sysTenant.getCpIds()
                            .split(SymbolConstant.COMMA))
                            .map(Long::parseLong).collect(Collectors.toList());
                    cpIdSet.addAll(cpIdList);
                }
            }
        }
        IPage<BmsChannel> channelsPage = bmsChannelMapper.getChannelsPage(page, bmsChannel, categoryId, status, channelNames,cpIdSet);
        return channelsPage;
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public Boolean resetPublishStatus(List<Long> idList) {
        RuleCondition.create()
                .and(PublishStatusRule.init(BmsChannel.class).policy(PublishCheck.MUST_ING).data(idList))
                .execute().check();
        //获取到所有的需要重置发布状态的频道
        List<BmsChannel> bmsChannels = bmsChannelMapper.selectBatchIds(idList);
        bmsChannels.forEach(
                channels ->
                {
                    channels.setPublishStatus(CommonUtils.resetPublishStatus(channels.getPublishStatus()));
                    channels.setPublishDescription("");
                }
        );
        return this.updateBatchById(bmsChannels);
    }


    @Transactional(rollbackFor = Exception.class)
    @Override
    public Boolean updatePublishStatus(List<Long> idList, Integer publishStatus) {
        List<BmsChannel> bmsChannels = bmsChannelMapper.selectBatchIds(idList);
        bmsChannels.forEach(channels -> {
                    channels.setPublishStatus(publishStatus);
                    channels.setPublishDescription("");
                }
        );
        this.updateBatchById(bmsChannels);
        return true;
    }


    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean setStatus(List<Long> ids, StatusEnum status) {
        RuleCondition.create()
                //发布状态为 发布中的频道不可修改
                .and(PublishStatusRule.init(BmsChannel.class).data(ids).policy(ING))
                .execute()
                .check();
        int i = setStatus(BmsChannel.class, ids, status);
        if (i == 0) {
            throw new CommonResponseException("重置状态异常");
        }
        return true;
    }

    @Autowired
    private BmsBaseMapper bmsBaseMapper;

    public int setStatus(Class<?> table, List<Long> ids, StatusEnum status) {
        TableInfo tableInfo = TableInfoHelper.getTableInfo(table);
        return bmsBaseMapper.setStatus(tableInfo.getTableName(), ids, status);
    }

    @Override
    public IPage<BmsChannel> getNotBindChannelsPage(Page<BmsChannel> page, Long categoryId, Long spId, String channelName, Long cpId) {
        return bmsChannelMapper.getNotBindChannelsPage(page, categoryId, spId, channelName, cpId);
    }

    @Override
    public Page getAuthorizationChannelList(Page page, SysAuthorizationParam param) {
        // 租户权限过滤
        SecurityUser securityUser = JwtTokenUtil.getSecurityUser();
        List<SysTenant> sysTenants = new ArrayList<>();
        if (SysUserTypeEnum.TENANT.getCode().equals(securityUser.getType())){
            sysTenants = sysTenantService.listByUserId(securityUser.getId());
        }
        Set<Long> spIdSet = new HashSet<>();
        if (org.apache.commons.lang3.ObjectUtils.isNotEmpty(sysTenants)){
            List<SysTenant> cpLimitList = sysTenants.stream()
                    .filter(sysTenant -> {
                        List<Long> cpIdList = Arrays.stream(sysTenant.getCpIds()
                                .split(SymbolConstant.COMMA))
                                .map(Long::parseLong).collect(Collectors.toList());
                        return cpIdList.contains(param.getCpId());
                    }).collect(Collectors.toList());
            if (org.apache.commons.lang3.ObjectUtils.isNotEmpty(cpLimitList)){
                for (SysTenant sysTenant : cpLimitList) {
                    List<Long> spIdList = Arrays.stream(sysTenant.getSpIds()
                            .split(SymbolConstant.COMMA))
                            .map(Long::parseLong).collect(Collectors.toList());
                    spIdSet.addAll(spIdList);
                }
            }
        }
        return bmsChannelMapper.getAuthorizationChannelList(page, param,spIdSet);
    }

    @Override
    public List<Long> getChannelByCmsContentId(List<Long> cmsIds, List<Long> spIds) {
        LambdaQueryWrapper<BmsChannel> eq = Wrappers.lambdaQuery(BmsChannel.class)
                .in(BmsChannel::getCmsChannelId, cmsIds).in(BmsChannel::getSpId, spIds);
        List<BmsChannel> bmsChannels = bmsChannelMapper.selectList(eq);
        if (bmsChannels.size() != 0) {
            return bmsChannels.stream().map(BmsChannel::getId).collect(Collectors.toList());
        }
        return new ArrayList<>();
    }

    @Override
    public List<BmsChannel> getChannelById(Long cmsIds, Long spIds) {
        LambdaQueryWrapper<BmsChannel> eq = Wrappers.lambdaQuery(BmsChannel.class)
                .eq(BmsChannel::getCmsChannelId, cmsIds).eq(BmsChannel::getSpId, spIds);
        List<BmsChannel> bmsChannels = bmsChannelMapper.selectList(eq);
        if (CollectionUtils.isEmpty(bmsChannels) || bmsChannels.size() == 0) {
            return null;
        }
        return bmsChannels;
    }

    @Override
    public boolean deleteByCodeAndSp(List<String> codeList, List<Long> spIdList, boolean isRollback) {

        if (isRollback) {
            // 删除全部授权物理频道
            bmsPhysicalChannelService.update(Wrappers.lambdaUpdate(BmsPhysicalChannel.class)
                    .set(BmsPhysicalChannel::getPublishStatus, PublishStatusEnum.ROLLBACK.getCode())
                    .in(BmsPhysicalChannel::getCmsChannelCode, codeList)
                    .in(BmsPhysicalChannel::getSpId, spIdList)
            );
            bmsScheduleService.update(Wrappers.lambdaUpdate(BmsSchedule.class)
                    .set(BmsSchedule::getPublishStatus, PublishStatusEnum.ROLLBACK.getCode())
                    .in(BmsSchedule::getCmsChannelCode, codeList)
                    .in(BmsSchedule::getSpId, spIdList)
            );
            bmsChannelMapper.update(null, Wrappers.lambdaUpdate(BmsChannel.class)
                    .set(BmsChannel::getPublishStatus, PublishStatusEnum.ROLLBACK.getCode())
                    .in(BmsChannel::getCmsChannelCode, codeList)
                    .in(BmsChannel::getSpId, spIdList)
            );
            return true;
        }
        // 删除全部授权物理频道
        bmsPhysicalChannelService.remove(Wrappers.lambdaQuery(BmsPhysicalChannel.class)
                .in(BmsPhysicalChannel::getCmsChannelCode, codeList)
                .in(BmsPhysicalChannel::getSpId, spIdList)
        );
        bmsScheduleService.remove(Wrappers.lambdaQuery(BmsSchedule.class)
                .in(BmsSchedule::getCmsChannelCode, codeList)
                .in(BmsSchedule::getSpId, spIdList)
        );
        bmsChannelMapper.delete(Wrappers.lambdaQuery(BmsChannel.class)
                .in(BmsChannel::getCmsChannelCode, codeList)
                .in(BmsChannel::getSpId, spIdList)
        );
        return true;
    }

    @Override
    public boolean updatePublishStatus(List<SubOrderObjectsEntity> orderObjectsEntities, List<Long> spIdList) {
        Integer result = bmsChannelMapper.updatePublishStatus(orderObjectsEntities, spIdList);
        return null != result && result >= 1;
    }
}
