package com.pukka.iptv.manage.controller.sys;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.pukka.iptv.common.base.vo.CommonResponse;
import com.pukka.iptv.common.data.model.sys.SysNat;
import com.pukka.iptv.common.data.vo.IdList;
import com.pukka.iptv.manage.service.sys.SysNatService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.AllArgsConstructor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;



/**
 * 
 *
 * <AUTHOR>
 * @email 
 * @date 2021-10-18 11:34:46
 */



@RestController
@AllArgsConstructor
@RequestMapping(value = "/sysNat", produces = MediaType.APPLICATION_JSON_VALUE)
@Api(tags="sysNat管理")
public class SysNatController {


    @Autowired
    private SysNatService sysNatService;

     @ApiOperation(value = "分页")
     @GetMapping("/page" )
     public CommonResponse<Page> page(@Valid Page page, SysNat sysNat) {
          return  CommonResponse.success(sysNatService.page(page, Wrappers.query(sysNat)));
     }

     @ApiOperation(value = "详情")
     @GetMapping("/getById")
     public CommonResponse<SysNat> getById(@Valid @RequestParam(name = "id", required = true)  Long id) {
        return CommonResponse.success(sysNatService.getById(id));
     }

     @ApiOperation(value = "新增")
     @PostMapping("/save")
     public CommonResponse<Boolean> save(@Valid @RequestBody SysNat sysNat) {
         return  CommonResponse.success(sysNatService.save(sysNat));
     }

     @ApiOperation(value = "修改")
     @PutMapping("/update")
     public CommonResponse<Boolean> updateById(@Valid @RequestBody SysNat sysNat) {
         return CommonResponse.success(sysNatService.updateById(sysNat));
     }

     @ApiOperation(value = "删除")
     @DeleteMapping("/deleteById" )
     public CommonResponse<Boolean> deleteById(@Valid @RequestParam(name = "id", required = true) Long id) {
         return  CommonResponse.success(sysNatService.removeById(id));
     }

     @ApiOperation(value = "批量删除")
     @DeleteMapping("/deleteByIds" )
     public CommonResponse<Boolean> deleteByIds(@Valid @RequestBody IdList idList) {
         return  CommonResponse.success(sysNatService.removeByIds(idList.getIds()));
     }

}
