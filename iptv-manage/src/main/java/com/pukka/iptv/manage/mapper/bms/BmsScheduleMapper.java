package com.pukka.iptv.manage.mapper.bms;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.pukka.iptv.common.data.model.bms.BmsChannel;
import com.pukka.iptv.common.data.model.bms.BmsSchedule;
import com.pukka.iptv.common.data.model.order.SubOrderObjectsEntity;
import com.pukka.iptv.common.data.vo.bms.BmsSchedulePageVO;
import com.pukka.iptv.common.data.vo.req.BmsSchedulePageReq;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 *
 * @author: zhengcl
 * @date: 2021-9-7 15:26:41
 */

@Mapper
public interface BmsScheduleMapper extends BaseMapper<BmsSchedule>{

    int updatePublishStatus(@Param("entityList") List<SubOrderObjectsEntity> orderObjectsEntities, @Param("spIdList") List<Long> spIdList);

    /**
     * 节目单分页查询
     * @param schedulePageReq
     * @return
     */
    IPage<BmsSchedulePageVO> getSchedulesPage(@Param("schedulePageReq") BmsSchedulePageReq schedulePageReq);

    /**
     * (cmsChannelId,cmsChannelCode, spId)
     * 循环in删除
     *
     * @param bmsChannels
     * @return
     */
    int deleteByCmsChannelIdAndCmsChannelCodeAndSpId(@Param("bmsChannels") List<BmsChannel> bmsChannels);
}
