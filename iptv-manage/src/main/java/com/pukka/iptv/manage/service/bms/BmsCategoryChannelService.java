package com.pukka.iptv.manage.service.bms;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.pukka.iptv.common.base.enums.ActionEnums;
import com.pukka.iptv.common.data.model.bms.BmsCategory;
import com.pukka.iptv.common.data.model.bms.BmsCategoryChannel;
import com.pukka.iptv.common.data.model.bms.BmsChannel;
import com.pukka.iptv.common.data.model.order.SubOrderMappingsEntity;
import com.pukka.iptv.common.data.util.Tuple2;
import com.pukka.iptv.common.data.vo.bms.BmsCategoryChannelPageVO;
import com.pukka.iptv.manage.service.condition.rule.enums.PublishCheck;

import java.text.ParseException;
import java.util.List;

/**
 * 栏目-节目关系表
 *
 * <AUTHOR>
 * @date 2021-09-06 17:05:51
 */
public interface BmsCategoryChannelService extends IService<BmsCategoryChannel>, BmsPublishParamApi {

    boolean updatePublishStatus(List<SubOrderMappingsEntity> orderMappingsEntities, List<Long> spIdList);


    /**
     * 频道与栏目关系分页查询
     * @param page
     * @param bmsCategoryChannel
     * @param name
     * @return
     */
    IPage<BmsCategoryChannelPageVO> getCategoryContentChannelpage(Page<BmsCategoryChannel> page, BmsCategoryChannel bmsCategoryChannel, String name, Long channelCpId);

    /**
     * 频道绑定栏目
     * @return
     */
    Tuple2<Boolean,String> categoryBindChannel(List<Tuple2<Long, BmsCategory>> bindMap);

    /**
     * 查询关系表，判断频道是否已绑定栏目
     * @param categorys
     * @param ids
     * @return
     */
    Tuple2<Boolean, List<Tuple2<Long, BmsCategory>>> isRepeatBind(List<BmsCategory> categorys, List<Long> ids);



    List<BmsCategoryChannel> getByCategoryChanelId(Long id);


    /**
     * 频道与栏目_发布
     * @param ids
     * @param doSchedule
     * @param scheduleTime
     * @return
     * @throws ParseException
     */
    boolean categorychannelPublish(List<Long> ids, Integer doSchedule, String scheduleTime) throws ParseException;

    /**
     * 频道与栏目关系回收
     * @param ids
     * @return
     */
    boolean categorychannelRollback(List<Long> ids);

    /**
     * 重置发布状态
     * @param idList
     * @return
     */
    Boolean resetPublishStatus(List<Long> idList);

    /**
     * 修改发布状态
     * @param idList
     * @return
     */
    Boolean updatePublishStatus(List<Long> idList,Integer updatePublishStatus);

    /**
     * 根据频道ids跟栏目ids查询是否是发布状态
     * @return
     */
    Boolean checkPulishStatus(List<Long> channelIds,List<Long> categoryIds);

    /**
     * 重排序
     * @param list
     * @return
     */
    Boolean reorder(List<BmsCategoryChannel> list);

    /**
     * 取消定时发布
     * @param id
     * @return
     */
    Tuple2<Boolean,String> deleteTiming(String id);

    /**
     * 根据栏目id检测是否锁定
     * @param categoryIds
     * @return
     */
    Boolean checkLockStatus(List<Long> categoryIds);

    /**
     *
     * @param categoryIds 栏目id
     * @param channelIds 频道ids
     * @return
     */
    Tuple2<Boolean,String> delete(List<Long> categoryIds, List<Long> channelIds);
    /**
     * 频道与栏目关系发布/回收
     *
     * @param spId
     * @param categoryChannelsIds
     * @param actionEnums
     * @return
     */
    void categoryChannelsPublishAndRollback(Long spId,String spName, List<Long> categoryChannelsIds, ActionEnums actionEnums);

    /**
     * 检查关系是否可发布/回收
     * @param Ids
     * @param publishCheck
     */
    void checkRelation(List<Long> Ids,List<Long> categoryIds, PublishCheck publishCheck);

    List<Long> getCategoryChannels(List<BmsChannel> channels);

    boolean deleteByCodeAndSp(List<SubOrderMappingsEntity> deleteCategoryChannelEntities, List<Long> spIdList);
}


