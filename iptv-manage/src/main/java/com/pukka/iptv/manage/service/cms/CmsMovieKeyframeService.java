package com.pukka.iptv.manage.service.cms;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.pukka.iptv.common.data.model.*;
import com.pukka.iptv.common.data.model.cms.CmsMovie;
import com.pukka.iptv.common.data.model.cms.CmsMovieKeyframe;
import org.springframework.web.multipart.MultipartFile;

/**
 * 视频关键帧
 *
 * @author: zhoul
 * @date: 2021-8-30 10:31:39
 */

public interface CmsMovieKeyframeService extends IService<CmsMovieKeyframe> {

    IPage<CmsMovieKeyframe> listById(Page page, CmsMovie cmsMovie);

    /**
     * 上传图片到sftp并返回图片路径
     *
     * @param file 文件流
     * @param cpId
     * @return string
     */
    String uploadImg(MultipartFile file,Long cpId);
}


