package com.pukka.iptv.manage.service.api.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.pukka.iptv.common.api.feign.cms.CmsMovieFeignClient;
import com.pukka.iptv.common.base.exception.BizException;
import com.pukka.iptv.common.base.vo.CommonResponse;
import com.pukka.iptv.common.core.constant.SymbolConstant;
import com.pukka.iptv.common.data.model.cms.CmsMovie;
import com.pukka.iptv.common.data.model.cms.CmsPicture;
import com.pukka.iptv.manage.mapper.cms.CmsPictureMapper;
import com.pukka.iptv.manage.service.api.MovieDownloadService;
import com.pukka.iptv.manage.service.common.FileUtil;
import com.pukka.iptv.manage.util.downloadUtils.FtpClientService;
import com.pukka.iptv.manage.util.downloadUtils.dto.RetrieveFileEntity;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.io.InputStream;
import java.io.OutputStream;
import java.net.URLEncoder;
import java.nio.file.Files;
import java.nio.file.Path;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

/**
 * @author: chiron
 * Date: 2022/5/12 14:17
 * Description: 视频介质下载操作实现
 */
@Slf4j
@Service
public class MovieDownloadServiceImpl implements MovieDownloadService {
    @Autowired
    private CmsMovieFeignClient cmsMovieFeignClient;
    @Autowired
    private FtpClientService ftpClientService;
    @Autowired
    private CmsPictureMapper cmsPictureMapper;

    /**
     * 校验视频信息
     *
     * @param cmsContentId
     * @param contentType
     * @param cpId
     * @return cmsmovie表id
     */
    @Override
    public String checkMovieInfo(Long cmsContentId, Integer contentType, Long cpId) {
        String ids = "";
        try {
            List<Long> longList = new ArrayList<>();
            CommonResponse<List<CmsMovie>> cmsMovieListResponse = cmsMovieFeignClient.getByContentIdContentType(cmsContentId, contentType, cpId);
            if (ObjectUtils.isEmpty(cmsMovieListResponse.getData())) {
                log.warn("视频介质下载操作实现 ->>>>>> 当前发布类型:{} 内容ID:{} 发布，获取视频信息为空！", contentType, cmsContentId);
                return null;
            }
            List<CmsMovie> movieList = JSONArray.parseArray(JSON.toJSONString(cmsMovieListResponse.getData())).toJavaList(CmsMovie.class);
            if (ObjectUtils.isEmpty(movieList)) {
                log.warn("视频介质下载操作实现 ->>>>>> 当前发布类型:{} 内容ID:{} 发布，视频介质对象为空！", contentType, cmsContentId);
                return null;
            }
            for (CmsMovie cmsMovie : movieList) {
                if (ObjectUtils.isEmpty(cmsMovie) || ObjectUtils.isEmpty(cmsMovie.getPlayUrl())
                        || cmsMovie.getStorageId().equals("-1") || ObjectUtils.isEmpty(cmsMovie.getFileSize())) {
                    log.info("下载视频介质 -----> 当前影片未存储至播控服务器,下载结束.影片信息:{}", JSON.toJSONString(cmsMovie));
                    continue;
                }
                longList.add(cmsMovie.getId());
            }
            ids = StringUtils.join(longList.toArray(), ",");
        } catch (Exception exception) {
            log.warn("视频介质下载操作实现 ->>>>>> 当前发布类型:{} 内容ID:{} 发布，获取视频信息失败,错误信息:{}！", contentType, cmsContentId, exception);
        }
        return ids;
    }

    /**
     * 获取视频信息
     *
     * @param cmsContentId
     * @param contentType
     * @return
     */
    @Override
    public List<CmsMovie> obtainMovieInfo(Long cmsContentId, Integer contentType, Long cpId) {
        try {
            CommonResponse<List<CmsMovie>> cmsMovieListResponse = cmsMovieFeignClient.getByContentIdContentType(cmsContentId, contentType, cpId);
            if (ObjectUtils.isEmpty(cmsMovieListResponse.getData())) {
                log.warn("视频介质下载操作实现 ->>>>>> 当前发布类型:{} 内容ID:{} 发布，获取视频信息为空！", contentType, cmsContentId);
                return null;
            }
            List<CmsMovie> movieList = JSONArray.parseArray(JSON.toJSONString(cmsMovieListResponse.getData())).toJavaList(CmsMovie.class);
            if (ObjectUtils.isEmpty(movieList)) {
                log.warn("视频介质下载操作实现 ->>>>>> 当前发布类型:{} 内容ID:{} 发布，视频介质对象为空！", contentType, cmsContentId);
                return null;
            }
            return movieList;
        } catch (Exception exception) {
            log.warn("视频介质下载操作实现 ->>>>>> 当前发布类型:{} 内容ID:{} 发布，获取视频信息失败,错误信息:{}！", contentType, cmsContentId, exception);
            return null;
        }
    }

    /**
     * 下载视频介质
     *
     * @param cmsMovieList
     * @return
     */
    @Override
    public Boolean retrieveMovieFile(List<CmsMovie> cmsMovieList, String showName, HttpServletResponse response) {
        List<String> nameArray = cmsMovieList.stream().map(CmsMovie::getName).collect(Collectors.toList());
        log.info("下载视频介质 -----> 共:{} 部影片，影片信息:{}", cmsMovieList.size(), JSON.toJSONString(nameArray));
        try {
            // 设置文件ContentType类型，这样设置，会自动判断下载文件类型
            response.setContentType("application/x-msdownload");
            for (CmsMovie cmsMovie : cmsMovieList) {
                try {
                    if (ObjectUtils.isEmpty(cmsMovie) || ObjectUtils.isEmpty(cmsMovie.getPlayUrl())
                            || cmsMovie.getStorageId().equals("-1") || ObjectUtils.isEmpty(cmsMovie.getFileSize())) {
                        log.info("下载视频介质 -----> 当前影片未存储至播控服务器,下载结束.影片信息:{}", JSON.toJSONString(cmsMovie));
                        continue;
                    }
                    RetrieveFileEntity retrieveFile = new RetrieveFileEntity().setRemotePath(cmsMovie.getPlayUrl())
                            .setFileName(cmsMovie.getName()).setFileUrl(cmsMovie.getFileUrl()).setResponse(response);
                    Boolean aBoolean = ftpClientService.readFtpStream(retrieveFile);
                    if (!aBoolean) {
                        log.warn("下载视频介质 -----> 影片信息:{},检索视频介质文件并下载失败!", JSON.toJSONString(cmsMovie));
                    }
                } catch (Exception exception) {
                    log.error("下载视频介质 -----> 影片信息:{},错误信息:{}", JSON.toJSONString(cmsMovie), exception);
                }
            }
        } catch (Exception exception) {
            log.error("下载视频介质 -----> 影片:{},错误信息:{}", showName, exception);
        }
        return true;
    }

    /**
     * 图片下载
     *
     * @param ids
     * @param response
     * @return
     */
    @Override
    public Boolean retrieveImageFile(String ids, HttpServletResponse response) {
        List<Long> cmsPictureList = Arrays.stream(ids.split(SymbolConstant.COMMA)).map(Long::valueOf).collect(Collectors.toList());
        List<CmsPicture> cmsPictures = cmsPictureMapper.selectBatchIds(cmsPictureList);
        if (CollectionUtils.isEmpty(cmsPictures)) {
            log.warn("图片下载操作实现 ->>>>>>  内容Ids:{} 发布，获取图片信息为空！", ids);
            throw new BizException("图片下载失败,获取图片信息为空!");
        }
        try {
            response.setContentType("image/jpeg");

            cmsPictures.forEach(cmsPicture -> {
                if (com.pukka.iptv.common.core.util.StringUtils.isEmpty(cmsPicture.getFileUrl())) {
                    log.warn("图片下载操作实现 ->>>>>>  内容Ids:{} 下载，获取图片信息为空，图片信息：{}", ids, cmsPicture);
                    return;
                }
                String fileName = cmsPicture.getFileUrl().substring(cmsPicture.getFileUrl().lastIndexOf(SymbolConstant.LEFT_DIVIDE) + 1);
                RetrieveFileEntity retrieveFile = new RetrieveFileEntity().setRemotePath(SymbolConstant.SPACE)
                        .setFileName(fileName).setFileUrl(cmsPicture.getFileUrl()).setResponse(response);
                Boolean result = ftpClientService.readFtpStream(retrieveFile);
                if (!result) {
                    log.warn("图片下载操作实现 ->>>>>>  内容Ids:{} 下载图片信息异常，图片信息：{}", ids, cmsPicture);

                }
            });
        } catch (Exception exception) {
            log.error("图片下载操作实现 ->>>>>>  内容Ids:{} ，获取图片信息异常,错误信息", ids, exception);
            throw new BizException("图片下载,进行 FTP 下载失败!");
        }

        return true;
    }

    /**
     * 图片下载并压缩
     *
     * @param ids
     * @param response
     * @return
     */
    @Override
    public Boolean retrieveImageFilesAndCompress(String ids, HttpServletResponse response) throws IOException {
        List<Long> cmsPictureList = Arrays.stream(ids.split(SymbolConstant.COMMA))
                .map(Long::valueOf)
                .collect(Collectors.toList());
        List<CmsPicture> cmsPictures = cmsPictureMapper.selectBatchIds(cmsPictureList);
        if (CollectionUtils.isEmpty(cmsPictures)) {
            log.warn("图片下载操作实现 ->>>>>>  内容Ids:{} 发布，获取图片信息为空！", ids);
            throw new BizException("图片下载失败,获取图片信息为空!");
        }
        // 创建临时目录存放图片文件
        Path tempDir = Files.createTempDirectory("images");
        try {
            for (CmsPicture cmsPicture : cmsPictures) {
                if (StringUtils.isEmpty(cmsPicture.getFileUrl())) {
                    log.warn("图片下载操作实现 ->>>>>>  内容Ids:{} 下载，获取图片信息为空，图片信息：{}", ids, cmsPicture);
                    continue;
                }

                String fileName = cmsPicture.getFileUrl().substring(cmsPicture.getFileUrl().lastIndexOf(SymbolConstant.LEFT_DIVIDE) + 1);
                Path tempFilePath = tempDir.resolve(fileName);

                RetrieveFileEntity retrieveFile = new RetrieveFileEntity()
                        .setRemotePath(SymbolConstant.SPACE)
                        .setFileName(fileName)
                        .setFileUrl(cmsPicture.getFileUrl())
                        .setResponse(response);

                Boolean result = ftpClientService.downloadFtpToLocal(retrieveFile, tempFilePath);
                if (!result) {
                    log.warn("图片下载操作实现 ->>>>>>  内容Ids:{} 下载图片信息异常，图片信息：{}", ids, cmsPicture);
                }
            }
            // 打包成ZIP文件
            Path zipFilePath = tempDir.resolve("images.zip");
            FileUtil.compressToZip(tempDir.toFile(), zipFilePath.toFile());
            // 设置响应头
            response.setContentType("application/octet-stream");
            response.setHeader("Content-Disposition", "attachment;filename*=UTF-8''" + URLEncoder.encode("images.zip", "UTF-8"));

            try (InputStream inputStream = Files.newInputStream(zipFilePath.toFile().toPath());
                 OutputStream outputStream = response.getOutputStream()) {
                byte[] buffer = new byte[1024];
                int bytesRead;
                while ((bytesRead = inputStream.read(buffer)) != -1) {
                    outputStream.write(buffer, 0, bytesRead);
                }
            }
        } catch (Exception exception) {
            log.error("图片下载操作实现 ->>>>>>  内容Ids:{} ，获取图片信息异常,错误信息", ids, exception);
            throw new BizException("图片下载,进行 FTP 下载失败!");
        } finally {
            // 删除临时文件
            FileUtil.deleteTempFiles(tempDir);
        }
        return true;
    }

}