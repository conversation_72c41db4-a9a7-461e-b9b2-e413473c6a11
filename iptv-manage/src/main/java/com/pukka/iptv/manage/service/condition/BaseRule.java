package com.pukka.iptv.manage.service.condition;


import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.toolkit.support.SFunction;

import java.util.Collection;
import java.util.List;
import java.util.function.Function;

/**
 * @author: wz
 * @date: 2021/9/7 18:51
 * @description:
 */
public interface BaseRule<T, A> {

    BaseRule<T, A> col(String col);
    BaseRule<T, A> type(String type);

    BaseRule<T, A> col(String col, String alias);

    BaseRule<T, A> col(SFunction<T, ?> func);

    BaseRule<T, A> col(SFunction<T, ?> func, String alias);

    BaseRule<T, A> cols(List<String> cols);

    @SuppressWarnings("unchecked")
    BaseRule<T, A> cols(SFunction<T, ?>... func);

    BaseRule<T, A> condCol(String condCol);

    BaseRule<T, A> condCol(SFunction<T, ?> func);

    BaseRule<T, A> policy(A action);

    BaseRule<T, A> mapper(BaseMapper<RuleInfo> mapper);

    BaseRule<T, A> data(Collection<Long> ids);

    BaseRule<T, A> data(Long id);

    BaseRule<T, A> data(QueryWrapper<?> wrapper);

    BaseRule<T, A> data(Function<?, T> wrapper);

    BaseRule<T, A> wrapper(QueryWrapper<?> wrapper);

    QueryWrapper<?> wrapper();

    RuleResult execute();

}
