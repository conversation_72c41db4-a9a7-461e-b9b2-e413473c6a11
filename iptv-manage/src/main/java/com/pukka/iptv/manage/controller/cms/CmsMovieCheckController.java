package com.pukka.iptv.manage.controller.cms;

import com.pukka.iptv.common.base.vo.CommonResponse;
import com.pukka.iptv.common.data.group.FrameGroup;
import com.pukka.iptv.common.data.vo.*;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.pukka.iptv.common.data.model.cms.CmsMovie;
import com.pukka.iptv.common.data.model.cms.CmsMovieCheck;

import javax.validation.Valid;

import com.pukka.iptv.manage.service.cms.CmsMovieCheckService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.http.MediaType;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.beans.factory.annotation.Autowired;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.AllArgsConstructor;

/**
 * 审核标记
 *
 * @author: zhoul
 * @date: 2021-8-30 10:16:44
 */

@RestController
@AllArgsConstructor
@RequestMapping(value = "/cmsMovieCheck", produces = MediaType.APPLICATION_JSON_VALUE)
@Api(tags="cmsMovieCheck管理")
public class CmsMovieCheckController {

    @Autowired
    private CmsMovieCheckService cmsMovieCheckService;

    @ApiOperation(value = "分页")
    @GetMapping("/page" )
    public CommonResponse<Page> page(@Valid Page page, CmsMovieCheck cmsMovieCheck) {
        return  CommonResponse.success(cmsMovieCheckService.page(page, Wrappers.query(cmsMovieCheck)));
    }
    @ApiOperation(value = "视频审核标记分页查询")
    @GetMapping("/listById" )
    public CommonResponse<IPage<CmsMovieCheck>> page(@Valid Page page,@Validated(FrameGroup.class) CmsMovie cmsMovie) {
        return  CommonResponse.success(cmsMovieCheckService.listById(page, cmsMovie));
    }

    @ApiOperation(value = "详情",hidden = true)
    @GetMapping("/getById")
    public CommonResponse<CmsMovieCheck> getById(@Valid @RequestParam(name = "id", required = true)  Long id) {
        return CommonResponse.success(cmsMovieCheckService.getById(id));
    }

    @ApiOperation(value = "播放器切片上报接口")
    @PostMapping("/save")
    public CommonResponse<Boolean> save(@Valid @RequestBody CmsMovieCheck cmsMovieCheck) {
        return  CommonResponse.success(cmsMovieCheckService.save(cmsMovieCheck));
    }

    @ApiOperation(value = "修改",hidden = true)
    @PutMapping("/update")
    public CommonResponse<Boolean> updateById(@Valid @RequestBody CmsMovieCheck cmsMovieCheck) {
        return CommonResponse.success(cmsMovieCheckService.updateById(cmsMovieCheck));
    }

    @ApiOperation(value = "删除",hidden = true)
    @DeleteMapping("/deleteById" )
    public CommonResponse<Boolean> deleteById(@Valid @RequestParam(name = "id", required = true) Long id) {
        return  CommonResponse.success(cmsMovieCheckService.removeById(id));
    }

    @ApiOperation(value = "批量删除",hidden = true)
    @DeleteMapping("/deleteByIds" )
    public CommonResponse<Boolean> deleteByIds(@Valid @RequestBody IdList idList) {
        return  CommonResponse.success(cmsMovieCheckService.removeByIds(idList.getIds()));
    }

}
