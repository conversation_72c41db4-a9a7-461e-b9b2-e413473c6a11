package com.pukka.iptv.manage.service.sys;

import com.baomidou.mybatisplus.extension.service.IService;
import com.pukka.iptv.common.data.model.sys.SysUserRole;

import java.util.List;

/**
 *
 * @author: zhengcl
 * @date: 2021-8-17 16:48:35
 */

public interface SysUserRoleService extends IService<SysUserRole> {

    /**
     * 查询用户角色
     * @param userId
     * @return
     */
    List<SysUserRole> listByUserId(Long userId);

    /**
     * 判断用户角色是否是管理员
     * @param userId
     * @return
     */
    boolean isAdmin(Long userId);
}


