package com.pukka.iptv.manage.util;

import com.alibaba.nacos.common.utils.StringUtils;
import com.pukka.iptv.common.base.enums.ActionEnums;
import com.pukka.iptv.common.base.enums.PublishStatusEnum;
import com.pukka.iptv.common.data.vo.bms.CheckNameApi;

import static com.pukka.iptv.common.base.enums.PublishStatusEnum.*;

/**
 * @Author: chiron
 * @Date: 2022/09/16/02:00
 * @Description:
 */

public class CommonUtils {

    public static String[] getNames(String name) {
        if (StringUtils.isEmpty(name)) {
            return null;
        }
        String[] names = name.replaceAll("(\r\n|\r|\n|\n\r)", "\r\n").split("\r\n");
        if (names.length > 0) {
            for (int i = 0; i < names.length; i++) {
                names[i] = names[i].trim();
            }
        } else {
            names = null;
        }
        return names;
    }

    public static String[] getNamesByAuth(String name) {
        if (StringUtils.isEmpty(name)) {
            return null;
        }
        String[] names = name.replaceAll("(\r\n|\r|\n|\n\r)", ",").split(",");
        if (names.length > 0) {
            for (int i = 0; i < names.length; i++) {
                names[i] = names[i].trim();
            }
        } else {
            names = null;
        }
        return names;
    }

    public static Integer getEnumByAction(ActionEnums actionEnums, Boolean status) {
        switch (actionEnums) {
            case DELETE:
                return status ? ROLLBACKING.getCode() : FAILPUBLISH.getCode();
            case REGIST:
                return status ? PUBLISHING.getCode() : FAILPUBLISH.getCode();
            default:
                return status ? UPDATING.getCode() : FAILUPDATE.getCode();
        }
    }

    public static Integer resetPublishStatus(Integer oldStatus) {
        PublishStatusEnum v = PublishStatusEnum.getByValue(oldStatus);
        switch (v) {
            //发布中->待发布
            case PUBLISHING:
                return PublishStatusEnum.WAITPUBLISH.getCode();
            //更新中->待更新
            case UPDATING:
                return PublishStatusEnum.WAITUPDATE.getCode();
            //回收中->发布成功 (更新成功)
            case ROLLBACKING:
                return PublishStatusEnum.PUBLISH.getCode();
            default:
                return v.getCode();
        }
    }

    public static void checkNames(CheckNameApi checkNameApi) {
        String name = checkNameApi.getName();
        String categoryName = checkNameApi.getCategoryName();
        String packageName = checkNameApi.getPackageName();
        String[] names = checkNameApi.getNames();
        if (StringUtils.isNotEmpty(categoryName)) {
            checkNameApi.setCategoryName(categoryName.trim());
            if (categoryName.contains("\n")) {
                if (StringUtils.isNotEmpty(checkNameApi.getCategoryName())) {
                    names = getNames(categoryName);
                }
                checkNameApi.setCategoryNames(names);
                checkNameApi.setCategoryName(null);
            }
        } else if (StringUtils.isNotEmpty(packageName)) {
            checkNameApi.setPackageName(packageName.trim());
            if (packageName.contains("\n")) {
                if (StringUtils.isNotEmpty(checkNameApi.getPackageName())) {
                    names = getNames(packageName);
                }
                checkNameApi.setPackageNames(names);
                checkNameApi.setPackageName(null);
            }
        } else if (StringUtils.isNotEmpty(name)) {
            checkNameApi.setName(name.trim());
            if (name.contains("\n")) {
                if (StringUtils.isNotEmpty(checkNameApi.getName())) {
                    names = getNames(name);
                }
                checkNameApi.setNames(names);
                checkNameApi.setName(null);
            }
        }
    }

}
