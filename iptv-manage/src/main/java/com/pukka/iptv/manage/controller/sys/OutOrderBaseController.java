package com.pukka.iptv.manage.controller.sys;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.pukka.iptv.common.base.enums.FtsSearchModeEnum;
import com.pukka.iptv.common.base.vo.CommonResponse;
import com.pukka.iptv.common.core.constant.SymbolConstant;
import com.pukka.iptv.common.data.model.OutOrderBase;
import com.pukka.iptv.common.data.model.OutOrderItem;
import com.pukka.iptv.common.data.vo.sys.OutOrderBaseRepublish;
import com.pukka.iptv.common.data.vo.sys.OutOrderBaseVo;
import com.pukka.iptv.common.data.vo.sys.OutOrderItemVo;
import com.pukka.iptv.manage.config.FtsSearchModeConfig;
import com.pukka.iptv.manage.service.sys.OutOrderBaseService;
import com.pukka.iptv.manage.util.searchModeUtils.factory.SearchModeFactory;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @Date 2021/9/9 10:18 上午
 * @description: 主工单查询，重新发布接口
 * @Version 1.0
 */
@Slf4j
@RestController
@AllArgsConstructor
@RequestMapping(value = "/outOrderBase", produces = MediaType.APPLICATION_JSON_VALUE)
@Api(tags = "OutOrderBase管理")
public class OutOrderBaseController {
    @Autowired
    private OutOrderBaseService outOrderBaseService;
    @Autowired
    private FtsSearchModeConfig ftsSearchModeConfig;

    @ApiOperation(value = "主工单重新发布")
    @PutMapping("/orderRepublish")
    public CommonResponse<Boolean> orderRepublish(@Valid @RequestBody OutOrderBaseRepublish outOrderBaseRepublish) {
        boolean republish = outOrderBaseService.orderRepublish(outOrderBaseRepublish);
        if (republish) {
            return CommonResponse.success(true);
        }
        return CommonResponse.fail(false);
    }

    @ApiOperation(value = "获取主工单信息")
    @GetMapping("/getOutOrderBaseInfo")
    public CommonResponse<List<OutOrderBase>> getOutOrderBaseInfo(@Valid @RequestParam(name = "contentId", required = true) String contentId, @Valid @RequestParam(name = "contentType", required = true) String contentType) {
        return CommonResponse.success(outOrderBaseService.getOrderBaseInfo(contentId, contentType));
    }

    @ApiOperation(value = "分页")
    @GetMapping("/listByOutOrderBaseProperty")
    public CommonResponse<IPage<OutOrderBase>> page(Page<OutOrderBase> page, OutOrderBaseVo outOrderBaseVo) {
        try {
            FtsSearchModeEnum ftsSearchModeEnum = FtsSearchModeEnum.getByMode(Integer.parseInt(ftsSearchModeConfig.getSearchmode()));
            IPage<OutOrderBase> result = SearchModeFactory.initMode(ftsSearchModeEnum, page, outOrderBaseVo).apply();
            return CommonResponse.success(result);
        } catch (Exception e) {
            log.error("Error occurred while searching for OutOrderBase: {}", e.getMessage());
            return CommonResponse.fail("主工单分页查询错误");
        }
    }

    @ApiOperation(value = "明细查看")
    @GetMapping("/getOrderItemInfo")
    public CommonResponse<IPage<OutOrderItemVo>> getOrderItemInfo(Page<OutOrderItem> page, OutOrderItem outOrderItem) {
        return CommonResponse.success(outOrderBaseService.getOrderItemInfo(page, outOrderItem));
    }

    @ApiOperation(value = "根据id获取主工单信息")
    @GetMapping("/getById")
    public CommonResponse<OutOrderBase> getById(@Valid @RequestParam(name = "id", required = true) Long id) {
        return CommonResponse.success(outOrderBaseService.getById(id));
    }

    @ApiOperation(value = "修改")
    @PutMapping("/update")
    public CommonResponse<Boolean> updateById(@Valid @RequestBody OutOrderBase outOrderBase) {
        return CommonResponse.success(outOrderBaseService.updateById(outOrderBase));
    }

    @ApiOperation(value = "根据关联ID查询主任务的接口")
    @GetMapping("/getOrderByCorrelateId")
    public CommonResponse<List<OutOrderBase>> getOrderByCorrelateId(@Valid @RequestParam(name = "correlateid", required = true) String correlateId) {
        return CommonResponse.success(outOrderBaseService.getOrderByCorrelateId(correlateId));
    }

    @ApiOperation(value = "查询物理频道id信息")
    @PostMapping("/getPhysicalChannelIds")
    public CommonResponse<List<Long>> getPhysicalChannelIds(@Valid @RequestParam(name = "baseOrderId", required = true) String baseOrderId) {
        return CommonResponse.success(outOrderBaseService.getPhysicalChannelIds(baseOrderId));
    }

    @ApiOperation(value = "查询节目单id信息")
    @PostMapping("/getScheduleIds")
    public CommonResponse<List<Long>> getScheduleIds(@Valid @RequestParam(name = "baseOrderId", required = true) String baseOrderId) {
        return CommonResponse.success(outOrderBaseService.getScheduleIds(baseOrderId));
    }

    @ApiOperation(value = "查询子集id信息")
    @PostMapping("/getSubsetIds")
    public CommonResponse<List<Long>> getSubsetIds(@Valid @RequestParam(name = "baseOrderId", required = true) String baseOrderId) {
        return CommonResponse.success(outOrderBaseService.getSubsetIds(baseOrderId));
    }

    /**
     * 查询主工单信息
     * @param ids
     * @return
     */
    @ApiOperation(value = "获取主工单信息")
    @GetMapping("/getByIds")
    CommonResponse<List<OutOrderBase>> getByIds(@Valid @RequestParam(name = "ids", required = true) String ids,@Valid @RequestParam(name = "contentType", required = true) String contentType, @Valid @RequestParam(name = "action", required = true) Integer action){
        try{
            List<Long> idList = Arrays.stream(ids.split(SymbolConstant.COMMA))
                    .map(Long::valueOf)
                    .collect(Collectors.toList());
            return CommonResponse.success(outOrderBaseService.getOrderBaseList(idList, contentType, action));
        } catch (Exception e) {
            log.error("根据媒资ids获取主工单信息,搜索时发生错误: {}", e.getMessage());
            return CommonResponse.fail("根据媒资ids获取主工单信息错误");
        }
    }
}
