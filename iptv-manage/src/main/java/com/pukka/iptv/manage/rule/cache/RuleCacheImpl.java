package com.pukka.iptv.manage.rule.cache;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.ObjectUtils;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.pukka.iptv.common.base.constant.RedisKeyConstants;
import com.pukka.iptv.common.data.model.copyright.RuleProhibit;
import com.pukka.iptv.common.redis.service.RedisService;
import com.pukka.iptv.manage.mapper.copyright.prohibit.RuleProhibitMapper;
import com.pukka.iptv.manage.service.cache.CacheServiceImpl;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * @Author: chiron
 * @Date: 2022/07/29/15:11
 * @Description: 违禁规则缓存类
 */
@Slf4j
@Component
public class RuleCacheImpl implements RuleCache {

    @Autowired
    private RedisService redisService;
    @Autowired
    private CacheServiceImpl cacheService;
    @Autowired
    private RuleProhibitMapper ruleProhibitMapper;

    @Override
    public List<RuleProhibit> getAllProhibitRules() {
        List<RuleProhibit> prohibits = new ArrayList<>();
        Map<String, RuleProhibit> redisServiceCacheMap = redisService.getCacheMap(RedisKeyConstants.RULE_CACHE);
        if (ObjectUtils.isEmpty(redisServiceCacheMap) || redisServiceCacheMap.size() < 1) {
            try {
                LambdaQueryWrapper<RuleProhibit> ruleProhibitLambdaQueryWrapper = Wrappers.lambdaQuery(RuleProhibit.class);
                prohibits = ruleProhibitMapper.selectList(ruleProhibitLambdaQueryWrapper);
                if (ObjectUtils.isNotEmpty(prohibits) && prohibits.size() > 0) {
                    //写redis
                    boolean result = cacheService.cacheRuleCache(prohibits);
                    if (result) {
                        log.info("RuleCacheImpl -----> redis插入RuleCache数据完成!");
                    } else {
                        log.warn("RuleCacheImpl -----> redis插入RuleCache数据失败");
                    }
                } else {
                    log.warn("RuleCacheImpl -----> 查询违禁规则为空!");
                }
            } catch (Exception exception) {
                log.error("RuleCacheImpl -----> 获取违禁规则失败，错误信息:{}", exception);
            }
            return prohibits;
        }else{
            List<RuleProhibit> ruleProhibits = new ArrayList<>(redisServiceCacheMap.values());
            return ruleProhibits;
        }
    }
}
