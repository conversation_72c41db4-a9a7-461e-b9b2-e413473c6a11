package com.pukka.iptv.manage.util;

import com.google.common.net.UrlEscapers;
import com.pukka.iptv.common.core.util.StringUtils;

import java.net.MalformedURLException;
import java.net.URL;
import java.util.HashMap;
import java.util.Map;

public class URLUtil {
	
	public static Map<String, String> parseFtp(String url) throws Exception{
		Map<String, String> temp = new HashMap<String, String>();
		String ipPort= "21";

		String temp2  =UrlEscapers.urlFragmentEscaper().escape(url);
		URL urltemp = new URL(temp2);

		String ipAddress = urltemp.getHost();
		if (urltemp.getPort()!=-1) {
			ipPort = urltemp.getPort()+"";
		};
		String path = urltemp.getPath();
		String userInfo = urltemp.getUserInfo();
		String userName = "";
		String userPwd = "";
		if(StringUtils.isNotEmpty(userInfo)){
			String[] array = userInfo.split(":");
			if(array.length>0){
				userName = array[0];
			}
			
			if(array.length>=1){
				userPwd = array[1];
			}
		}
		temp.put("userName", userName);
		temp.put("passWord", percentDecode(userPwd));
		temp.put("ipAddress", ipAddress);
		temp.put("ipPort", ipPort);
		temp.put("path", path);
		
		return temp;
	}

	public static String percentEncode(String encodeMe) {
		if (encodeMe == null) {
			return "";
		}
		String encoded = encodeMe.replace("%", "%25");
		encoded = encoded.replace(" ", "%20");
		encoded = encoded.replace("!", "%21");
		encoded = encoded.replace("#", "%23");
		encoded = encoded.replace("$", "%24");
		encoded = encoded.replace("&", "%26");
		encoded = encoded.replace("'", "%27");
		encoded = encoded.replace("(", "%28");
		encoded = encoded.replace(")", "%29");
		encoded = encoded.replace("*", "%2A");
		encoded = encoded.replace("+", "%2B");
		encoded = encoded.replace(",", "%2C");
		encoded = encoded.replace("/", "%2F");
		encoded = encoded.replace(":", "%3A");
		encoded = encoded.replace(";", "%3B");
		encoded = encoded.replace("=", "%3D");
		encoded = encoded.replace("?", "%3F");
		encoded = encoded.replace("@", "%40");
		encoded = encoded.replace("[", "%5B");
		encoded = encoded.replace("]", "%5D");
		return encoded;
	}

	public static String percentDecode(String encodeMe) {
		if (encodeMe == null) {
			return "";
		}
		String decoded = encodeMe.replace("%21", "!");
		decoded = decoded.replace("%20", " ");
		decoded = decoded.replace("%23", "#");
		decoded = decoded.replace("%24", "$");
		decoded = decoded.replace("%26", "&");
		decoded = decoded.replace("%27", "'");
		decoded = decoded.replace("%28", "(");
		decoded = decoded.replace("%29", ")");
		decoded = decoded.replace("%2A", "*");
		decoded = decoded.replace("%2B", "+");
		decoded = decoded.replace("%2C", ",");
		decoded = decoded.replace("%2F", "/");
		decoded = decoded.replace("%3A", ":");
		decoded = decoded.replace("%3B", ";");
		decoded = decoded.replace("%3D", "=");
		decoded = decoded.replace("%3F", "?");
		decoded = decoded.replace("%40", "@");
		decoded = decoded.replace("%5B", "[");
		decoded = decoded.replace("%5D", "]");
		decoded = decoded.replace("%25", "%");
		return decoded;
	}

	/**
	 * @param args
	 */
	public static void main(String[] args) {
		// TODO Auto-generated method stub
		String ipPort= "21";
		URL urltemp;
		try {
			urltemp = new URL("*****************************/xml/d/asd.jsp");
			String ipAddress = urltemp.getHost();
			if (urltemp.getPort()!=-1) {
				ipPort = urltemp.getPort()+"";
			};
			String path = urltemp.getPath();
			String userInfo = urltemp.getUserInfo();
			String userName = "";
			String userPwd = "";
			if(StringUtils.isNotEmpty(userInfo)){
				String[] array = userInfo.split(":");
				if(array.length>0){
					userName = array[0];
				}
				
				if(array.length>=1){
					userPwd = array[1];
				}
			}
			System.out.println("userName= " + userName);
			System.out.println("passWord= " + userPwd);
			System.out.println("ipAddress= " + ipAddress);
			System.out.println("ipPort= " + ipPort);
			System.out.println("path= " + path);
		} catch (MalformedURLException e) {
			// TODO Auto-generated catch block
			e.printStackTrace();
		}

	}

}
