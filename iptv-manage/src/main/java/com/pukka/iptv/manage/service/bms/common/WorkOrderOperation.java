package com.pukka.iptv.manage.service.bms.common;

import cn.hutool.core.collection.CollectionUtil;
import com.alibaba.fastjson.JSON;
import com.pukka.iptv.common.api.feign.sys.OutPublishFeignClient;
import com.pukka.iptv.common.base.constant.PublishParamTypeConstants;
import com.pukka.iptv.common.base.enums.ActionEnums;
import com.pukka.iptv.common.base.enums.ContentTypeEnum;
import com.pukka.iptv.common.base.enums.ContentTypeItemEnum;
import com.pukka.iptv.common.base.enums.PublishStatusEnum;
import com.pukka.iptv.common.base.util.JwtTokenUtil;
import com.pukka.iptv.common.base.vo.CommonResponse;
import com.pukka.iptv.common.base.vo.SecurityUser;
import com.pukka.iptv.common.core.constant.SymbolConstant;
import com.pukka.iptv.common.data.model.OutParamExpand;
import com.pukka.iptv.common.data.model.OutPublish;
import com.pukka.iptv.manage.service.bms.common.model.PriorityAction;
import com.pukka.iptv.manage.service.bms.common.service.CommonService;
import com.pukka.iptv.manage.service.bms.common.service.PriorityApi;
import com.pukka.iptv.manage.service.sys.OutScheduledTasksService;

import java.util.*;
import java.util.stream.Collectors;

import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.lang.reflect.Field;

/**
 * <AUTHOR>
 * @Date 2021-10-29 12:04
 * @Description 工单操作
 */
@Slf4j
@Component
public class WorkOrderOperation implements PriorityApi {

    @Autowired
    private OutPublishFeignClient opFeignClient;
    @Autowired
    private CommonService commonService;
    @Autowired
    private OutScheduledTasksService outScheduledTasksService;

    /**
     * <AUTHOR>
     * @Description 参数： 工单类型、内容类型、内容主键id、spId、spName、操作完成的回调
     **/
    public boolean send(ActionEnums actionEnum, ContentTypeEnum typeEnum, List<Long> ids,
                        Map<String, String> picMap, Long spId, String spName, WorkOrderConsumer consumer,
                        Map<String, OutParamExpand> paramMap) {
        // 构建发布参数
        OutPublish outPublish = buildOutPublish(actionEnum, typeEnum, ids, picMap, spId, spName,
                paramMap);
        // 发布工单
        CommonResponse<Boolean> publish = sendWorkOrder(outPublish);
        Boolean success = publish.getData();
        log.info(success ? "接口调用成功-动作 " + actionEnum.getInfo()
                : "接口调用失败-动作 " + actionEnum.getInfo());
        log.info("调用发布接口，下发参数：{}", JSON.toJSON(outPublish));
        // 确认即将要修改的发布状态
        Integer publishStatus = assertModifyPublishStatus(success, actionEnum).getCode();
        // 回调
        consumer.publishCallback(success, publishStatus, publish.getMessage());
        log.info("发布接口反馈信息：{}", publish.getMessage());
        return success;
    }

    public CommonResponse<Boolean> sendWorkOrder(OutPublish outPublish) {
        return opFeignClient.publish(outPublish);
    }

    // 构建OutPublish发布对象
    public OutPublish buildOutPublish(ActionEnums actionEnums, ContentTypeEnum typeEnum,
                                      List<Long> contentIds,
                                      Map<String, String> picMap, Long spId, String spName,
                                      Map<String, OutParamExpand> paramMap) {

        OutPublish outPublish = new OutPublish();
        paramMap.put(PublishParamTypeConstants.ORDER_PICTUREACTION,
                new OutParamExpand().setSpareMap(picMap));
        setPriorityByNacos(paramMap, typeEnum, actionEnums);
        outPublish.setOutParam(paramMap);
        StringJoiner ids = new StringJoiner(",");
        contentIds.forEach((id) -> ids.add(id.toString()));
        // 类型
        outPublish.setContentType(typeEnum.getValue());
        outPublish.setSpId(spId + "");
        outPublish.setSpName(spName);
        // bms表的主键id 逗号分隔批量
        outPublish.setContentIds(ids.toString());
        // 执行动作
        outPublish.setAction(actionEnums.getCode());
        // 设置操作者id和name
        buildOperator(outPublish);
        return outPublish;
    }

    /**
     * 根据nacos中获取到的优先级进行优先级调整
     */
    private void setPriorityByNacos(Map<String, OutParamExpand> paramMap, ContentTypeEnum typeEnum,
                                    ActionEnums actionEnums) {
        //当为优先发布时则不读取nacos的优先级
        if (getPriority()) {
            return;
        }
        PriorityAction priorityByNacos = commonService.getPriorityByNacos(
                ContentTypeItemEnum.getTableNameByContentType(typeEnum.getValue()), actionEnums);
        if (priorityByNacos.isEnable()) {
            paramMap.put(PublishParamTypeConstants.PRIORITY,
                    new OutParamExpand().setPriority(priorityByNacos.getPriority()));
        }
    }

    /**
     * 构建操作者信息
     * @param outPublish
     */
    public void buildOperator(OutPublish outPublish) {
        try {
            Optional<SecurityUser> userOptional = Optional.ofNullable(JwtTokenUtil.getSecurityUser());
            if (userOptional.isPresent()) {
                setCreator(outPublish, userOptional.get());
            } else {
                handleNonAuthenticatedUser(outPublish);
            }
        } catch (Exception e) {
            log.error("构建 outPublish 操作员时出错: {}", outPublish, e);
        }
    }

    /**
     * 设置操作者信息
     * @param outPublish
     * @param user
     */
    private void setCreator(OutPublish outPublish, SecurityUser user) {
        outPublish.setCreatorId(String.valueOf(user.getId()));
        outPublish.setCreatorName(user.getName());
    }

    /**
     * 处理未认证用户
     * @param outPublish
     */
    private void handleNonAuthenticatedUser(OutPublish outPublish) {
        String contentIds = outPublish.getContentIds();
        Integer contentType = outPublish.getContentType();

        if (ContentTypeEnum.SCHEDULE.getValue().equals(contentType)) {
            contentIds = getScheduleContentIds(outPublish);
            contentType = ContentTypeEnum.SCHEDULE.getValue();
        }

        Optional<SecurityUser> securityUserOptional = getSecurityUserForScheduledTask(contentIds, contentType);
        securityUserOptional.ifPresent(user -> setCreator(outPublish, user));
    }

    /**
     * 获取节目单类型的定时发布内容ID
     * @param outPublish
     * @return
     */
    private String getScheduleContentIds(OutPublish outPublish) {
        OutParamExpand outParamExpand = outPublish.getOutParam().get(PublishParamTypeConstants.ORDER_CHANNELSCHEDULE);
        Map<String, String> retrievedSendMap = outParamExpand.getSpareMap();
        List<Long> scheduleIds = retrievedSendMap.keySet().stream()
                .map(Long::parseLong)
                .collect(Collectors.toList());

        if (!scheduleIds.isEmpty()) {
            return scheduleIds.stream()
                    .map(String::valueOf)
                    .collect(Collectors.joining(SymbolConstant.COMMA));
        }
        return outPublish.getContentIds();
    }

    /**
     * 获取定时任务的用户信息
     * @param contentIds
     * @param contentType
     * @return
     */
    private Optional<SecurityUser> getSecurityUserForScheduledTask(String contentIds, Integer contentType) {
        try {
            return Optional.ofNullable(outScheduledTasksService.getSecurityUser(contentIds, contentType));
        } catch (Exception e) {
            log.error("获取内容 ID 为 {}，内容类型为 {} 的安全用户时出错", contentIds, contentType, e);
            return Optional.empty();
        }
    }

    // 确认即将要修改的发布状态
    public PublishStatusEnum assertModifyPublishStatus(boolean success, ActionEnums actionEnums) {
        return success ? transPublishSuccessStatus(actionEnums)
                : transPublishFailedStatus(actionEnums);
    }

    // 通过枚举值获取即将要修改的发布状态 发布操作->发布中 | 更新操作->更新中 | 回收操作->回收中
    public PublishStatusEnum transPublishSuccessStatus(ActionEnums actionEnums) {
        switch (actionEnums) {
            case REGIST:
                return PublishStatusEnum.PUBLISHING;
            case UPDATE:
                return PublishStatusEnum.UPDATING;
            default:
                return PublishStatusEnum.ROLLBACKING;
        }
    }

    // 通过枚举值获取即将要修改的发布状态 可以发布的->发布失败 | 可以更新的->更新失败 | 可以回收的->回收失败
    public PublishStatusEnum transPublishFailedStatus(ActionEnums actionEnums) {
        switch (actionEnums) {
            case REGIST:
                return PublishStatusEnum.FAILPUBLISH;
            case UPDATE:
                return PublishStatusEnum.FAILUPDATE;
            default:
                return PublishStatusEnum.FAILROLLBACK;
        }
    }

    // 获取集合泛型的指定字段值
    public static String getStringAbel(List<?> list, Class<?> c, String field) {
        StringJoiner stringJoiner = new StringJoiner(",");
        Field[] fields = c.getDeclaredFields();
        int pos;
        for (pos = 0; pos < fields.length; pos++) {
            if (field.equals(fields[pos].getName())) {
                break;
            }
        }
        for (Object o : list) {
            try {
                fields[pos].setAccessible(true);
                stringJoiner.add(fields[pos].get(o).toString());
                //
            } catch (Exception e) {
                e.printStackTrace();
            }
        }
        return stringJoiner.toString();
    }

    @FunctionalInterface
    public interface WorkOrderConsumer {

        // 是否调用成功、即将要修改的发布状态、即将要修改的发布描述
        void publishCallback(boolean success, Integer publishStatus, String description);
    }

}
