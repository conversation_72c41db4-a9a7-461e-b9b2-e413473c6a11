package com.pukka.iptv.manage.service.api;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.pukka.iptv.common.data.model.virtual.VirtualProgram;
import com.pukka.iptv.common.data.model.virtual.VirtualSeries;
import com.pukka.iptv.common.data.vo.req.VirtualChannelSearchReq;
import com.pukka.iptv.common.data.vo.virtual.VirtualSeriesVo;

public interface VirtualSeriesService extends IService<VirtualSeries> {

    /**
     * 获取剧集信息
     * @param req
     * @return
     */
    IPage<VirtualSeriesVo> getVirtualSeriesByPage(Page<VirtualProgram> page, VirtualChannelSearchReq req);

    /**
     * 新增或更新
     * @param virtualSeries
     * @return
     */
    Boolean insertOrUpdate(VirtualSeries virtualSeries);

    /**
     * 更新剧集及子集
     * @param virtualSeries
     * @return
     */
    Boolean updateSeriesAndSubset(VirtualSeries virtualSeries);
}
