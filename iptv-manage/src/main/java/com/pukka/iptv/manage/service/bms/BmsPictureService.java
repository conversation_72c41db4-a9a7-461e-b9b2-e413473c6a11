package com.pukka.iptv.manage.service.bms;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.pukka.iptv.common.base.enums.ActionEnums;
import com.pukka.iptv.common.base.enums.ContentTypeEnum;
import com.pukka.iptv.common.base.vo.CommonResponse;
import com.pukka.iptv.common.data.model.bms.BmsChannel;
import com.pukka.iptv.common.data.model.bms.BmsPicture;
import com.pukka.iptv.common.data.model.order.SubOrderMappingsEntity;
import com.pukka.iptv.common.data.util.Tuple2;
import com.pukka.iptv.common.data.vo.bms.BmsPictureVO;
import com.pukka.iptv.common.data.vo.bms.ResultUrlVo;
import com.pukka.iptv.manage.service.bms.dto.BmsPicRecycleDto;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * <AUTHOR>
 * @create: 2021-09-01 14:37
 */
public interface BmsPictureService extends IService<BmsPicture>, BmsRecycleApi<BmsPicRecycleDto, BmsPicture>, BmsPublishParamApi {
    List<BmsPicture> getByContentIdContentType(Long contentId, Integer contentType, Long spId);

    List<BmsPicture> getPicByContentIds(List<Long> contentIds, Integer contentType);


    List<Long> getPicIdByContentIds(List<Long> contentIds, Integer contentType);

    List<Long> getPicIdByWrapper(LambdaQueryWrapper<BmsPicture> wrappers);

    boolean updatePublishStatus(List<SubOrderMappingsEntity> orderMappingsEntities, List<Long> spIdList);

    /**
     * 图片发布
     *
     * @param ids
     * @return
     */
    CommonResponse<Boolean> picturePublish(List<Long> ids);


    /**
     * 图片回收
     *
     * @param ids
     * @return
     */
    CommonResponse<Boolean> pictureRollback(List<Long> ids);

    /**
     * 修改发布状态
     *
     * @return
     */
    boolean modifyPublishStatus(List<Long> ids, Integer publishStatus);

    /**
     * 重置发布状态
     * @param ids
     * @return
     */
    boolean resetPublishStatus(List<Long> ids);

    /**
     * 上传图片
     *
     * @return
     */
    ResultUrlVo upload(MultipartFile file, Long cpId, Long spId) throws IOException;

    /**
     * 查询可回收的图片
     * @param bmsContentIds
     * @param contentTypeEnum
     * @return
     */
    List<Long> pictureCanBeRecycled(List<Long> bmsContentIds, ContentTypeEnum contentTypeEnum);

    // 标记删除
    boolean pictureMarkRecycled(List<Long> bmsPictureId, ContentTypeEnum contentTypeEnum);

    // 删除图片信息 通过内容id和内容类型
    boolean removePicture(List<Long> bmsContentIds, ContentTypeEnum contentTypeEnum, boolean deleteFTP);

    // 删除图片信息 通过主键id
    boolean removeCategoryPicture(List<Long> ids, boolean deleteFTP);

    /**
     * 图片发布/回收/更新
     *
     * @param picturesIds
     * @param spId
     * @param actionEnums
     * @return
     */
    Boolean picturePublishAndRollback(List<Long> picturesIds, Long spId,String spName, ActionEnums actionEnums);

    /**
     * 根据逻辑频道以及不同的动作获取到需要同步进行该动作的图片
     *
     * @param channels
     * @return
     */
    /**
     * 根据不同的动作获取到相对应应该进行该动作的物理频道
     * @param channels
     * @param actionStatus true  发布and更新   false  回收
     * @return
     */
    Tuple2<List<Long>, Map<String, String>> getAllpictures(List<BmsChannel> channels, Boolean actionStatus);


    Page<BmsPicture> picturePage(Page page, BmsPicture bmsPicture);

    IPage<BmsPictureVO> findPictureByCategoryId(Page<BmsPicture> page, Long categoryId);

    // 通过contentId查询可以组装发布工单的图片id
    Map<String, String> findCanPublishPicture(List<Long> contentIds, ContentTypeEnum typeEnum);

    //组建 发布|更新 所需的Map
    Map<String, String> buildPublishAndUpdateWorkOrderMap(List<BmsPicture> bmsPictures);

    // 组建 回收 所需的Map
    Map<String, String> buildRecycleWorkOrderMap(List<Long> ids);

    // 处理图片下发后的修改状态以及描述
    boolean pictureIssuedProcess(boolean success, List<Long> publishPicIds, List<Long> updatePicIds, List<Long> recyclePicIds, String description);


    /**
     * 重排序
     *
     * @param bmsPicture
     * @return
     */
    Boolean reorder(BmsPicture bmsPicture);

    /**
     * 图片发布/回收/更新 成功回调
     * @param picturesTuple
     * @param status
     * @return
     */
    Boolean pictureCallback(Tuple2<List<Long>, Map<String, String>> picturesTuple, Boolean status);

    boolean deleteByCodeAndSp(List<SubOrderMappingsEntity> deletePictureMappingEntities, List<Long> spIdList);

    boolean delPicByCodeAndSp(List<String> codeList, List<Long> spIdList, boolean isRollback);

    List<BmsPicture> listByCmsPictureId(Long id);

    void deleteByCmsPictureIds(Set<Long> seriesPictureIds);

    /**
     * 图片校验
     * @param file
     * @param imageType
     * @throws IOException
     */
    Boolean validatePicture(MultipartFile file, Integer imageType) throws IOException;
}
