package com.pukka.iptv.manage.mapper.sys;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.pukka.iptv.common.data.model.sys.SysOutpassageStorageRela;
import com.pukka.iptv.common.data.vo.sys.SysOutpassageStorageRelaVO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
public interface SysOutpassageStorageRelaMapper extends BaseMapper<SysOutpassageStorageRela> {

    Page<SysOutpassageStorageRelaVO> page(@Param("page") Page page, @Param("sysOutpassageStorageRelaVO") SysOutpassageStorageRelaVO sysOutpassageStorageRelaVO);

    SysOutpassageStorageRelaVO getByOutPassageIdAndStorageId(@Param("outPassageId") Long outPassageId, @Param("storageId") Long storageId);

    List<SysOutpassageStorageRelaVO> listAll();

    SysOutpassageStorageRelaVO getVoById(@Param("id") Long id);
}
