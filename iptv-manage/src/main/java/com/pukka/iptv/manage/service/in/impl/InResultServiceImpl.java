package com.pukka.iptv.manage.service.in.impl;


import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.pukka.iptv.common.data.model.in.InResult;
import com.pukka.iptv.manage.mapper.in.InResultMapper;
import com.pukka.iptv.manage.service.in.InResultService;
import org.springframework.stereotype.Service;


/**
 *
 * @author: tan
 * @date: 2021-8-24 10:50:00
 */

@Service
public class InResultServiceImpl extends ServiceImpl<InResultMapper, InResult> implements InResultService {

}


