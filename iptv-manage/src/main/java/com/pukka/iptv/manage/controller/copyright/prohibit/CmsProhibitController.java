package com.pukka.iptv.manage.controller.copyright.prohibit;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.pukka.iptv.common.base.vo.CommonResponse;
import com.pukka.iptv.common.data.dto.CmsProhibitDO;
import com.pukka.iptv.common.data.model.copyright.CmsProhibit;
import com.pukka.iptv.common.data.vo.copyright.CmsProhibitListVo;
import com.pukka.iptv.common.data.vo.copyright.CmsProhibitVo;
import com.pukka.iptv.common.log.annotation.SysLog;
import com.pukka.iptv.common.log.enums.OperateObjectEnum;
import com.pukka.iptv.common.log.enums.OperateTypeEnum;
import com.pukka.iptv.manage.service.*;

import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;

import com.pukka.iptv.manage.service.copyright.prohibit.CmsProhibitService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.http.MediaType;
import org.springframework.util.Assert;
import org.springframework.web.bind.annotation.*;
import org.springframework.beans.factory.annotation.Autowired;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.AllArgsConstructor;
import com.pukka.iptv.common.data.vo.*;
import com.pukka.iptv.common.data.model.*;

import java.util.List;

/**
 *
 * @author: zhengcl
 * @date: 2022-7-26 9:41:15
 */

@RestController
@AllArgsConstructor
@RequestMapping(value = "/cmsProhibit", produces = MediaType.APPLICATION_JSON_VALUE)
@Api(tags="违禁片管理")
public class CmsProhibitController {

    @Autowired
    private CmsProhibitService cmsProhibitService;

    @ApiOperation(value = "分页")
    @PostMapping("/page" )
    public CommonResponse<IPage<CmsProhibitVo>> page(@RequestBody CmsProhibitDO cmsProhibit) {
        Page page = new Page();
        page.setCurrent(cmsProhibit.getCurrent());
        page.setSize(cmsProhibit.getSize());
        return  CommonResponse.success(cmsProhibitService.selectPage(page, cmsProhibit));
    }

    @ApiOperation(value = "详情")
    @GetMapping("/getById")
    public CommonResponse<CmsProhibit> getById(@Valid @RequestParam(name = "id", required = true)  Long id) {
        return CommonResponse.success(cmsProhibitService.getById(id));
    }

    @SysLog(objectType = OperateObjectEnum.CMS_PROHIBIT, operateType = OperateTypeEnum.SAVE)
    @ApiOperation(value = "新增")
    @PostMapping("/save")
    public CommonResponse<Boolean> save(@Valid @RequestBody CmsProhibitListVo cmsProhibitListVo) {
        int insert = cmsProhibitService.add(cmsProhibitListVo);
        return  insert > 0 ? CommonResponse.success(true) : CommonResponse.fail(false);
    }

    //@SysLog(objectType = OperateObjectEnum.CMS_PROHIBIT, operateType = OperateTypeEnum.UPDATE)
    @ApiOperation(value = "修改")
    @PutMapping("/update")
    public CommonResponse<Boolean> updateById(@Valid @RequestBody CmsProhibit cmsProhibit) {
        return CommonResponse.success(cmsProhibitService.updateById(cmsProhibit));
    }

    @SysLog(objectType = OperateObjectEnum.CMS_PROHIBIT, operateType = OperateTypeEnum.DELETE)
    @ApiOperation(value = "删除")
    @DeleteMapping("/deleteById" )
    public CommonResponse<Boolean> deleteById(@Valid @RequestParam(name = "id", required = true) Long id) {
        return  CommonResponse.success(cmsProhibitService.removeById(id));
    }

    @SysLog(objectType = OperateObjectEnum.CMS_PROHIBIT, operateType = OperateTypeEnum.DELETE)
    @ApiOperation(value = "批量删除")
    @PostMapping("/deleteByIds" )
    public CommonResponse<Boolean> deleteByIds(@Valid @RequestBody IdList idList) {
        int res = cmsProhibitService.deleteByIds(idList.getIds());
        return  res > 0 ? CommonResponse.success(true) : CommonResponse.fail(false);
    }

    @PostMapping("/export")
    @ApiOperation(value = "违禁片导出")
    public CommonResponse export(@Valid @RequestBody IdList body, HttpServletResponse response) {
        Assert.notEmpty(body.getIds(), "查询参数不能为空");
        return CommonResponse.success(cmsProhibitService.export(body,response));
    }

}
