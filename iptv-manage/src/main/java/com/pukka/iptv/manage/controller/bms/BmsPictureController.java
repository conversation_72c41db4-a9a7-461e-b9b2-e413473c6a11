package com.pukka.iptv.manage.controller.bms;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.pukka.iptv.common.base.constant.PublishParamTypeConstants;
import com.pukka.iptv.common.base.exception.BizException;
import com.pukka.iptv.common.base.vo.CommonResponse;
import com.pukka.iptv.common.data.model.OutParamExpand;
import com.pukka.iptv.common.data.model.bms.BmsPicture;
import com.pukka.iptv.common.data.vo.bms.BmsPictureVO;
import com.pukka.iptv.common.data.vo.bms.PublishStatusVo;
import com.pukka.iptv.common.data.vo.bms.ResultUrlVo;
import com.pukka.iptv.manage.service.bms.BmsPictureService;
import com.pukka.iptv.manage.service.bms.BmsPublishParamApi;
import com.pukka.iptv.manage.service.common.FtpAnalysisUtil;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import java.io.IOException;
import java.util.List;
import javax.validation.Valid;
import lombok.AllArgsConstructor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

/**
 * <AUTHOR>
 * @Date 2021/9/2 10:07 上午
 * @description: 图片
 * @Version 1.0
 */
@RestController
@AllArgsConstructor
@RequestMapping(value = "/bmsPicture", produces = MediaType.APPLICATION_JSON_VALUE)
@Api(tags = "bmsPicture管理")
public class BmsPictureController implements BmsPublishParamApi
{

    @Autowired
    private BmsPictureService bmsPictureService;

    @ApiOperation(value = "图片分页")
    @GetMapping("/query")
    public CommonResponse<Page> page(@Valid Page page, @Valid BmsPicture bmsPicture) {
        return CommonResponse.success(bmsPictureService.picturePage(page, bmsPicture));
    }

    @ApiOperation(value = "详情")
    @GetMapping("/getById")
    public CommonResponse<BmsPicture> getById(@Valid @RequestParam(name = "id", required = true) Long id) {
        return CommonResponse.success(bmsPictureService.getById(id));
    }

    @ApiOperation(value = "新增")
    @PostMapping("/save")
    public CommonResponse<Boolean> save(@Valid @RequestBody BmsPicture bmsPicture) {
        return CommonResponse.success(bmsPictureService.save(bmsPicture));
    }

    @ApiOperation(value = "修改")
    @PutMapping("/update")
    public CommonResponse<Boolean> updateById(@Valid @RequestBody BmsPicture bmsPicture) {
        return CommonResponse.success(bmsPictureService.updateById(bmsPicture));
    }

    @ApiOperation(value = "栏目图片删除")
    @DeleteMapping("/deleteCategoryPicByIds")
    public CommonResponse<Boolean> deleteById(@RequestBody List<Long> ids) {
        return CommonResponse.success(bmsPictureService.removeCategoryPicture(ids, true));
    }

    @ApiOperation(value = "删除FTP图片")
    @DeleteMapping("/deletePicByUrl")
    public CommonResponse<Boolean> deletePicByUrl(@RequestBody JSONObject body) {
        String ftpUrl = body.getString("ftpUrl");
        if(ftpUrl == null || ftpUrl.isEmpty()){
            return CommonResponse.success(true);
        }
        return CommonResponse.success(FtpAnalysisUtil.DelFtp(ftpUrl));
    }

    @ApiOperation(value = "图片详情")
    @GetMapping("/getByContentIdContentType")
    public CommonResponse<List<BmsPicture>> getByContentIdContentType(@Valid @RequestParam(name = "contentId", required = true) Long contentId, @Valid @RequestParam(name = "contentType", required = true) Integer contentType,@Valid @RequestParam(name = "spId", required = true) Long spId) {
        return CommonResponse.success(bmsPictureService.getByContentIdContentType(contentId, contentType,spId));
    }

    @ApiOperation(value = "图片回收")
    @PutMapping("/rollback")
    public CommonResponse<Boolean> pictureRollback(@RequestBody List<Long> ids) {
        setParam(PublishParamTypeConstants.PRIORITY,new OutParamExpand().setPriority(0));
        CommonResponse<Boolean> response = bmsPictureService.pictureRollback(ids);
        clearParm();
        return response;
    }

    @ApiOperation(value = "图片发布")
    @PutMapping("/publish")
    public CommonResponse<Boolean> picturePublish(@RequestBody List<Long> ids) {
        if (ids.size() == 0) {
            return CommonResponse.commonfail("参数校验异常：不可为空");
        }
        setParam(PublishParamTypeConstants.PRIORITY,new OutParamExpand().setPriority(0));
        CommonResponse<Boolean> response = bmsPictureService.picturePublish(ids);
        clearParm();
        return response;
    }

    @ApiOperation(value = "图片上传")
    @PutMapping("/upload")
    public CommonResponse<ResultUrlVo> pictureUpload(@RequestParam(value = "file") MultipartFile file,
                                                     @RequestParam(value = "cpId", required = false) Long cpId,
                                                     @RequestParam(value = "spId", required = false) Long spId) throws IOException {
        if (cpId == null && spId == null) throw new BizException("请传spId,或是cpId");
        ResultUrlVo upload = bmsPictureService.upload(file, cpId, spId);
        return CommonResponse.success(upload);
    }

    @ApiOperation(value = "图片重置发布状态")
    @PutMapping("/reset_publish_status")
    public CommonResponse<Boolean> publishStatusReset(@RequestBody List<Long> ids) {
        Boolean status = bmsPictureService.resetPublishStatus(ids);
        return status ? CommonResponse.success(true) : CommonResponse.commonfail("图片重置发布状态失败");
    }



    @ApiOperation(value = "图片修改发布状态")
    @PutMapping("/change_publish_status")
    public CommonResponse<Boolean> updatePublishStatus(@RequestBody @Valid PublishStatusVo publishStatusVo) {
        List<Long> idList = publishStatusVo.getIdList();
        Integer publishStatus = publishStatusVo.getPublishStatus();
        Boolean status = bmsPictureService.modifyPublishStatus(idList, publishStatus);
        return status ? CommonResponse.success(true) : CommonResponse.commonfail("图片重置发布状态失败");
    }

    @ApiOperation(value = "查询图片通过栏目id(栏目编辑展示)")
    @GetMapping("/getCategoryPic")
    public CommonResponse<IPage<BmsPictureVO>> findPictureByCategoryId(Page<BmsPicture> page, Long categoryId) {
        return CommonResponse.success(bmsPictureService.findPictureByCategoryId(page, categoryId));
    }

    @ApiOperation(value = "图片重排序")
    @PutMapping("/sort")
    public CommonResponse<Boolean> categoryChannelSort(@RequestBody BmsPicture bmsPicture)
    {
        Boolean reorder = bmsPictureService.reorder(bmsPicture);
        return reorder ? CommonResponse.success(true) : CommonResponse.commonfail("重排序失败");
    }

    @ApiOperation(value = "图片校验")
    @PutMapping("/validatePicture")
    public CommonResponse<Boolean> validatePicture(@RequestParam(value = "file") MultipartFile file,
                                                     @RequestParam(value = "imageType", required = false) Integer imageType) throws IOException {
        if (imageType == null) {
            throw new BizException("图片类型为空");
        }
        Boolean result = bmsPictureService.validatePicture(file, imageType);
        return CommonResponse.success(result);
    }
}
