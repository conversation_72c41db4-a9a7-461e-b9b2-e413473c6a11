package com.pukka.iptv.manage.service.copyright.prohibit;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.pukka.iptv.common.data.dto.CmsProhibitDO;
import com.pukka.iptv.common.data.model.copyright.CmsProhibit;
import com.pukka.iptv.common.data.vo.IdList;
import com.pukka.iptv.common.data.vo.copyright.CmsProhibitListVo;
import com.pukka.iptv.common.data.vo.copyright.CmsProhibitVo;

import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import java.util.List;

/**
 *
 * @author: zhengcl
 * @date: 2022-7-26 9:41:15
 */

public interface CmsProhibitService extends IService<CmsProhibit> {

    /**
     * 查询
     * @param page
     * @param cmsProhibit
     * @return
     */
    IPage<CmsProhibitVo> selectPage(Page page, CmsProhibitDO cmsProhibit);

    /**
     * 插入
     * @param cmsProhibitListVo 违禁片信息
     */
    int add(CmsProhibitListVo cmsProhibitListVo);

    /**
     * 批量删除，支持单个删除
     * @param ids
     * @return
     */
    int deleteByIds(List<Long> ids);

    /**
     * 批量导出
     * @param body 导出数据
     * @param response Http响应流
     */
    Object export(@Valid IdList body, HttpServletResponse response);

    /**
     * 判断表中是否存在该条记录
     * @param code
     * @param contentType
     * @return
     */
    Boolean isExist(String code, Integer contentType);
}


