package com.pukka.iptv.manage.mapper.cms;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.pukka.iptv.common.data.annotation.DataPermission;
import com.pukka.iptv.common.data.model.cms.CmsPlayerPicture;
import org.apache.ibatis.annotations.Mapper;
import com.pukka.iptv.common.data.model.*;

/**
 *
 *
 * @author: zhoul
 * @date: 2021-11-3 14:28:16
 */

@Mapper
@DataPermission(config = "cp_id=cpIds")
public interface CmsPlayerPictureMapper extends BaseMapper<CmsPlayerPicture>{

}
