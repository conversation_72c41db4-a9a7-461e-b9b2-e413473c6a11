package com.pukka.iptv.manage.controller.api;

import com.alibaba.fastjson.JSONObject;
import com.pukka.iptv.common.core.util.ResponseUtil;
import com.pukka.iptv.common.data.dto.PlayerReportDto;
import com.pukka.iptv.common.data.vo.resp.PlayerReportResponse;
import com.pukka.iptv.common.data.vo.resp.PlayerResponse;
import com.pukka.iptv.manage.service.cms.CmsPlayerPictureService;
import com.pukka.iptv.manage.service.cms.CmsPlayerSliceService;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;

/**
 * @Author: liaowj
 * @Description: 播放器关键帧上传，播放器结果上报接口
 * @CreateDate: 2021/11/6 20:01
 * @Version: 1.0
*/
@RequestMapping(value = "/api/player", produces = MediaType.APPLICATION_JSON_VALUE)
@RestController
public class PlayerController {

    @Autowired
    private CmsPlayerPictureService cmsPlayerPictureService;

    @Autowired
    private CmsPlayerSliceService cmsPlayerSliceService;


    /** 参数已经由播放器确定， 参数名不可改 */
    @ApiOperation(value = "关键帧图片上传")
    @PostMapping(value = "/imgUpload", produces = "text/html;charset=UTF-8")
    public void playerImgUpload(HttpServletResponse httpServletResponse, @RequestParam("imagefile") MultipartFile imagefile, Long cpid, String filename) {
        cmsPlayerPictureService.playerImgUpload(httpServletResponse, imagefile, cpid, filename);
    }

    @ApiOperation(value = "播放器上报接口")
    @PostMapping(value = "/report", produces = "text/html;charset=UTF-8")
    public void playerReport(HttpServletResponse httpServletResponse,@Valid @RequestBody PlayerReportDto params) {
        cmsPlayerSliceService.playerReport(httpServletResponse, params);
    }
}
