package com.pukka.iptv.manage.service.sys.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.pukka.iptv.common.data.model.sys.SysLog;
import com.pukka.iptv.manage.mapper.sys.SysLogMapper;
import com.pukka.iptv.manage.service.sys.SysLogService;
import org.springframework.amqp.rabbit.core.RabbitTemplate;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;


/**
 *
 * @author: chenyudong
 * @date: 2021-09-29 14:50:59
 */

@Service
public class SysLogServiceImpl extends ServiceImpl<SysLogMapper, SysLog> implements SysLogService {

    @Transactional(rollbackFor = Exception.class)
    @Override
    public boolean save(SysLog sysLog){
        //保存到数据库并放入MQ
        sysLog.insert();
        return true;
    }
}


