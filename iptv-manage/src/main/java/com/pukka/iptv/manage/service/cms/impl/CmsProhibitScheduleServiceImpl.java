package com.pukka.iptv.manage.service.cms.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.bean.copier.CopyOptions;
import cn.hutool.core.collection.CollectionUtil;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.pukka.iptv.common.base.constant.RedisKeyConstants;
import com.pukka.iptv.common.base.enums.SourceEnum;
import com.pukka.iptv.common.data.dto.CmsScheduleDto;
import com.pukka.iptv.common.data.dto.DateFormatCompletionDto;
import com.pukka.iptv.common.data.model.cms.CmsProhibitSchedule;
import com.pukka.iptv.common.data.model.cms.CmsProhibitScheduleDTO;
import com.pukka.iptv.common.data.model.cms.CmsSchedule;
import com.pukka.iptv.common.data.vo.IdList;
import com.pukka.iptv.manage.export.model.ExcelTaskInfo;
import com.pukka.iptv.manage.export.model.ExportInfo;
import com.pukka.iptv.manage.export.task.ExportTask;
import com.pukka.iptv.manage.mapper.cms.CmsProhibitScheduleMapper;
import com.pukka.iptv.manage.service.cms.CmsProhibitScheduleService;
import com.pukka.iptv.manage.service.cms.CmsScheduleService;
import com.pukka.iptv.manage.util.CalendarUtil;
import com.pukka.iptv.manage.util.CommonUtils;
import com.pukka.iptv.manage.util.TimeResolutionUtil;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.io.ByteArrayOutputStream;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;
import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;

/**
 * <AUTHOR>
 * @description 针对表【cms_prohibit_schedule(违禁节目单)】的数据库操作Service实现
 * @createDate 2023-11-17 14:32:11
 */
@Service
@Slf4j
@AllArgsConstructor
public class CmsProhibitScheduleServiceImpl extends ServiceImpl<CmsProhibitScheduleMapper, CmsProhibitSchedule>
        implements CmsProhibitScheduleService {
    @Override
    public IPage<CmsProhibitSchedule> selectList(Page page, CmsProhibitScheduleDTO scheduleDTO) {
        // 开始、结束时间格式化
        DateFormatCompletionDto dateFormatCompletionDto =
                new DateFormatCompletionDto()
                        .setStartTime(scheduleDTO.getStartDateTime())
                        .setEndTime(scheduleDTO.getEndDateTime());
        Boolean aBoolean = TimeResolutionUtil.dateTimeCompare(dateFormatCompletionDto);
        scheduleDTO.setStartDateTime(dateFormatCompletionDto.getStartTime());
        scheduleDTO.setEndDateTime(dateFormatCompletionDto.getEndTime());
        if (StringUtils.isNotEmpty(scheduleDTO.getName())) {
            if (scheduleDTO.getName().contains("\n")) {
                //防止name为空字符串以及只有回车的情况  进行再一次校验
                scheduleDTO.setName(scheduleDTO.getName().trim());
                String name = scheduleDTO.getName();
                if (StringUtils.isNotEmpty(name)) {
                    String[] names = CommonUtils.getNames(name);
                    if (names.length > 0) {
                        scheduleDTO.setProgramNameList(names);
                    }
                }
            } else {
                String[] names = CommonUtils.getNames(scheduleDTO.getName());
                if (names != null && names.length > 0) {
                    scheduleDTO.setProgramName(names[0]);
                }
            }
        }
        List<CmsProhibitSchedule> cmsProhibitSchedules = cmsProhibitScheduleMapper.selectList(page, scheduleDTO);
        page.setRecords(cmsProhibitSchedules);
        return page;
    }

    @Override
    public Object exportList(@Valid IdList idList, HttpServletResponse response) {
        List<CmsProhibitSchedule> list = cmsProhibitScheduleMapper.selectBatchIds(idList.getIds());
        //导出设置
        long ms = System.currentTimeMillis();
        ExportInfo<CmsProhibitSchedule> exportInfo = new ExportInfo<>();
        exportInfo.setOut(new ByteArrayOutputStream())
                .setSheetName("违禁节目单库")
                .setQueryWrapper(null)
                .setPojoClass(CmsProhibitSchedule.class)
                .setCacheKey(RedisKeyConstants.CMS_PROHIBIT_SCHEDULE_SELECT_EXPORT)
                .setBaseMapper(cmsProhibitScheduleMapper)
                .setResultList(list);
        ExcelTaskInfo excelTaskInfo = exportTask.startProcess(exportInfo).dowork();
        log.info("违禁节目单库总导出时长为 {} ms", (System.currentTimeMillis() - ms));
        return JSON.toJSON(excelTaskInfo);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void delete(@Valid IdList idList) {
        //1.先将对应的数据写入到节目单库表，在删除数据
        //List<CmsProhibitSchedule> cmsProhibitSchedules = cmsProhibitScheduleMapper.selectBatchIds(idList.getIds());
        //2.写入cms_schedules与bms_schedules表中
        //updateOrSaveCmsSchedules(cmsProhibitSchedules);
        //3.删除违禁节目单的数据
        cmsProhibitScheduleMapper.deleteBatchIds(idList.getIds());
    }

    @Override
    public void save(List<CmsProhibitSchedule> cmsProhibitSchedules) {
        if (CollectionUtil.isEmpty(cmsProhibitSchedules)) {
            return;
        }
        //先删除历史数据
        cmsProhibitScheduleMapper.deleteByChannelCodeAndStartTime(cmsProhibitSchedules.get(0).getChannelCode(),cmsProhibitSchedules);
        //计算结束时间
        cmsProhibitSchedules.forEach(cmsProhibitSchedule -> {
            if (cmsProhibitSchedule.getEndTime() == null || cmsProhibitSchedule.getEndTime().isEmpty()) {
                cmsProhibitSchedule.setEndTime(CalendarUtil.newDuration(cmsProhibitSchedule.getStartTime(), cmsProhibitSchedule.getDuration()));
            }
        });
        //再批量写入
        cmsProhibitScheduleMapper.batchInsert(cmsProhibitSchedules);
    }

    /**
     * 更新与存入cms节目单
     * 1.先查询cms_schedule中满足条件的节目单(channelCode+startDate为条件查询)
     * 2.判断删除违禁节目单的startTime是否在其中，如在就更新，否则写入
     * 3.将新增的违禁节目单调用cms的save()方法
     * 4.更新违禁节目单数据到cms的update()方法
     *
     * @param cmsProhibitSchedules
     *
     *
     */
    public void updateOrSaveCmsSchedules(List<CmsProhibitSchedule> cmsProhibitSchedules) {
        //先查询库的数据
        Map<String, List<CmsProhibitSchedule>> prohibitSchedulesMap = cmsProhibitSchedules.stream().collect(Collectors.groupingBy(e -> (e.getChannelCode() + e.getStartDate())));
        for (Map.Entry<String, List<CmsProhibitSchedule>> entry : prohibitSchedulesMap.entrySet()) {
            List<CmsProhibitSchedule> insertList = new ArrayList<>();
            List<CmsProhibitSchedule> updateList = new ArrayList<>();
            List<CmsProhibitSchedule> mapValue = entry.getValue();

            List<CmsSchedule> cmsSchedules = cmsScheduleService.selectByChannelCodeAndStartDate(mapValue.get(0).getChannelCode(), mapValue.get(0).getStartDate());
            if (!CollectionUtils.isEmpty(cmsSchedules)) {
                Map<String,CmsSchedule> map = cmsSchedules.stream().collect(Collectors.toMap(CmsSchedule::getStartTime, Function.identity(),(e1,e2)->e1));
                List<String> timeList = cmsSchedules.stream().map(CmsSchedule::getStartTime).collect(Collectors.toList());
                insertList = mapValue.stream().filter(e -> !timeList.contains(e.getStartTime())).collect(Collectors.toList());
                mapValue.forEach(e->{
                    if(timeList.contains(e.getStartTime())){
                        e.setId(map.get(e.getStartTime()).getId());
                        updateList.add(e);
                    }
                });
            } else {
                insertList = mapValue;
            }
            if (!CollectionUtils.isEmpty(insertList)) {
                List<CmsScheduleDto> insertCmsSchedules = BeanUtil.copyToList(insertList, CmsScheduleDto.class, CopyOptions.create().setIgnoreProperties(new String[] {"id"}));
                insertCmsSchedules.forEach(e -> {
                    e.setRequestResource(SourceEnum.SYSWORK.getValue());
                    cmsScheduleService.news(e);
                });
            }
            if (!CollectionUtils.isEmpty(updateList)) {
                List<CmsScheduleDto> updateCmsSchedules = BeanUtil.copyToList(updateList, CmsScheduleDto.class,null);
                updateCmsSchedules.forEach(e -> {
                    e.setRequestResource(SourceEnum.SYSWORK.getValue());
                    cmsScheduleService.up(e);
                });
            }
        }
    }

    @Resource
    private CmsProhibitScheduleMapper cmsProhibitScheduleMapper;
    @Resource
    private CmsScheduleService cmsScheduleService;
    @Resource
    private ExportTask<CmsProhibitSchedule> exportTask;
}
