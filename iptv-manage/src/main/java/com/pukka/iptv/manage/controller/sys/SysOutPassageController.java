package com.pukka.iptv.manage.controller.sys;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.pukka.iptv.common.base.vo.CommonResponse;
import com.pukka.iptv.common.data.model.sys.SysOutPassage;
import com.pukka.iptv.common.data.vo.IdList;
import com.pukka.iptv.common.log.annotation.SysLog;
import com.pukka.iptv.common.log.enums.OperateObjectEnum;
import com.pukka.iptv.common.log.enums.OperateTypeEnum;
import com.pukka.iptv.manage.service.sys.SysOutPassageService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.AllArgsConstructor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;

/**
 *
 * @author: tan
 * @date: 2021-9-2 23:02:27
 */

@RestController
@AllArgsConstructor
@RequestMapping(value = "/sysOutPassage", produces = MediaType.APPLICATION_JSON_VALUE)
@Api(tags="sysOutPassage管理")
public class SysOutPassageController {

    @Autowired
    private SysOutPassageService sysOutPassageService;

    @ApiOperation(value = "分页")
    @GetMapping("/page" )
    public CommonResponse<Page> page(@Valid Page<SysOutPassage> page, SysOutPassage sysOutPassage) {
        return  CommonResponse.success(sysOutPassageService.page(page, sysOutPassage));
    }

    @ApiOperation(value = "分页")
    @GetMapping("/pageFilter" )
    public CommonResponse<Page> pageFilter(@Valid Page<SysOutPassage> page, SysOutPassage sysOutPassage) {
        return  CommonResponse.success(sysOutPassageService.pageFilter(page, sysOutPassage));
    }

    @ApiOperation(value = "统计报表专用分页")
    @GetMapping("/pageStatistics" )
    public CommonResponse<Page> pageStatistics(@Valid Page<SysOutPassage> page, Long spId) {
        return  CommonResponse.success(sysOutPassageService.pageStatistics(page, spId));
    }

    @ApiOperation(value = "详情")
    @GetMapping("/getById")
    public CommonResponse<SysOutPassage> getById(@Valid @RequestParam(name = "id", required = true)  Long id) {
        return CommonResponse.success(sysOutPassageService.getById(id));
    }
    @ApiOperation(value = "查询")
    @GetMapping("/getByLspId")
    public CommonResponse<SysOutPassage> getByLspId(@Valid @RequestParam(name = "lspId", required = true)  String lspId){
        return CommonResponse.success(sysOutPassageService.getByLspId(lspId));
    }

    @SysLog(objectType = OperateObjectEnum.SYS_OUT_PASSAGE, operateType = OperateTypeEnum.SAVE, objectIds = "#sysOutPassage.id", objectNames = "#sysOutPassage.name")
    @ApiOperation(value = "新增")
    @PostMapping("/save")
    public CommonResponse<Boolean> save(@Valid @RequestBody SysOutPassage sysOutPassage) {
        return  CommonResponse.success(sysOutPassageService.saveToCacheAndDB(sysOutPassage));
    }

    @SysLog(objectType = OperateObjectEnum.SYS_OUT_PASSAGE, operateType = OperateTypeEnum.UPDATE, objectIds = "#sysOutPassage.id", objectNames = "#sysOutPassage.name")
    @ApiOperation(value = "修改")
    @PutMapping("/update")
    public CommonResponse<Boolean> updateById(@Valid @RequestBody SysOutPassage sysOutPassage) {
        return CommonResponse.success(sysOutPassageService.updateToCacheAndDB(sysOutPassage));
    }

    @SysLog(objectType = OperateObjectEnum.SYS_OUT_PASSAGE, operateType = OperateTypeEnum.DELETE, objectIds = "#id")
    @ApiOperation(value = "删除" ,hidden = true)
    @DeleteMapping("/deleteById" )
    public CommonResponse<Boolean> deleteById(@Valid @RequestParam(name = "id", required = true) Long id) {
        return  CommonResponse.success(sysOutPassageService.removeTwoById(id));
    }

    @SysLog(objectType = OperateObjectEnum.SYS_OUT_PASSAGE, operateType = OperateTypeEnum.DELETE, objectIds = "#idList.ids")
    @ApiOperation(value = "批量删除")
    @DeleteMapping("/deleteByIds" )
    public CommonResponse<Boolean> deleteByIds(@Valid @RequestBody IdList idList) {
        return  CommonResponse.success(sysOutPassageService.removeTwoByIds(idList.getIds()));
    }

}
