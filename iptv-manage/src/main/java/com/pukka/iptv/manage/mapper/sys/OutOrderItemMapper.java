package com.pukka.iptv.manage.mapper.sys;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.pukka.iptv.common.data.model.OutOrderItem;
import com.pukka.iptv.common.data.vo.sys.OutOrderItemVo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR>
 * @Date 2021/9/9 10:19 上午
 * @description: 子工单
 * @Version 1.0
 */
@Mapper
public interface OutOrderItemMapper extends BaseMapper<OutOrderItem> {
    /**
     * 子工单重新发布
     *
     * @param code
     * @param id
     * @param correlateId
     * @return
     */
    boolean orderRepublish(@Param("code") String code, @Param("id") Long id, @Param("correlateId") String correlateId);

    /**
     * 获取主工单任务id
     *
     * @param ids
     * @return
     */
    List<String> getBaseOrderId(@Param("code") String code, @Param("ids") List<String> ids);

    /**
     * 子工单更新工单文件地址
     *
     * @return
     */
    boolean updateCmdFileUrl(@Param("code") String code, @Param("correlateId") String correlateId, @Param("cmdfileurl") String cmdFileUrl);

    /**
     * 分页
     *
     * @param page
     * @param size
     * @return
     */
    List<OutOrderItemVo> findListByPage(@Param("code") String code, @Param("outOrderItemVo") OutOrderItemVo outOrderItemVo, @Param("startTime") String startTime, @Param("endTime") String endTime, @Param("page") Long page, @Param("size") Long size);

    /**
     * 计数
     *
     * @param code
     * @param outOrderItemVo
     * @param startTime
     * @param endTime
     * @return
     */
    long findList(@Param("code") String code, @Param("outOrderItemVo") OutOrderItemVo outOrderItemVo, @Param("startTime") String startTime, @Param("endTime") String endTime);

    /**
     * 通过id，type查询contentId,contentCode
     *
     * @param tableName
     * @param contentId
     * @return
     */
    OutOrderItemVo selectByIdType(@Param("tableName") String tableName, @Param("contentId") Long contentId);

    /**
     * 通过id，type查询Category contentId,contentCode
     *
     * @param tableName
     * @param contentId
     * @return
     */
    OutOrderItem selectCategory(@Param("tableName") String tableName, @Param("contentId") Long contentId);

    /**
     * 通过id，type查询Package contentId,contentCode
     *
     * @param tableName
     * @param contentId
     * @return
     */
    OutOrderItem selectPackage(@Param("tableName") String tableName, @Param("contentId") Long contentId);

    /**
     * 插入工单
     *
     * @param outOrderItem
     * @return
     */
    Boolean insertItem(@Param("param") OutOrderItemVo outOrderItem);

    /**
     * 更新工单
     *
     * @param outOrderItem
     * @return
     */
    Boolean updateResult(@Param("param") OutOrderItemVo outOrderItem);

    /**
     * 反馈工单
     *
     * @param outOrderItem
     * @return
     */
    Boolean callBackResult(@Param("param") OutOrderItemVo outOrderItem);

    /**
     * 查询工单信息
     *
     * @param outOrderItem
     * @return
     */
    OutOrderItem getItem(@Param("param") OutOrderItemVo outOrderItem);

    /**
     * 查询子工单结果
     */
    OutOrderItem selectResult(@Param("param") OutOrderItemVo outOrderItem);

    /**
     * 子工单信息查询
     *
     * @param code 分发通道code
     * @param id   id
     * @return
     */
    OutOrderItemVo getOrderItemById(@Param("code") String code, @Param("id") String id);

    /**
     * 子工单信息查询
     *
     * @param code
     * @param id
     * @return
     */
    OutOrderItemVo getOrderItemByBaseOrderId(@Param("code") String code, @Param("baseid") String id);

    /**
     * 根据ids批量查询
     *
     * @param code
     * @param ids
     * @return
     */
    List<OutOrderItemVo> getOrderItemByIds(@Param("code") String code, @Param("ids") List<String> ids);

    OutOrderItemVo selectCategoryCodeAndNameByIdType(@Param("tableName") String tableName, @Param("contentId") Long contentId);

    OutOrderItemVo selectPackageCodeAndNameByIdType(@Param("tableName") String tableName, @Param("contentId") Long contentId);

    OutOrderItemVo selectPhysicalChannelCodeAndNameByIdType(@Param("tableName") String tableName, @Param("contentId") Long contentId);

    OutOrderItemVo selectScheduleAndNameByIdType(@Param("tableName") String tableName, @Param("contentId") Long contentId);

    OutOrderItemVo selectPictureAndNameByIdType(@Param("tableName") String tableName, @Param("contentId") Long contentId);

    /**
     * 创建子工单表
     * @param code
     * @return
     */
    Boolean createOutOrderItem(@Param("code") String code);
}
