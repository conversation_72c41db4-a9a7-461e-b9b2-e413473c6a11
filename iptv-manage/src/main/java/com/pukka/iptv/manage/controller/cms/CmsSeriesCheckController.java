package com.pukka.iptv.manage.controller.cms;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.pukka.iptv.common.base.exception.CommonResponseException;
import com.pukka.iptv.common.base.vo.CommonResponse;
import com.pukka.iptv.common.core.util.DateUtils;
import com.pukka.iptv.common.data.dto.CmsSeriesDO;
import com.pukka.iptv.common.data.dto.CmsSeriesEndDto;
import com.pukka.iptv.common.data.vo.cms.CmsSeriesVO;
import com.pukka.iptv.common.log.annotation.SysLog;
import com.pukka.iptv.common.log.enums.OperateObjectEnum;
import com.pukka.iptv.common.log.enums.OperateTypeEnum;
import com.pukka.iptv.manage.service.cms.CmsSeriesCheckService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.AllArgsConstructor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;

@RestController
@AllArgsConstructor
@RequestMapping(value = "/cmsSeriesCheck", produces = MediaType.APPLICATION_JSON_VALUE)
@Api(tags = "cmsSeries管理")
public class CmsSeriesCheckController {
    @Autowired
    private CmsSeriesCheckService cmsSeriesCheckService;

    @ApiOperation(value = "剧集自审列表")
    @PostMapping("/series/selfList")
    public CommonResponse seriesSelfList(@RequestBody CmsSeriesDO cmsSeriesDO) {
        Page page = new Page();
        page.setCurrent(cmsSeriesDO.getCurrent());
        page.setSize(cmsSeriesDO.getSize());
        if (!DateUtils.checkDataPattern(DateUtils.YYYY_MM_DD, cmsSeriesDO.getStartTime(), cmsSeriesDO.getEndTime())) {
            return CommonResponse.commonfail("日期格式错误");
        }
        return CommonResponse.success(cmsSeriesCheckService.seriesSelfList(page, cmsSeriesDO, cmsSeriesDO.getStartTime(), cmsSeriesDO.getEndTime(), true));
//        return CommonResponse.success(cmsSeriesCheckService.seriesSelfList(page,cmsSeries, startTime, endTime));
    }

    @ApiOperation(value = "剧集详情")
    @GetMapping("/series/getById")
    public CommonResponse seriesById(@Valid @RequestParam("id") Long id) {
        return CommonResponse.success(cmsSeriesCheckService.seriesById(id));
    }

    @ApiOperation(value = "剧集终重列表")
    @GetMapping("/series/endList")
    public CommonResponse seriesEndList(@Valid Page page, CmsSeriesEndDto cmsSeriesEndDto) {
        return CommonResponse.success(cmsSeriesCheckService.seriesEndList(page, cmsSeriesEndDto));
    }

    @SysLog(objectType = OperateObjectEnum.CMS_SERIES_EXPROT, operateType = OperateTypeEnum.EXPORT)
    @ApiOperation(value = "剧集媒资导出")
    @PostMapping("/cmsSeriesExport")
    public CommonResponse export(@RequestBody CmsSeriesVO cmsSeriesDO) throws InterruptedException {
        if (!DateUtils.checkDataPattern(DateUtils.YYYY_MM_DD, cmsSeriesDO.getStartTime(), cmsSeriesDO.getEndTime())) {
            throw new CommonResponseException("日期格式错误");
        }
        return CommonResponse.success(cmsSeriesCheckService.export(cmsSeriesDO, cmsSeriesDO.getStartTime(), cmsSeriesDO.getEndTime()));
    }
}
