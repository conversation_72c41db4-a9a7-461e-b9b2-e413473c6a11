package com.pukka.iptv.manage.controller.sys;

import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.pukka.iptv.common.base.enums.AuthorityTypeEnums;
import com.pukka.iptv.common.base.enums.StorageDirctoryTypeEnum;
import com.pukka.iptv.common.base.exception.CommonResponseException;
import com.pukka.iptv.common.base.vo.CommonResponse;
import com.pukka.iptv.common.data.config.CommonConfig;
import com.pukka.iptv.common.data.model.sys.*;
import com.pukka.iptv.common.data.util.ThreeDes;
import com.pukka.iptv.common.data.vo.IdList;
import com.pukka.iptv.common.log.annotation.SysLog;
import com.pukka.iptv.common.log.enums.OperateObjectEnum;
import com.pukka.iptv.common.log.enums.OperateTypeEnum;
import com.pukka.iptv.manage.service.sys.SysCpService;
import com.pukka.iptv.manage.service.sys.SysStorageDirctoryService;
import com.pukka.iptv.manage.service.sys.SysStorageService;
import com.thoughtworks.xstream.XStream;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import java.io.BufferedOutputStream;
import java.io.IOException;
import java.io.OutputStream;
import java.net.URLEncoder;
import java.util.ArrayList;
import java.util.List;

/**
 * @author: luo
 * @date: 2021-9-9 8:36:53
 */

@RestController
@AllArgsConstructor
@RequestMapping(value = "/sysCp", produces = MediaType.APPLICATION_JSON_VALUE)
@Api(tags = "sysCp管理")
@Slf4j
public class SysCpController {

    @Autowired
    private SysCpService sysCpService;
    @Autowired
    private SysStorageService storageService;
    @Autowired
    private SysStorageDirctoryService storageDirctoryService;
    @Autowired
    private ThreeDes threeDes;


    @Autowired
    private CommonConfig commonConfig;

    @ApiOperation(value = "分页")
    @GetMapping("/page")
    public CommonResponse<Page> page(@Valid Page<SysCp> page, SysCp sysCp) {
        Page<SysCp> pageMysql = sysCpService.page(page, sysCp);
        List<SysCp> records = pageMysql.getRecords();
        if (CollUtil.isNotEmpty(records)) {
            for (SysCp record : records) {
                Long totalSpace = record.getTotalSpace();
                record.setTotalSpace(null != totalSpace ? totalSpace / 1024 / 1024 : 0);
            }
        }
        return CommonResponse.success(pageMysql);
    }

    @ApiOperation(value = "统计报表专用分页根据存储查cp")
    @GetMapping("/pageByStorage")
    public CommonResponse<Page> pageByStorage(@Valid Page<SysCp> page, Long storageId) {
        return  CommonResponse.success(sysCpService.pageByStorage(page, storageId));
    }

    @ApiOperation(value = "统计报表专用分页")
    @GetMapping("/pageStatistics" )
    public CommonResponse<Page> pageStatistics(@Valid Page<SysCp> page, Long spId) {
        return  CommonResponse.success(sysCpService.pageStatistics(page, spId));
    }

    @ApiOperation(value = "详情")
    @GetMapping("/getById")
    public CommonResponse<SysCp> getById(@Valid @RequestParam(name = "id", required = true) Long id) {
        SysCp byId = sysCpService.getById(id);
        if (null != byId) {
            Long totalSpace = byId.getTotalSpace();
            byId.setTotalSpace(null != totalSpace ? totalSpace / 1024 / 1024 : 0);
        }
        return CommonResponse.success(byId);
    }

    @SysLog(objectType = OperateObjectEnum.SYS_CP, operateType = OperateTypeEnum.SAVE, objectIds = "#sysCp.id", objectNames = "#sysCp.name")
    @ApiOperation(value = "新增")
    @PostMapping("/save")
    public CommonResponse<Boolean> save(@Valid @RequestBody SysCp sysCp) {
        return CommonResponse.success(sysCpService.saveToCacheAndDB(sysCp));
    }

    @SysLog(objectType = OperateObjectEnum.SYS_CP, operateType = OperateTypeEnum.UPDATE, objectIds = "#sysCp.id", objectNames = "#sysCp.name")
    @ApiOperation(value = "修改")
    @PutMapping("/update")
    public CommonResponse<Boolean> updateById(@Valid @RequestBody SysCp sysCp) {
        return CommonResponse.success(sysCpService.updateToCacheAndDB(sysCp));
    }

    @SysLog(objectType = OperateObjectEnum.SYS_CP, operateType = OperateTypeEnum.UPDATE, objectIds = "#sysCp.id", objectNames = "#sysCp.name")
    @ApiOperation(value = "修改状态")
    @PutMapping("/updateStatus")
    public CommonResponse<Boolean> updateStatusById(@Valid @RequestBody SysCp sysCp) {
        return CommonResponse.success(sysCpService.updateStatusById(sysCp));
    }

    @SysLog(objectType = OperateObjectEnum.SYS_CP, operateType = OperateTypeEnum.DELETE, objectIds = "#id")
    @ApiOperation(value = "删除", hidden = true)
    @DeleteMapping("/deleteById")
    public CommonResponse<Boolean> deleteById(@Valid @RequestParam(name = "id", required = true) Long id) {
        return CommonResponse.success(sysCpService.removeById(id));
    }

    @SysLog(objectType = OperateObjectEnum.SYS_CP, operateType = OperateTypeEnum.DELETE, objectIds = "#idList.ids")
    @ApiOperation(value = "批量删除")
    @DeleteMapping("/deleteByIds")
    public CommonResponse<Boolean> deleteByIds(@Valid @RequestBody IdList idList) {
        return CommonResponse.success(sysCpService.removeByIds(idList.getIds()));
    }

    @SysLog(objectType = OperateObjectEnum.SYS_CP, operateType = OperateTypeEnum.DOWNLOAD, objectIds = "#id")
    @ApiOperation(value = "下载存储关联文件")
    @GetMapping("/api/download")
    public CommonResponse downBlob(HttpServletResponse response, @RequestParam(name = "id", required = true) Long id) {
        log.info("开始生成存储关联文件 cpId = {} ", id);
        String xmlHeader = "<?xml version='1.0' encoding='UTF-8'?>";
        SysCp cp = sysCpService.getById(id);
        if (cp == null) {
            log.error("CP不存在 id ", id);
            throw new CommonResponseException("CP不存在");
        }

        SysStorage storage = storageService.getById(cp.getStorageId());
        if (storage == null) {
            log.error("存储不存在 storageId ", cp.getStorageId());
            throw new CommonResponseException("CP 未关联存储或存储不存在");
        }

        SysStorageDirctory storageDirctory = storageDirctoryService.getOne(
                Wrappers.lambdaQuery(SysStorageDirctory.class)
                        .eq(SysStorageDirctory::getStorageId, cp.getStorageId())
                        .eq(SysStorageDirctory::getType, StorageDirctoryTypeEnum.COMPLETED_FILM.getCode())
                        .eq(SysStorageDirctory::getAuthorityType, AuthorityTypeEnums.READ_WRITE.getCode())
                        .last("LIMIT 1")
        );
        if (storageDirctory == null) {
            log.error("该CP存储 目录 不存在 storageId ", cp.getStorageId());
            throw new CommonResponseException("该CP存储 目录 不存在 storageId =" + cp.getStorageId());
        }
        String blobsResourceURL = "ftp://" + storage.getInnerUrl() + "/" + cp.getName() + "/";
        String fileName = "";
        try (
                OutputStream outputStream = new BufferedOutputStream(response.getOutputStream())
        ) {
            log.info("构建存储关联文件内容 cpId = {} ", id);
            /** 构建XML文件内容 */
            String encoding = commonConfig.getEncoding();
            BlobFile blobFile = new BlobFile();
            blobFile.setAccount(storageDirctory.getAccount());
            blobFile.setPassword(ThreeDes.encrypt(storageDirctory.getPassword()));
            blobFile.setBlobuploadurl(commonConfig.getBlobUploadURL());
            blobFile.setBlobsourceurl(URLEncoder.encode(blobsResourceURL, encoding));
            blobFile.setCpid(id.toString());
            blobFile.setCpcode(cp.getCode());
            blobFile.setCpname(URLEncoder.encode(cp.getName(), encoding));
            blobFile.setResourceinfourl(commonConfig.getBlobUploadURL());
            blobFile.setDiskSpaceUrl(commonConfig.getDiskSpaceUrl());

            Blobs blobs = new Blobs();
            ArrayList<BlobFile> blobFiles = new ArrayList<>();
            blobFiles.add(blobFile);
            blobs.setBlobs(blobFiles);
            XStream xStream = new XStream();
            xStream.alias("root", Blobs.class);
            xStream.alias("blob", BlobFile.class);
//            String context = xmlHeader + xStream.toXML(blobs);
            // 转成XML字符串
            String context = ThreeDes.encrypt(xmlHeader + xStream.toXML(blobs));
            fileName = cp.getName() + ".xml";

            byte[] buffer = context.getBytes();

            // 清空response
            response.reset();
            // 设置response的Header
            response.setCharacterEncoding("UTF-8");
            //Content-Disposition的作用：告知浏览器以何种方式显示响应返回的文件，用浏览器打开还是以附件的形式下载到本地保存
            //attachment表示以附件方式下载   inline表示在线打开   "Content-Disposition: inline; filename=文件名.mp3"
            // filename表示文件的默认名称，因为网络传输只支持URL编码的相关支付，因此需要将文件名URL编码后进行传输,前端收到后需要反编码才能获取到真正的名称
            response.addHeader("Content-Disposition", "attachment;filename=" + URLEncoder.encode(fileName, "UTF-8"));
            // 告知浏览器文件的大小
            response.addHeader("Content-Length", String.valueOf(context.length()));
            response.setContentType("application/octet-stream");
            outputStream.write(buffer);
            outputStream.flush();
            log.info("下载存储关联文件结束");
        } catch (IOException e) {
            log.error("生成 存储关联文件异常 {} ", e);
            throw new CommonResponseException("生成 存储关联文件异常=" + id);
        }
        return CommonResponse.success(fileName);
    }


    @ApiOperation(value = "根据账户查询cp列表", hidden = true)
    @GetMapping("/getCpList")
    public CommonResponse<List<SysCp>> getCpList(@RequestParam(name = "type", required = false) Integer type, String name) {
        return CommonResponse.success(sysCpService.findSysCpByType(type, name));
    }
}
