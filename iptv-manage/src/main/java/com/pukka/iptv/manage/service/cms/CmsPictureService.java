package com.pukka.iptv.manage.service.cms;

import com.baomidou.mybatisplus.extension.service.IService;
import com.pukka.iptv.common.base.vo.CommonResponse;
import com.pukka.iptv.common.data.model.cms.CmsPicture;
import com.pukka.iptv.common.data.model.order.SubOrderMappingsEntity;
import com.pukka.iptv.common.data.vo.cms.CmsPictureVO;

import java.util.Collection;
import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * 图片
 */

public interface CmsPictureService extends IService<CmsPicture> {

    boolean autoFeedBackPicDel(List<String> codeList, List<Long> spIdList);
    //ToDo:20230214 弃用方法
    boolean delPicByCodeAndSp(List<String> codeList, List<Long> spIdList);
    boolean autoFeedBackPicMappingDel(List<SubOrderMappingsEntity> deletePictureMappingEntities, List<Long> spIdList);
    //ToDo:20230214 弃用方法
    boolean deletePictureMapping(List<SubOrderMappingsEntity> deletePictureMappingEntities, List<Long> spIdList);
    List<CmsPicture> getByContentIdContentType(Long contentId, Integer contentType);

    /**
     * 图片列表查询
     * @param cmsPicture
     * @return
     */
    List<CmsPictureVO> listById(CmsPicture cmsPicture);

    void delPicture(CmsPicture cmsPicture) ;

    CommonResponse delPictureCheckStatus(CmsPicture cmsPicture);

    CmsPicture getByCode(String code);

    CommonResponse<String> up(CmsPicture cmsPicture);

    List<CmsPicture> listBySeriesId(List<Long> seriesIdList);

    List<CmsPicture> listByProgramId(List<Long> programIdList);

    void deleteByIds(Set<Long> seriesPictureIds);

    Set<String> countByCode(Set<String> pictureCodes);

    Map<String, CmsPicture> listByContentCode(Collection<String> cmsContentCode);
}


