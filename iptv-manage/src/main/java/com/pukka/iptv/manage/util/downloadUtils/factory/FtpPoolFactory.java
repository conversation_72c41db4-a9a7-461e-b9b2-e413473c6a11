package com.pukka.iptv.manage.util.downloadUtils.factory;

import com.pukka.iptv.manage.util.downloadUtils.config.FtpClientProperties;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.net.ftp.FTPClient;
import org.apache.commons.net.ftp.FTPReply;
import org.apache.commons.pool2.BaseObjectPool;
import org.apache.commons.pool2.PooledObject;
import org.springframework.util.ObjectUtils;

import java.io.BufferedInputStream;
import java.io.IOException;
import java.io.InputStream;
import java.io.OutputStream;
import java.util.concurrent.ArrayBlockingQueue;
import java.util.concurrent.BlockingQueue;
import java.util.concurrent.TimeUnit;

/**
 * @author: chiron
 * Date: 2022/5/12 14:16
 * Description: FTP连接池
 */
@Slf4j
public class FtpPoolFactory extends BaseObjectPool<FTPClient> {

    private FtpClientProperties config;

    private final BlockingQueue<FTPClient> ftpBlockingQueue;
    private final FtpClientTemplate ftpClientTemplate;

    /**
     * 初始化连接池，需要注入一个工厂来提供FTPClient实例
     *
     * @param ftpClientTemplate
     * @throws Exception
     */
    public FtpPoolFactory(FtpClientProperties ftpClientProperties,FtpClientTemplate ftpClientTemplate) {
        this.config = ftpClientProperties;
        this.ftpClientTemplate = ftpClientTemplate;
        ftpBlockingQueue = new ArrayBlockingQueue<>(config.getDefaultPoolSize());
        initPool(config.getDefaultPoolSize());
    }

    /**
     * 初始化连接池，需要注入一个工厂来提供FTPClient实例
     *
     * @param maxPoolSize 最大连接数
     */
    private void initPool(int maxPoolSize) {
        try {
            for (int i = 0; i < maxPoolSize; i++) {
                // 往池中添加对象
                addObject();
            }
        } catch (Exception exception) {
            log.error("init ftp Pool error:{}", exception);
        }

    }

    /**
     * 从连接池中获取对象
     */
    @Override
    public FTPClient borrowObject() throws Exception {
        FTPClient client = ftpBlockingQueue.take();
        if (ObjectUtils.isEmpty(client)) {
            //创建对象
            client = ftpClientTemplate.create();
        }
        PooledObject<FTPClient> ftpClientPooled = ftpClientTemplate.wrap(client);
        if (!ftpClientTemplate.validateObject(ftpClientPooled)) {
            // 对无效的对象进行处理
            invalidateObject(client);
            // 创建新的对象
            client = ftpClientTemplate.create();
        }
        return client;
    }

    /**
     * 返还对象到连接池中
     */
    @Override
    public void returnObject(FTPClient client) {
        try {
            long timeout = 3L;
            if (client != null && !ftpBlockingQueue.offer(client, timeout, TimeUnit.SECONDS)) {
                ftpClientTemplate.destroyObject(ftpClientTemplate.wrap(client));
            }
        } catch (InterruptedException e) {
            log.error("return ftp client interrupted ...{}", e);
        }
    }

    /**
     * 移除无效的对象
     */
    @Override
    public void invalidateObject(FTPClient client) {
        try {
            client.changeWorkingDirectory("/");
        } catch (IOException e) {
            e.printStackTrace();
        } finally {
            ftpBlockingQueue.remove(client);
        }
    }

    /**
     * 增加一个新的链接，超时失效
     */
    @Override
    public void addObject() throws Exception {
        // 插入对象到队列
        ftpBlockingQueue.offer(ftpClientTemplate.create(), 3, TimeUnit.SECONDS);
    }

    /**
     * 关闭连接池
     */
    @Override
    public void close() {
        try {
            while (ftpBlockingQueue.iterator().hasNext()) {
                FTPClient client = ftpBlockingQueue.take();
                ftpClientTemplate.destroyObject(ftpClientTemplate.wrap(client));
            }
        } catch (Exception e) {
            log.error("close ftp client ftpBlockingQueue failed...{}", e);
        }
    }

    /**
     * 检查FTPClient实例是否存在
     *
     * @return
     */
    public boolean checkFTPClientObject() {
        if (org.apache.commons.lang3.ObjectUtils.isNotEmpty(ftpBlockingQueue)) {
            if (ftpBlockingQueue.size() > 0) {
                return true;
            }
        } else {
            log.warn("FTPClient is Empty");
        }
        return false;
    }

    /**
     * 写入文件流至浏览器端
     * @param ftpClient
     * @param remotePath
     * @param out
     * @return
     */
    public Boolean streamTrans(FTPClient ftpClient,String remotePath, OutputStream out) {
        InputStream inputStream = null;
        BufferedInputStream bufferedInputStream = null;
        try {

            byte[] buf = new byte[1024];
            int len = 0;
            // 验证FTP服务器是否登录成功
            int replyCode = ftpClient.getReplyCode();
            if (!FTPReply.isPositiveCompletion(replyCode)) {
                log.warn("ftpServer refused connection, replyCode:{}", replyCode);
                return false;
            }
            inputStream = ftpClient.retrieveFileStream(remotePath);
            bufferedInputStream = new BufferedInputStream(inputStream);
            while (bufferedInputStream != null && (len = bufferedInputStream.read(buf)) > 0) {
                out.write(buf, 0, len);
            }
            out.flush();
            out.close();
            //ftpClient.logout();
            return true;
        } catch (Exception exception) {
            log.error("下载视频介质 -----> retrieve file failure!,错误信息:{}", exception);
            return false;
        } finally {
            if (bufferedInputStream != null) {
                try {
                    bufferedInputStream.close();
                } catch (IOException e) {
                    log.error("下载视频介质 -----> 关闭buffer流失败,错误信息:{}", e);
                }
            }
            if (inputStream != null) {
                try {
                    inputStream.close();
                } catch (IOException e) {
                    log.error("下载视频介质 -----> 关闭inputStream流失败,错误信息:{}", e);
                }
            }
        }
    }
}
