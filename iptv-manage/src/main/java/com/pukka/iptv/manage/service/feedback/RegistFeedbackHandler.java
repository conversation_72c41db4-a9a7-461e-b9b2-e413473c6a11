package com.pukka.iptv.manage.service.feedback;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.pukka.iptv.common.base.enums.ContentTypeEnum;
import com.pukka.iptv.common.base.enums.OrderBaseResultEnum;
import com.pukka.iptv.common.base.enums.PublishStatusEnum;
import com.pukka.iptv.common.core.util.ResultData;
import com.pukka.iptv.common.data.dto.PublishParamsDto;
import com.pukka.iptv.common.data.model.bms.*;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

/** 发布反馈处理器
 * @Author: liaowj
 * @Description:
 * @CreateDate: 2021/9/18 10:53
 * @Version: 1.0
*/
@Slf4j
@Component
public class RegistFeedbackHandler extends FeedbackHandler {


    @Override
    public ResultData getResult() {
        return null;
    }

    @Override
    public void excute(ContentTypeEnum contentType, PublishParamsDto params) {
        initParams(params, PublishStatusEnum.PUBLISH, PublishStatusEnum.FAILPUBLISH);
        switch (contentType) {
            // 单集剧头和序列片等bms_content内容
            case FILM:
            case TELEPLAY:
            case EPISODES:
                // 由于剧头发布工单和子集工单是独立的，此处只需要处理剧头
                dealContent().dealMappingPicture();
                break;
            // 子集
            case SUBSET:
                dealProgram().dealMappingPicture();
                break;
            case CHANNEL:
                //频道
                dealChannel().dealMappingPicture();
                break;
            case CHANNEL_PHYSICAL:
                //频道
                dealChannel().dealPhysicalChannel().dealMappingPicture();
                break;
            case PHYSICAL_CHANNEL:
                //物理频道
                dealPhysicalChannel();
                break;
            case CATEGORY:
                //栏目
                dealCategory().dealMappingPicture();
                break;
            case PACKAGE:
                //产品包
                dealPackage().dealMappingPicture();
                break;
            case SCHEDULE:
                //节目单
                dealSchedule();
                break;
            case PICTURE:
                dealPicture();
                break;
            case CATEGORY_PROGRAM:
            case CATEGORY_SERIES:
                //栏目单集/剧集
                dealCategoryContent();
                break;
            case CATEGORY_CHANNEL:
                //栏目频道
                dealCategryChannel();
                break;
            case PACKAGE_CHANNEL:
                //产品包频道
                dealPackageChannel();
                break;
            case PACKAGE_SERIES:
            case PACKAGE_PROGRAM:
                //产品包剧集
                dealPackageContent();
                break;
            default:
                log.error("处理内容反馈失败，ContentType={},失败原因:不存在的ContentType类型", contentType);
                break;

        }
    }


}
