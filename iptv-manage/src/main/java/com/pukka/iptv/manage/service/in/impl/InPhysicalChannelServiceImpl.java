package com.pukka.iptv.manage.service.in.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.pukka.iptv.common.data.model.in.InPhysicalChannel;
import com.pukka.iptv.manage.mapper.in.InPhysicalChannelMapper;
import com.pukka.iptv.manage.service.in.InPhysicalChannelService;
import org.springframework.stereotype.Service;

/**
 *
 * @author: tan
 * @date: 2021-8-24 10:48:50
 */

@Service
public class InPhysicalChannelServiceImpl extends ServiceImpl<InPhysicalChannelMapper, InPhysicalChannel> implements InPhysicalChannelService {


}


