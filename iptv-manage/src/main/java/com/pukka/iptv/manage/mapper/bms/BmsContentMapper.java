package com.pukka.iptv.manage.mapper.bms;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.pukka.iptv.common.data.annotation.DataPermission;
import com.pukka.iptv.common.data.model.bms.BmsContent;
import com.pukka.iptv.common.data.model.bms.BmsProgram;
import com.pukka.iptv.common.data.model.order.SubOrderObjectsEntity;
import com.pukka.iptv.common.data.vo.bms.CpFeedbackContentVO;
import com.pukka.iptv.common.data.vo.req.BmsContentQueryReq;
import com.pukka.iptv.common.data.vo.req.BmsContentQueryReqBySP;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 内容表，不包含子集
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2021-08-27 10:20:57
 */

@Mapper
@DataPermission(config = "cp_id=cpIds")
public interface BmsContentMapper extends BaseMapper<BmsContent>{
    List<Long> queryIdByCmsSeriesIdAndSpId(List<BmsProgram> list);

    List<Long> getId(@Param("id") Long id, @Param("type") Integer type);

    List<CpFeedbackContentVO> getCpFeedbackContents(@Param("count") Integer cpFeedbackCount, @Param("cpIdList") List<String> cpIdList);

    int updatePublishStatus(@Param("entityList") List<SubOrderObjectsEntity> orderObjectsEntities, @Param("spIdList") List<Long> spIdList);

    Page<BmsContentQueryReqBySP> getAuthorizedContentBySp(Page page, @Param("param") BmsContentQueryReq param);

    Page<BmsContentQueryReqBySP> getAuthorizedContentBySp( @Param("param") BmsContentQueryReq param);

    int setFirstPublishTime(@Param("ids") List<Long> ids);
}
