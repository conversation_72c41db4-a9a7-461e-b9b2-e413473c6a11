package com.pukka.iptv.manage.mapper.sys;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.pukka.iptv.common.data.annotation.DataPermission;
import com.pukka.iptv.common.data.dto.SysAuthorizationContentDto;
import com.pukka.iptv.common.data.model.sys.SysAuthorization;
import com.pukka.iptv.common.data.params.SysAuthorizationParam;
import com.pukka.iptv.common.data.vo.req.BmsContentQueryReq;
import com.pukka.iptv.common.data.vo.req.BmsContentQueryReqBySP;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 *
 * @author: tan
 * @date: 2021-8-26 18:34:22
 */

@Mapper
@DataPermission(config = "cp_id=cpIds&sp_id=spIds")
public interface SysAuthorizationMapper extends BaseMapper<SysAuthorization>{

    /**
     * 分页查询未授权的媒资
     *
     * @param page 分页参数
     * @param param 查询参数
     * @return page
     */
    Page<SysAuthorizationContentDto> getUnauthorizedContent(Page page, @Param("param") SysAuthorizationParam param);

    IPage<BmsContentQueryReqBySP> getUnauthorizedContentBySp(Page page, @Param("param") BmsContentQueryReq param);

    IPage<BmsContentQueryReqBySP> getUnauthorizedContentBySp( @Param("param") BmsContentQueryReq param);

    IPage<BmsContentQueryReqBySP> getUnauthorizedAndAuthContentBySp( @Param("param") BmsContentQueryReq param);

    IPage<BmsContentQueryReqBySP> getUnauthorizedAndAuthContentBySpForSeries( @Param("param") BmsContentQueryReq param);

    IPage<BmsContentQueryReqBySP> getUnauthorizedAndAuthContentBySpForProgram( @Param("param") BmsContentQueryReq param);

    IPage<BmsContentQueryReqBySP> getSeriesAndProgramBySpForUnAnthorized( @Param("param") BmsContentQueryReq param);

    IPage<BmsContentQueryReqBySP> getSeriesAndProgramBySpForAnthorized( @Param("param") BmsContentQueryReq param);
    /**
     * 查询授权合同树
     *
     * @param name cp名称
     * @param businessType 运营类型
     * @return
     */
    @DataPermission(config = "s.cp_id=cpIds")
    List<SysAuthorization> getAuthorList(@Param("name") String name,@Param("businessType") Integer businessType);
}
