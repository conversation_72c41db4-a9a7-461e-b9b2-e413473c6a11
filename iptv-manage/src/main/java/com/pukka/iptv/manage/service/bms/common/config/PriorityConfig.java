package com.pukka.iptv.manage.service.bms.common.config;

import com.pukka.iptv.manage.service.bms.common.model.PriorityAction;
import lombok.Getter;
import lombok.Setter;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Component;

import java.util.Map;

/**
 * @Author: wangbo
 * @Date: 2022/3/23 9:25
 */
@RefreshScope
@Component
@ConfigurationProperties(prefix = "content")
@Getter
@Setter
public class PriorityConfig {
    private Map<String, Map<String, PriorityAction>> priority;

}
