package com.pukka.iptv.manage.listener.mq;

import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.google.common.base.Joiner;
import com.pukka.iptv.common.base.constant.RedisKeyConstants;
import com.pukka.iptv.common.base.enums.*;
import com.pukka.iptv.common.data.dto.SimpleSetExcelDto;
import com.pukka.iptv.common.data.model.bms.BmsContent;
import com.pukka.iptv.common.data.model.cms.CmsProgram;
import com.pukka.iptv.common.data.model.sys.*;
import com.pukka.iptv.common.data.vo.cms.CmsProgramVO;
import com.pukka.iptv.common.redis.service.RedisService;
import com.pukka.iptv.manage.config.SimpleSetMQConfig;
import com.pukka.iptv.manage.rule.IRuleProhibitHelper;
import com.pukka.iptv.manage.service.bms.BmsContentService;
import com.pukka.iptv.manage.service.cms.CmsProgramService;
import com.pukka.iptv.manage.service.sys.*;
import com.rabbitmq.client.Channel;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.springframework.amqp.core.Message;
import org.springframework.amqp.rabbit.annotation.Exchange;
import org.springframework.amqp.rabbit.annotation.Queue;
import org.springframework.amqp.rabbit.annotation.QueueBinding;
import org.springframework.amqp.rabbit.annotation.RabbitListener;
import org.springframework.amqp.support.converter.MessageConversionException;
import org.springframework.amqp.support.converter.MessageConverter;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.BeansException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.io.IOException;
import java.util.*;
import java.util.stream.Collectors;

@Component
@Slf4j
@Transactional
public class SimpleSetMQListener {

    @Autowired
    private CmsProgramService cmsProgramService;

    @Autowired
    private BmsContentService bmsContentService;

    @Autowired
    private SysAuthorizationService sysAuthorizationService;

    @Autowired
    private SysCpService sysCpService;

    @Autowired
    private SysCpContentProviderService sysCpContentProviderService;

    @Autowired
    private SubSetMQListener setMQListener;

    @Autowired
    private RedisService redisService;

    @Autowired
    private SysDictionaryItemService sysDictionaryItemService;

    @Autowired
    private SysDictionaryBaseService sysDictionaryBaseService;

    @Autowired
    private MessageConverter jackson2JsonMessageConverter;

    @Autowired
    private IRuleProhibitHelper iRuleProhibitHelper;


    /**
     * 处理cp收到后发送的消息
     *
     * @param messageorigin
     * @param channel
     */
    @Transactional(rollbackFor = Exception.class)
    @RabbitListener(bindings = {
            @QueueBinding(value = @Queue(value = SimpleSetMQConfig.EXCEL_IMPORT_QUEUE),
                    exchange = @Exchange(value = SimpleSetMQConfig.EXCEL_IMPORT_EXCHANGE),
                    key = SimpleSetMQConfig.EXCEL_IMPORT_ROUTING)}, containerFactory = "rabbitListenerContainerFactory")
    public void recieved(Message messageorigin, Channel channel) {
        try {
//            Jackson2JsonMessageConverter jackson2JsonMessageConverter = new Jackson2JsonMessageConverter();
            String excel = (String) jackson2JsonMessageConverter.fromMessage(messageorigin);
            log.info("excel单集导入消息消费:{}", excel);
            List<SimpleSetExcelDto> simpleSetExcelDto = JSONObject.parseArray(excel, SimpleSetExcelDto.class);
            for (SimpleSetExcelDto cmsprogramExcel : simpleSetExcelDto) {
                try {
                    CmsProgram cmsProgram = new CmsProgram();
                    BeanUtils.copyProperties(cmsprogramExcel, cmsProgram);
                    cmsProgram.setProhibitStatus(ProhibitStatusEnum.NO.getValue());
         /*           CmsProgram program = cmsProgramService.getOne(Wrappers.<CmsProgram>lambdaQuery()
                            .eq(CmsProgram::getCpName, cmsProgram.getCpName())
                            .eq(CmsProgram::getName, cmsProgram.getName())
                            .last("limit 1")
                    );

                    if (ObjectUtil.isNotEmpty(program)) {
                        log.info("单集导入失败 名称：{},所属CP：{}已存在",program.getName(),program.getCpName());
                        continue;
                    }*/
                    if (ObjectUtil.isNotEmpty(cmsProgram)) {
                        cmsProgram.setSeriesFlag(SeriesFlagEnum.SimpleSet.getValue());

                        //查询所属cp是否为跳过自审 如果是跳过自审
                        SysCp sysCp = sysCpService.getByName(cmsProgram.getCpName());
                        if (ObjectUtil.isNotEmpty(sysCp)) {
                            if (sysCp.getSkipCheck().equals(SkipCheckEnum.NoSkip.getValue())) {
                                cmsProgram.setCpCheckStatus(CpCheckStatusEnum.Await.getValue());
                                cmsProgram.setOpCheckStatus(OpCheckStatusEnum.Await.getValue());
                            }
                            if (sysCp.getSkipCheck().equals(SkipCheckEnum.SelfSkip.getValue())) {
                                cmsProgram.setCpCheckStatus(CpCheckStatusEnum.Pass.getValue());
                                cmsProgram.setCpCheckDesc("自动跳过审核");
                                cmsProgram.setCpCheckTime(new Date());
                            }
                            if (sysCp.getSkipCheck().equals(SkipCheckEnum.EndSkip.getValue())) {
                                cmsProgram.setOpCheckStatus(OpCheckStatusEnum.Pass.getValue());
                                cmsProgram.setOpCheckDesc("自动跳过审核");
                                cmsProgram.setOpCheckTime(new Date());
                            }
                            List<SysCpContentProvider> providerList = sysCpContentProviderService.getByCpId(sysCp.getId());
                            List<String> providerName = new ArrayList<>();
                            if (ObjectUtil.isNotEmpty(providerList)) {
                                providerList.forEach(p -> providerName.add(p.getName()));
                                if (!providerName.contains(cmsprogramExcel.getContentProvider())) {
                                    cmsProgram.setContentProvider(null);
                                }
                            } else {
                                cmsProgram.setContentProvider(null);
                            }
                            //如果为全部跳过则修改自审和终审状态
                            if (sysCp.getSkipCheck().equals(SkipCheckEnum.AllSkip.getValue())) {
                                cmsProgram.setCpCheckStatus(CpCheckStatusEnum.Pass.getValue());
                                cmsProgram.setOpCheckStatus(CpCheckStatusEnum.Pass.getValue());
                                cmsProgram.setOpCheckDesc("自动跳过审核");
                                cmsProgram.setOpCheckTime(new Date());
                                cmsProgram.setCpCheckDesc("自动跳过审核");
                                cmsProgram.setCpCheckTime(new Date());
                            }
                            cmsProgram.setCpId(sysCp.getId());
                            cmsProgram.setSource(RequestResourceEnum.SYSTEM.getCode());
                            cmsProgram.setPreviewStatus(MovieStatusEnum.NotRelevancy.getValue());
                            cmsProgram.setReleaseStatus(MovieStatusEnum.NotRelevancy.getValue());
                            cmsProgram.setStatus(1);
                            cmsProgram.setIsProhibit(IsProhibitEnum.ING.getValue());
                            setItem(cmsProgram);
                            cmsprogramExcel.def(cmsProgram);
                            //正片关联
                            setMQListener.setMovCode(cmsProgram, cmsprogramExcel.getMovName(), sysCp, 1);
                            //预览片关联
                            setMQListener.setMovCode(cmsProgram, cmsprogramExcel.getPreviewName(), sysCp, 2);
                            cmsProgramService.save(cmsProgram);
                            //添加违禁检测
                            if (!ProhibitSkipEnum.SKIP.getValue().equals(sysCp.getSkipProhibit())) {
                                ProhibitPointEnum prohibitPointEnum = null;
                                switch (ProhibitPointEnum.getByValue(sysCp.getPointProhibit())) {
                                    case INSERT:
                                        prohibitPointEnum = ProhibitPointEnum.INSERT;
                                        break;
                                    case EXAMINE:
                                        if (sysCp.getSkipCheck().equals(SkipCheckEnum.SelfSkip.getValue())
                                                || sysCp.getSkipCheck().equals(SkipCheckEnum.AllSkip.getValue())) {
                                            prohibitPointEnum = ProhibitPointEnum.EXAMINE;
                                        }
                                        break;
                                    default:
                                        break;
                                }
                                if (ObjectUtils.isNotEmpty(prohibitPointEnum)) {
                                    CmsProgramVO cmsProgramVO = new CmsProgramVO(cmsProgram, prohibitPointEnum);
                                    CmsProgram cmsProgram1 = iRuleProhibitHelper.checkProgramRuleProhibit(cmsProgramVO);
                                    cmsProgram.setProhibitStatus(cmsProgram1.getProhibitStatus());
                                    cmsProgram.setIsProhibit(cmsProgram1.getIsProhibit());
                                    cmsProgramService.updateById(cmsProgram);
                                }
                            } else {
                                cmsProgram.setProhibitStatus(ProhibitStatusEnum.NO.getValue());
                                cmsProgram.setIsProhibit(IsProhibitEnum.SKIP.getValue());
                                cmsProgramService.updateById(cmsProgram);
                            }
                            List<SysAuthorization> sysAuthorizations = sysAuthorizationService
                                    .list(Wrappers.<SysAuthorization>lambdaQuery()
                                            .eq(SysAuthorization::getCpName, cmsProgram.getCpName())
                                            .eq(SysAuthorization::getStatus, StatusEnum.COME.getCode()));
                            List<SysAuthorization> filterSysAuthorizations = new ArrayList<>();
                            if (ObjectUtils.isNotEmpty(sysAuthorizations)) {
                                filterSysAuthorizations = sysAuthorizations.stream()
                                        .filter(sysAuthorization ->
                                                ObjectUtils.isNotEmpty(redisService
                                                        .getCacheMapValue(RedisKeyConstants.SYS_SP
                                                                , String.valueOf(sysAuthorization.getSpId()))))
                                        .collect(Collectors.toList());
                            }
                            if (ProhibitStatusEnum.NO.getValue().equals(cmsProgram.getProhibitStatus()) && ObjectUtil.isNotEmpty(filterSysAuthorizations)) {
                                for (SysAuthorization sysAuthorization : filterSysAuthorizations) {
                                    BmsContent bmsContent = new BmsContent();
                                    //是否开启自动授权
                                    if (sysAuthorization.getAutoAuthorize().equals(AutoAuthorizeEnum.Yes.getValue())) {
                                        //将单集内容写入bms_content表中
                                        BeanUtils.copyProperties(cmsProgram, bmsContent, "id", "code");
                                        set(cmsProgram, sysAuthorization, bmsContent);
                                        bmsContentService.save(bmsContent);
                                    }
                                }
                            }
                        } else {
                            log.info("单集模板导入失败:当前CP不存在");
                        }

                    }
                } catch (BeansException e) {
                    log.info("单集导入异常:" + e.getMessage());
                    e.printStackTrace();
                }

            }
        } catch (MessageConversionException e) {
            log.info("单集导入异常:" + e.getMessage());
        } finally {
            //成功确认消息
            try {
                log.info("消息成功确认");
                channel.basicAck(messageorigin.getMessageProperties().getDeliveryTag(), false);
            } catch (IOException e) {
                log.info("basicAck ex:" + e.getMessage());
                try {
                    channel.basicAck(messageorigin.getMessageProperties().getDeliveryTag(), false);
                } catch (IOException ee) {
                    log.info("again basicAck ex:" + ee.getMessage());
                }
            }
        }
    }


    public void setItem(CmsProgram cmsProgram) {
        //一级分类id
        if (ObjectUtil.isNotEmpty(cmsProgram.getPgmCategory())) {
            SysDictionaryBase firstClass = sysDictionaryBaseService.getOne(Wrappers.<SysDictionaryBase>lambdaQuery().select(SysDictionaryBase::getId).eq(SysDictionaryBase::getCode, DictionaryBaseEnums.FIRST_CLASS.getValue()));
            SysDictionaryItem one = sysDictionaryItemService.getOne(Wrappers.<SysDictionaryItem>lambdaQuery()
                    .select(SysDictionaryItem::getId, SysDictionaryItem::getName)
                    .eq(SysDictionaryItem::getDictionaryBaseId, firstClass.getId())
                    .eq(SysDictionaryItem::getName, cmsProgram.getPgmCategory())
                    .last("limit 1")
            );
            if (ObjectUtil.isNotEmpty(one)) {
                cmsProgram.setPgmCategoryId(one.getId());
                cmsProgram.setPgmCategory(one.getName());
            } else if (ObjectUtil.isEmpty(one)) {
                cmsProgram.setPgmCategory(null);
            }
        }
        if (ObjectUtil.isNotEmpty(cmsProgram.getPgmSndClass())) {
            //将excel中的二级分类切割成数组
            String[] split = cmsProgram.getPgmSndClass().split(",");
            //查出二级分类的dictionaryBaseId
            SysDictionaryBase secondClass = sysDictionaryBaseService.getOne(Wrappers.<SysDictionaryBase>lambdaQuery().select(SysDictionaryBase::getId).eq(SysDictionaryBase::getCode, DictionaryBaseEnums.SECOND_CLASS.getValue()));
            //根据二级分类dictionaryBaseId 和 名称 查出所有二级分类 并进行名称去重
            List<SysDictionaryItem> list = sysDictionaryItemService.list(Wrappers.<SysDictionaryItem>lambdaQuery()
                    .select(SysDictionaryItem::getId, SysDictionaryItem::getName)
                    .eq(SysDictionaryItem::getDictionaryBaseId, secondClass.getId())
                    .in(SysDictionaryItem::getName, split)
            );
            //将二级分类id存入库中
            if (ObjectUtil.isNotEmpty(list)) {
                TreeSet<SysDictionaryItem> collect = list.stream().collect(Collectors.toCollection(() -> new TreeSet<>(Comparator.comparing(p -> p.getName()))));
                List<Long> idLis = new ArrayList<>();
                collect.forEach(p -> idLis.add(p.getId()));
                String join = Joiner.on(",").join(idLis);
                cmsProgram.setPgmSndClassId(join);

                List<String> nameList = new ArrayList<>();
                collect.forEach(p -> nameList.add(p.getName()));
                String nameJoin = Joiner.on(",").join(nameList);
                cmsProgram.setPgmSndClass(nameJoin);
            } else if (ObjectUtil.isEmpty(list)) {
                cmsProgram.setPgmSndClass(null);
            }
        }
        //产地
        if (ObjectUtil.isNotEmpty(cmsProgram.getOriginalCountry())) {
            SysDictionaryBase originalCountry = sysDictionaryBaseService.getOne(Wrappers.<SysDictionaryBase>lambdaQuery().select(SysDictionaryBase::getId).eq(SysDictionaryBase::getCode, DictionaryBaseEnums.ORIGINAL_COUNTRY.getValue()));
            SysDictionaryItem orig = sysDictionaryItemService.getOne(Wrappers.<SysDictionaryItem>lambdaQuery().select(SysDictionaryItem::getId)
                    .eq(SysDictionaryItem::getDictionaryBaseId, originalCountry.getId())
                    .eq(SysDictionaryItem::getName, cmsProgram.getOriginalCountry())
                    .last("limit 1"));
            if (ObjectUtil.isNotEmpty(orig)) {
                cmsProgram.setOriginalCountryId(orig.getId());
            } else if (ObjectUtil.isEmpty(orig)) {
                cmsProgram.setOriginalCountry(null);
            }
        }
    }

    private void set(CmsProgram cmsProgram, SysAuthorization sysAuthorization, BmsContent bmsContent) {
        bmsContent.setContentType(ContentTypeEnum.FILM.getValue());
        bmsContent.setPgmCategory(cmsProgram.getPgmCategory());
        bmsContent.setPgmSndClass(cmsProgram.getPgmSndClass());
        //关联
        bmsContent.setCmsContentCode(cmsProgram.getCode());
        bmsContent.setCmsContentId(cmsProgram.getId());

        bmsContent.setSpId(sysAuthorization.getSpId());
        bmsContent.setSpName(sysAuthorization.getSpName());
        bmsContent.setCpId(sysAuthorization.getCpId());
        bmsContent.setCpName(sysAuthorization.getCpName());
        bmsContent.setStatus(1);

//        SysSp sysSp = sysSpMapper.selectById(sysAuthorization.getSpId());
        SysSp sysSp = redisService.getCacheMapValue(RedisKeyConstants.SYS_SP, String.valueOf(sysAuthorization.getSpId()));
        if (ObjectUtil.isNotEmpty(sysSp)) {
            bmsContent.setOutPassageIds(sysSp.getOutPassageIds());
            bmsContent.setOutPassageNames(sysSp.getOutPassageNames());
            bmsContent.setBmsSpChannelId(sysSp.getBmsSpChannelId());
            bmsContent.setBmsSpChannelName(sysSp.getBmsSpChannelName());
        }
    }

}
