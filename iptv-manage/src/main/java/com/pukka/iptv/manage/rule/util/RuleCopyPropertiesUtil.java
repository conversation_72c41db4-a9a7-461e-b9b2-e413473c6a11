package com.pukka.iptv.manage.rule.util;

import com.pukka.iptv.common.base.enums.ContentTypeEnum;
import com.pukka.iptv.common.core.util.SafeUtil;
import com.pukka.iptv.common.data.vo.cms.CmsProgramVO;
import com.pukka.iptv.common.data.vo.cms.CmsSeriesVO;
import com.pukka.iptv.common.data.vo.copyright.RuleProhibitVo;
import org.springframework.stereotype.Component;

/**
 * @Author: chiron
 * @Date: 2022/08/09/16:59
 * @Description:
 */
@Component
public class RuleCopyPropertiesUtil {
    /**
     * 实体赋值
     *
     * @param cmsProgram
     * @return
     */
    public RuleProhibitVo copyCmsProgramProperties(CmsProgramVO cmsProgram) {
        RuleProhibitVo ruleProhibitVo = new RuleProhibitVo();
        ruleProhibitVo.setProhibitPointEnum(cmsProgram.getProhibitPointEnum());
        ruleProhibitVo.setType(SafeUtil.getString(ContentTypeEnum.FILM.getValue()))
                .setContentName(cmsProgram.getName())
                .setCpId(cmsProgram.getCpId())
                .setContentCode(cmsProgram.getCode())
                .setContentType(ContentTypeEnum.FILM.getValue())
                .setDirector(cmsProgram.getDirector())
                .setOriginalCountryId(cmsProgram.getOriginalCountryId())
                .setOriginalCountry(cmsProgram.getOriginalCountry())
                .setKpeople(cmsProgram.getKpeople())
                .setPgmCategory(cmsProgram.getPgmCategory())
                .setPgmCategoryId(cmsProgram.getPgmCategoryId())
                .setReleaseYear(cmsProgram.getReleaseYear());
        return ruleProhibitVo;
    }

    /**
     * 实体赋值
     *
     * @param cmsSeries
     * @return
     */
    public RuleProhibitVo copyCmsSeriesProperties(CmsSeriesVO cmsSeries) {
        RuleProhibitVo ruleProhibitVo = new RuleProhibitVo();
        ruleProhibitVo.setProhibitPointEnum(cmsSeries.getProhibitPointEnum());
        ruleProhibitVo.setType(SafeUtil.getString(ContentTypeEnum.TELEPLAY.getValue()))
                .setContentName(cmsSeries.getName())
                .setCpId(cmsSeries.getCpId())
                .setContentCode(cmsSeries.getCode())
                .setContentType(ContentTypeEnum.TELEPLAY.getValue())
                .setDirector(cmsSeries.getDirector())
                .setOriginalCountryId(cmsSeries.getOriginalCountryId())
                .setOriginalCountry(cmsSeries.getOriginalCountry())
                .setKpeople(cmsSeries.getKpeople())
                .setPgmCategory(cmsSeries.getPgmCategory())
                .setPgmCategoryId(cmsSeries.getPgmCategoryId())
                .setReleaseYear(cmsSeries.getReleaseYear());
        return ruleProhibitVo;
    }
}
