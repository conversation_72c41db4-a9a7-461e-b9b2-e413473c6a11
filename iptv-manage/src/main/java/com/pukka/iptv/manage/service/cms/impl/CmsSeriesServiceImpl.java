package com.pukka.iptv.manage.service.cms.impl;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.google.common.base.Joiner;
import com.pukka.iptv.common.api.feign.sys.DownloadFeignClient;
import com.pukka.iptv.common.base.constant.RedisKeyConstants;
import com.pukka.iptv.common.base.constant.TextConstants;
import com.pukka.iptv.common.base.enums.*;
import com.pukka.iptv.common.base.exception.BizException;
import com.pukka.iptv.common.base.exception.CommonResponseException;
import com.pukka.iptv.common.base.vo.CommonResponse;
import com.pukka.iptv.common.core.constant.SymbolConstant;
import com.pukka.iptv.common.core.util.bean.BeanUtils;
import com.pukka.iptv.common.data.dto.*;
import com.pukka.iptv.common.data.model.bms.*;
import com.pukka.iptv.common.data.model.cms.*;
import com.pukka.iptv.common.data.model.statistics.*;
import com.pukka.iptv.common.data.model.sys.*;
import com.pukka.iptv.common.data.vo.cms.CmsSeriesVO;
import com.pukka.iptv.common.data.vo.req.StatisticsInVo;
import com.pukka.iptv.common.rabbitmq.config.CmsContentRenameMQConfig;
import com.pukka.iptv.common.redis.service.RedisService;
import com.pukka.iptv.manage.export.model.ExcelTaskInfo;
import com.pukka.iptv.manage.export.model.ExportInfo;
import com.pukka.iptv.manage.export.task.ExportTask;
import com.pukka.iptv.manage.mapper.bms.BmsCategoryContentMapper;
import com.pukka.iptv.manage.mapper.bms.BmsPackageContentMapper;
import com.pukka.iptv.manage.mapper.cms.CmsMovieMapper;
import com.pukka.iptv.manage.mapper.cms.CmsResourceMapper;
import com.pukka.iptv.manage.mapper.cms.CmsSeriesMapper;
import com.pukka.iptv.manage.mapper.sys.SysDictionaryItemMapper;
import com.pukka.iptv.manage.rule.IRuleProhibitHelper;
import com.pukka.iptv.manage.service.api.StatisticsInDeleteService;
import com.pukka.iptv.manage.service.bms.BmsContentService;
import com.pukka.iptv.manage.service.bms.BmsPictureService;
import com.pukka.iptv.manage.service.bms.BmsProgramService;
import com.pukka.iptv.manage.service.cms.*;
import com.pukka.iptv.manage.service.common.FtpAnalysisUtil;
import com.pukka.iptv.manage.service.common.FtpUtil;
import com.pukka.iptv.manage.service.condition.RuleCondition;
import com.pukka.iptv.manage.service.condition.RuleResult;
import com.pukka.iptv.manage.service.condition.rule.LockStatusRule;
import com.pukka.iptv.manage.service.condition.rule.SpCheckRule;
import com.pukka.iptv.manage.service.sys.SysDictionaryItemService;
import com.pukka.iptv.manage.service.sys.SysStorageService;
import com.pukka.iptv.manage.service.sys.impl.SysCpServiceImpl;
import com.pukka.iptv.manage.service.sys.impl.SysDictionaryItemServiceImpl;
import com.pukka.iptv.manage.service.sys.impl.SysInPassageServiceImpl;
import com.pukka.iptv.manage.util.CopyUtil;
import com.pukka.iptv.manage.util.DateUtils;
import com.pukka.iptv.manage.util.RuleUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.amqp.rabbit.core.RabbitTemplate;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.validation.constraints.Max;
import java.io.ByteArrayOutputStream;
import java.time.LocalDateTime;
import java.util.*;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

import static java.util.regex.Pattern.compile;

/**
 * 剧集
 *
 * @author: luo
 * @date: 2021-8-31 16:36:16
 */

@Service
@Slf4j
public class CmsSeriesServiceImpl extends ServiceImpl<CmsSeriesMapper, CmsSeries> implements
    CmsSeriesService {

  @Autowired
  private RedisService redisService;

  @Autowired
  private CmsPictureService cmsPictureService;

  @Autowired
  private CmsMovieService cmsMovieService;

  @Autowired
  private BmsContentService bmsContentService;

  @Autowired
  private BmsPictureService bmsPictureService;

  @Lazy
  @Autowired
  private CmsProgramService cmsProgramService;

  @Autowired
  private BmsProgramService bmsProgramService;

  @Autowired
  private CmsResourceService cmsResourceService;

  @Autowired
  private SysDictionaryItemServiceImpl itemServiceimpl;

  @Autowired
  private SysStorageService storageService;

  @Autowired
  private SysInPassageServiceImpl sysInPassageService; //已缓存

  @Autowired
  private SysCpServiceImpl sysCpServiceimpl; //已缓存

  @Autowired
  private SysDictionaryItemService sysDictionaryItemService;

  @Autowired
  private CmsMovieMapper cmsMovieMapper;

  @Autowired
  private CmsResourceMapper cmsResourceMapper;

  @Autowired
  private CmsSeriesMapper cmsSeriesMapper;

  @Autowired
  private SysDictionaryItemMapper sysDictionaryItemMapper;

  @Autowired
  private CmsChannelServiceImpl cmsChannelServiceImpl;

  @Autowired
  DownloadFeignClient downloadFeignClient;

  @Autowired
  private StatisticsInDeleteService statisticsInDeleteService;

  @Lazy
  @Autowired
  private IRuleProhibitHelper iRuleProhibitHelper;

  @Autowired
  private RabbitTemplate rabbitTemplate;

  @Autowired
  private BmsCategoryContentMapper bmsCategoryContentMapper;

  @Autowired
  private BmsPackageContentMapper bmsPackageContentMapper;

  /**
   * 自动发布反馈-剧集删除操作
   *
   * @param codeList
   * @param spIdList
   * @return
   */
  @Override
  public boolean autoFeedBackSeriesDel(List<String> codeList, List<Long> spIdList) {
    //参数校验
    if (org.springframework.util.CollectionUtils.isEmpty(codeList)
        || org.springframework.util.CollectionUtils.isEmpty(spIdList)) {
      log.warn("自动发布反馈-剧集删除操作,参数codes:{},sps:{} 不全", codeList, spIdList);
      return false;
    }
    //更新剧集
    bmsContentService.update(Wrappers.lambdaUpdate(BmsContent.class)
        .set(BmsContent::getPublishDescription, "")
        .set(BmsContent::getPublishStatus, PublishStatusEnum.ROLLBACK.getCode())
        .ne(BmsContent::getContentType, ContentTypeEnum.FILM.getValue())
        .in(BmsContent::getSpId, spIdList)
        .in(BmsContent::getCmsContentCode, codeList));
    return true;
  }

  /**
   * ToDo: 20230214 弃用方法
   *
   * @param codeList
   * @param spIdList
   * @return
   */
  @Override
  public boolean deleteSeries(List<String> codeList, List<Long> spIdList) {
    boolean allSpRollback = true;
    //验证是否所有的sp都是回收成功或者未发布状态
    Long extendSpContentCount = bmsContentService.count(Wrappers.lambdaQuery(BmsContent.class)
        .notIn(BmsContent::getSpId, spIdList)
        .in(BmsContent::getCmsContentCode, codeList)
    );
    if (extendSpContentCount != null && extendSpContentCount > 0) {
      log.error("该Cp内容已授权给其他非自动发布SP且已经发布到运营商");
      allSpRollback = false;
//            throw new CommonResponseException(CommonResponseEnum.DELETE_ERROR);
    }
    if (allSpRollback) {
      log.info("满足全部sp回收条件，删除cp数据");
      bmsContentService.deletePicAndMov(codeList, ContentTypeEnum.TELEPLAY.getValue(),
          ContentTypeEnum.EPISODES.getValue());
      // 解除 源子集关系
      cmsProgramService.update(Wrappers.lambdaUpdate(CmsProgram.class)
          .set(CmsProgram::getSeriesCode, null)
          .set(CmsProgram::getSeriesId, null)
          .set(CmsProgram::getSeriesName, null)
          .in(CmsProgram::getSeriesCode, codeList)
      );
      // 删除 源媒资
      //  this.remove(Wrappers.lambdaQuery(CmsSeries.class).in(CmsSeries::getCode, codeList));
    }
    return true;
  }

  /**
   * 剧集新增
   *
   * @param cmsSeriesDto
   * @return
   */
  @Override
  @Transactional(rollbackFor = Exception.class)
  public CommonResponse news(CmsSeriesDto cmsSeriesDto) {

    CmsSeries cmsSeries = new CmsSeries();
    org.springframework.beans.BeanUtils.copyProperties(cmsSeriesDto, cmsSeries);
    cmsSeries.setProhibitStatus(ProhibitStatusEnum.NO.getValue());
    //cmsSeries.setCode(cmsSeriesDto.getCode());
    SysCp sysCp = sysCpServiceimpl.cpName(cmsSeriesDto.getCpId());
    if (ObjectUtil.isEmpty(sysCp)) {
      return CommonResponse.general(CommonResponseEnum.FAIL, "当前CP不存在或状态失效");
    }
    //20230220 vspCode直接从cp中取code值
    cmsSeries.setVspCode(sysCp.getCode());
    cmsSeries.setCpName(sysCp.getName());
    //判断是否只关联一个预览片
    if (preTypeStatus(cmsSeriesDto)) {
      return CommonResponse.general(CommonResponseEnum.FAIL, "只能关联一个预览片");
    }
    //查询当前信息是否跳过审核
    sysCpSkipCheck(cmsSeries);
    //注册字典 获取请求来源
    getRequestResource(cmsSeriesDto, cmsSeries);
//        setCpName(cmsSeriesDto, cmsSeries);
    setItem(cmsSeriesDto, cmsSeries);
    cmsSeries.setPreviewStatus(MovieStatusEnum.NotRelevancy.getValue());
    cmsSeries.setIsProhibit(IsProhibitEnum.ING.getValue());
    this.save(cmsSeries);
    //将图片保存进cms_picture表
    List<Long> picIds = cmsSavePic(cmsSeriesDto, cmsSeries);
    Integer type = getType(cmsSeries.getSeriesType());
    //预览片关联
    correlationMov(cmsSeriesDto.getPreviewIds(), cmsSeries, type);
    //20220913fix添加违禁检测
    if (!ProhibitSkipEnum.SKIP.getValue().equals(sysCp.getSkipProhibit())) {
      ProhibitPointEnum prohibitPointEnum = null;
      switch (ProhibitPointEnum.getByValue(sysCp.getPointProhibit())) {
        case INSERT:
          prohibitPointEnum = ProhibitPointEnum.INSERT;
          break;
        case EXAMINE:
          if (sysCp.getSkipCheck().equals(SkipCheckEnum.SelfSkip.getValue())
              || sysCp.getSkipCheck().equals(SkipCheckEnum.AllSkip.getValue())) {
            prohibitPointEnum = ProhibitPointEnum.EXAMINE;
          }
          break;
        default:
          break;
      }
      if (ObjectUtils.isNotEmpty(prohibitPointEnum)) {
        CmsSeriesVO cmsSeriesVO = new CmsSeriesVO(cmsSeries, prohibitPointEnum);
        CmsSeries cmsSeries1 = iRuleProhibitHelper.checkSeriesRuleProhibit(cmsSeriesVO);
        cmsSeries.setProhibitStatus(cmsSeries1.getProhibitStatus());
        cmsSeries.setIsProhibit(cmsSeries1.getIsProhibit());
        this.updateById(cmsSeries);
      }
    } else {
      cmsSeries.setProhibitStatus(ProhibitStatusEnum.NO.getValue());
      cmsSeries.setIsProhibit(IsProhibitEnum.SKIP.getValue());
      this.updateById(cmsSeries);
    }
    //判断是否开启自动授权
    List<SysAuthorization> sysAuthorizations = redisService.getCacheMapValue(
        RedisKeyConstants.ALL_SYS_AUTHORIZATION, String.valueOf(cmsSeries.getCpId()));
//        List<SysAuthorization> sysAuthorizations = sysAuthorizationMapper.selectList(Wrappers.<SysAuthorization>lambdaQuery().eq(SysAuthorization::getCpId, cmsSeries.getCpId()));
    if (ProhibitStatusEnum.NO.getValue().equals(cmsSeries.getProhibitStatus())
        && ObjectUtil.isNotEmpty(sysAuthorizations)) {
      SysInPassage sysInPassage = sysInPassageService.sysInPassage(cmsSeriesDto.getCspId());
      for (SysAuthorization sysAuthorization : sysAuthorizations) {
        BmsContent bmsContent = new BmsContent();
        bmsContent.setContentType(type);
        //判断是否开启自动授权
        if (sysAuthorization.getAutoAuthorize().equals(AutoAuthorizeEnum.Yes.getValue())) {
          //将剧集内容写入bms_content表中
          saveBms(cmsSeries, sysAuthorization, bmsContent);
          if (ObjectUtil.isNotEmpty(sysInPassage)) {
            if (ObjectUtil.isEmpty(sysInPassage.getSpPublishType())) {
              bmsContent.setPublishStatus(PublishStatusEnum.WAITPUBLISH.getCode());
            } else if (sysInPassage.getSpPublishType()
                .equals(SpPublishTypeEnums.NOT_RELEASE.getValue())) {
              bmsContent.setPublishStatus(PublishStatusEnum.WAITPUBLISH.getCode());
            } else if (sysInPassage.getSpPublishType()
                .equals(SpPublishTypeEnums.RELEASE_SUCCESS.getValue())) {
              bmsContent.setPublishStatus(PublishStatusEnum.PUBLISH.getCode());
              bmsContent.setPublishDescription("系统配置,自动发布");
            }
          }
          bmsContentService.save(bmsContent);
          //保存图片
          cmsProgramService.bmsSavePic(picIds, bmsContent, type);
        }
      }
    }
    return CommonResponse.general(CommonResponseEnum.SUCCESS);

  }


  /**
   * 剧集删除
   *
   * @param ids
   * @return
   */
  @Override
  @Transactional(rollbackFor = Exception.class)
  public CommonResponse del(String ids) {
    try {
      //参数、媒资是否存在校验
      CommonResponse<Object> FAIL = getResponse(ids);
      if (FAIL != null) {
        return FAIL;
      }
      String[] idArray = ids.split(",");
      List<Long> idList = CmsProgramServiceImpl.getIdList(idArray);
      RuleResult ruleResult = RuleUtil.checkLockStatus(CmsSeries.class, idList);
      if (!ruleResult.isPass()) {
        throw new CommonResponseException(ruleResult.getMsg());
      }

      List<BmsContent> bmsContentList = bmsContentService.listBySeriesIds(idList);
      if (CollectionUtil.isNotEmpty(bmsContentList)) {
        if (bmsContentList.stream().<SysSp>map(
                bmsContent -> redisService.getCacheMapValue(RedisKeyConstants.SYS_SP,
                    String.valueOf(bmsContent.getSpId())))
            .anyMatch(cacheSp -> cacheSp != null && StatusEnum.COME.getCode()
                .equals(cacheSp.getStatus()))) {
          return CommonResponse.fail("已授权到SP侧，无法删除！");
        }
      }

      for (String cmsid : idArray) {
        CmsSeries cmsSeries = this.getById(cmsid);
        //删除子集逻辑： 剧集没有授权到sp侧 所以子集也不会授权 不需要判断子集的发布状态以及移除sp侧的问题 只需要删除cp侧的子集
        List<CmsProgram> cmsPrograms = cmsProgramService.list(
            Wrappers.<CmsProgram>lambdaQuery().eq(CmsProgram::getSeriesId, cmsid));
        if (ObjectUtil.isNotEmpty(cmsPrograms)) {
          for (CmsProgram cmsProgram : cmsPrograms) {
            cmsProgramService.update(null, Wrappers.<CmsProgram>lambdaUpdate()
                .set(CmsProgram::getSeriesId, null)
                .set(CmsProgram::getSeriesCode, null)
                .set(CmsProgram::getSeriesName, null)
                .eq(CmsProgram::getId, cmsProgram.getId())
                .eq(CmsProgram::getSeriesId, cmsProgram.getSeriesId()));
          }
        }
        //删除图片 删除视频
        if (SeriesFlagEnum.SeriesFilm.getValue().equals(cmsSeries.getSeriesType())) {
          cmsProgramService.picAndMovDel(cmsid, ContentTypeEnum.EPISODES.getValue());
        } else {
          cmsProgramService.picAndMovDel(cmsid, ContentTypeEnum.TELEPLAY.getValue());
        }
        this.removeById(cmsid);
      }
/*
        //删除剧头的图片以及解绑视频关联
        cmsPictureService.remove(Wrappers.<CmsPicture>lambdaQuery()
                .in(CmsPicture::getContentId, idArray)
                .in(CmsPicture::getContentType, ContentTypeEnum.TELEPLAY.getValue(), ContentTypeEnum.EPISODES.getValue()));

        cmsResourceService.update(Wrappers.<CmsResource>lambdaUpdate()
                .set(CmsResource::getContentId, null)
                .set(CmsResource::getContentCode, null)
                .set(CmsResource::getContentName, null)
                .set(CmsResource::getContentType, null)
                .set(CmsResource::getContentStatus, MediaAssociationStatusEnum.AwaitRelevancy.getValue())
                .in(CmsResource::getContentId, idArray)
                .in(CmsResource::getContentType, ContentTypeEnum.TELEPLAY.getValue(), ContentTypeEnum.EPISODES.getValue())

        );*/
      return CommonResponse.general(CommonResponseEnum.SUCCESS);
    } catch (Exception e) {
      log.error("剧集删除失败,ids:{},错误信息:{}", ids, e.getMessage());
      return CommonResponse.fail("剧集删除失败");
    }
  }


  /**
   * 批量锁定解锁
   *
   * @param ids
   * @return
   */
  @Override
  @Transactional(rollbackFor = Exception.class)
  public CommonResponse batchLock(String ids, String status) {
    if (ObjectUtil.isEmpty(ids) || ObjectUtil.isEmpty(status)) {
      throw new CommonResponseException("参数不能为空");
    }
    if (!exist(ids)) {
      return CommonResponse.general(CommonResponseEnum.FAIL, "媒资不存在,请刷新页面重试");
    }
    String[] idArray = ids.split(",");
    this.update(null, Wrappers.<CmsSeries>lambdaUpdate()
        .set(status.equals("2"), CmsSeries::getLockStatus, LockStatusEnum.LOCK.getCode())
        .set(status.equals("1"), CmsSeries::getLockStatus, LockStatusEnum.NOT_LOCK.getCode())
        .in(CmsSeries::getId, idArray)
    );
    return CommonResponse.general(CommonResponseEnum.SUCCESS);
  }

  @Override
  public void test() {
    missedXxlJob();
  }

  @Override
  @Transactional(rollbackFor = Exception.class)
  public CommonResponse<Boolean> relateRelease(Integer relateCount) {
    List<ContentResourceDto> previewList = cmsSeriesMapper.getPreviewList(
        relateCount,
        MovieStatusEnum.NotRelevancy.getValue(),
        MovieStatusEnum.NotRelevancy.getValue());
    /** 关联结果描述信息，无异常时为空 */
    String message = "";
    if (previewList.isEmpty()) {
      message = "====待关联Series的 预览片 为空====";
      log.warn(message);
      return CommonResponse.success(true);
    }
    ArrayList<Long> idList = new ArrayList<>();
    try {

      /**
       * 准备需要更新的movieList和resourceList
       */
      previewList.forEach(contentResourceDto -> {
        contentResourceDto.setReleaseStatus(MovieStatusEnum.Relevancy.getValue());
        idList.add(contentResourceDto.getContentId());
      });

      /** 更新 Series关联正片状态 */
      update(Wrappers.lambdaUpdate(CmsSeries.class)
          .set(CmsSeries::getPreviewStatus, MovieStatusEnum.Relevancy.getValue())
          .in(CmsSeries::getId, idList));
      int result = cmsResourceMapper.updateBatchById(previewList);
      if (result > 0) {
        cmsMovieMapper.updateBatchByResourceId(previewList);
      }
    } catch (Exception e) {
      message = "===更新Series、Resource、Movie关联信息异常ProgramIDs = " + idList;
      log.error("===更新Series、Resource、Movie关联信息异常ProgramIDs = {}, 错误信息： {}", idList,
          e.fillInStackTrace());
      return CommonResponse.commonfail(message);
    }
    return CommonResponse.success(true);
  }

  @Override
  public boolean check(Long id, List<Integer> episodeIndexs) {
    // 判断集数为0和null的情况
    List<Integer> wrongEpisodeIndexs = episodeIndexs.stream()
        .filter(episodeIndex -> episodeIndex == null || episodeIndex == 0)
        .collect(Collectors.toList());
    if (ObjectUtils.isNotEmpty(wrongEpisodeIndexs)) {
      throw new CommonResponseException("绑定子集的集数为null或者0");
    }
    // 判断集数为0和null的情况
    List<Integer> negativeWrongEpisodeIndexs = episodeIndexs.stream()
        .filter(episodeIndex -> episodeIndex < 0)
        .collect(Collectors.toList());
    if (ObjectUtils.isNotEmpty(negativeWrongEpisodeIndexs)) {
      throw new CommonResponseException("绑定子集的集数小于0");
    }
    //1.判断绑定时勾选的子集集数是否重复
    long count = episodeIndexs.stream().distinct().count();
    if (episodeIndexs.size() != count) {
      throw new CommonResponseException("请勿绑定相同集数的子集");
    }
    //2.判断要绑定的子集集数是否已存在
    List<CmsProgram> cmsPrograms = cmsProgramService.list(Wrappers.<CmsProgram>lambdaQuery()
        .select(CmsProgram::getEpisodeIndex)
        .eq(CmsProgram::getSeriesId, id)
        .in(CmsProgram::getEpisodeIndex, episodeIndexs)
    );
    if (ObjectUtil.isNotEmpty(cmsPrograms)) {
      throw new CommonResponseException("该子集集数已存在,请勿重复绑定");
    }
    //3.判断子集集数是否超过剧集总集数
    CmsSeries cmsSeries = this.getOne(Wrappers
        .lambdaQuery(CmsSeries.class)
        .select(CmsSeries::getVolumnCount, CmsSeries::getVolumnUpdate)
        .eq(CmsSeries::getId, id));
    if (cmsSeries.getVolumnCount() < cmsSeries.getVolumnUpdate() + episodeIndexs.size()) {
      throw new CommonResponseException("绑定的子集数量不能大于剧集的总集数");
    }
    // 4.判断绑定子集的集数是否超过剧集总集数
    for (Integer episodeIndex : episodeIndexs) {
      if (episodeIndex > cmsSeries.getVolumnCount()) {
        throw new CommonResponseException("绑定的集数不能大于剧集的总集数");
      }
    }
    return true;
  }

  /**
   * 删除编目+子集+介质
   *
   * @param ids
   * @return
   */
  @Override
  public CommonResponse deleteMov(String ids) {
    long time = System.currentTimeMillis();
    // 剧集id
    List<Long> idList = Arrays.stream(ids.split(","))
        .map(s -> Long.parseLong(s.trim())).collect(Collectors.toList());

    // 1. 前置状态检查
    List<CmsSeries> cmsSeriesList = this.listByIds(idList);
    if (CollectionUtil.isEmpty(cmsSeriesList)) {
      return CommonResponse.fail("剧集不存在.");
    }
    for (CmsSeries cmsSeries : cmsSeriesList) {
      if (LockStatusEnum.LOCK.getCode().equals(cmsSeries.getLockStatus())) {
        return CommonResponse.fail("剧集已锁定.：" + cmsSeries.getName());
      }
    }

    List<CmsProgram> cmsProgramList = cmsProgramService.listBySeriesIds(idList);
    for (CmsProgram cmsProgram : cmsProgramList) {
      if (LockStatusEnum.LOCK.getCode().equals(cmsProgram.getLockStatus())) {
        return CommonResponse.fail("子集已锁定.：" + cmsProgram.getId());
      }
    }

    List<BmsContent> bmsContentList = bmsContentService.listBySeriesIds(idList);
    if (CollectionUtil.isNotEmpty(bmsContentList)) {
      if (bmsContentList.stream().<SysSp>map(
              bmsContent -> redisService.getCacheMapValue(RedisKeyConstants.SYS_SP,
                  String.valueOf(bmsContent.getSpId())))
          .anyMatch(cacheSp -> cacheSp != null && StatusEnum.COME.getCode()
              .equals(cacheSp.getStatus()))) {
        return CommonResponse.fail("已授权到SP侧，无法删除！");
      }
    }
    long time1 = System.currentTimeMillis();
    log.info("状态检查耗时：{} ms", time1 - time);

    // 要删除的ftp介质 先缓存
    Set<FileUrlAndStorageId> filePath = new HashSet<>();

    // 2.ftp删除成功再从数据库中删除 这里加事务
    deleteSeriesFromDB(cmsSeriesList, cmsProgramList, filePath);

    long time2 = System.currentTimeMillis();
    log.info("数据库删除耗时：{} ms", time2 - time1);
    // 3. 删除ftp介质
    //deleteFtpData(filePath);
    long time3 = System.currentTimeMillis();
    log.info("介质删除耗时：{} ms", time3 - time2);

    //重新统计锚点
    StatisticsInDelete statisticsInDelete = new StatisticsInDelete();
    statisticsInDelete.setType(StatisticsTypeEnum.Subset.getValue());
    statisticsInDelete.setStaticticDate(DateUtils.getNowString());
    statisticsInDeleteService.sendMessage(statisticsInDelete);
    return CommonResponse.general(CommonResponseEnum.SUCCESS);
  }

  private void deleteFtpData(Set<FileUrlAndStorageId> filePathList) {
    for (FileUrlAndStorageId fileUrl : filePathList) {
      try {
        CommonResponse<String> response = downloadFeignClient.fileDelete(fileUrl.getFileUrl(),
            fileUrl.getStorageId());
        if (!TextConstants.OK.equals(response.getCode())) {
          log.warn("介质删除失败, {}, response msg: {}", fileUrl.toString(), response.getMessage());
        }
      } catch (Exception e) {
        log.error("介质删除失败, {}", fileUrl.toString(), e);
      }
    }
  }

  @Override
  public void deleteByIds(List<Long> seriesIdList) {
    if (CollectionUtil.isEmpty(seriesIdList)) {
      return;
    }
    LambdaQueryWrapper<CmsSeries> query = Wrappers.lambdaQuery();
    query.in(CmsSeries::getId, seriesIdList);
    remove(query);
  }

  @Autowired
  private ExportTask<CmsSeries> exportTask;

  @Override
  public Object exportByIds(String ids) {
    String[] idList = ids.split(",");
    LambdaQueryWrapper<CmsSeries> lambdaQueryWrapper = new LambdaQueryWrapper();
    lambdaQueryWrapper.in(CmsSeries::getId, idList);
    long ms = System.currentTimeMillis();
    ExportInfo<CmsSeries> exportInfo = new ExportInfo<>();
    exportInfo.setOut(new ByteArrayOutputStream())
        .setSheetName("剧集")
        .setQueryWrapper(lambdaQueryWrapper)
        .setPojoClass(CmsSeries.class)
        .setCacheKey(RedisKeyConstants.SERIES_EXPORT)
        .setBaseMapper(cmsSeriesMapper);
    ExcelTaskInfo excelTaskInfo = exportTask.startProcess(exportInfo).dowork();
    log.info("总导出时长为 {} ms", (System.currentTimeMillis() - ms));
    return JSON.toJSON(excelTaskInfo);
  }

  @Override
  public CommonResponse<List<StatisticsIn>> getSeriesStatistic(StatisticsInVo statisticsInVo) {
    List<StatisticsIn> statisticsInsList = cmsSeriesMapper.getSeriesStatistics(statisticsInVo);
    statisticsInsList.stream().forEach(e -> {
      e.setStatisticDate(statisticsInVo.getStartTime());
      e.setType(StatisticsTypeEnum.Series.getValue());
    });
    return CommonResponse.success(statisticsInsList);
  }

  @Override
  public CommonResponse<List<StatisticsInCheck>> getSelfSeriesStatistic(
      StatisticsInVo statisticsInVo) {
    List<StatisticsInCheck> statisticsInCheckList = cmsSeriesMapper.getSelfSeriesStatisticsAndSelf(
        statisticsInVo);
    statisticsInCheckList.stream().forEach(e -> {
      e.setStatisticDate(statisticsInVo.getStartTime());
      e.setType(StatisticsTypeEnum.Series.getValue());
    });
    return CommonResponse.success(statisticsInCheckList);
  }

  @Override
  public CommonResponse<List<StatisticsInFinal>> getFinalSeriesStatistic(
      StatisticsInVo statisticsInVo) {
    List<StatisticsInFinal> statisticsInFinalList = cmsSeriesMapper.getFinalSeriesStatistics(
        statisticsInVo);
    statisticsInFinalList.stream().forEach(e -> {
      e.setStatisticDate(statisticsInVo.getStartTime());
      e.setType(StatisticsTypeEnum.Series.getValue());
    });
    return CommonResponse.success(statisticsInFinalList);
  }

  @Override
  public CommonResponse<List<StatisticsInAgain>> getAgainSeriesStatistic(
      StatisticsInVo statisticsInVo) {
    List<StatisticsInAgain> statisticsInAgainList = cmsSeriesMapper.getAgainSeriesStatistics(
        statisticsInVo);
    statisticsInAgainList.stream().forEach(e -> {
      e.setStatisticDate(statisticsInVo.getStartTime());
      e.setType(StatisticsTypeEnum.Series.getValue());
    });
    return CommonResponse.success(statisticsInAgainList);
  }

  @Async
  @Override
  public void updateProhibitStatusByCode(List<String> codes, int isProhibit, int prohibitStatus,
      int lock) {
    cmsSeriesMapper.update(null, Wrappers.<CmsSeries>lambdaUpdate()
        .set(CmsSeries::getIsProhibit, isProhibit).set(CmsSeries::getProhibitStatus, prohibitStatus)
        .set(CmsSeries::getLockStatus, lock)
        .in(CollectionUtils.isNotEmpty(codes), CmsSeries::getCode, codes));
  }

  @Override
  public Long getIdByCode(@Max(value = 32, message = "Code不能超过32位") String code) {
    CmsSeries cmsSeries = cmsSeriesMapper.selectOne(Wrappers.<CmsSeries>lambdaQuery()
        .select(CmsSeries.class, i -> i.getProperty().equals("id")).eq(CmsSeries::getCode, code));
    if (null != cmsSeries) {
      return cmsSeries.getId();
    }
    return null;
  }

  @Override
  public List<CmsSeries> listByCode(Collection<String> cmsContentCode) {
    LambdaQueryWrapper<CmsSeries> query = Wrappers.lambdaQuery();
    query.in(CmsSeries::getCode, cmsContentCode)
            .notIn(CmsSeries::getPgmCategory, Arrays.asList("新闻","农业","财经","体育","生活"));
    return list(query);
  }

  @Override
  public CommonResponse checkVolumnCount(CmsSeriesDto cmsSeriesDto) {
    CmsSeries cmsSeries2 = cmsSeriesMapper.selectById(cmsSeriesDto.getId());
    // 判断总集数是否修改
    if (ObjectUtils.isNotEmpty(cmsSeriesDto.getVolumnCount())) {
      if (!cmsSeriesDto.getVolumnCount().equals(cmsSeries2.getVolumnCount())) {
        if (cmsSeriesDto.getVolumnCount() < cmsSeriesDto.getVolumnUpdate()) {
          return CommonResponse.general(CommonResponseEnum.FAIL, "修改之后总集数小于实际集数");
        }
        List<CmsProgram> cmsProgramList = cmsProgramService.list(Wrappers.<CmsProgram>lambdaQuery()
            .select(CmsProgram::getEpisodeIndex)
            .eq(CmsProgram::getSeriesId, cmsSeriesDto.getId()));
        List<CmsProgram> wrongCmsProgramList = cmsProgramList.stream()
            .filter(cmsProgram -> ObjectUtils.isNotEmpty(cmsProgram.getEpisodeIndex()))
            .filter(cmsProgram -> cmsProgram.getEpisodeIndex() > cmsSeriesDto.getVolumnCount())
            .collect(Collectors.toList());
        if (ObjectUtils.isNotEmpty(wrongCmsProgramList)) {
          return CommonResponse.general(CommonResponseEnum.FAIL, "修改之后总集数小于子集集数");
        }
      }
    }
    return CommonResponse.success("true");
  }


  /**
   * 根据给定的条件获取节目列表的分页结果。
   *
   * @param cmsProgramDO 包含分页信息和筛选条件的数据对象。
   * @return 返回包含满足条件的节目列表的分页对象。
   * @throws CommonResponseException 如果必要的参数为空，则抛出此异常。
   *
   */
  @Override
  public Page<CmsProgram> getProgramListBySeries(CmsProgramDO cmsProgramDO) {
    int current = cmsProgramDO.getCurrent();
    int size = cmsProgramDO.getSize();
    if (current < 1) {
      current = 1;
    }
    if (size < 1 || size > 500) {
      size = 500;
    }
    if (ObjectUtil.isEmpty(cmsProgramDO.getSeriesId())) {
      throw new CommonResponseException("剧集参数不能为空");
    }
    Page iPage = new Page(current, size);
    LambdaQueryWrapper<CmsProgram> wrapper =  Wrappers.lambdaQuery(CmsProgram.class)
            .eq(CmsProgram::getSeriesId, cmsProgramDO.getSeriesId())
            .eq(ObjectUtils.isNotEmpty(cmsProgramDO.getAuditStatus()), CmsProgram::getAuditStatus, cmsProgramDO.getAuditStatus())
            .eq(ObjectUtils.isNotEmpty(cmsProgramDO.getCode()), CmsProgram::getCode, cmsProgramDO.getCode())
            .orderByDesc(CmsProgram::getEpisodeIndex);
    Page contentPage = cmsProgramService.page(iPage, wrapper);
    return contentPage;
  }


  /**
   * @param cmsSeriesList
   * @param cmsProgramList
   * @param filePath
   */
  @Transactional(rollbackFor = Exception.class)
  public void deleteSeriesFromDB(List<CmsSeries> cmsSeriesList, List<CmsProgram> cmsProgramList,
      Set<FileUrlAndStorageId> filePath) {
    // 预览片code集合
    List<String> previewCodeList = cmsSeriesList.stream().map(CmsSeries::getResourcePreviewCode)
        .filter(StringUtils::isNotEmpty).collect(Collectors.toList());
    // 正片code集合
    List<String> releaseCodeList = cmsProgramList.stream().map(CmsProgram::getResourceReleaseCode)
        .filter(StringUtils::isNotEmpty).collect(Collectors.toList());
    // code合并
    previewCodeList.addAll(releaseCodeList);

    // 查询视频数据
    List<CmsResource> cmsResourceList = cmsResourceService.listByCode(previewCodeList);
    List<Long> resourceIdList = cmsResourceList.stream().map(CmsResource::getId)
        .collect(Collectors.toList());
    List<CmsMovie> cmsMovieList = cmsMovieService.listByResourceId(resourceIdList);

    // 查询图片数据
    List<Long> seriesIdList = cmsSeriesList.stream().map(CmsSeries::getId).filter(Objects::nonNull)
        .collect(Collectors.toList());
    List<Long> programIdList = cmsProgramList.stream().map(CmsProgram::getId)
        .filter(Objects::nonNull).collect(Collectors.toList());
    List<CmsPicture> seriesPictureList = cmsPictureService.listBySeriesId(seriesIdList);
    List<CmsPicture> programPictureList = cmsPictureService.listByProgramId(programIdList);

    // 准备删除操作所需id
    Set<Long> seriesPictureIds = seriesPictureList.stream().map(CmsPicture::getId)
        .filter(Objects::nonNull).collect(Collectors.toSet());
    Set<Long> programPictureIds = programPictureList.stream().map(CmsPicture::getId)
        .filter(Objects::nonNull).collect(Collectors.toSet());
    seriesPictureIds.addAll(programPictureIds);
    List<Long> cmsMovieIds = cmsMovieList.stream().map(CmsMovie::getId)
        .collect(Collectors.toList());

    // 准备图片code
    Set<String> seriesPictureCodes = seriesPictureList.stream().map(CmsPicture::getCode)
        .filter(StringUtils::isNotEmpty).collect(Collectors.toSet());
    Set<String> programPictureCodes = programPictureList.stream().map(CmsPicture::getCode)
        .filter(StringUtils::isNotEmpty).collect(Collectors.toSet());
    seriesPictureCodes.addAll(programPictureCodes);

    // StorageId == -1 表示透传下来 介质不在我们的播控平台，所以不用删除
    // 缓存需要删除的介质的地址
    Set<FileUrlAndStorageId> resourceFileUrlSet = cmsResourceList.stream()
        .filter(
            cmsResource -> cmsResource.getFileUrl() != null && cmsResource.getStorageId() != null
                && cmsResource.getStorageId() != -1L)
        .map(cmsResource -> new FileUrlAndStorageId(cmsResource.getFileUrl(),
            cmsResource.getStorageId())).collect(Collectors.toSet());
    filePath.addAll(resourceFileUrlSet);
    Set<FileUrlAndStorageId> movieFileUrlSet = cmsMovieList.stream()
        .filter(cmsMovie -> cmsMovie.getFileUrl() != null && cmsMovie.getStorageId() != null
            && cmsMovie.getStorageId() != -1L)
        .map(cmsMovie -> new FileUrlAndStorageId(cmsMovie.getFileUrl(), cmsMovie.getStorageId()))
        .collect(Collectors.toSet());
    filePath.addAll(movieFileUrlSet);
    /**
     // 统计Picture表中同一code存在多条数据的code
     Set<String> notDelSet = cmsPictureService.countByCode(seriesPictureCodes);
     **/
    // 若notDelSet存在 则说明该介质图片还管理了其他媒资 不用删除介质
    Set<FileUrlAndStorageId> seriesPictureFileUrlSet = seriesPictureList.stream()
        .filter(cmsPicture -> StringUtils.isNotEmpty(cmsPicture.getFileUrl())
            && cmsPicture.getStorageId() != null)
        .map(cmsPicture -> new FileUrlAndStorageId(cmsPicture.getFileUrl(),
            cmsPicture.getStorageId())).collect(Collectors.toSet());
    filePath.addAll(seriesPictureFileUrlSet);
    Set<FileUrlAndStorageId> programPictureFileUrlSet = programPictureList.stream()
        .filter(cmsPicture -> StringUtils.isNotEmpty(cmsPicture.getFileUrl())
            && cmsPicture.getStorageId() != null)
        .map(cmsPicture -> new FileUrlAndStorageId(cmsPicture.getFileUrl(),
            cmsPicture.getStorageId())).collect(Collectors.toSet());
    filePath.addAll(programPictureFileUrlSet);

    // 删除介质
    if (!filePath.isEmpty()) {
      try {
        filePath.stream().map(FileUrlAndStorageId::getFileUrl).forEach(FtpAnalysisUtil::DelFtp);
      } catch (Exception exception) {
        log.error("删除ftp文件失败", exception);
        throw new BizException("删除ftp文件失败");
      }
    }
    // 删除剧集
    this.deleteByIds(seriesIdList);
    // 删除子集
    cmsProgramService.deleteByIds(programIdList);
    bmsProgramService.deleteByCmsContentIds(programIdList);

    // 删除视频 包括预览片，正片
    cmsResourceService.deleteByIds(resourceIdList);
    cmsMovieService.deleteByIds(cmsMovieIds);

    // 删除图片
    cmsPictureService.deleteByIds(seriesPictureIds);
    bmsPictureService.deleteByCmsPictureIds(seriesPictureIds);
  }

  /**
   * 批量元数据修改
   *
   * @param cmsSeriesDto
   * @return
   */
  @Override
  @Transactional(rollbackFor = Exception.class)
  public CommonResponse updateAll(CmsSeriesDto cmsSeriesDto) {
    //参数、媒资是否存在校验
    CommonResponse<Object> FAIL = getResponse(cmsSeriesDto.getIds());
    if (FAIL != null) {
      return FAIL;
    }
    //获取批量修改得ids
    String[] idArray = cmsSeriesDto.getIds().split(",");

    List<Long> idList = new ArrayList<>();
    Arrays.stream(idArray).forEach(p -> idList.add(Long.parseLong(p)));

    LambdaQueryWrapper<BmsContent> eq = Wrappers.lambdaQuery(BmsContent.class)
        .select(BmsContent::getName, BmsContent::getSpName)
        .in(BmsContent::getCmsContentId, idList)
        .in(BmsContent::getContentType, ContentTypeEnum.TELEPLAY.getValue(),
            ContentTypeEnum.EPISODES.getValue())
        .in(BmsContent::getPublishStatus, PublishStatusEnum.PUBLISHING.getCode(),
            PublishStatusEnum.ROLLBACKING.getCode(), PublishStatusEnum.UPDATING.getCode())
        .last(" limit 1 ");
    BmsContent badData = bmsContentService.getOne(eq);
    if (ObjectUtil.isNotEmpty(badData)) {
      throw new BizException(
          badData.getName() + "spname:" + badData.getSpName() + "是发布中的状态不能修改");
    }
    //发布中，更新中，回收中检查
    RuleResult rr = RuleCondition.create()
        .and(LockStatusRule.init(CmsSeries.class).data(idList))
        .execute();
    if (!rr.isPass()) {
      throw new BizException(rr.getMsg());
    }
    setItem(cmsSeriesDto);
    List<CmsSeries> cmsSeriesList = this.list(
        Wrappers.<CmsSeries>lambdaQuery().in(CmsSeries::getId, idArray));
    cmsSeriesList.forEach(p -> {
      CmsSeries cmsSeries = new CmsSeries();
//            org.springframework.beans.BeanUtils.copyProperties(p, cmsSeries);
      CopyUtil.copyPropertiesIgnoreNull(cmsSeriesDto, cmsSeries);
      SysCp sysCp = sysCpServiceimpl.cpName(p.getCpId());
      if (ObjectUtil.isNotEmpty(sysCp)) {
        if (sysCp.getSkipCheck().equals(SkipCheckEnum.NoSkip.getValue())) {
          cmsSeries.setCpCheckStatus(CpCheckStatusEnum.Await.getValue());
          cmsSeries.setOpCheckStatus(OpCheckStatusEnum.Await.getValue());
        }
        if (sysCp.getSkipCheck().equals(SkipCheckEnum.SelfSkip.getValue())) {
          cmsSeries.setOpCheckStatus(OpCheckStatusEnum.Await.getValue());
        }
        if (sysCp.getSkipCheck().equals(SkipCheckEnum.EndSkip.getValue())) {
          cmsSeries.setCpCheckStatus(CpCheckStatusEnum.Await.getValue());
        }
      }
      if (ObjectUtil.isEmpty(cmsSeriesDto.getDefinitionFlag())) {
        cmsSeries.setDefinitionFlag(p.getDefinitionFlag());
      }
      this.update(cmsSeries, Wrappers.<CmsSeries>lambdaUpdate().eq(CmsSeries::getId, p.getId()));

      bmsContentService.update(null, Wrappers.<BmsContent>lambdaUpdate()
          .set(ObjectUtil.isNotEmpty(cmsSeries.getPgmCategory()), BmsContent::getPgmCategory,
              cmsSeries.getPgmCategory())
          .set(ObjectUtil.isNotEmpty(cmsSeries.getPgmCategoryId()), BmsContent::getPgmCategoryId,
              cmsSeries.getPgmCategoryId())
          .set(ObjectUtil.isNotEmpty(cmsSeries.getPgmSndClass()), BmsContent::getPgmSndClass,
              cmsSeries.getPgmSndClass())
          .set(ObjectUtil.isNotEmpty(cmsSeries.getPgmSndClassId()), BmsContent::getPgmSndClassId,
              cmsSeries.getPgmSndClassId())
          .set(ObjectUtil.isNotEmpty(cmsSeries.getLicensingWindowStart()),
              BmsContent::getLicensingWindowStart, cmsSeries.getLicensingWindowStart())
          .set(ObjectUtil.isNotEmpty(cmsSeries.getLicensingWindowEnd()),
              BmsContent::getLicensingWindowEnd, cmsSeries.getLicensingWindowEnd())
          .set(ObjectUtil.isNotEmpty(cmsSeries.getCpCheckStatus()), BmsContent::getCpCheckStatus,
              cmsSeries.getCpCheckStatus())
          .set(ObjectUtil.isNotEmpty(cmsSeries.getOpCheckStatus()), BmsContent::getOpCheckStatus,
              cmsSeries.getOpCheckStatus())
          .set(ObjectUtil.isNotEmpty(cmsSeries.getDefinitionFlag()), BmsContent::getDefinitionFlag,
              cmsSeries.getDefinitionFlag())
          .eq(BmsContent::getCmsContentId, p.getId())
          .in(BmsContent::getContentType, ContentTypeEnum.TELEPLAY.getValue(),
              ContentTypeEnum.EPISODES.getValue())
      );
    });

    bmsContentService.update(null, Wrappers.<BmsContent>lambdaUpdate()
        .set(BmsContent::getPublishStatus, PublishStatusEnum.WAITUPDATE.getCode())
        .in(BmsContent::getContentType, ContentTypeEnum.TELEPLAY.getValue(),
            ContentTypeEnum.EPISODES.getValue())
        .in(BmsContent::getCmsContentId, idArray)
        //发布成功 回收失败 更新失败 需要更新为待更新
        .in(BmsContent::getPublishStatus,
            PublishStatusEnum.PUBLISH.getCode(),
            PublishStatusEnum.FAILROLLBACK.getCode(),
            PublishStatusEnum.FAILUPDATE.getCode()));
    //        3、发布失败时修改媒资/栏目/产品包信息，此时状态变成待发布。
    bmsContentService.update(null, Wrappers.<BmsContent>lambdaUpdate()
        .set(BmsContent::getPublishStatus, PublishStatusEnum.WAITPUBLISH.getCode())
        .in(BmsContent::getCmsContentId, idArray)
        .in(BmsContent::getContentType, ContentTypeEnum.TELEPLAY.getValue(),
            ContentTypeEnum.EPISODES.getValue())
        .eq(BmsContent::getPublishStatus, PublishStatusEnum.FAILPUBLISH.getCode())
    );
    return CommonResponse.general(CommonResponseEnum.SUCCESS);
  }

  /**
   * 根据ids查询
   *
   * @param ids
   * @return
   */
  @Override
  public List<CmsSeries> selectByIds(String ids) {
    if (ids == null) {
      throw new CommonResponseException("参数不能为空");
    }
    List<CmsSeries> cmsSeries = new ArrayList<>();
    String[] idArray = ids.split(",");
    for (String id : idArray) {
      CmsSeries cmsProgram = this.getById(id);
      cmsSeries.add(cmsProgram);
    }
    return cmsSeries;
  }

  /**
   * 剧集编辑
   *
   * @param cmsSeriesDto
   * @return
   */
  @Override
  @Transactional(rollbackFor = Exception.class)
  public CommonResponse up(CmsSeriesDto cmsSeriesDto) {
    //参数、锁定状态、媒资是否存在校验
    String id = cmsSeriesDto.getId().toString();
    CommonResponse<Object> FAIL = getResponse(id);
    if (FAIL != null) {
      return FAIL;
    }
    //20230220 vspCode直接从cp中取code值
    SysCp sysCp = sysCpServiceimpl.cpName(cmsSeriesDto.getCpId());
    if (ObjectUtil.isEmpty(sysCp)) {
      return CommonResponse.general(CommonResponseEnum.FAIL, "当前CP不存在或状态失效");
    }
    cmsSeriesDto.setVspCode(sysCp.getCode());
    //发布状态检查
    cmsChannelServiceImpl.check(ContentTypeEnum.TELEPLAY.getValue(), cmsSeriesDto.getId());
    CmsSeries cmsSeries2 = cmsSeriesMapper.selectById(cmsSeriesDto.getId());

    //判断是否只关联一个预览片
    if (preTypeStatus(cmsSeriesDto)) {
      return CommonResponse.general(CommonResponseEnum.FAIL, "只能关联一个预览片");
    }

    CmsSeries cmsSeries = new CmsSeries();
    upCpStatus(cmsSeriesDto);
    org.springframework.beans.BeanUtils.copyProperties(cmsSeriesDto, cmsSeries);
    cmsSeries.setResourcePreviewCode(null);
    cmsSeries.setPreviewStatus(null);
//        CopyConfig.copyPropertiesIgnoreNull(cmsSeriesDto,cmsSeries);
    if (cmsSeriesDto.getSource().equals(SourceEnum.SYSWORK.getValue())) {
      cmsSeriesDto.setRequestResource(SourceEnum.SYSWORK.getValue());
    } else {
      cmsSeriesDto.setRequestResource(SourceEnum.MANUALWORK.getValue());
    }
//        cmsSeriesDto.setRequestResource(SourceEnum.SYSWORK.getValue());
    getRequestResource(cmsSeriesDto, cmsSeries);
    setItemUpdate(cmsSeriesDto, cmsSeries);
    //查询正片或预览片的视频关联状态 如果为未关联 清空视频code
    /*    if (MovieStatusEnum.NotRelevancy.getValue().equals(cmsSeriesDto.getPreviewStatus())) {
            cmsSeries.setResourcePreviewCode("");
        }*/
    this.updateById(cmsSeries);

    //保存图片
    List<Long> picIds = cmsSavePic(cmsSeriesDto, cmsSeries);

    Integer type = getType(cmsSeries.getSeriesType());

    Integer upType = getType(cmsSeriesDto.getUpType());
    //关联预览片
    correlationMov(cmsSeriesDto.getPreviewIds(), cmsSeries, type);

    List<BmsContent> bmsContents = bmsContentService.list(
        Wrappers.<BmsContent>lambdaQuery().eq(BmsContent::getCmsContentId, cmsSeries.getId())
            .eq(BmsContent::getContentType, upType));
    Integer finalType = type;
    bmsContents.stream().forEach(p -> {
      BeanUtils.copyProperties(cmsSeries, p, "id", "code", "createTime", "updateTime");
      p.setContentType(finalType);
      bmsContentService.updateById(p);
      cmsProgramService.bmsSavePic(picIds, p, finalType);
    });

    //针对已授权到SP侧并且媒资发布状态为【发布成功】时，其发布状态变更为【待更新】。
    bmsContentService.update(null, Wrappers.<BmsContent>lambdaUpdate()
        .set(BmsContent::getPublishStatus, PublishStatusEnum.WAITUPDATE.getCode())
        .eq(BmsContent::getCmsContentId, cmsSeriesDto.getId())
        .in(BmsContent::getContentType, ContentTypeEnum.TELEPLAY.getValue(),
            ContentTypeEnum.EPISODES.getValue())
        .in(BmsContent::getPublishStatus,
            PublishStatusEnum.PUBLISH.getCode(),
            PublishStatusEnum.FAILROLLBACK.getCode(),
            PublishStatusEnum.FAILUPDATE.getCode()));

    //        3、发布失败时修改媒资/栏目/产品包信息，此时状态变成待发布。
    bmsContentService.update(null, Wrappers.<BmsContent>lambdaUpdate()
        .set(BmsContent::getPublishStatus, PublishStatusEnum.WAITPUBLISH.getCode())
        .eq(BmsContent::getCmsContentId, cmsSeriesDto.getId())
        .in(BmsContent::getContentType, ContentTypeEnum.TELEPLAY.getValue(),
            ContentTypeEnum.EPISODES.getValue())
        .eq(BmsContent::getPublishStatus, PublishStatusEnum.FAILPUBLISH.getCode())
    );
    //
    //同步修改未授权子集,修改cms子集
    List<CmsProgram> cmsProgramList = cmsProgramService.list(
        Wrappers.<CmsProgram>query().eq("series_id", cmsSeries.getId()));
    List<CmsProgram> cmsProgramsListBatch = new ArrayList<>();
    cmsProgramList.forEach(p -> {
      CmsProgram cmsProgram = new CmsProgram();
//                org.springframework.beans.BeanUtils.copyProperties(cmsSeries, cmsProgram, "id", "code", "createTime", "updateTime");
      cmsProgram.setSeriesName(cmsSeries.getName());
      cmsProgram.setLicensingWindowEnd(cmsSeries.getLicensingWindowEnd());
      cmsProgram.setLicensingWindowStart(cmsSeries.getLicensingWindowStart());
      cmsProgram.setDefinitionFlag(cmsSeries.getDefinitionFlag());
      cmsProgram.setId(p.getId());
      cmsProgramsListBatch.add(cmsProgram);
    });
    cmsProgramService.updateBatchById(cmsProgramsListBatch);
    // 4、添加违禁检测
    CmsSeriesVO cmsSeriesVO = new CmsSeriesVO(cmsSeries, ProhibitPointEnum.UPDATE);
    CmsSeries cmsSeries1 = iRuleProhibitHelper.checkSeriesRuleProhibit(cmsSeriesVO);
    cmsSeries.setProhibitStatus(cmsSeries1.getProhibitStatus());
    cmsSeries.setIsProhibit(cmsSeries1.getIsProhibit());
    this.updateById(cmsSeries);

    //重新统计锚点
    StatisticsInDelete statisticsInDelete = new StatisticsInDelete();
    statisticsInDelete.setType(StatisticsTypeEnum.Series.getValue());
    statisticsInDelete.setStaticticDate(DateUtils.getNowString());
    statisticsInDeleteService.sendMessage(statisticsInDelete);

    //媒资改名埋点
    sendMQ(cmsSeries2, cmsSeriesDto);
    return CommonResponse.general(CommonResponseEnum.SUCCESS);
  }

  @Override
  @Transactional(rollbackFor = Exception.class)
  public CommonResponse upWithoutMov(CmsSeriesDto cmsSeriesDto) {
    //参数、锁定状态、媒资是否存在校验
    String id = cmsSeriesDto.getId().toString();
    CommonResponse<Object> FAIL = getResponse(id);
    if (FAIL != null) {
      return FAIL;
    }
    //20230220 vspCode直接从cp中取code值
    SysCp sysCp = sysCpServiceimpl.cpName(cmsSeriesDto.getCpId());
    if (ObjectUtil.isEmpty(sysCp)) {
      return CommonResponse.general(CommonResponseEnum.FAIL, "当前CP不存在或状态失效");
    }
    cmsSeriesDto.setVspCode(sysCp.getCode());
    //发布状态检查
    cmsChannelServiceImpl.check(ContentTypeEnum.TELEPLAY.getValue(), cmsSeriesDto.getId());
    CmsSeries cmsSeries2 = cmsSeriesMapper.selectById(cmsSeriesDto.getId());

    //判断是否只关联一个预览片
    if (preTypeStatus(cmsSeriesDto)) {
      return CommonResponse.general(CommonResponseEnum.FAIL, "只能关联一个预览片");
    }

    CmsSeries cmsSeries = new CmsSeries();
    upCpStatus(cmsSeriesDto);
    org.springframework.beans.BeanUtils.copyProperties(cmsSeriesDto, cmsSeries);
    cmsSeries.setResourcePreviewCode(null);
    cmsSeries.setPreviewStatus(null);
//        CopyConfig.copyPropertiesIgnoreNull(cmsSeriesDto,cmsSeries);
    if (cmsSeriesDto.getSource().equals(SourceEnum.SYSWORK.getValue())) {
      cmsSeriesDto.setRequestResource(SourceEnum.SYSWORK.getValue());
    } else {
      cmsSeriesDto.setRequestResource(SourceEnum.MANUALWORK.getValue());
    }
//        cmsSeriesDto.setRequestResource(SourceEnum.SYSWORK.getValue());
    getRequestResource(cmsSeriesDto, cmsSeries);
    setItemUpdate(cmsSeriesDto, cmsSeries);
    //查询正片或预览片的视频关联状态 如果为未关联 清空视频code
    /*    if (MovieStatusEnum.NotRelevancy.getValue().equals(cmsSeriesDto.getPreviewStatus())) {
            cmsSeries.setResourcePreviewCode("");
        }*/
    this.updateById(cmsSeries);

    //保存图片
    List<Long> picIds = cmsSavePic(cmsSeriesDto, cmsSeries);

    Integer type = getType(cmsSeries.getSeriesType());

    Integer upType = getType(cmsSeriesDto.getUpType());

    List<BmsContent> bmsContents = bmsContentService.list(
            Wrappers.<BmsContent>lambdaQuery().eq(BmsContent::getCmsContentId, cmsSeries.getId())
                    .eq(BmsContent::getContentType, upType));
    Integer finalType = type;
    bmsContents.stream().forEach(p -> {
      BeanUtils.copyProperties(cmsSeries, p, "id", "code", "createTime", "updateTime");
      p.setContentType(finalType);
      bmsContentService.updateById(p);
      cmsProgramService.bmsSavePic(picIds, p, finalType);
    });

    //针对已授权到SP侧并且媒资发布状态为【发布成功】时，其发布状态变更为【待更新】。
    bmsContentService.update(null, Wrappers.<BmsContent>lambdaUpdate()
            .set(BmsContent::getPublishStatus, PublishStatusEnum.WAITUPDATE.getCode())
            .eq(BmsContent::getCmsContentId, cmsSeriesDto.getId())
            .in(BmsContent::getContentType, ContentTypeEnum.TELEPLAY.getValue(),
                    ContentTypeEnum.EPISODES.getValue())
            .in(BmsContent::getPublishStatus,
                    PublishStatusEnum.PUBLISH.getCode(),
                    PublishStatusEnum.FAILROLLBACK.getCode(),
                    PublishStatusEnum.FAILUPDATE.getCode()));

    //        3、发布失败时修改媒资/栏目/产品包信息，此时状态变成待发布。
    bmsContentService.update(null, Wrappers.<BmsContent>lambdaUpdate()
            .set(BmsContent::getPublishStatus, PublishStatusEnum.WAITPUBLISH.getCode())
            .eq(BmsContent::getCmsContentId, cmsSeriesDto.getId())
            .in(BmsContent::getContentType, ContentTypeEnum.TELEPLAY.getValue(),
                    ContentTypeEnum.EPISODES.getValue())
            .eq(BmsContent::getPublishStatus, PublishStatusEnum.FAILPUBLISH.getCode())
    );
    //
    //同步修改未授权子集,修改cms子集
    List<CmsProgram> cmsProgramList = cmsProgramService.list(
            Wrappers.<CmsProgram>query().eq("series_id", cmsSeries.getId()));
    List<CmsProgram> cmsProgramsListBatch = new ArrayList<>();
    cmsProgramList.forEach(p -> {
      CmsProgram cmsProgram = new CmsProgram();
//                org.springframework.beans.BeanUtils.copyProperties(cmsSeries, cmsProgram, "id", "code", "createTime", "updateTime");
      cmsProgram.setSeriesName(cmsSeries.getName());
      cmsProgram.setLicensingWindowEnd(cmsSeries.getLicensingWindowEnd());
      cmsProgram.setLicensingWindowStart(cmsSeries.getLicensingWindowStart());
      cmsProgram.setDefinitionFlag(cmsSeries.getDefinitionFlag());
      cmsProgram.setId(p.getId());
      cmsProgramsListBatch.add(cmsProgram);
    });
    cmsProgramService.updateBatchById(cmsProgramsListBatch);
    // 4、添加违禁检测
    CmsSeriesVO cmsSeriesVO = new CmsSeriesVO(cmsSeries, ProhibitPointEnum.UPDATE);
    CmsSeries cmsSeries1 = iRuleProhibitHelper.checkSeriesRuleProhibit(cmsSeriesVO);
    cmsSeries.setProhibitStatus(cmsSeries1.getProhibitStatus());
    cmsSeries.setIsProhibit(cmsSeries1.getIsProhibit());
    this.updateById(cmsSeries);

    //重新统计锚点
    StatisticsInDelete statisticsInDelete = new StatisticsInDelete();
    statisticsInDelete.setType(StatisticsTypeEnum.Series.getValue());
    statisticsInDelete.setStaticticDate(DateUtils.getNowString());
    statisticsInDeleteService.sendMessage(statisticsInDelete);

    //媒资改名埋点
    sendMQ(cmsSeries2, cmsSeriesDto);
    return CommonResponse.general(CommonResponseEnum.SUCCESS);
  }

  private void sendMQ(CmsSeries cmsSeries, CmsSeriesDto cmsSeriesDto) {
    try {
      String name = cmsSeriesDto.getName();
      if (!cmsSeries.getName().equals(name) ||
          !cmsSeries.getPgmSndClassId().equals(cmsSeriesDto.getPgmSndClassId())) {
        HashMap<String, Object> map = new HashMap<>();
        String cmsContentCode = cmsSeries.getCode();
        map.put("cmsContentCode", cmsContentCode);
        if (StringUtils.isEmpty(name) && null == cmsSeriesDto.getPgmCategoryId()
            && StringUtils.isEmpty(cmsSeriesDto.getPgmCategory())) {
          return;
        }
        if (StringUtils.isNotEmpty(name) && !cmsSeries.getName().equals(name)) {
          map.put("name", name);
          //更新栏目关系、包关系中媒资名称及分类
          BmsCategoryContent bmsCategoryContent = new BmsCategoryContent();
          bmsCategoryContent.setContentName(name);
          bmsCategoryContentMapper.update(bmsCategoryContent,
              new LambdaQueryWrapper<BmsCategoryContent>()
                  .eq(BmsCategoryContent::getCmsContentCode, cmsContentCode));
          BmsPackageContent bmsPackageContent = new BmsPackageContent();
          bmsPackageContent.setContentName(name);
          bmsPackageContentMapper.update(bmsPackageContent,
              new LambdaQueryWrapper<BmsPackageContent>()
                  .eq(BmsPackageContent::getCmsContentCode, cmsContentCode));
        }
        if (null != cmsSeriesDto.getPgmCategoryId() && !cmsSeries.getPgmCategoryId()
            .equals(cmsSeriesDto.getPgmCategoryId())) {
          map.put("pgmCategoryId", cmsSeriesDto.getPgmCategoryId().toString());
          SysDictionaryItem item = sysDictionaryItemService.getById(
              cmsSeriesDto.getPgmCategoryId());
          map.put("pgmCategory", item.getName());
        }
        if (StringUtils.isNotEmpty(cmsSeriesDto.getPgmCategory()) && !cmsSeries.getPgmCategory()
            .equals(cmsSeriesDto.getPgmCategory())) {
          map.put("pgmCategory", cmsSeriesDto.getPgmCategory());
          List<SysDictionaryItem> sysDictionaryItems = sysDictionaryItemMapper.selectList(
              new LambdaQueryWrapper<SysDictionaryItem>()
                  .eq(SysDictionaryItem::getName, cmsSeriesDto.getPgmCategory()));
          map.put("pgmCategoryId", sysDictionaryItems.get(0).getId());
        }
        HashMap<String, Object> hashMap = new HashMap<>();
        BmsContent bmsContent = JSON.parseObject(JSON.toJSONString(map), BmsContent.class);
        hashMap.put(StatisticsMediaMQEnum.BMS_CONTENT.getValue(), bmsContent);
        log.info("cmsseries.sendMQ.hashMap={}", JSON.toJSONString(hashMap));
        this.rabbitTemplate.convertAndSend(CmsContentRenameMQConfig.CMS_CONTENT_RENAME_EXCHANGE,
            CmsContentRenameMQConfig.CMS_CONTENT_RENAME_ROUTING, hashMap);
      }
    } catch (Exception e) {
      log.error("CmsContentServiceImpl.sendMQ.cmsSeries={},cmsSeriesDto={},Exception=",
          JSON.toJSONString(cmsSeries), JSON.toJSONString(cmsSeriesDto), e);
    }
  }

  /**
   * 子集列表
   *
   * @param page
   * @param id
   * @return
   */
  @Override
  public Page<CmsProgram> selectByPage(Page page, String id) {
    //分页参数检查
    if (page.getCurrent() < 0) {
      page.setCurrent(1);
    }
    if (page.getSize() < 0 || page.getSize() > 500) {
      page.setSize(20);
    }
    if (ObjectUtil.isEmpty(id)) {
      throw new CommonResponseException("参数不能为空");
    }
    Page iPage = new Page(page.getCurrent(), page.getSize());
    LambdaQueryWrapper<CmsProgram> wrapper = new LambdaQueryWrapper<>();
    wrapper.eq(CmsProgram::getSeriesId, id);
    wrapper.orderByDesc(CmsProgram::getEpisodeIndex);
    Page contentPage = cmsProgramService.page(iPage, wrapper);
    return contentPage;
  }

  /**
   * 绑定子集列表
   *
   * @param page
   * @param cpId
   * @param name
   * @param releaseStatus
   * @return
   */
  @Override
  public Page<CmsProgram> selectBybindProgramPage(Page page, String cpId, String name,
      Integer releaseStatus) {
    //分页参数检查
    if (page.getCurrent() < 0) {
      page.setCurrent(1);
    }
    if (page.getSize() < 0 || page.getSize() > 500) {
      page.setSize(20);
    }
    if (ObjectUtil.isEmpty(cpId)) {
      throw new CommonResponseException("参数不能为空");
    }
    Page iPage = new Page(page.getCurrent(), page.getSize());
    LambdaQueryWrapper<CmsProgram> wrapper = new LambdaQueryWrapper<>();
    if (ObjectUtil.isNotEmpty(name)) {
      wrapper.like(CmsProgram::getName, name);
    }
    if (ObjectUtil.isNotEmpty(releaseStatus)) {
      wrapper.eq(CmsProgram::getReleaseStatus, releaseStatus);
    }
    // series_id 为空 cpid必须相同
    wrapper.eq(CmsProgram::getCpId, cpId);
    wrapper.eq(CmsProgram::getSeriesFlag, SeriesFlagEnum.Subset.getValue());
    wrapper.isNull(CmsProgram::getSeriesId);
    wrapper.orderByDesc(CmsProgram::getId);
    Page contentPage = cmsProgramService.page(iPage, wrapper);
    return contentPage;
  }


  /**
   * 绑定子集
   *
   * @param seriesId
   * @param ids
   * @return
   */
  @Override
  @Transactional(rollbackFor = Exception.class)
  public CommonResponse bindProgram(String seriesId, String ids) {
    //参数校验
    CommonResponse<Object> FAIL = bindCheckOut(seriesId, ids);
    if (FAIL != null) {
      return FAIL;
    }
    String[] idArray = ids.split(",");
    List<Long> idList = CmsProgramServiceImpl.getIdList(idArray);
    //发布中，更新中，回收中检查
    RuleResult rr = RuleCondition.create()
        .and(LockStatusRule.init(CmsSeries.class).data(Long.parseLong(seriesId)))
        .and(LockStatusRule.init(CmsProgram.class).data(idList))
//                .and(PublishStatusRule.init(BmsContent.class).data(Long.parseLong(seriesId)).condCol(BmsContent::getCmsContentId).policy(PublishCheck.ING))
        .execute();
    if (!rr.isPass()) {
      throw new BizException(rr.getMsg());
    }
    //绑定子集
    // 先根据子集id查出对应子集集数
    List<Integer> episodeIndexs = cmsProgramService.listByIds(idList)
        .stream().map(CmsProgram::getEpisodeIndex)
        .collect(Collectors.toList());
    // 判断绑定子集是否重复，剧集总集数是否小于子集集数
    check(Long.parseLong(seriesId), episodeIndexs);
    //1.遍历要绑定的子集
    CmsSeries cmsSeries = this.getById(seriesId);
    Arrays.stream(idArray).forEach(p -> CmsPro(p, cmsSeries));
    //4.绑定子集后需要修改剧头的缺集检测状态为待检测 实际集数+1
    cmsSeries.setMissingDetection(MissingDetectionEnum.YES.getValue());
    cmsSeries.setVolumnUpdate(
        cmsSeries.getVolumnUpdate() + Long.parseLong(String.valueOf(idArray.length)));
    this.updateById(cmsSeries);
    return CommonResponse.general(CommonResponseEnum.SUCCESS);
  }

  private void CmsPro(String id, CmsSeries cmsSeries) {
    //2.子集与剧集建立关联关系
    CmsProgram cmsProgram = new CmsProgram();
    saveCms(cmsSeries, cmsProgram);
    LambdaUpdateWrapper<CmsProgram> wrapper = new LambdaUpdateWrapper<>();
    wrapper.eq(CmsProgram::getId, id);
    cmsProgramService.update(cmsProgram, wrapper);

    //3.判断剧集有没有授权 授权后就同步
    List<BmsContent> bmsContents = bmsContentService.list(Wrappers.<BmsContent>lambdaQuery()
        .eq(BmsContent::getCmsContentId, cmsSeries.getId())
        .in(BmsContent::getContentType, ContentTypeEnum.TELEPLAY.getValue(),
            ContentTypeEnum.EPISODES.getValue()));
    if (ObjectUtil.isNotEmpty(bmsContents)) {
      CmsProgram program = cmsProgramService.getById(id);
      bmsContents.forEach(bms -> {
        BmsProgram bmsProgram = new BmsProgram();
        saveBms(cmsSeries, program, bms, bmsProgram);
        bmsProgramService.save(bmsProgram);
        //子集绑定的图片同步到bms
        List<CmsPicture> cmsPictures = cmsPictureService.list(
            Wrappers.<CmsPicture>lambdaQuery().eq(CmsPicture::getContentId, id)
                .eq(CmsPicture::getContentType, ContentTypeEnum.SUBSET.getValue()));
        List<BmsPicture> collect = cmsPictures.stream()
            .map(p -> cmsProgramService.tranBatch(p, bmsProgram, ContentTypeEnum.SUBSET.getValue()))
            .collect(Collectors.toList());
        bmsPictureService.saveBatch(collect);
      });
    }
  }


  /**
   * 移除子集
   *
   * @param ids
   * @return
   */
  @Override
  @Transactional(rollbackFor = Exception.class)
  public CommonResponse deleteById(String seriesId, String ids) {
    //参数校验
    CommonResponse<Object> FAIL = bindCheckOut(seriesId, ids);
    if (FAIL != null) {
      return FAIL;
    }

    String[] idArray = ids.split(",");
    List<Long> bmsProgramId = new ArrayList<>();
    for (String id : idArray) {
      bmsProgramId.add(Long.parseLong(id));
    }
    List<Long> contentId = new ArrayList<>();
    contentId.add(Long.parseLong(seriesId));
    //发布中，更新中，回收中检查
    RuleResult rr = RuleCondition.create()
        .and(LockStatusRule.init(CmsSeries.class).data(contentId))
        .and(LockStatusRule.init(CmsProgram.class).data(bmsProgramId))
        /*  .and(RecycleStatusRule.init(BmsContent.class).data(contentId).condCol(BmsContent::getCmsContentId).policy(RecycleCheck.CAH_DELETE))
          .and(RecycleStatusRule.init(BmsProgram.class).data(bmsProgramId).condCol(BmsProgram::getCmsContentId).policy(RecycleCheck.CAH_DELETE))*/
        .execute();
    if (!rr.isPass()) {
      throw new BizException(rr.getMsg());
    }

    List<BmsProgram> list1 = bmsProgramService.list(Wrappers.<BmsProgram>lambdaQuery()
        .in(BmsProgram::getCmsContentId, idArray)
        .notIn(BmsProgram::getPublishStatus, PublishStatusEnum.WAITPUBLISH.getCode(),
            PublishStatusEnum.ROLLBACK.getCode()
            , PublishStatusEnum.FAILPUBLISH.getCode(), PublishStatusEnum.DISABLE.getCode()));
    if (ObjectUtil.isNotEmpty(list1)) {
      throw new CommonResponseException("请检查媒资的发布状态是否满足条件!");
    }
    for (String Programid : idArray) {
      long id = Long.parseLong(Programid);
      //sp侧的子集
      List<BmsProgram> bmsPrograms = bmsProgramService.list(Wrappers.<BmsProgram>lambdaQuery()
          .eq(BmsProgram::getCmsContentId, id)
          .eq(BmsProgram::getCmsSeriesId, seriesId));
      //如果子集授权到了sp侧就移除
      long count = cmsProgramService.count(
          Wrappers.<CmsProgram>lambdaQuery().eq(CmsProgram::getSeriesId, seriesId));
      if (ObjectUtil.isEmpty(count)) {
        count = 0L;
      }
      if (ObjectUtil.isNotEmpty(bmsPrograms)) {
        CommonResponse<Object> sp = SP(seriesId, id, bmsPrograms, count);
        if (sp != null) {
          return sp;
        }
      } else {
        CP(seriesId, id, count);
      }
    }
    return CommonResponse.general(CommonResponseEnum.SUCCESS);
  }


  /**
   * 缺集信息
   *
   * @param id
   * @return
   */
  @Override
  public CommonResponse lack(String id) {
    if (ObjectUtil.isEmpty(id)) {
      throw new CommonResponseException("参数不能为空");
    }
    CmsSeries cmsSeries = this.getById(Long.parseLong(id));
    return CommonResponse.general(CommonResponseEnum.SUCCESS, "查询成功",
        cmsSeries.getMissedInfo());
  }

  //========================================公共方法======================================================

  /**
   * 移除授权到sp侧的子集
   *
   * @param seriesId
   * @param id
   * @param bmsPrograms
   * @return
   */
  private CommonResponse<Object> SP(String seriesId, long id, List<BmsProgram> bmsPrograms,
      long count) {
    //判断是否为禁用SP下的子集
    List<Long> bmsIds = bmsPrograms.stream().map(p -> p.getId()).collect(Collectors.toList());
    RuleResult execute = RuleCondition.create()
        .and(SpCheckRule.init(BmsProgram.class).data(bmsIds))
        .execute();
    if (!execute.isPass()) {
      //更新媒资发布状态为禁用
      Arrays.stream(execute.getNameVal().split(SymbolConstant.COMMA)).collect(Collectors.toList())
          .forEach(programId -> {
            bmsProgramService.update(Wrappers.lambdaUpdate(BmsProgram.class)
                .set(BmsProgram::getPublishStatus, PublishStatusEnum.DISABLE.getCode())
                .eq(BmsProgram::getId, programId));
          });
      //根据BmsProgram的id删除bmsPrograms的元素，返回List<BmsProgram>
      bmsPrograms.removeIf(
          bmsProgram -> Arrays.stream(execute.getNameVal().split(SymbolConstant.COMMA))
              .map(Long::parseLong).collect(Collectors.toList()).contains(bmsProgram.getId()));
    }
    //判断该子集在SP侧是否为【待发布】或【回收成功】状态
    for (BmsProgram bmsProgram : bmsPrograms) {
      Integer publishStatus = bmsProgram.getPublishStatus();
      if (!publishStatus.equals(PublishStatusEnum.WAITPUBLISH.getCode()) && !publishStatus.equals(
          PublishStatusEnum.ROLLBACK.getCode())) {
        //判断是否存在发布状态为已禁用，sp状态为已启用
        if (PublishStatusEnum.DISABLE.getCode().equals(publishStatus)) {
          SysSp cacheMapValue = redisService.getCacheMapValue(RedisKeyConstants.SYS_SP,
              bmsProgram.getSpId().toString());
          if (StatusEnum.COME.getCode().equals(cacheMapValue.getStatus())) {
            return CommonResponse.general(CommonResponseEnum.FAIL,
                bmsProgram.getName() + "在SP侧是【禁用】状态，请先回收！");
          } else {
            continue;
          }

        }
        return CommonResponse.general(CommonResponseEnum.FAIL,
            bmsProgram.getName() + "在SP侧不是【待发布】或【回收成功】状态，无法移除！");
      }
    }

    for (BmsProgram bmsProgram : bmsPrograms) {
      Integer publishStatus = bmsProgram.getPublishStatus();
      if (publishStatus.equals(PublishStatusEnum.WAITPUBLISH.getCode()) || publishStatus.equals(
          PublishStatusEnum.ROLLBACK.getCode())) {
        //如果是待发布 删除 bms图片
        bmsPictureService.remove(Wrappers.<BmsPicture>lambdaQuery()
            .eq(BmsPicture::getContentType, ContentTypeEnum.SUBSET.getValue())
            .eq(BmsPicture::getBmsContentId, bmsProgram.getId()));
        bmsProgramService.removeById(bmsProgram);
      }
    }
    CP(seriesId, id, count);
    return null;
  }

  /**
   * 解绑cp侧的关系
   *
   * @param seriesId
   * @param id
   * @param count
   */
  private void CP(String seriesId, long id, long count) {
    //解绑跟cms子集的关系
    cmsProgramService.update(null, Wrappers.<CmsProgram>lambdaUpdate()
        .set(CmsProgram::getSeriesId, null)
        .set(CmsProgram::getSeriesCode, null)
        .set(CmsProgram::getSeriesName, null)
        .eq(CmsProgram::getId, id)
        .eq(CmsProgram::getSeriesId, seriesId));
    count = count - 1L;
    //解绑子集后需要修改剧头的缺集检测状态为待检测
    this.update(null, Wrappers.<CmsSeries>lambdaUpdate()
        .set(CmsSeries::getVolumnUpdate, count)
        .set(CmsSeries::getMissingDetection, MissingDetectionEnum.YES.getValue())
        .set(CmsSeries::getVolumnUpdate, count)
        .eq(CmsSeries::getId, seriesId)
    );
  }


  /**
   * 授权到sp侧的子集
   *
   * @param cmsSeries
   * @param cmsProgram
   * @param bmsContent
   * @param bmsProgram
   */
  private void saveBms(CmsSeries cmsSeries, CmsProgram cmsProgram, BmsContent bmsContent,
      BmsProgram bmsProgram) {
    org.springframework.beans.BeanUtils.copyProperties(cmsProgram, bmsProgram, "id", "code",
        "createTime", "updateTime");
    //关联
    bmsProgram.setName(cmsProgram.getName());
    bmsProgram.setCmsContentId(cmsProgram.getId());
    bmsProgram.setCmsContentCode(cmsProgram.getCode());
    bmsProgram.setCpId(bmsContent.getCpId());
    bmsProgram.setCpName(bmsContent.getCpName());
    bmsProgram.setSpId(bmsContent.getSpId());
    bmsProgram.setSpName(bmsContent.getSpName());
    bmsProgram.setContentProvider(bmsContent.getContentProvider());
    bmsProgram.setOutPassageIds(bmsContent.getOutPassageIds());
    bmsProgram.setOutPassageNames(bmsContent.getOutPassageNames());
    bmsProgram.setLicensingWindowStart(bmsContent.getLicensingWindowStart());
    bmsProgram.setLicensingWindowEnd(bmsContent.getLicensingWindowEnd());
    bmsProgram.setCmsSeriesId(cmsSeries.getId());
    bmsProgram.setCmsSeriesCode(cmsSeries.getCode());
    bmsProgram.setBmsSpChannelId(bmsContent.getBmsSpChannelId());
    bmsProgram.setBmsSpChannelName(bmsContent.getBmsSpChannelName());
  }

  /**
   * 关联cp侧的关系
   *
   * @param cmsSeries
   * @param cmsProgram
   */
  private void saveCms(CmsSeries cmsSeries, CmsProgram cmsProgram) {
    //其他编目信息在子集绑定剧头后，继承剧头的信息。
    BeanUtils.copyProperties(cmsSeries, cmsProgram, "id", "code", "createTime", "updateTime",
        "name", "cpId", "cpName", "movieHeadDuration", "movieTailDuration", "approval",
        "description", "cpCheckStatus", "cpCheckDesc", "cpCheckTime", "cpChecker", "opCheckStatus",
        "opCheckDesc", "opChecker", "opCheckTime", "source", "creatorName", "creatorId",
        "lockStatus", "resourcePreviewCode", "previewStatus", "searchName", "originalName");
    cmsProgram.setSeriesId(cmsSeries.getId());
    cmsProgram.setSeriesCode(cmsSeries.getCode());
    cmsProgram.setSeriesName(cmsSeries.getName());
    //剧集设置片头后，若子集没有单独设置时，子集片头默认继承剧头的。
    if (ObjectUtil.isEmpty(cmsProgram.getMovieHeadDuration()) && ObjectUtil.isNotEmpty(
        cmsSeries.getMovieHeadDuration())) {
      cmsProgram.setMovieHeadDuration(cmsSeries.getMovieHeadDuration());
    }
    //剧集设置片尾后，若子集没有单独设置时，子集片尾默认继承剧头的。
    if (ObjectUtil.isEmpty(cmsProgram.getMovieTailDuration()) && ObjectUtil.isNotEmpty(
        cmsSeries.getMovieTailDuration())) {
      cmsProgram.setMovieTailDuration(cmsSeries.getMovieTailDuration());
    }
  }

  private void setItem(CmsSeriesDto cmsSeriesDto, CmsSeries cmsSeries) {
    if (SourceEnum.SYSWORK.getValue().equals(cmsSeriesDto.getSource())) {
      // 专线不走这个逻辑
      return;
    }
    //判断产地参数
    if (cmsSeriesDto.getOriginalCountryId() != null) {
      SysDictionaryItem item = sysDictionaryItemService.getOne(Wrappers.<SysDictionaryItem>query()
          .eq("id", cmsSeriesDto.getOriginalCountryId())
          .ne("status", StatusEnum.DELETE.getCode())
          .last("limit 1"));
      if (item == null) {
        throw new CommonResponseException("当前产地参数不存在");
      }
      if (!StatusEnum.COME.getCode().equals(item.getStatus())) {
        throw new CommonResponseException("当前产地参数状态失效");
      }
      cmsSeries.setOriginalCountry(item.getName());
    }
    SysDictionaryItem item1 = sysDictionaryItemService.getById(cmsSeriesDto.getPgmCategoryId());
    if (ObjectUtil.isNotEmpty(cmsSeriesDto.getPgmSndClassIdList())) {
      List<SysDictionaryItem> sysDictionaryItems = sysDictionaryItemMapper.selectList(
          Wrappers.<SysDictionaryItem>lambdaQuery().select(SysDictionaryItem::getName)
              .in(SysDictionaryItem::getId, cmsSeriesDto.getPgmSndClassIdList()));
      if (ObjectUtil.isNotEmpty(item1)) {
        cmsSeries.setPgmCategory(item1.getName());
        cmsSeries.setPgmCategoryId(cmsSeriesDto.getPgmCategoryId());
      }
      if (ObjectUtil.isNotEmpty(sysDictionaryItems)) {
        List<String> names = sysDictionaryItems.stream().map(p -> p.getName())
            .collect(Collectors.toList());
        cmsSeries.setPgmSndClass(StringUtils.join(names, ","));
        cmsSeries.setPgmSndClassId(Joiner.on(",").join(cmsSeriesDto.getPgmSndClassIdList()));
      }
    }
  }

  private void setItemUpdate(CmsSeriesDto cmsSeriesDto, CmsSeries cmsSeries) {

    //判断产地参数
    if (cmsSeriesDto.getOriginalCountryId() != null) {
      SysDictionaryItem item = sysDictionaryItemService.getOne(Wrappers.<SysDictionaryItem>query()
          .eq("id", cmsSeriesDto.getOriginalCountryId())
          .ne("status", StatusEnum.DELETE.getCode())
          .last("limit 1"));
      if (item == null) {
        throw new CommonResponseException("当前产地参数不存在");
      }
      if (!StatusEnum.COME.getCode().equals(item.getStatus())) {
        throw new CommonResponseException("当前产地参数状态失效");
      }
      cmsSeries.setOriginalCountry(item.getName());
    }
    SysDictionaryItem item1 = sysDictionaryItemService.getById(cmsSeriesDto.getPgmCategoryId());
    if (ObjectUtil.isNotEmpty(cmsSeriesDto.getPgmSndClassIdList())) {
      List<SysDictionaryItem> sysDictionaryItems = sysDictionaryItemMapper.selectList(
          Wrappers.<SysDictionaryItem>lambdaQuery().select(SysDictionaryItem::getName)
              .in(SysDictionaryItem::getId, cmsSeriesDto.getPgmSndClassIdList()));
      if (ObjectUtil.isNotEmpty(item1)) {
        cmsSeries.setPgmCategory(item1.getName());
        cmsSeries.setPgmCategoryId(cmsSeriesDto.getPgmCategoryId());
      }
      if (ObjectUtil.isNotEmpty(sysDictionaryItems)) {
        List<String> names = sysDictionaryItems.stream().map(p -> p.getName())
            .collect(Collectors.toList());
        cmsSeries.setPgmSndClass(StringUtils.join(names, ","));
        cmsSeries.setPgmSndClassId(Joiner.on(",").join(cmsSeriesDto.getPgmSndClassIdList()));
      }
    }
  }

  private void setItem(CmsSeriesDto cmsSeriesDto) {
    //判断产地参数
    if (cmsSeriesDto.getOriginalCountryId() != null) {
      SysDictionaryItem item = sysDictionaryItemService.getOne(Wrappers.<SysDictionaryItem>query()
          .eq("id", cmsSeriesDto.getOriginalCountryId())
          .ne("status", StatusEnum.DELETE.getCode())
          .last("limit 1"));
      if (item == null) {
        throw new CommonResponseException("当前产地参数不存在");
      }
      if (!StatusEnum.COME.getCode().equals(item.getStatus())) {
        throw new CommonResponseException("当前产地参数状态失效");
      }
      cmsSeriesDto.setOriginalCountry(item.getName());
    }
    SysDictionaryItem item1 = sysDictionaryItemService.getById(cmsSeriesDto.getPgmCategoryId());
    if (ObjectUtil.isNotEmpty(item1)) {
      cmsSeriesDto.setPgmCategory(item1.getName());
      cmsSeriesDto.setPgmCategoryId(item1.getId());
    }
    if (ObjectUtil.isNotEmpty(cmsSeriesDto.getPgmSndClassIdList())) {
      List<SysDictionaryItem> sysDictionaryItems = sysDictionaryItemMapper.selectList(
          Wrappers.<SysDictionaryItem>lambdaQuery().select(SysDictionaryItem::getName)
              .in(SysDictionaryItem::getId, cmsSeriesDto.getPgmSndClassIdList()));
      if (ObjectUtil.isNotEmpty(sysDictionaryItems)) {
        List<String> names = sysDictionaryItems.stream().map(p -> p.getName())
            .collect(Collectors.toList());
        cmsSeriesDto.setPgmSndClass(StringUtils.join(names, ","));
        cmsSeriesDto.setPgmSndClassId(Joiner.on(",").join(cmsSeriesDto.getPgmSndClassIdList()));
      }
    }
  }

  /**
   * 判断是否只关联一个预览片
   *
   * @param cmsSeriesDto
   * @return
   */
  private boolean preTypeStatus(CmsSeriesDto cmsSeriesDto) {

    if (ObjectUtil.isNotEmpty(cmsSeriesDto.getPreviewIds())) {
      String[] preSplit = cmsSeriesDto.getPreviewIds().split(",");
      if (preSplit.length > 1) {
        return true;
      }
      return false;
    }
    return false;
  }

  /**
   * 查询当前cp是否跳过审核
   *
   * @param cmsSeries
   */
  private void sysCpSkipCheck(CmsSeries cmsSeries) {
    //查询所属cp是否为跳过自审 如果是跳过自审
//        SysCp sysCp = sysCpMapper.selectById(cmsSeries.getCpId());
    SysCp sysCp = redisService.getCacheMapValue(RedisKeyConstants.SYS_CP,
        String.valueOf(cmsSeries.getCpId()));
    if (ObjectUtil.isNotEmpty(sysCp)) {
      if (sysCp.getSkipCheck().equals(SkipCheckEnum.NoSkip.getValue())) {
        cmsSeries.setCpCheckStatus(CpCheckStatusEnum.Await.getValue());
        cmsSeries.setOpCheckStatus(OpCheckStatusEnum.Await.getValue());
      }
      if (sysCp.getSkipCheck().equals(SkipCheckEnum.SelfSkip.getValue())) {
        cmsSeries.setCpCheckStatus(CpCheckStatusEnum.Pass.getValue());
        cmsSeries.setCpCheckDesc("自动跳过审核");
        cmsSeries.setCpCheckTime(new Date());
        cmsSeries.setOpCheckStatus(OpCheckStatusEnum.Await.getValue());
      }
      if (sysCp.getSkipCheck().equals(SkipCheckEnum.EndSkip.getValue())) {
        cmsSeries.setOpCheckStatus(OpCheckStatusEnum.Pass.getValue());
        cmsSeries.setOpCheckDesc("自动跳过审核");
        cmsSeries.setOpCheckTime(new Date());
        cmsSeries.setCpCheckStatus(CpCheckStatusEnum.Await.getValue());
      }
      //如果为全部跳过则修改自审和终审状态
      if (sysCp.getSkipCheck().equals(SkipCheckEnum.AllSkip.getValue())) {
        cmsSeries.setCpCheckStatus(CpCheckStatusEnum.Pass.getValue());
        cmsSeries.setOpCheckStatus(CpCheckStatusEnum.Pass.getValue());
        cmsSeries.setOpCheckDesc("自动跳过审核");
        cmsSeries.setOpCheckTime(new Date());
        cmsSeries.setCpCheckDesc("自动跳过审核");
        cmsSeries.setCpCheckTime(new Date());
      }
    }
  }

  /**
   * 图片保存到cms_picture表中
   *
   * @param cmsSeriesDto
   * @param cmsSeries
   * @return 返回的是保存后的图片id
   */
  private List<Long> cmsSavePic(CmsSeriesDto cmsSeriesDto, CmsSeries cmsSeries) {
    List<Long> picIds = new ArrayList<>();
    List<CmsPicture> picList = new ArrayList<>();
    List<CmsPictureDto> picArray = cmsSeriesDto.getPicArray();
    if (ObjectUtil.isNotEmpty(picArray)) {
      SysCp sysCp = redisService.getCacheMapValue(RedisKeyConstants.SYS_CP,
          String.valueOf(cmsSeries.getCpId()));
      picArray.stream().forEach(p -> {
        CmsPicture cmsPicture = new CmsPicture();
        cmsPicture.setType(p.getType());
        cmsPicture.setSequence(p.getSequence());
        cmsPicture.setRatio(p.getRatio());
        cmsPicture.setFileUrl(p.getFileUrl());
        //新增添加图片大小
        cmsPicture.setFileSize((p.getFileSize() != null) ? p.getFileSize(): 0L);
        //图片与媒资建立关联关系
        cmsPicture.setContentId(cmsSeries.getId());
        cmsPicture.setContentCode(cmsSeries.getCode());
        if (ObjectUtil.isEmpty(cmsSeries.getSeriesType())) {
          cmsPicture.setContentType(ContentTypeEnum.TELEPLAY.getValue());
        } else if (cmsSeries.getSeriesType().equals(0)) {
          cmsPicture.setContentType(ContentTypeEnum.TELEPLAY.getValue());
        } else if (cmsSeries.getSeriesType().equals(1)) {
          cmsPicture.setContentType(ContentTypeEnum.EPISODES.getValue());
        }
        cmsPicture.setCpId(cmsSeries.getCpId());
        cmsPicture.setCpName(cmsSeries.getCpName());

        if (ObjectUtil.isNotEmpty(sysCp)) {
          cmsPicture.setStorageId(sysCp.getStorageId());
          cmsPicture.setStorageName(sysCp.getStorageName());
        }
        picList.add(cmsPicture);
      });
      cmsPictureService.saveBatch(picList);
      picList.stream().forEach(p -> picIds.add(p.getId()));
    }

    return picIds;
  }


  /**
   * 关联视频
   *
   * @param movieId
   * @param cmsSeries
   * @param type
   */
  private void correlationMov(String movieId, CmsSeries cmsSeries, Integer type) {

    if (ObjectUtil.isNotEmpty(movieId)) {
      long id = Long.parseLong(movieId);
      CmsResource mov = cmsResourceService.getOne(Wrappers.<CmsResource>lambdaQuery()
          .eq(CmsResource::getId, id)
      );
      if (ObjectUtil.isNotEmpty(mov)) {
        //视频与媒资建立关系
        mov.setContentId(cmsSeries.getId());
        mov.setContentCode(cmsSeries.getCode());
        mov.setContentName(cmsSeries.getName());
        mov.setContentType(type);
        mov.setMediaSpec(cmsSeries.getMediaSpec());
        mov.setCpId(cmsSeries.getCpId());
        mov.setCpName(cmsSeries.getCpName());
        mov.setSource(cmsSeries.getSource());
        mov.setContentStatus(MediaAssociationStatusEnum.Relevancy.getValue());
        cmsSeries.setPreviewStatus(MovieStatusEnum.Relevancy.getValue());
        cmsSeries.setResourcePreviewCode(mov.getCode());
        this.updateById(cmsSeries);
        cmsResourceService.updateById(mov);

        Long storageId = mov.getStorageId();
        String fileUrl = mov.getFileUrl();
        SysStorage sysStorage = null;
        String movUrl = null;
        if (ObjectUtil.isNotEmpty(storageId)) {
          sysStorage = storageService.getOne(Wrappers.lambdaQuery(SysStorage.class)
              .eq(SysStorage::getId, storageId)
              .eq(SysStorage::getStatus, StorageStatusEnum.ALLOW.getCode())
              .last("limit 1")
          );
          if (sysStorage != null && sysStorage.getMovieHttpPrefix() != null) {
            Pattern p = compile("\\d+\\.\\d+\\.\\d+\\.\\d+\\:\\d+");
            Matcher matcher = p.matcher(fileUrl);
            if (matcher.find()) {
                            /*int length = fileUrl.length();
                            String concat = sysStorage.getMovieHttpPrefix().concat(fileUrl.substring(fileUrl.indexOf(matcher.group()) + matcher.group().length() + 1, length));
                            String movieUrl = concat + "?cpid=" + mov.getCpId() + "&moviecode=" + mov.getCode();
                            movUrl = movieUrl;*/
              /** PlayURL只保存相对路径 */
              movUrl = FtpUtil.getPath(fileUrl);
            }
          }
        }
        //同步到cms_movie表中
        cmsMovieService.update(null, Wrappers.<CmsMovie>lambdaUpdate()
            .set(CmsMovie::getContentStatus, MediaAssociationStatusEnum.Relevancy.getValue())
            .set(CmsMovie::getContentType, type)
            .set(CmsMovie::getPlayUrl, movUrl)
            .set(CmsMovie::getFileUrl, mov.getFileUrl())
            .set(CmsMovie::getContentId, cmsSeries.getId())
            .set(CmsMovie::getCpId, cmsSeries.getCpId())
            .set(CmsMovie::getCpName, cmsSeries.getCpName())
            .set(CmsMovie::getContentCode, cmsSeries.getCode())
            .set(CmsMovie::getContentId, cmsSeries.getId())
            .set(CmsMovie::getMediaSpec, cmsSeries.getMediaSpec())
            .eq(CmsMovie::getResourceId, id)
        );
      }
    }
  }

  /**
   * 参数检查
   *
   * @param ids
   * @return
   */
  private CommonResponse<Object> getResponse(String ids) {
    if (ObjectUtil.isEmpty(ids)) {
      throw new CommonResponseException("参数不能为空");
    }
    if (!exist(ids)) {
      return CommonResponse.general(CommonResponseEnum.DELETE_ERROR, "媒资不存在，请刷新页面重试");
    }
    return null;
  }

  private void upCpStatus(CmsSeriesDto cmsSeriesDto) {
    SysCp sysCp = redisService.getCacheMapValue(RedisKeyConstants.SYS_CP,
        String.valueOf(cmsSeriesDto.getCpId()));
    if (ObjectUtil.isNotEmpty(sysCp)) {
      if (sysCp.getSkipCheck().equals(SkipCheckEnum.NoSkip.getValue())) {
        cmsSeriesDto.setCpCheckStatus(CpCheckStatusEnum.Await.getValue());
        cmsSeriesDto.setOpCheckStatus(OpCheckStatusEnum.Await.getValue());
      }
      if (sysCp.getSkipCheck().equals(SkipCheckEnum.SelfSkip.getValue())) {
        cmsSeriesDto.setOpCheckStatus(OpCheckStatusEnum.Await.getValue());
      }
      if (sysCp.getSkipCheck().equals(SkipCheckEnum.EndSkip.getValue())) {
        cmsSeriesDto.setCpCheckStatus(CpCheckStatusEnum.Await.getValue());
      }
    }
  }

  /**
   * 绑定子集参数校验
   *
   * @param seriesId
   * @param ids
   * @return
   */
  private CommonResponse<Object> bindCheckOut(String seriesId, String ids) {
    if (ObjectUtil.isEmpty(seriesId) || ObjectUtil.isEmpty(ids)) {
      throw new CommonResponseException("参数不能为空");
    }
    if (!cmsProgramService.exist(ids)) {
      return CommonResponse.general(CommonResponseEnum.FAIL, "子集不存在,请刷新页面重试");
    }

    if (!exist(seriesId)) {
      return CommonResponse.general(CommonResponseEnum.DELETE_ERROR, "剧集不存在，请刷新页面重试");
    }
    return null;
  }

  /**
   * 编辑后修改状态
   *
   * @param cmsSeriesDto
   * @param flag
   * @return
   */
  private CommonResponse<Object> updateStatus(CmsSeriesDto cmsSeriesDto, Boolean flag) {
    if (flag) {
      //获取批量修改得ids
      String[] idArray = cmsSeriesDto.getIds().split(",");
      this.update(null, Wrappers.<CmsSeries>lambdaUpdate()
          .set(CmsSeries::getCpCheckStatus, CpCheckStatusEnum.Await.getValue())
          .set(CmsSeries::getOpCheckStatus, OpCheckStatusEnum.Await.getValue())
          .in(CmsSeries::getId, idArray)
      );

      bmsContentService.update(null, Wrappers.<BmsContent>lambdaUpdate()
          .set(BmsContent::getCpCheckStatus, CpCheckStatusEnum.Await.getValue())
          .set(BmsContent::getOpCheckStatus, OpCheckStatusEnum.Await.getValue())
          .in(BmsContent::getCmsContentId, idArray)
          .in(BmsContent::getContentType, ContentTypeEnum.TELEPLAY.getValue(),
              ContentTypeEnum.EPISODES.getValue())
      );

      //发布成功修改为待更新
      bmsContentService.update(null, Wrappers.<BmsContent>lambdaUpdate()
          .set(BmsContent::getPublishStatus, PublishStatusEnum.WAITUPDATE.getCode())
          .in(BmsContent::getCmsContentId, idArray)
          .in(BmsContent::getContentType, ContentTypeEnum.TELEPLAY.getValue(),
              ContentTypeEnum.EPISODES.getValue())
          .eq(BmsContent::getPublishStatus, PublishStatusEnum.PUBLISH.getCode())
      );
      return CommonResponse.general(CommonResponseEnum.SUCCESS);
    } else {
      return CommonResponse.general(CommonResponseEnum.FAIL);
    }
  }


  /**
   * 新增时授权通过保存到bms表
   *
   * @param cmsSeries
   * @param sysAuthorization
   * @param bmsContent
   */
  private void saveBms(CmsSeries cmsSeries, SysAuthorization sysAuthorization,
      BmsContent bmsContent) {
    BeanUtils.copyProperties(cmsSeries, bmsContent, "id", "code", "createTime", "updateTime");
    //关联
    bmsContent.setCmsContentCode(cmsSeries.getCode());
    bmsContent.setCmsContentId(cmsSeries.getId());

    if (ObjectUtil.isNotEmpty(cmsSeries.getSeriesType())) {
      if (cmsSeries.getSeriesType() == 0) {
        bmsContent.setContentType(ContentTypeEnum.TELEPLAY.getValue());
      }
      if (cmsSeries.getSeriesType() == 1) {
        bmsContent.setContentType(ContentTypeEnum.EPISODES.getValue());
      }
    }
    bmsContent.setStatus(StatusEnum.COME.getCode());
    bmsContent.setSpId(sysAuthorization.getSpId());
    bmsContent.setSpName(sysAuthorization.getSpName());
    bmsContent.setCpId(sysAuthorization.getCpId());
    bmsContent.setCpName(sysAuthorization.getCpName());

//        SysSp sysSp = sysSpMapper.selectById(sysAuthorization.getSpId());
    SysSp sysSp = redisService.getCacheMapValue(RedisKeyConstants.SYS_SP,
        String.valueOf(sysAuthorization.getSpId()));
    if (ObjectUtil.isNotEmpty(sysSp)) {
      bmsContent.setOutPassageIds(sysSp.getOutPassageIds());
      bmsContent.setOutPassageNames(sysSp.getOutPassageNames());
      bmsContent.setBmsSpChannelId(sysSp.getBmsSpChannelId());
      bmsContent.setBmsSpChannelName(sysSp.getBmsSpChannelName());
    }

  }


  /**
   * 判断媒资是否存在
   *
   * @param ids
   * @return
   */
  public Boolean exist(String ids) {
    for (String id : ids.split(",")) {
      CmsSeries cms = this.getById(id);
      if (ObjectUtil.isEmpty(cms)) {
        return false;
      }
    }
    return true;
  }

  private void getRequestResource(CmsSeriesDto cmsSeriesDto, CmsSeries cmsSeries) {
    if (SourceEnum.SYSWORK.getValue().equals(cmsSeriesDto.getRequestResource())) {
      //是否需要注册字典
      cmsSeries.setSource(SourceEnum.SYSWORK.getValue());
      itemServiceimpl.setDictionaryId(cmsSeries);
    } else {
      cmsSeries.setSource(SourceEnum.MANUALWORK.getValue());
    }
  }

  /**
   * 缺集检测
   */
  public void missedXxlJob() {
    //需要检测的剧集
    List<CmsSeries> cmsSeries = cmsSeriesMapper.selectList(Wrappers.<CmsSeries>lambdaQuery()
        .eq(CmsSeries::getMissingDetection, MissingDetectionEnum.YES.getValue()));
    for (CmsSeries series : cmsSeries) {
      LambdaUpdateWrapper<CmsSeries> wrapper = new LambdaUpdateWrapper<>();
      //剧集下的所有子集
      List<CmsProgram> cmsPrograms = cmsProgramService.list(
          Wrappers.<CmsProgram>lambdaQuery().select(CmsProgram::getEpisodeIndex)
              .eq(CmsProgram::getSeriesId, series.getId()));
      if (ObjectUtil.isNotEmpty(cmsPrograms)) {
        //采集子集集数
        List<Integer> episodeIndex = cmsPrograms.stream().map(p -> p.getEpisodeIndex())
            .collect(Collectors.toList());

        if (ObjectUtil.isNotEmpty(episodeIndex)) {
          //缺集信息
          Integer integer = episodeIndex.stream().max(Integer::compare).get();
          List<Integer> missing = missedInfo(episodeIndex.toArray(new Integer[0]), integer);
          wrapper.set(CmsSeries::getVolumnUpdate, episodeIndex.size());
//            LambdaUpdateWrapper<BmsContent> bmsContentLambdaUpdateWrapper = new LambdaUpdateWrapper<>();
          if (ObjectUtil.isNotEmpty(missing)) {
            wrapper
                .set(CmsSeries::getMissedInfo, "疑似缺集: " + StringUtils.join(missing, "、"))
                .set(CmsSeries::getMissedStatus, 1);

//                bmsContentLambdaUpdateWrapper
//                        .set(BmsContent::getMissedInfo, "疑似缺集: " + StringUtils.join(missing, "、"));
            //缺集信息补齐后 清空缺集信息
          } else {
            wrapper
                .set(CmsSeries::getMissedStatus, 2)
                .set(CmsSeries::getMissingDetection, 2)
                .set(CmsSeries::getMissedInfo, "");

         /*       bmsContentLambdaUpdateWrapper
                        .set(BmsContent::getMissedInfo, "");*/
          }

   /*         bmsContentMapper.update(null, bmsContentLambdaUpdateWrapper
                    .eq(BmsContent::getCmsContentId, series.getId())
                    .eq(BmsContent::getContentType, ContentTypeEnum.TELEPLAY.getValue())
            );*/
          //剧集下没有子集也需要将缺集信息清空
        }
      } else {
        wrapper
            .set(CmsSeries::getMissedStatus, 2)
            .set(CmsSeries::getMissingDetection, 2)
            .set(CmsSeries::getMissedInfo, "");

         /*       bmsContentLambdaUpdateWrapper
                        .set(BmsContent::getMissedInfo, "");*/
      }
      cmsSeriesMapper.update(null, wrapper.eq(CmsSeries::getId, series.getId()));
    }
    log.info("任务调度器执行了,当前时间:" + LocalDateTime.now());
  }

  public Integer getType(Integer type) {
    if (ObjectUtil.isEmpty(type)) {
      type = 3;
    } else if (type == 0) {
      type = 3;
    } else if (type == 1) {
      type = 4;
    }
    return type;
  }


  /**
   * 对连续数组进行缺字段检测
   *
   * @param array
   * @param end
   * @return
   */
  public static List<Integer> missedInfo(Integer[] array, Integer end) {
    List<Integer> missing = new ArrayList<>();
    int b[] = null;
    b = new int[end];
    for (int i = 0; i < array.length; i++) {
      b[array[i] - 1] = 1;
    }
    for (int i = 0; i < b.length; i++) {
      if (b[i] == 0) {
        missing.add(i + 1);
      }
    }
    return missing;
  }
}


