package com.pukka.iptv.manage.util;

import java.util.ArrayList;
import java.util.List;

/**
 * @title:
 * @author: <PERSON><PERSON>
 * @date: 2021/5/24 19:19
 */
public abstract class ListUtil {

    private ListUtil() {
    }

    public static List<List<Long>> splitList(List<Long> messagesList, int groupSize) {
        int length = messagesList.size();
        // 计算可以分成多少组
        int num = (length + groupSize - 1) / groupSize;
        List<List<Long>> newList = new ArrayList<>(num);
        for (int i = 0; i < num; i++) {
            // 开始位置
            int fromIndex = i * groupSize;
            // 结束位置
            int toIndex = (i + 1) * groupSize < length ? (i + 1) * groupSize : length;
            newList.add(messagesList.subList(fromIndex, toIndex));
        }
        return newList;
    }
}
