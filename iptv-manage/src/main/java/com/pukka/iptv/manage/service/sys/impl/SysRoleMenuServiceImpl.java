package com.pukka.iptv.manage.service.sys.impl;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.pukka.iptv.common.base.enums.CommonResponseEnum;
import com.pukka.iptv.common.base.enums.MenuCheckedEnum;
import com.pukka.iptv.common.base.exception.CommonResponseException;
import com.pukka.iptv.common.base.util.Menu;
import com.pukka.iptv.common.data.model.sys.SysRole;
import com.pukka.iptv.common.data.model.sys.SysRoleMenu;
import com.pukka.iptv.common.data.vo.req.RoleMenus;
import com.pukka.iptv.manage.mapper.sys.SysRoleMenuMapper;
import com.pukka.iptv.manage.service.sys.SysMenuService;
import com.pukka.iptv.manage.service.sys.SysRoleMenuService;
import com.pukka.iptv.manage.service.sys.SysRoleService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;

/**
 *
 * @author: zhengcl
 * @date: 2021-8-17 16:48:33
 */

@Service
public class SysRoleMenuServiceImpl extends ServiceImpl<SysRoleMenuMapper, SysRoleMenu> implements SysRoleMenuService {

    @Autowired
    private SysRoleMenuMapper sysRoleMenuMapper;

    @Autowired
    private SysMenuService sysMenuService;

    @Autowired
    private SysRoleService sysRoleService;


    @Override
    @Transactional
    public boolean updateRoleMenus(RoleMenus roleMenus) {
//        if(menuIds.isEmpty()){
//            throw new CommonResponseException(CommonResponseEnum.FAIL,"菜单不能为空");
//        }
        SysRole sysRole = sysRoleService.getById(roleMenus.getRoleId());
        if(Objects.isNull(sysRole)){
            throw new CommonResponseException(CommonResponseEnum.FAIL,"角色不存在");
        }
        /** 传过来的menu id是空，直接移除这个角色下的全部菜单 */
        if (roleMenus.getMenuIds().isEmpty()) {
            remove(Wrappers.lambdaQuery(SysRoleMenu.class).eq(SysRoleMenu::getRoleId, roleMenus.getRoleId()));
            return true;
        }
        //先查询角色原有菜单
        List<Menu> sysMenus = sysMenuService.listAll(roleMenus.getRoleId());
        //遍历所有角色,找到移除的menu和包含的menu
//        Set<Long> menuIdList = CollectionUtil.stringToLongSet(menuIds,",");
        Set<Long> removeRoleMenuIds = new HashSet<>();
        Set<Long> containsMenuIds = new HashSet<>();
        Map<Long, Menu> menuMap = new HashMap();
        sysMenus.stream().forEach(
                sysMenu -> {
                    menuMap.put(sysMenu.getId(), sysMenu);
                    if(sysMenu.getChecked().equals(MenuCheckedEnum.SELECTED.getCode())){
                        if(roleMenus.getMenuIds().contains(sysMenu.getId())){
                            containsMenuIds.add(sysMenu.getId());
                        }else{
                            removeRoleMenuIds.add(sysMenu.getRoleMenuId());
                        }
                    }
                }
        );
        //移除角色已经存在的菜单
        roleMenus.getMenuIds().removeAll(containsMenuIds);
        //删除需要移除的菜单
        if(!removeRoleMenuIds.isEmpty()){
            sysRoleMenuMapper.deleteBatchIds(removeRoleMenuIds);
        }
        //遍历需要保存的菜单
        roleMenus.getMenuIds().stream().forEach(
                menuId -> {
                    if(!menuMap.containsKey(menuId)){
                        throw new CommonResponseException(CommonResponseEnum.FAIL,"菜单不存在，菜单ID:"+menuId);
                    }else{
                        SysRoleMenu sysRoleMenu = new SysRoleMenu();
                        sysRoleMenu.setMenuId(menuId);
                        sysRoleMenu.setRoleId(roleMenus.getRoleId());
                        sysRoleMenuMapper.insert(sysRoleMenu);
                    }
                }
        );
        return true;
    }
}
