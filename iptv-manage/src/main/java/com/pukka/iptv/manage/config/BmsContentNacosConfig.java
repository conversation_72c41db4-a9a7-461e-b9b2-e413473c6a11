package com.pukka.iptv.manage.config;

import lombok.Getter;
import lombok.Setter;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.context.annotation.Configuration;

import java.util.Map;

@Getter
@Setter
@Configuration
@RefreshScope//动态刷新bean
@ConfigurationProperties(prefix = "iptv.bms.content.filter")
public class BmsContentNacosConfig
{
    private Map<String,String> mapping;


}
