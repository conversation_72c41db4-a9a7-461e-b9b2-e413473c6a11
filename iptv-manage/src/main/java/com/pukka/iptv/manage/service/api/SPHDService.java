package com.pukka.iptv.manage.service.api;

import java.util.List;
import java.util.Map;

public interface SPHDService {

    /**
     * @param operator 运营商
     * @return
     */
    String getValidMediaUpFtp(String operator);

    /**
     * @param spIds 分发域
     * @param codes
     * @return
     */
    Map<String, List<?>> validSubscriptionMedia( List<String> spIds, List<String> codes);

    /**
     *
     * @param operator 运营商
     * @return
     */
    String getMediaFtp(String operator);
}
