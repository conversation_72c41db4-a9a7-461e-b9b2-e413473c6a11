package com.pukka.iptv.manage.service.bms.publish;

import cn.hutool.core.util.ObjectUtil;
import com.alibaba.nacos.common.utils.StringUtils;
import com.pukka.iptv.common.api.feign.sys.OutPublishFeignClient;
import com.pukka.iptv.common.base.util.JwtTokenUtil;
import com.pukka.iptv.common.base.vo.CommonResponse;
import com.pukka.iptv.common.base.vo.SecurityUser;
import com.pukka.iptv.common.data.model.OutPublish;
import com.pukka.iptv.manage.service.bms.dto.PublishParam;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.List;
import java.util.Map;

/**
 * @Author: wz
 * @Date: 2021/12/3 16:38
 * @Description:
 */
@Slf4j
public class AbsPublishApi implements PublishApi<Object> {
    @Autowired
    private OutPublishFeignClient outPublishFeignClient;
    private final ThreadLocal<PublishParam> PARAM = new ThreadLocal<>();
    private final ThreadLocal<PublishCallback> CALLBACK = new ThreadLocal<>();


    @Override
    public PublishApi buildParam(PublishParam param) {
        this.PARAM.set(param);
        return this;
    }

    @Override
    public PublishApi publish() {
        PublishParam param = this.PARAM.get();
        List<Long> contentIds = param.contentIds();
        Integer contentType = param.contentTypeEnum().getValue();
        String spId = param.spId();
        String spName = param.spName();
        Map<String, String> picIds = param.picMap();
        Integer action = param.action().getCode();
        String Ids = StringUtils.join(contentIds, ",");
        OutPublish outPublish = new OutPublish();
        outPublish.setContentType(contentType);
        outPublish.setSpId(spId);
        if (StringUtils.isNotEmpty(spName)) {
            outPublish.setSpName(spName);
        }

        SecurityUser securityUser = JwtTokenUtil.getSecurityUser();
        if (ObjectUtil.isNotEmpty(securityUser)) {
            outPublish.setCreatorId(securityUser.getId() + "");
            outPublish.setCreatorName(securityUser.getName());
        }

        outPublish.setContentIds(Ids);
        outPublish.setAction(action);
        CommonResponse<Boolean> result = null;
        try {
            result = outPublishFeignClient.publish(outPublish);
        } catch (Exception e) {
            log.error(e.getMessage(), e);
        } finally {
            try {
                if (result.getData()) {
                    log.info("调用发布接口成功，发布类型：{}，发布媒资类型：{}，spId:{}，contentIds：{}", action, contentType, action, contentIds);
                } else {
                    log.error("调用发布接口失败，发布类型：{}，发布媒资类型：{}，spId:{}，contentIds：{},失败信息:{}", action, contentType, action, contentIds, result.getMessage());
                }
                if (result != null) {
                    PublishCallback callback = this.CALLBACK.get();
                    callback.notify(result);
                }
            } catch (Exception e) {
                log.error(e.getMessage(), e);
            } finally {
                PARAM.remove();
                CALLBACK.remove();
            }
        }
        return this;
    }

    @Override
    public PublishApi<Object> callback(PublishCallback func) {
        this.CALLBACK.set(func);
        return this;
    }


}
