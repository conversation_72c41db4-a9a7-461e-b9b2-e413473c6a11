package com.pukka.iptv.manage.controller.bms;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.pukka.iptv.common.base.vo.CommonResponse;
import com.pukka.iptv.common.data.model.bms.BmsCategoryChannel;
import com.pukka.iptv.common.data.vo.IdList;
import com.pukka.iptv.manage.service.bms.BmsCategoryChannelService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.AllArgsConstructor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.List;


/**
 * 栏目-节目关系表
 *
 * <AUTHOR>
 * @date 2021-09-06 17:05:51
 */

@RestController
@AllArgsConstructor
@RequestMapping(value = "/bmsCategoryChannel", produces = MediaType.APPLICATION_JSON_VALUE)
@Api(tags = "bmsCategoryChannel管理")
public class BmsCategoryChannelController
{


    @Autowired
    private BmsCategoryChannelService bmsCategoryChannelService;

    @ApiOperation(value = "分页")
    @GetMapping("/page")
    public CommonResponse<IPage> page(@Valid Page page, BmsCategoryChannel bmsCategoryChannel)
    {
        return CommonResponse.success(bmsCategoryChannelService.page(page, Wrappers.query(bmsCategoryChannel)));
    }

    @ApiOperation(value = "详情")
    @GetMapping("/getById")
    public CommonResponse<BmsCategoryChannel> getById(@Valid @RequestParam(name = "id", required = true) Long id)
    {
        return CommonResponse.success(bmsCategoryChannelService.getById(id));
    }

    @ApiOperation(value = "新增")
    @PostMapping("/save")
    public CommonResponse<Boolean> save(@Valid @RequestBody BmsCategoryChannel bmsCategoryChannel)
    {
        return CommonResponse.success(bmsCategoryChannelService.save(bmsCategoryChannel));
    }

    @ApiOperation(value = "修改")
    @PutMapping("/update")
    public CommonResponse<Boolean> updateById(@Valid @RequestBody BmsCategoryChannel bmsCategoryChannel)
    {
        return CommonResponse.success(bmsCategoryChannelService.updateById(bmsCategoryChannel));
    }

    @ApiOperation(value = "删除")
    @DeleteMapping("/deleteById")
    public CommonResponse<Boolean> deleteById(@Valid @RequestParam(name = "id", required = true) Long id)
    {
        return CommonResponse.success(bmsCategoryChannelService.removeById(id));
    }

    @ApiOperation(value = "批量删除")
    @DeleteMapping("/deleteByIds")
    public CommonResponse<Boolean> deleteByIds(@Valid @RequestBody IdList idList)
    {
        return CommonResponse.success(bmsCategoryChannelService.removeByIds(idList.getIds()));
    }
    @ApiOperation(value = "获取频道信息")
    @GetMapping("/getByCategoryChanelId" )
    public CommonResponse<List<BmsCategoryChannel>> getByCategoryChanelId(@Valid @RequestParam(name = "id", required = true)  Long id) {
        return  CommonResponse.success(bmsCategoryChannelService.getByCategoryChanelId(id));
    }
}
