package com.pukka.iptv.manage.util;

/**
 * <AUTHOR>
 * @create 2021-09-06 10:50
 */
public class PublishMessageUtil {
    /**
     * 发布状态 1:待发布 2:发布中 3:发布成功 4:发布失败 5:待更新 6:更新中 7:更新失败 8:回收中 9:回收成功 10:回收失败
     */
    public static String checkProcess(String name, int status) {
        String str= "";
        switch (status) {
            case 1:
                str = name+" 为待发布";
                break;
            case 2:
                str = name+" 为发布中";
                break;
            case 3:
                str = name+" 为发布成功";
                break;
            case 4:
                str = name+" 为发布失败";
                break;
            case 5:
                str = name+" 为待更新";
                break;
            case 6:
                str = name+" 为更新中";
                break;
            case 7:
                str = name+" 为更新失败";
                break;
            case 8:
                str = name+" 为回收中";
                break;
            case 9:
                str = name+" 为回收成功";
                break;
            default:
                str = name+" 为回收失败";
        }
        return str;
    }
}
