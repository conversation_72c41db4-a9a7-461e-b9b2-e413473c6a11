package com.pukka.iptv.manage.controller.api;


import com.pukka.iptv.common.api.feign.vcr.VcrMediaAuditFeignClient;
import com.pukka.iptv.common.base.enums.VcrAuditStatusEnums;
import com.pukka.iptv.common.base.vo.CommonResponse;
import com.pukka.iptv.common.data.model.baidu.vcr.VcrAuditSubTaskEntity;
import com.pukka.iptv.common.data.model.baidu.vcr.VcrAuditTransit;
import com.pukka.iptv.common.data.model.baidu.vcr.response.VcrTemplateResponse;
import com.pukka.iptv.common.data.model.cms.CmsProgram;
import com.pukka.iptv.manage.service.cms.CmsProgramService;
import io.swagger.annotations.Api;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @Description 视频审核记录操作类
 * @Date 2024/11/27 09:35
 * @Version V1.0
 **/
@Slf4j
@RestController
@AllArgsConstructor
@RequestMapping(value = "/vcrMediaAuditController", produces = MediaType.APPLICATION_JSON_VALUE)
@Api(tags = "视频审核记录操作类")
public class VcrMediaAuditController {

    @Autowired
    private CmsProgramService cmsProgramService;
    @Autowired
    private VcrMediaAuditFeignClient vcrMediaAuditFeignClient;

    /**
     * 保存视频审核记录
     *
     * @param vcrAuditTransitEntityList 视频审核记录实体列表
     * @return 保存结果，包含操作是否成功的信息
     */
    @PostMapping("/save")
    public CommonResponse<Boolean> saveAuditRecords(@RequestBody @Valid List<VcrAuditTransit> vcrAuditTransitEntityList) {
        Boolean result = cmsProgramService.saveAuditRecords(vcrAuditTransitEntityList);
        if (result) {
            return vcrMediaAuditFeignClient.save(vcrAuditTransitEntityList);
        }
        return CommonResponse.fail("保存视频审核记录失败");
    }

    /**
     * 取消审核任务
     *
     * @param taskId 审核任务ID
     * @return 包含操作结果的通用响应对象
     */
    @PutMapping("/cancelAuditTask")
    public CommonResponse<Boolean> cancelAuditTask(@RequestParam(name = "taskId") String taskId) {
        return vcrMediaAuditFeignClient.cancelAuditTask(taskId);
    }

    /**
     * 根据内容ID获取审核任务
     *
     * @param contentId   内容ID
     * @param contentType 内容类型
     * @return 包含审核任务信息的通用响应对象
     */
    @GetMapping("/getAuditTaskByContentId")
    public CommonResponse<VcrAuditSubTaskEntity> getAuditTaskByContentId(@RequestParam(name = "contentId") String contentId, @RequestParam(name = "contentType") String contentType) {
        return vcrMediaAuditFeignClient.getAuditTaskByContentId(contentId, contentType);
    }

    /**
     * 获取所有模板
     *
     * @return 包含所有模板名称的通用响应对象
     */
    @GetMapping("/getAllTemplates")
    public CommonResponse<List<VcrTemplateResponse>> getAllTemplates() {
        return vcrMediaAuditFeignClient.getAllTemplates();
    }

    /**
     * 修改智审状态
     *
     * @param taskIds
     * @return
     */
    @PutMapping("/updateAuditStatus")
    public CommonResponse<Boolean> updateAuditStatus(@Valid @RequestParam(name = "taskIds") String taskIds) {
        return vcrMediaAuditFeignClient.updateAuditStatus(taskIds);
    }

    /**
     * 更新审核任务备注信息
     *
     * @param taskId 需要更新备注的审核任务ID，通过@RequestParam注解从请求参数中获取，参数名为"taskId"。
     * @param remark 新的备注信息，通过@RequestParam注解从请求参数中获取，参数名为"remark"。
     * @return 返回一个CommonResponse<Boolean>对象，包含更新操作的结果。如果更新成功，CommonResponse的data字段为true；如果更新失败，为false。
     */
    @PutMapping("/updateAuditRemark")
    public CommonResponse<Boolean> updateAuditRemark(@RequestParam(name = "taskId") String taskId, @RequestParam(name = "remark") String remark) {
        return vcrMediaAuditFeignClient.updateAuditRemark(taskId, remark);
    }
}