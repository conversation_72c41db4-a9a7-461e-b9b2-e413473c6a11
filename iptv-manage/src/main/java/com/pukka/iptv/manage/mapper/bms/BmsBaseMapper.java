package com.pukka.iptv.manage.mapper.bms;

import com.pukka.iptv.common.base.enums.PublishStatusEnum;
import com.pukka.iptv.common.base.enums.StatusEnum;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Update;
import org.w3c.dom.stylesheets.LinkStyle;

import java.util.Collection;
import java.util.List;
import java.util.Map;

/**
 * @author: wz
 * @date: 2021/9/8 09:27
 * @description:
 */
@Mapper
public interface BmsBaseMapper {


    @Select({"<script>" +
            "  select `name` from `${t}` where lock_status=${lockStatus} and id in " +
            "<foreach item='i' index='index' collection='ids' open='(' separator=',' close=')'>"
            + "#{i} "
            + "</foreach>" + " limit 1"
            + "</script>"
    })
    String getLockCountByTable(@Param("t") String table, @Param("ids") Collection<Long> ids, @Param("lockStatus") Integer lockStatus);


    //op审核 1：op未审核 2：审核中 3：审核未通过 4：审核通过
    @Select({"<script>" +
            " select `name` from `${t}`" +
            " where  op_check_status !=${opCheckStatus}  and id in " +
            "<foreach item='i' index='index' collection='ids' open='(' separator=',' close=')'>"
            + "#{i} "
            + "</foreach>" + " limit 1"
            + "</script>"
    })
    String getOpCheckStatusCount(@Param("t") String table, @Param("opCheckStatus") Integer code, @Param("ids") Collection<Long> ids);


    /**
     * @param table         表名
     * @param data          主键ids
     * @param publishStatus 发布状态
     * @param ins           true: publish_status in ;false: publish_status not in
     * @return java.lang.Long
     * @description: 查询符合 传入publish_status条件的数据
     * <AUTHOR>
     * @date 2021/9/14 16:27
     */
    @Select({"<script>" +
            " select " +
            // "<if test='colAlias!=null'>CONCAT(`${colAlias}`,`${col}`)</if>" +
            "<if test='colAlias==null'>`${col}`</if>" +
            " , publish_status "+
            " from `${t}`" +
            " where publish_status"
            + "<if test='ins'> in </if>"
            + "<if test='!ins'> not in </if>"
            + "<foreach item='s' index='index' collection='publishStatus' open='(' separator=',' close=')'>"
            + "#{s}"
            + "</foreach>"
            + " and ${id} in " +
            "<foreach item='i' index='index' collection='data' open='(' separator=',' close=')'>"
            + "#{i} "
            + "</foreach>" + " limit 1"
            + "</script>"
    })
    Map<String,Object> getColByPublishStatus(@Param("t") String table, @Param("col") String col,
                                             @Param("colAlias") String alias, @Param("id") String id,
                                             @Param("data") Collection<Long> data,
                                             @Param("publishStatus") Collection<Integer> publishStatus, @Param("ins") Boolean ins);

    /**
     * 设置生效/失效
     *
     * @param table  表名
     * @param ids    主键
     * @param status 状态 0：失效 1：有效
     * @return
     */
    @Update({"<script>" +
            " UPDATE `${t}` a " +
            "SET `status` = #{status.code} ," +
            "a.publish_status =( " +
            " CASE " +
            " WHEN a.publish_status IN ( 9, 4 ) THEN " +
            " 1  " +
            " WHEN a.publish_status IN ( 10, 8, 3, 7 ) THEN " +
            " 5  " +
            " WHEN a.publish_status = 1 THEN " +
            " 1  " +
            " WHEN a.publish_status = 5 THEN " +
            " 5  " +
            " END " +
            " ) " +
            " where id in " +
            "<foreach item='i' index='index' collection='ids' open='(' separator=',' close=')'>"
            + "#{i} "
            + "</foreach>"
            + "</script>"
    })
    int setStatus(@Param("t") String table, @Param("ids") Collection<Long> ids, @Param("status") StatusEnum status);


    @Select({"<script>" +
            " select " +
            // "<if test='colAlias!=null'>CONCAT(`${colAlias}`,`${col}`)</if>" +
            "<if test='colAlias==null'>`${col}`</if>" +
            " from `${t}`" +
            " where "
            + "<if test='ins'> = </if>"
            + "<if test='!ins'> != </if>"
            + "<if test=''> release_status =   and preview_status = </if>"
            + " and ${id} in " +
            "<foreach item='i' index='index' collection='data' open='(' separator=',' close=')'>"
            + "#{i} "
            + "</foreach>" + " limit 1"
            + "</script>"
    })
    String getColByMovieRelation(@Param("t") String table, @Param("col") String col, @Param("colAlias") String alias, @Param("id") String id,
                                 @Param("data") Collection<Long> data,
                                 @Param("publishStatus") Collection<PublishStatusEnum> publishStatus, @Param("ins") Boolean ins);

    @Select("select sequence from `${t}` where category_id =#{categoryId} order by sequence limit 1")
    Integer getSequence(@Param("t") String table,@Param("categoryId") Long categoryId);

    /**
     * 获取启用状态为禁用的sp集合
     * @param table
     * @param code
     * @param ids
     * @return
     */
    @Select({"<script>" +
            " select t.id from sys_sp s,`${t}` t" +
            " where s.id = t.sp_id and s.status !=${spCheckStatus}  and t.id in " +
            "<foreach item='i' index='index' collection='ids' open='(' separator=',' close=')'>"
            + "#{i} "
            + "</foreach>"
            + "</script>"
    })
    List<String> getSpCheckStatusList(@Param("t") String table, @Param("spCheckStatus") Integer code, @Param("ids") Collection<Long> ids);
}
