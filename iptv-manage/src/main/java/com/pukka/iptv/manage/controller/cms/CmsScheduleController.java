package com.pukka.iptv.manage.controller.cms;

import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.pukka.iptv.common.base.exception.CommonResponseException;
import com.pukka.iptv.common.base.vo.CommonResponse;
import com.pukka.iptv.common.data.dto.CmsScheduleDto;
import com.pukka.iptv.common.data.model.bms.BmsSchedule;
import com.pukka.iptv.common.data.model.cms.CmsSchedule;
import com.pukka.iptv.common.data.vo.CodeList;
import com.pukka.iptv.common.data.vo.cms.CmsScheduleVO;
import com.pukka.iptv.common.log.annotation.SysLog;
import com.pukka.iptv.common.log.enums.OperateObjectEnum;
import com.pukka.iptv.common.log.enums.OperateTypeEnum;
import com.pukka.iptv.manage.service.cms.CmsScheduleService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.AllArgsConstructor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.validation.Valid;
import java.util.List;

/**
 * @author: luo
 * @date: 2021-9-15 11:39:55
 */

@RestController
@AllArgsConstructor
@RequestMapping(value = "/cmsSchedule", produces = MediaType.APPLICATION_JSON_VALUE)
@Api(tags = "cmsSchedule管理")
public class CmsScheduleController {

    @Autowired
    private CmsScheduleService cmsScheduleService;


    @ApiOperation(value = "分页")
    @GetMapping("/page")
    public CommonResponse<Page> page(@Valid Page page, CmsScheduleDto cmsScheduleDto) {
        return CommonResponse.success(cmsScheduleService.pageList(page, cmsScheduleDto));
    }

    @SysLog(objectType = OperateObjectEnum.CMS_SCHEDULE, operateType = OperateTypeEnum.SAVE, objectIds = "#cmsScheduleDto.id", objectNames = "#cmsScheduleDto.programName")
    @ApiOperation(value = "节目单新增")
    @PostMapping("/save")
    public CommonResponse<Boolean> save(@RequestBody CmsScheduleDto cmsScheduleDto) {
        cmsScheduleDto.validRequestResource();
        cmsScheduleDto.validScheduleSave();
        return cmsScheduleService.news(cmsScheduleDto);
    }

    @SysLog(objectType = OperateObjectEnum.CMS_SCHEDULE, operateType = OperateTypeEnum.DELETE, objectIds ="#cmsScheduleDto.ids")
    @ApiOperation(value = "批量删除")
    @DeleteMapping("/deleteByIds")
    public CommonResponse<Boolean> deleteByIds(@RequestBody CmsScheduleDto cmsScheduleDto) {
        return cmsScheduleService.del(cmsScheduleDto.getIds());
    }

    @SysLog(objectType = OperateObjectEnum.CMS_SCHEDULE, operateType = OperateTypeEnum.DELETE, objectIds ="#cmsScheduleDto.ids")
    @ApiOperation(value = "批量删除")
    @DeleteMapping("/deleteByIdsAll")
    public CommonResponse<Boolean> deleteByIdsAll(@RequestBody CmsScheduleVO cmsScheduleVO) {
        //todo 节目单批量删除
        return cmsScheduleService.del(cmsScheduleVO.getIds());
    }

    @ApiOperation(value = "导入")
    @PostMapping("/import")
    public CommonResponse<Boolean> importSchedule(@Valid @RequestParam("file") MultipartFile file) {
        return CommonResponse.success(cmsScheduleService.importSchedule(file));
    }

    @ApiOperation(value = "编辑回显")
    @GetMapping("/getById")
    public CommonResponse<CmsSchedule> getById(@Valid @RequestParam(name = "id", required = true) String id) {
        if (ObjectUtil.isEmpty(id)) {
            throw new CommonResponseException("参数不能为空");
        }
        return CommonResponse.success(cmsScheduleService.getById(id));
    }

    @SysLog(objectType = OperateObjectEnum.CMS_SCHEDULE, operateType = OperateTypeEnum.UPDATE, objectIds = "#cmsSchedule.id")
    @ApiOperation(value = "编辑")
    @PutMapping("/update")
    public CommonResponse<Boolean> updateById(@RequestBody CmsScheduleDto cmsSchedule) {
        cmsSchedule.validRequestResource();
        cmsSchedule.validScheduleUpdate();
        return cmsScheduleService.up(cmsSchedule);
    }

}
