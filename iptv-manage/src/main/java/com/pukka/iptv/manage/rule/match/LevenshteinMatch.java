package com.pukka.iptv.manage.rule.match;

/**
 * <AUTHOR>
 * @Description:最小编辑距离
 */
public class LevenshteinMatch {
    /**
     * 获取最小编辑距离静态方法
     *
     * @param str1
     * @param str2
     * @return
     */
    public static int getLevenshtein(String str1, String str2) {
        int[][] array = initArray(str1, str2);
        for (int i = 1; i <= str2.length(); i++) {
            for (int j = 1; j <= str1.length(); j++) {
                setValue(array, i, j, str2.charAt(i - 1) == str1.charAt(j - 1));
            }
        }
        int Levenshtein = array[str2.length()][str1.length()];
        int max = Math.max(str1.length(), str2.length());
        int similarity = 100 - (int) Math.floor(((float) Levenshtein / (float) max) * 100);
        return similarity;
    }

    /**
     * @param array
     * @param i     代表纵向量
     * @param j     代表横向量
     * @param flag  代表字符是否相等
     */
    private static void setValue(int[][] array, int i, int j, boolean flag) {
        int ss = array[i - 1][j - 1];
        int dd = array[i - 1][j] + 1;
        int pp = array[i][j - 1] + 1;
        if (flag) {
            array[i][j] = getMin(ss, dd, pp);
        } else {
            array[i][j] = getMin(ss + 1, dd, pp);
        }
    }

    /**
     * 求三个值中的最小值
     *
     * @param a
     * @param b
     * @param c
     * @return
     */
    private static int getMin(int a, int b, int c) {
        return Math.min(Math.min(a, b), c);
    }


    /**
     * 初始化矩阵
     *
     * @param str1 横坐标代表字符串
     * @param str2 纵坐标代表字符串
     * @return
     */
    private static int[][] initArray(String str1, String str2) {
        int len1 = str1.length();
        int len2 = str2.length();
        int[][] array = new int[len2 + 1][len1 + 1];
        for (int i = 0; i <= len1; i++) {
            array[0][i] = i;
        }
        for (int i = 0; i <= len2; i++) {
            array[i][0] = i;
        }
        return array;
    }
}
