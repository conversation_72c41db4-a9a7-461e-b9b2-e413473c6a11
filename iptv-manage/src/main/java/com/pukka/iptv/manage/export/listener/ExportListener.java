package com.pukka.iptv.manage.export.listener;

import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.ExcelWriter;
import com.alibaba.excel.write.metadata.WriteSheet;
import com.alibaba.excel.write.style.column.LongestMatchColumnWidthStyleStrategy;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.pukka.iptv.common.core.util.SpringUtils;
import com.pukka.iptv.manage.export.model.ExportInfo;
import com.pukka.iptv.manage.util.CloneUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;

import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.CountDownLatch;
import java.util.concurrent.locks.ReentrantLock;
import java.util.stream.IntStream;

/**
 * @Author: wangbo
 * @Date: 2022/4/18 14:20
 */
@Slf4j
public class ExportListener<T> {
    private BaseMapper<T> baseMapper;

    private static ThreadPoolTaskExecutor threadPoolTaskExecutor;

    static {
        threadPoolTaskExecutor = SpringUtils.getBean("exportThreadPool");
    }

    public ExportListener(BaseMapper<T> baseMapper) {
        this.baseMapper = baseMapper;
    }

    ReentrantLock lock = new ReentrantLock();

    private static final int SHEET_SIZE = 5;

    private static final String DATA_FORMAT = "yyyy_MM_dd_HH_mm";

    private static final String CHARACTER_UTF_8 = "UTF-8";

    private static final String CONTENT_TYPE = "application/vnd.ms-excel";

    private static final String CONTENT_DISPOSITION = "Content-Disposition";

    private static final String CACHE_CONTROL = "Cache-Control";

    private static final String NO_STORE = "no-store";

    private static final String MAX_AGE = "max-age=0";

    private static final Integer PAGE_SIZE = 50000;

    private String getLimit(int i) {
        return "limit " + (i * PAGE_SIZE) + "," + (PAGE_SIZE);
    }

    /**
     * 针对QueryWrapper获取结果集进行导出
     *
     * @param exportInfo
     * @throws Exception
     */
    public void exportExcel(ExportInfo<T> exportInfo) throws Exception {
        // 这里 需要指定写用哪个class去写
        ExcelWriter excelWriter = EasyExcel.write(exportInfo.getOut(), exportInfo.getPojoClass()).build();

        // 这里注意 如果同一个sheet只要创建一次
        int totalCount = Math.toIntExact(this.baseMapper.selectCount(exportInfo.getQueryWrapper()));
        //获取到分页数
        int pageNumber = (int) Math.ceil((double) totalCount / (double) PAGE_SIZE);
        CountDownLatch latch = new CountDownLatch(pageNumber);
        // 去调用写入,根据数据库分页的总的页数来
        try {
            for (int i = 0; i < pageNumber; i++) {
                try {
                    int limit = i;
                    threadPoolTaskExecutor.execute(() -> {
                        batchExcel(limit, exportInfo.getQueryWrapper(), excelWriter, exportInfo.getSheetName(), latch);
                    });
                } catch (Exception e) {
                    e.printStackTrace();
                }
            }
        } finally {
            latch.await();
            // 千万别忘记finish 会帮忙关闭流
            excelWriter.finish();
            exportInfo.getOut().flush();
        }
    }

    /**
     * 针对结果集进行导出
     *
     * @param exportInfo
     * @throws Exception
     */
    public void exportResultSetExcel(ExportInfo<T> exportInfo) throws Exception {
        ExcelWriter excelWriter = EasyExcel.write(exportInfo.getOut(), exportInfo.getPojoClass()).build();
        int totalCount = exportInfo.getResultList().size();
        int pageNumber = (int) Math.ceil((double) totalCount / (double) PAGE_SIZE);
        CountDownLatch latch = new CountDownLatch(pageNumber);
        try {
            IntStream.range(0, pageNumber).forEach(i -> {
                try {
                    threadPoolTaskExecutor.execute(() -> {
                        try {
                            WriteSheet writeSheet = null;
                            lock.lock();
                            try {
                                long millis = System.currentTimeMillis();
                                writeSheet = EasyExcel.writerSheet(i, exportInfo.getSheetName() + (i + 1)).head(exportInfo.getPojoClass())
                                        .registerWriteHandler(new LongestMatchColumnWidthStyleStrategy()).build();
                                excelWriter.write(exportInfo.getResultList().subList(i * PAGE_SIZE,
                                        PAGE_SIZE * (i + 1) > exportInfo.getResultList().size() ? exportInfo.getResultList().size() : PAGE_SIZE * (i + 1)), writeSheet);
                                log.info("针对结果集进行导出 -----> 单次写入耗时:{} ms", System.currentTimeMillis() - millis);
                            } finally {
                                lock.unlock();
                            }
                        } finally {
                            latch.countDown();
                        }
                    });
                } catch (Exception exception) {
                    log.error("针对结果集进行导出 -----> 进行数据写入excel失败,错误信息:{}", exception);
                }
            });

        } catch (Exception exception) {
            log.error("针对结果集进行导出 -----> 失败,错误信息:{}", exception);
        } finally {
            latch.await();
            excelWriter.finish();
            exportInfo.getOut().flush();
        }
    }

    public void batchExcel(int i, LambdaQueryWrapper<T> queryWrapper, ExcelWriter excelWriter, String sheetName, CountDownLatch latch) {
        LambdaQueryWrapper<T> lambdaQueryWrapper = CloneUtils.clone(queryWrapper);
        List<T> ts = null;
        try {
            ts = new ArrayList<>();
            lambdaQueryWrapper.last(getLimit(i));
            long mss = System.currentTimeMillis();
            ts = baseMapper.selectList(lambdaQueryWrapper);
            log.info("单次查询耗时：" + (System.currentTimeMillis() - mss) + "ms");
            WriteSheet writeSheet = null;
            lock.lock();
            try {
                long msss = System.currentTimeMillis();
                writeSheet = EasyExcel.writerSheet(i, sheetName + (i + 1)).head(lambdaQueryWrapper.getEntityClass())
                        .registerWriteHandler(new LongestMatchColumnWidthStyleStrategy()).build();
                excelWriter.write(ts, writeSheet);
                log.info("单次写入耗时：" + (System.currentTimeMillis() - msss) + "ms");
            } finally {
                lock.unlock();
            }

        } catch (Exception e) {
            e.printStackTrace();
        } finally {
            ts.clear();
            lambdaQueryWrapper.clear();
            latch.countDown();
        }
    }

     /*   private WriteSheet createSheet(WriteSheet writeSheet, String sheetName, LambdaQueryWrapper<T> lambdaQueryWrapper, int i) {
        int sheet = i / SHEET_SIZE;
        if (i % SHEET_SIZE != 0) {
            return writeSheet;
        }
        return
    }*/
      /*  public void exportExcel(HttpServletResponse response, String sheetName, Class<?> pojoClass,
                       LambdaQueryWrapper<T> queryWrapper, ExportCountInfo exportCountInfo) throws Exception {
        initHttpResponse(response, sheetName);
        ServletOutputStream out = response.getOutputStream();
        List<FutureTask<List<T>>> futureTasks = new ArrayList<FutureTask<List<T>>>();
        int pageSize = (int) Math.ceil((double) exportCountInfo.getCount() / (double) exportCountInfo.getBatchSize());

        for (int i = 0; i < pageSize; i++) {
            int finalI = i;
            FutureTask<List<T>> futureTask = new FutureTask<>(new Callable<List<T>>() {
                @Override
                public List<T> call()   {
                    LambdaQueryWrapper<T> clone = CloneUtils.clone(queryWrapper);
                    clone.apply("id < " + (exportCountInfo.getMostId()-exportCountInfo.getBatchSize()* finalI))
                            .last("limit " + exportCountInfo.getBatchSize());
                    List<T> ts = baseMapper.selectList(clone);
                    return ts;
                }

            });
            futureTasks.add(futureTask);
            threadPoolTaskExecutor.submit(futureTask);
        }
        Set<T> dataSet = new HashSet<>();
        for (FutureTask<List<T>> futureTask : futureTasks)
        {
            List<T> ts = futureTask.get();
            dataSet.addAll(ts);
        }
        ExcelWriter excelWriter = EasyExcel.write(out, pojoClass).build();
        WriteSheet writeSheet;
        writeSheet = EasyExcel.writerSheet(sheetName).head(queryWrapper.getEntityClass())
                .registerWriteHandler(new LongestMatchColumnWidthStyleStrategy()).build();
        List<T> dataList = new ArrayList<>(dataSet);
        excelWriter.write(dataList, writeSheet);
        excelWriter.finish();
        out.flush();
    }
*/
/*    private void initHttpResponse(HttpServletResponse response, String name) throws Exception {

        DateTimeFormatter formatter = DateTimeFormatter.ofPattern(DATA_FORMAT);
        String nowTime = formatter.format(LocalDateTime.now());

        response.setContentType(CONTENT_TYPE);
        //设置字符集为utf-8
        response.setCharacterEncoding(CHARACTER_UTF_8);

        String fileName = name.concat("_").concat(nowTime).concat(".xls");

        //文件名乱码解决：用postman测正常，浏览器多了filename_=utf-8等字样
        response.setHeader(CONTENT_DISPOSITION,
                "attachment;filename=" + URLEncoder.encode(fileName, CHARACTER_UTF_8)
                        + ";filename*=utf-8''" + URLEncoder.encode(fileName, CHARACTER_UTF_8));

        //文件名乱码解决：postman测会乱码，但浏览器下载就正常
//        response.setHeader(CONTENT_DISPOSITION,
//                "attachment;filename=" + URLEncoder.encode(fileName, "UTF-8"));

        //发送一个报头，告诉浏览器当前页面不进行缓存，每次访问的时间必须从服务器上读取最新的数据
        response.setHeader(CACHE_CONTROL, NO_STORE);
        response.addHeader(CACHE_CONTROL, MAX_AGE);
    }*/
}
