package com.pukka.iptv.manage.service.sys.impl;

import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.pukka.iptv.common.base.constant.ObjectsTypeConstants;
import com.pukka.iptv.common.base.constant.RedisKeyConstants;
import com.pukka.iptv.common.base.enums.*;
import com.pukka.iptv.common.base.exception.CommonResponseException;
import com.pukka.iptv.common.base.util.UUID;
import com.pukka.iptv.common.core.constant.SymbolConstant;
import com.pukka.iptv.common.core.util.StringUtils;
import com.pukka.iptv.common.data.dto.DateFormatCompletionDto;
import com.pukka.iptv.common.data.model.*;
import com.pukka.iptv.common.data.model.sys.SysOutPassage;
import com.pukka.iptv.common.data.vo.sys.OutOrderBaseRepublish;
import com.pukka.iptv.common.data.vo.sys.OutOrderBaseVo;
import com.pukka.iptv.common.data.vo.sys.OutOrderItemVo;
import com.pukka.iptv.common.rabbitmq.constans.OutPublishConstant;
import com.pukka.iptv.common.redis.service.RedisService;
import com.pukka.iptv.manage.mapper.sys.*;
import com.pukka.iptv.manage.service.sys.OutOrderBaseService;
import com.pukka.iptv.manage.service.sys.OutOrderItemService;
import com.pukka.iptv.manage.util.CommonUtils;
import com.pukka.iptv.manage.util.TimeResolutionUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.springframework.amqp.rabbit.core.RabbitTemplate;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * <AUTHOR> @Date 2021/9/9 10:20 上午
 * @description: 主工单 @Version 1.0
 */
@Slf4j
@Service
public class OutOrderBaseServiceImpl extends ServiceImpl<OutOrderBaseMapper, OutOrderBase>
        implements OutOrderBaseService {
    @Autowired
    private OutOrderBaseMapper outOrderBaseMapper;
    @Autowired
    private OutOrderBaseService outOrderBaseService;
    @Autowired
    private OutOrderItemService outOrderItemService;
    @Autowired
    private SysOutPassageMapper sysOutPassageMapper;
    @Autowired
    private RedisService redisService;
    @Autowired
    private OutResultMapper outResultMapper;
    @Autowired
    private OutOrderItemMapper outOrderItemMapper;
    @Autowired
    private RabbitTemplate rabbitTemplate;
    @Autowired
    private SummaryMapper summaryMapper;


    /**
     * 子工单重新发布 重新发布默认优先级为 优先
     *
     * @param outOrderBaseRepublish
     * @return
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean orderRepublish(OutOrderBaseRepublish outOrderBaseRepublish) {
        String ids = outOrderBaseRepublish.getIds();
        try {
            if (StringUtils.isEmpty(ids)) {
                log.warn("主工单重新发布 ->>>>> 传入主工单ids有误，主工单ids参数为空");
                return false;
            }
            List<String> idArray = Arrays.asList(ids.split(SymbolConstant.COMMA));
            if (ObjectUtils.isEmpty(idArray)) {
                log.warn("主工单重新发布 ->>>>> 传入主工单ids有误，主工单ids参数为空");
                return false;
            }

            for (String id : idArray) {
                OutOrderBase outBase = outOrderBaseService.getById(id);
                if (ObjectUtils.isEmpty(outBase)) {
                    log.warn("主工单重新发布 ->>>>> 主工单id:{}.获取主工单信息为空！", id);
                    continue;
                }
                String outPassageIds = outBase.getOutPassageIds();
                if (StringUtils.isEmpty(outPassageIds)) {
                    log.warn("主工单重新发布 ->>>>> 主工单id:{}，获取主工单分发通道信息信息为空", id);
                    continue;
                }
                List<String> outPassageArray = Arrays.asList(outPassageIds.split(SymbolConstant.COMMA));
                //是否修改主工单状态
                boolean orderBaseStatus = true;
                for (String outPassageId : outPassageArray) {
                    // 获取通道信息
                    SysOutPassage sysOutPassage =
                            redisService.getCacheMapValue(
                                    RedisKeyConstants.SYS_OUT_PASSAGE, String.valueOf(outPassageId));
                    if (ObjectUtils.isEmpty(sysOutPassage.getCode())) {
                        log.warn("子工单重新发布 ->>>>> 子工单分发通道信息:{} 分发通道信息code为空，重新发布失败！", sysOutPassage);
                        continue;
                    }
                    //20220613 添加判断:筛选失败工单进行重新发布
                    OutOrderItemVo outOrderItemVo =
                            outOrderItemMapper.getOrderItemByBaseOrderId(
                                    SymbolConstant.UNDER_SCORE + sysOutPassage.getCode(),
                                    String.valueOf(outBase.getId()));
                    if (ObjectUtils.isEmpty(outOrderItemVo)) {
                        log.warn("子工单重新发布 ->>>>> 子工单ID:{} 获取子工单为空！", id);
                        continue;
                    }
                    if (ObjectUtils.isEmpty(outOrderItemVo.getCspId())
                            || ObjectUtils.isEmpty(outOrderItemVo.getLspId())
                            || ObjectUtils.isEmpty(outOrderItemVo.getCorrelateId())
                            || ObjectUtils.isEmpty(outOrderItemVo.getCmdFileUrl())) {
                        // 子工单更新状态并发送反馈队列
                        outOrderItemVo.setErrorDescription("子工单必须信息不全,重新发布失败");
                        outOrderItemService.getReportEntity(outOrderItemVo);
                        log.warn("子工单重新发布 ->>>>> 子工单ID:{} 获取子工单必须信息不全，重新发布失败！", id);
                        continue;
                    }
                    //子工单重新发布开始修改主工单状态
                    if (orderBaseStatus && sysOutPassage.getDealReportResult().equals(DealReportResultEnum.DEAL.getCode())) {
                        boolean status = outOrderBaseService.updateBaseOutOrderStatus(
                                new ArrayList<String>() {{
                                    add(id);
                                }},
                                ItemStatusEnum.StayHandle.getValue(),
                                null,
                                "",
                                "");
                        if (status) {
                            orderBaseStatus = false;
                        } else {
                            log.warn("主工单重新发布 ->>>>> 主工单id:{}.重置主工单状态为发布中失败！", id);
                        }
                    }
                    // 业务ID前缀组成，采用雪花算法
                    String correlateId = UUID.nextSnow().toString();
                    outOrderItemVo.setCorrelateId(correlateId);
                    boolean isRepublish =
                            outOrderItemMapper.orderRepublish(
                                    "_" + sysOutPassage.getCode(),
                                    outOrderItemVo.getId(),
                                    outOrderItemVo.getCorrelateId());
                    if (isRepublish) {
                        try {
                            outOrderItemVo.setPath(sysOutPassage.getPath());
                            String routingKey = "";
                            OutOrderBase orderBase = outOrderBaseService.getById(outOrderItemVo.getBaseOrderId());
                            outOrderItemVo.setCpId(orderBase.getCpId());
                            if (ActionEnums.REGIST.getCode().equals(outOrderItemVo.getAction())) {
                                routingKey =
                                        OutPublishConstant.OUT_ORDER_SEND_ROUTING
                                                + SymbolConstant.UNDER_SCORE
                                                + outOrderItemVo.getOutPassageCode()
                                                + ObjectsTypeConstants.REGIST_PRIORITY_QUEUE;
                            } else {
                                routingKey =
                                        OutPublishConstant.OUT_ORDER_SEND_ROUTING
                                                + SymbolConstant.UNDER_SCORE
                                                + outOrderItemVo.getOutPassageCode()
                                                + ObjectsTypeConstants.OTHER_PRIORITY_QUEUE;
                            }
                            if (StringUtils.isEmpty(routingKey)) {
                                // 子工单更新状态并发送反馈队列
                                outOrderItemVo.setErrorDescription("组装队列routingKey为空,重新发布失败");
                                outOrderItemService.getReportEntity(outOrderItemVo);
                                log.warn(
                                        "子工单重新发布 ->>>>> 当前子工单信息,组装队列routingKey为空,子工单发布至MQ任务队列失败!子工单:{}.",
                                        JSON.toJSONString(outOrderItemVo));
                                continue;
                            }
                            rabbitTemplate.convertAndSend(
                                    OutPublishConstant.OUT_EXCHANGE,
                                    routingKey,
                                    outOrderItemVo,
                                    message -> {
                                        message.getMessageProperties().setPriority(PriorityEnums.PRIORITY.getValue());
                                        return message;
                                    });
                        } catch (Exception exception) {
                            log.warn(
                                    "子工单重新发布 ->>>>> 子工单:{}.子工单发布至MQ任务队列失败！错误信息:{}",
                                    JSON.toJSONString(outOrderItemVo),
                                    exception);
                        }

                    } else {
                        // 子工单更新状态并发送反馈队列
                        outOrderItemVo.setErrorDescription("重置子工单状态失败,重新发布失败");
                        outOrderItemService.getReportEntity(outOrderItemVo);
                        log.warn("子工单重新发布 ->>>>> 子工单:{}.重置子工单状态失败！", JSON.toJSONString(outOrderItemVo));
                        continue;
                    }
                }
            }
        } catch (Exception exception) {
            log.error("主工单重新发布 ->>>>> 主工单重新发布失败，错误信息:{}", exception);
            return false;
        }
        log.info("主工单重新发布 ->>>>> 主工单重新发布成功");
        return true;
    }

    /**
     * 主工单信息查询
     *
     * @param contentId   内容id
     * @param contentType 内容类型
     * @return
     */
    @Override
    public List<OutOrderBase> getOrderBaseInfo(String contentId, String contentType) {
        LambdaQueryWrapper<OutOrderBase> outOrderBaseQueryWrapper =
                Wrappers.lambdaQuery(OutOrderBase.class)
                        .eq(Objects.nonNull(contentId), OutOrderBase::getBmsContentId, contentId)
                        .eq(Objects.nonNull(contentType), OutOrderBase::getContentType, contentType)
                        .orderByDesc(OutOrderBase::getCreateTime);
        List<OutOrderBase> outOrderBases = outOrderBaseMapper.selectList(outOrderBaseQueryWrapper);
        return outOrderBases;
    }


    /**
     * 主工单分页查询 20220308 加入结果查询
     *
     * @param page
     * @param outOrderBaseVo
     * @return
     */
    @Override
    public IPage<OutOrderBase> listByOutOrderBaseProperty(
            Page<OutOrderBase> page, OutOrderBaseVo outOrderBaseVo) {
        // 开始、结束时间格式化
        DateFormatCompletionDto dateFormatCompletionDto =
                new DateFormatCompletionDto()
                        .setStartTime(outOrderBaseVo.getStartTime())
                        .setEndTime(outOrderBaseVo.getEndTime());
        Boolean aBoolean = TimeResolutionUtil.dateTimeCompare(dateFormatCompletionDto);
        // 发布时间的开始、结束时间格式化
        DateFormatCompletionDto launchDateFormat =
                new DateFormatCompletionDto()
                        .setStartTime(outOrderBaseVo.getStartLaunchTime())
                        .setEndTime(outOrderBaseVo.getEndLaunchTime());
        TimeResolutionUtil.dateTimeCompare(launchDateFormat);
        // size等于-1时不分页
        page.setSize(page.getSize() == -1 ? Integer.MAX_VALUE : page.getSize());
        LambdaQueryWrapper<OutOrderBase> lambdaQueryWrapper =
                Wrappers.lambdaQuery(OutOrderBase.class)
                        .eq(
                                ObjectUtils.isNotEmpty(outOrderBaseVo.getAction()),
                                OutOrderBase::getAction,
                                outOrderBaseVo.getAction())
                        .like(
                                ObjectUtils.isNotEmpty(outOrderBaseVo.getCreatorName()),
                                OutOrderBase::getCreatorName,
                                outOrderBaseVo.getCreatorName())
                        .eq(
                                ObjectUtils.isNotEmpty(outOrderBaseVo.getCreateTime()),
                                OutOrderBase::getCreateTime,
                                outOrderBaseVo.getCreateTime())
                        .eq(
                                ObjectUtils.isNotEmpty(outOrderBaseVo.getStatus()),
                                OutOrderBase::getStatus,
                                outOrderBaseVo.getStatus())
                        .eq(
                                ObjectUtils.isNotEmpty(outOrderBaseVo.getBmsSpChannelId()),
                                OutOrderBase::getBmsSpChannelId,
                                outOrderBaseVo.getBmsSpChannelId())
                        .eq(
                                ObjectUtils.isNotEmpty(outOrderBaseVo.getSpId()),
                                OutOrderBase::getSpId,
                                outOrderBaseVo.getSpId())
                        .eq(
                                ObjectUtils.isNotEmpty(outOrderBaseVo.getCpId()),
                                OutOrderBase::getCpId,
                                outOrderBaseVo.getCpId())
                        .eq(
                                ObjectUtils.isNotEmpty(outOrderBaseVo.getOrderCorrelateId()),
                                OutOrderBase::getOrderCorrelateId,
                                outOrderBaseVo.getOrderCorrelateId())
                        .eq(
                                ObjectUtils.isNotEmpty(outOrderBaseVo.getOrderType()),
                                OutOrderBase::getOrderType,
                                outOrderBaseVo.getOrderType())
                        .eq(
                                ObjectUtils.isNotEmpty(outOrderBaseVo.getResult()),
                                OutOrderBase::getResult,
                                outOrderBaseVo.getResult())
                        .isNotNull(NullEnum.NOT_NULL.getValue().equals(outOrderBaseVo.getFirstLaunchTimeIsNull()),
                                OutOrderBase::getFirstLaunchTime)

                        .isNull(NullEnum.NULL.getValue().equals(outOrderBaseVo.getFirstLaunchTimeIsNull()),
                                OutOrderBase::getFirstLaunchTime);
        //名称搜索
        if (outOrderBaseVo.getShowName() != null) {
            if (outOrderBaseVo.getShowName().contains("\n")) {
                //防止name为空字符串以及只有回车的情况  进行再一次校验
                outOrderBaseVo.setShowName(outOrderBaseVo.getShowName().trim());
                if (ObjectUtils.isNotEmpty(outOrderBaseVo.getShowName())) {
                    String[] names = CommonUtils.getNames(outOrderBaseVo.getShowName());
                    if (names != null) {
                        lambdaQueryWrapper.in(OutOrderBase::getShowName, Arrays.asList(names));
                    }
                }
            } else {
                String[] names = CommonUtils.getNames(outOrderBaseVo.getShowName());
                if (names != null && names.length > 0) {
                    lambdaQueryWrapper.like(OutOrderBase::getShowName, names[0]);
                }
            }
        }
        //枚举类型为关系时
        if (ObjectUtils.isNotEmpty(outOrderBaseVo.getContentType()) && outOrderBaseVo.getContentType().equals(ContentTypeEnum.RELATIONSHIP.getValue())) {
            log.info("开始进行枚举转换");
            lambdaQueryWrapper.in(OutOrderBase::getContentType, new ArrayList<String>() {
                {
                    add(String.valueOf(ContentTypeEnum.CATEGORY_PROGRAM.getValue()));
                    add(String.valueOf(ContentTypeEnum.CATEGORY_SERIES.getValue()));
                    add(String.valueOf(ContentTypeEnum.CATEGORY_CHANNEL.getValue()));
                    add(String.valueOf(ContentTypeEnum.PACKAGE_PROGRAM.getValue()));
                    add(String.valueOf(ContentTypeEnum.PACKAGE_SERIES.getValue()));
                }
            });
        } else {
            lambdaQueryWrapper.eq(
                    ObjectUtils.isNotEmpty(outOrderBaseVo.getContentType()),
                    OutOrderBase::getContentType,
                    outOrderBaseVo.getContentType());
        }
        lambdaQueryWrapper
                .ge(
                        ObjectUtils.isNotEmpty(dateFormatCompletionDto.getStartTime()),
                        OutOrderBase::getCreateTime,
                        dateFormatCompletionDto.getStartTime())
                .le(
                        ObjectUtils.isNotEmpty(dateFormatCompletionDto.getEndTime()),
                        OutOrderBase::getCreateTime,
                        dateFormatCompletionDto.getEndTime());
        lambdaQueryWrapper
                .ge(
                        ObjectUtils.isNotEmpty(launchDateFormat.getStartTime()),
                        OutOrderBase::getFirstLaunchTime,
                        launchDateFormat.getStartTime())
                .le(
                        ObjectUtils.isNotEmpty(launchDateFormat.getEndTime()),
                        OutOrderBase::getFirstLaunchTime,
                        launchDateFormat.getEndTime());
        //全表查询时从缓存中获取总数
        if (lambdaQueryWrapper.isEmptyOfWhere()) {
            page.setSearchCount(false);
            Summary summary = summaryMapper.selectOne(
                    Wrappers.lambdaQuery(Summary.class)
                            .select(Summary::getOutOrderCount)
                            .orderByDesc(Summary::getId)
                            .last("limit 1"));
            page.setTotal(ObjectUtils.isNotEmpty(summary) ? summary.getOutOrderCount() : 0L);
        }
        return outOrderBaseService.page(
                page, lambdaQueryWrapper.orderByDesc(OutOrderBase::getCreateTime));
    }

    /**
     * 主工单分页查询
     *
     * @param page
     * @param outOrderBase
     * @return
     */
    @Override
    public IPage<OutOrderBase> page(Page<OutOrderBase> page, OutOrderBase outOrderBase) {
        OutOrderBaseVo outOrderBaseVo = new OutOrderBaseVo();
        BeanUtils.copyProperties(outOrderBase, outOrderBaseVo);
        Page<OutOrderBase> outOrderBasePage = new Page<>();
        page.setSize(page.getSize() == -1 ? Integer.MAX_VALUE : page.getSize());
        page.setCurrent(page.getCurrent() == -1 ? 1 : page.getCurrent());
        try {
            //时间格式化
            DateFormatCompletionDto dateFormatCompletionDto =
                    new DateFormatCompletionDto()
                            .setStartTime(outOrderBaseVo.getStartTime())
                            .setEndTime(outOrderBaseVo.getEndTime());
            TimeResolutionUtil.dateTimeCompare(dateFormatCompletionDto);
            //校验实体类
            boolean checkOutOrderBase = checkOutOrderBase(outOrderBaseVo);
            //名称处理
            if (!StringUtils.isEmpty(outOrderBaseVo.getShowName())) {
                if (outOrderBaseVo.getShowName().contains("\n")) {
                    //防止name为空字符串以及只有回车的情况  进行再一次校验
                    outOrderBaseVo.setShowName(outOrderBaseVo.getShowName().trim());
                    if (StringUtils.isNotEmpty(outOrderBaseVo.getShowName())) {
                        String[] names = CommonUtils.getNames(outOrderBaseVo.getShowName());
                        if (names.length > 0) {
                            outOrderBaseVo.setNameList(names);
                            outOrderBaseVo.setLikeOrinFlag(2);
                        }
                    }
                } else {
                    String[] names = CommonUtils.getNames(outOrderBaseVo.getShowName());
                    if (names != null && names.length > 0) {
                        outOrderBaseVo.setNameList(names);
                        outOrderBaseVo.setNameLike(names[0]);
                        outOrderBaseVo.setLikeOrinFlag(1);
                    }
                }
            }
            //查询数据库
            List<OutOrderBase> outOrderArray = outOrderBaseMapper.getPage(outOrderBaseVo, dateFormatCompletionDto.getStartTime(), dateFormatCompletionDto.getEndTime(),
                    (page.getCurrent() - 1) * page.getSize(), page.getSize());

            if (ObjectUtils.isNotEmpty(outOrderArray)) {
                long pageSize;
                if (checkOutOrderBase) {
                    Summary summary = summaryMapper.selectOne(
                            Wrappers.lambdaQuery(Summary.class)
                                    .select(Summary::getOutOrderCount)
                                    .orderByDesc(Summary::getId)
                                    .last("limit 1")
                    );
                    long outOrderCount = ObjectUtils.isNotEmpty(summary) ? summary.getOutOrderCount() : 0L;
                    outOrderBasePage.setTotal(outOrderCount);
                    if (outOrderCount == 0L) {
                        pageSize = outOrderBaseMapper.getPageSize(outOrderBaseVo, dateFormatCompletionDto.getStartTime(), dateFormatCompletionDto.getEndTime());
                        outOrderBasePage.setTotal(ObjectUtils.isNotEmpty(pageSize) ? pageSize : 0L);
                    }
                } else {
                    pageSize = outOrderBaseMapper.getPageSize(outOrderBaseVo, dateFormatCompletionDto.getStartTime(), dateFormatCompletionDto.getEndTime());
                    outOrderBasePage.setTotal(ObjectUtils.isNotEmpty(pageSize) ? pageSize : 0L);
                }
            }
            outOrderBasePage.setRecords(outOrderArray);
            outOrderBasePage.setSize(page.getSize());
            outOrderBasePage.setCurrent(page.getCurrent());
        } catch (Exception exception) {
            log.error("主工单分页查询异常,错误信息:{}", exception.getMessage());
            throw new CommonResponseException("主工单分页查询异常");
        }
        return outOrderBasePage;
    }

    /**
     * 校验实体字段是否赋值
     *
     * @param outOrderBaseVo
     */
    private boolean checkOutOrderBase(OutOrderBaseVo outOrderBaseVo) {
        return Stream.of(
                        outOrderBaseVo.getContentType(),
                        outOrderBaseVo.getShowName(),
                        outOrderBaseVo.getStartTime(),
                        outOrderBaseVo.getEndTime(),
                        outOrderBaseVo.getAction(),
                        outOrderBaseVo.getCreatorName(),
                        outOrderBaseVo.getCreateTime(),
                        outOrderBaseVo.getStatus(),
                        outOrderBaseVo.getBmsSpChannelId(),
                        outOrderBaseVo.getSpId(),
                        outOrderBaseVo.getCpId(),
                        outOrderBaseVo.getOrderCorrelateId(),
                        outOrderBaseVo.getOrderType(),
                        outOrderBaseVo.getResult()
                )
                .allMatch(ObjectUtils::isEmpty);
    }


    /**
     * 明细查看
     *
     * @param page
     * @param outOrderItem
     * @return
     */
    @Override
    public IPage<OutOrderItemVo> getOrderItemInfo(
            IPage<OutOrderItem> page, OutOrderItem outOrderItem) {
        Page<OutOrderItemVo> outOrderItemPage = new Page<>();
        try {
            if (StringUtils.isEmpty(outOrderItem.getBaseOrderId())) {
                log.warn("传入关联主工单id为空，未查询到相关数据");
                return null;
            }
            // 获取子工单频道信息
            SysOutPassage sysOutPassage = sysOutPassageMapper.selectById(outOrderItem.getOutPassageId());
            if (sysOutPassage == null || StringUtils.isEmpty(sysOutPassage.getCode())) {
                log.warn("传入分发通道id有误，未查询到相关数据");
                return null;
            }
            String code = "_" + sysOutPassage.getCode();
            // size等于-1时不分页
            page.setSize(page.getSize() == -1 ? Integer.MAX_VALUE : page.getSize());
            page.setCurrent(page.getCurrent() == -1 ? 1 : page.getCurrent());
            List<OutOrderItemVo> outOrderItemVoList =
                    outOrderBaseMapper.findListByPage(
                            code,
                            outOrderItem.getBaseOrderId(),
                            outOrderItem.getAction(),
                            outOrderItem.getStatus(),
                            (page.getCurrent() - 1) * page.getSize(),
                            page.getSize());
            if (ObjectUtils.isNotEmpty(outOrderItemVoList)) {
                outOrderItemVoList.stream()
                        .filter(item -> ObjectUtils.isNotEmpty(item.getResult()))
                        .forEach(
                                item -> {
                                    OutResult byCorrelateId =
                                            outResultMapper.findByCorrelateId(item.getCorrelateId());
                                    if (ObjectUtils.isNotEmpty(byCorrelateId)
                                            && StringUtils.isNotEmpty(byCorrelateId.getResultFileUrl())) {
                                        item.setResultFileUrl(byCorrelateId.getResultFileUrl());
                                    }
                                });
                outOrderItemPage.setRecords(outOrderItemVoList);
                outOrderItemPage.setSize(page.getSize());
                outOrderItemPage.setCurrent(page.getCurrent());
                long total =
                        outOrderBaseMapper.findList(
                                code,
                                outOrderItem.getBaseOrderId(),
                                outOrderItem.getAction(),
                                outOrderItem.getStatus());
                outOrderItemPage.setTotal(total);
            }
        } catch (Exception exception) {
            log.error("子工单信息分页查询失败：", exception);
        }
        return outOrderItemPage;
    }

    /**
     * 更新主工单状态
     *
     * @param baseOutOrderIds
     * @param status
     * @param result
     * @param statusDscription
     * @param errorDescription
     * @return
     */
    @Override
    public Boolean updateBaseOutOrderStatus(
            List<String> baseOutOrderIds,
            Integer status,
            Integer result,
            String statusDscription,
            String errorDescription) {
        Boolean flag = false;
        for (String id : baseOutOrderIds) {
            flag =
                    outOrderBaseService.update(
                            Wrappers.lambdaUpdate(OutOrderBase.class)
                                    .set(OutOrderBase::getStatus, status)
                                    .set(OutOrderBase::getResult, result)
                                    .set(OutOrderBase::getStatusDescription, statusDscription)
                                    .set(OutOrderBase::getErrorDescription, errorDescription)
                                    .eq(OutOrderBase::getId, id));
        }
        return flag;
    }

    /**
     * 根据关联ID查询主任务
     *
     * @param correlateId
     * @return
     */
    @Override
    public List<OutOrderBase> getOrderByCorrelateId(String correlateId) {
        return outOrderBaseMapper.selectList(
                Wrappers.lambdaQuery(OutOrderBase.class)
                        .eq(OutOrderBase::getOrderCorrelateId, correlateId));
    }

    /**
     * 查询物理频道id信息
     *
     * @param baseOrderId
     * @return
     */
    @Override
    public List<Long> getPhysicalChannelIds(String baseOrderId) {
        OutOrderBase outOrderBase =
                outOrderBaseMapper.selectOne(
                        Wrappers.lambdaQuery(OutOrderBase.class).eq(OutOrderBase::getId, baseOrderId));
        OutParamExpand outParamExpand =
                JSON.parseObject(outOrderBase.getBmsRelations(), OutParamExpand.class);
        Map<String, String> outParamExpandSpareMap = outParamExpand.getSpareMap();
        List<String> result = new ArrayList<String>(outParamExpandSpareMap.keySet());
        List<Long> longList = result.stream().map(Long::parseLong).collect(Collectors.toList());
        return longList;
    }

    /**
     * 查询节目单id信息
     *
     * @param baseOrderId
     * @return
     */
    @Override
    public List<Long> getScheduleIds(String baseOrderId) {
        OutOrderBase outOrderBase =
                outOrderBaseMapper.selectOne(
                        Wrappers.lambdaQuery(OutOrderBase.class).eq(OutOrderBase::getId, baseOrderId));
        OutParamExpand outParamExpand =
                JSON.parseObject(outOrderBase.getBmsRelations(), OutParamExpand.class);
        Map<String, String> outParamExpandSpareMap = outParamExpand.getSpareMap();
        List<String> result = new ArrayList<String>(outParamExpandSpareMap.keySet());
        List<Long> longList = result.stream().map(Long::parseLong).collect(Collectors.toList());
        return longList;
    }

    @Override
    public List<Long> getSubsetIds(String baseOrderId) {
        OutOrderBase outOrderBase =
                outOrderBaseMapper.selectOne(
                        Wrappers.lambdaQuery(OutOrderBase.class).eq(OutOrderBase::getId, baseOrderId));
        OutParamExpand outParamExpand =
                JSON.parseObject(outOrderBase.getBmsRelations(), OutParamExpand.class);
        Map<String, String> outParamExpandSpareMap = outParamExpand.getSpareMap();
        List<String> result = new ArrayList<String>(outParamExpandSpareMap.keySet());
        List<Long> longList = result.stream().map(Long::parseLong).collect(Collectors.toList());
        return longList;
    }

    @Override
    public List<OutOrderBase> getOrderBaseList(List<Long> idList, String contentType, Integer action) {
        return outOrderBaseMapper.selectLatestForEachBmsContentId(idList, contentType, action,Arrays.asList(ResultStatusEnum.SUCCESS.getCode(), ResultStatusEnum.FAIL.getCode()));
    }

}
