package com.pukka.iptv.manage.service.in.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.pukka.iptv.common.data.model.in.InCategory;
import com.pukka.iptv.manage.mapper.in.InCategoryMapper;
import com.pukka.iptv.manage.service.in.InCategoryService;
import org.springframework.stereotype.Service;


/**
 *
 * @author: tan
 * @date: 2021-8-24 10:43:49
 */

@Service
public class InCategoryServiceImpl extends ServiceImpl<InCategoryMapper, InCategory> implements InCategoryService {

}


