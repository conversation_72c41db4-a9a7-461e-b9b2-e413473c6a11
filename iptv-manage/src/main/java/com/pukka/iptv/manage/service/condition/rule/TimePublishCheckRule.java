package com.pukka.iptv.manage.service.condition.rule;


import cn.hutool.core.collection.CollectionUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.pukka.iptv.common.base.enums.IsTimedEnums;
import com.pukka.iptv.common.core.util.SpringUtils;
import com.pukka.iptv.common.data.model.bms.*;
import com.pukka.iptv.manage.mapper.bms.*;
import com.pukka.iptv.manage.service.condition.AbstractRule;
import com.pukka.iptv.manage.service.condition.BaseRule;
import com.pukka.iptv.manage.service.condition.RuleResult;
import com.pukka.iptv.manage.service.condition.rule.enums.MovieCheck;
import lombok.Setter;

import java.util.Collection;

import static com.pukka.iptv.manage.service.condition.RuleResult.fail;


/**
 * @author: wz
 * @date: 2021/9/7 18:57
 * @description: 定时发布检查
 */
@Setter
public class TimePublishCheckRule<T> extends AbstractRule<T, MovieCheck> {

    protected TimePublishCheckRule(Class<T> clazz) {
        super(clazz);
    }

    public static <T> TimePublishCheckRule<T> init(Class<T> clazz) {
        return new TimePublishCheckRule<>(clazz);
    }


    //查找是否有没有关联视频的记录
    @Override
    public RuleResult execute() {
        //获取要查询的数据
        Collection<Long> data = getData();
        //为空不检查
        if (CollectionUtil.isEmpty(data)) return RuleResult.ok();
        Class<T> clazz = getTableClass();
        //子集
        if (BmsProgram.class.equals(clazz)) {
            BmsProgramMapper programMapper = SpringUtils.getBean(BmsProgramMapper.class);
            BmsProgram bmsProgram = programMapper.selectOne(Wrappers.lambdaQuery(BmsProgram.class)
                    .select(BmsProgram::getName)
                    .eq(BmsProgram::getTimedPublishStatus, IsTimedEnums.IS_TIMED.getCode())
                    .in(BmsProgram::getId, data)
                    .last(" limit 1 "));
            if (bmsProgram != null) {
                return fail(bmsProgram.getName() + "设定了定时发布状态，不能操作!", "");
            }
            //剧集和单集
        } else if (BmsContent.class.equals(clazz)) {
            BmsContentMapper mapper = SpringUtils.getBean(BmsContentMapper.class);
            BmsContent bmsContent = mapper.selectOne(Wrappers.lambdaQuery(BmsContent.class)
                    .select(BmsContent::getName)
                    .eq(BmsContent::getTimedPublishStatus, IsTimedEnums.IS_TIMED.getCode())
                    .in(BmsContent::getId, data)
                    .last(" limit 1 "));
            if (bmsContent != null) {
                return fail(bmsContent.getName() + "设定了定时发布状态，不能操作", "");
            }
            //栏目内容关系定时发布
        } else if (BmsCategoryContent.class.equals(clazz)) {
            BmsCategoryContentMapper mapper = SpringUtils.getBean(BmsCategoryContentMapper.class);
            BmsCategoryContent bcc = mapper.selectOne(Wrappers.lambdaQuery(BmsCategoryContent.class)
                    .select(BmsCategoryContent::getCategoryName)
                    .eq(BmsCategoryContent::getTimedPublishStatus, IsTimedEnums.IS_TIMED.getCode())
                    .in(BmsCategoryContent::getId, data)
                    .last(" limit 1 "));
            if (bcc != null) {
                return fail("栏目名称 为:" + bcc.getCategoryName() + "的关系设定了定时发布状态，不能操作", "");
            }
            //产品包内容关系定时发布
        } else if (BmsPackageContent.class.equals(clazz)) {
            BmsPackageContentMapper mapper = SpringUtils.getBean(BmsPackageContentMapper.class);
            BmsPackageContent bcc = mapper.selectOne(Wrappers.lambdaQuery(BmsPackageContent.class)
                    .select(BmsPackageContent::getPackageName)
                    .eq(BmsPackageContent::getTimedPublishStatus, IsTimedEnums.IS_TIMED.getCode())
                    .in(BmsPackageContent::getId, data)
                    .last(" limit 1 "));
            if (bcc != null) {
                return fail("产品包名称 为:" + bcc.getPackageName() + "的关系设定了定时发布状态，不能操作", "");
            }
        } else if(BmsSchedule.class.equals(clazz))
        {
            BmsScheduleMapper mapper = SpringUtils.getBean(BmsScheduleMapper.class);
            BmsSchedule bcc = mapper.selectOne(Wrappers.lambdaQuery(BmsSchedule.class)
                    .select(BmsSchedule::getProgramName)
                    .eq(BmsSchedule::getTimedStatus, IsTimedEnums.IS_TIMED.getCode())
                    .in(BmsSchedule::getId, data)
                    .last(" limit 1 "));
            if (bcc != null) {
                return fail(bcc.getProgramName() + "设定了定时发布状态，不能操作", "");
            }
        }
        return RuleResult.ok();
    }

    @Override
    public BaseRule wrapper(QueryWrapper wrapper) {
        return this;
    }


}
