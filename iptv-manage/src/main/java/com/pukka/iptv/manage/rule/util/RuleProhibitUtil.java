package com.pukka.iptv.manage.rule.util;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.pukka.iptv.common.base.enums.DictionaryBaseEnums;
import com.pukka.iptv.common.data.model.sys.SysDictionaryBase;
import com.pukka.iptv.common.data.model.sys.SysDictionaryItem;
import com.pukka.iptv.common.data.vo.copyright.RuleProhibitVo;
import com.pukka.iptv.manage.service.sys.SysDictionaryBaseService;
import com.pukka.iptv.manage.service.sys.SysDictionaryItemService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * @Author: chiron
 * @Date: 2022/08/24/17:09
 * @Description:
 */
@Slf4j
@Component
public class RuleProhibitUtil {

    @Autowired
    private SysDictionaryItemService sysDictionaryItemService;

    @Autowired
    private SysDictionaryBaseService sysDictionaryBaseService;

    /**
     * 违禁规则实体信息补全
     *
     * @param ruleProhibitVo
     * @return
     */
    public Boolean ruleProhibitDataCompletion(RuleProhibitVo ruleProhibitVo) {
        if (ObjectUtils.isEmpty(ruleProhibitVo)) {
            return true;
        }
        try {
            //媒资分类id为空，媒资分类不为空
            if (ObjectUtils.isEmpty(ruleProhibitVo.getPgmCategoryId()) && StringUtils.isNotEmpty(ruleProhibitVo.getPgmCategory())) {
                SysDictionaryBase sysDictionaryBase = sysDictionaryBaseService.getOne(Wrappers.<SysDictionaryBase>lambdaQuery().select(SysDictionaryBase::getId).eq(SysDictionaryBase::getCode, DictionaryBaseEnums.FIRST_CLASS.getValue()));
                SysDictionaryItem sysDictionaryItem = sysDictionaryItemService.getOne(Wrappers.<SysDictionaryItem>lambdaQuery()
                        .select(SysDictionaryItem::getId, SysDictionaryItem::getName)
                        .eq(SysDictionaryItem::getDictionaryBaseId, sysDictionaryBase.getId())
                        .eq(SysDictionaryItem::getName, ruleProhibitVo.getPgmCategory())
                        .last("limit 1")
                );
                ruleProhibitVo.setPgmCategoryId(sysDictionaryItem.getId());
            }
            if (ObjectUtils.isEmpty(ruleProhibitVo.getOriginalCountryId()) && StringUtils.isNotEmpty(ruleProhibitVo.getOriginalCountry())) {
                SysDictionaryBase sysDictionaryBase = sysDictionaryBaseService.getOne(Wrappers.<SysDictionaryBase>lambdaQuery().select(SysDictionaryBase::getId).eq(SysDictionaryBase::getCode, DictionaryBaseEnums.ORIGINAL_COUNTRY.getValue()));
                SysDictionaryItem sysDictionaryItem = sysDictionaryItemService.getOne(Wrappers.<SysDictionaryItem>lambdaQuery()
                        .select(SysDictionaryItem::getId, SysDictionaryItem::getName)
                        .eq(SysDictionaryItem::getDictionaryBaseId, sysDictionaryBase.getId())
                        .eq(SysDictionaryItem::getName, ruleProhibitVo.getOriginalCountry())
                        .last("limit 1")
                );
                ruleProhibitVo.setOriginalCountryId(sysDictionaryItem.getId());
            }
        } catch (Exception exception) {
            log.error("违禁规则实体信息补全 -----> 失败,错误信息:{}", exception);
        }
        return true;
    }
}
