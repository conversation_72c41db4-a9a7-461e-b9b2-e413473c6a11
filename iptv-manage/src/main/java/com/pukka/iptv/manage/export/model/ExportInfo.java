package com.pukka.iptv.manage.export.model;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

import java.io.ByteArrayOutputStream;
import java.util.List;

/**
 * @Author: wangbo
 * @Date: 2022/4/18 15:25
 */
@Getter
@Setter
@Accessors(chain = true)
public class ExportInfo<T> {
    /**
     * 字节流输出
     */
    private ByteArrayOutputStream out;
    /**
     * sheet名称
     */
    private String sheetName;
    /**
     * excel名称
     */
    private String excelName;
    /**
     * 操作类
     */
    private Class<?> pojoClass;
    /**
     * mybatis-puls数据操作
     */
    private LambdaQueryWrapper<T> queryWrapper;
    /**
     * mapper
     */
    private BaseMapper<T> baseMapper;
    /**
     * 唯一键
     */
    private String cacheKey;
    /**
     * 结果集
     */
    private List<T> resultList;
}
