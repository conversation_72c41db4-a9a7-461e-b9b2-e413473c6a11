package com.pukka.iptv.manage.util;


import lombok.extern.slf4j.Slf4j;
import org.apache.logging.log4j.util.Strings;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.time.*;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.regex.Matcher;
import java.util.regex.Pattern;


/**
 *
 * <p>日期帮助类</p>
 * <AUTHOR> @创建时间：2022-07-22
 * @修改历史：
 * @修改内容:
 * @修改时间:
 *
 */
@Slf4j
public class DateUtils extends org.apache.commons.lang3.time.DateUtils {

    public static final String YYYY = "yyyy";
    public static final String YYYYMM = "yyyy-MM";
    public static final String YYYYMMDD = "yyyy-MM-dd";
    public static final String YYYYMMDDHHMMSS = "yyyy-MM-dd HH:mm:ss";
    public static final String BASE_DATE = "yyyyMMddHHmmss";
    public static final String HHMMSS="HH:mm:ss";
    public static final String YYYYMMDD_NO_SPLIT = "yyyyMMdd";

    /**日*/
    private static final String DAY_PATTERN = "\\d{4}-\\d{2}-\\d{2}";
    /**月*/
    private static final String MONTH_PATTERN = "\\d{4}-\\d{2}";
    /**季*/
    private static final String SEASON_PATTERN = "\\d{4}-\\d{1}";
    /**年*/
    private static final String YEAR_PATTERN = "\\d{4}";

    /**
     * 获取当前时间到凌晨的时间差 单位:秒
     * @return
     */
    public static long timeOut() {
        LocalDate today = LocalDate.now();
        LocalDateTime todayMidnight = LocalDateTime.of(today, LocalTime.MIDNIGHT);
        LocalDateTime tomorrowMidnight = todayMidnight.plusDays(1);
        long seconds = TimeUnit.NANOSECONDS.toSeconds(Duration.between(LocalDateTime.now(), tomorrowMidnight).toNanos());
        return seconds;
    }

    /**
     * 获取当前时间到凌晨的时间差 单位:秒
     * @return
     */
    public static long timeAndTimeOut(String start,String end) {
        DateTimeFormatter fmt = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
        LocalDateTime startTime = LocalDateTime.parse(start, fmt);
        LocalDateTime endTime = LocalDateTime.parse(end, fmt);
        long seconds = TimeUnit.NANOSECONDS.toSeconds(Duration.between(startTime, endTime).toNanos());
        return seconds;
    }

    /**
     * 获取当前时间afterDay之后的时间
     * @param afterDay
     * @return
     */
    public static String getAfterDay(LocalDateTime dateTime,Long afterDay){
        String formatTime = "";
        if(null == afterDay){
            return formatTime;
        }
        try{
            LocalDateTime now = null == dateTime?LocalDateTime.now().plusDays(afterDay):dateTime.plusDays(afterDay);
            DateTimeFormatter dateTimeFormatter = DateTimeFormatter.ofPattern(DateUtils.YYYYMMDDHHMMSS);
            formatTime = dateTimeFormatter.format(now);
        }catch (Exception e){
            e.printStackTrace();
        }
        return formatTime;
    }



    /**
     * 字符串转日期
     * @param str 日期字符串
     * @param fmt 日期格式
     * @return
     */
    public static Date stringToDate(String str,String fmt){
        try {
            //后期要改成SimpleDateFormat,传入格式字符串
            //用这种方法，则转换的日期格式必须相同
            SimpleDateFormat sdf = new SimpleDateFormat(fmt);
            Date date = sdf.parse(str);
            return date;
        } catch(Exception e) {
            return null;
        }
    }

    /**
     * 返回 yyyy-MM-dd hh:mm:ss的当前时间字符串
     * @return
     */
    public static String getNowString(){
        return getFormatDate(new Date(), DateUtils.YYYYMMDDHHMMSS);
    }

    /**
     * 返回 hh:mm:ss的当前时间字符串
     * @return
     */
    public static String getNowStringHHMMSS(){
        return getFormatDate(new Date(), DateUtils.HHMMSS);
    }

    /**
     * 返回 hh:mm:ss的当前时间字符串
     * @return
     */
    public static String getNowStringYYYYMMDD(){
        return getFormatDate(new Date(), DateUtils.YYYYMMDD);
    }

    /**
     * 返回 yyyy-MM-dd hh:mm:ss的当前时间字符串
     * @return
     */
    public static String getNowDateStartOrEndString(boolean isStart){
        String now = getFormatDate(new Date(), DateUtils.YYYYMMDDHHMMSS);
        String result = now.substring(0,10)+(isStart?" 00:00:00":" 23:59:59");
        return result;
    }

    /**
     * 日期转字符串
     * @param date
     * @param fmt 日期格式
     * @return
     */
    public static String getFormatDate(Date date, String fmt) {
        try {
            SimpleDateFormat sdf = new SimpleDateFormat(fmt);
            String str = sdf.format(date);
            return str;
        } catch(Exception e) {
            return null;
        }
    }

    /**
     * 日期转字符串格式：yyyy-MM-dd HH:mm:ss
     * @param date
     * @return
     */
    public static String getFormatDateForString(Date date) {
        try {
            SimpleDateFormat sdf = new SimpleDateFormat(YYYYMMDDHHMMSS);
            String str = sdf.format(date);
            return str;
        } catch(Exception e) {
            return null;
        }
    }

    /**
     *
     * @Title: getNextDay
     * @Description: 给定日期的下一天
     * @param @param str
     * @param @return
     * @return String    返回类型
     * @throws
     */
    public static String getNextDay(String str) throws ParseException {
        Date date = stringToDate(str, YYYYMMDD);
        date =addDays(date, 1);
        return getFormatDate(date, YYYYMMDD);
    }

    /**
     *
     * @Title: getYesterDay
     * @Description: 给定日期的昨天
     * @param @param str
     * @param @return
     * @return String    返回类型
     * @throws
     */
    public static String getYesterDay(String str) throws ParseException {
        Date date = stringToDate(str, YYYYMMDD);
        date =addDays(date, -1);
        return getFormatDate(date, YYYYMMDD);
    }

    /**
     * 获取当前时间，并转成 yyyy-MM-dd HH:mm:ss格式
     * @return
     */
    public static String getNewDate(){
        return getFormatDate(new Date(),YYYYMMDDHHMMSS);
    }

    /**
     * 判断date是否在date1 之前
     * @Title: isDateBefore
     * @param date
     * @param date1
     * @return true 是,false 否
     * @throws
     * @Description:
     */
    public static boolean isDateBefore(Date date,Date date1){
        try{
            boolean isDateBefore = date.before(date1);
            return isDateBefore;
        }catch(Exception ex){
            return false;
        }
    }

    public static int getWeek(Date date){
        Calendar cal = Calendar.getInstance();
        cal.setTime(date);
        int week_index = cal.get(Calendar.DAY_OF_WEEK) - 1;
        if(week_index<0){
            week_index = 0;
        }
        return week_index;
    }



    /**
     * 验证日期是否合法
     * @param paramDate 日期字符串
     * @param pattern 需要验证的正则
     * @return
     */
    public static Boolean chaeckDate(String paramDate,String pattern){
        //实例化Pattern
        Pattern p= Pattern.compile(pattern);
        //验证字符串内容是否合法
        Matcher m=p.matcher(paramDate);
        //使用正则验证
        return m.matches();
    }

    /**
     * 校验日期是否合法
     * @param paramDate 日期字符串
     * @param pattern 日期格式
     * @return
     */
    public static Boolean checkDate(String paramDate,String pattern){
        SimpleDateFormat sdf = new SimpleDateFormat(pattern);
        try {
            sdf.parse(paramDate);
        } catch (Exception e) {
            return false;
        }
        return true;
    }

    /**
     *
     * @param
     * @return  true 是今天;false;不是今天
     */
    public static boolean isToday(String dateStr) {
        Date date = DateUtils.stringToDate(dateStr, DateUtils.YYYYMMDD);
        SimpleDateFormat fmt = new SimpleDateFormat("yyyy-MM-dd");
        if (!Strings.isBlank(dateStr)&&fmt.format(new Date()).equals(fmt.format(date))) {//格式化为相同bai格式
            return true;
        } else {
            return false;
        }
    }
    /**
     * 获取指定周的第一天或最后一天
     * @param today 日期 （默认当前日期）
     * @param isFirst true 表示开始时间，false表示结束时间
     * @return
     */
    public static String getStartOrEndDayOfWeek(LocalDate today, Boolean isFirst){
        LocalDate resDate = LocalDate.now();
        if (today == null) {
            today = resDate;
        }
        DayOfWeek week = today.getDayOfWeek();
        int value = week.getValue();
        if (isFirst) {
            resDate = today.minusDays(value - 1);
        } else {
            resDate = today.plusDays(7 - value);
        }
        return resDate.toString();
    }

    /**
     * 获取指定月的第一天或最后一天
     * @param today 日期 （默认当前日期）
     * @param isFirst true 表示开始时间，false表示结束时间
     * @return
     */
    public static String getStartOrEndDayOfMonth(LocalDate today, Boolean isFirst){
        LocalDate resDate = LocalDate.now();
        if (today == null) {
            today = resDate;
        }
        Month month = today.getMonth();
        int length = month.length(today.isLeapYear());
        if (isFirst) {
            resDate = LocalDate.of(today.getYear(), month, 1);
        } else {
            resDate = LocalDate.of(today.getYear(), month, length);
        }
        return resDate.toString();
    }

    /**
     * 获取指定季度的第一天或最后一天
     * @param today 日期 （默认当前日期）
     * @param isFirst true 表示开始时间，false表示结束时间
     * @return
     */
    public static String getStartOrEndDayOfQuarter(LocalDate today, Boolean isFirst){
        LocalDate resDate = LocalDate.now();
        if (today == null) {
            today = resDate;
        }
        Month month = today.getMonth();
        Month firstMonthOfQuarter = month.firstMonthOfQuarter();
        Month endMonthOfQuarter = Month.of(firstMonthOfQuarter.getValue() + 2);
        if (isFirst) {
            resDate = LocalDate.of(today.getYear(), firstMonthOfQuarter, 1);
        } else {
            resDate = LocalDate.of(today.getYear(), endMonthOfQuarter, endMonthOfQuarter.length(today.isLeapYear()));
        }
        return resDate.toString();
    }

    /**
     * 获取指定年的第一天或最后一天
     * @param today 日期 （默认当前日期）
     * @param isFirst true 表示开始时间，false表示结束时间
     * @return
     */
    public static String getStartOrEndDayOfYear(LocalDate today, Boolean isFirst){
        LocalDate resDate = LocalDate.now();
        if (today == null) {
            today = resDate;
        }
        if (isFirst) {
            resDate = LocalDate.of(today.getYear(), Month.JANUARY, 1);
        } else {
            resDate = LocalDate.of(today.getYear(), Month.DECEMBER, Month.DECEMBER.length(today.isLeapYear()));
        }
        return resDate.toString();
    }

    public static String timestampToString(Long timestamp,String format){
        Date mydate=new Date();
        mydate.setTime(timestamp);
        if(format == null || format.length() == 0){
            format = DateUtils.YYYYMMDDHHMMSS;
        }
        return DateUtils.getFormatDate(mydate,format);
    }

    //获取测试节目单-B
    /**
     *
     * @param startDate
     * @return 日期 yyyyMMdd
     */
    public static String getTestStartDate(String startDate,String myTestStartDate) {
        String ret  = startDate;//默认真实的开始日期

        try {
            List<String> dates = datesOfWeek(myTestStartDate);//一周测试数据
            //List<String> dates = datesOfWeek("20200802");//一周测试数据

            Integer idx = dateIndexInWeek(startDate);//周几

            if (dates != null && dates.size() > 0 && idx < dates.size()) {
                ret = dates.get(idx);
            }
        }catch (Exception e){
            log.error("--getTestStartDate",e);
        }

        return ret;
    }

    /**
     * 一周的日期列表
     * @param sunday 周日  yyyyMMdd
     * @return
     */
    public static List<String> datesOfWeek(String sunday){
        List<String> dates = new ArrayList<>();
        Calendar   calendar   =   null;
        Date date = null;
        date = DateUtils.stringToDate(sunday,"yyyyMMdd");
        String strDate = "";
        dates.add(sunday);
        for(int i=0;i < 6;i++){
            calendar = new GregorianCalendar();
            calendar.setTime(date);
            calendar.add(calendar.DATE,1);//把日期往后增加一天.整数往后推,负数往前移动
            date=calendar.getTime();
            strDate = DateUtils.getFormatDate(date,"yyyyMMdd");
            dates.add(strDate);
        }
        return dates;
    }

    /**
     *
     * @param datetime yyyyMMdd
     * @return 返回周几索引
     */
    public static Integer dateIndexInWeek(String datetime) {
        SimpleDateFormat f = new SimpleDateFormat("yyyyMMdd");
        // String[] weekDays = {"星期日", "星期一", "星期二", "星期三", "星期四", "星期五", "星期六"};
        Calendar cal = Calendar.getInstance();
        Date date;
        try {
            date = f.parse(datetime);
            cal.setTime(date);
        } catch (ParseException e) {
            log.error("--dateToWeek",e);
        }catch (Exception e2){
            log.error("--dateToWeek",e2);
        }

        //一周的第几天
        Integer w = cal.get(Calendar.DAY_OF_WEEK) - 1;
        if (w < 0)
            w = 0;
        // return weekDays[w];
        return w;
    }

    /**
     * 更换日期 ,例如 2020-10-02 11:22:33 转换为 2021-11-11 11:22:33 ；日期更换时间不变；
     * @param rawDate 2020-10-02 11:22:33
     * @param replaceDateStr 2021-11-11
     * @return 2021-11-11 11:22:33
     */
    public static Date replaceDate(Date rawDate,String replaceDateStr){
        Date ret = null;
        Date replaceDate = stringToDate(replaceDateStr,YYYYMMDD_NO_SPLIT);
        ret = new Date(replaceDate.getYear()
                ,replaceDate.getMonth()
                ,replaceDate.getDate()
                ,rawDate.getHours()
                ,rawDate.getMinutes()
                ,rawDate.getSeconds());
        return ret;
    }

    public static Long dateToTimeStamp(String date){
        Date time= null;
        SimpleDateFormat simpleDateFormat = new SimpleDateFormat(YYYYMMDDHHMMSS);
        try {
            time = simpleDateFormat.parse(date);
        } catch (ParseException e) {

        }
        long ts = time.getTime();
        return ts;
    }

    /**
     * 根据要减的天数，得到新的时间
     * @param startTime 时间  2021-08-06
     * @param days 要减的天数  3
     * @return 返回已减的天数  2021-08-03
     */
    public static String timeReduceDays(LocalDate startTime,int days){
        LocalDate start = startTime.plusDays(days);
        return start.toString();
    }

    /**
     * 根据要减的天数，得到新的时间
     * @param startTime 开始时间  2021-08-06
     * @param endTime 结束时间  2021-08-07
     * @return 返回相差的天数  1
     */
    public static Integer getDayDiff(String startTime,String endTime) {
        long betweenDays = 0L;
        try {
            SimpleDateFormat sdf=new SimpleDateFormat("yyyy-MM-dd");
            Calendar cal = Calendar.getInstance();
            cal.setTime(sdf.parse(startTime));
            long time1 = cal.getTimeInMillis();
            cal.setTime(sdf.parse(endTime));
            long time2 = cal.getTimeInMillis();
            betweenDays=(time2-time1)/(1000*3600*24);
            System.out.println();
        }catch (Exception e) {
            e.printStackTrace();
        }
        return Integer.parseInt(String.valueOf(betweenDays));
    }

    //计算两个时间相差几个月
    public  static long getMonth(Date start, Date end) {
        if (start.after(end)) {
            Date t = start;
            start = end;
            end = t;
        }
        Calendar firstCalendar = Calendar.getInstance();
        firstCalendar.setTime(start);
        Calendar lastCalendar = Calendar.getInstance();
        lastCalendar.setTime(end);
        long monthNum = (lastCalendar.get(Calendar.MONTH) - firstCalendar.get(Calendar.MONTH));
        int yearMonth = (lastCalendar.get(Calendar.YEAR) - firstCalendar.get(Calendar.YEAR)) * 12;
        return monthNum + yearMonth;
    }

    /**
     * 时间戳转日期
     * @param ms
     * @return
     */
    public static Date transForDate(Long ms){
        if(ms==null){
            ms=0L;
        }
        long msl=ms*1000;
        SimpleDateFormat sdf=new SimpleDateFormat("yyyy-MM-dd");
        Date temp=null;
        if(ms!=null){
            try {
                String str=sdf.format(msl);
                temp=sdf.parse(str);
            } catch (ParseException e) {
                e.printStackTrace();
            }
        }
        return temp;
    }

    /**
     * 时间戳字符串-格式-返回固定格式时间
     * @param date
     * @param format
     * @return
     */
    public static Date formatDateStringToDate(String date,String format){
        Date time= null;
        SimpleDateFormat simpleDateFormat = new SimpleDateFormat(format);
        try {
            time = simpleDateFormat.parse(date);
        } catch (ParseException e) {

        }
        return time;
    }


    /**
     * 获取目标日期yyyy-MM-dd  距离今日指定天数
     * @param days
     * @return
     */
    public static String getDayTimeDiff(Integer days){
        SimpleDateFormat format = new SimpleDateFormat("yyyy-MM-dd");
        Calendar beforeTime = Calendar.getInstance();
        beforeTime.add(Calendar.DAY_OF_MONTH, days);
        Date time = beforeTime.getTime();
        return format.format(time);
    }

    /**
     * 根据年月，返回月末最后一天
     * @param yearMonth 2021-10
     * @return  2021-10-31
     */
    public static String getLastDayOfMonth(String yearMonth)
    {
        int year = Integer.parseInt(yearMonth.split("-")[0]);  //年
        int month = Integer.parseInt(yearMonth.split("-")[1]); //月
        Calendar cal = Calendar.getInstance();
        //设置年份
        cal.set(Calendar.YEAR,year);
        //设置月份
        cal.set(Calendar.MONTH, month-1);
        //获取某月最大天数
        int lastDay = cal.getActualMaximum(Calendar.DAY_OF_MONTH);
        //设置日历中月份的最大天数
        cal.set(Calendar.DAY_OF_MONTH, lastDay);
        //格式化日期
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
        String lastDayOfMonth = sdf.format(cal.getTime());

        return lastDayOfMonth;
    }


//	public static void main(String[] args) {
//		ScheduleServiceImpl a =new ScheduleServiceImpl();
//
//		a.getTestStartDate("20200810","20200802");
//	}
    //获取测试节目单-E

    public static void main(String[] arg){
//    	System.out.println(WeekEnum.getByDate(new Date()).getDescribe());
//		System.out.println(getFirstDayOfMonth(2019,3));
//		System.out.println(getLastDayOfMonth(2019,3));

//		Date d = replaceDate(new Date(),"20190211");

//	           Date mydate=new Date();
//			   mydate.setTime(1599235199000L);
//			  String ss =  DateUtils.getFormatDate(mydate, DateUtils.YYYYMMDDHHMMSS);
//				System.out.println(ss);



//		System.out.println(getActualDateByDateUnit("DAY","2019-11-06",false));
//		System.out.println(getActualDateByDateUnit("DAY","2019-11-06",true));
//
//		System.out.println(getActualDateByDateUnit("WEEK","2019-11-06",false));
//		System.out.println(getActualDateByDateUnit("WEEK","2019-11-06",true));
//
//		System.out.println(getActualDateByDateUnit("MONTH","2019-11",false));
//		System.out.println(getActualDateByDateUnit("MONTH","2019-11",true));
//
//		System.out.println(getActualDateByDateUnit("SEASON","2019-4",false));
//		System.out.println(getActualDateByDateUnit("SEASON","2019-4",true));
//
//		System.out.println(getActualDateByDateUnit("YEAR","2019",false));
//		System.out.println(getActualDateByDateUnit("YEAR","2019",true));

//		System.out.println(dateToTimeStamp("2021-08-12"));



    }

}
