package com.pukka.iptv.manage.refresh.cache.copyright;

import com.pukka.iptv.common.data.model.copyright.CopyrightInfo;
import com.pukka.iptv.common.data.vo.req.CopyrightInfoSizeReq;

import java.util.List;

/**
 * @Author: chiron
 * @Date: 2022/08/30/18:11
 * @Description: 版权信息缓存
 */

public interface CopyrightCache {
    /**
     * 获取版权信息数据
     * @param cpId
     * @return
     */
    List<CopyrightInfo> getCopyrightInfoCache(long cpId);

    /**
     * 版权到期时间
     * @param cpId
     * @return
     */
    CopyrightInfoSizeReq getCopyrightInfoSize(long cpId);
}
