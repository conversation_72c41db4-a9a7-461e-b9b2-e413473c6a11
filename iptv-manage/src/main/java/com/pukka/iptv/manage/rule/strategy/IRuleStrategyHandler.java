package com.pukka.iptv.manage.rule.strategy;

import com.pukka.iptv.common.base.enums.ContentTypeEnum;
import com.pukka.iptv.common.data.model.copyright.RuleProhibit;
import com.pukka.iptv.common.data.vo.copyright.RuleProhibitVo;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @Des: 1：单集 3：电视剧
 */
public interface IRuleStrategyHandler<T extends Serializable> {

    /**
     * 策略类型的方法
     * @return
     */
    ContentTypeEnum getContentType();


    /**
     * 规则处理类
     * @param ruleProhibitList 规则实体
     * @param entity 数据实体
     */
    Integer execute(List<RuleProhibit> ruleProhibitList, RuleProhibitVo entity);

}
