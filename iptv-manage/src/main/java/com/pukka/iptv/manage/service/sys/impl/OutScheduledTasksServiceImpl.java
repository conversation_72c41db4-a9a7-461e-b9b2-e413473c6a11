package com.pukka.iptv.manage.service.sys.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.ObjectUtils;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.pukka.iptv.common.base.enums.ContentTypeEnum;
import com.pukka.iptv.common.base.enums.IsTimedEnums;
import com.pukka.iptv.common.base.enums.PriorityEnums;
import com.pukka.iptv.common.base.util.JwtTokenUtil;
import com.pukka.iptv.common.base.util.UUID;
import com.pukka.iptv.common.base.vo.SecurityUser;
import com.pukka.iptv.common.core.constant.SymbolConstant;
import com.pukka.iptv.common.core.util.StringUtils;
import com.pukka.iptv.common.data.model.OutScheduledTask;
import com.pukka.iptv.common.data.model.bms.BmsContent;
import com.pukka.iptv.manage.mapper.sys.OutScheduledTasksMapper;
import com.pukka.iptv.manage.service.bms.BmsProgramService;
import com.pukka.iptv.manage.service.sys.OutScheduledTasksService;
import com.pukka.iptv.manage.util.DateUtils;
import io.seata.common.util.CollectionUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @Description 定时发布任务服务实现类
 * @Date 2024/04/12
 * @Version V1.0
 **/
@Slf4j
@Component
public class OutScheduledTasksServiceImpl extends
        ServiceImpl<OutScheduledTasksMapper, OutScheduledTask> implements OutScheduledTasksService {

    @Autowired
    private BmsProgramService bmsProgramService;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean createScheduledTasks(List<Long> ids, Integer contentType, String scheduleTime,
                                        int priority) {
        if (ObjectUtils.isEmpty(ids) || ObjectUtils.isEmpty(contentType) || ObjectUtils.isEmpty(
                scheduleTime)) {
            log.warn("创建定时发布任务失败，参数为空");
            return true;
        }
        try {
            List<OutScheduledTask> outScheduledTasks = buildScheduledTasks(ids, contentType,
                    scheduleTime, priority);
            //保存任务到数据库
            boolean isSaved = this.saveBatch(outScheduledTasks);
            if (!isSaved) {
                log.error("保存定时发布任务失败，类型: {}, ids:{}",
                        ContentTypeEnum.getContentTypeNameByContentType(contentType), ids);
                return false;
            }
            return true;
        } catch (Exception e) {
            log.error("创建定时发布任务失败: {}", e.getMessage());
            return false;
        }
    }

    /**
     * 删除定时发布任务
     *
     * @param id
     * @param contentType
     * @return
     */
    @Override
    public boolean deleteScheduledTask(Long id, Integer contentType) {
        if (ObjectUtils.isEmpty(id) || ObjectUtils.isEmpty(contentType)) {
            log.error("删除定时发布任务失败，参数为空");
            return true;
        }
        try {
            LambdaUpdateWrapper<OutScheduledTask> updateWrapper = Wrappers.lambdaUpdate(
                            OutScheduledTask.class)
                    .set(OutScheduledTask::getTimedPublishStatus, IsTimedEnums.NO_TIMED.getCode())
                    .set(OutScheduledTask::getTimedPublishDescription, "取消定时发布")
                    .set(OutScheduledTask::getCancelTime, new Date())
                    .eq(OutScheduledTask::getContentId, id)
                    .eq(OutScheduledTask::getContentType, contentType)
                    .ne(OutScheduledTask::getTimedPublishStatus, IsTimedEnums.NO_TIMED.getCode());
            boolean update = this.update(updateWrapper);
            if (!update) {
                log.error("删除定时发布任务失败 id: {}, contentType: {}", id,
                        contentType);
                return false;
            }
            return true;
        } catch (Exception e) {
            log.error("删除定时发布任务报错，错误信息: {}", e.getMessage());
            return false;
        }
    }

    /**
     * 获取定时发布任务
     *
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public List<OutScheduledTask> getScheduledTasks(Integer contentType) {
        if (ObjectUtils.isEmpty(contentType)) {
            log.warn("获取定时发布任务失败，参数为空");
            return null;
        }
        try {
            //根据时间升序找出最早创建的定时发布任务的code
            LambdaQueryWrapper<OutScheduledTask> subQueryWrapper = Wrappers.lambdaQuery(
                            OutScheduledTask.class)
                    .select(OutScheduledTask::getCode)
                    .eq(OutScheduledTask::getTimedPublishStatus, IsTimedEnums.IS_TIMED.getCode())
                    .eq(OutScheduledTask::getContentType, contentType)
                    .lt(OutScheduledTask::getTimedPublish, new Date())
                    .orderByAsc(OutScheduledTask::getCreateTime)
                    .last(" limit 1");
            OutScheduledTask outScheduledTask = this.getOne(subQueryWrapper);
            if (ObjectUtils.isEmpty(outScheduledTask)) {
                log.warn("获取定时发布任务失败，没有找到定时发布任务");
                return null;
            }
            //新增查询条件，根据code查询所有定时发布任务
            LambdaQueryWrapper<OutScheduledTask> queryWrapper = Wrappers.lambdaQuery(
                            OutScheduledTask.class)
                    .eq(OutScheduledTask::getTimedPublishStatus, IsTimedEnums.IS_TIMED.getCode())
                    .eq(OutScheduledTask::getContentType, contentType)
                    .eq(OutScheduledTask::getCode, outScheduledTask.getCode())
                    .lt(OutScheduledTask::getTimedPublish, new Date());
            List<OutScheduledTask> list = this.list(queryWrapper);
            if (CollectionUtils.isEmpty(list)) {
                log.warn("根据任务 code 查找关联任务失败，任务code:{}", outScheduledTask.getCode());
                return null;
            }
            return list;
        } catch (Exception e) {
            log.error("获取定时发布任务报错，错误信息:{}", e.getMessage());
            return null;
        }
    }

    /**
     * 完成定时发布任务
     *
     * @param ids
     * @param contentType
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public boolean finishScheduledTasks(List<Long> ids, Integer contentType) {
        if (ObjectUtils.isEmpty(ids) || ObjectUtils.isEmpty(contentType)) {
            log.error("参数为空");
            return true;
        }
        try {
            int type = convertType(contentType);
            LambdaQueryWrapper<OutScheduledTask> outScheduledTaskLambdaQueryWrapper = Wrappers.lambdaQuery(
                            OutScheduledTask.class)
                    .eq(OutScheduledTask::getContentType, type)
                    .ne(OutScheduledTask::getTimedPublishStatus, IsTimedEnums.NO_TIMED.getCode())
                    .in(OutScheduledTask::getContentId, ids);
            List<OutScheduledTask> list = this.list(outScheduledTaskLambdaQueryWrapper);
            if (CollectionUtils.isNotEmpty(list)) {
                try {
                    LambdaUpdateWrapper<OutScheduledTask> updateWrapper = Wrappers.lambdaUpdate(
                                    OutScheduledTask.class)
                            .set(OutScheduledTask::getTimedPublishStatus, IsTimedEnums.NO_TIMED.getCode())
                            .set(OutScheduledTask::getTimedPublishDescription, "定时发布结束")
                            .eq(OutScheduledTask::getContentType, type)
                            .in(OutScheduledTask::getContentId, ids);
                    boolean updated = this.update(updateWrapper);
                    if (!updated) {
                        log.error("更新定时发布任务失败");
                        return false;
                    }
                } catch (Exception e) {
                    log.error("完成定时发布任务,更新定时发布任务失败，错误信息: {}", e.getMessage());

                }
            }
            return true;
        } catch (Exception e) {
            log.error("更新定时发布任务报错，错误信息: {}", e.getMessage());
            return false;
        }
    }

    /**
     * 获取定时发布任务操作人信息
     *
     * @param contentIds
     * @param contentType
     * @return
     */
    @Override
    public SecurityUser getSecurityUser(String contentIds, Integer contentType) {
        if (StringUtils.isEmpty(contentIds) || ObjectUtils.isEmpty(contentType)) {
            log.error("参数为空: contentIds:{}, contentType:{}", contentIds, contentType);
            return null;
        }

        try {
            List<Long> ids = Arrays.stream(contentIds.split(SymbolConstant.COMMA))
                    .map(Long::parseLong)
                    .collect(Collectors.toList());
            int type = convertType(contentType);

            LambdaQueryWrapper<OutScheduledTask> queryWrapper = buildQueryWrapper(type, ids);
            OutScheduledTask outScheduledTask = this.getOne(queryWrapper);

            if (ObjectUtils.isNotEmpty(outScheduledTask)) {
                return createSecurityUser(outScheduledTask);
            } else {
                //判断是否是剧集携带子集下发
                return getSecurityUserFromOthers(contentType, ids);
            }
        } catch (Exception e) {
            log.error("获取SecurityUser时发生错误: {}", e.getMessage(), e);
            return null;
        }
    }

    /**
     * 构建查询条件
     *
     * @param type
     * @param ids
     * @return
     */
    private LambdaQueryWrapper<OutScheduledTask> buildQueryWrapper(int type, List<Long> ids) {
        return Wrappers.lambdaQuery(OutScheduledTask.class)
                .eq(OutScheduledTask::getContentType, type)
                .lt(OutScheduledTask::getTimedPublish, new Date())
                .in(OutScheduledTask::getContentId, ids)
                .orderByDesc(OutScheduledTask::getCreateTime)
                .last("limit 1");
    }

    /**
     * 创建操作人信息
     *
     * @param task
     * @return
     */
    private SecurityUser createSecurityUser(OutScheduledTask task) {
        SecurityUser user = new SecurityUser();
        user.setName(task.getCreatorName());
        user.setId(task.getCreatorId());
        return user;
    }

    /**
     * 子集时获取操作人信息
     *
     * @param contentType
     * @param ids
     * @return
     */
    private SecurityUser getSecurityUserFromOthers(Integer contentType, List<Long> ids) {
        if (ContentTypeEnum.SUBSET.getValue().equals(contentType)) {
            try {
                BmsContent bmsContent = bmsProgramService.getSeriesBySubProgramIds(ids.get(0));
                if (ObjectUtils.isNotEmpty(bmsContent)) {
                    LambdaQueryWrapper<OutScheduledTask> taskLambdaQueryWrapper = Wrappers.lambdaQuery(OutScheduledTask.class)
                            .eq(OutScheduledTask::getContentType, ContentTypeEnum.FILM.getValue())
                            .lt(OutScheduledTask::getTimedPublish, new Date())
                            .eq(OutScheduledTask::getContentId, bmsContent.getId())
                            .orderByDesc(OutScheduledTask::getCreateTime)
                            .last("limit 1");
                    OutScheduledTask scheduledTask = this.getOne(taskLambdaQueryWrapper);
                    if (ObjectUtils.isNotEmpty(scheduledTask)) {
                        return createSecurityUser(scheduledTask);
                    }
                }
            } catch (Exception e) {
                log.error("通过子集获取SecurityUser时发生错误: {}", e.getMessage(), e);
            }
        }
        return null;
    }


    /**
     * 获取媒资类型
     *
     * @param contentType
     * @return
     */
    private Integer convertType(Integer contentType) {
        ContentTypeEnum contentTypeEnum = ContentTypeEnum.getByValue(contentType);
        Set<ContentTypeEnum> mediaTypes = EnumSet.of(ContentTypeEnum.FILM, ContentTypeEnum.TELEPLAY,
                ContentTypeEnum.EPISODES);
        Set<ContentTypeEnum> packageRelatTypes = EnumSet.of(ContentTypeEnum.PACKAGE_PROGRAM,
                ContentTypeEnum.PACKAGE_SERIES);
        Set<ContentTypeEnum> categoryRelatTypes = EnumSet.of(ContentTypeEnum.CATEGORY_PROGRAM,
                ContentTypeEnum.CATEGORY_SERIES);
        if (mediaTypes.contains(contentTypeEnum)) {
            contentType = ContentTypeEnum.FILM.getValue();
        } else if (packageRelatTypes.contains(contentTypeEnum)) {
            contentType = ContentTypeEnum.PACKAGE_PROGRAM.getValue();
        } else if (categoryRelatTypes.contains(contentTypeEnum)) {
            contentType = ContentTypeEnum.CATEGORY_PROGRAM.getValue();
        }
        return contentType;
    }

    /**
     * 准备定时任务
     *
     * @param ids
     * @param contentType
     * @param scheduleTime
     * @param priority
     */

    private List<OutScheduledTask> buildScheduledTasks(List<Long> ids, Integer contentType,
                                                       String scheduleTime, int priority) {
        //创建唯一标识
        String code = UUID.nextSnow().toString();
        List<OutScheduledTask> outScheduledTasks = new ArrayList<>();
        //获取操作人信息
        SecurityUser user = JwtTokenUtil.getSecurityUser();
        SecurityUser userToUse;
        if (user == null || user.getId() == null) {
            userToUse = new SecurityUser();
            userToUse.setCreatorId(0L);
            userToUse.setCreatorName("未知创建人");
            log.error("当前定时发布任务获取创建人信息失败，定时任务信息:{}", outScheduledTasks);
        } else {
            userToUse = user;
        }
        ids.forEach(id -> {
            OutScheduledTask outScheduledTask = new OutScheduledTask();
            outScheduledTask.setContentId(id);
            outScheduledTask.setCreatorId(userToUse.getId());
            outScheduledTask.setCreatorName(userToUse.getName());
            outScheduledTask.setPriority(
                    priority >= 0 ? priority : PriorityEnums.GENERAL.getValue());
            outScheduledTask.setCode(code);
            outScheduledTask.setContentType(contentType);
            outScheduledTask.setTimedPublish(
                    DateUtils.formatDateStringToDate(scheduleTime, DateUtils.YYYYMMDDHHMMSS));
            outScheduledTask.setTimedPublishStatus(IsTimedEnums.IS_TIMED.getCode());
            outScheduledTasks.add(outScheduledTask);
        });
        return outScheduledTasks;
    }
}
