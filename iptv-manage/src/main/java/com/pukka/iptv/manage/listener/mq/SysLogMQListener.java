package com.pukka.iptv.manage.listener.mq;

import com.alibaba.fastjson.JSON;
import com.pukka.iptv.common.data.model.sys.SysLog;
import com.pukka.iptv.common.rabbitmq.config.SysLogMQConfig;
import com.rabbitmq.client.Channel;
import lombok.extern.slf4j.Slf4j;
import org.springframework.amqp.rabbit.annotation.Exchange;
import org.springframework.amqp.rabbit.annotation.Queue;
import org.springframework.amqp.rabbit.annotation.QueueBinding;
import org.springframework.amqp.rabbit.annotation.RabbitListener;
import org.springframework.amqp.support.AmqpHeaders;
import org.springframework.messaging.handler.annotation.Header;
import org.springframework.messaging.handler.annotation.Payload;
import org.springframework.stereotype.Component;

import java.io.IOException;

/**
 * 此类暂时未用
 */
//@Component
@Slf4j
public class SysLogMQListener {

    /**
     * 日志队列监听器
     *
     * @param params   消息体
     * @param channel   通道
     * @param deliveryTag   消息投递序号
     * @throws IOException 异常
     */
    @RabbitListener(bindings = {@QueueBinding(
            value = @Queue(value = SysLogMQConfig.SYSLOG_QUEUE),
            exchange = @Exchange(value = SysLogMQConfig.SYSLOG_EXCHANGE),
            key = SysLogMQConfig.SYSLOG__ROUTING)},
            containerFactory = "rabbitListenerContainerFactory"
    )
    public void recieved(@Payload SysLog params, Channel channel, @Header(AmqpHeaders.DELIVERY_TAG) Long deliveryTag) throws Exception {
        try {


        } catch (Exception e) {
            log.error("日志 RabbitMQ监听异常,消息内容={},异常原因:",
                    (null == params ? "暂无内容" : JSON.toJSONString(params)), e);
        } finally {
            // 不论处理结果如何，消费完直接移除消息，不做重试机制
            channel.basicAck(deliveryTag, false);
        }
    }

    /**
     * 重试处理
     * @param channel 通道
     * @param deliveryTag 消息投递序号
     * @param key 缓存Key
     * @throws IOException 异常
     */
    private void retryHandle(Channel channel, Long deliveryTag, String key) throws IOException {

    }

}