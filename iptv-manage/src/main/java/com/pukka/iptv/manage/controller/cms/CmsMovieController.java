package com.pukka.iptv.manage.controller.cms;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.pukka.iptv.common.base.vo.CommonResponse;
import com.pukka.iptv.common.data.group.GetMovieGroup;
import com.pukka.iptv.common.data.model.cms.CmsMovie;
import com.pukka.iptv.common.data.vo.IdList;
import com.pukka.iptv.manage.service.cms.CmsMovieService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.AllArgsConstructor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.List;

/**
 * 视频
 *
 * @author: zhoul
 * @date: 2021-8-31 10:54:12
 */

@RestController
@AllArgsConstructor
@RequestMapping(value = "/cmsMovie", produces = MediaType.APPLICATION_JSON_VALUE)
@Api(tags="cmsMovie管理")
public class CmsMovieController {

    @Autowired
    private CmsMovieService cmsMovieService;

    @ApiOperation(value = "分页")
    @GetMapping("/page" )
    public CommonResponse<Page> page(@Valid Page page, CmsMovie cmsMovie) {
        return  CommonResponse.success(cmsMovieService.page(page, Wrappers.query(cmsMovie)));
    }

    @ApiOperation(value = "详情")
    @GetMapping("/getById")
    public CommonResponse<CmsMovie> getById(@Valid @RequestParam(name = "id", required = true)  Long id) {
        return CommonResponse.success(cmsMovieService.getById(id));
    }
    @ApiOperation(value = "视频列表查询")
    @GetMapping("/listById")
    public CommonResponse listById(@Validated(GetMovieGroup.class)
                                                     @RequestParam(name = "cmsContentId")  Long cmsContentId,
                                                     @RequestParam(name = "contentType")  Integer contentType,
                                                     @RequestParam(name = "cpId")  Long cpId,
                                                    @RequestParam (name = "type" ,required = false)Integer type) {
        return cmsMovieService.listByCmsContentIdAndType(cmsContentId, contentType,cpId,type);
    }

    @ApiOperation(value = "新增")
    @PostMapping("/save")
    public CommonResponse<Boolean> save(@Valid @RequestBody CmsMovie cmsMovie) {
        return  CommonResponse.success(cmsMovieService.save(cmsMovie));
    }

    @ApiOperation(value = "修改")
    @PutMapping("/update")
    public CommonResponse<Boolean> updateById(@Valid @RequestBody CmsMovie cmsMovie) {
        return CommonResponse.success(cmsMovieService.updateById(cmsMovie));
    }

    @ApiOperation(value = "删除")
    @DeleteMapping("/deleteById" )
    public CommonResponse<Boolean> deleteById(@Valid @RequestParam(name = "id", required = true) Long id) {
        return  CommonResponse.success(cmsMovieService.removeById(id));
    }

    @ApiOperation(value = "批量删除")
    @DeleteMapping("/deleteByIds" )
    public CommonResponse<Boolean> deleteByIds(@Valid @RequestBody IdList idList) {
        return  CommonResponse.success(cmsMovieService.removeByIds(idList.getIds()));
    }

    @ApiOperation(value = "根据id和type,cpId获取视频信息")
    @GetMapping("/getByContentIdContentType")
    CommonResponse<List<CmsMovie>> getByContentIdContentType(@Valid @RequestParam(name = "contentId", required = true)  Long contentId, @Valid @RequestParam(name = "contentType", required = true)  Integer contentType, @RequestParam(name = "cpId")  Long cpId){
        return  CommonResponse.success(cmsMovieService.getByContentIdContentType(contentId,contentType,cpId));
    }

    @ApiOperation(value = "根据code获取视频信息")
    @GetMapping("/getByCode")
    public CommonResponse<CmsMovie> getByCode(@Valid @RequestParam(name = "code", required = true) String code){
        return  CommonResponse.success(cmsMovieService.getByCode(code));
    }
}
