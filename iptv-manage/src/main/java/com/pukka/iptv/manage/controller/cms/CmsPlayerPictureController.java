package com.pukka.iptv.manage.controller.cms;

import com.pukka.iptv.common.base.vo.CommonResponse;
import com.pukka.iptv.common.data.model.cms.CmsMovie;
import javax.validation.Valid;

import com.pukka.iptv.common.data.model.cms.CmsPlayerPicture;
import com.pukka.iptv.common.log.annotation.SysLog;
import com.pukka.iptv.common.log.enums.OperateObjectEnum;
import com.pukka.iptv.common.log.enums.OperateTypeEnum;
import com.pukka.iptv.manage.service.cms.CmsPlayerPictureService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.*;
import org.springframework.beans.factory.annotation.Autowired;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.AllArgsConstructor;

/**
 *  视频关键帧
 *
 * @author: zhoul
 * @date: 2021-11-3 14:28:16
 */

@RestController
@AllArgsConstructor
@RequestMapping(value = "/cmsPlayerPicture", produces = MediaType.APPLICATION_JSON_VALUE)
@Api(tags="cmsPlayerPicture管理")
public class CmsPlayerPictureController {

    @Autowired
    private CmsPlayerPictureService cmsPlayerPictureService;

    @ApiOperation(value = "分页")
    @GetMapping("/page" )
    public CommonResponse<Page> listByCmsContentIdContentType(@Valid Page page, CmsMovie cmsMovie) {
        return  CommonResponse.success(cmsPlayerPictureService.listByCmsContentIdContentType(page, cmsMovie));
    }

    @ApiOperation(value = "修改")
    @PutMapping("/update")
    public CommonResponse<Boolean> updateById(@Valid @RequestBody CmsPlayerPicture cmsPlayerPicture) {
        return CommonResponse.success(cmsPlayerPictureService.updateById(cmsPlayerPicture));
    }

    @ApiOperation(value = "删除")
    @DeleteMapping("/deleteById" )
    public CommonResponse<Boolean> deleteById(@Valid @RequestBody CmsPlayerPicture cmsPlayerPicture) {
        return  CommonResponse.success(cmsPlayerPictureService.removeById(cmsPlayerPicture));
    }


}
