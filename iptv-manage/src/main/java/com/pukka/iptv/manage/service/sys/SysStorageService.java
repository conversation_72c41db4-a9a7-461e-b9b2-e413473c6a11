package com.pukka.iptv.manage.service.sys;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.pukka.iptv.common.data.model.sys.SysStorage;

import java.util.List;

/**
 *
 * @author: zhengcl
 * @date: 2021-9-8 18:11:54
 */

public interface SysStorageService extends IService<SysStorage> {

    Page<SysStorage> page(Page<SysStorage> page, SysStorage sysStorage);

    boolean saveToCacheAndDB(SysStorage storage);

    /**
     * 通过ids删除
     * @param ids 分发通道id集合
     * @return bool
     */
    boolean removeByIds(List<Long> ids);

    /**
     * 获取工单存储目录
     * @param storageType
     * @return
     */
    SysStorage getByStorageType(Integer storageType);

    boolean removeById(Long id);

    boolean updateCacheAndDB(SysStorage storage);
}


