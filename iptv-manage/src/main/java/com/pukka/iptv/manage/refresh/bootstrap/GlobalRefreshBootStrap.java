package com.pukka.iptv.manage.refresh.bootstrap;

import com.pukka.iptv.common.base.constant.RedisKeyConstants;
import com.pukka.iptv.manage.refresh.handle.GlobalRefreshHandle;
import org.springframework.util.CollectionUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.CommandLineRunner;
import org.springframework.core.annotation.Order;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Component;

import java.util.Set;
import java.util.concurrent.CompletableFuture;

/**
 * <AUTHOR>
 * @Date 2021/10/15 9:50 上午
 * @description:
 * @Version 1.0
 */
@Component
@Order(1)
@Slf4j
public class GlobalRefreshBootStrap implements CommandLineRunner {
    @Autowired
    private StringRedisTemplate stringRedisTemplate;
    @Autowired
    private GlobalRefreshHandle globalRefreshHandle;
    @Value("${spring.application.deleteBeforeInit:false}")
    private boolean deleteBeforeInit;

    @Override
    public void run(String... args) {
        try {
            if (deleteBeforeInit) {
                log.info("开始清理Redis 全局配置信息");
                Set<String> keys = stringRedisTemplate.keys(RedisKeyConstants.SYS_CONFIG_FIX + "*");
                if (!CollectionUtils.isEmpty(keys)) {
                    stringRedisTemplate.delete(keys);
                }
                log.info("清理Redis 全局配置信息完成");
            }
            log.info("开始初始化全局配置信息到REDIS");
            globalRefreshHandle.refresh();
            log.info("初始化全局配置信息到REDIS完成");
        } catch (Exception e) {
            log.error("全局配置信息初始化失败");
            e.printStackTrace();
        }
    }
}
