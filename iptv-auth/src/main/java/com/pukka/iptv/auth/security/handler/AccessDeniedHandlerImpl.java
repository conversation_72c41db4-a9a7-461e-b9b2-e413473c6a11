package com.pukka.iptv.auth.security.handler;

import com.pukka.iptv.common.base.enums.CommonResponseEnum;
import com.pukka.iptv.common.core.util.ResponseUtil;
import com.pukka.iptv.common.base.vo.CommonResponse;
import org.springframework.security.access.AccessDeniedException;
import org.springframework.security.web.access.AccessDeniedHandler;
import org.springframework.stereotype.Component;
import javax.servlet.ServletException;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;

/**
 * <AUTHOR>
 * @date 2021/8/1
 * @title 当访问接口没有权限时，自定义的返回结果
 */
@Component
public class AccessDeniedHandlerImpl implements AccessDeniedHandler {
    @Override
    public void handle(HttpServletRequest request,
                       HttpServletResponse response,
                       AccessDeniedException e) throws IOException, ServletException {
//        int code = HttpStatus.FORBIDDEN;
//        String msg = StringUtils.format("请求访问：{}，访问失败，无权访问系统资源", request.getRequestURI());
        ResponseUtil.out(response, CommonResponse.general(CommonResponseEnum.PERMISSION_DENIED));

    }
}
