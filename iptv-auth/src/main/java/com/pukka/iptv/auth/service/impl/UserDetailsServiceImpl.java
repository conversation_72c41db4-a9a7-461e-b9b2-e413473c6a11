package com.pukka.iptv.auth.service.impl;

import com.pukka.iptv.common.api.feign.sys.SysUserFeignClient;
import com.pukka.iptv.common.base.vo.CommonResponse;
import com.pukka.iptv.common.base.vo.SecurityUser;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.core.userdetails.UserDetails;
import org.springframework.security.core.userdetails.UserDetailsPasswordService;
import org.springframework.security.core.userdetails.UserDetailsService;
import org.springframework.security.core.userdetails.UsernameNotFoundException;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @date 2021/8/6
 * @title 用户请求
 */
@Service
public class UserDetailsServiceImpl implements UserDetailsService, UserDetailsPasswordService {
    @Autowired
    SysUserFeignClient sysUserFeignClient;

    @Override
    public UserDetails loadUserByUsername(String s) throws UsernameNotFoundException {
        CommonResponse<SecurityUser> commonSecurity = sysUserFeignClient.getByUsername(s);
        return  commonSecurity.getData();
    }

    @Override
    public UserDetails updatePassword(UserDetails user, String newPassword) {
        return null;
    }
}
