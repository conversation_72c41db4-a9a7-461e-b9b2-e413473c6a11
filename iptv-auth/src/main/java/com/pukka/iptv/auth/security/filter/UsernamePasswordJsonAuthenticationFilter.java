package com.pukka.iptv.auth.security.filter;

import com.pukka.iptv.auth.util.AesUtils;
import com.pukka.iptv.auth.util.RSAUtils;
import com.pukka.iptv.auth.util.UserFailApi;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.security.authentication.AuthenticationServiceException;
import org.springframework.security.authentication.InternalAuthenticationServiceException;
import org.springframework.security.authentication.UsernamePasswordAuthenticationToken;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.AuthenticationException;
import org.springframework.security.web.authentication.UsernamePasswordAuthenticationFilter;
import org.springframework.util.StringUtils;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.security.PrivateKey;

@Slf4j
public class UsernamePasswordJsonAuthenticationFilter extends UsernamePasswordAuthenticationFilter {


    private boolean postOnly = true;

    @Value("${manage.rsa.privateKey}")
    private String rsaPrivateKey;
    @Value("${manage.key.hander}")
    private String keyHandler;


    public Authentication attemptAuthentication(HttpServletRequest request, HttpServletResponse response)
            throws AuthenticationException {
        if (postOnly && !request.getMethod().equals("POST")) {
            throw new AuthenticationServiceException("Authentication method not supported: " + request.getMethod());
        }
        String username = obtainUsername(request);
        String password = obtainPassword(request);

        UserFailApi.getInstance().checkFailCount(username);
        password = decryptPassword(request, password);
        if (username == null) {
            username = "";
        }
        if (password == null) {
            password = "";
        }
        username = username.trim();
        UsernamePasswordAuthenticationToken authRequest = new UsernamePasswordAuthenticationToken(username, password);
        // Allow subclasses to set the "details" property
        setDetails(request, authRequest);
        return super.getAuthenticationManager().authenticate(authRequest);
    }

    /**
     * 获取到加密后的公钥
     * 使用私钥解密key获取到真实key
     * 将真实key保存
     * @param request
     * @param password
     * @return
     */
    private String decryptPassword(HttpServletRequest request, String password) {
        String rsaKey = request.getHeader(keyHandler);
        if (!StringUtils.isEmpty(rsaKey)) {
            try {
                PrivateKey privateKey = RSAUtils.string2Privatekey(rsaPrivateKey);
                String key = RSAUtils.decrypt(rsaKey, privateKey);
                return AesUtils.decryptAES(password, key);
            } catch (Exception e) {
                log.error("解析key异常,异常信息：{}", e.getMessage());
                throw new InternalAuthenticationServiceException("非法登录请求，禁止登录");
            }
        } else {
            throw new InternalAuthenticationServiceException("非法登录请求，禁止登录");
        }
    }
}
