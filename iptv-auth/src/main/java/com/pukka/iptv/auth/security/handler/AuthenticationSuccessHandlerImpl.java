package com.pukka.iptv.auth.security.handler;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.pukka.iptv.auth.util.UserFailApi;
import com.pukka.iptv.common.api.feign.sys.SysMenuFeignClient;
import com.pukka.iptv.common.api.feign.sys.SysRoleFeignClient;
import com.pukka.iptv.common.api.feign.sys.SysTenantFeignClient;
import com.pukka.iptv.common.api.feign.sys.SysUserFeignClient;
import com.pukka.iptv.common.base.config.AuthoritiesConfig;
import com.pukka.iptv.common.base.enums.CommonResponseEnum;
import com.pukka.iptv.common.base.enums.SysUserTypeEnum;
import com.pukka.iptv.common.base.exception.CommonResponseException;
import com.pukka.iptv.common.base.util.JwtTokenUtil;
import com.pukka.iptv.common.base.util.Menu;
import com.pukka.iptv.common.base.util.MenuUtil;
import com.pukka.iptv.common.base.vo.CommonResponse;
import com.pukka.iptv.common.base.vo.SecurityUser;
import com.pukka.iptv.common.core.constant.MenuRedisKeyConstant;
import com.pukka.iptv.common.core.enums.UserEnableEnum;
import com.pukka.iptv.common.core.util.ResponseUtil;
import com.pukka.iptv.common.data.model.sys.SysMenu;
import com.pukka.iptv.common.data.model.sys.SysRole;
import com.pukka.iptv.common.data.model.sys.SysTenant;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.GrantedAuthority;
import org.springframework.security.core.authority.SimpleGrantedAuthority;
import org.springframework.security.web.authentication.AuthenticationSuccessHandler;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import javax.servlet.ServletException;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2021/8/1
 * @title 登录成功
 */
@Component
@Slf4j
public class AuthenticationSuccessHandlerImpl implements AuthenticationSuccessHandler {
    @Autowired
    private RedisTemplate redisTemplate;
    @Autowired
    private SysMenuFeignClient sysMenuFeignClient;
    @Autowired
    private SysRoleFeignClient sysRoleFeignClient;
    @Autowired
    private SysTenantFeignClient sysTenantFeignClient;
    @Autowired
    private SysUserFeignClient sysUserFeignClient;

    /**
     * 认证成功
     *
     * @param httpServletRequest
     * @param httpServletResponse
     * @param authentication
     * @throws IOException
     * @throws ServletException
     */
    @Override
    public void onAuthenticationSuccess(HttpServletRequest httpServletRequest, HttpServletResponse httpServletResponse, Authentication authentication) throws IOException, ServletException {
        SecurityUser securityUser = null;
        try {
            securityUser = (SecurityUser) authentication.getPrincipal();//拿到登录用户信息
           if (UserFailApi.getInstance().getUserFailCache(securityUser.getUsername())!=null){
               UserFailApi.getInstance().removeUser(securityUser.getUsername());
           }
            if (securityUser == null) {
                throw new CommonResponseException(CommonResponseEnum.NON_EXISTENT_USERS);
            }
            if (securityUser.getStatus().equals(UserEnableEnum.DISENABLED.getValue())) {
                throw new CommonResponseException(CommonResponseEnum.ACCOUNT_DISABLE);
            }
            List<Menu> sysMenus = new ArrayList<>();
            List<SysRole> sysRoles = new ArrayList<>();
            List<SysTenant> sysTenants = new ArrayList<>();
            Set<Long> roleSet = new HashSet();
            Set<Long> cpIdSet = new HashSet();
            Set<Long> spIdSet = new HashSet();
            String roleIds = "";
            String tenantIds = "";
            String cpIds = "";
            String spIds = "";
            if (SysUserTypeEnum.SUPER_ADMIN.getCode().equals(securityUser.getType())) {
                //根据角色获取菜单
                sysMenus = sysMenuFeignClient.listByRoleIds(roleSet).getData();
            } else {
                sysRoles = sysRoleFeignClient.listByUserId(securityUser.getId()).getData();
                sysTenants = sysTenantFeignClient.listByUserId(securityUser.getId()).getData();
                //获取角色ID集合
                roleIds = getRoleIds(sysRoles,roleSet);
                //获取租户ID集合
                tenantIds = getTenantIds(sysTenants, cpIdSet, spIdSet);
                //获取CPID集合
                cpIds = StringUtils.collectionToDelimitedString(cpIdSet, ",");
                //获取SPID集合
                spIds = StringUtils.collectionToDelimitedString(spIdSet, ",");
                if(StringUtils.hasLength(roleIds)){
                    //根据角色获取菜单
                    sysMenus = sysMenuFeignClient.listByRoleIds(roleSet).getData();
                }
            }
            //设置用户信息
            setSecurityUserInfo(securityUser, roleIds, tenantIds, cpIds, spIds);
            //生成Token
            String token = JwtTokenUtil.generateUserToken(securityUser, JwtTokenUtil.jwtTokenExpired);
            List<Menu> menuTreeList = new ArrayList<>();
            Map<String, List<Menu>> tabMap = new HashMap<>();
            Map<String, List<Menu>> toolButtonMap = new HashMap<>();
            Map<String, List<Menu>> columnButtonMap = new HashMap<>();
            //获取菜单列表
            MenuUtil.getMenuTreeListAndButtonMap(sysMenus, menuTreeList, tabMap, toolButtonMap, columnButtonMap);
            //securityUser.setMenus(menuTreeList);
            //securityUser.setButtons(buttonMap);
            //securityUser.setAuthorities(getAuthorities(sysMenus));
            //securityUser.setAuthorities(null);
            //刷新Redis
            setRedisUserInfo(securityUser, token, menuTreeList, sysMenus, tabMap, toolButtonMap, columnButtonMap);
            //登录成功
            sysUserFeignClient.login(securityUser.getUsername(), securityUser.getId());
            //隐藏密码
            securityUser.setPassword("******");
        } catch (Exception e) {
            e.printStackTrace();
            log.error(e.getMessage(),e.fillInStackTrace());
            ResponseUtil.out(httpServletResponse, CommonResponse.general(CommonResponseEnum.LOGIN_ERROR,e.getMessage()));
        }
        ResponseUtil.out(httpServletResponse, CommonResponse.general(CommonResponseEnum.SUCCESS, securityUser));
    }

    private void setSecurityUserInfo(SecurityUser securityUser, String roleIds, String tenantIds, String cpIds, String spIds) {
        securityUser.setRoleIds(roleIds);
        securityUser.setTenantIds(tenantIds);
        securityUser.setCpIds(cpIds);
        securityUser.setSpIds(spIds);
        securityUser.setLoginTime(new Date());
    }

    private String getTenantIds(List<SysTenant> sysTenants, Set<Long> cpIdSet, Set<Long> spIdSet) {
        String tenantIds = "";
        for (SysTenant sysTenant : sysTenants) {
            if (StringUtils.hasLength(tenantIds)) {
                tenantIds += ",";
            }
            tenantIds += sysTenant.getId();
            if (StringUtils.hasLength(sysTenant.getCpIds())) {
                String[] cpIdArray = sysTenant.getCpIds().split(",");
                for (String cpId : cpIdArray) {
                    cpIdSet.add(Long.parseLong(cpId));
                }
            }
            if (StringUtils.hasLength(sysTenant.getSpIds())) {
                String[] spIdArray = sysTenant.getSpIds().split(",");
                for (String spId : spIdArray) {
                    spIdSet.add(Long.parseLong(spId));
                }
            }
        }
        return tenantIds;
    }

    private String getRoleIds(List<SysRole> sysRoles,Set roleSet) {
        String roleIds = "";
        for (SysRole sysRole : sysRoles) {
            if (StringUtils.hasLength(roleIds)) {
                roleIds += ",";
            }
            roleIds += sysRole.getId();
            if (Objects.nonNull(sysRole.getId())) {
                roleSet.add(sysRole.getId());
            }
        }
        return roleIds;
    }

    private void setRedisUserInfo(SecurityUser securityUser, String token, List<Menu> menuTreeList, List<Menu> menus, Map<String, List<Menu>> tabMap, Map<String, List<Menu>> toolButtonMap, Map<String, List<Menu>> columnButtonMap) {
        Map userMap = new HashMap();
        userMap.put(MenuRedisKeyConstant.TOKEN, token);
        userMap.put(MenuRedisKeyConstant.MENUS, menuTreeList);
        userMap.put(MenuRedisKeyConstant.AUTHORITIES, getAuthorities(menus));
        userMap.put(MenuRedisKeyConstant.TABS, tabMap);
        userMap.put(MenuRedisKeyConstant.TOOL_BUTTONS, toolButtonMap);
        userMap.put(MenuRedisKeyConstant.COLUMN_BUTTONS, columnButtonMap);
        redisTemplate.opsForHash().putAll(JwtTokenUtil.getUserRedisTokenKey(securityUser.getId()), userMap);
        redisTemplate.boundHashOps(JwtTokenUtil.getUserRedisTokenKey(securityUser.getId())).expire(JwtTokenUtil.redisTokenExpired, TimeUnit.SECONDS);

    }

    /**
     * 获取分页菜单
     *
     * @param menuTreeList
     * @return
     */
    public IPage<SysMenu> getMenuTreeListPage(List<SysMenu> menuTreeList) {
        Page<SysMenu> menuPage = new Page();
        menuPage.setCurrent(1);
        menuPage.setTotal(1);
        menuPage.setRecords(menuTreeList);
        menuPage.setSize(menuTreeList.size());
        return menuPage;
    }

    /**
     * 获取权限
     *
     * @param menus
     * @return
     */

    public List<GrantedAuthority> getAuthorities(List<Menu> menus) {
        List<GrantedAuthority> grantedAuthorities = new ArrayList<>();
        if (!CollectionUtils.isEmpty(menus)) {
            List<String> collect = menus.stream().map(Menu::getPermission).collect(Collectors.toList());
            for (String authority : collect) {
                if (!("").equals(authority) & authority != null) {
                    GrantedAuthority grantedAuthority = new SimpleGrantedAuthority(authority);
                    grantedAuthorities.add(grantedAuthority);
                }
            }
        }
        //添加默认权限组
        grantedAuthorities.add(new SimpleGrantedAuthority(AuthoritiesConfig.DEFAULT_AUTHORITIES));
        return grantedAuthorities;
    }
}
