package com.pukka.iptv.auth;


import com.baomidou.mybatisplus.autoconfigure.MybatisPlusAutoConfiguration;
import com.pukka.iptv.common.swagger.annotation.EnableCustomOpenApi;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.autoconfigure.jdbc.DataSourceAutoConfiguration;
import org.springframework.cloud.openfeign.EnableFeignClients;

/**
 * <AUTHOR>
 * @date: 2021/7/23
 * @title 认证启动器
 */

@EnableCustomOpenApi
@EnableFeignClients(basePackages = "com.pukka.iptv.common.api.feign")
@SpringBootApplication
public class AuthApplication {
        public static void main(String[] args) {
                SpringApplication.run(AuthApplication.class,args);
        }
}

