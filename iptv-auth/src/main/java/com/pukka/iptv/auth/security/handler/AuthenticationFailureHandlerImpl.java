package com.pukka.iptv.auth.security.handler;

import cn.hutool.core.util.CharsetUtil;
import com.pukka.iptv.auth.config.LoginConfig;
import com.pukka.iptv.auth.exception.LoginLockException;
import com.pukka.iptv.auth.util.UserFailApi;
import com.pukka.iptv.common.base.enums.CommonResponseEnum;
import com.pukka.iptv.common.base.vo.CommonResponse;
import com.pukka.iptv.common.core.util.ResponseUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.security.authentication.*;
import org.springframework.security.core.AuthenticationException;
import org.springframework.security.web.authentication.AuthenticationFailureHandler;
import org.springframework.stereotype.Component;

import javax.servlet.ServletException;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;

/**
 * <AUTHOR>
 * @date 2021/8/2
 * @title 认证失败
 */
@Component
public class AuthenticationFailureHandlerImpl implements AuthenticationFailureHandler {

    @Autowired
    private LoginConfig loginConfig;

    @Override
    public void onAuthenticationFailure(HttpServletRequest httpServletRequest, HttpServletResponse httpServletResponse, AuthenticationException e) throws IOException, ServletException {
        //修改编码格式
        httpServletResponse.setCharacterEncoding(CharsetUtil.UTF_8);
        httpServletResponse.setContentType(MediaType.APPLICATION_JSON_VALUE);
        String msg = null;
        CommonResponseEnum commonResponseEnum = CommonResponseEnum.LOGIN_ERROR;
        String username = httpServletRequest.getParameter("username");
        if (e instanceof AccountExpiredException) {
            msg = "账户过期，登录失败!";
        } else if (e instanceof LoginLockException) {
            Long lockTime = UserFailApi.getInstance().getLockTime(username);
            msg = "由于您多次登录失败，您的账户已被锁定，请"+ (lockTime ==0 ? 1 : lockTime) + "分钟后尝试" ;
        } else if (e instanceof BadCredentialsException) {
            Integer failCount = UserFailApi.getInstance().setUserFailCache(username);
            if (loginConfig.getFailCountMax()<=failCount) {
                msg = "由于您多次登录失败，您的账户已被锁定，请"+loginConfig.getLockTime()+"分钟后尝试";
            } else {
                msg = "用户名或密码错误，登录失败! 您还剩 " + (loginConfig.getFailCountMax() - failCount) + "次尝试机会";
            }
            commonResponseEnum = CommonResponseEnum.SIGN_ERROR;
        } else if (e instanceof CredentialsExpiredException) {
            msg = "密码过期，登录失败!";
        } else if (e instanceof DisabledException) {
            msg = "账户被禁用，登录失败!";
        } else if (e instanceof LockedException) {
            msg = "账户被锁，登录失败!";
        } else if (e instanceof InternalAuthenticationServiceException) {
            msg = "非法登录请求，禁止登录";
        } else {
            msg = "该用户未登录！";
        }
        ResponseUtil.out(httpServletResponse, CommonResponse.general(commonResponseEnum, msg, null));
    }
}
