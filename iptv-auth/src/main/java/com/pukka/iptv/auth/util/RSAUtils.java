package com.pukka.iptv.auth.util;

import javax.crypto.Cipher;
import java.io.UnsupportedEncodingException;
import java.nio.charset.StandardCharsets;
import java.security.*;
import java.security.spec.InvalidKeySpecException;
import java.security.spec.PKCS8EncodedKeySpec;
import java.security.spec.X509EncodedKeySpec;
import java.util.Base64;

public class RSAUtils {

    /**
     * 生成密钥对：密钥对中包含公钥和私钥
     * @return 包含 RSA 公钥与私钥的 keyPair
     * @throws NoSuchAlgorithmException
     */
    public static KeyPair getKeyPair() throws NoSuchAlgorithmException, UnsupportedEncodingException {
        KeyPairGenerator keyPairGenerator = KeyPairGenerator.getInstance("RSA");    // 获得RSA密钥对的生成器实例
        SecureRandom secureRandom = new SecureRandom(String.valueOf(System.currentTimeMillis()).getBytes("utf-8")); // 说的一个安全的随机数
        keyPairGenerator.initialize(2048, secureRandom);    // 这里可以是1024、2048 初始化一个密钥对
        KeyPair keyPair = keyPairGenerator.generateKeyPair();   // 获得密钥对
        return keyPair;
    }

    /**
     * 获取公钥 (并进行 Base64 编码，返回一个 Base64 编码后的字符串)
     * @param keyPair：RSA 密钥对
     * @return 返回一个 Base64 编码后的公钥字符串
     */
    public static String getPublicKey(KeyPair keyPair){
        PublicKey publicKey = keyPair.getPublic();
        byte[] bytes = publicKey.getEncoded();

        return Base64.getEncoder().encodeToString(bytes);
    }

    /**
     * 获取私钥(并进行Base64编码，返回一个 Base64 编码后的字符串)
     * @param keyPair：RSA 密钥对
     * @return 返回一个 Base64 编码后的私钥字符串
     */
    public static String getPrivateKey(KeyPair keyPair){
        PrivateKey privateKey = keyPair.getPrivate();
        byte[] bytes = privateKey.getEncoded();
        return Base64.getEncoder().encodeToString(bytes);
    }

    /**
     * 将 Base64 编码后的公钥转换成 PublicKey 对象
     * @param pubStr：Base64 编码后的公钥字符串
     * @return PublicKey
     */
    public static PublicKey string2PublicKey(String pubStr) throws NoSuchAlgorithmException, InvalidKeySpecException, InvalidKeySpecException {
        byte[] bytes = Base64.getDecoder().decode(pubStr);
        X509EncodedKeySpec keySpec = new X509EncodedKeySpec(bytes);
        KeyFactory keyFactory = KeyFactory.getInstance("RSA");
        PublicKey publicKey = keyFactory.generatePublic(keySpec);
        return publicKey;
    }

    /**
     * 将 Base64 码后的私钥转换成 PrivateKey 对象
     * @param priStr：Base64 编码后的私钥字符串
     * @return PrivateKey
     */
    public static PrivateKey string2Privatekey(String priStr) throws NoSuchAlgorithmException, InvalidKeySpecException {
        byte[] bytes = Base64.getDecoder().decode(priStr);
        PKCS8EncodedKeySpec keySpec = new PKCS8EncodedKeySpec(bytes);
        KeyFactory keyFactory = KeyFactory.getInstance("RSA");
        PrivateKey privateKey = keyFactory.generatePrivate(keySpec);
        return privateKey;
    }




    /**
     * 解密
     */
    public static String decrypt(String data, PrivateKey privateKey) throws Exception {
        // 先用base64解码
        byte[] decode = Base64.getDecoder().decode(data);
        // 解密后的内容
        return decrypt(decode, privateKey);
    }

    /**
     * 解密
     */
    public static String decrypt(byte[] data, PrivateKey privateKey) throws Exception {
        Cipher cipher = Cipher.getInstance("RSA");
        cipher.init(Cipher.DECRYPT_MODE, privateKey);
        byte[] cache = cipher.doFinal(data);
        // 解密后的内容
        return new String(cache, StandardCharsets.UTF_8);
    }


    public static String encrypt(String data, PublicKey publicKey) throws Exception {
        Cipher cipher = Cipher.getInstance("RSA");
        cipher.init(Cipher.ENCRYPT_MODE, publicKey);

        byte[] cache = cipher.doFinal(data.getBytes());

        // 加密后的字符串
        return Base64.getEncoder().encodeToString(cache);
    }

    public static void main(String[] args) throws Exception {
        String data = "ysqiKTr100LkDNAjcz89Bhrs8CTASqBaIKQFAr+Bg4uyrtdBxe1JXcYJ1jjLvVSORJ1wKl8OuY5qTbkqKhuxLg==";
        String privateKey = "MIIBVQIBADANBgkqhkiG9w0BAQEFAASCAT8wggE7AgEAAkEA3O0y6hGCVcclyvq8FwO83ZZt73ZK6voozzBKnLuvnl+B2GC5KrzAbcG8SJmT0rlTwMfsWH1roimBFN601iqQ0wIDAQABAkEAucs1dUnSTWcUrnZc9hHrpIKc47Sc3q4Q4eVf6h2hXNKvIJe5fFDVO7siqLQSkBsHQSDz4h4FJIZg4/GaAjrFkQIhAPKDr8SoNUL45gfhL9/9gAWP83NZUN/BrKkfDumW534rAiEA6TYy1pZx4AbJsxOHSMAFpzjg5cO3SB7EkAKerzA/C/kCIQDhowR2xMpJdasQycPxc7sZccXhHMjSZzBuaidFztGz4QIgGUA9EO8JNXCGtlUO+NmRwyFteHNQjQaH0e/2rpFSwkECIH1k21OE/Md+zskk+nz/dLrcoLhJAyx3ahmGrM+i63GC";
        //String publicKey = FileUtil.readString("D:\\code\\publicKey", "UTF-8");
        PrivateKey privateKey1 = string2Privatekey(privateKey);
        System.out.println(decrypt(data, privateKey1));

/*        System.out.println(publicKey);
        PublicKey publicKey1 = string2PublicKey(publicKey);
        String asd = encrypt("ASD", publicKey1);
        System.out.println(encrypt("ASD", publicKey1));*/
        ;

        System.out.println(decrypt(data,privateKey1));
    }

}
