package com.pukka.iptv.auth.security.filter;

import com.pukka.iptv.common.base.util.JwtTokenUtil;
import com.pukka.iptv.common.base.vo.SecurityUser;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.security.authentication.UsernamePasswordAuthenticationToken;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.security.core.userdetails.UserDetails;
import org.springframework.security.core.userdetails.UserDetailsService;
import org.springframework.security.web.authentication.WebAuthenticationDetailsSource;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;
import org.springframework.web.filter.OncePerRequestFilter;

import javax.servlet.FilterChain;
import javax.servlet.ServletException;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;

/**
 * <AUTHOR>
 * @createTime 2021/7/30
 */

@Slf4j
@Component
public class AuthenticationFilter extends OncePerRequestFilter  {
    @Autowired
    private UserDetailsService userDetailsService;
    @Value("${jwt.token.header}")
    private String jwtTokenHeader;

    @Value("${jwt.token.head}")
    private String jwtTokenHead;

    @Value("${manage.login-url}")
    private String loginUrl;

    @Value("${manage.logout-url}")
    private String logoutUrl;

    @Value("${manage.captcha-url}")
    private String captchaUrl;

   /* public TokenFilter(){
        super.setFilterProcessesUrl("/sysUser/login");
        this.setPostOnly(false);
        this.setRequiresAuthenticationRequestMatcher(new AntPathRequestMatcher("/sysUser/login","POST"));

    }*/

    @Override
    protected void doFilterInternal(HttpServletRequest request,
                                    HttpServletResponse response,
                                    FilterChain chain) throws ServletException, IOException {

        // 由request的输入流生成user对象
       /* SysLoginUser sysLoginUser = new ObjectMapper().readValue(request.getInputStream(), SysLoginUser.class);
        return authenticationManager.authenticate(
                new UsernamePasswordAuthenticationToken(
                        sysLoginUser.getUsername(),
                        sysLoginUser.getPassword(),
                        new ArrayList<>())
        );*/
       /* Authentication authentication2 = SecurityContextHolder.getContext().getAuthentication();
        if(authentication2!= null && authentication2.isAuthenticated()){
            ResponseUtil.out(response, CommonResponse.success(authentication2));
        }

        String url = request.getRequestURI();
        if(url.endsWith(loginUrl)){
            String username = request.getParameter("username");
            String password = request.getParameter("password");
            UserDetails userDetails = userDetailsServiceImpl.loadUserByUsername(username);
            if (userDetails != null) {
                UsernamePasswordAuthenticationToken authentication = new UsernamePasswordAuthenticationToken(userDetails, null, userDetails.getAuthorities());
                authentication.setDetails(new WebAuthenticationDetailsSource().buildDetails(request));
                log.info("authenticated user:{}", userDetails.getUsername());
                SecurityContextHolder.getContext().setAuthentication(authentication);
            }
        }else{*/
        //拿到requset中的head
        try {
            String url = request.getRequestURI();
            if (!url.endsWith(loginUrl) && !url.endsWith(logoutUrl) && !url.endsWith(captchaUrl)) {
                String authHeader = request.getHeader(this.jwtTokenHeader);
                //String username = request.getParameter("username");
                if (authHeader != null && authHeader.startsWith(this.jwtTokenHead)) {
                    // The part after "Bearer "
                    String authToken = authHeader.substring(this.jwtTokenHead.length());
                    //解析token获取用户名
                    SecurityUser securityUser = JwtTokenUtil.getSecurityUser(authToken);
                    log.info("checking username:{}", securityUser.getUsername());
                    if (securityUser != null && StringUtils.hasLength(securityUser.getUsername()) && SecurityContextHolder.getContext().getAuthentication() == null) {
                        UserDetails userDetails = userDetailsService.loadUserByUsername(securityUser.getUsername());
                        if (userDetails != null) {
                            UsernamePasswordAuthenticationToken authentication = new UsernamePasswordAuthenticationToken(userDetails, null, userDetails.getAuthorities());
                            authentication.setDetails(new WebAuthenticationDetailsSource().buildDetails(request));
                            log.info("authenticated user:{}", securityUser.getUsername());
                            SecurityContextHolder.getContext().setAuthentication(authentication);
                        }
                    }
                }
            }
        } catch (Exception e) {
            log.error("用户认证失败：{}", e.fillInStackTrace());
        }
        //  }
        chain.doFilter(request, response);
    }

    /*private UsernamePasswordAuthenticationToken getAuthentication(HttpServletRequest request) {
        // token置于header里
        String token = request.getHeader("token");
        if (token != null && !"".equals(token.trim())) {
            String userName = tokenManager.getUserFromToken(token);

            List<String> permissionValueList = (List<String>) redisTemplate.opsForValue().get(userName);
            Collection<GrantedAuthority> authorities = new ArrayList<>();
            for(String permissionValue : permissionValueList) {
                if(StringUtils.isEmpty(permissionValue)) {
                    continue;
                }
                SimpleGrantedAuthority authority = new SimpleGrantedAuthority(permissionValue);
                authorities.add(authority);
            }

            if (!StringUtils.isEmpty(userName)) {
                return new UsernamePasswordAuthenticationToken(userName, token, authorities);
            }
            return null;
        }
        return null;
    }*/

    //验证token
    /*private void validateToken(HttpServletRequest request) {
        //获取前端传来的token,filter中参数都是小写
        String token = request.getHeader("usertoken");
        String loginFailue = "token已过期,请重新登录!";
        if (StringUtils.isBlank(token)) {
            token = request.getParameter("userToken");
            if(StringUtils.isBlank(token)){
                throw new TokenException(loginFailue);
            }
        }
        String useId = jwtUtils.getUseId(token);
        Long issueAt = jwtUtils.getIssueAt(token);
        Long aLong = (Long) redisTemplate.opsForValue().get(UserRedisKeysEnum.MANAGE_TOKEN_REFRESH.join(useId));
        if(StringUtils.isBlank(useId)||issueAt==null||aLong==null){
            throw new TokenException(loginFailue);
        }
        //如果不相等,其他人登录了
        if(!issueAt.equals(aLong)){
            throw new TokenException("该账号已在别处登录,请重新登录!");
        }
        if (jwtUtils.isTokenExpired(token)) {
            throw new TokenException(loginFailue);
        }
        String username = jwtUtils.getUsernameFromToken(token);
        if (!StringUtils.isNotBlank(username)) {
            throw new TokenException(loginFailue);
        }
        CustomUserDetail userDetails = (CustomUserDetail)customerUserDetailsService.loadUserByUsername(username);
        if (userDetails == null) {
            throw new TokenException(loginFailue);
        }
        UsernamePasswordAuthenticationToken authentication = new UsernamePasswordAuthenticationToken(userDetails, null, userDetails.getAuthorities());
        authentication.setDetails(new WebAuthenticationDetailsSource().buildDetails(request));
        //设置为已登录
        SecurityContextHolder.getContext().setAuthentication(authentication);

    }*/

    //验证token
    /*public ApiResponse<String> validateModelToken(String token) {
        String expiredStr = "token已过期,请重新登录!";
        String aFalse = "false";
        if(StringUtils.isBlank(token)){
            return new ApiResponse<>(ApiResponseEnum.USER_TOKEN_INVALID.getCode(), expiredStr, aFalse);
        }
        String useId = jwtUtils.getUseId(token);
        Long issueAt = jwtUtils.getIssueAt(token);
        Long aLong = (Long) redisTemplate.opsForValue().get(UserRedisKeysEnum.MANAGE_TOKEN_REFRESH.join(useId));
        if(StringUtils.isBlank(useId)||issueAt==null||aLong==null){
            return new ApiResponse<>(ApiResponseEnum.USER_TOKEN_INVALID.getCode(), expiredStr, aFalse);
        }
        //如果不相等,其他人登录了
        if(!issueAt.equals(aLong)){
            return new ApiResponse<>(ApiResponseEnum.USER_TOKEN_INVALID.getCode(), "该账号已在别处登录,请重新登录!", aFalse);
        }
        if (jwtUtils.isTokenExpired(token)) {
            return new ApiResponse<>(ApiResponseEnum.USER_TOKEN_INVALID.getCode(), expiredStr, aFalse);
        }
        String username = jwtUtils.getUsernameFromToken(token);
        if (!StringUtils.isNotBlank(username)) {
            return new ApiResponse<>(ApiResponseEnum.USER_TOKEN_INVALID.getCode(), expiredStr, aFalse);
        }
        CustomUserDetail userDetails = (CustomUserDetail)customerUserDetailsService.loadUserByUsername(username);
        if (userDetails == null) {
            return new ApiResponse<>(ApiResponseEnum.FAIL.getCode(), expiredStr, aFalse);
        }
        return null;
    }*/

    //验证验证码
    /*private void validateImage(HttpServletRequest request) {
        //1.获取登录请求的验证码
        String inputCode = request.getParameter("kaptcha");
        log.info("----input kaptcha: {}", inputCode);
        //2.判断验证码是否为空
        if (StringUtils.isBlank(inputCode)) {
            throw new KaptchaException("验证码不能为空!");
        }
        //3.Redis中的验证码
        String uuid = request.getHeader("uuid");
        if (StringUtils.isBlank(inputCode)) {
            throw new KaptchaException("验证码失效,请重新获取!");
        }
        String kaptcha = (String) redisTemplate.opsForValue().get(UserRedisKeysEnum.KAPTCHA.join(uuid));
        //4.判断验证码是否相等
        if (!inputCode.equalsIgnoreCase(kaptcha)) {
            throw new KaptchaException("验证码输入错误!");
        }
    }*/

}
