package com.pukka.iptv.auth.controller;

import com.pukka.iptv.common.base.vo.CommonResponse;
import com.pukka.iptv.common.base.vo.SecurityUser;
import com.pukka.iptv.common.log.annotation.SysLog;
import com.pukka.iptv.common.log.enums.OperateObjectEnum;
import com.pukka.iptv.common.log.enums.OperateTypeEnum;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR>
 * @date 2021/7/19
 * @title 认证
 */

@RestController
@Api(tags = "认证")
public class AuthController {



    @SysLog(objectType = OperateObjectEnum.SYS_USER, operateType = OperateTypeEnum.REFRESH_TOKEN)
    @ApiOperation(value ="刷新令牌" )
    @GetMapping("/refreshToken")
    public CommonResponse<SecurityUser> logout(@RequestHeader(name = "token", required = true) String token) {
        return CommonResponse.success(null);
    }


}
