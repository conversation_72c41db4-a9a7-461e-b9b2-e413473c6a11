package com.pukka.iptv.auth.config;

import lombok.Getter;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.context.annotation.Configuration;

@Configuration
@Getter
@RefreshScope
public class LoginConfig {
    @Value("${manage.login.failMax}")
    private Integer failCountMax;
    @Value("${manage.login.lock-time}")
    private Integer lockTime;
}
