package com.pukka.iptv.auth.security.filter;

import com.pukka.iptv.common.base.enums.CommonResponseEnum;
import com.pukka.iptv.common.core.util.ResponseUtil;
import com.pukka.iptv.common.core.util.StringUtils;
import com.pukka.iptv.common.base.vo.CommonResponse;
import com.pukka.iptv.common.base.util.JwtTokenUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Component;
import org.springframework.web.filter.OncePerRequestFilter;

import javax.servlet.FilterChain;
import javax.servlet.ServletException;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;

/**
 * <AUTHOR>
 * @createTime 2021/7/20
 */
@Component
public class VerifyCodeFilter extends OncePerRequestFilter {

    private String defaultFilterProcessUrl = "/login";
    private String method = "POST";
    @Autowired
    private RedisTemplate redisTemplate;

    @Override
    protected void doFilterInternal(HttpServletRequest request, HttpServletResponse response, FilterChain chain) throws ServletException, IOException {
        if (method.equalsIgnoreCase(request.getMethod()) && defaultFilterProcessUrl.equals(request.getServletPath())) {
            // 登录请求校验验证码，非登录请求不用校验
            //HttpSession session = request.getSession();
            String requestCaptcha = request.getParameter("captcha");
            //验证码的信息存放在seesion种，具体看EasyCaptcha官方解释
            //String genCaptcha = (String) request.getSession().getAttribute("captcha");
            response.setContentType("application/json;charset=UTF-8");
//            String uuid = request.getHeader("uuid");
            String uuid = request.getParameter("uuid");
            if (!StringUtils.hasLength(uuid)) {
                ResponseUtil.out(response, CommonResponse.general(CommonResponseEnum.CAPTCHA_ERROR,"验证码无效"));
                return;
            }
            //3.Redis中的验证码
            String kaptcha = (String) redisTemplate.opsForValue().get(JwtTokenUtil.getUserRedisCaptchaKey(uuid));
            //删除缓存里的验证码信息
            redisTemplate.delete(JwtTokenUtil.getUserRedisCaptchaKey(uuid));
            if (!StringUtils.hasLength(requestCaptcha)) {
                ResponseUtil.out(response, CommonResponse.general(CommonResponseEnum.CAPTCHA_ERROR, "验证码为空"));
                return;
            }
            if (!StringUtils.hasLength(kaptcha)) {
                ResponseUtil.out(response, CommonResponse.general(CommonResponseEnum.CAPTCHA_ERROR, "验证码过期"));
                return;
            }

            if (!requestCaptcha.equalsIgnoreCase(kaptcha)) {
                ResponseUtil.out(response, CommonResponse.general(CommonResponseEnum.CAPTCHA_ERROR, "验证码错误"));
                return;
            }

        }
        chain.doFilter(request, response);
    }
}
