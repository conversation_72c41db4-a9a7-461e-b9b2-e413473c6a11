package com.pukka.iptv.auth.util;

import com.pukka.iptv.auth.config.LoginConfig;
import com.pukka.iptv.auth.constants.StatusConstants;
import com.pukka.iptv.auth.exception.LoginLockException;
import com.pukka.iptv.common.core.util.SpringUtils;
import com.pukka.iptv.common.core.util.StringUtils;
import com.pukka.iptv.common.redis.util.RedisUtils;

import java.util.concurrent.TimeUnit;

/**
 * 登录失败处理机制
 */
public class UserFailApi {
    private static volatile UserFailApi userFailApi;
    private final RedisUtils redisUtils = SpringUtils.getBean(RedisUtils.class);
    private final LoginConfig loginConfig = SpringUtils.getBean(LoginConfig.class);

    public static UserFailApi getInstance() {
        if (userFailApi == null) {
            synchronized (UserFailApi.class) {
                if (userFailApi == null) {
                    userFailApi = new UserFailApi();
                }
            }
        }
        return userFailApi;
    }

    /**
     * 根据用户名获取到登录失败次数
     *
     * @param userName
     * @return
     */
    public Integer getUserFailCache(String userName) {
        if (StringUtils.isEmpty(userName)) {
            return null;
        }
        String count = (String) redisUtils.get(StatusConstants.USER_FAIL + "-" + userName);
        if (StringUtils.isEmpty(count)) {
            return null;
        }
        return Integer.parseInt(count);
    }

    /**
     * 根据用户名检查登录次数是否达到最大值
     *
     * @param userName
     * @return
     */
    public void checkFailCount(String userName) {
        Integer failCount = getUserFailCache(userName);
        if (failCount != null && loginConfig.getFailCountMax() <= failCount) {
            throw new LoginLockException("锁定账号");
        }
    }

    /**
     * 登录失败次数新增
     *
     * @param userName
     * @return
     */
    public Integer setUserFailCache(String userName) {
        if (StringUtils.isEmpty(userName)) return null;
        Integer failCount = getUserFailCache(userName);
        if (failCount == null) {
            redisUtils.setEx(generateUserFailKeyName(userName), 1 + "", 10, TimeUnit.MINUTES);
            return 1;
        } else {
            if (failCount == (loginConfig.getFailCountMax() - 1)) {
                redisUtils.setEx(generateUserFailKeyName(userName), failCount + 1 + "", loginConfig.getLockTime(), TimeUnit.MINUTES);
                return loginConfig.getFailCountMax();
            }
            redisUtils.setEx(generateUserFailKeyName(userName), failCount + 1 + "", 10, TimeUnit.MINUTES);
            return ++failCount;
        }
    }

    private String generateUserFailKeyName(String name) {
        return StatusConstants.USER_FAIL + "-" + name;
    }

    public void removeUser(String userName) {
        redisUtils.delete(generateUserFailKeyName(userName));
    }

    public Long getLockTime(String userName) {
        return redisUtils.getExpire(generateUserFailKeyName(userName), TimeUnit.MINUTES);
    }
}
