package com.pukka.iptv.auth.security.handler;


import com.pukka.iptv.common.api.feign.sys.SysUserFeignClient;
import com.pukka.iptv.common.base.enums.CommonResponseEnum;
import com.pukka.iptv.common.core.constant.CacheConstants;
import com.pukka.iptv.common.core.util.ResponseUtil;
import com.pukka.iptv.common.base.vo.CommonResponse;
import com.pukka.iptv.common.base.util.JwtTokenUtil;
import com.pukka.iptv.common.base.vo.SecurityUser;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.security.web.authentication.logout.LogoutSuccessHandler;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;
import javax.servlet.ServletException;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.Objects;

/**
 * <AUTHOR>
 * @date 2021/7/15
 * @title 退出登录
 */
@Component
public class LogoutSuccessHandlerImpl implements LogoutSuccessHandler {
    @Autowired
    private RedisTemplate redisTemplate;

    @Autowired
    SysUserFeignClient sysUserFeignClient;

    @Override
    public void onLogoutSuccess(HttpServletRequest request, HttpServletResponse response, Authentication authentication) throws IOException, ServletException {
        Authentication auth = SecurityContextHolder.getContext().getAuthentication();
        if (auth != null) {
            //删除Redis中的登录信息,版本号自动过期
            String token = getUserTokenFromHeader(request);
            if (StringUtils.hasLength(token)) {
                SecurityUser securityUser = JwtTokenUtil.getSecurityUser(token);
                if(Objects.nonNull(securityUser)){
                    redisTemplate.delete(JwtTokenUtil.getUserRedisTokenKey(securityUser.getId()));
                    //退出成功
                    sysUserFeignClient.logout(securityUser.getUsername(),securityUser.getId());
                }
            }
        }
        String msg = com.pukka.iptv.common.core.util.StringUtils.format("退出成功");
        ResponseUtil.out(response, CommonResponse.general(CommonResponseEnum.SUCCESS,msg));
    }

    /**
     * 获取用户token
     * @param request
     * @return
     */
    private String getUserTokenFromHeader(HttpServletRequest request) {
        String token = request.getHeader(CacheConstants.HEADER);
        if (StringUtils.hasLength(token) && token.startsWith(CacheConstants.TOKEN_PREFIX)) {
            token = token.replace(CacheConstants.TOKEN_PREFIX, "");
        }
        return token;
    }

}