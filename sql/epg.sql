-- 分发事件表
CREATE TABLE `epg_out_order` (
                                 `id` bigint NOT NULL AUTO_INCREMENT COMMENT 'ObjectID',
                                 `file_set_id` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '唯一ID,时间戳yyyymmddHHmmss+自增序号格式',
                                 `node_code` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL,
                                 `node_name` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '节点名称',
                                 `template_code` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL,
                                 `template_name` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '模版名称',
                                 `epg_file_code` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL,
                                 `epg_file_name` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '文件名称',
                                 `status` tinyint DEFAULT NULL COMMENT '状态1：待处理2：处理中3：成功4：失败',
                                 `remark` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci COMMENT '备注',
                                 `publish_time` date DEFAULT NULL COMMENT '发布时间',
                                 `uncompression` tinyint DEFAULT NULL COMMENT '解压状态\r\n0：不解压\r\n1：解压',
                                 `status_description` varchar(1024) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '状态描述',
                                 `cmd_file_url` varchar(1024) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '工单文件',
                                 `result_file_url` varchar(1024) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '下游反馈工单地址',
                                 `duration` int DEFAULT NULL COMMENT '发布时长',
                                 `feedback_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '工单下游反馈完成时间',
                                 `begin_time` datetime DEFAULT NULL COMMENT '生效时间',
                                 `creator_id` bigint DEFAULT NULL COMMENT '操作员id',
                                 `creator_name` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '操作员name',
                                 `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
                                 `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
                                 PRIMARY KEY (`id`) USING BTREE,
                                 KEY `index_file_set_id` (`file_set_id`) USING BTREE,
                                 KEY `index_node_code` (`node_code`) USING BTREE,
                                 KEY `index_epg_code` (`epg_file_code`) USING BTREE,
                                 KEY `index_status` (`status`) USING BTREE,
                                 KEY `index_create_time` (`create_time`),
                                 KEY `index_template_code` (`template_code`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci ROW_FORMAT=DYNAMIC COMMENT='事件分发表';

-- 文件管理表
CREATE TABLE `epg_file` (
                            `id` bigint NOT NULL AUTO_INCREMENT,
                            `code` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '文件唯一编码',
                            `file_id` varchar(32) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '唯一ID,时间戳yyyymmddHHmmss+自增序号格式',
                            `show_name` varchar(256) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '文件名称,不可重复',
                            `description` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci COMMENT '文件描述',
                            `creator_id` bigint DEFAULT NULL COMMENT '创建人ID，来自于user表',
                            `creator_name` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '创建人',
                            `file_path` varchar(256) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '文件ftp地址',
                            `system_file` tinyint DEFAULT NULL COMMENT '是否为系统层文件,0:否，1:是',
                            `md5` varchar(256) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT 'md5值',
                            `status` tinyint DEFAULT '1' COMMENT '状态 1：有效 2：无效，默认有效',
                            `storage_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '所属存储',
                            `storage_id` bigint DEFAULT NULL COMMENT '存储ID',
                            `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '上传时间',
                            `update_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '更新时间',
                            PRIMARY KEY (`id`) USING BTREE,
                            KEY `idx_name` (`show_name`) USING BTREE,
                            KEY `idx_code` (`code`) USING BTREE,
                            KEY `idx_create_time` (`create_time` DESC) USING BTREE,
                            KEY `index_file_id` (`file_id`) USING BTREE,
                            KEY `index_file_path` (`file_path`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=7 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci ROW_FORMAT=DYNAMIC COMMENT='epg_files';

-- 分发节点表
CREATE TABLE `epg_node` (
                            `id` bigint NOT NULL AUTO_INCREMENT,
                            `name` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT 'NULL' COMMENT 'lsp名称',
                            `code` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '唯一编码',
                            `csp_id` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '默认HBUT',
                            `lsp_id` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT 'lspid',
                            `negotiate` tinyint DEFAULT '1' COMMENT '使用协议,默认协议1',
                            `path` varchar(1024) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '下发地址',
                            `creator_name` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '创建人来自admin表',
                            `creator_id` bigint DEFAULT NULL COMMENT '来自admin表',
                            `epg_template_names` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '关联分发模版名称，多个使用英文,分割开',
                            `epg_template_codes` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '分发模版列表',
                            `status` int DEFAULT '1' COMMENT '状态 1：有效 2：无效，默认有效',
                            `create_time` datetime DEFAULT CURRENT_TIMESTAMP,
                            `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                            PRIMARY KEY (`id`) USING BTREE,
                            KEY `index_name` (`name`) USING BTREE,
                            KEY `index_status` (`status`) USING BTREE,
                            KEY `idx_lsp_id` (`lsp_id`) USING BTREE,
                            KEY `index_create_time` (`create_time`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci ROW_FORMAT=DYNAMIC COMMENT='分发节点';

-- 模版路径表
CREATE TABLE `epg_template` (
                                `id` bigint NOT NULL AUTO_INCREMENT,
                                `name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '分发模版',
                                `code` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '唯一编码',
                                `creator_name` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '创建人来自admin表',
                                `creator_id` bigint DEFAULT NULL COMMENT '来自admin表',
                                `path` varchar(256) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT 'epg路径',
                                `node_name` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT 'NULL' COMMENT 'lsp名称',
                                `node_code` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT 'epg唯一编码',
                                `status` int DEFAULT '1' COMMENT '状态 1：有效 2：无效，默认有效',
                                `description` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci COMMENT '描述',
                                `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
                                `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
                                PRIMARY KEY (`id`) USING BTREE,
                                KEY `idx_name` (`name`) USING BTREE,
                                KEY `idx_code` (`code`) USING BTREE,
                                KEY `idx_status` (`status`) USING BTREE,
                                KEY `idx_node_code` (`node_code`) USING BTREE,
                                KEY `idx_node_name` (`node_name`) USING BTREE,
                                KEY `idx_create_time` (`create_time`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci ROW_FORMAT=DYNAMIC COMMENT='分发模版';

-- 日志记录表
CREATE TABLE `sys_log` (
                           `id` bigint NOT NULL AUTO_INCREMENT,
                           `operate_type` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '操作类型',
                           `object_names` varchar(1024) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '对象名称',
                           `object_ids` varchar(1024) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '对象ID',
                           `object_type` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '对象类型',
                           `request_function` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '请求方法',
                           `operate_result` varchar(2) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '操作结果',
                           `creator_id` bigint DEFAULT NULL COMMENT '操作员ID',
                           `creator_name` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '操作员名称',
                           `creator_username` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '操作员账号',
                           `client_address` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '客户端地址',
                           `server_address` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '服务端地址',
                           `create_time` datetime DEFAULT NULL COMMENT '创建时间',
                           `update_time` datetime DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
                           `description` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci COMMENT '描述',
                           `detail` varchar(4096) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '详情',
                           `exception` varchar(1024) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '异常',
                           `duration` bigint DEFAULT NULL COMMENT '请求耗时',
                           `request_param` varchar(4096) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '请求参数',
                           `request_method` varchar(8) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL,
                           PRIMARY KEY (`id`) USING BTREE,
                           KEY `index_creator_username` (`creator_username`),
                           KEY `index_operate_result` (`operate_result`),
                           KEY `index_createtime` (`create_time`) USING BTREE,
                           KEY `index_operate_type` (`operate_type`),
                           KEY `index_object_names` (`object_names`(755)) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci ROW_FORMAT=DYNAMIC COMMENT='系统日志';

-- 系统用户表
CREATE TABLE `sys_user` (
                            `id` bigint NOT NULL AUTO_INCREMENT COMMENT 'ID',
                            `name` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '名称',
                            `username` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '账号',
                            `password` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '密码',
                            `role_id` bigint DEFAULT NULL COMMENT '角色id',
                            `gender` tinyint DEFAULT NULL COMMENT '性别 1：男 2：女',
                            `avatar` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '头像',
                            `birthday` date DEFAULT NULL COMMENT '生日',
                            `phone` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '手机',
                            `email` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '邮箱',
                            `type` tinyint DEFAULT NULL COMMENT '用户类型 1：超级管理员 2：管理类 3：普通用户类 ',
                            `status` tinyint DEFAULT '1' COMMENT '状态 1：有效 2：无效，默认有效',
                            `creator_id` bigint DEFAULT NULL COMMENT '作者ID',
                            `creator_name` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '作者',
                            `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
                            `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
                            `description` varchar(1024) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '备注',
                            PRIMARY KEY (`id`) USING BTREE,
                            KEY `index_name` (`name`) USING BTREE,
                            KEY `index_type` (`type`) USING BTREE,
                            KEY `index_status` (`status`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci ROW_FORMAT=DYNAMIC COMMENT='系统用户表';

-- 角色表
CREATE TABLE `sys_role` (
                            `id` bigint NOT NULL AUTO_INCREMENT COMMENT 'ID',
                            `name` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '名称',
                            `type` tinyint DEFAULT NULL COMMENT '类型 1：系统 2：普通用户',
                            `status` tinyint DEFAULT NULL COMMENT '状态 1：有效 2：无效，默认有效',
                            `creator_id` bigint DEFAULT NULL COMMENT '作者ID',
                            `creator_name` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '作者',
                            `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
                            `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
                            `description` varchar(1024) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '备注',
                            PRIMARY KEY (`id`) USING BTREE,
                            KEY `index_name` (`name`) USING BTREE,
                            KEY `index_type` (`type`) USING BTREE,
                            KEY `index_status` (`status`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci ROW_FORMAT=DYNAMIC COMMENT='角色表';

-- 角色菜单关系
CREATE TABLE `sys_role_menu` (
                                 `id` bigint NOT NULL AUTO_INCREMENT COMMENT 'ID',
                                 `role_id` bigint DEFAULT NULL COMMENT '角色ID',
                                 `menu_params` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci COMMENT '菜单参数集',
                                 `create_time` datetime DEFAULT CURRENT_TIMESTAMP,
                                 `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                                 PRIMARY KEY (`id`) USING BTREE,
                                 KEY `index_role_id` (`role_id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci ROW_FORMAT=DYNAMIC COMMENT='角色菜单关系';

-- 用户模版关系
CREATE TABLE `sys_user_template` (
                                     `id` bigint NOT NULL AUTO_INCREMENT COMMENT 'ID',
                                     `user_id` bigint DEFAULT NULL COMMENT '用户ID',
                                     `template_id` bigint DEFAULT NULL COMMENT '模版ID',
                                     `create_time` datetime DEFAULT CURRENT_TIMESTAMP,
                                     `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                                     PRIMARY KEY (`id`) USING BTREE,
                                     KEY `index_role_id` (`user_id`) USING BTREE,
                                     KEY `index_template_ids` (`template_id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci ROW_FORMAT=DYNAMIC COMMENT='用户模版关系';

-- 菜单表
CREATE TABLE `sys_menu` (
                            `icon` varchar(255) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '图标',
                            `id` bigint NOT NULL AUTO_INCREMENT COMMENT 'ID',
                            `name` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '名称',
                            `parent_id` bigint DEFAULT NULL COMMENT '父菜单ID',
                            `path` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '路径',
                            `status` tinyint DEFAULT NULL COMMENT '状态 1：有效 2：无效，默认有效',
                            `creator_id` bigint DEFAULT NULL COMMENT '作者ID',
                            `creator_name` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '作者',
                            `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
                            `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
                            `description` varchar(1024) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '备注',
                            PRIMARY KEY (`id`) USING BTREE,
                            KEY `index_name` (`name`) USING BTREE,
                            KEY `index_status` (`status`) USING BTREE,
                            KEY `index_parent_id` (`parent_id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci ROW_FORMAT=DYNAMIC COMMENT='菜单';