/*
 Navicat Premium Data Transfer

 Source Server         : 湖北播控开发环境
 Source Server Type    : MySQL
 Source Server Version : 80029
 Source Host           : ***************:3306
 Source Schema         : iptv

 Target Server Type    : MySQL
 Target Server Version : 80029
 File Encoding         : 65001

 Date: 09/08/2022 09:08:16
*/

SET NAMES utf8mb4;
SET
FOREIGN_KEY_CHECKS = 0;

-- ----------------------------
-- Table structure for statistics_disk_space
-- ----------------------------
DROP TABLE IF EXISTS `statistics_disk_space`;
CREATE TABLE `statistics_disk_space`
(
    `id`              bigint NOT NULL AUTO_INCREMENT COMMENT '主键',
    `total_space`     bigint NULL DEFAULT NULL COMMENT '总空间大小',
    `used_space`      bigint NULL DEFAULT NULL COMMENT '已使用磁盘空间大小',
    `used_proportion` double(5, 4
) NULL DEFAULT NULL COMMENT '已使用比例',
  `storage_id` bigint NULL DEFAULT NULL COMMENT '存储盘id',
  `storage_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '存储盘名称',
  `cp_id` bigint NULL DEFAULT NULL COMMENT 'CPID',
  `cp_name` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT 'CP名称',
  `statistic_date` datetime NULL DEFAULT NULL COMMENT '统计日期',
  `create_time` datetime NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1549206203601638212 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '磁盘空间统计表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for statistics_online
-- ----------------------------
DROP TABLE IF EXISTS `statistics_online`;
CREATE TABLE `statistics_online`
(
    `id`                  bigint NOT NULL AUTO_INCREMENT COMMENT '主键',
    `content_type`        tinyint NULL DEFAULT NULL COMMENT '内容类型\\\r\n1：电影 \r\n2：子集\r\n3：电视剧\r\n4：系列片\r\n5：片花',
    `count`               int NULL DEFAULT NULL COMMENT '媒资数量',
    `duration`            bigint NULL DEFAULT NULL COMMENT '视频长度（s）',
    `file_size`           bigint NULL DEFAULT NULL COMMENT '视频大小',
    `media_online_status` tinyint NULL DEFAULT NULL COMMENT '统计类型\r\n1线上节目汇总\r\n2节目上新\r\n3节目下线',
    `definition_flag`     tinyint NULL DEFAULT NULL COMMENT '节目清晰度标识\r\n0：标清\r\n1：高清\r\n2：超清\r\n3. 4K\r\n4. 杜比（4K+杜比）\r\n5.H264\r\n6.H264(标清)\r\n7.H264(高清)\r\n8.H264(4K)\r\n9.H265(高清)\r\n10.H265(4K)',
    `media_bind_status`   tinyint NULL DEFAULT NULL COMMENT '节目状态\r\n1.单节目发布\r\n2.节目已绑栏目发布\r\n3.节目未绑栏目发布',
    `cp_id`               bigint NULL DEFAULT NULL,
    `cp_name`             varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT 'CP名称',
    `sp_id`               bigint NULL DEFAULT NULL,
    `sp_name`             varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT 'SP名称',
    `bms_sp_channel_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '所属渠道',
    `bms_sp_channel_id`   bigint NULL DEFAULT NULL COMMENT '所属渠道id',
    `statistic_date`      datetime NULL DEFAULT NULL COMMENT '统计日期',
    `create_time`         datetime NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_time`         datetime NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1555067950030860292 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '下发统计表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for statistics_in
-- ----------------------------
DROP TABLE IF EXISTS `statistics_in`;
CREATE TABLE `statistics_in`  (
  `id` bigint NOT NULL AUTO_INCREMENT,
  `type` tinyint NULL DEFAULT NULL COMMENT '统计类型:\r\n0. 单集\r\n1.子集\r\n2.电视剧\r\n3.媒资介质',
  `content_type` int NULL DEFAULT NULL COMMENT '媒资类型',
  `count` int NULL DEFAULT NULL COMMENT '数量',
  `duration` bigint NULL DEFAULT NULL,
  `cp_id` bigint NULL DEFAULT NULL,
  `cp_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL,
  `statistic_date` datetime NULL DEFAULT NULL COMMENT '统计日期',
  `create_time` datetime NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT ' 修改时间',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `index_content_type`(`content_type`) USING BTREE,
  INDEX `index_cp_id`(`cp_id`) USING BTREE,
  INDEX `index_statistic_date`(`statistic_date`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1559413358777778178 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for statistics_in_again
-- ----------------------------
DROP TABLE IF EXISTS `statistics_in_again`;
CREATE TABLE `statistics_in_again`  (
  `id` bigint NOT NULL AUTO_INCREMENT,
  `type` tinyint NULL DEFAULT NULL COMMENT '统计类型',
  `content_type` int NULL DEFAULT NULL COMMENT '媒资类型',
  `count` int NULL DEFAULT NULL COMMENT '数量',
  `duration` bigint NULL DEFAULT NULL,
  `cp_id` bigint NULL DEFAULT NULL,
  `cp_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL,
  `statistic_date` datetime NULL DEFAULT NULL COMMENT '统计日期',
  `create_time` datetime NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT ' 修改时间',
  `pass_type` tinyint NULL DEFAULT NULL COMMENT '0:通过； 1：不通过',
  `operator` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '操作员',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `index_content_type`(`content_type`) USING BTREE,
  INDEX `index_cp_id`(`cp_id`) USING BTREE,
  INDEX `index_statistic_date`(`statistic_date`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1559828298422915076 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci ROW_FORMAT = DYNAMIC;


-- ----------------------------
-- Table structure for statistics_in_again_init
-- ----------------------------
DROP TABLE IF EXISTS `statistics_in_again_init`;
CREATE TABLE `statistics_in_again_init`  (
  `id` bigint NOT NULL AUTO_INCREMENT,
  `statistic_date` datetime NULL DEFAULT NULL COMMENT '需要统计的日期',
  `status` tinyint NULL DEFAULT NULL,
  `create_time` datetime NULL DEFAULT CURRENT_TIMESTAMP,
  `update_time` datetime NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `index_statistic_date`(`statistic_date`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1559824352163561477 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for statistics_in_check
-- ----------------------------
DROP TABLE IF EXISTS `statistics_in_check`;
CREATE TABLE `statistics_in_check`  (
  `id` bigint NOT NULL AUTO_INCREMENT,
  `type` tinyint NULL DEFAULT NULL COMMENT '统计类型',
  `content_type` int NULL DEFAULT NULL COMMENT '媒资类型',
  `count` int NULL DEFAULT NULL COMMENT '数量',
  `duration` bigint NULL DEFAULT NULL,
  `cp_id` bigint NULL DEFAULT NULL,
  `cp_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL,
  `statistic_date` datetime NULL DEFAULT NULL COMMENT '统计日期',
  `create_time` datetime NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT ' 修改时间',
  `statistic_type` tinyint NULL DEFAULT NULL COMMENT '1.自审；2.终审',
  `pass_type` tinyint NULL DEFAULT NULL COMMENT '0:通过； 1：不通过',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `index_content_type`(`content_type`) USING BTREE,
  INDEX `index_cp_id`(`cp_id`) USING BTREE,
  INDEX `index_statistic_date`(`statistic_date`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1559447674974007302 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci ROW_FORMAT = DYNAMIC;


-- ----------------------------
-- Table structure for statistics_in_check_init
-- ----------------------------
DROP TABLE IF EXISTS `statistics_in_check_init`;
CREATE TABLE `statistics_in_check_init`  (
  `id` bigint NOT NULL AUTO_INCREMENT,
  `statistic_date` datetime NULL DEFAULT NULL COMMENT '需要统计的日期',
  `status` tinyint NULL DEFAULT NULL,
  `create_time` datetime NULL DEFAULT CURRENT_TIMESTAMP,
  `update_time` datetime NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `index_statistic_date`(`statistic_date`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1559430443208413189 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci ROW_FORMAT = DYNAMIC;

SET FOREIGN_KEY_CHECKS = 1;

-- ----------------------------
-- Table structure for statistics_in_delete
-- ----------------------------

DROP TABLE IF EXISTS `statistics_in_delete`;
CREATE TABLE `statistics_in_delete`  (
  `id` bigint NOT NULL AUTO_INCREMENT,
  `type` tinyint NULL DEFAULT NULL COMMENT '统计类型',
  `content_type` int NULL DEFAULT NULL COMMENT '媒资类型',
  `code` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '媒资code',
  `name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '媒资名称',
  `cms_content_id` bigint NULL DEFAULT NULL,
  `cms_content_code` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL,
  `statictic_date` datetime NULL DEFAULT NULL COMMENT '删除的时间',
  `status` tinyint NULL DEFAULT NULL COMMENT '状态（0 未处理， 1已处理）',
  `create_time` datetime NULL DEFAULT CURRENT_TIMESTAMP,
  `update_time` datetime NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `index_content_type`(`content_type`) USING BTREE,
  INDEX `index_statictic_date`(`statictic_date`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1561912327511261186 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for statistics_in_final
-- ----------------------------
DROP TABLE IF EXISTS `statistics_in_final`;
CREATE TABLE `statistics_in_final`  (
  `id` bigint NOT NULL AUTO_INCREMENT,
  `type` tinyint NULL DEFAULT NULL COMMENT '统计类型',
  `content_type` int NULL DEFAULT NULL COMMENT '媒资类型',
  `count` int NULL DEFAULT NULL COMMENT '数量',
  `duration` bigint NULL DEFAULT NULL,
  `cp_id` bigint NULL DEFAULT NULL,
  `cp_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL,
  `statistic_date` datetime NULL DEFAULT NULL COMMENT '统计日期',
  `create_time` datetime NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT ' 修改时间',
  `pass_type` tinyint NULL DEFAULT NULL COMMENT '0:通过； 1：不通过',
  `operator` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '操作员',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `index_content_type`(`content_type`) USING BTREE,
  INDEX `index_cp_id`(`cp_id`) USING BTREE,
  INDEX `index_statistic_date`(`statistic_date`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1562014560445734914 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for statistics_in_final_init
-- ----------------------------
DROP TABLE IF EXISTS `statistics_in_final_init`;
CREATE TABLE `statistics_in_final_init`  (
  `id` bigint NOT NULL AUTO_INCREMENT,
  `statistic_date` datetime NULL DEFAULT NULL COMMENT '需要统计的日期',
  `status` tinyint NULL DEFAULT NULL,
  `create_time` datetime NULL DEFAULT CURRENT_TIMESTAMP,
  `update_time` datetime NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `index_statistic_date`(`statistic_date`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1562015726304411651 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci ROW_FORMAT = DYNAMIC;
-- ----------------------------
-- Table structure for statistics_in_init
-- ----------------------------
DROP TABLE IF EXISTS `statistics_in_init`;
CREATE TABLE `statistics_in_init`
(
    `id`             bigint NOT NULL AUTO_INCREMENT,
    `statistic_date` datetime NULL DEFAULT NULL COMMENT '需要统计的日期',
    `status`         tinyint NULL DEFAULT NULL,
    `create_time`    datetime NULL DEFAULT CURRENT_TIMESTAMP,
    `update_time`    datetime NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    PRIMARY KEY (`id`) USING BTREE,
    INDEX            `index_statistic_date`(`statistic_date` ASC) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1550022759479365639 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci ROW_FORMAT = Dynamic;

ALTER TABLE `sys_cp`
    ADD COLUMN `total_space` bigint NULL DEFAULT NULL COMMENT 'CP总空间大小（单位Kb）' ,
    ADD COLUMN `space_threshold` double(5, 4) NULL DEFAULT 0.8000 COMMENT 'CP磁盘预警阈值',
    ADD COLUMN `used_space` bigint NULL DEFAULT 0 COMMENT '老版上传工具已使用空间';

ALTER TABLE `sys_storage`
    ADD COLUMN `total_space` bigint NULL DEFAULT NULL COMMENT '存储磁盘总空间（单位Kb）';

ALTER TABLE `out_order_item_1`
    ADD COLUMN `duration` int NULL COMMENT '发布时长' AFTER `error_info`,ADD COLUMN `feedback_time` datetime(0) NULL DEFAULT CURRENT_TIMESTAMP(0) COMMENT '工单下游反馈完成时间' AFTER `duration`;
ALTER TABLE `out_order_item_2`
   ADD COLUMN `duration` int NULL COMMENT '发布时长' AFTER `error_info`,ADD COLUMN `feedback_time` datetime(0) NULL DEFAULT CURRENT_TIMESTAMP(0) COMMENT '工单下游反馈完成时间' AFTER `duration`;
ALTER TABLE `out_order_item_3`
   ADD COLUMN `duration` int NULL COMMENT '发布时长' AFTER `error_info`,ADD COLUMN `feedback_time` datetime(0) NULL DEFAULT CURRENT_TIMESTAMP(0) COMMENT '工单下游反馈完成时间' AFTER `duration`;
ALTER TABLE `out_order_item_4`
   ADD COLUMN `duration` int NULL COMMENT '发布时长' AFTER `error_info`,ADD COLUMN `feedback_time` datetime(0) NULL DEFAULT CURRENT_TIMESTAMP(0) COMMENT '工单下游反馈完成时间' AFTER `duration`;
ALTER TABLE `out_order_item_5`
   ADD COLUMN `duration` int NULL COMMENT '发布时长' AFTER `error_info`,ADD COLUMN `feedback_time` datetime(0) NULL DEFAULT CURRENT_TIMESTAMP(0) COMMENT '工单下游反馈完成时间' AFTER `duration`;
ALTER TABLE `out_order_item_6`
   ADD COLUMN `duration` int NULL COMMENT '发布时长' AFTER `error_info`,ADD COLUMN `feedback_time` datetime(0) NULL DEFAULT CURRENT_TIMESTAMP(0) COMMENT '工单下游反馈完成时间' AFTER `duration`;
ALTER TABLE `out_order_item_7`
   ADD COLUMN `duration` int NULL COMMENT '发布时长' AFTER `error_info`,ADD COLUMN `feedback_time` datetime(0) NULL DEFAULT CURRENT_TIMESTAMP(0) COMMENT '工单下游反馈完成时间' AFTER `duration`;
ALTER TABLE `out_order_item_8`
   ADD COLUMN `duration` int NULL COMMENT '发布时长' AFTER `error_info`,ADD COLUMN `feedback_time` datetime(0) NULL DEFAULT CURRENT_TIMESTAMP(0) COMMENT '工单下游反馈完成时间' AFTER `duration`;
ALTER TABLE `out_order_item_9`
   ADD COLUMN `duration` int NULL COMMENT '发布时长' AFTER `error_info`,ADD COLUMN `feedback_time` datetime(0) NULL DEFAULT CURRENT_TIMESTAMP(0) COMMENT '工单下游反馈完成时间' AFTER `duration`;
ALTER TABLE `out_order_item_10`
   ADD COLUMN `duration` int NULL COMMENT '发布时长' AFTER `error_info`,ADD COLUMN `feedback_time` datetime(0) NULL DEFAULT CURRENT_TIMESTAMP(0) COMMENT '工单下游反馈完成时间' AFTER `duration`;
ALTER TABLE `out_order_item_11`
   ADD COLUMN `duration` int NULL COMMENT '发布时长' AFTER `error_info`,ADD COLUMN `feedback_time` datetime(0) NULL DEFAULT CURRENT_TIMESTAMP(0) COMMENT '工单下游反馈完成时间' AFTER `duration`;
ALTER TABLE `out_order_item_12`
   ADD COLUMN `duration` int NULL COMMENT '发布时长' AFTER `error_info`,ADD COLUMN `feedback_time` datetime(0) NULL DEFAULT CURRENT_TIMESTAMP(0) COMMENT '工单下游反馈完成时间' AFTER `duration`;
ALTER TABLE `out_order_item_13`
   ADD COLUMN `duration` int NULL COMMENT '发布时长' AFTER `error_info`,ADD COLUMN `feedback_time` datetime(0) NULL DEFAULT CURRENT_TIMESTAMP(0) COMMENT '工单下游反馈完成时间' AFTER `duration`;
ALTER TABLE `out_order_item_14`
   ADD COLUMN `duration` int NULL COMMENT '发布时长' AFTER `error_info`,ADD COLUMN `feedback_time` datetime(0) NULL DEFAULT CURRENT_TIMESTAMP(0) COMMENT '工单下游反馈完成时间' AFTER `duration`;
ALTER TABLE `out_order_item_15`
   ADD COLUMN `duration` int NULL COMMENT '发布时长' AFTER `error_info`,ADD COLUMN `feedback_time` datetime(0) NULL DEFAULT CURRENT_TIMESTAMP(0) COMMENT '工单下游反馈完成时间' AFTER `duration`;
ALTER TABLE `out_order_item_16`
   ADD COLUMN `duration` int NULL COMMENT '发布时长' AFTER `error_info`,ADD COLUMN `feedback_time` datetime(0) NULL DEFAULT CURRENT_TIMESTAMP(0) COMMENT '工单下游反馈完成时间' AFTER `duration`;
ALTER TABLE `out_order_item_17`
   ADD COLUMN `duration` int NULL COMMENT '发布时长' AFTER `error_info`,ADD COLUMN `feedback_time` datetime(0) NULL DEFAULT CURRENT_TIMESTAMP(0) COMMENT '工单下游反馈完成时间' AFTER `duration`;
ALTER TABLE `out_order_item_18`
   ADD COLUMN `duration` int NULL COMMENT '发布时长' AFTER `error_info`,ADD COLUMN `feedback_time` datetime(0) NULL DEFAULT CURRENT_TIMESTAMP(0) COMMENT '工单下游反馈完成时间' AFTER `duration`;
ALTER TABLE `out_order_item_19`
   ADD COLUMN `duration` int NULL COMMENT '发布时长' AFTER `error_info`,ADD COLUMN `feedback_time` datetime(0) NULL DEFAULT CURRENT_TIMESTAMP(0) COMMENT '工单下游反馈完成时间' AFTER `duration`;
ALTER TABLE `out_order_item_20`
   ADD COLUMN `duration` int NULL COMMENT '发布时长' AFTER `error_info`,ADD COLUMN `feedback_time` datetime(0) NULL DEFAULT CURRENT_TIMESTAMP(0) COMMENT '工单下游反馈完成时间' AFTER `duration`;
ALTER TABLE `out_order_item_21`
   ADD COLUMN `duration` int NULL COMMENT '发布时长' AFTER `error_info`,ADD COLUMN `feedback_time` datetime(0) NULL DEFAULT CURRENT_TIMESTAMP(0) COMMENT '工单下游反馈完成时间' AFTER `duration`;
ALTER TABLE `out_order_item_22`
   ADD COLUMN `duration` int NULL COMMENT '发布时长' AFTER `error_info`,ADD COLUMN `feedback_time` datetime(0) NULL DEFAULT CURRENT_TIMESTAMP(0) COMMENT '工单下游反馈完成时间' AFTER `duration`;
ALTER TABLE `out_order_item_23`
   ADD COLUMN `duration` int NULL COMMENT '发布时长' AFTER `error_info`,ADD COLUMN `feedback_time` datetime(0) NULL DEFAULT CURRENT_TIMESTAMP(0) COMMENT '工单下游反馈完成时间' AFTER `duration`;
ALTER TABLE `out_order_item_24`
   ADD COLUMN `duration` int NULL COMMENT '发布时长' AFTER `error_info`,ADD COLUMN `feedback_time` datetime(0) NULL DEFAULT CURRENT_TIMESTAMP(0) COMMENT '工单下游反馈完成时间' AFTER `duration`;
ALTER TABLE `out_order_item_25`
   ADD COLUMN `duration` int NULL COMMENT '发布时长' AFTER `error_info`,ADD COLUMN `feedback_time` datetime(0) NULL DEFAULT CURRENT_TIMESTAMP(0) COMMENT '工单下游反馈完成时间' AFTER `duration`;
ALTER TABLE `out_order_item_26`
   ADD COLUMN `duration` int NULL COMMENT '发布时长' AFTER `error_info`,ADD COLUMN `feedback_time` datetime(0) NULL DEFAULT CURRENT_TIMESTAMP(0) COMMENT '工单下游反馈完成时间' AFTER `duration`;
ALTER TABLE `out_order_item_27`
   ADD COLUMN `duration` int NULL COMMENT '发布时长' AFTER `error_info`,ADD COLUMN `feedback_time` datetime(0) NULL DEFAULT CURRENT_TIMESTAMP(0) COMMENT '工单下游反馈完成时间' AFTER `duration`;
ALTER TABLE `out_order_item_28`
   ADD COLUMN `duration` int NULL COMMENT '发布时长' AFTER `error_info`,ADD COLUMN `feedback_time` datetime(0) NULL DEFAULT CURRENT_TIMESTAMP(0) COMMENT '工单下游反馈完成时间' AFTER `duration`;
ALTER TABLE `out_order_item_29`
   ADD COLUMN `duration` int NULL COMMENT '发布时长' AFTER `error_info`,ADD COLUMN `feedback_time` datetime(0) NULL DEFAULT CURRENT_TIMESTAMP(0) COMMENT '工单下游反馈完成时间' AFTER `duration`;
ALTER TABLE `out_order_item_30`
   ADD COLUMN `duration` int NULL COMMENT '发布时长' AFTER `error_info`,ADD COLUMN `feedback_time` datetime(0) NULL DEFAULT CURRENT_TIMESTAMP(0) COMMENT '工单下游反馈完成时间' AFTER `duration`;



ALTER TABLE cms_program
    ADD is_prohibit TINYINT DEFAULT 0 COMMENT '是否为违禁片,0:不为违禁片1:违禁片';
ALTER TABLE cms_program
    ADD prohibit_status TINYINT DEFAULT 0 COMMENT '是否加入违禁片库，0:未加入，1:已加入';
ALTER TABLE cms_series
    ADD is_prohibit TINYINT DEFAULT 0 COMMENT '是否为违禁片,0:不为违禁片1:违禁片';
ALTER TABLE cms_series
    ADD prohibit_status TINYINT DEFAULT 0 COMMENT '是否加入违禁片库，0:未加入，1:已加入';
ALTER TABLE sys_cp
    ADD skip_prohibit TINYINT DEFAULT 0 COMMENT '是否跳过违禁片检测,0:不跳过(自动加入违禁片库)1:不跳过(手动加入违禁片库)2:跳过';
ALTER TABLE sys_cp
    ADD point_prohibit TINYINT DEFAULT 0 COMMENT '违禁检测出发点,0:入库成功1:自审通过';


DROP TABLE IF EXISTS `copyright_data`;
CREATE TABLE `copyright_data`
(
    `id`              bigint                                                       NOT NULL AUTO_INCREMENT,
    `code`            varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '版权信息编码',
    `pgm_category_id` bigint                                                        DEFAULT NULL COMMENT 'n1：电影\\r\\n2：子集\\r\\n3：电视剧',
    `pgm_category`    varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci  DEFAULT NULL COMMENT '媒资分类',
    `cp_id`           bigint                                                        DEFAULT NULL COMMENT 'cpid',
    `cp_name`         varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci  DEFAULT NULL COMMENT 'cp名称',
    `show_name`       varchar(256) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '版权名称',
    `creator_id`      bigint                                                        DEFAULT NULL COMMENT '创建人ID，来自于user表',
    `creator_name`    varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci  DEFAULT NULL COMMENT '创建人',
    `file_path`       varchar(256) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '版权文件ftp地址',
    `status`          tinyint                                                       DEFAULT '1' COMMENT '状态,0:失效1:生效，默认生效',
    `storage_name`    varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '所属存储',
    `storage_id`      bigint                                                        DEFAULT NULL COMMENT '存储ID',
    `create_time`     datetime                                                      DEFAULT CURRENT_TIMESTAMP COMMENT '上传时间',
    `update_time`     datetime                                                      DEFAULT CURRENT_TIMESTAMP COMMENT '更新时间',
    PRIMARY KEY (`id`) USING BTREE,
    UNIQUE KEY `unique_code` (`code`),
    KEY               `idx_name` (`show_name`) USING BTREE,
    KEY               `idx_code` (`code`) USING BTREE,
    KEY               `idx_content_type` (`pgm_category_id`) USING BTREE,
    KEY               `idx_create_time` (`create_time` DESC) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci ROW_FORMAT=DYNAMIC COMMENT='copyright_info';
SET
FOREIGN_KEY_CHECKS = 1;


DROP TABLE IF EXISTS `cms_prohibit`;
CREATE TABLE `cms_prohibit`
(
    `id`                  bigint NOT NULL AUTO_INCREMENT COMMENT '主键',
    `code`                varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci   DEFAULT NULL COMMENT '全局唯一标识',
    `rule_code`           varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci   DEFAULT NULL COMMENT '违禁片规则对应标识',
    `content_type`        tinyint                                                        DEFAULT NULL COMMENT '1：电影\\r\\n2：子集\\r\\n3：电视剧\\r\\n4：系列片\\r\\n5：片花\\r\\n6：直播\\r\\n7：物理频道\\r\\n8：栏目\\r\\n9：产品包\\r\\n10：节目单\\r\\n11：图片\\r\\n12：视频介质\\r\\n13：节目图片\\r\\n14：剧集图片\\r\\n15：产品包图片\\r\\n16：栏目图片\\r\\n17：频道图片\\r\\n18：栏目节目\\r\\n19：栏目剧集\\r\\n20：栏目频道\\r\\n21：产品包节目\\r\\n22：产品包剧集\\r\\n',
    `name`                varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci  DEFAULT NULL COMMENT '节目名称',
    `original_name`       varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci  DEFAULT '' COMMENT '原名',
    `actor_display`       varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci  DEFAULT '' COMMENT '演员列表',
    `original_country_id` bigint                                                         DEFAULT NULL COMMENT '产地id，来自于sys_dictionary_item表',
    `original_country`    varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci   DEFAULT '' COMMENT '产地名称，来自于sys_dictionary_item表',
    `release_year`        varchar(4) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci    DEFAULT '' COMMENT '上映年份',
    `pgm_category_id`     bigint                                                         DEFAULT NULL COMMENT '媒资分类ID，来自sys_dictionary_item表',
    `pgm_category`        varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci   DEFAULT '' COMMENT '节目形态，如：新闻，电影',
    `pgm_snd_class_id`    varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci   DEFAULT NULL COMMENT '媒资类型ID，来自sys_dictionary_item表',
    `pgm_snd_class`       varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci   DEFAULT '' COMMENT '二级标签，如：动作，科幻',
    `status`              tinyint                                                        DEFAULT '1' COMMENT '状态标志\r\n0:失效  1:生效',
    `source_type`         tinyint unsigned DEFAULT NULL COMMENT '节目类型1:  视频类节目\r\n2:  图文类节目\r',
    `kpeople`             varchar(150) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci  DEFAULT '' COMMENT '主要人物',
    `director`            varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci   DEFAULT '' COMMENT '导演',
    `cp_id`               bigint                                                         DEFAULT NULL,
    `cp_name`             varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci   DEFAULT NULL COMMENT 'CP名称',
    `create_time`         datetime                                                       DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_time`         datetime                                                       DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    `cp_check_status`     tinyint                                                        DEFAULT '1' COMMENT '审核状态  \r\n审核状态  \r\n1：待审核  \r\n2：审核中  \r\n3：通过  \r\n4：未通过',
    `cp_check_desc`       varchar(1024) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '自审描述',
    `cp_check_time`       datetime                                                       DEFAULT NULL COMMENT '审核时间',
    `cp_checker`          varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci   DEFAULT '' COMMENT '审核人',
    `op_check_status`     tinyint                                                        DEFAULT '1' COMMENT 'op审核状态  \r\n1：待审核  \r\n2：审核中  \r\n3：通过  \r\n4：未通过',
    `op_check_desc`       varchar(1024) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT 'op审核描述',
    `op_checker`          varchar(127) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci  DEFAULT NULL COMMENT '终审人员',
    `op_check_time`       datetime                                                       DEFAULT NULL COMMENT '终审时间',
    `source`              tinyint                                                        DEFAULT NULL COMMENT '来源  1：专线注入  2：人工创建',
    `creator_name`        varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci   DEFAULT NULL COMMENT '创建人，只人工上传时可用',
    `creator_id`          bigint                                                         DEFAULT NULL,
    `content_id`          bigint                                                         DEFAULT NULL COMMENT '媒资表主键',
    `series_type`         tinyint                                                        DEFAULT '0' COMMENT '0：连续剧，1：系列片默认连续剧',
    PRIMARY KEY (`id`) USING BTREE,
    KEY                   `index_name` (`name`) USING BTREE,
    KEY                   `index_pgm_category_id` (`pgm_category_id`) USING BTREE,
    KEY                   `index_cp_id` (`cp_id`) USING BTREE,
    KEY                   `index_cp_check_status` (`cp_check_status`) USING BTREE,
    KEY                   `index_op_check_status` (`op_check_status`) USING BTREE,
    KEY                   `index_code` (`code`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=432 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci ROW_FORMAT=DYNAMIC COMMENT='cms违禁片表';
SET
FOREIGN_KEY_CHECKS = 1;


DROP TABLE IF EXISTS `artist_prohibit`;
CREATE TABLE `artist_prohibit`
(
    `id`             bigint NOT NULL AUTO_INCREMENT COMMENT '主键',
    `code`           varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci  DEFAULT NULL COMMENT '全局唯一标识',
    `artist_name`    varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT '' COMMENT '艺人名称',
    `artist_sex`     tinyint                                                       DEFAULT NULL COMMENT '艺人性别,0:男性1:女性',
    `birthday`       varchar(14) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci  DEFAULT '' COMMENT '出生日期（YYYYMMDDHH24MiSS）',
    `description`    text COLLATE utf8mb4_unicode_ci COMMENT '简介',
    `prohibit_cause` text COLLATE utf8mb4_unicode_ci COMMENT '封杀原因',
    `creator_name`   varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci  DEFAULT NULL COMMENT '创建人',
    `create_time`    datetime                                                      DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_time`    datetime                                                      DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    PRIMARY KEY (`id`) USING BTREE,
    KEY              `index_name` (`artist_name`) USING BTREE,
    KEY              `index_code` (`code`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci ROW_FORMAT=DYNAMIC COMMENT='违禁艺人表';
SET
FOREIGN_KEY_CHECKS = 1;



DROP TABLE IF EXISTS `rule_prohibit`;
CREATE TABLE `rule_prohibit`
(
    `id`                  bigint NOT NULL AUTO_INCREMENT COMMENT '主键',
    `code`                varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci  DEFAULT NULL COMMENT '全局唯一\n	标识',
    `show_name`           varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT '' COMMENT '媒资名称',
    `pgm_category_id`     bigint                                                        DEFAULT NULL COMMENT '媒资分类ID，来自sys_dictionary_item表',
    `pgm_category`        varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci  DEFAULT NULL COMMENT '媒资分类',
    `content_type`        tinyint                                                       DEFAULT NULL COMMENT '1：电影\\r\\n2：子集\\r\\n3：电视\n	剧\\r\\n4：系列片\\r\\n5：片花\\r\\n6：直播\\r\\n7：物理频道\\r\\n8：栏目\\r\\n9：产\n	品包\\r\\n10：节目单\\r\\n11：图片\\r\\n12：视频介质\\r\\n13：节目图片\\r\\n14：剧集\n	图片\\r\\n15：产品包图片\\r\\n16：栏目图片\\r\\n17：频道图片\\r\\n18：栏目节目\\r\\n19：\n	栏目剧集\\r\\n20：栏目频道\\r\\n21：产品包节目\\r\\n22：产品包剧集\\r\\n',
    `kpeople`             varchar(150) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '主要人物',
    `director`            varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci  DEFAULT NULL COMMENT '导演',
    `original_country_id` bigint                                                        DEFAULT NULL COMMENT '产地id，来自于\n	sys_dictionary_item表',
    `original_country`    varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci  DEFAULT NULL COMMENT '产地名称，来自于sys_dictionary_item表',
    `release_year`        varchar(4) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci   DEFAULT NULL COMMENT '上映年份',
    `creator_name`        varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci  DEFAULT NULL COMMENT '创建人',
    `create_time`         datetime                                                      DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_time`         datetime                                                      DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    PRIMARY KEY (`id`) USING BTREE,
    KEY                   `index_name` (`show_name`) USING BTREE,
    KEY                   `index_type` (`content_type`) USING BTREE,
    KEY                   `index_pgm` (`pgm_category_id`) USING BTREE,
    KEY                   `index_code` (`code`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=1437178 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci ROW_FORMAT=DYNAMIC COMMENT='违禁规则表';

SET
FOREIGN_KEY_CHECKS = 1;


/*2022-08-23*/
ALTER TABLE `rule_prohibit`
    MODIFY COLUMN `kpeople` varchar (150) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '主要人物',
    MODIFY COLUMN `director` varchar (50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '导演',
    MODIFY COLUMN `release_year` varchar (4) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '上映年份',
    MODIFY COLUMN `original_country` varchar (64) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '产地名称，来自于sys_dictionary_item表';

/*2022-08-24*/
INSERT INTO `iptv`.`sys_dictionary_base`(`code`, `name`, `sequence`, `type`, `status`, `tenant_id`, `creator_id`,
                                         `creator_name`, `description`)
VALUES ('AUTHORIZATION_EXPIRATION_TIME', '授权到期时间', NULL, NULL, 1, NULL, NULL, NULL, NULL);
INSERT INTO `iptv`.`sys_dictionary_item`(`code`, `name`, `sequence`, `type`, `status`, `parent_id`,
                                         `dictionary_base_id`, `tenant_id`, `creator_id`, `creator_name`, `description`)
VALUES ('AuthorizationExpirationTime', '10', 100, NULL, 1, 0, 8, NULL, 1, '管理员', '单位: 天');

/*2022-09-01*/
ALTER TABLE cms_series ADD INDEX index_prohibit_licensing (prohibit_status, licensing_window_end);
ALTER TABLE cms_series ADD INDEX index_actor_display (actor_display);
ALTER TABLE cms_series ADD INDEX index_director (director);
ALTER TABLE cms_series ADD INDEX index_isprohibit (is_prohibit);

ALTER TABLE cms_program ADD INDEX index_seriesflag_prohibit_licensing (series_flag, prohibit_status, licensing_window_end);
ALTER TABLE cms_program ADD INDEX index_actor_display (actor_display);
ALTER TABLE cms_program ADD INDEX index_director (director);
ALTER TABLE cms_program ADD INDEX index_isprohibit (is_prohibit);

ALTER TABLE out_order_base ADD INDEX index_order_correlate_id (order_correlate_id);
ALTER TABLE out_order_base ADD INDEX index_correlate_id (correlate_id);

/*2022-09-01*/
INSERT INTO `sys_menu`(`id`, `meta`, `redirect`, `component`, `name`, `parent_id`, `group`, `permission`, `path`, `icon`, `sequence`, `type`, `status`, `tenant_id`, `tenant_name`, `creator_id`, `creator_name`, `create_time`, `update_time`, `description`) VALUES (10045, '{\"title\": \"版权管理\",\"isHide\":0,\"isKeepAlive\":1,\"isAffix\": 0,\"icon\":\"el-icon-folder\"}', '/copyright/information', 'layout/routerView/parent', 'copyright', 0, '0', 'global', '/copyright', NULL, 9, 1, 1, NULL, NULL, NULL, NULL, '2021-08-08 00:00:00', '2022-08-19 18:02:31', NULL);
INSERT INTO `sys_menu`(`id`, `meta`, `redirect`, `component`, `name`, `parent_id`, `group`, `permission`, `path`, `icon`, `sequence`, `type`, `status`, `tenant_id`, `tenant_name`, `creator_id`, `creator_name`, `create_time`, `update_time`, `description`) VALUES (10046, '{\"title\": \"版权材料库\",\"isHide\":0,\"isKeepAlive\":1,\"isAffix\": 0,\"icon\":\"\"}', NULL, 'copyright/material/index', 'material', 10045, '0', 'global', '/ccpyright/material', NULL, 2, 1, 1, NULL, NULL, NULL, NULL, '2021-08-08 00:00:00', '2022-08-19 18:02:38', NULL);
INSERT INTO `sys_menu`(`id`, `meta`, `redirect`, `component`, `name`, `parent_id`, `group`, `permission`, `path`, `icon`, `sequence`, `type`, `status`, `tenant_id`, `tenant_name`, `creator_id`, `creator_name`, `create_time`, `update_time`, `description`) VALUES (10047, '{\"title\": \"版权信息库\",\"isHide\":0,\"isKeepAlive\":1,\"isAffix\": 0,\"icon\":\"\"}', NULL, 'copyright/information/index', 'information', 10045, '0', 'global', '/copyright/information', NULL, 1, 1, 1, NULL, NULL, NULL, NULL, '2021-08-08 00:00:00', '2022-08-19 18:02:46', NULL);
INSERT INTO `sys_menu`(`id`, `meta`, `redirect`, `component`, `name`, `parent_id`, `group`, `permission`, `path`, `icon`, `sequence`, `type`, `status`, `tenant_id`, `tenant_name`, `creator_id`, `creator_name`, `create_time`, `update_time`, `description`) VALUES (10048, '{\"title\": \"违禁片库\",\"isHide\":0,\"isKeepAlive\":1,\"isAffix\": 0,\"icon\":\"\"}', NULL, 'copyright/contraband/index', 'contraband', 10045, '0', 'global', '/copyright/contraband', NULL, 4, 1, 1, NULL, NULL, NULL, NULL, '2021-08-08 00:00:00', '2022-08-19 18:02:50', NULL);
INSERT INTO `sys_menu`(`id`, `meta`, `redirect`, `component`, `name`, `parent_id`, `group`, `permission`, `path`, `icon`, `sequence`, `type`, `status`, `tenant_id`, `tenant_name`, `creator_id`, `creator_name`, `create_time`, `update_time`, `description`) VALUES (10049, '{\"title\": \"违禁艺人库\",\"isHide\":0,\"isKeepAlive\":1,\"isAffix\": 0,\"icon\":\"\"}', NULL, 'copyright/artist/index', 'artist', 10045, '0', 'global', '/copyright/artist', NULL, 5, 1, 1, NULL, NULL, NULL, NULL, '2021-08-08 00:00:00', '2022-08-19 18:02:56', NULL);
INSERT INTO `sys_menu`(`id`, `meta`, `redirect`, `component`, `name`, `parent_id`, `group`, `permission`, `path`, `icon`, `sequence`, `type`, `status`, `tenant_id`, `tenant_name`, `creator_id`, `creator_name`, `create_time`, `update_time`, `description`) VALUES (10050, '{\"title\": \"加入违禁片库\",\"icon\":\"el-icon-circle-plus-outline\"}', NULL, NULL, 'addLibrary', 10047, '0', 'global', NULL, NULL, 1, 4, 1, NULL, NULL, NULL, NULL, '2022-06-06 16:15:17', '2022-06-06 16:36:52', NULL);
INSERT INTO `sys_menu`(`id`, `meta`, `redirect`, `component`, `name`, `parent_id`, `group`, `permission`, `path`, `icon`, `sequence`, `type`, `status`, `tenant_id`, `tenant_name`, `creator_id`, `creator_name`, `create_time`, `update_time`, `description`) VALUES (10051, '{\"title\": \"导出\",\"icon\":\"el-icon-download\"}', NULL, NULL, 'exportInformation', 10047, '0', 'global', NULL, NULL, 2, 4, 1, NULL, NULL, NULL, NULL, '2022-06-06 16:48:43', '2022-06-06 16:51:35', NULL);
INSERT INTO `sys_menu`(`id`, `meta`, `redirect`, `component`, `name`, `parent_id`, `group`, `permission`, `path`, `icon`, `sequence`, `type`, `status`, `tenant_id`, `tenant_name`, `creator_id`, `creator_name`, `create_time`, `update_time`, `description`) VALUES (10052, '{\"title\": \"授权时间设置\",\"icon\":\"el-icon-view\"}', NULL, NULL, 'settingTime', 10047, '0', 'global', NULL, NULL, 3, 4, 1, NULL, NULL, NULL, NULL, '2022-06-06 16:52:06', '2022-06-06 16:53:53', NULL);
INSERT INTO `sys_menu`(`id`, `meta`, `redirect`, `component`, `name`, `parent_id`, `group`, `permission`, `path`, `icon`, `sequence`, `type`, `status`, `tenant_id`, `tenant_name`, `creator_id`, `creator_name`, `create_time`, `update_time`, `description`) VALUES (10053, '{\"title\": \"批量回收\",\"icon\":\"el-icon-refresh\"}', NULL, NULL, 'recycleBatch', 10047, '0', 'global', NULL, NULL, 4, 4, 1, NULL, NULL, NULL, NULL, '2022-06-06 16:54:47', '2022-06-06 16:55:25', NULL);
INSERT INTO `sys_menu`(`id`, `meta`, `redirect`, `component`, `name`, `parent_id`, `group`, `permission`, `path`, `icon`, `sequence`, `type`, `status`, `tenant_id`, `tenant_name`, `creator_id`, `creator_name`, `create_time`, `update_time`, `description`) VALUES (10054, '{\"title\": \"发布详情\"}', NULL, NULL, 'checkDetails', 10047, '0', 'global', NULL, NULL, 5, 5, 1, NULL, NULL, NULL, NULL, '2022-06-06 16:59:03', '2022-08-15 09:19:55', NULL);
INSERT INTO `sys_menu`(`id`, `meta`, `redirect`, `component`, `name`, `parent_id`, `group`, `permission`, `path`, `icon`, `sequence`, `type`, `status`, `tenant_id`, `tenant_name`, `creator_id`, `creator_name`, `create_time`, `update_time`, `description`) VALUES (10055, '{\"title\": \"一键回收\",\"icon\":\"el-icon-refresh\"}', NULL, NULL, 'recycleBatch', 10054, '0', 'global', NULL, NULL, 6, 4, 1, NULL, NULL, NULL, NULL, '2022-06-07 09:31:29', '2022-08-18 18:13:30', NULL);
INSERT INTO `sys_menu`(`id`, `meta`, `redirect`, `component`, `name`, `parent_id`, `group`, `permission`, `path`, `icon`, `sequence`, `type`, `status`, `tenant_id`, `tenant_name`, `creator_id`, `creator_name`, `create_time`, `update_time`, `description`) VALUES (10056, '{\"title\": \"新增\",\"icon\":\"el-icon-circle-plus-outline\"}', NULL, NULL, 'create', 10046, '0', 'global', NULL, NULL, 1, 4, 1, NULL, NULL, NULL, NULL, '2022-06-07 09:38:49', '2022-06-07 09:40:00', NULL);
INSERT INTO `sys_menu`(`id`, `meta`, `redirect`, `component`, `name`, `parent_id`, `group`, `permission`, `path`, `icon`, `sequence`, `type`, `status`, `tenant_id`, `tenant_name`, `creator_id`, `creator_name`, `create_time`, `update_time`, `description`) VALUES (10057, '{\"title\": \"删除\",\"icon\":\"el-icon-delete\"}', NULL, NULL, 'delete', 10046, '0', 'global', NULL, NULL, 2, 4, 1, NULL, NULL, NULL, NULL, '2022-06-07 09:40:13', '2022-06-07 09:41:01', NULL);
INSERT INTO `sys_menu`(`id`, `meta`, `redirect`, `component`, `name`, `parent_id`, `group`, `permission`, `path`, `icon`, `sequence`, `type`, `status`, `tenant_id`, `tenant_name`, `creator_id`, `creator_name`, `create_time`, `update_time`, `description`) VALUES (10058, '{\"title\": \"查看\"}', NULL, NULL, 'check', 10046, '0', 'global', NULL, NULL, 3, 5, 1, NULL, NULL, NULL, NULL, '2022-06-07 09:41:46', '2022-06-07 09:42:40', NULL);
INSERT INTO `sys_menu`(`id`, `meta`, `redirect`, `component`, `name`, `parent_id`, `group`, `permission`, `path`, `icon`, `sequence`, `type`, `status`, `tenant_id`, `tenant_name`, `creator_id`, `creator_name`, `create_time`, `update_time`, `description`) VALUES (10059, '{\"title\": \"下载\"}', NULL, NULL, 'download', 10046, '0', 'global', NULL, NULL, 4, 5, 1, NULL, NULL, NULL, NULL, '2022-06-07 09:42:50', '2022-06-07 09:43:41', NULL);
INSERT INTO `sys_menu`(`id`, `meta`, `redirect`, `component`, `name`, `parent_id`, `group`, `permission`, `path`, `icon`, `sequence`, `type`, `status`, `tenant_id`, `tenant_name`, `creator_id`, `creator_name`, `create_time`, `update_time`, `description`) VALUES (10060, '{\"title\": \"新增\",\"icon\":\"el-icon-circle-plus-outline\"}', NULL, NULL, 'create', 10048, '0', 'global', NULL, NULL, 1, 4, 2, NULL, NULL, NULL, NULL, '2022-06-07 10:18:36', '2022-08-11 11:13:14', NULL);
INSERT INTO `sys_menu`(`id`, `meta`, `redirect`, `component`, `name`, `parent_id`, `group`, `permission`, `path`, `icon`, `sequence`, `type`, `status`, `tenant_id`, `tenant_name`, `creator_id`, `creator_name`, `create_time`, `update_time`, `description`) VALUES (10061, '{\"title\": \"移除\",\"icon\":\"el-icon-delete\"}', NULL, NULL, 'delete', 10048, '0', 'global', NULL, NULL, 2, 4, 1, NULL, NULL, NULL, NULL, '2022-06-07 10:19:29', '2022-08-11 11:10:16', NULL);
INSERT INTO `sys_menu`(`id`, `meta`, `redirect`, `component`, `name`, `parent_id`, `group`, `permission`, `path`, `icon`, `sequence`, `type`, `status`, `tenant_id`, `tenant_name`, `creator_id`, `creator_name`, `create_time`, `update_time`, `description`) VALUES (10062, '{\"title\": \"导出\",\"icon\":\"el-icon-download\"}', NULL, NULL, 'exportAll', 10048, '0', 'global', NULL, NULL, 3, 4, 1, NULL, NULL, NULL, NULL, '2022-06-07 10:20:13', '2022-06-07 10:22:04', NULL);
INSERT INTO `sys_menu`(`id`, `meta`, `redirect`, `component`, `name`, `parent_id`, `group`, `permission`, `path`, `icon`, `sequence`, `type`, `status`, `tenant_id`, `tenant_name`, `creator_id`, `creator_name`, `create_time`, `update_time`, `description`) VALUES (10063, '{\"title\": \"导入违禁片单\",\"icon\":\"el-icon-upload2\"}', NULL, NULL, 'import', 10048, '0', 'global', NULL, NULL, 4, 4, 2, NULL, NULL, NULL, NULL, '2022-06-07 10:31:33', '2022-08-11 11:13:24', NULL);
INSERT INTO `sys_menu`(`id`, `meta`, `redirect`, `component`, `name`, `parent_id`, `group`, `permission`, `path`, `icon`, `sequence`, `type`, `status`, `tenant_id`, `tenant_name`, `creator_id`, `creator_name`, `create_time`, `update_time`, `description`) VALUES (10065, '{\"title\": \"编辑\"}', NULL, NULL, 'edit', 10048, '0', 'global', NULL, NULL, 6, 5, 2, NULL, NULL, NULL, NULL, '2022-06-07 10:34:07', '2022-08-11 11:13:32', NULL);
INSERT INTO `sys_menu`(`id`, `meta`, `redirect`, `component`, `name`, `parent_id`, `group`, `permission`, `path`, `icon`, `sequence`, `type`, `status`, `tenant_id`, `tenant_name`, `creator_id`, `creator_name`, `create_time`, `update_time`, `description`) VALUES (10066, '{\"title\": \"子集管理\"}', NULL, NULL, 'contrabandSonManage', 10048, '0', 'global', NULL, NULL, 7, 5, 1, NULL, NULL, NULL, NULL, '2022-06-07 10:34:48', '2022-08-11 17:00:17', NULL);
INSERT INTO `sys_menu`(`id`, `meta`, `redirect`, `component`, `name`, `parent_id`, `group`, `permission`, `path`, `icon`, `sequence`, `type`, `status`, `tenant_id`, `tenant_name`, `creator_id`, `creator_name`, `create_time`, `update_time`, `description`) VALUES (10067, '{\"title\": \"新增\",\"icon\":\"el-icon-circle-plus-outline\"}', NULL, NULL, 'create', 10049, '0', 'global', NULL, NULL, 1, 4, 1, NULL, NULL, NULL, NULL, '2022-06-07 10:43:02', '2022-06-07 10:43:47', NULL);
INSERT INTO `sys_menu`(`id`, `meta`, `redirect`, `component`, `name`, `parent_id`, `group`, `permission`, `path`, `icon`, `sequence`, `type`, `status`, `tenant_id`, `tenant_name`, `creator_id`, `creator_name`, `create_time`, `update_time`, `description`) VALUES (10068, '{\"title\": \"删除\",\"icon\":\"el-icon-delete\"}', NULL, NULL, 'delete', 10049, '0', 'global', NULL, NULL, 2, 4, 1, NULL, NULL, NULL, NULL, '2022-06-07 10:43:52', '2022-06-07 10:44:31', NULL);
INSERT INTO `sys_menu`(`id`, `meta`, `redirect`, `component`, `name`, `parent_id`, `group`, `permission`, `path`, `icon`, `sequence`, `type`, `status`, `tenant_id`, `tenant_name`, `creator_id`, `creator_name`, `create_time`, `update_time`, `description`) VALUES (10069, '{\"title\": \"导出\",\"icon\":\"el-icon-download\"}', NULL, NULL, 'exportAll', 10049, '0', 'global', NULL, NULL, 3, 4, 1, NULL, NULL, NULL, NULL, '2022-06-07 10:44:51', '2022-06-07 10:45:19', NULL);
INSERT INTO `sys_menu`(`id`, `meta`, `redirect`, `component`, `name`, `parent_id`, `group`, `permission`, `path`, `icon`, `sequence`, `type`, `status`, `tenant_id`, `tenant_name`, `creator_id`, `creator_name`, `create_time`, `update_time`, `description`) VALUES (10070, '{\"title\": \"导入违禁艺人名单\",\"icon\":\"el-icon-upload2\"}', NULL, NULL, 'importAll', 10049, '0', 'global', NULL, NULL, 4, 4, 1, NULL, NULL, NULL, NULL, '2022-06-07 10:45:50', '2022-06-07 10:46:37', NULL);
INSERT INTO `sys_menu`(`id`, `meta`, `redirect`, `component`, `name`, `parent_id`, `group`, `permission`, `path`, `icon`, `sequence`, `type`, `status`, `tenant_id`, `tenant_name`, `creator_id`, `creator_name`, `create_time`, `update_time`, `description`) VALUES (10071, '{\"title\": \"编辑\"}', NULL, NULL, 'edit', 10049, '0', 'global', NULL, NULL, 5, 5, 1, NULL, NULL, NULL, NULL, '2022-06-07 10:46:58', '2022-06-07 10:47:31', NULL);
INSERT INTO `sys_menu`(`id`, `meta`, `redirect`, `component`, `name`, `parent_id`, `group`, `permission`, `path`, `icon`, `sequence`, `type`, `status`, `tenant_id`, `tenant_name`, `creator_id`, `creator_name`, `create_time`, `update_time`, `description`) VALUES (10072, '{\"title\": \"查看关联媒资\"}', NULL, NULL, 'relation', 10049, '0', 'global', NULL, NULL, 6, 5, 1, NULL, NULL, NULL, NULL, '2022-06-07 10:47:41', '2022-06-07 10:49:08', NULL);
INSERT INTO `sys_menu`(`id`, `meta`, `redirect`, `component`, `name`, `parent_id`, `group`, `permission`, `path`, `icon`, `sequence`, `type`, `status`, `tenant_id`, `tenant_name`, `creator_id`, `creator_name`, `create_time`, `update_time`, `description`) VALUES (10073, '{\"title\": \"报表统计\",\"isHide\":0,\"isKeepAlive\":1,\"isAffix\": 0,\"icon\":\"el-icon-s-data\"}', '/report/injectionStatistics', 'layout/routerView/parent', 'report', 0, '0', 'global', '/report', NULL, 10, 1, 1, NULL, NULL, NULL, NULL, '2022-07-13 14:58:39', '2022-08-19 18:01:07', NULL);
INSERT INTO `sys_menu`(`id`, `meta`, `redirect`, `component`, `name`, `parent_id`, `group`, `permission`, `path`, `icon`, `sequence`, `type`, `status`, `tenant_id`, `tenant_name`, `creator_id`, `creator_name`, `create_time`, `update_time`, `description`) VALUES (10074, '{\"title\": \"注入统计\",\"isHide\":0,\"isKeepAlive\":1,\"isAffix\": 0,\"icon\":\"\"}', NULL, 'report/injectionStatistics/index', 'injectionStatistics', 10073, '0', 'global', '/report/injectionStatistics', NULL, 1, 1, 1, NULL, NULL, NULL, NULL, '2022-07-13 15:04:02', '2022-08-19 18:01:12', NULL);
INSERT INTO `sys_menu`(`id`, `meta`, `redirect`, `component`, `name`, `parent_id`, `group`, `permission`, `path`, `icon`, `sequence`, `type`, `status`, `tenant_id`, `tenant_name`, `creator_id`, `creator_name`, `create_time`, `update_time`, `description`) VALUES (10075, '{\"title\": \"下发统计\",\"isHide\":0,\"isKeepAlive\":1,\"isAffix\": 0,\"icon\":\"\"}', NULL, 'report/lssueStatistics/index', 'lssueStatistics', 10073, '0', 'global', '/report/lssueStatistics', NULL, 2, 1, 1, NULL, NULL, NULL, NULL, '2022-07-13 15:26:50', '2022-08-19 18:01:16', NULL);
INSERT INTO `sys_menu`(`id`, `meta`, `redirect`, `component`, `name`, `parent_id`, `group`, `permission`, `path`, `icon`, `sequence`, `type`, `status`, `tenant_id`, `tenant_name`, `creator_id`, `creator_name`, `create_time`, `update_time`, `description`) VALUES (10076, '{\"title\": \"下发排行\",\"isHide\":0,\"isKeepAlive\":1,\"isAffix\": 0,\"icon\":\"\"}', NULL, 'report/lssusRanking/index', 'lssusRanking', 10073, '0', 'global', '/report/lssusRanking', NULL, 4, 1, 1, NULL, NULL, NULL, NULL, '2022-07-13 15:27:49', '2022-08-19 18:01:20', NULL);
INSERT INTO `sys_menu`(`id`, `meta`, `redirect`, `component`, `name`, `parent_id`, `group`, `permission`, `path`, `icon`, `sequence`, `type`, `status`, `tenant_id`, `tenant_name`, `creator_id`, `creator_name`, `create_time`, `update_time`, `description`) VALUES (10077, '{\"title\": \"注入排行\",\"isHide\":0,\"isKeepAlive\":1,\"isAffix\": 0,\"icon\":\"\"}', NULL, 'report/injectionRanking/index', 'injectionRanking', 10073, '0', 'global', '/report/injectionRanking', NULL, 3, 1, 1, NULL, NULL, NULL, NULL, '2022-07-13 15:36:56', '2022-08-19 18:01:26', NULL);
INSERT INTO `sys_menu`(`id`, `meta`, `redirect`, `component`, `name`, `parent_id`, `group`, `permission`, `path`, `icon`, `sequence`, `type`, `status`, `tenant_id`, `tenant_name`, `creator_id`, `creator_name`, `create_time`, `update_time`, `description`) VALUES (10078, '{\"title\": \"发布统计\",\"isHide\":0,\"isKeepAlive\":1,\"isAffix\": 0,\"icon\":\"\"}', NULL, 'report/releaseStatistics/index', 'releaseStatistics', 10073, '0', 'global', '/report/releaseStatistics', NULL, 5, 1, 1, NULL, NULL, NULL, NULL, '2022-07-13 15:41:36', '2022-08-19 18:01:30', NULL);
INSERT INTO `sys_menu`(`id`, `meta`, `redirect`, `component`, `name`, `parent_id`, `group`, `permission`, `path`, `icon`, `sequence`, `type`, `status`, `tenant_id`, `tenant_name`, `creator_id`, `creator_name`, `create_time`, `update_time`, `description`) VALUES (10079, '{\"title\": \"回收统计\",\"isHide\":0,\"isKeepAlive\":1,\"isAffix\": 0,\"icon\":\"\"}', NULL, 'report/recoveryStatistics/index', 'recoveryStatistics', 10073, '0', 'global', '/report/recoveryStatistics', NULL, 6, 1, 1, NULL, NULL, NULL, NULL, '2022-07-13 15:43:12', '2022-08-19 18:01:33', NULL);
INSERT INTO `sys_menu`(`id`, `meta`, `redirect`, `component`, `name`, `parent_id`, `group`, `permission`, `path`, `icon`, `sequence`, `type`, `status`, `tenant_id`, `tenant_name`, `creator_id`, `creator_name`, `create_time`, `update_time`, `description`) VALUES (10080, '{\"title\": \"终审统计\",\"isHide\":0,\"isKeepAlive\":1,\"isAffix\": 0,\"icon\":\"\"}', NULL, 'report/verifyStatistics/index', 'verifyStatistics', 10073, '0', 'global', '/report/verifyStatistics', NULL, 7, 1, 1, NULL, NULL, NULL, NULL, '2022-07-13 15:44:23', '2022-08-19 18:01:37', NULL);
INSERT INTO `sys_menu`(`id`, `meta`, `redirect`, `component`, `name`, `parent_id`, `group`, `permission`, `path`, `icon`, `sequence`, `type`, `status`, `tenant_id`, `tenant_name`, `creator_id`, `creator_name`, `create_time`, `update_time`, `description`) VALUES (10081, '{\"title\": \"工单统计\",\"isHide\":0,\"isKeepAlive\":1,\"isAffix\": 0,\"icon\":\"\"}', NULL, 'report/workStatistics/index', 'workStatistics', 10073, '0', 'global', '/report/workStatistics', NULL, 8, 1, 1, NULL, NULL, NULL, NULL, '2022-07-13 15:45:38', '2022-08-19 18:01:42', NULL);
INSERT INTO `sys_menu`(`id`, `meta`, `redirect`, `component`, `name`, `parent_id`, `group`, `permission`, `path`, `icon`, `sequence`, `type`, `status`, `tenant_id`, `tenant_name`, `creator_id`, `creator_name`, `create_time`, `update_time`, `description`) VALUES (10082, '{\"title\": \"存储统计\",\"isHide\":0,\"isKeepAlive\":0,\"isAffix\": 0,\"icon\":\"\"}', NULL, 'report/storageStatistics/index', 'storageStatistics', 10073, '0', 'global', '/report/storageStatistics', NULL, 9, 1, 1, NULL, NULL, NULL, NULL, '2022-07-13 15:47:12', '2022-07-13 15:48:16', NULL);
INSERT INTO `sys_menu`(`id`, `meta`, `redirect`, `component`, `name`, `parent_id`, `group`, `permission`, `path`, `icon`, `sequence`, `type`, `status`, `tenant_id`, `tenant_name`, `creator_id`, `creator_name`, `create_time`, `update_time`, `description`) VALUES (10096, '{\"title\": \"违禁片规则\",\"isHide\":0,\"isKeepAlive\":1,\"isAffix\": 0,\"icon\":\"\"}', NULL, 'copyright/rulelib/index', 'rulelib', 10045, '0', 'global', '/copyright/rulelib', NULL, 3, 1, 1, NULL, NULL, NULL, NULL, '2022-07-19 17:28:20', '2022-08-22 09:01:14', NULL);
INSERT INTO `sys_menu`(`id`, `meta`, `redirect`, `component`, `name`, `parent_id`, `group`, `permission`, `path`, `icon`, `sequence`, `type`, `status`, `tenant_id`, `tenant_name`, `creator_id`, `creator_name`, `create_time`, `update_time`, `description`) VALUES (10097, '{\"title\": \"新增\",\"icon\":\"el-icon-circle-plus-outline\"}', NULL, '', 'created', 10096, '0', 'global', NULL, NULL, 1, 4, 1, NULL, NULL, NULL, NULL, '2022-07-19 17:33:20', '2022-07-19 17:34:30', NULL);
INSERT INTO `sys_menu`(`id`, `meta`, `redirect`, `component`, `name`, `parent_id`, `group`, `permission`, `path`, `icon`, `sequence`, `type`, `status`, `tenant_id`, `tenant_name`, `creator_id`, `creator_name`, `create_time`, `update_time`, `description`) VALUES (10098, '{\"title\": \"删除\",\"icon\":\"el-icon-delete\"}', NULL, NULL, 'delete', 10096, '0', 'global', NULL, NULL, 2, 4, 1, NULL, NULL, NULL, NULL, '2022-07-19 17:34:41', '2022-08-11 11:08:53', NULL);
INSERT INTO `sys_menu`(`id`, `meta`, `redirect`, `component`, `name`, `parent_id`, `group`, `permission`, `path`, `icon`, `sequence`, `type`, `status`, `tenant_id`, `tenant_name`, `creator_id`, `creator_name`, `create_time`, `update_time`, `description`) VALUES (10099, '{\"title\": \"导出\",\"icon\":\"el-icon-download\"}', NULL, NULL, 'export', 10096, '0', 'global', NULL, NULL, 3, 4, 1, NULL, NULL, NULL, NULL, '2022-07-19 17:35:47', '2022-07-19 17:36:32', NULL);
INSERT INTO `sys_menu`(`id`, `meta`, `redirect`, `component`, `name`, `parent_id`, `group`, `permission`, `path`, `icon`, `sequence`, `type`, `status`, `tenant_id`, `tenant_name`, `creator_id`, `creator_name`, `create_time`, `update_time`, `description`) VALUES (10100, '{\"title\": \"导入违禁片单\",\"icon\":\"el-icon-upload2\"}', NULL, NULL, 'import', 10096, '0', 'global', NULL, NULL, 4, 4, 1, NULL, NULL, NULL, NULL, '2022-07-19 17:36:41', '2022-07-19 17:37:59', NULL);
INSERT INTO `sys_menu`(`id`, `meta`, `redirect`, `component`, `name`, `parent_id`, `group`, `permission`, `path`, `icon`, `sequence`, `type`, `status`, `tenant_id`, `tenant_name`, `creator_id`, `creator_name`, `create_time`, `update_time`, `description`) VALUES (10101, '{\"title\": \"编辑\"}', NULL, NULL, 'edit', 10096, '0', 'global', NULL, NULL, 1, 5, 1, NULL, NULL, NULL, NULL, '2022-07-19 17:38:09', '2022-07-19 17:38:44', NULL);
INSERT INTO `sys_menu`(`id`, `meta`, `redirect`, `component`, `name`, `parent_id`, `group`, `permission`, `path`, `icon`, `sequence`, `type`, `status`, `tenant_id`, `tenant_name`, `creator_id`, `creator_name`, `create_time`, `update_time`, `description`) VALUES (10102, '{\"title\": \"查看关联媒资\"}', NULL, NULL, 'check', 10096, '0', 'global', NULL, NULL, 2, 5, 1, NULL, NULL, NULL, NULL, '2022-07-19 17:38:51', '2022-07-19 17:39:36', NULL);
INSERT INTO `sys_menu`(`id`, `meta`, `redirect`, `component`, `name`, `parent_id`, `group`, `permission`, `path`, `icon`, `sequence`, `type`, `status`, `tenant_id`, `tenant_name`, `creator_id`, `creator_name`, `create_time`, `update_time`, `description`) VALUES (10103, '{\"title\": \"导出\",\"icon\":\"el-icon-download\"}', NULL, NULL, 'export', 10082, '0', 'global', NULL, NULL, 1, 4, 1, NULL, NULL, NULL, NULL, '2022-07-20 09:09:10', '2022-07-20 09:09:52', NULL);
INSERT INTO `sys_menu`(`id`, `meta`, `redirect`, `component`, `name`, `parent_id`, `group`, `permission`, `path`, `icon`, `sequence`, `type`, `status`, `tenant_id`, `tenant_name`, `creator_id`, `creator_name`, `create_time`, `update_time`, `description`) VALUES (10104, '{\"title\": \"导出\",\"icon\":\"el-icon-download\"}', NULL, NULL, 'export', 10076, '0', 'global', NULL, NULL, 1, 4, 1, NULL, NULL, NULL, NULL, '2022-07-25 15:02:01', '2022-07-25 15:02:46', NULL);
INSERT INTO `sys_menu`(`id`, `meta`, `redirect`, `component`, `name`, `parent_id`, `group`, `permission`, `path`, `icon`, `sequence`, `type`, `status`, `tenant_id`, `tenant_name`, `creator_id`, `creator_name`, `create_time`, `update_time`, `description`) VALUES (10105, '{\"title\": \"导出\",\"icon\":\"el-icon-download\"}', NULL, NULL, 'export', 10077, '0', 'global', NULL, NULL, 1, 4, 1, NULL, NULL, NULL, NULL, '2022-07-25 15:03:52', '2022-07-25 15:04:20', NULL);
INSERT INTO `sys_menu`(`id`, `meta`, `redirect`, `component`, `name`, `parent_id`, `group`, `permission`, `path`, `icon`, `sequence`, `type`, `status`, `tenant_id`, `tenant_name`, `creator_id`, `creator_name`, `create_time`, `update_time`, `description`) VALUES (10106, '{\"title\": \"导出\",\"icon\":\"el-icon-download\"}', NULL, NULL, 'export', 10081, '0', 'global', NULL, NULL, 1, 4, 1, NULL, NULL, NULL, NULL, '2022-08-09 09:10:05', '2022-08-09 09:10:57', NULL);
INSERT INTO `sys_menu`(`id`, `meta`, `redirect`, `component`, `name`, `parent_id`, `group`, `permission`, `path`, `icon`, `sequence`, `type`, `status`, `tenant_id`, `tenant_name`, `creator_id`, `creator_name`, `create_time`, `update_time`, `description`) VALUES (10107, '{\"title\": \"导出\",\"icon\":\"el-icon-download\"}', NULL, NULL, 'export', 10074, '0', 'global', NULL, NULL, 1, 4, 1, NULL, NULL, NULL, NULL, '2022-08-10 09:50:09', '2022-08-10 09:51:09', NULL);
INSERT INTO `sys_menu`(`id`, `meta`, `redirect`, `component`, `name`, `parent_id`, `group`, `permission`, `path`, `icon`, `sequence`, `type`, `status`, `tenant_id`, `tenant_name`, `creator_id`, `creator_name`, `create_time`, `update_time`, `description`) VALUES (10108, '{\"title\": \"导出\",\"icon\":\"el-icon-download\"}', NULL, NULL, 'export', 10075, '0', 'global', NULL, NULL, 1, 4, 1, NULL, NULL, NULL, NULL, '2022-08-10 09:51:19', '2022-08-10 09:52:03', NULL);
INSERT INTO `sys_menu`(`id`, `meta`, `redirect`, `component`, `name`, `parent_id`, `group`, `permission`, `path`, `icon`, `sequence`, `type`, `status`, `tenant_id`, `tenant_name`, `creator_id`, `creator_name`, `create_time`, `update_time`, `description`) VALUES (10109, '{\"title\": \"导出\",\"icon\":\"el-icon-download\"}', NULL, NULL, 'export', 10078, '0', 'global', NULL, NULL, 1, 4, 1, NULL, NULL, NULL, NULL, '2022-08-10 09:52:32', '2022-08-10 09:53:00', NULL);
INSERT INTO `sys_menu`(`id`, `meta`, `redirect`, `component`, `name`, `parent_id`, `group`, `permission`, `path`, `icon`, `sequence`, `type`, `status`, `tenant_id`, `tenant_name`, `creator_id`, `creator_name`, `create_time`, `update_time`, `description`) VALUES (10110, '{\"title\": \"导出\",\"icon\":\"el-icon-download\"}', NULL, NULL, 'export', 10079, '0', 'global', NULL, NULL, 1, 4, 1, NULL, NULL, NULL, NULL, '2022-08-10 09:53:06', '2022-08-10 09:53:37', NULL);
INSERT INTO `sys_menu`(`id`, `meta`, `redirect`, `component`, `name`, `parent_id`, `group`, `permission`, `path`, `icon`, `sequence`, `type`, `status`, `tenant_id`, `tenant_name`, `creator_id`, `creator_name`, `create_time`, `update_time`, `description`) VALUES (10111, '{\"title\": \"导出\",\"icon\":\"el-icon-download\"}', NULL, NULL, 'export', 10080, '0', 'global', NULL, NULL, 1, 4, 1, NULL, NULL, NULL, NULL, '2022-08-10 09:53:43', '2022-08-10 09:54:12', NULL);
INSERT INTO `sys_menu`(`id`, `meta`, `redirect`, `component`, `name`, `parent_id`, `group`, `permission`, `path`, `icon`, `sequence`, `type`, `status`, `tenant_id`, `tenant_name`, `creator_id`, `creator_name`, `create_time`, `update_time`, `description`) VALUES (10129, '{\"title\": \"查看\"}', NULL, NULL, 'check', 10066, '0', 'global', NULL, NULL, 5, 5, 1, NULL, NULL, NULL, NULL, '2022-06-07 10:33:16', '2022-06-07 10:34:01', NULL);
INSERT INTO `sys_menu`(`id`, `meta`, `redirect`, `component`, `name`, `parent_id`, `group`, `permission`, `path`, `icon`, `sequence`, `type`, `status`, `tenant_id`, `tenant_name`, `creator_id`, `creator_name`, `create_time`, `update_time`, `description`) VALUES (10130, '{\"title\": \"加入违禁片库\",\"icon\":\"el-icon-circle-plus-outline\"}', NULL, NULL, 'createProhibit', 705, '0', 'global', NULL, NULL, 1, 4, 1, NULL, NULL, NULL, NULL, '2021-08-08 00:00:00', '2022-08-15 10:28:14', NULL);
INSERT INTO `sys_menu`(`id`, `meta`, `redirect`, `component`, `name`, `parent_id`, `group`, `permission`, `path`, `icon`, `sequence`, `type`, `status`, `tenant_id`, `tenant_name`, `creator_id`, `creator_name`, `create_time`, `update_time`, `description`) VALUES (10131, '{\"title\": \"加入违禁片库\",\"icon\":\"el-icon-circle-plus-outline\"}', NULL, NULL, 'createProhibit', 707, '0', 'global', NULL, NULL, 1, 4, 1, NULL, NULL, NULL, NULL, '2021-08-08 00:00:00', '2022-08-15 10:28:22', NULL);


/*电信单集子集剧集添加预留字段以及节目试看字段*/
alter table cms_program
    add extendInfoList longtext null null comment '预留字段';
alter table cms_program
    add preDuration long null comment '节目试看(秒)';
alter table cms_series
    add extendInfoList longtext null null comment '预留字段';
alter table cms_series
    add preDuration long null comment '节目试看(秒)';
alter table bms_program
    add extendInfoList longtext null null comment '预留字段';
alter table bms_program
    add preDuration long null comment '节目试看(秒)';
alter table bms_content
    add extendInfoList longtext null null comment '预留字段';
alter table bms_content
    add preDuration long null comment '节目试看(秒)';

alter table iptv.sys_in_passage add column realtime_sp_ids varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '自动发布直播sp集合';
alter table iptv.sys_in_passage add column realtime_sp_names varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '自动发布直播sp名称';
alter table iptv.sys_in_passage add column delaytime_sp_ids varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '自动发布点播sp集合';
alter table iptv.sys_in_passage add column delaytime_sp_names varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '自动发布点播sp名称';
alter table iptv.sys_in_passage add column business_type int COMMENT '运营类型 1：媒资内容 2：直播频道';

alter table iptv.sys_in_passage DROP COLUMN out_sp_ids;
alter table iptv.sys_in_passage DROP COLUMN out_sp_names;

update iptv.sys_in_passage i set i.business_type = (select c.business_type from iptv.sys_cp c where c.id = i.cp_id);

INSERT INTO iptv.sys_menu
( meta, redirect, component, name, parent_id, `group`, permission, `path`, icon, `sequence`, `type`, status, tenant_id, tenant_name, creator_id, creator_name, create_time, update_time, description)
VALUES( '{"title": "优先发布","icon":"el-icon-position"}', '', '', 'priorityPublish', 22, '1-1', 'global', '', '', 41, 4, 1, 0, '', 0, '', '2008-08-01 00:00:00', '2023-02-24 09:27:09', '');

INSERT INTO iptv.sys_menu
( meta, redirect, component, name, parent_id, `group`, permission, `path`, icon, `sequence`, `type`, status, tenant_id, tenant_name, creator_id, creator_name, create_time, update_time, description)
VALUES( '{"title": "历史数据查询","isHide":0,"isKeepAlive":1,"isAffix": 0,"icon":""}', '', 'content/historical/index', 'historicalManage', 10, '0', 'global', '/content/historical', '', 4, 1, 1, NULL, '', NULL, '', '2021-08-08 00:00:00', '2022-11-29 09:28:07', '');

INSERT INTO iptv.sys_menu
( meta, redirect, component, name, parent_id, `group`, permission, `path`, icon, `sequence`, `type`, status, tenant_id, tenant_name, creator_id, creator_name, create_time, update_time, description)
VALUES( '{"title": "优先发布","icon":"el-icon-position"}', '', '', 'priorityPublish', 22, '0-1', 'global', '', '', 41, 4, 1, 0, '', 0, '', '2008-08-01 00:00:00', '2023-02-24 09:27:09', '');

INSERT INTO iptv.sys_menu
( meta, redirect, component, name, parent_id, `group`, permission, `path`, icon, `sequence`, `type`, status, tenant_id, tenant_name, creator_id, creator_name, create_time, update_time, description)
VALUES( '{"title": "查看详情"}', '', '', 'detail', 10141, '0', 'global', '', '', 1, 5, 1, NULL, '', NULL, '', '2021-08-08 00:00:00', '2021-12-02 00:00:00', '');

UPDATE
    iptv.sys_menu
SET
    meta = '{"title": "历史数据查询","isHide":0,"isKeepAlive":1,"isAffix": 0,"icon":""}',
    redirect = '',
    component = 'content/historical/index',
    name = 'historicalManage',
    parent_id = 10,
    `group` = '0',
    permission = 'global',
    `path` = '/content/historical',
    icon = '',
    `sequence` = 5,
    `type` = 1,
    status = 1,
    tenant_id = NULL,
    tenant_name = '',
    creator_id = NULL,
    creator_name = '',
    create_time = '2021-08-08 00:00:00',
    update_time = '2022-11-29 09:28:07',
    description = ''
WHERE
        id = 10141;

UPDATE
    iptv.sys_menu
SET
    meta = '{"title": "回看节目单","isHide":0,"isKeepAlive":1,"isAffix": 0,"icon":""}',
    redirect = '',
    component = 'content/schedule/index',
    name = 'scheduleManage',
    parent_id = 10,
    `group` = '0',
    permission = 'global',
    `path` = '/content/schedule',
    icon = '',
    `sequence` = 4,
    `type` = 1,
    status = 1,
    tenant_id = NULL,
    tenant_name = '',
    creator_id = NULL,
    creator_name = '',
    create_time = '2021-08-08 00:00:00',
    update_time = '2022-11-29 09:28:07',
    description = ''
WHERE
        id = 10132;

-- iptv.ftp_media_medium definition

CREATE TABLE `ftp_media_medium` (
                                    `id` bigint NOT NULL AUTO_INCREMENT COMMENT '唯一标识符',
                                    `file_name` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '文件名',
                                    `file_size` bigint DEFAULT NULL COMMENT '文件大小',
                                    `file_path` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '文件路径',
                                    `cp_id` bigint DEFAULT NULL COMMENT '所属CP',
                                    `del_sign` int DEFAULT '0' COMMENT '删除标记，状态0：不删除  1：可删除  2:待对比',
                                    `del_status` int DEFAULT '0' COMMENT '删除状态，状态0：未删除  1：等待删除 2:已删除  255:删除失败',
                                    `status_description` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '状态描述',
                                    `create_time` timestamp NULL DEFAULT NULL COMMENT '创建时间',
                                    `update_time` timestamp NULL DEFAULT NULL COMMENT '更新时间',
                                    `cp_code` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT 'code',
                                    PRIMARY KEY (`id`),
                                    KEY `ftp_media_medium_file_name_IDX` (`file_name`) USING BTREE,
                                    KEY `ftp_media_medium_cp_id_IDX` (`cp_id`) USING BTREE,
                                    KEY `ftp_media_medium_cp_code_IDX` (`cp_code`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci ROW_FORMAT=DYNAMIC COMMENT='ftp媒资数据表';

-- iptv.cms_prohibit_schedule definition

CREATE TABLE `cms_prohibit_schedule` (
                                         `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键',
                                         `code` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '唯一标识',
                                         `rule_prohibit_code` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '节目单违禁规则表唯一标识',
                                         `channel_code` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '频道Code',
                                         `channel_name` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '频道名称',
                                         `channel_id` bigint DEFAULT NULL COMMENT '逻辑频道 ID',
                                         `program_name` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '节目名称',
                                         `start_date` varchar(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '节目开播日期',
                                         `start_time` varchar(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '节目开播时间',
                                         `end_time` varchar(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '节目结束时间',
                                         `duration` varchar(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '节目时长(HH24MISS) ',
                                         `storage_duration` int DEFAULT NULL COMMENT 'TVOD 保存时长(小时)\r\n缺省为空\r',
                                         `status` tinyint DEFAULT '1' COMMENT '状态标志\r\n1:生效\r\n2:失效 ',
                                         `description` varchar(1024) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '描述信息',
                                         `source` tinyint DEFAULT NULL COMMENT '来源 1：专线注入 2：人工创建',
                                         `genre` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '节目的分类标签，如“体育”，\r\n多个标签用空格或“;”区分\r',
                                         `cp_id` bigint DEFAULT NULL,
                                         `cp_name` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT 'CP名称',
                                         `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
                                         `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
                                         `creator_name` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '创建人，只人工上传时可用',
                                         `creator_id` bigint DEFAULT NULL COMMENT '创建人ID来自于sys_user',
                                         PRIMARY KEY (`id`) USING BTREE,
                                         KEY `index_channel_id` (`channel_id`) USING BTREE,
                                         KEY `index_channel_name` (`channel_name`) USING BTREE,
                                         KEY `index_source` (`source`) USING BTREE,
                                         KEY `index_status` (`status`) USING BTREE,
                                         KEY `index_cp_id` (`cp_id`) USING BTREE,
                                         KEY `index_code` (`code`) USING BTREE,
                                         KEY `index_rule_prohibit_code` (`rule_prohibit_code`) USING BTREE,
                                         KEY `index_channel_code` (`channel_code`,`start_date`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci ROW_FORMAT=DYNAMIC COMMENT='违禁节目单';

-- iptv.rule_prohibit_schedule definition

CREATE TABLE `rule_prohibit_schedule` (
                                          `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键',
                                          `code` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '规则唯一标识',
                                          `channel_code` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '频道Code',
                                          `channel_name` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '频道名称',
                                          `channel_number` bigint DEFAULT NULL COMMENT '频道号',
                                          `channel_id` bigint DEFAULT NULL COMMENT '逻辑频道 ID',
                                          `name` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '规则名称',
                                          `prohibited_words` text COLLATE utf8mb4_unicode_ci COMMENT '违禁词',
                                          `cp_id` bigint DEFAULT NULL,
                                          `cp_name` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT 'CP名称',
                                          `status` tinyint DEFAULT '1' COMMENT '状态1：有效 2：删除',
                                          `match_status` tinyint DEFAULT '1' COMMENT '状态1：精确 2：模糊',
                                          `creator_name` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '创建人',
                                          `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
                                          `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
                                          PRIMARY KEY (`id`) USING BTREE,
                                          KEY `index_program_name` (`name`) USING BTREE,
                                          KEY `index_channel_code` (`channel_code`) USING BTREE,
                                          KEY `index_status` (`status`) USING BTREE,
                                          KEY `index_code` (`code`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci ROW_FORMAT=DYNAMIC COMMENT='节目单违禁规则表';

-- iptv.internal_information_log definition

CREATE TABLE `internal_information_log` (
                                            `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键',
                                            `code` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '全局唯一标识',
                                            `type` tinyint DEFAULT NULL COMMENT '消息类型,1:注入消息，2:下载消息，3:分发消息，4：系统消息，5：审核消息，99：其他消息',
                                            `title` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '消息标题',
                                            `content` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci COMMENT '消息内容',
                                            `level` tinyint DEFAULT '0' COMMENT '消息等级，分发消息采用 1：一般，2：重要，3：紧急',
                                            `status` tinyint DEFAULT '1' COMMENT '投递状态,1:不可投递，2:待投递，3：投递完成',
                                            `status_description` varchar(1024) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '状态描述',
                                            `result` tinyint DEFAULT '1' COMMENT '确认状态,1:未读，2:已读',
                                            `target_user_id` bigint DEFAULT NULL COMMENT '目标用户id',
                                            `target_user_name` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '目标用户名',
                                            `param` varchar(512) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '参数集合',
                                            `remark` varchar(512) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT ' 备注信息',
                                            `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
                                            `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
                                            PRIMARY KEY (`id`),
                                            KEY `internal_Information_log_code` (`code`) USING BTREE,
                                            KEY `internal_Information_target_user_id` (`target_user_id`) USING BTREE,
                                            KEY `internal_Information_log_type` (`type`) USING BTREE,
                                            KEY `internal_Information_log_status` (`status`) USING BTREE,
                                            KEY `internal_Information_log_result` (`result`) USING BTREE,
                                            KEY `internal_Information_log_create_time_IDX` (`create_time`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='站内信记录表';

-- iptv.out_scheduled_tasks definition

CREATE TABLE `out_scheduled_tasks` (
                                       `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键',
                                       `code` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '全局唯一标识',
                                       `content_id` bigint DEFAULT NULL COMMENT 'bms表媒资主键',
                                       `content_type` tinyint DEFAULT NULL COMMENT '1：电影\r\n2：子集\r\n3：电视剧\r\n4：系列片\r\n5：片花\r\n6：直播\r\n7：物理频道\r\n8：栏目\r\n9：产品包\r\n10：节目单\r\n11：图片\r\n12：视频介质\r\n13：节目图片\r\n14：剧集图片\r\n15：产品包图片\r\n16：栏目图片\r\n17：频道图片\r\n18：栏目节目\r\n19：栏目剧集\r\n20：栏目频道\r\n21：产品包节目\r\n22：产品包剧集',
                                       `priority` tinyint DEFAULT '0' COMMENT '优先级，0：普通，5：优先，10：紧急',
                                       `timed_publish` datetime DEFAULT NULL COMMENT '定时发布，时间存在则代表已经设定为定时发布',
                                       `timed_publish_status` tinyint DEFAULT NULL COMMENT '是否定时发布 1 定时发布  0 不定时发布',
                                       `timed_publish_description` varchar(2000) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '定时发布描述',
                                       `creator_id` bigint DEFAULT NULL COMMENT '操作员id',
                                       `creator_name` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '操作员名称',
                                       `cancel_time` datetime DEFAULT NULL COMMENT '取消时间',
                                       `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
                                       `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
                                       PRIMARY KEY (`id`) USING BTREE,
                                       KEY `index_content_type` (`content_type`) USING BTREE,
                                       KEY `index_create_time` (`create_time`) USING BTREE,
                                       KEY `index_code` (`code`) USING BTREE,
                                       KEY `index_creator_id` (`creator_id`) USING BTREE,
                                       KEY `index_timed` (`timed_publish_status`,`timed_publish`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci ROW_FORMAT=DYNAMIC COMMENT='定时发布任务表';

-- iptv.out_transmission_result definition

CREATE TABLE `out_transmission_result` (
                                           `id` bigint NOT NULL AUTO_INCREMENT,
                                           `correlate_id` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '透传工单ID',
                                           `csp_id` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL,
                                           `lsp_id` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL,
                                           `cmd_file_url` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL,
                                           `result_file_url` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '下游结果反馈xml',
                                           `path` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '下游地址',
                                           `outPassage_id` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '分发通道id',
                                           `outPassage_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '分发通道名',
                                           `result` tinyint DEFAULT NULL COMMENT '下游结果{0:成功,-1:失败}',
                                           `error_description` varchar(1024) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '错误描述',
                                           `status` tinyint DEFAULT NULL COMMENT '调用下游下发接口结果{0:成功,-1:失败}',
                                           `status_description` varchar(1024) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '状态描述',
                                           `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
                                           `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
                                           PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci ROW_FORMAT=DYNAMIC COMMENT='透传工单下发结果';

-- iptv.sys_outpassage_storage_rela definition

CREATE TABLE `sys_outpassage_storage_rela` (
                                               `id` bigint NOT NULL AUTO_INCREMENT,
                                               `out_passage_id` bigint DEFAULT NULL COMMENT 'sys_out_passage表id',
                                               `storage_directory_id` bigint DEFAULT NULL COMMENT 'sys_storage_directory表id',
                                               `limit_speed` bigint DEFAULT NULL COMMENT '限制速度',
                                               `type` tinyint DEFAULT NULL COMMENT '目录类型 1：源片存储目录  2：成片存储目录 3：图片存储目录 4：文档存储目录 5：工单存储目录',
                                               `status` tinyint DEFAULT '1' COMMENT '状态：1正常 -1 : 删除',
                                               `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
                                               `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
                                               PRIMARY KEY (`id`),
                                               KEY `idx_storage_directory_id` (`storage_directory_id`) USING BTREE,
                                               KEY `idx_out_passage_id` (`out_passage_id`) USING BTREE,
                                               KEY `idx_status` (`status`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=68 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='分发存储关系表';

-- iptv.virtual_program definition

CREATE TABLE `virtual_program` (
                                   `id` bigint NOT NULL AUTO_INCREMENT,
                                   `code` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '全局编码',
                                   `series_code` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '剧集code',
                                   `content_type` tinyint DEFAULT NULL COMMENT 'n1：电影\\r\\n2：子集\\r\\n3：电视剧',
                                   `type` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '类型',
                                   `show_name` varchar(256) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '名称',
                                   `vsp_name` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '来源',
                                   `episode_index` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '当前集数',
                                   `duration` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '时长',
                                   `price` tinyint DEFAULT NULL COMMENT '属性:0免费；1收费',
                                   `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
                                   `source` varchar(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT 'hw(华为),zx(中兴),xnpd(本身),bk(播控)',
                                   PRIMARY KEY (`id`) USING BTREE,
                                   UNIQUE KEY `unique_code` (`code`),
                                   KEY `idx_name` (`show_name`) USING BTREE,
                                   KEY `idx_code` (`code`) USING BTREE,
                                   KEY `idx_content_type` (`content_type`) USING BTREE,
                                   KEY `idx_series_code` (`series_code`) USING BTREE,
                                   KEY `idx_create_time` (`create_time` DESC) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=1409666 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci ROW_FORMAT=DYNAMIC COMMENT='virtual_program';

-- iptv.virtual_series definition

CREATE TABLE `virtual_series` (
                                  `id` bigint NOT NULL AUTO_INCREMENT,
                                  `code` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '全局编码',
                                  `content_type` tinyint DEFAULT NULL COMMENT 'n1：电影\\r\\n2：子集\\r\\n3：电视剧',
                                  `type` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '类型',
                                  `show_name` varchar(256) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '名称',
                                  `vsp_name` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '来源',
                                  `episode_num` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '总集数',
                                  `price` tinyint DEFAULT NULL COMMENT '属性:0免费；1收费',
                                  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
                                  PRIMARY KEY (`id`) USING BTREE,
                                  UNIQUE KEY `unique_code` (`code`),
                                  KEY `idx_name` (`show_name`) USING BTREE,
                                  KEY `idx_code` (`code`) USING BTREE,
                                  KEY `idx_content_type` (`content_type`) USING BTREE,
                                  KEY `idx_create_time` (`create_time` DESC) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=18993 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci ROW_FORMAT=DYNAMIC COMMENT='virtual_series';

-- iptv.audit_task definition

CREATE TABLE `audit_task` (
                              `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键ID',
                              `code` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '全局唯一标识',
                              `result` tinyint NOT NULL COMMENT '审核结果,1：待审核，2：审核中，3：通过，4：不通过，99：审核失败/取消',
                              `remarks` varchar(512) DEFAULT NULL COMMENT '备注',
                              `creator_name` varchar(64) NOT NULL COMMENT '创建人',
                              `creator_id` bigint unsigned NOT NULL COMMENT '创建人ID',
                              `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
                              `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
                              PRIMARY KEY (`id`),
                              KEY `idx_code` (`code`),
                              KEY `idx_result` (`result`),
                              KEY `idx_creator_name` (`creator_name`),
                              KEY `idx_create_time` (`create_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='审核任务表';

-- iptv.audit_sub_task definition

CREATE TABLE `audit_sub_task` (
                                  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键ID',
                                  `parent_code` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '父code',
                                  `content_id` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '内容ID',
                                  `content_type` tinyint NOT NULL COMMENT '1：电影,2:子集,3：电视剧,4：系列片,5：片花',
                                  `show_name` varchar(256) DEFAULT NULL COMMENT '内容名称',
                                  `task_id` varchar(64) NOT NULL COMMENT '审核任务ID',
                                  `source` varchar(512) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '介质地址',
                                  `status` tinyint NOT NULL COMMENT '审核结果,1：待审核，2：审核中，3：通过，4：不通过，5：人工复审通过，99：审核失败/取消',
                                  `priority` varchar(32) DEFAULT NULL COMMENT '任务优先级，可选值为 IDLE、NORMAL、HIGH，默认值为 NORMAL。',
                                  `preset` varchar(64) DEFAULT NULL COMMENT '模板名称',
                                  `notification` varchar(40) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '回调名称',
                                  `notification_type` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '回调类型，可选值包括：all（默认，包括 NORMAL、REJECT、REVIEW），abnormal（包括 REJECT、REVIEW），reject（仅 REJECT）',
                                  `description` varchar(256) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '备注',
                                  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
                                  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
                                  PRIMARY KEY (`id`),
                                  KEY `idx_parent_code` (`parent_code`),
                                  KEY `idx_cms_content_id` (`content_id`),
                                  KEY `idx_content_type` (`content_type`),
                                  KEY `idx_task_id` (`task_id`),
                                  KEY `idx_status` (`status`),
                                  KEY `idx_create_time` (`create_time`),
                                  CONSTRAINT `fk_parent_code` FOREIGN KEY (`parent_code`) REFERENCES `audit_task` (`code`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='审核子任务表';

ALTER TABLE iptv.cms_program ADD audit_status tinyint NULL COMMENT '智审状态';
ALTER TABLE iptv.cms_program ADD audit_description LONGTEXT NULL COMMENT '智审备注';
ALTER TABLE iptv.cms_program ADD content_rating VARCHAR(10)  NULL COMMENT '内容评级';
ALTER TABLE iptv.cms_series ADD content_rating VARCHAR(10)  NULL COMMENT '内容评级';
ALTER TABLE iptv_dev.bms_content ADD sub_remind tinyint default 0 COMMENT '子集提示';
ALTER TABLE iptv_test.bms_content ADD sub_remind tinyint default 0 COMMENT '子集提示';

ALTER TABLE iptv_dev.bms_content ADD INDEX idx_sub_remind (sub_remind);
ALTER TABLE iptv_test.bms_content ADD INDEX idx_sub_remind (sub_remind);
ALTER TABLE iptv.cms_program ADD INDEX idx_content_rating (content_rating);
ALTER TABLE iptv.cms_series ADD INDEX idx_content_rating (content_rating);
